<p class="client_focus">{{ T_estimado|safe }}&nbsp;{{ name|safe }}, </p>

<div class="email_header">
    {% if email_header_from_re %}
        {{ email_header_from_re|safe }}
    {% else %}
        {{ email_header|safe }}

    {% endif %}
</div>


{% for extra_link in list_of_extra_links %}

    {% if extra_link.link_extra_booking_4 %}

        {{ extra_link.text_link_booking_4|safe }}&nbsp;
        <a href="{{ extra_link.link_extra_booking_4 }}" target="_blank" style="display: block;font-size: 18px;color:black;cursor:pointer; text-decoration: underline">
            {% if extra_link.label_link %}
                {{ extra_link.label_link|safe }}
            {% else %}
                {{ T_servicio_traslado|safe }}
            {% endif %}

            {% if extra_link.img_extra_booking_4 %}
                <br><br>
                <img src="{{ extra_link.img_extra_booking_4.0 }}">
            {% endif %}
        </a>
         <br><br>

    {% endif %}

{% endfor %}
 {% if qr_code %}
    <p style="margin-top: 5px;">{{ T_qr_code }}</p>
    <img src="{{ qr_code }}" style="width: 150px;">
{% endif %}


<h3 style="padding-top: 10px;margin-bottom: 5px;" class="booking_data_info">{{ T_datos_reserva|safe }}</h3>
<p class="customer_info"><b>{{ T_localizador|safe }}:</b> {{ localizador|safe }}<br/>
    <b>{{ T_nombre|safe }}:</b> {{ name|safe }}&nbsp;{{ lastName|safe }}<br/>
    <b>{{ T_email|safe }}:</b> {{ email|safe }}<br/>
    <b>{{ T_telefono|safe }}:</b> {{ telephone|safe }}
    {% if checkinHour %}<br><b>{{ T_hora_entrada }}:</b> {{ checkinHour }}{% endif %}
</p>
<br/>

{% if gift_data %}
    <h3 class="booking_data_info" style="padding-top: 10px;margin-bottom: 5px; clear:both">{{ T_datos_destinatario }}</h3>
    <p class="customer_info">
        <b>{{T_nombre|safe}}:</b> {{gift_data.gift_name|safe}}&nbsp;{{gift_data.gift_last_name|safe}}<br/>
        <b>{{T_email|safe}}:</b> {{gift_data.gift_email|safe}}<br/>
        <b>{{T_telefono|safe}}:</b> {{gift_data.gift_telephone|safe}}<br/>
        <b>{{T_ciudad|safe}}:</b> {{gift_data.gift_city|safe}}<br/>
    </p>
{% endif %}

{% if extraInfo.billing_name and extraInfo.billing_cif %}
    <h3 class="booking_data_info" style="padding-top: 10px;margin-bottom: 5px; clear:both">{{ T_datos_facturacion }}</h3>
    <p class="customer_info">
        <b>{{ T_nombre_y_apellidos }}</b>: {{ extraInfo.billing_name|safe }}<br/>
        <b>{{ T_nif }}</b>: {{ extraInfo.billing_cif|safe }}<br/>
    </p>
{% endif %}

<div class="total_price_confirm">
    <hr/>
    <b>{{ T_precio_total|safe }}:</b> {{ price|safe }} Euros<br/>
    {% if priceSupplements > 0 %}
        <br/>
        <b>{{ T_estancia|safe }}:</b> &nbsp; {{ roomPrice|safe }} Euros<br/>
        <b>{{ T_servicios_adicionales|safe }}:</b> &nbsp; {{ priceSupplements|safe|floatformat:"-2" }} Euros<br/>
    {% endif %}
    <hr/>
</div>


{% if message_discount_applied %}<br/><b style="color:red;">{{ message_discount_applied }}</b><br/>{% endif %}

<h3 class="instance_confirm_title">{{ T_estancia|safe }}</h3>

<div class="search_confirm_info" style="overflow: auto; padding: 5px;">
    {{ search }}
</div>


 {% if html_specifics_room_modification %}
            <div style="overflow: auto; padding: 5px;">
                <h3 style="padding-top: 10px;margin-bottom: 5px;">{{ T_cambios_modificacion }} </h3>

            {{ html_specifics_room_modification }}
            </div>
        {% endif %}


<h3 class="selected_room_title" style="padding-top: 10px;margin-bottom: 5px;">{{ T_habitacion_seleccionada|safe }}</h3>

<div class="selected_room_info">

    {% if booking_shopping_cart_v2 %}
        <div class="shopping_cart_wrapper" style="text-align: left;">
            <div class="rooms_wrapper">
                <h3 class="room_title"
                        style="padding: 10px;margin:10px 0 5px;box-sizing: border-box;font-size: 16px;text-transform: uppercase;text-align: center;
                                {% if shopping_cart_styles.main_color %}background-color:{{ shopping_cart_styles.main_color }};color: white;{% endif %}{% if shopping_cart_styles.confirmation_block_title_styles %}{{ shopping_cart_styles.confirmation_block_title_styles }}{% endif %}">{{ T_habitaciones }} ({{ extraInfo.shopping_cart_human_read.rooms|length }})</h3>

                <div class="room_options_wrapper">
                    {% for room_cart in extraInfo.shopping_cart_human_read.rooms %}
                        <br>
                        <div class="room_element_title" style="font-size: 14px;"><b>{{ T_habitacion }} {{ forloop.counter }}</b></div>
                        <br>
                        <div class="room_element">
                            {% if dates_per_room %}
                                <div class="room_item"><b>{{ T_entrada }}: </b> {% if room_cart.entry_date %}{{ room_cart.entry_date|safe }}{% else %}{{ localizedStartDate|safe }}{% endif %}</div>
                                <div class="room_item"><b>{{ T_salida }}: </b> {% if room_cart.departure_date %}{{ room_cart.departure_date|safe }}{% else %}{{ localizedEndDate|safe }}{% endif %}</div>
                            {% endif %}
                            <div class="room_item"><b>{{ T_nombre }}: </b> {{ room_cart.room_name|safe }}</div>
                            <div class="room_item"><b>{{ T_tarifa }}: </b> {{ room_cart.rate_name|safe }}</div>
                            <div class="room_item"><b>{{ T_regimen }}: </b> {{ room_cart.board_name|safe }}</div>
                            {% if room_cart.occupancy %}
                                <div class="room_item"><b>{{ T_ocupacion }}: </b>
                                {% for occupancy in room_cart.occupancy|split:"-" %}
                                    {% cycle T_adultos T_ninos T_bebes %}: {{ occupancy }}{% if not forloop.last %},{% endif %}
                                {% endfor %}
                                </div>
                            {% endif %}
                            {% if not hide_offers_summaries %}
                                {% if promotionName %}
                                    <b>{{T_oferta|safe}}:&nbsp;</b><span id="numDays">{{promotionName|safe}}</span><br/>
                                {% endif %}
                            {% endif %}
                            {% if room_cart.package_name %}
                                <div class="room_item"><b>{{ room_cart.package_name|safe }}</b></div>
                            {% endif %}
                            {% if room_cart.forfait %}
                                <div class="room_item">
                                    <b>{{ room_cart.forfait.forfait_name|safe }}</b>
                                </div>
                                {% for adult in room_cart.forfait.adults %}
                                    <br>
                                    <div class="forfait_item"><b>{{ T_adulto }} {{ forloop.counter }}</b></div>
                                    <div class="forfait_item"><b>{{ T_dias }}: </b> {{ adult.days }}</div>
                                    <div class="forfait_item"><b>{{ T_fecha }}: </b> {{ adult.date }}</div>
                                    <div class="forfait_item"><b>{{ T_precio }}: </b> {{ adult.price_total }}</div>
                                {% endfor %}

                                {% for kid in room_cart.forfait.kids %}
                                    <br>
                                    <div class="forfait_item"><b>{{ T_nino }} {{ forloop.counter }}</b></div>
                                    <div class="forfait_item"><b>{{ T_dias }}: </b> {{ kid.days }}</div>
                                    <div class="forfait_item"><b>{{ T_fecha }}: </b> {{ kid.date }}</div>
                                    <div class="forfait_item"><b>{{ T_precio }}: </b> {{ kid.price_total }}</div>
                                {% endfor %}
                            {% endif %}
                        </div>
                    {% endfor %}
                {% if kidsAges %}
                <b>{{T_edades_nino|safe}}</b>:{% for age in kidsAges %} {{ T_habitacion }} {{ forloop.counter }}:  {% if age %} {% for eachAge in age %} {{eachAge}} {{T_anyos|safe}}, {% endfor %} {% endif %} {% endfor %}<br/>
                {% endif %}

                {% if babiesAges %}
                <b>{{T_edades_bebes|safe}}</b>:{% for age in babiesAges %} {{ T_habitacion }} {{ forloop.counter }}:  {% if age %} {% for eachAge in age %} {{eachAge}} {{T_anyos|safe}}, {% endfor %} {% endif %} {% endfor %}<br/>
                {% endif %}
                </div>
            </div>
        </div>
    {% else %}
        {% if rooms_dict %}
            {% for room in rooms_dict %}
                <b>{{ room.room_name|safe|title }}</b><br>
                {% if room.room_description %}{{ room.room_description|safe }}<br><br>{% endif %}
            {% endfor %}
        {% else %}
            <B>{{ T_habitacion_seleccionada|safe }}:&nbsp;</B><span id="numDays">{{ room|safe }}</span><br/>
        {% endif %}

        {% if regimen and not hide_board %}
            <B>{{ T_regimen_seleccionado|safe }}:&nbsp;</B><span id="numDays">{{ regimen|safe }}</span><br/>
        {% endif %}
        <B>{{ T_tarifa|safe }}:&nbsp;</B><span id="numDays">{{ rate|safe }}</span>
        {% if creditCardDetails %}
            &nbsp;({{ rateInternalName|safe }})
        {% endif %}<br/>
        {% if not hide_offers_summaries %}
            {% if promotionName %}
                <B>{{ T_oferta|safe }}:&nbsp;</B><span id="numDays">{{ promotionName|safe }}</span><br/>
            {% endif %}
        {% endif %}
    {% endif %}
</div>


{% if extra_html_booking_4 %}
    {{ extra_html_booking_4|safe }}
{% endif %}

{% if included_supplements %}
    <h3 class="supplement_confirm_title"
        style="padding-top: 10px;margin-bottom: 5px;">{{ T_servicios_incluidos|safe }}</h3>
    <div class="supplement_confirm_info">
        {% for supplement in included_supplements %}
            <p>
                <B>{{ supplement.name|safe }}</B><br/>
                {{ supplement.description|safe }}<br/>
                {% if supplement.extra_info %}
                    <p class="extra_info_service_b3">{{ supplement.extra_info|safe }}</p>
                    <br/>
                {% endif %}
                {{ T_numero_noches }}:&nbsp;<span id="numDays">{{ supplement.days }}</span>
                &nbsp;&nbsp; {{ T_cantidad|safe }}:&nbsp;<span id="numDays">{{ supplement.amount }}</span>&nbsp;&nbsp;
                {% if supplement.price %}({{ supplement.price|floatformat:"-2" }}&nbsp;Euros){% endif %}
            </p>
        {% endfor %}
    </div>
{% endif %}

{% if supplements %}
    <h3 class="supplement_confirm_title"
        style="padding-top: 10px;margin-bottom: 5px;">{{ T_servicios_adicionales|safe }}</h3>
    <div class="supplement_confirm_info">
        {% for supplement in supplements %}
            <p>
                <B>{{ supplement.name|safe }}</B><br/>
                {% if supplement.extra_info %}
                    <p class="extra_info_service_b3">{{ supplement.extra_info|safe }}</p>
                    <br/>
                {% endif %}
                {{ T_numero_noches }}:&nbsp;<span id="numDays">{{ supplement.days }}</span>
                &nbsp;&nbsp; {{ T_cantidad|safe }}:&nbsp;<span
                    id="numDays">{{ supplement.amount }}</span>&nbsp;&nbsp;({{ supplement.price }}&nbsp;Euros)
            </p>
        {% endfor %}
    </div>
{% endif %}

{% if advanced_supplements %}
    <h3 style="padding-top: 10px;margin-bottom: 5px;">{{T_servicios_adicionales|safe}} {% if booking_shopping_cart_v2 %}({{ advanced_supplements|length }}){% endif %}</h3>
    <div class="supplement_confirm_info">
    {% for service in advanced_supplements %}
        <div class="additional_service_element">
            <div><b>{{ service.name }} {% if service.guest_type and service.guest_type == 'child' %}({{ T_nino }}){% endif %}</b></div>
            <div>{{ T_cantidad|safe }}: {{ service.amount|safe }}</div>
            {% if service.customer_name %}
                <div>{{ T_benefier|safe }}: {{ service.customer_name|safe }}</div>
            {% endif %}
            {% if service.extra_info %}
                {% if service.extra_info.customer_first_name %}
                    <div>{{ T_nombre|safe }}: {{ service.extra_info.customer_first_name|safe }}</div>
                {% endif %}
                {% if service.extra_info.customer_last_name %}
                    <div>{{ T_apellidos|safe }}: {{ service.extra_info.customer_last_name|safe }}</div>
                {% endif %}
                {% if service.extra_info.customer_nationality %}
                    <div>{{ T_nacionalidad|safe }}: {{ service.extra_info.customer_nationality_country_name|safe }}</div>
                {% endif %}
                {% if service.extra_info.customer_dni %}
                    <div>{{ T_DNI|safe }}: {{ service.extra_info.customer_dni|safe }}</div>
                {% endif %}
                {% if service.extra_info.customer_dni_expiration %}
                    <div>{{ T_fecha_expiracion_dni|safe }}: {{ service.extra_info.customer_dni_expiration_format_date|safe }}</div>
                {% endif %}
                {% if service.extra_info.customer_birthday %}
                    <div>{{ T_fecha_nacimiento|safe }}: {{ service.extra_info.customer_birthday_format_date|safe }}</div>
                {% endif %}
            {% endif %}
            {% if service.date %}
                <div>{{ T_fecha_servicio|safe }}: {% if service.date == 'all' %}{{ T_toda_la_estancia|safe }}{% else %}{{ service.format_date|safe }}{% endif %}</div>
            {% endif %}
            {% if service.hour %}
                <div>{{ T_horario_reserva|safe }}: {{ service.hour|safe }} hs</div>
            {% endif %}
            {% if service.room_name %}
                <div>{{ T_habitacion_asignada|safe }}: {{ service.room_name|safe }}</div>
            {% endif %}

            {% if service.extra_info %}
            <!-- Special values -->
                {% if service.extra_info.transfer_data_direction and forManager %}
                    {% if service.extra_info.transfer_data_direction == 'to' %}
                        <div>{{ T_trayecto|safe }}: {{ T_ida|safe }}</div>
                    {% elif service.extra_info.transfer_data_direction == 'from' %}
                        <div>{{ T_trayecto|safe }}: {{ T_vuelta|safe }}</div>
                    {% else %}
                        <div>{{ T_trayecto|safe }}: {{ T_ida_vuelta|safe }}</div>
                    {% endif %}
                {% endif %}
                {% if service.extra_info.transfer_data %}
                    <!-- TRANSFER -->
                    {% if service.extra_info.transfer_data.to %}
                        <div>{{ T_ida|safe }}:</div>
                        {% for label, data in service.extra_info.transfer_data.to.items %}
                            <div>{{ label|safe }}: {{data|safe }}</div>
                        {% endfor %}
                    {% endif %}

                    {% if service.extra_info.transfer_data.from %}
                        <div>{{ T_vuelta|safe }}:</div>
                        {% for label, data in service.extra_info.transfer_data.from.items %}
                            <div>{{ label|safe }}: {{data|safe }}</div>
                        {% endfor %}
                    {% endif %}

                    {% if service.extra_info.transfer_data.extra_info_fields %}
                        {% for label, data in service.extra_info.transfer_data.extra_info_fields.items %}
                            <div>{{ label|safe }}: {{data|safe }}</div>
                        {% endfor %}
                    {% endif %}
                    <!-- END TRANSFER -->
                {% endif %}
                <!-- PORT AVENTURA -->
                {% if service.extra_info.port_aventura and service.extra_info.port_aventura.ticket_name %}
                    <div>{{ T_tipo|safe }}: {{ service.extra_info.port_aventura.ticket_name|safe }}</div>
                {% endif %}
                <!-- END PORT AVENTURA -->
                <!-- TRANSFERS T&E -->
                {% if service.extra_info.transfer_tye %}
                    {% if service.extra_info.transfer_tye.origin %}
                        <div>{{ T_origen|safe }}: {{ service.extra_info.transfer_tye.origin|safe }}</div>
                    {% endif %}
                    {% if service.extra_info.transfer_tye.flight_number_to and service.extra_info.transfer_tye.hour_to %}
                        <div>{{ T_flight_details|safe }} ({{ T_ida|safe }}): {{ service.extra_info.transfer_tye.flight_number_to|safe }} - {{ service.extra_info.transfer_tye.hour_to|safe }} hs</div>
                    {% endif %}
                    {% if service.extra_info.transfer_tye.flight_number_from and service.extra_info.transfer_tye.hour_from %}
                        <div>{{ T_flight_details|safe }} ({{ T_vuelta|safe }}): {{ service.extra_info.transfer_tye.flight_number_from|safe }} - {{ service.extra_info.transfer_tye.hour_from|safe }} hs</div>
                    {% endif %}
                {% endif %}
                <!-- END TRANSFERS T&E -->
                <!-- SUPPLEMENTS -->
                {% if service.extra_info.supplements %}
                    <div>{{ T_supplements|safe }}: {% for supplement in service.extra_info.supplements %} {{ supplement.amount|safe }} {{ supplement.name|safe }}{% if not forloop.last %}, {% endif %}{% endfor %}</div>
                {% endif %}
                <!-- END SUPPLEMENTS -->
            <!-- End Special values -->
            {% endif %}

            <div>{{ T_precio|safe }}: {% if service.price == 0 %}{{ T_gratis|safe }}{% else %}<span class="value_elem currencyValue">{% if service.price_with_taxes %}{{ service.price_with_taxes|safe }}{% else %}{{ service.price|safe }}{% endif %}</span><span class="monedaConv">{{ currency|safe }}</span>{% endif %}</div>
            {% if service.special_tax %}
                <div>{{ T_impuesto_especial|safe }}: {{ service.special_tax|safe }}%</div>
            {% endif %}
            {% if show_supplement_description and service.description %}
                <div>{{ T_description|safe }}:</div>
                <div>{{ service.description|safe }}</div>
            {% endif %}
        </div>
        <br/>
    {% endfor %}
    </div>
{% endif %}


{% if pay_later_supplements %}
    <h3 class="supplement_confirm_title"
        style="color:red;padding-top: 10px;margin-bottom: 5px;">{{ T_supplement_pay_later|safe }}</h3>
    <div class="supplement_confirm_info">
        {% for supplement in pay_later_supplements %}
            <p>
                <B>{{ supplement.name|safe }}</B><br/>
                {% if supplement.extra_info %}
                    <p class="extra_info_service_b3">{{ supplement.extra_info|safe }}</p>
                    <br/>
                {% endif %}
                {{ T_numero_noches }}:&nbsp;<span id="numDays">{{ supplement.days }}</span>
                &nbsp;&nbsp; {{ T_cantidad|safe }}:&nbsp;<span
                    id="numDays">{{ supplement.amount }}</span>&nbsp;&nbsp;({{ supplement.price }}&nbsp;Euros)
            </p>
        {% endfor %}
    </div>
{% endif %}

<div class="selected_room_info">
    <b>{{ T_comentarios|safe }}:</b> {{ comments|safe }}<br/>
    {% if extra_room_options %}
        {% for option in extra_room_options %}
            <div>
                <b>{{ option.email_title|safe }}</b> {{ option.option_name|safe }} ({{ option.room_name|safe }})
            </div>
        {% endfor %}
    {% endif %}


    {% if forManager %}
        <b>{{T_comentarios|safe}}&nbsp;Call Center</b>
        {{extraInfo.comments_callseeker|safe}}<br/>
    {% endif %}
</div>

<br/><br/>
{% if starPromotions %}
    {% for currentPopup in starPromotions %}
        {{ currentPopup.pictures.0.description|safe }}<br/>
    {% endfor %}
{% endif %}

{% if rate_conditions %}
    <div class="terms_and_rates_conditions_wrapper">
        <B>{{ T_terminos_y_condiciones_reserva|safe }}</B></br>
        <span class="rate_conditions">{{ rate_conditions|safe }}</span>
        <br/>
    </div>
{% endif %}


{% if promocode_information %}
    {{ promocode_information|safe }}
{% endif %}


{% if not creditCardDetails and not paymentOrderId %}
    <div class="email_footer_confirm">
        {{ email_footer|safe }}
    </div>
{% endif %}

{% if creditCardDetails %}
    <p class="credit_card_confirm_info">
        {% if encrypted %}
            {{ creditCardDetails|safe }}
        {% else %}
            Numero de Tarjeta: {{ creditCardDetails.number }}<br/>
            Empresa Tarjeta: {{ creditCardDetails.company }}<br/>
            Fecha caducidad: {{ creditCardDetails.expiryDate }}<br/>
            cvv: {{ creditCardDetails.cvv }}<br/>
            <br/>
            Documento identidad: {{ personalID|safe }}
        {% endif %}
    </p>

{% endif %}

<!-- Rescueseekers information -->
<input type="hidden" id="rsk_reservationId" value="{{ localizador|safe }}">
<input type="hidden" id="rsk_totalPrice" value="{{ price|safe|floatformat:"-2" }}">



{% if gatewayFailed %}
    <div class="failed_transaction_confirm">
        ERROR EN TRANSACCIÓN BANCARIA: CARGO NO REALIZADO.
        Deberá realizar dicho cargo en tarjeta de forma manual.
    </div>
{% endif %}

{% if show_pending_amount_pay and pending_amount %}
    <p class="pending_amount"><span style="color:red">{{ T_pendiente_cobro }} <b>{{ pending_amount }} {% if not default_currency %}Euros{% else %}{{ default_currency }}{% endif %}</b></span></p>
    <br/>
{% else %}
    {% if show_pending_amount and pending_amount %}
        <p><span style="color:red">{{ T_pendiente_pago_recepcion }} <b>{{ pending_amount }} {% if not default_currency %}Euros{% else %}{{ default_currency }}{% endif %}</b></span></p>
        <br/>
    {% endif %}
{% endif %}

{% if paymentOrderId %}
    <p class="payment_order_confirm">
        Identificador transacción: (Order Id:{{ paymentOrderId }})<br/>
        {% if not gatewayFailed %}
            Pagado mediante pasarela: {{ paymentAmount }}
            <br/>
            Documento identidad: {{ personalID|safe }}
        {% endif %}
    </p>
{% endif %}



{% if club_name %}
    <p>
        {{ club_name|safe }}
    </p>
    <p>
        {{ club_conditions|safe }}
    </p>
{% endif %}



{% if forBrowser %}

    <!-- Rescueseekers information -->
    <input type="hidden" id="rsk_reservationId" value="{{ localizador|safe }}">
    <input type="hidden" id="rsk_totalPrice" value="{{ price|safe|floatformat:"-2" }}">


    <script type="text/javascript" src="https://www.tripadvisor.com/js3/conversion/pixel.js"></script>
    <script type="text/javascript">
        TAPixel.conversionWithReferer("001F000000vA4u0", "{{ applicationId }}", {{ priceInCents|safe }}, "EUR", {{iva|safe}}, 0, "{{ entry_date|safe }}", "{{leave_date|safe }}", {{ numAdults|safe }}, "{{ localizador|safe }}");
    </script>

    <!-- Adwords conversion tracking -->
    {{ adwords|safe }}
    {{ rescueseeker|safe }}
    {{ ratecheck|safe }}
{% endif %}
<input name="days_supplement" class="days_supplement" type="hidden" value="0">
<div class="calendar_dates_selector {% if service_element.duration == 'Uno' %}one_calendar{% endif %}" {% if service_element.duration == 'Uno' %}data-one-day-duration="True"{% endif %}>
    {% for i in service_element.range[1:] %}
        <div class="calendar_wrapper hide" data-service-reference="{{ i }}" data-days="1">
            <input type="hidden" class="current_selected_price currencyValue" value="{{ service_element.price }}">
            <div class="service_label">{{ i }}.</div>
            <div class="additional_service_entry">
                <label class="calendar_label">{% if service_element.duration == 'A elegir' %}{{ T_fecha_inicio_servicio|safe }}{% else %}{{ T_fecha_servicio|safe }}{% endif %}</label>
                <input type="text" class="entry_date">
            </div>
            {% if service_element.duration == 'A elegir' %}
            <div class="additional_service_departure">
                <label class="calendar_label">{{ T_fecha_fin_servicio|safe }}</label>
                <input type="text" class="departure_date">
            </div>
        {% endif %}
        </div>
    {% endfor %}
</div>
<div id="login_wrapper_element" class="version_banner_v1">
    <div class="content_login_wrapper">
        <div class="overlay_element"></div>

        {% if login_banner_club.login_extra_info.background %}
            <div class="background_image">
                <img src="{{ login_banner_club.login_extra_info.background.0.servingUrl }}" alt="">
            </div>
        {% endif %}

        <div class="logo_wrapper">
            {% if login_banner_club.custom_logotype %}
                <img src="{{ login_banner_club.custom_logotype|safe }}" data-resize="true">
            {% else %}
                <img src="{{ login_banner_club.logotype.0 }}" data-resize="true">
            {% endif %}
        </div>

        <div class="club_icons_wrapper">
            {% for club_icon in login_banner_club.login_extra_info.icons %}
                <div class="club_icon_element">
                    <div class="icon_image_wrapper">
                        <img src="{{ club_icon.servingUrl }}" alt="{{ club_icon.altText }}" class="icon_image_element" data-resize="true">
                    </div>
                </div>
            {% endfor %}
        </div>

        <div class="button_wrapper">
            <span class="close"></span>
            <span class="see_more_button">{{ login_banner_club.custom_see_advantages|safe if login_banner_club.custom_see_advantages else T_ver_ventajas }} <i class="fal fa-plus"></i></span>
        </div>

        <div class="hidden_user_club_info">
            {{ login_banner_club.section_info.content|safe }}
        </div>
    </div>

    <div class="users_buttons_wrapper">
        <div class="already_member_wrapper" login="true">
            {{ login_banner_club.custom_acceder|safe if login_banner_club.custom_acceder else T_miembro_acceder|upper }}
        </div>

        <div class="join_button_wrapper">
            <p class="want_join" register="true">{{ login_banner_club.custom_unirme|safe if login_banner_club.custom_unirme else T_quiero_unirme|upper }}</p>
        </div>
    </div>
</div>


<div id="logged_user_info_wrapper" class="version_banner_v1">
    <div class="logo_wrapper">
        {% if login_banner_club.custom_logotype %}
            <img src="{{ login_banner_club.custom_logotype|safe }}" data-resize="true">
        {% else %}
            <img src="{{ login_banner_club.logotype.0 }}" data-resize="true">
        {% endif %}
    </div><!--
    --><div class="content_logged_wrapper">
        <div class="overlay_element"></div>
        <div class="center_content_wrapper">
            <div class="logged_user_text">
                <span class="default_text">{{ T_bienvenido }} </span>
                <span class="username_text"></span>
            </div>
            {% if login_banner_club.login_extra_info.label_welcome_user %}
                <div class="welcome_user_label">{{ login_banner_club.login_extra_info.label_welcome_user|safe }}</div>
            {% endif %}
            <div class="user_category_image"></div><!--
            --><div class="user_points">
                <div class="image_wrapper">
                    <img src="{{ login_banner_club.points_image.0.servingUrl if login_banner_club.points_image }}" alt="" class="points_ico">
                </div>
                <div class="content_wrapper">
                    <span class="points_amount"></span>
                    <span class="points_label">
                        {% if login_banner_club.login_extra_info.coin_name %}
                            {{ login_banner_club.login_extra_info.coin_name }}
                        {% else %}
                            {{ T_noches }}
                        {% endif %}
                    </span>
                </div>
            </div>
        </div>
    </div>
    <div class="logout_button_wrapper">
        <i class="fa fa-power-off" aria-hidden="true"></i>
        <span>{{ T_salir }}</span>
    </div>
</div>

{# Popup information #}
<div id="popup_login_information" class="version_v1" style="display: none">
    <div class="tabs_wrapper">
        <div class="login_tab active" login="true">{{ login_banner_club.login_title_tab|safe if login_banner_club.login_title_tab else T_login }}</div>
        <div class="register_tab" register="true">{{ login_banner_club.register_title_tab|safe if login_banner_club.register_title_tab else T_register }}</div>
    </div>
    {% include "mobile/booking_process/login_club/v2/_register_form_v2.html" %}
    {% include "mobile/booking_process/login_club/v2/_login_form_v2.html" %}
</div>


<script async type="text/javascript" src="/static_1/scripts/utils/users/banners_controllers_v1.js?v=@@automatic_version@@"></script>

<script>
    $(window).on("load", function(){
        bannerController_v1.config.version = 'v2';
        bannerController_v1.config.custom_modal_class = 'users_modal_v2';
        {% if loyalty_seeker_endpoint %}bannerController_v1.config.loyalty_endpoint = '{{ loyalty_seeker_endpoint|safe }}';{% endif %}
        bannerController_v1.init();
        $(".square_plus").click(function () {
            $(this).toggleClass("active");
            $(this).closest("#login_wrapper_element").toggleClass("active");
        });
    });
</script>

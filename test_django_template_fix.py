#!/usr/bin/env python3
"""
Test the fixed dumps_json_for_javascript function for Django template compatibility
"""

import json
from utils.compatibility.compatibility_utils import dumps_json_for_javascript

def test_django_template_compatibility():
    """Test that the function works correctly in Django template context"""
    
    print("=== Testing Django Template Compatibility ===")
    
    # Test the original problematic case
    test_data = {"property_name": 'Cama balinesa "Fresquito" '}
    
    print(f"Input: {test_data}")
    
    # Get the function output
    result = dumps_json_for_javascript(test_data)
    print(f"Function output: {result}")
    print(f"Function repr: {repr(result)}")
    
    # Simulate Django template rendering
    django_template_output = f"JSON.parse(`{result}`)"
    print(f"\nDjango template generates:")
    print(django_template_output)
    
    # Simulate what JavaScript would see after template literal processing
    # In JavaScript template literals, \\" becomes \"
    js_sees = result.replace('\\\\"', '\\"')
    print(f"\nAfter template literal processing, JavaScript sees:")
    print(f"JSON.parse(`{js_sees}`)")
    
    # Test if this would be valid JSON for JavaScript
    try:
        # This simulates what JSON.parse would receive in JavaScript
        parsed_js = json.loads(js_sees)
        print(f"✓ JavaScript JSON.parse would work: {parsed_js}")
        
        # Check if the data is preserved correctly
        if parsed_js == test_data:
            print("✓ Data integrity: PRESERVED")
        else:
            print("✗ Data integrity: CORRUPTED")
            print(f"  Expected: {test_data}")
            print(f"  Got: {parsed_js}")
            
    except Exception as e:
        print(f"✗ JavaScript JSON.parse would fail: {e}")
        return False
    
    return True

def test_comprehensive_scenarios():
    """Test various scenarios to ensure the fix works comprehensively"""
    
    print("\n=== Testing Comprehensive Scenarios ===")
    
    test_cases = [
        {
            "name": "Original problem case",
            "data": {"property_name": 'Cama balinesa "Fresquito" '}
        },
        {
            "name": "Multiple quotes",
            "data": {"text": 'Text with "multiple" "quotes" here'}
        },
        {
            "name": "Mixed quotes and other chars",
            "data": {"text": 'Mixed "quotes" with `backticks` and ${expr}'}
        },
        {
            "name": "Nested quotes",
            "data": {"nested": {"inner": 'Value with "quotes"'}}
        },
        {
            "name": "Array with quotes",
            "data": ["item1", 'item with "quotes"', {"key": 'value with "quotes"'}]
        },
        {
            "name": "No quotes (should still work)",
            "data": {"simple": "No quotes here", "number": 123}
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test {i}: {test_case['name']} ---")
        
        result = dumps_json_for_javascript(test_case['data'])
        print(f"Input: {test_case['data']}")
        print(f"Output: {result}")
        
        # Simulate JavaScript template literal processing
        js_sees = result.replace('\\\\"', '\\"')
        
        # Test if JavaScript would be able to parse it
        try:
            parsed_js = json.loads(js_sees)
            print("✓ JavaScript compatibility: SUCCESS")
            
            # Check data integrity
            if parsed_js == test_case['data']:
                print("✓ Data integrity: PRESERVED")
            else:
                print("✗ Data integrity: CORRUPTED")
                all_passed = False
                
        except Exception as e:
            print(f"✗ JavaScript compatibility: FAILED - {e}")
            all_passed = False
    
    return all_passed

def test_edge_cases():
    """Test edge cases and potential issues"""
    
    print("\n=== Testing Edge Cases ===")
    
    edge_cases = [
        {
            "name": "Empty string",
            "data": {"empty": ""}
        },
        {
            "name": "Only quotes",
            "data": {"quotes": '""'}
        },
        {
            "name": "Escaped quotes in input",
            "data": {"escaped": 'Text with \\"already escaped\\" quotes'}
        },
        {
            "name": "Unicode with quotes",
            "data": {"unicode": 'Café "special" résumé'}
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(edge_cases, 1):
        print(f"\n--- Edge Case {i}: {test_case['name']} ---")
        
        result = dumps_json_for_javascript(test_case['data'])
        print(f"Input: {test_case['data']}")
        print(f"Output: {result}")
        
        # Simulate JavaScript processing
        js_sees = result.replace('\\\\"', '\\"')
        
        try:
            parsed_js = json.loads(js_sees)
            print("✓ JavaScript compatibility: SUCCESS")
        except Exception as e:
            print(f"✗ JavaScript compatibility: FAILED - {e}")
            all_passed = False
    
    return all_passed

def main():
    """Run all tests"""
    
    print("Testing Fixed dumps_json_for_javascript Function")
    print("=" * 60)
    
    # Test Django template compatibility
    django_passed = test_django_template_compatibility()
    
    # Test comprehensive scenarios
    comprehensive_passed = test_comprehensive_scenarios()
    
    # Test edge cases
    edge_cases_passed = test_edge_cases()
    
    # Final result
    print("\n" + "=" * 60)
    if django_passed and comprehensive_passed and edge_cases_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✓ Django template compatibility fixed")
        print("✓ JavaScript JSON.parse will work correctly")
        print("✓ All edge cases handled")
        print("\nThe function now properly double-escapes quotes for JavaScript template literals!")
    else:
        print("❌ SOME TESTS FAILED")
        print("The fix needs further adjustment.")
    print("=" * 60)

if __name__ == "__main__":
    main()

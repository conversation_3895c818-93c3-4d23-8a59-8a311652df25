<form class="payment_ceca" method="POST" id="paymentForm" enctype="application/x-www-form-urlencoded">
    <input name="MerchantID"     type=hidden>
    <input name="AcquirerBIN"    type=hidden>
    <input name="TerminalID"     type=hidden>
    <input name="Num_operacion"  type=hidden>
    <input name="Importe"        type=hidden>
    <input name="Descripcion"    type=hidden>
    <input name="TipoMoneda"     type=hidden>
    <input name="Exponente"      type=hidden value="2">
    <input name="URL_OK"         type=hidden value="">
    <input name="URL_NOK"        type=hidden value="">
    <input name="Firma"          type=hidden>
    <input name="Cifrado"        type=hidden value="SHA2">
    <input name="Pago_soportado" type=hidden value="SSL">
    <input name="Idioma"         type=hidden value="1">
    <input name="Idusuario"      type=hidden>
    <input name="Token"          type=hidden>
</form>

<script>

    function set_values(data) {

        $("[name=MerchantID]").val(data.merchant_id);
        $("[name=AcquirerBIN]").val(data.acquirer_bin);
        $("[name=TerminalID]").val(data.terminal_id);
        $("[name=Num_operacion]").val(data.order_id);
        $("[name=Importe]").val(data.amount);
        $("[name=TipoMoneda]").val(data.currency);
        $("[name=Descripcion]").val(data.description);
        $("[name=Firma]").val(data.sign);
        $("[name=Idusuario]").val(data.user_id);
        $("[name=Token]").val(data.token);
        $("[name=URL_OK]").val(data.url_ok);
        $("[name=URL_NOK]").val(data.url_nok);

        $("#paymentForm")[0].action = data.url_payment_token;

    }

    var controller = function () {
        return {
            open_popup: function(price, seed, beeper_code) {

                var data = {'price': price,
                            'seed': seed,
                            'identifier': beeper_code};
                $.ajax({ url:"{{ url_payment }}", data: data, type:"POST", async:false }).done((response)=>{

                    var response_json = JSON.parse(response);
                    if (response_json.status != "error") {
                        set_values(response_json);
                        $("#paymentForm")[0].submit();
                    } else {
                        $("#error-msg").val("Something was wrong creating payment order!");
                    }

                }).fail((message)=>{
                    $("#error-msg").val("Something was wrong creating payment order!");
                });

            }
        }
    }();

</script>

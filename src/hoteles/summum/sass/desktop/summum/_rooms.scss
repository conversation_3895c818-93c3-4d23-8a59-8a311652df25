.top_rooms_banner{
  margin-bottom: 50px;
  .banner{
    .title {
      color: $corporate_1;
      font-family: $font_1;
    }
    .subtitle {
      font-family: $font_2;
      font-weight: 200;
    }
    .fa-angle-down{
      color: $black;
    }
  }
}

.hide_rooms_banner{
  display: none;
}

div#step-1, div#step-2 {
  #rooms_b1_wrapper {
    span.last_day_cancellation_text {
      padding-top: 5px;
      padding-bottom: 5px;
    }

    .room_step_block {
      .room_step_label {
        padding: 22px 35px;
        line-height: 20px;
        letter-spacing: 0.4px;
        border: 2px solid #CDCFD0;
        border-radius: 8px;
        color: $black;

        &::before {
          content: "";
          background: url(https://storage.googleapis.com/cdn.paraty.es/parkroyal-corpo/files/fi-rr-angle-small-down.svg);
          background-repeat: no-repeat;
          background-size: cover;
          width: 30px;
          height: 15px;
          transform: rotate(180deg);
          color: $black;
        }
      }

      &.active {
        .room_step_label {
          &::before {
            transform: rotate(0deg);
            top: 40%;
          }
        }

        .room_step_options {
          .tipoHabitacion {
            width: 575px !important;
          }
        }
      }
    }

    .total_price_wrapper {
      .total_price {
        background: #333333;
      }

      .rooms_submit_button {
        background-color: $corporate_2;
        border-radius: 0 8px 8px 0;
        letter-spacing: 0.4px;
        font-weight: bold;
        font-size: 20px;
        border: none;
        margin-left: 5px;

        &.disabled {
          background-color: #CECECE;
        }
      }
    }

    .contTipoHabitacion {
      border: 1px solid #CDCFD0;
      border-radius: 0;
      padding: 0 !important;
      margin-bottom: 40px;
      position: relative;
      @media only screen and (max-width: 1140px) {
        width: 1077px !important;
      }

      &:first-of-type {
        padding: 10px 0;
      }

      .contFotoDescripcion {
        padding: 15px;
        height: 155px;

        div.contFotoHabitacion.lupa-container {
          min-height: 205px;
          width: 306px !important;
          border-radius: 3px 0 0 0;

          .see_more_rooms_v2 {
            i {
              position: absolute;
              left: 10px;
              top: 10px;
              font-weight: 300;
              font-size: 30px;
            }

            &::before {
              display: none;
            }
          }

          .virtual_tour_wrapper {
            @include center_xy;
            z-index: 2;

            .tour_button {
              position: relative;
              display: block;
              width: 55px;
              height: 55px;
              border-radius: 50%;
              background-color: rgba($black, 0.5);
              @include transition(background-color, 0.3s);

              &::before {
                display: none;
              }

              .tour_virtual_icon {
                @include center_xy;

                i {
                  font-size: 28px;
                  color: white;
                }
              }
            }

            .tour_button:hover {
              background-color: rgba($black, 0.7);
            }
          }
        }

        .contDescHabitacion {
          width: 780px;
          height: 210px;
          margin: 0 !important;

          .room_description_name_wrapper {
            max-height: 145px !important;
            overflow: visible;

            .cabeceraNombreHabitacion {
              padding-top: 0;
              margin-bottom: 30px;

              &.booking_message_width {
                margin-bottom: 15px;

                .tipoHabitacion {
                  width: 407.562px !important;
                  height: 60px;
                }
              }

              .tipoHabitacion {
                font-family: $title_family;
                color: $corporate_1;
                text-decoration: none;
                font-size: 22px;
                line-height: 39px;
                letter-spacing: 0.48px;
                font-weight: 400;
                margin-bottom: -25px;
              }

              .just_booking_message {
                background-color: #DED8CF;
                font-size: 12px;
                letter-spacing: 1.2px;
                line-height: 22px;
                color: #212121;
                font-family: $text-family;
                text-transform: uppercase;
                //border-radius: 0 5px 5px 0;
                margin-right: -30px;
                padding: 5px 10px;

                &::before {
                  display: none;
                }
              }

              .very_asked_message {
                font-family: $text_family !important;
                background-color: $corporate_3;
                //border-radius: 5px 0 0 5px;
                font-size: 15px;
                line-height: 22px;
                letter-spacing: 1.1px;
                text-transform: uppercase;
                padding: 7px 10px 3px !important;
                color: white;
                height: 22px;
                font-weight: 700;
                opacity: 1;

                &::before, &::after {
                  display: none;
                }
              }

              &.booking_message_width {
                .just_booking_message {
                  overflow: hidden;

                  &::before {
                    border-bottom: 30px solid #ea6d64;
                    border-left: 15px solid white;
                  }
                }

                span:last-child.just_booking_message {
                  &::before {
                    border-left: 15px solid white;
                  }
                }

                .very_asked_message {
                  bottom: 0;
                  line-height: 19px;
                }
              }
            }

            .descripcionHabitacion {
              font-family: $text_family;
              font-weight: 200;
              font-size: 15px;
              letter-spacing: 0.35px;
              line-height: 26px;
              color: $black;
              padding: 0 20px;
              height: 70px !important;
              @include ellipsis(3);
            }
          }

          .see_more_rooms_v2 {
            bottom: 45px !important;
            border-bottom: 1px solid $black;

            .see_more {
              text-transform: none;
              font-weight: 400;
              font-family: $text_family;
              text-decoration: none;
              font-size: 16px;
              line-height: 21px;
              letter-spacing: 0.35px;
              padding: 2px 0 0 10px;
              color: $black;
            }

            .plus_sign {
              color: #383838;
              padding: 3px 7px 0 0;
            }
          }

          .room_services {
            border-top: 0;
            border-bottom: 0;
            margin: 0 auto !important;
            width: 94% !important;

            &::before,
            &::after {
              position: absolute;
              content: '';
              top: 0;
              bottom: 0;
              width: 50px;
            }

            &::before {
              left: 0;
              background: linear-gradient(270deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
              z-index: 1;
            }

            &::after {
              right: 0;
              background: linear-gradient(90deg, rgba(255,255,255,0) 0%, rgba(255,255,255,1) 100%);
              z-index: 1;
            }

            .service_element {
              border-right: 0 !important;
              display: inline-flex;

              i {
                color: $corporate_2 !important;
                margin-right: 15px;
              }

              .service_description {
                font-weight: 400 !important;
                line-height: 26px !important;
                color: #383838;
                font-size: 14px !important;
                letter-spacing: 0.7px;
                text-align: left;
              }
            }

            .owl-nav {
              z-index: 2;
            }
          }
        }
      }
    }

    .preciosHabitacion {
      margin-top: 0;

      .listadoHabsTarifas {
        font-family: $text_family;
        margin-top: 50px;
        border-spacing: 0;

        .rate_tr_element {
          .filaTipoTarifa {
            .contTitTipoTarifa {
              background-color: #EAF3F9 !important;
              font-family: $text_family;
              font-weight: 600;
              text-transform: none;
              height: 45px;
              margin: 0 5px;

              .titTipoTarifa {
                color: $black;
                font-weight: 300;
                font-size: 14px;
                line-height: 24px;
                letter-spacing: 0.31px;
                padding: 15px 15px 15px 5px;
                text-transform: capitalize;
              }

              .cheapest_rate_message {
                background-color: white;
                font-weight: 400;
                font-size: 12px;
                line-height: 22px;
                letter-spacing: 0.58px;
                position: relative;
                color: $black;
                border-radius: 14px;
                padding: 3px 20px 10px 40px !important;
                margin-top: 5px;
                margin-left: 10px;

                .before_block {
                  display: none;
                }

                .conditions_info_wrapper .rate_conditions_link {
                  color: $black !important;
                  font-size: 9px;
                  text-decoration: none !important;
                  font-weight: 300;
                }

                .conditions_info_wrapper {
                  top: 16px;
                }

                &::before {
                  content: "\f005";
                  display: inline-block;
                  color: $corporate_2;
                  font-family: "Font Awesome 5 Pro";
                  left: 10px;
                  bottom: 25%;
                  font-size: 16px;
                  font-weight: 700;
                  border-left: 0 !important;
                }
              }

              .conditions_info_wrapper {
                top: 14px;
                text-transform: none;
                font-size: 12px;

                a {
                  color: $black !important;
                  font-size: 14px;
                  letter-spacing: 0;
                }

                .rate_conditions_link {
                  font-weight: 300;
                  background-image: none;

                }

                .last_day_cancellation_text {
                  background-image: none;
                  font-style: normal !important;
                  position: relative;

                  &::before {
                    position: absolute;
                    font-family: 'Font Awesome 5 Pro';
                    content: '\f00c';
                    top: 50%;
                    left: 0;
                    transform: translateY(-50%);
                    color: $corporate_1;
                    font-size: 15px;
                    font-weight: 700;
                  }
                }
              }

              .advice_rate_message {
                background-color: $corporate_2;
                color: white;
                height: 24px;
                line-height: 24px;

                .rate_conditions_link {
                  color: white !important;
                  font-size: 9px;
                }

                &::before {
                  border-left-color: #E5F1F4 !important;
                  border-bottom: 47px solid transparent;
                }
              }
            }
          }
        }

        .regimen_tr_element {
          @media only screen and (max-width: 1140px) {
            height: 80px;
          }
          border-bottom: 1px solid $lightgrey;

          .regimenColumn {
            text-transform: none;

            .regimenColumnContent {
              & > div {
                //display: inline-block !important;
              }
            }

            .regimen_name_wrapper, .tTextoOferta {
              font-family: $text_family;
              font-size: 14px;
              line-height: 24px;
              letter-spacing: 0.31px;
              color: $black;
            }

            .tTextoOferta {
              color: $red !important;
              font-weight: 600;
              margin-top: 0;
              font-size: 14px;
              letter-spacing: 0.31px;
              line-height: 24px;
            }
          }

          .precioNocheColumn,
          .precioTotalColumn {
            .priceValues {
              text-align: right;

              .precioTachadoDiv {
                color: $corporate_1;
                margin-bottom: -5px;
                text-decoration: none;


                .tPrecioTachado {
                  color: $red;
                  font-size: 11px;
                  line-height: 24px;
                  font-weight: bold;
                  text-decoration: line-through;
                }
              }

              .precioGeneralDiv {
                margin-bottom: 0;
                display: flex;
                align-items: center;
                justify-content: flex-end;

                .tPrecioOferta {
                  color: #383838;
                  font-size: 18px;
                  line-height: 24px;
                  font-weight: bold;
                }

                .precioGeneral {
                  font-weight: 600 !important;
                  color: $corporate_1 !important;
                }

                .logged_agency_img_info {
                  width: 16px !important;
                  height: 16px !important;
                  margin-left: 5px !important;
                  margin-bottom: 0 !important;

                  &::before {
                    margin-right: 4px !important;
                  }
                }
              }

              .tax_not_included_label {
                display: none;
              }

              .logged_agency_wrapper_info {
                background: white;
                border: 1px solid $black;
                width: max-content;

                .field {
                  color: $black;
                  font-size: 12px;
                  line-height: 20px;

                  .label_nam {
                    color: $black;
                    font-weight: bold;
                  }

                  .value_elem, .monedaConv {
                    color: $corporate-1;
                  }

                  &.agency_comission_info {
                    display: block;
                  }

                  &.total_field {
                    font-size: 12px;
                  }
                }
              }

              .priceTitle {
                font-weight: 400;
                font-size: 9px;
                line-height: 14.4px;
                margin-top: -1px;
              }

              .tax_not_included_label {
                font-size: 12px;
              }

              .promotion_percentage_square {
                background-color: $red;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-right: -50px;
                margin-top: -40px;

                .promotion_percentage {
                  font-size: 13px;
                }

                .promotion_discount {
                  padding: 5px 5px;
                  text-align: center;
                  z-index: 1;
                  background: $red;
                  min-width: fit-content;
                }
              }

              .tax_inc_wrapper_info {
                .tax_included {
                  display: none;
                }
              }
            }
          }

          .precioTotalColumn {
            width: 25%;
            text-align: right;

            .priceValues {
              margin-left: 0;
              margin-right: 140px;
            }
          }

          .botonReservarColumn {
            button {
              padding: 12px 25px;
              font-family: $text_family;
              font-size: 17px;
              font-weight: bold;
              line-height: 17px;
              letter-spacing: 0.85px;
              position: relative;
              border-radius: 0;
              transition: all .6s;
              text-transform: uppercase;
              background: $corporate-1;
              white-space: nowrap;

              &:lang(fr) {
                padding: 15px 5px;
              }

              &:hover {
                background: $corporate-2;
              }
            }
          }
        }
      }
    }
  }
  .regimen_for_users.has_promotion {
    .lock_board_wrapper.toggle_discount {
        float: right;
    }
  }

  .with_highlight_column {
    .regimenColumn {
        width: 540px;
    }
  }
}

.fancyboxContent_second_v .room_popup_individual_element {
  min-height: 680px;
}

.room_popup_individual_element {
  .popup_title {
    background: linear-gradient(to left, white, white 33%, #ececec 33%, #ececec);
    font-family: $title_family;
    font-size: 25px;
    font-weight: 700;
    color: #333;
    top: auto;
    bottom: calc(100% - 400px);
    padding-right: 35%;
    padding-bottom: 10px;
    border-top: 10px solid white;
  }

  .close_button_element {
    font-family: $text_family;
    font-size: 18px;
    font-weight: 700;
    color: white;
    background-color: $black;
    width: 50px;
    line-height: 50px;
    top: 0;
    right: 0;
  }

  .popup_carousel {
    .element_carousel_pictures {
      .exceded {
        height: 400px;

        img {
          width: 100%;
        }

        a {
          display: block !important;
          position: absolute;
          top: 270px;
          right: 0;
          background: radial-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
          padding: 10px;
          color: white;

          i.fa {
            font-size: 25px;
          }

          &:hover {
            color: $corporate_1;
          }
        }
      }
    }

    .flex-direction-nav {
      .flex-nav-prev, .flex-nav-next {
        width: 50px;
        height: 50px;
        @include transition(width, .6s);

        &:hover {
          width: 60px;

          a {
            &:before {
              margin-left: 10px;
            }
          }
        }

        a {
          background-color: white;
          @extend .icon-longarrow;

          &:before {
            font-family: "icomoon", sans-serif;
            font-size: 30px;
            color: $corporate_3;
            position: absolute;
            top: 50%;
            left: 50%;
            -webkit-transform: translate(-50%, -50%);
            -moz-transform: translate(-50%, -50%);
            -ms-transform: translate(-50%, -50%);
            -o-transform: translate(-50%, -50%);
            transform: translate(-50%, -50%);
            @include transition(margin, .6s);
          }

          img {
            display: none;
          }
        }
      }

      .flex-nav-prev {
        a:before {
          -webkit-transform: translate(-50%, -50%) rotate(-180deg);
          -moz-transform: translate(-50%, -50%) rotate(-180deg);
          -ms-transform: translate(-50%, -50%) rotate(-180deg);
          -o-transform: translate(-50%, -50%) rotate(-180deg);
          transform: translate(-50%, -50%) rotate(-180deg);
        }

        &:hover {
          a {
            &:before {
              margin-left: -10px;
            }
          }
        }
      }
    }
  }



  .popup_room_pictures {
    display: none !important;
  }

  .room_services {
    margin-left: 0 !important;
    width: 100% !important;
    padding: 0 !important;
    border-top-style: dashed;
    border-bottom-style: dashed;
    border-top-color: #f5f5f5;
    border-bottom-color: #f5f5f5;
    border-top-width: 2px;
    border-bottom-width: 2px;
    background: transparent !important;

    .service_element {
      border-right-style: dashed;
      border-right-color: #f5f5f5;
      border-right-width: 2px;
      height: auto !important;

      i {
        font-size: 25px;
        display: inline-block;
        padding-bottom: 0;
        margin-right: 10px;
        color: $corporate-3 !important;
      }

      .service_description {
        font-size: 14px;
        font-weight: 700;
      }
    }
  }

  .popup_room_description {
    margin-top: 10px;
    margin-bottom: 10px;
    font-size: 14px;
    line-height: 1.2;
    padding-top: 20px;
    padding-bottom: 20px;
    margin-left: 0 !important;
    width: 100% !important;
    font-family: $text_family;
    background: linear-gradient(to left, white, white 33%, #ececec 33%, #ececec);
    display: flex !important;
    justify-content: space-between;
    position: relative;

    > div {
      &:not(.list) {
        width: 400px;

        strong {
          font-weight: 700;
        }
      }
    }

    div.list {
      position: absolute;
      right: 21px;
      top: 20px;
      background: white;
    }

    .list {
      padding: 0 0 0 45px;
      width: 160px;

      li {
        font-weight: 700;
        padding: 5px 0;
        @extend .icon-longarrow;
        position: relative;

        &:before {
          position: absolute;
          top: 6;
          left: -22px;
          font-family: "icomoon", sans-serif;
          margin-right: 10px;
          color: $corporate_3;
        }
      }
    }
  }
}

div#step-1 .contTipoHabitacion .contFotoHabitacion a:before {
  display: none;
}


.popup_see_more_rooms_second {
  .room_popup .room_desc_wrapper {
    .room_name {
      font-family: $title_family!important;
    }

    .room_name {
      font-family: $text_family!important;
    }
  }
}

@import "booking_mobile/v1/mixin_colores";
@import "booking_mobile/booking";
@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');


$loader-size: 60px;
$loader-width: 250px;
$loader-color: linear-gradient(45deg, #5C1669 0%, #9A1F6E 100%);

@keyframes loading {
  0% {
    transform: scale(.1);
  }

  45% {
    left: ($loader-width - $loader-size) / 2;
    transform: scale(1);
  }

  90% {
    left: $loader-width - $loader-size * 0.1;
    transform: scale(.1);
  }
}

body.booking_process_mobile_v1 {

  $corporate_1: #1D4E43;
  $corporate_2: #1D4E43;
  $corporate_3: #1D4E43;
  $corporate_4: #333;

  $black: #222222;
  $lightgrey: #F5F5F5;
  $red: #EC6363;

  $font_1: 'Poppins', sans-serif;
  $font_2: 'Poppins', sans-serif;
  $fa6: "Font Awesome 6 Pro";

  $title_family: $font_1;
  $text_family: $font_2;

  color: $black;
  font-family: $font_1;

  .main_content_wrapper.step_2 .reservation_summary .option_selected .price:not(.with_custom_taxes_details),
  .main_content_wrapper.step_2 .reservation_summary .option_selected .price:not(.with_accomodation_tax_extra_info) {
    position: relative;
    display: block;
    width: 100%;
    margin-top: 30px;
  }

  #destination_cobrador_form {
    padding: 20px;
  }

  .main_content_wrapper {
    &.step_0 {
      .room_list {
        .room_pack_option {
          .room_name {
            &.content_title {
              .title {
                text-transform: none;
              }
            }
          }
          .rates_details_wrapper {
            .regime_item_content {
              .regime_price_wrapper {
                div.submit {
                  span {
                    font-weight: 500;
                    letter-spacing: 1px;
                    width: fit-content;
                  }
                }
              }
            }
            .prices_options {
              &.no_promo {
                .final_price {
                  font-weight: 700;
                }
              }
            }
          }
        }
      }
    }
  }

  .lock_board_wrapper {
    .discount_activated {
      .price_info_wrapper {
        .currencyValue,
        .monedaConv {
          color: $corporate_4;
        }
      }
    }
  }

  #main_modal {
    &.active {
      .body_modal {
        &.rooms_features_modal_wrapper {
          .body_modal_content {
            .modal_container {
              &.room_content {
                .icons_room {
                  .room_services {
                    .service_element {
                      i {
                        color: $corporate_1;
                      }
                      .service_description {
                        font-weight: 400;
                      }
                    }
                  }
                }
                .room_description {
                  font-weight: 400;
                }
                .content_title {
                  .title {
                    text-transform: none;
                    font-size: 14px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
   .main_content_wrapper.step_2  {
     .login_selection_wrapper .login_selection_btn,
     .personal_details_form_wrapper .personal_details_form .bottom_button_wrapper #btn-finish-booking {
        border-radius: 40px;
        font-weight: 500;
        letter-spacing: 1px;
        text-transform: uppercase;
     }
     .login_selection_wrapper {
       .login_selection_btn.login_option {
         color: white;
         background: $black;
       }
     }
   }

  input, button, select{
      font-family: $font_1;
  }

  #booking1-app-root {
    .rooms-list.is-mobile {
      .board-list-wrapper {
        .board-element-wrapper.board-for-users {
          .price-wrapper.toggle-club {
            .price-room {
              justify-content: flex-start;
            }
          }

          .promotion-percentage {
            right: 170px;
            top: 20px;
          }
        }
      }
    }
  }

  .btn.add_service, .btn.continue_booking{
    border-radius: 0px;
  }
}

header {
  padding: 10px 0;
  border-bottom: 1px solid rgba($corporate_3, 0.5);

  .header_top {
    .container {
      justify-content: flex-end;

      .logo_wrapper {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 112px;
      }

      .container_right {
        .selectors_wrapper {
          display: none;
        }

        .links_right {
          .link {
            color: $black;
            text-align: right;

            span {
              margin-bottom: 10px;
              display: block;
              font-family: $text_family;
              font-weight: 500;
              font-size: 14px;
              letter-spacing: 0;
            }

            a {
              color: $corporate_3;
              font-family: $text_family;
              font-weight: 400;
              font-size: 18px;

              i {
                font-weight: 400;
              }
            }
          }
        }
      }
    }
  }
}

#full_wrapper_booking {
  border-bottom: 1px solid rgba($corporate_3, 0.5);

  #booking.boking_widget_inline {
    .wrapper_booking_button {
      .submit_button {
        background: $corporate_1;
        border: 0;
        color: white !important;

        &::before {
          color: white;
        }

        &:hover {
          background: $corporate_2;
        }
      }
    }
  }
}

#wizard {
  background: white;
}

div#step-1 {
  .contTipoHabitacion {
    .very_asked_message {
      background: $corporate_1 !important;

      &::after {
        border-top: 15px solid $corporate_1 !important;
        border-left: 15px solid $corporate_1 !important;
        border-bottom: 15px solid $corporate_1 !important;
        border-left-color: $corporate_1 !important;
      }
    }

    .just_booking_message {
      background: rgba($corporate_1, .75) !important;

      &::after {
        border-top: 15px solid rgba($corporate_1, .75);
        border-left: 15px solid rgba($corporate_1, .75);
        border-right: 15px solid transparent;
        border-bottom: 15px solid rgba($corporate_1, .75);
      }
    }

    .descripcionHabitacion, .tipoHabitacion {
      font-weight: 300 !important;
    }
  }

  .precioNocheColumn {
    .priceValues {
      .promotion_percentage_square {
        transform: translateY(-15px);
        margin-right: -55px;
        background: $corporate_7 !important;

        .promotion_discount {
          border: 1px solid $corporate_1;
          z-index: 30;
          top: 20px;
          left: 50px;
          background: $corporate_7 !important;
        }
      }
    }

    .botonReservarColumn {
      z-index: 0;
    }
  }
}

div#step-3 {
  #personal-details-form {
    .booking_details_prices_wrapper {
      .booking-button {
        margin-top: 20px;
      }
    }
  }
}

.booking-box--search .booking-box__content .booking-search-results__new-search,
.hidden_booking_summary .booking-search-results__new-search {
  .booking-button {
    &::before {
      background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-villa-palmar/files/calendario-green.svg) !important;
    }

    &:hover {
      &::before {
        background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-garza-blanca/files/calendario-white.png) !important;
      }
    }
  }
}

footer {
  .container {
    .footer_top {
      border-top: 1px solid rgba($corporate_3, 0.5);
      border-bottom: 1px solid rgba($corporate_3, 0.5);

      .inner_wrapper {
        .logo_wrapper {
          width: 96px;
        }
      }
    }

    .footer_middle {
      .footer_info, .phone_wrapper {
        font-family: $text-family;
        font-weight: 400;

        span, .title_social {
          font-family: $text-family;
          font-weight: 500;
          color: $green;
        }

        a {
          font-family: $text-family;
          color: $black;
          font-weight: 400;
        }

        .social_wrapper {
          .social_links {
            i {
              color: $green;
            }
          }
        }
      }

      .awards_wrapper {
        width: 425px;

        .awards_title {
          font-family: $text-family;
          font-weight: 500;
          color: $green;
        }

        img {
          max-height: 38px;
        }
      }
    }

    .footer_bottom {
      background: #1f2e1c url(https://www.villapalmarcancun.com/lib/dist/img/assets/leaves-of-palm-tree.png);
      background-attachment: fixed;

      .inner_wrapper {
        .footer-textoslegales-copyright {
          font-family: $text-family;
          border-top: 1px solid rgba(#ffffff, .5);
        }
      }
    }
  }
}
.double_button_wrapper {
  .btn {
    &.re-search_button {
      background: $corporate_1 !important;
    }
  }
}

.main_content_wrapper.wizard {
  background: white !important;
}

.main_content_wrapper.step_0 {
  .room_list {
    .room_pack_option {
      .labels_wrapper {
        .very_asked {
          background: $corporate_1;
          color: white;
        }

        .just_booking {
          background: rgba($corporate_1, 0.75);
          color: white;
        }
      }
    }
  }

  .room_list {
    .room_pack_option {
      .room_name.content_title {
        .title {
          font-family: $title_family;
          font-weight: 300;
        }
      }
    }
  }
}

footer {
  .container {
    .footer_top {
      border-top: 1px solid rgba($corporate_3, 0.5);
      border-bottom: 1px solid rgba($corporate_3, 0.5);

      .inner_wrapper {
        .logo_wrapper {
          width: 100px;
        }
      }
    }

    .footer_middle {
      .footer_info, .phone_wrapper {
        font-family: $text-family;
        font-weight: 400;

        span, .title_social {
          font-family: $text-family;
          font-weight: 500;
          color: $green;
        }

        a {
          font-family: $text-family;
          color: black;
          font-weight: 400;
        }

        .social_wrapper {
          i {
            color: $green;
          }
        }
      }

      .awards_wrapper {
        .awards_title {
          font-family: $text-family;
          font-weight: 500;
          color: $green;
        }

        img {
          max-height: 36px;
        }
      }
    }

    .footer_bottom {
      background: #1f2e1c url(https://www.villapalmarcancun.com/lib/dist/img/assets/leaves-of-palm-tree.png);
      background-size: cover;
    }
  }
}
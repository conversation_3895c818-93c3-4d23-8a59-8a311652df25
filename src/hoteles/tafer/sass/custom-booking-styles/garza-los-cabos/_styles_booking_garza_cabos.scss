$corporate_3: #1B539D;
$corporate_4: #081D4F;
$text_family: "lato", sans-serif;

header {
  padding: 35px 0;
  border-bottom: 1px solid rgba($corporate_3, 0.5);

  .header_top {
    padding: 0;

    .container {
      justify-content: flex-end;

      .logo_wrapper {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 240px;
      }

      .container_right {
        .selectors_wrapper {
          display: none;
        }

        .links_right {
          text-align: right;

          span {
            margin-bottom: 10px;
            display: block;
            font-family: $text_family;
            font-weight: 700;
            text-transform: none;
          }

          a {
            color: $corporate_3;
            font-family: $text_family;
            font-weight: 500;
          }
        }
      }
    }
  }
}

footer {
  .container {
    .footer_top {
      border-top: 0;
      border-bottom: 1px solid rgba($corporate_3, .5);

      .logo_wrapper {
        width: 192px;
      }
    }

    .footer_middle {
      box-sizing: border-box;
      width: 100%;
      padding: 40px 10%;
      background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-garza-los-cabos/files/texture-bg-footer.png);
      background-size: cover;
      background-repeat: no-repeat;

      .footer_info, .phone_wrapper {
        font-family: $text-family;
        font-weight: 400;

        span, .title_social {
          font-family: $text-family;
          font-weight: bold;
        }

        a {
          font-family: $text-family;
          color: $corporate_3;
          font-weight: 400;
        }

        .social_wrapper {
          i {
            color: $corporate_4;
          }
        }
      }

      .awards_wrapper {
        width: 443px;

        .awards_title {
          font-family: $text-family !important;
          font-weight: bold;
        }
      }
    }

    .footer_bottom {
      .inner_wrapper {
        .footer-textoslegales-copyright {
          font-family: $text-family;
          border-top: 1px solid rgba(#ffffff, .3);
        }
      }
    }
  }
}
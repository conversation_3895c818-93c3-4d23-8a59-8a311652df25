header {
  padding: 20px 0;
  border-bottom: 1px solid rgba($corporate_3, 0.5);

  .header_top {
    .container {
      justify-content: flex-end;

      .logo_wrapper {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 97px;
      }

      .container_right {
        .selectors_wrapper {
          display: none;
        }

        .links_right {
          .link {
            color: $black;
            text-align: right;

            span {
              margin-bottom: 10px;
              display: block;
              font-family: $text_family;
              font-weight: 500;
              font-size: 14px;
              letter-spacing: 0;
            }

            a {
              color: $corporate_3;
              font-family: $text_family;
              font-weight: 400;
              font-size: 18px;

              i {
                font-weight: 400;
              }
            }
          }
        }
      }
    }
  }
}

#full_wrapper_booking {
  border-bottom: 1px solid rgba($corporate_3, 0.5);

  #booking.boking_widget_inline {
    .wrapper_booking_button {
      .submit_button {
        background: $corporate_1;
        border: 0;
        color: white !important;
        border-radius: 8px;

        &::before {
          color: white;
        }

        &:hover {
          background: $corporate_2;
        }
      }
    }
  }
}

#wizard {
  background: white;
}

div#step-1 {
  .contTipoHabitacion {
    .very_asked_message {
      background: $corporate_1 !important;

      &::after {
        border-top: 15px solid $corporate_1 !important;
        border-left: 15px solid $corporate_1 !important;
        border-bottom: 15px solid $corporate_1 !important;
        border-left-color: $corporate_1 !important;
      }
    }

    .just_booking_message {
      background: rgba($corporate_1, .75) !important;

      &::after {
        border-top: 15px solid rgba($corporate_1, .75);
        border-left: 15px solid rgba($corporate_1, .75);
        border-right: 15px solid transparent;
        border-bottom: 15px solid rgba($corporate_1, .75);
      }
    }

    .descripcionHabitacion, .tipoHabitacion {
      font-weight: 300 !important;
    }

    .contDescHabitacion.with_rates_dropdown {
      .room_from_price_wrapper {
        .prices_dropdown_toggle {
          border-radius: 8px;
        }
      }
    }

    .preciosHabitacion {
      table.listadoHabsTarifas {
        .botonReservarColumn {
          .booking-button {
            border-radius: 8px;
          }
        }
      }
    }
  }

  .precioNocheColumn {
    .priceValues {
      .promotion_percentage_square {
        transform: translateY(-15px);
        margin-right: -55px;
        background: $corporate_3 !important;

        .promotion_discount {
          border: 1px solid $corporate_1;
          z-index: 30;
          top: 20px;
          left: 50px;
          background: $corporate_3 !important;
        }
      }
    }

    .botonReservarColumn {
      z-index: 0;
    }
  }
}

div#step-2 {
  .additional_services_total_wrapper {
    .perform_additional_services_booking {
      border-radius: 8px;
    }
  }
}

div#step-3 {
  #personal-details-form {
    .booking_details_prices_wrapper {
      .booking-button {
        border-radius: 8px;
        margin-top: 20px;
      }
    }
  }
}

.booking-box--search .booking-box__content .booking-search-results__new-search,
.hidden_booking_summary .booking-search-results__new-search {
  .booking-button {
    border-radius: 8px !important;

    &::before {
      background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-mousai-pv/files/calendario-gold.svg) !important;
    }

    &:hover {
      &::before {
        background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-garza-blanca/files/calendario-white.png) !important;
      }
    }
  }
}
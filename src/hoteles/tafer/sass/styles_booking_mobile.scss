@import "booking_mobile/booking.scss";
@import "booking_mobile/v1/mixin_colores";
@import "plugins/fontawesome5pro";
@import "plugins/fonts";

@keyframes swinging {
  0% {
    transform: rotate(25deg);
  }
  50% {
    transform: rotate(-25deg)
  }
  100% {
    transform: rotate(25deg);
  }
}

@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap');

@font-face {
  font-family: "itc-galliard";
  src: url("https://use.typekit.net/af/4dbeb4/00000000000000007735bba9/30/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n4&v=3") format("woff2"), url("https://use.typekit.net/af/4dbeb4/00000000000000007735bba9/30/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n4&v=3") format("woff"), url("https://use.typekit.net/af/4dbeb4/00000000000000007735bba9/30/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n4&v=3") format("opentype");
  font-display: swap;
  font-style: normal;
  font-weight: 400;
  font-stretch: normal;
}


body.booking_process_mobile_v1 {
  $corporate_1: #DC3728;
  $corporate_2: #750806;
  $corporate_3: #1B539D;
  $corporate_3_2: #1B539D66;
  $corporate_4: #081D4F;
  $corporate_5: #F1F4F9;
  $corporate_6: #F8D7D4;
  $corporate_club: #AB9766;
  $corporate_club_2: #85744C;
  $grey: #F8F8F8;
  $grey_2: #444444;
  $grey_3: #B4B4B4;
  $white: #FFFFFF;
  $white_2: #F2EFE7;
  $black: #121317;
  $black_2: #081D4F;
  $black_3: #222222;
  $black_4: #303948;
  $black_5: #23414D;
  $red: #EC6363;
  $border_offer: #AB9766;
  $gold: #84754D;
  $text_family: "lato", sans-serif;
  $title_family: "itc-galliard", serif;

  font-family: $text_family;
  color: $black;

  @import "garza-booking-mobile-styles/_club_styles.scss";
  @import "garza-booking-mobile-styles/club_v4_styles_mobile";
  @import "garza-booking-mobile-styles/_styles_mobile";
  @import "garza-booking-mobile-styles/booking1_react";

  &.black_friday {
    @import "garza-booking-styles/campaign_styles";

    #login_wrapper_element.version_banner_v1.v4 {
      .users_buttons_wrapper {
        .join_button_wrapper {
          background: white !important;
        }
      }
    }

    #logged_user_info_wrapper {
      .logout_button_wrapper {
        background: white;

        i {
          color: $black;
        }
      }
    }
  }

  overflow-x: hidden;

  &.currency_MXN{
    .main_content_wrapper{
      .room_list{
        .room_pack_option{
          .rates_details_wrapper{
            .regime_item_content{
              .regime_description{
                .precioNocheColumn{
                  .price_container{
                     grid-template-columns: repeat(3, auto)!important;
                  }
                }
              }
            }
          }
        }
      }
    }

  }

 &:has(.precioNocheColumn .prices_detail_popup.active){
   .daily_prices_club_rate{
     .prices_detail_popup.active{
       display: none!important;
     }
   }
 }


}

@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

body.booking_process_mobile_v1.tafer-palmar-azul {
  $corporate_1: #84A06F;
  $corporate_2: #6A8059;
  $corporate_3: #468299;
  $corporate_4: #468299;
  $corporate_5: #EDF3F5;
  $corporate_6: #F8D7D4;
  $corporate_club: #AB9766;
  $corporate_club_2: #85744C;
  $black: #23414D;
  $black_2: $black;
  $black_3: $black;
  $red: #EC6363;
  $border_offer: #AB9766;
  $gold: #84754D;
  $title_family: "Playfair Display", serif;
  $text_family: "Roboto", sans-serif;

  font-family: $text_family;
  color: $black;

  @import "garza-booking-mobile-styles/_club_styles.scss";
  @import "garza-booking-mobile-styles/_styles_mobile";


  .loading_animation_popup {
    .center_xy {
      .spincircle {
        height: 120px;

        video {
          height: 100px;
        }
      }
    }
  }

  .main_content_wrapper.step_2 {
    .login_selection_wrapper {
      .login_selection_btn {
        border-color: $corporate_2;
        color: $corporate_2;

        &.active {
          background: $corporate_2;
          color: white;
        }
      }
    }
  }

  #main_modal.active {
    .body_modal.rooms_features_modal_wrapper {
      .body_modal_content {
        .modal_container.room_content {
          .icons_room {
            .room_services {
              .service_element {
                i {
                  color: $corporate_4;
                }
              }
            }
          }
        }
      }
    }
  }
  .main_content_wrapper.step_0{
    .room_list{
      .room_pack_option{
        .rates_details_wrapper{
          .regime_item_content{
            .prices_options{
              .precioNocheColumn{
                .prices_detail_popup{
                  top: -70px!important;
                }
              }
            }
          }
        }
      }
    }
  }

}

body.booking_process_mobile_v1.tafer-lemuria {
  $corporate_1: rgb(131,118,82);
  $corporate_2: $corporate_1;
  $corporate_3: $corporate_1;
  $corporate_4: #081D4F;
  $corporate_5: #F1F4F9;
  $corporate_6: #F8D7D4;
  $corporate_club: #AB9766;
  $corporate_club_2: #85744C;
  $grey: #F8F8F8;
  $black: rgb(42,67,74);
  $black_2: rgb(42,67,74);
  $black_3: rgb(42,67,74);
  $red: #EC6363;
  $border_offer: #AB9766;
  $gold: #84754D;
  $text_family: "lato", sans-serif;
  $title_family: "itc-galliard", serif;

  font-family: $text_family;
  color: $black;

  @import "garza-booking-mobile-styles/_club_styles.scss";
  @import "garza-booking-mobile-styles/club_v4_styles_mobile";
  @import "garza-booking-mobile-styles/_styles_mobile";

  &.black_friday {
    @import "garza-booking-styles/campaign_styles";

    #login_wrapper_element.version_banner_v1.v4 {
      .users_buttons_wrapper {
        .join_button_wrapper {
          background: white !important;
        }
      }
    }

    #logged_user_info_wrapper {
      .logout_button_wrapper {
        background: white;

        i {
          color: $black;
        }
      }
    }
  }

  .double_button_wrapper {
    .btn {
      color: $corporate_1;
      border: 1px solid $corporate_1;

      &::before {
        color: $corporate_1;
      }
    }
  }

  overflow-x: hidden;

  header {
    background: rgb(0,0,0);

    .icon_toggle_wrapper, .icon_toggle_wrapper::before, .icon_toggle_wrapper::after {
      background: white;
    }
  }

  footer {
    background: #000;
    color: white;

    a, .legal_info_wrapper a {
      color: white;
    }
  }

  .loading_animation_popup {
    .center_xy {
      .spincircle {
        height: 76px;
        width: 270px;

        img {
          object-fit: contain;
          max-width: 270px;
        }
      }
    }
  }
}

body.booking_process_mobile_v1.tafer-garza-blanca,
body.booking_process_mobile_v1.tafer-lemuria {
  .booking_widget_wrapper {
    #entry_date_popup, #departure_date_popup {
      z-index: *********;
    }
  }

  .loading_animation_popup {
    z-index: *********;
  }
}

@media (max-width: 380px) {
  body .mail_wrapper.v2 .right_side .wrapper .form_content {
    padding: 15px;
  }

  .mail_wrapper.v2 .left_side .wrapper .info_wrapper {
    padding: 30px 15px !important;
  }
}


body.booking_process_mobile_v1.tafer-garza-blanca{
  &.currency_MXN{
     .main_content_wrapper.step_0{
    .room_list{
      .room_pack_option{
        .rates_details_wrapper{
          .regime_item_content{
            .regime_price_wrapper{
              .prices_detail_tooltip{
                top: 10px;
              }
              .lock_board_wrapper:not(.toggle_discount){
                .precioGeneralDiv{
                  margin-left: -2px;
                }
              }
            }
          }
        }
      }
    }
  }
  }
  .main_content_wrapper{
    .room_list{
      .room_pack_option{
        .rates_details_wrapper{
          .regime_item_content{
            .regime_price_wrapper{
              .tax_inc_wrapper_info{
                left: 10px!important;
              }
            }
            .regime_description{
              z-index: 1;
            }
            .prices_options{
              .precioNocheColumn{
                &.daily_prices_normal_rate{
                  .prices_detail_popup.tax_inc_wrapper_info{
                    top: -6px;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  .modal_wrapper.users_modal_v2{
    .modal_content{
      padding-bottom: 50px;
    }
  }
  #main_modal.active{
    z-index: *********;
  }
}

@import url("https://use.typekit.net/dvi7zga.css");

body.booking_process_mobile_v1.tafer-mousai-pv,
body.booking_process_mobile_v1.tafer-mousai {
  $corporate_1: #520A75;
  $corporate_2: #282361;
  $corporate_3: #85744C;
  $corporate_4: $corporate_3;
  $corporate_5: white;
  $corporate_6: #F8D7D4;
  $corporate_club: #AB9766;
  $corporate_club_2: $corporate_3;
  $black: #11100e;
  $black_2: $black;
  $black_3: $black;
  $red: #EC6363;
  $border_offer: #AB9766;
  $gold: $corporate_3;
  $title_family: "Playfair Display", serif;
  $text_family: "museo-sans", sans-serif;

  font-family: $text_family;
  color: $black;

  @import "garza-booking-mobile-styles/_club_styles.scss";
  @import "garza-booking-mobile-styles/_styles_mobile";
  @import "custom-booking-styles/mousai-vallarta/styles_booking_mobile_mousai_vallarta";
}

body.booking_process_mobile_v1.tafer-garza-los-cabos {
  @import "custom-booking-styles/garza-los-cabos/styles_booking_mobile_garza_cabos";
}


@import url("https://use.typekit.net/yxc6djy.css");
body.booking_process_mobile_v1.tafer-sierra {
  $corporate_1: #17623F;
  $corporate_2: #043C2E;
  $corporate_3: #864336;
  $corporate_4: $corporate_3;
  $corporate_5: rgb(245, 247, 250);
  $corporate_6: #EOE7E3;
  $black: #000000;
  $black_2: $black;
  $black_3: $black;
  $red: #EC6363;
  $green: #4c9d78;
  $border_offer: #AB9766;
  $corporate_club: #AB9766;
  $corporate_club_2: #85744C;
  $gold: $corporate_3;

  $title_family: "questa-grande", serif;
  $text_family: "yorkten-slab-normal", sans-serif;
  font-family: $text_family;
  color: $black;

  @import "garza-booking-mobile-styles/_club_styles.scss";
  @import "garza-booking-mobile-styles/_styles_mobile";
  @import "custom-booking-styles/sierra-lago/styles_booking_mobile_sierra";
}


@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@import url("https://use.typekit.net/qft3sck.css");

body.booking_process_mobile_v1.tafer-villa-palmar {
  $corporate_1: #468299;
  $corporate_2: #468299d6;
  $corporate_3: #8CA771;
  $corporate_4: $corporate_3;
  $corporate_5: rgb(245, 247, 250);
  $corporate_6: #B79E79;
  $black: #1F2E1C;
  $black_2: $black;
  $black_3: $black;
  $red: #EC6363;
  $green: #6a8153;
  $border_offer: #AB9766;
  $corporate_club: #AB9766;
  $corporate_club_2: #85744C;
  $gold: $corporate_3;

  $title_family: "ivypresto-display", serif;
  $text_family: 'Roboto', sans-serif;
  font-family: $text_family;
  color: $black;

  @import "garza-booking-mobile-styles/_club_styles.scss";
  @import "garza-booking-mobile-styles/_styles_mobile";
  @import "custom-booking-styles/palmar-cancun/styles_booking_mobile_palmar";
}
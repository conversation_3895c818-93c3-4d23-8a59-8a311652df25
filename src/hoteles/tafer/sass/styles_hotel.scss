@import "booking/booking_colors";
@import "booking/booking_all_styles";
@import "plugins/mixins";
@import "plugins/fontawesome5pro";
@import "plugins/fonts";
@import "booking/booking_process_v1/booking_header";
@import "mixins";

@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap');

@font-face {
  font-family: "itc-galliard";
  src: url("https://use.typekit.net/af/4dbeb4/00000000000000007735bba9/30/l?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n4&v=3") format("woff2"), url("https://use.typekit.net/af/4dbeb4/00000000000000007735bba9/30/d?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n4&v=3") format("woff"), url("https://use.typekit.net/af/4dbeb4/00000000000000007735bba9/30/a?primer=7cdcb44be4a7db8877ffa5c0007b8dd865b3bbc383831fe2ea177f62257a9191&fvd=n4&v=3") format("opentype");
  font-display: swap;
  font-style: normal;
  font-weight: 400;
  font-stretch: normal;
}


@keyframes swinging {
  0% {
    transform: rotate(25deg);
  }
  50% {
    transform: rotate(-25deg)
  }
  100% {
    transform: rotate(25deg);
  }
}


body.booking_process_version_1 {
  $black: #121317;
  $black_2: #081D4F;
  $corporate_1: #DC3728;
  $corporate_2: #750806;
  $corporate_3: #1B539D;
  $corporate_4: #081D4F;
  $corporate_4_2: #081D4F33;
  $corporate_5: rgb(245, 247, 250);
  $corporate_6: #F2EFE7;
  $corporate_7: #F8D7D4;
  $white: #FFFFFF;
  $grey: #F8F8F8;
  $grey_2: #A7A8A9;
  $red: #EC6363;
  $green: #228B22;
  $corporate_club: #AB9766;
  $corporate_club_2: #85744C;
  $gold: #84754D;
  $grey2: #1c1c1c1a;

  $title_family: "itc-galliard", serif;
  $text_family: "lato", sans-serif;
  font-family: $text_family;

  @import "garza-booking-styles/garza_styles";
  @import "garza-booking-styles/booking1_react_styles";

  &.black_friday {
    @import "garza-booking-styles/campaign_styles";
  }

  div#step-3 {
    #personal-details-form {
      .personal_details_payment_wrapper {
        display: flex;
        flex-direction: column;

        .personal_details_grid {
          order: 01;

          .booking-box {
            .booking-box__title {
              color: $corporate_4;
            }
          }

          .billing_adress_title {
            color: $corporate_4;
          }
        }

        .optional_comments_wrapper {
          order: 03;

          #optional_comments {
            strong {
              color: $corporate_4;
            }
          }
        }

        .booking-form-field#comments_wrapper {
          order: 03;
        }

        .payment_type_grid {
          order: 04;

          .booking-box {
            .booking-box__title {
              color: $corporate_4;
            }

            .clearfix {
              label {
                color: $corporate_4 !important;
              }
            }
          }
        }

        .form_bottom_content {
          order: 05;
        }
      }
    }

    .form_bottom_content {
      .total_booking_wrapper {
        padding: 20px;
        width: 100%;
        margin: 0 auto;

        &::before {
          content: "";
          position: absolute;
          background: $grey2;
          width: 95%;
          height: 1px;
          left: 0;
          right: 0;
          margin: 40px auto 0px;
        }

        .exchange_message, .sub_total_field, .tax_field {
          display: none;
        }

        .total_field {
          display: flex;
          font-size: 18px;
          color: $corporate_4;

          .label_nam {
            width: 100%;
          }

          .first_currency, .currencyValue {
            width: fit-content;
          }

        }
      }
    }
  }


  div#step-1 {
    table.listadoHabsTarifas {
      td {
        .marketing_logo_info_wrapper.hover {
          z-index: 70;
        }
      }
    }
  }

  .fancybox-wrap.fancy-booking-search_v2 {
    background: rgb(240, 245, 249);

    .container_popup_booking {
      width: 900px;
      top: 55%;

      .description_top_popup_booking {
        video, img {
          height: 200px;
        }
      }

      .description_bottom_popup_booking {
        font-family: $text_family;
        font-size: 15px;
        letter-spacing: 0.9px;
        line-height: 19px;
        text-transform: none;
        text-align: center;

        div {
          color: #222222 !important;
          font-weight: 400;
          letter-spacing: 0.35px;
          line-height: 24px;
        }

        .advantages_traveler_popup {
          margin-top: 30px;
        }
      }

      .gif_wrapper {
        display: none;
      }
    }
  }

  #currencyDiv {
    display: none;
  }

  .site-main {
    margin-bottom: 0 !important;
  }

  &.shopping_cart_process_v2 {
    .shopping_cart_summary.v2 {
      font-family: $text_family !important;
      z-index: 1000;

      .t_light {
        font-weight: 400;
      }

      .full_body_wrapper {
        .footer_wrapper {
          .total_text {
            width: auto;
            margin-bottom: 10px;
          }
        }
      }
    }

    .site-main {
      #wizard {
        &.step_2 {
          #step-3 {
            #personal-details-form {
              .booking_details_prices_wrapper {
                .booking_3_hotel_name {
                  .hotel_name {
                    margin-left: 25px;
                  }
                }

                .booking_details_wrapper {
                  .rooms_breakdown_wrapper {
                    .room_selected_element {
                      .room_details_wrapper {
                        .rate_name {
                          color: white;
                        }
                      }
                    }
                  }

                  .total_booking_wrapper {
                    .total_label {
                      width: 40%;
                      color: white;
                    }

                    .total_price {
                      width: 60%;
                      color: white;
                      text-align: right;
                      font-size: 24px;
                    }
                  }
                }
              }
            }

            .comments_call {
              .botonera {
                #save_comments {
                  background: white;
                  border: 0;
                  box-shadow: none;
                  height: 40px;
                  display: block;
                  margin: 20px auto 0;
                  text-transform: uppercase;
                  padding: 0 20px;
                }
              }
            }
          }
        }
      }
    }
  }

  #wizard {
    background: $corporate_5;
    position: relative;

    div#step-1, div#step-2, div#step-3 {
      background: transparent;
      position: relative;
    }
  }

  .site-footer {
    #footer_bottom_text {
      p, a, div {
        font-size: 14px;
        letter-spacing: .77px;
        line-height: 24.8px;
        font-weight: 300;
        color: $black;
      }
    }
  }

  &.fr,
  &.de {
    font-size: 13px;

    .booking_engine_wrapper_process {
      &.has_babies {
        #booking.boking_widget_inline .stay_selection .date_day,
        #booking.boking_widget_inline .guest_selector .placeholder_text,
        #booking.boking_widget_inline .guest_selector .placeholder_text {
          &,
          span {
            font-size: 18px !important;
            letter-spacing: 0;
          }
        }
      }
    }

    div#step-1 .contTipoHabitacion .preciosHabitacion table.listadoHabsTarifas .botonReservarColumn .booking-button {
      font-size: 15px;
    }

    div#step-3 #personal-details-form .personal_details_payment_wrapper .lock_rates_wrapper .lock_content {
      width: 50%;
    }
  }

  div#step-1 .total_price_wrapper {
    .total_price {
      background-color: $corporate_club;
    }

    .rooms_submit_button {
      background-color: $corporate_1;
      color: white;
    }
  }

  // Ratecheck
  .mail_wrapper.v2.text_left .left_side .check_privacy label {
    font-size: 12px;
  }

  .help-block span[for=RescueBookingPrivacy] {
    position: absolute;
    transform: translateX(-440px);
    top: 360px;
  }

  &.black_friday {
    .cards_banners_wrapper {
      bottom: 300px;
    }
  }

  .matcher-start-wrapper {
    z-index: 1000 !important;
  }

  .calendar_app {
    .price_calendar_wrapper {
      font-family: "lato", sans-serif;

      .full_container {
        .bottom_wrapper {
          .top {
            .info_currency_wrapper {
              .left_wrapper {
                .legend {
                  .icon:not(.dispo) {
                    background-repeat: no-repeat;
                    background-size: contain;
                    background-color: transparent;

                    &.high_occupancy {
                      background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-garza-blanca/files/high-occupancy.svg);
                    }

                    &.no_dispo {
                      background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-garza-blanca/files/sin-dispo.svg);
                    }

                    &.min_stay {
                      background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-garza-blanca/files/estancia-minima.svg);
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }

  &.iPad_user {
    div#step-1 {
      .contTipoHabitacion {
        .preciosHabitacion {
          .listadoHabsTarifas {
            .lock_board {
              .lock_board_wrapper {
                .lock_top_elements {
                  left: 0 !important;

                  .lock_mini_label {
                    font-size: 9px !important;
                  }
                }

                &:not(.toggle_discount) {
                  .lock_ico {
                    left: 10px !important;
                  }
                }
              }
            }
          }
        }
      }

      .precioNocheColumn {
        .priceValues {
          .promotion_percentage_square {
            transform: translate(25px, -15px) !important;
            margin-right: 0 !important;
          }
        }
      }
    }

    div#step-3 {
      #personal-details-form {
        .personal_details_grid {
          .booking-box__content {
            .booking-form-field {
              padding: 0 5px !important;
            }
          }
        }
      }
    }

    .modal_wrapper {
      &.login_information_wrapper {
        .modal_content {
          #popup_login_information {
            #login_form_wrapper_v1 {
              &.v3 {
                .login_block {
                  .login_form {
                    .login_data_block {
                      #password_input {
                        font-size: 11px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }

    &.en {
      div#step-1 {
        .booking-box--search, .hidden_booking_summary {
          .booking-box__content {
            .title_booking_breakdown {
              padding: 17px 10px 0 10px !important;
            }

            .booking-search-results__search-data {
              padding-left: 30px !important;
              padding-right: 100px !important;
            }
          }
        }
      }
    }

    .fancybox-wrap.calendar_v2_selection_fancybox {
      .fancybox-inner {
        height: initial !important;
      }
    }

    div#step-1 .regimen_tr_element.regimen_for_users .precioNocheColumn .priceValues .promotion_percentage_square {
      margin-right: 0 !important;
      transform: translate(46px, -15px) !important;
    }
  }

  @supports (-moz-appearance: none) {
    /* Styles for Firefox */
    div#step-1 {
      table.listadoHabsTarifas {
        td.regimenColumn {
          .regimenColumnContent {
            margin-top: 25px;
          }
        }
      }
    }
  }

  &:not(.tafer-palmar-azul) {
    @media (max-width: 1550px) {
      .booking_engine_wrapper_process {
        #booking.boking_widget_inline {
          .room_list_wrapper {
            left: 365px !important;
          }
        }
      }
    }
  }
}


@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');

body.booking_process_version_1.tafer-palmar-azul {
  $black: #23414D;
  $black_2: $black;
  $corporate_1: #84A06F;
  $corporate_2: #6A8059;
  $corporate_3: #468299;
  $corporate_4: #468299;
  $corporate_5: #EDF3F5;
  $corporate_6: #F2EFE7;
  $corporate_7: #F8D7D4;
  $corporate_8: #38687A;
  $grey: #F8F8F8;
  $red: #EC6363;
  $green: #228B22;
  $corporate_club: #AB9766;
  $corporate_club_2: #85744C;
  $gold: #84754D;

  $title_family: "Playfair Display", serif;
  $text_family: "Roboto", sans-serif;
  font-family: $text_family;

  @import "garza-booking-styles/garza_styles";

  .fancybox-wrap.fancy-booking-search_v2 {
    background: $corporate_5;

    .description_top_popup_booking {
      video {
        height: 100px;
        margin-bottom: 20px;
      }
    }

    .description_bottom_popup_booking {
      div {
        color: $black !important;
      }
    }
  }

  header {
    a, .link, span {
      font-family: $text_family;
      color: $corporate_3;
    }

    i {
      color: $corporate_3;
    }

    .currency_selector_label span, .selected_language_code, .language_selector div, .currency_selector .option {
      font-family: $text_family !important;
      color: $corporate_3 !important;
    }

    .header_top .container .logo_wrapper {
      width: 175px;
    }

    .header_menu {
      a.header_menu_link {
        text-transform: none
      }
    }
  }

  #wizard {
    background: $corporate_5;

    div .clearfix .booking-box--search .booking-box__content .booking-search-results__search-data .booking-title-info,
    div .clearfix .booking-box--search .booking-box__content .booking-search-results__search-data .notranslate {
      font-weight: bold;
    }

    div .clearfix .booking-box--search .booking-box__content .booking-search-results__new-search .booking-button,
    .hidden_booking_summary .center_container .booking-search-results .booking-search-results__new-search .booking-button {
      border-color: $black;
      color: $black !important;

      &::before {
        background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-palmar-azul/files/calendario_green.svg);
      }

      &:hover {
        background: $corporate_4 !important;
        color: white !important;

        &::before {
          background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-garza-blanca/files/calendario-white.png);
        }
      }
    }

    .wizard_steps {
      ul.actual_wizard_step {
        .step {
          &.actual {
            .number_step {
              background: transparent;

              span {
                background: $corporate_3;
              }
            }
          }

          &.done {
            .number_step {
              &::after {
                background: $corporate_3;
              }
            }
          }
        }
      }
    }
  }

  .booking_engine_wrapper_process {
    #booking.boking_widget_inline {
      .stay_selection, .guest_selector {
        .date_day, .placeholder_text, .placeholder_text span {
          font-weight: 400;
          color: $black;
        }
      }

      .wrapper_booking_button {
        .submit_button:hover {
          background: $corporate_4;
          border-color: $corporate_8;
        }
      }
    }
  }

  div#step-1 .contTipoHabitacion .contDescHabitacion .room_description_name_wrapper .cabeceraNombreHabitacion .tipoHabitacion {
    font-weight: bold;
  }

  div#step-1 .contTipoHabitacion .room_services .service_element .service_description {
    font-size: 12px;
  }

  div#step-1 .total_price_wrapper {
    .total_price {
      background-color: $corporate_club !important;
    }

    .rooms_submit_button {
      background-color: $corporate_1 !important;
      color: white;
    }
  }

  div#step-2 {
    .all_additional_services_wrapper.with_tabs_scroll {
      .category_wrapper {
        .additional_services_wrapper {
          .additional_service_element {
            background: $corporate-5;

            &::after {
              background: $corporate-4;
            }

            .service_content {
              .service_title {
                font-weight: bold;
                font-size: 18px;
              }
            }

            .service_selection_wrapper {
              .add_service_button {
                i, .label {
                  color: $corporate_1;
                }
              }
            }
          }
        }
      }
    }
  }

  div#step-3 {
    #personal-details-form {
      .personal_details_payment_wrapper {
        .personal_details_grid {
          .booking-box__content {
            .login_selection_wrapper {
              width: 100%;
              display: flex;
              flex-direction: column;
              order: 00;

              .login_selection_btn {
                border-color: $corporate_2 !important;
                color: $corporate_2 !important;

                &:hover, &.active {
                  background: $corporate_1 !important;
                  color: white !important;
                }
              }
            }

            .booking-form-field {
              &.firstName {
                order: 01;
              }

              &.lastName1 {
                order: 02;
              }

              &.country {
                order: 07;

                .select2-container {
                  width: 100% !important;
                }
              }

              &.personalID {
                order: 04;
              }

              &.email_field_wrap {
                order: 03;
              }

              &.password_club {
                order: 05;
              }

              &.telephone {
                order: 06;
              }

              &.recover_password {
                flex-basis: 52% !important;
                margin-bottom: 15px;
                margin-top: -10px;
                text-align: center;
              }

              &.repeat_password_club, &.personalID, &.emailConfirmation {
                display: none !important;
              }
            }
          }
        }
      }

      .booking_details_prices_wrapper {
        background: $black;

        .booking_3_hotel_name {
          .hotel_name {
            color: white;
          }
        }

        .booking-button {
          border: 1px solid $corporate_2;
        }
      }
    }
  }
}

body.booking_process_version_1.tafer-lemuria {
  $black: rgb(42, 67, 74);
  $black_2: $black;
  $corporate_1: rgb(131, 118, 82);
  $corporate_2: $corporate_1;
  $corporate_3: $corporate_1;
  $corporate_4: #081D4F;
  $corporate_5: #EDF3F5;
  $corporate_6: #F2EFE7;
  $corporate_7: #F8D7D4;
  $corporate_8: #38687A;
  $grey: #F8F8F8;
  $red: #EC6363;
  $green: #228B22;
  $corporate_club: #AB9766;
  $corporate_club_2: #85744C;
  $gold: #84754D;

  $title_family: "Playfair Display", serif;
  $text_family: "Roboto", sans-serif;
  font-family: $text_family;

  @import "garza-booking-styles/garza_styles";

  header {
    background: rgb(0, 0, 0);

    a, div, span {
      color: white !important;
    }

    .container_right {
      .selectors_wrapper {
        .currency_selector_wrapper, .language_header_selector_booking_process {
          i {
            color: white !important;
          }

          div {
            color: $black !important;
          }
        }
      }
    }

    .currency_selector_wrapper::before {
      background: white;
    }

    .header_bottom {
      border-top: 1px solid white;
      border-bottom: 1px solid white;
    }
  }

  .booking-box--search .booking-box__content .booking-search-results__new-search .booking-button:before,
  .hidden_booking_summary .booking-search-results__new-search .booking-button:before {
    background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-lemuria/files/calendario-%23837652.svg) !important;
  }

  .booking-box--search .booking-box__content .booking-search-results__new-search .booking-button:hover::before,
  .hidden_booking_summary .booking-search-results__new-search .booking-button:hover::before {
    background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-garza-blanca/files/calendario-white.png) !important;
  }

  .fancybox-wrap.fancy-booking-search_v2 {
    .container_popup_booking {
      .description_top_popup_booking {
        video, img {
          height: 76px;
        }
      }
    }
  }

  div#step-1 .precioNocheColumn .priceValues .promotion_percentage_square {
    margin-right: -50px;
    transform: translateY(-14px);
  }
}

body.booking_process_version_1.tafer-garza-blanca,
body.booking_process_version_1.tafer-lemuria {
  @media screen and (max-width: 1440px) {
    .booking_engine_wrapper_process {
      #booking.boking_widget_inline {
        .stay_selection {
          width: 31% !important;
        }
      }
    }
    div#step-1 {
      .booking-box--search {
        .booking-box__content {
          .booking-search-results__rooms-list {
            .search-item {
              white-space: nowrap;
            }
          }
        }
      }
    }
    div#step-2 {
      .additional_service_element {
        .service_selection_wrapper {
          &.counter_service_selection {
            .service_select {
              .quantity_selection_wrapper, .days_selection_wrapper {
                white-space: nowrap;
              }
            }
          }
        }
      }
    }
    footer {
      .container {
        .footer_top {
          .inner_wrapper {
            .links_wrapper_left {
              margin-left: 40px;
            }

            .phone_link {
              margin-right: 40px;
            }
          }
        }

        .footer_middle {
          width: 1070px;
        }

        .footer_bottom {
          .links_hotels_wrapper {
            margin-left: 95px;
          }
        }
      }
    }
  }

  .modal_wrapper.login_information_wrapper {
    .modal_content {
      max-width: 920px;
      width: 840px !important;
    }
  }
}

body.new_styles.booking_process_version_1.hide_brand.vacations_club {
  div#step-1 .contTipoHabitacion .preciosHabitacion table.listadoHabsTarifas td.regimenColumn {
    width: 60%;
  }
}

html[lang='de'] {
  body.booking_process_version_1 div#step-1 .booking-box--search .booking-box__content .booking-search-results__rooms-list {
    padding-left: 0;
  }

  body.booking_process_version_1 .hidden_booking_summary .booking-search-results__rooms-list {
    max-width: 440px;
  }

  body.booking_process_version_1 .hidden_booking_summary .title_booking_breakdown:before {
    right: 15px;
  }

  body.booking_process_version_1.iPad_user .booking-button--confirmed-booking {
    width: 150px;
    margin-bottom: 20px;
  }

  body.booking_process_version_1.iPad_user .site-footer {
    clear: both;
  }
}

body.booking_process_version_1.tafer-garza-blanca,
body.booking_process_version_1.tafer-garza-los-cabos {
  $corpo_1: #750806;

  div#step-1 {
    .precioNocheColumn {
      .priceValues {
        .promotion_percentage_square {
          transform: translateY(-15px);
          margin-right: -55px;

          .promotion_discount {
            border: 1px solid $corpo_1;
            z-index: 30;
            top: 20px;
            left: 50px;
          }
        }
      }

      .botonReservarColumn {
        z-index: 0;
      }
    }


    .contTipoHabitacion {
      .preciosHabitacion {
        table.listadoHabsTarifas {
          tr.regimen_tr_element {
            &.withMarketingLogoPopup {
              .regimenColumn {
                width: 26%;
              }
            }
          }
        }
      }
    }
  }

  div#step-3 {
    .form_bottom_content {
      .booking-button {
        &:hover::before {
          background: $corpo_1 !important;
        }
      }
    }
  }
}

@import url("https://use.typekit.net/dvi7zga.css");
body.booking_process_version_1.tafer-mousai-pv,
body.booking_process_version_1.tafer-mousai {
  $black: #11100e;
  $black_2: $black;
  $corporate_1: #520A75;
  $corporate_2: #282361;
  $corporate_3: #85744C;
  $corporate_4: $corporate_3;
  $corporate_5: rgb(245, 247, 250);
  $corporate_6: #F2EFE7;
  $corporate_7: #F8D7D4;
  $corporate_8: #38687A;
  $grey: #F8F8F8;
  $red: #EC6363;
  $green: #228B22;
  $corporate_club: #AB9766;
  $corporate_club_2: #85744C;
  $gold: $corporate_3;

  $title_family: "Playfair Display", serif;
  $text_family: "museo-sans", sans-serif;
  font-family: $text_family;
  color: $black;

  @import "garza-booking-styles/garza_styles";
  @import "custom-booking-styles/mousai-vallarta/styles_booking_mousai_vallarta";
}


body.booking_process_version_1.tafer-garza-los-cabos {
  @import "custom-booking-styles/garza-los-cabos/styles_booking_garza_cabos";
}


@import url("https://use.typekit.net/yxc6djy.css");

body.booking_process_version_1.tafer-sierra {
  $black: #000000;
  $black_2: $black;
  $corporate_1: #17623F;
  $corporate_2: #043C2E;
  $corporate_3: #864336;
  $corporate_4: $corporate_3;
  $corporate_5: rgb(245, 247, 250);
  $corporate_6: #EOE7E3;
  $corporate_7: #F8D7D4;
  $corporate_8: #38687A;
  $grey: #F8F8F8;
  $red: #EC6363;
  $green: #4c9d78;
  $corporate_club: #AB9766;
  $corporate_club_2: #85744C;
  $gold: $corporate_3;

  $title_family: "questa-grande", serif;
  $text_family: "yorkten-slab-normal", sans-serif;
  font-family: $text_family;
  color: $black;

  @import "garza-booking-styles/garza_styles";
  @import "custom-booking-styles/sierra-lago/styles_booking_sierra";
}


@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap');
@import url("https://use.typekit.net/qft3sck.css");

body.booking_process_version_1.tafer-villa-palmar {
  $black: #1F2E1C;
  $black_2: $black;
  $corporate_1: #468299;
  $corporate_2: #468299d6;
  $corporate_3: #8CA771;
  $corporate_4: $corporate_3;
  $corporate_5: rgb(245, 247, 250);
  $corporate_6: #F2EFE7;
  $corporate_7: #B79E79;
  $corporate_8: #38687A;
  $grey: #F8F8F8;
  $red: #EC6363;
  $green: #6a8153;
  $corporate_club: #AB9766;
  $corporate_club_2: #85744C;
  $gold: $corporate_3;

  $title_family: "ivypresto-display", serif;
  $text_family: 'Roboto', sans-serif;
  font-family: $text_family;
  color: $black;

  @import "garza-booking-styles/garza_styles";
  @import "custom-booking-styles/palmar-cancun/styles_booking_palmar";
}


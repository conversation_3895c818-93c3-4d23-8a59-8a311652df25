div#step-1 {
  .room_step_block {
    .room_step_label {
      font-size: 20px;
      letter-spacing: 0.4px;
      line-height: 20px;
      color: $black;
      border-radius: 8px;
      border: 2px solid #CDCFD0;

      .room_step_occupancy {
        display: none;
      }

      &::before {
        font-size: 40px;
        color: #7A7E81;
      }
    }
  }

  .contTipoHabitacion {
    border: 0 !important;
    margin-top: 35px;
    border-radius: 0;
    display: block;
    position: relative;
    box-shadow: 0px 0px 10px #0000001A !important;

    .overlay_picture_text {
      position: absolute;
      z-index: 2;
      text-align: center;
      top: 30px;
      left: 52px;
      font-size: 9px;
      font-weight: bold;
      width: 99px;

      strong {
        display: block;
        font-size: 27px;
        margin-bottom: 15px;
      }

      .s_tag {
        color: #E75354;
      }

      .m_tag {
        color: green;
      }

      .l_tag {
        color: #7CCFF4;
      }

      .xl_tag {
        color: black;
      }
    }

    .contDescHabitacion .room_services i {
      font-size: 25px;
    }

    .contFotoDescripcion {
      .room_button_controls {
        .tour_button {
          .tour_virtual_icon {
            background-color: $black_2;
            padding: 5px 10px 5px 5px;

            .hidden_tooltip {
              background-color: $black_2;

              &::before {
                border-color: transparent transparent transparent $black_2;
              }
            }
          }
        }

        .open_see_more_room_v2 {
          display: none;
        }
      }
    }

    .contFotoHabitacion {
      min-height: 220px;
      width: 340px;

      a::before {
        display: none;
      }

      .occupancy {
        display: table;
        width: 100%;
        background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.4));
        position: absolute;
        bottom: 0;
        right: 0;
        z-index: 100;
        padding: 5px;
        text-align: right;
        font-family: $text_family;
        font-weight: 700;
        font-size: 12px;
        line-height: 12px;
        color: white;

        i {
          display: inline-block;
          vertical-align: bottom;
          padding: 0;
          color: white;
          font-size: 12px;
          line-height: 12px;

          &.adult {
            font-size: 22px;
            line-height: 22px;
            padding-right: 5px;
          }

          &.kid {
            font-size: 18px;
            line-height: 18px;
            padding-right: 5px;
          }

          &.baby {
            font-size: 14px;
            line-height: 14px;
            margin-bottom: 0;
            padding-left: 2px;
          }
        }

        b {
          position: absolute;
          bottom: 15px;
          right: 0;
          opacity: 0;
          display: inline-block;
          padding: 10px;
          font-weight: 400;
          text-transform: lowercase;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          border-radius: 5px;
          @include transition(all, .6s);

          &:after {
            position: absolute;
            top: 100%;
            right: 30px;
            content: '';
            border: 5px solid transparent;
            border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
          }
        }

        &:hover {
          b {
            bottom: 30px;
            opacity: 1;
          }
        }
      }
    }

    .contDescHabitacion {
      width: 755px !important;
      margin-top: 15px !important;

      .room_description_name_wrapper {
        .cabeceraNombreHabitacion {
          .tipoHabitacion {
            font-size: 20px;
            letter-spacing: 0.4px;
            line-height: 39px;
            font-weight: 500;
            color: $black_2 !important;
            text-transform: uppercase;
            font-family: $title_family;
            @include ellipsis(1);
          }

          .just_booking_message {
            background-color: white;
            color: $black_2;
            box-shadow: 0px 3px 6px #00000029;
            padding: 5px 15px;

            &::before {
              display: none;
            }
          }

          .very_asked_message {
            font-family: $text_family !important;
            background-color: $corporate_7 !important;
            font-size: 14px;
            line-height: 24px;
            letter-spacing: 0.21px;
            bottom: 15px;
            font-weight: 400;
            text-transform: uppercase;
            padding: 5px 10px 5px 35px !important;
            box-shadow: 0px 3px 6px #00000029;
            color: $black_2;

            &::after {
              content: "\f005";
              display: inline-block;
              color: $black_2;
              font-family: "Font Awesome 6 Pro";
              left: -140px;
              bottom: 30%;
              border-left: 0 !important;
              font-weight: 700;
              animation: wobble 1s ease infinite;
            }

            @keyframes wobble {
              0%, 100% {
                -webkit-transform: translateX(0%);
                transform: translateX(0%);
                -webkit-transform-origin: 50% 50%;
                transform-origin: 50% 50%;
              }

              15% {
                -webkit-transform: translateX(-2px) rotate(-10deg);
                transform: translateX(-2px) rotate(-10deg);
              }

              30% {
                -webkit-transform: translateX(calc(2px / 2)) rotate(10deg);
                transform: translateX(calc(2px / 2)) rotate(10deg);
              }

              45% {
                -webkit-transform: translateX(calc(-2px / 2)) rotate(calc(-10deg / 1.8));
                transform: translateX(calc(-2px / 2)) rotate(calc(-10deg / 1.8));
              }

              60% {
                -webkit-transform: translateX(calc(2px / 3.3)) rotate(calc(10deg / 3));
                transform: translateX(calc(2px / 3.3)) rotate(calc(10deg / 3));
              }
            }

            &::before {
              display: none;
            }
          }

          &.booking_message_width {
            .just_booking_message {
              overflow: hidden;
            }

            .very_asked_message {
              bottom: 0;
              line-height: 19px;
            }
          }

          &.two_tags {
            .tipoHabitacion {
              width: 440px !important;
            }
          }
        }
      }

      .room_from_price_wrapper {
        .room_from_price {
          * {
            color: $black_2;
          }
        }

        .prices_dropdown_toggle {
          &, &.active {
            background: $corporate_3;
            border: 1px solid $corporate_4;
            border-radius: 1px;
          }
        }
      }
    }

    .descripcionHabitacion {
      height: 45px;
      font-size: 16px;
      letter-spacing: 0;
      line-height: 26px;
      color: $black;
    }

    .see_more_rooms_v2 {
      bottom: 60px;

      .plus_sign {
        color: #333 !important;
      }

      .see_more {
        text-decoration: none !important;
        text-transform: uppercase !important;
        color: $black !important;
        font-size: 12px !important;
        letter-spacing: 1.44px !important;
        line-height: 24px !important;
      }

      .lupa {
        top: 5px;
        left: 5px;
        font-size: 30px;
        font-family: "Font Awesome 6 pro", sans-serif;
        font-weight: 300;
        text-shadow: 0 0 5px rgba(0, 0, 0, .5);
      }
    }

    .very_asked_message {
      background-color: $red !important;
      color: white;
      letter-spacing: 1px;
      text-transform: uppercase;
      margin-right: 3px;

      &:after {
        border-left-color: transparent;
      }
    }

    .just_booking_message + .very_asked_message {
      &:after {
        border-left-color: $red;
        border-top: 15px solid $red;
        border-left: 15px solid $red;
        border-right: 15px solid transparent;
        border-bottom: 15px solid $red;
      }
    }

    .room_services {
      border-top-width: 2px;
      border-top-style: dashed;
      border-bottom-width: 2px;
      border-color: rgba($black_2, .3) !important;
      width: 97% !important;
      display: flex !important;
      gap: 10px;

      .service_element {
        border-right: 0 !important;
        height: 40px !important;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          color: $corporate_3;
          margin-right: 10px;
        }

        .service_description {
          color: $black_2;
          font-weight: 400;
          font-size: 14px;
          line-height: 16px;
        }
      }
    }

    .preciosHabitacion {
      margin: 60px 0 0 0;

      &.rates_dropdown.dropdown_active {
        max-height: 3000px;
      }

      table.listadoHabsTarifas {
        tr.rate_tr_element {
          margin-top: 0 !important;

          .rate_offer {
            background: #F2EFE7;
            color: $black_2;
            z-index: 5;

            .rate_offer_tooltip {
              background: $corporate_6;

              &::before {
                border-top: 15px solid $corporate_6;
                left: 50%;
              }
            }

            .best_rate_offer {
              font-size: 15px;
              font-family: $title_family;
              letter-spacing: 0.31px;
              line-height: 18px;
            }

            &::before {
              border-top: 39px solid #F2EFE7;
            }

            &::after {
              border-bottom: 38px solid #F2EFE7;
            }
          }

          .rate_offer + .minium_price_rate + .conditions_info_wrapper {
            position: relative;
            float: right;
            display: table;
            border-bottom: 1px solid #dfdfdf;
            width: 100%;

            .rate_conditions_link {
              padding: 0 30px 0 0;
              margin-bottom: 10px !important;
              text-decoration: underline;
              margin-right: 7px !important;
              @extend .fa-info;

              &:before {
                @extend .fa;
                width: 3px;
                height: 14px;
                position: absolute;
                top: 50%;
                right: 3px;
                -webkit-transform: translateY(-50%);
                -moz-transform: translateY(-50%);
                -ms-transform: translateY(-50%);
                -o-transform: translateY(-50%);
                transform: translateY(-50%);
                border: 1px solid $black;
                background: transparent;
                color: $black;
                padding: 0 5px;
                font-size: 10px;
                line-height: 14px;
                border-radius: 50%;
              }

              &::after {
                display: none;
              }

              &:hover {
                color: $corporate_3;
              }
            }
          }

          .contTitTipoTarifa {
            background-color: #F8F8F8;
            color: $black;
            font-family: $text_family;
            width: 100%;

            .titTipoTarifa {
              font-weight: 700;
              font-size: 15px;
              letter-spacing: 1.5px;
              line-height: 16px;
              color: $black_2;
              text-transform: uppercase;
            }

            .cheapest_rate_message:not(.has_conditions) {
              display: none;
            }

            .cheapest_rate_message.has_conditions {
              background-color: transparent;
              color: $black_2;
              font-size: 14px;
              letter-spacing: 0.31px;
              width: 100%;
              display: block;
              text-align: right;
              box-sizing: border-box;
              border-bottom: 1px solid #dfdfdf;
              padding: 3px 27px 10px 10px !important;
              height: 35px;

              .best_price_label_info {
                display: none;
              }

              &:before {
                display: none;
              }

              .before_block {
                border-left-color: $corporate_6;
              }
            }

            .advice_rate_message {
              background-color: $corporate_6;
              color: $black_2;

              &:before {
                border-left-color: $grey;
              }
            }

            > .conditions_info_wrapper {
              position: relative;
              float: right;
              display: table;
              border-bottom: 1px solid #dfdfdf;
              width: 100%;

              a {
                color: $black_2;
                padding: 10px 35px;
                position: relative;
                font-size: 14px;
                font-weight: 600;
                letter-spacing: 0.31px;
                text-transform: none;
                @extend .fa-info;

              &:before {
                @extend .fa;
                width: 3px;
                height: 14px;
                position: absolute;
                top: 50%;
                right: 15px;
                -webkit-transform: translateY(-50%);
                -moz-transform: translateY(-50%);
                -ms-transform: translateY(-50%);
                -o-transform: translateY(-50%);
                transform: translateY(-50%);
                border: 1px solid $black;
                background: transparent;
                color: $black;
                padding: 0 5px;
                font-size: 10px;
                line-height: 14px;
                border-radius: 50%;
              }

                &:after {
                  display: none;
                }
              }
            }
          }
        }

        tr.regimen_tr_element {
          position: relative;
          width: calc(100% - 10px);

          .regimenColumn {
            .regimenColumnContent {
              padding-top: 25px;
            }
          }

          .marketing_logo_info_wrapper {
            table.table {
              background: transparent !important;

              tr {
                border-bottom: 0;
              }
            }
          }

          &.withMarketingLogoPopup {
            .regimenColumn {
              width: 35%;
            }

            .custom_board_message.open_marketing_extra_info {
              width: 13%;

              .message_element {
                display: flex;
                flex-direction: column;
              }
            }
          }
        }

        tr.regimen_tr_element:not(.regimen_for_users) {
          td.precioTotalColumn {
            width: 45% !important;

            .priceValues {
              left: -60px;
            }
          }
        }

        tr .lock_board {
          .lock_board_wrapper {
            display: flex;
            width: 100%;
            justify-content: space-between;
          }
        }

        td.regimenColumn {
          width: 40%;

          .regimen_name_wrapper {
            font-size: 16px;
            letter-spacing: 0.48px;
            line-height: 16px;
            color: $black_2;
            text-transform: none;
          }

          .tTextoOferta {
            font-size: 15px;
            letter-spacing: 0.45px;
            line-height: 16px;
            color: $corporate_3;
            text-transform: none;
            font-weight: 700;

            .separator:first-child {
              display: none;
            }
          }
        }

        .precioNocheColumn.noPrecioNocheColumn {
          display: none;
        }

        .precioTotalColumn {
          .priceValues {
            margin-left: -30px;
            width: auto;

            .precioGeneralDiv {
              span:not(.night_label)  {
                font-size: 22px;
                letter-spacing: 0.48px;
                line-height: 24px;
                font-weight: bold;
                color: $black_2;
              }
            }

            .tax_not_included_label, .priceTitle {
              color: $black_2;
              font-family: $text_family;
            }

            .promotion_percentage_square {
              background: $corporate_club;

              .promotion_discount {
                background: $corporate_club;
              }
            }
          }
        }

        .botonReservarColumn {
          .booking-button {
            font-family: $text_family;
            background-color: $corporate_1;
            border: 1px solid $corporate_2;
            color: white;
            padding: 18px 0;
            border-radius: 1px;
            width: 188px;
            margin-right: 0;
            font-size: 16px;
            line-height: 16px;
            font-weight: 700;
            position: relative;
            -webkit-transition: all 0.6s;
            -moz-transition: all 0.6s;
            -ms-transition: all 0.6s;
            -o-transition: all 0.6s;
            transition: all 0.6s;
            display: inline-block;
            vertical-align: middle;
            text-transform: uppercase;
            letter-spacing: 0;
            z-index: 1;
            text-align: center;

            &:hover {
              background-color: $corporate_2;
            }
          }
        }
      }
    }

    .last_day_cancellation_text {
      background: transparent;
      position: relative;
      @extend .fa-check;
      color: $green;
      font-family: $text_family;
      font-size: 14px;
      font-weight: 400;
      font-style: italic;
      margin-bottom: 10px;

      &:before {
        @extend .fa;
        position: absolute;
        top: 50%;
        left: 3px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        padding: 0 3px;
        font-size: 14px;
        line-height: 14px;
        border-radius: 50%;
      }
    }

    .cheapest_rate_message {
      .conditions_info_wrapper {
        display: inline-block;
        vertical-align: middle;
        width: auto;
        position: unset;
      }

      .rate_conditions_link {
        color: $black_2;
        font-weight: 600;
        padding: 0 10px;
        position: relative;
        font-size: 14px;
        letter-spacing: 0.31px;
        text-transform: none;

        &::before {
          content: "\f129";
          right: -17px;
        }

        &:after {
          display: none;
        }
      }
    }

    .rate_conditions_link {
      background: transparent;
      position: relative;
      @extend .fa-info;

      &:before {
        @extend .fa;
        width: 3px;
        height: 14px;
        position: absolute;
        top: 50%;
        right: 3px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        border: 1px solid $black;
        background: transparent;
        color: $black;
        padding: 0 5px;
        font-size: 10px;
        line-height: 14px;
        border-radius: 50%;
      }

      &:hover {
        color: $corporate_3;
      }
    }

    .sectoption {
      border: none;
      padding: 5px 0;

      .titSelOpt {
        color: $black;
        font-size: 18px;
        font-family: $title_family;
      }

      .listaradio {
        li {
          input {
            display: inline-block;
            vertical-align: middle;
            margin: 0 5px 0;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: white;
            border: 1px solid $black;
            cursor: pointer;

            &:checked {
              background-color: $corporate_1;
            }

            &:focus {
              outline: 0;
            }
          }
        }
      }
    }
  }

  .total_price_wrapper {
    .total_price {
      background: #333333;
      border-radius: 8px 0 0 8px;
      padding: 3px 20px;

      .total_price_label {
        font-size: 14px;
        line-height: 24px;
      }

      .total_price_value {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .rooms_submit_button {
      border-radius: 0 8px 8px 0;
      background: #CECECE;
      font-size: 20px;
      font-weight: bold;
      letter-spacing: 0.4px;
      line-height: 17px;
      padding: 12px 40px;
      margin: 0 0 0 5px;
      border: 0;
    }

  }
}

div#step-1 .conditions_info_wrapper .rate_conditions_link {
  font-size: 14px;
  font-weight: 600;
  box-sizing: border-box;
  letter-spacing: 0.31px;
  padding: 0 30px 0 0;
  color: $black_2;
}

.popup_see_more_rooms_second {
  a.fancybox-nav {
    display: none;
  }

  .fancybox-inner {
    background-color: white;
  }

  .room_popup_individual_element {
    .popup_title {
      background: white;
      font-family: $title_family;
      font-size: 25px;
      font-weight: 700;
      color: $black_2;
      top: auto;
      bottom: calc(100% - 450px);
      padding-right: 35%;
      padding-bottom: 10px;
      border-top: 10px solid white;
      line-height: normal;
      white-space: nowrap;
    }

    .close_button_element {
      font-family: $text_family;
      font-size: 18px;
      font-weight: 700;
      color: white;
      background-color: $corporate_1;
      width: 50px;
      line-height: 50px;
      top: 0;
      right: 0;
    }

    .popup_carousel {
      .element_carousel_pictures {
        .exceded {
          height: 450px;

          img {
            width: 100%;
          }

          a {
            display: block !important;
            position: absolute;
            top: 290px;
            right: 0;
            background: radial-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
            padding: 10px;
            color: white;

            i.fa {
              font-size: 25px;
            }

            &:hover {
              color: $corporate_1;
            }
          }
        }
      }

      .flex-direction-nav {
        .flex-nav-prev, .flex-nav-next {
          width: 50px;
          height: 50px;
          @include transition(width, .6s);

          &:hover {
            width: 60px;

            a {
              &:before {
                margin-left: 8px;
              }
            }
          }

          a {
            background-color: $corporate_2;
            @extend .icon-longarrow;

            &:before {
              font-family: "icomoon", sans-serif;
              font-size: 30px;
              color: white;
              position: absolute;
              top: 50%;
              left: 50%;
              -webkit-transform: translate(-50%, -50%);
              -moz-transform: translate(-50%, -50%);
              -ms-transform: translate(-50%, -50%);
              -o-transform: translate(-50%, -50%);
              transform: translate(-50%, -50%);
              @include transition(margin, .6s);
            }
          }
        }

        .flex-nav-prev {
          a:before {
            -webkit-transform: translate(-50%, -50%) rotate(-180deg);
            -moz-transform: translate(-50%, -50%) rotate(-180deg);
            -ms-transform: translate(-50%, -50%) rotate(-180deg);
            -o-transform: translate(-50%, -50%) rotate(-180deg);
            transform: translate(-50%, -50%) rotate(-180deg);
          }

          &:hover {
            a {
              &:before {
                margin-left: -8px;
              }
            }
          }
        }
      }
    }

    .popup_room_pictures {
      display: none !important;
    }

    .room_services {
      margin-left: 0 !important;
      width: 100% !important;
      padding: 0 !important;
      border-top-style: dashed;
      border-bottom-style: dashed;
      border-top-color: $grey;
      border-bottom-color: $grey;
      border-top-width: 2px;
      border-bottom-width: 2px;
      background: transparent;
      display: flex;

      .service_element {
        border-right-style: dashed;
        border-right-color: $grey;
        border-right-width: 2px;
        height: auto !important;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 7px 10px;

        i {
          font-size: 25px;
          display: inline-block;
          padding-bottom: 0;
          margin-right: 10px;
          color: $corporate_3;
        }

        .service_description {
          font-size: 14px;
          line-height: 16px;
          font-weight: 400;
          color: $black_2;
        }
      }
    }

    .popup_room_description {
      margin-top: 10px;
      margin-bottom: 10px;
      padding-top: 20px;
      padding-bottom: 20px;
      margin-left: 0 !important;
      width: 100% !important;
      font-family: $text_family;
      //background: linear-gradient(to left, white, white 33%, #ececec 33%, #ececec);
      box-sizing: border-box;

      .desc, .list {
        display: inline-block;
        vertical-align: top;
        width: calc(100% / 3);
        box-sizing: border-box;
      }

      .desc {
        width: calc(100% / 3 * 2);
        padding-right: 20px;

        strong {
          font-weight: 700;
        }
      }

      .list {
        padding: 0 0 0 25px;

        li {
          font-weight: 700;
          padding: 5px 0;
          @extend .icon-longarrow;

          &:before {
            font-family: "icomoon", sans-serif;
            margin-right: 10px;
            color: $corporate_2;
          }
        }
      }
    }
  }
}
#login_wrapper_element.v4,
#logged_user_info_wrapper.v4 {
  background: #F2EFE7;
  border: 1px solid $corporate_club;
  border-radius: 0;
  max-height: 120px;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url(https://storage.googleapis.com/cdn.paraty.es/tafer-garza-blanca/files/pattern.png);
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    pointer-events: none;
  }

  .logo_wrapper {
    max-height: 120px;

    &::after {
      border-right: 1px solid $black;
    }
  }

  .content_login_wrapper {
    max-height: 120px;

    .club_icons_wrapper {
      .club_icon_element {
        .icon_image_wrapper {
          display: none;
        }

        .club_icon_description {
          font-family: $text_family;
          color: $black;
          font-size: 16px;
          letter-spacing: 0.7px;
          font-weight: 300;
        }
      }
    }
  }

  .users_buttons_wrapper {
    margin-top: 22px;

    .join_button_wrapper {
      .want_join {
        border-radius: 0;
        font-family: $text_family;
        background: $corporate_club;
      }
    }

    .already_member_wrapper {
      border-radius: 0;
      font-family: $text_family !important;
      border: 1px solid $corporate_club;
      color: $corporate_club;
    }
  }

  .content_logged_wrapper {
    max-height: 120px;

    .center_content_wrapper {
      display: flex;

      .logged_user_text {
        display: flex;
        align-items: center;
        max-width: 100%;
        padding: 0 40px;

        &::after {
          display: none;
        }

        .default_text, .username_text, .extra_logged_user_info {
          color: $black;
          font-family: $text_family;
          text-align: left;
        }

        .extra_logged_user_info {
          margin-top: 0;
          margin-left: 50px;
          max-width: 390px;
          letter-spacing: 0.7px;
          line-height: 19px;
        }
      }

      .user_category_image, .user_points {
        display: none;
      }

      .logout_button_wrapper {
        position: absolute;
        top: -5px;

        i, span {
          color: $black;
          text-decoration: none;
        }
      }
    }
  }
}

.modal_wrapper.register_information_wrapper,
.modal_wrapper.login_information_wrapper {
  background: #33333386 !important;

  .modal_content {
    max-width: 850px;
    .modal_content_close {
      top: -30px;
      right: 20px;

      &::before,
      &::after {
        background: white !important;
      }
    }

    #popup_login_information {
      #register_form_wrapper.v3,
      #login_form_wrapper_v1.v3 {
        border: none;
        border-radius: 5px;
        background-color: white;
        width: 410px;

        .register_title_form_wrapper,
        .header_login_wrapper {
          background-color: white;
          border-radius: 5px 5px 0 0;
          padding: 10px 0;
          display: flex;
          align-items: center;
          justify-content: center;
          flex-direction: column;
          text-align: center;
          min-height: 100px;

          .main_form_title,
          .login_title {
            font-family: $text_family;
            font-weight: 500;
            font-size: 24px;
            line-height: 28px;
            text-transform: none;
            margin: 0 0 7px;
            color: $black;
          }

          .main_form_subtitle {
            font-family: $text_family;
            font-size: 14px;
            font-weight: 300;
            letter-spacing: 0.7px;
            line-height: 16px;
            text-align: center !important;
            color: $black;
          }
        }

        .inputs_wrapper {
          display: flex;
          flex-wrap: wrap;
          justify-content: space-between;
        }

        .data_form_field {
          float: none;

          label[for="birthday"] {
            color: #b4b4b4 !important;
            font-family: $text_family !important;
            font-weight: 400 !important;
          }

          #birthday {
            color: #b4b4b4 !important;
          }
        }

        .promotions_checkbox {
          margin: 10px;

          input {
            margin: 2px 0 2px 10px;
          }
        }

        .login_block {
          padding: 0 20px;

          .login_form_title {
            display: none;
          }

          .login_form {
            padding-top: 5px;

            .login_data_block {
              width: 100%;

              label {
                font-family: $text_family;
                top: -12px;
                padding: 0 8px;
              }

              input {
                padding: 0;
                font-size: 16px;
              }
            }
          }

          .club_send_password_wrapper {
            padding-top: 10px;
            padding-bottom: 15px;

            .title_wrapper_recover_password .title_text,
            .toggled_content {
              font-family: $text_family;
              font-size: 12px;
              font-weight: 400 !important;
              color: $corporate_club;
            }

            .title_wrapper_recover_password .title_text {
              font-size: 13px;
              font-weight: 500 !important;
            }
          }
        }

        .lopd_wrapper {
          display: none;
        }

        .login_button_element {
          margin: 0;
        }

        .sign_up_button,
        .login_button_element {
          background: $corporate_club_2;
          border: none;
          color: white;
          font-family: $text_family;
          font-weight: 500;
          text-transform: none;
          border-radius: 3px;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          padding: 0 35px;
          height: 43px;
          width: 100%;
        }

        .social_login_xee {
          position: relative;

          #facebook-login-xee, #google-login-xee {
            padding: 9px 0;
            font-family: $text_family;
          }

          &::before {
            content: "O";
            position: absolute;
            left: 50%;
            top: -2px;
            transform: translateX(-50%);
            width: 60px;
            height: 20px;
            background: white;
            font-family: $text_family;
            font-size: 13px;
            letter-spacing: 0.39px;
            line-height: 20px;
            color: #444444;
            text-align: center;
          }
        }

        input,
        select,
        label[for="birthday"] {
          font-family: $text_family;
          font-size: 12px;
          font-weight: 300 !important;
          color: $corporate_club;
          appearance: none;
          -moz-appearance: none;
          -webkit-appearance: none;

          &::-webkit-input-placeholder { /* Chrome/Opera/Safari */
            font-family: $text_family;
            font-size: 12px;
            font-weight: 300 !important;
            color: $corporate_club;
          }

          &::-moz-placeholder { /* Firefox 19+ */
            font-family: $text_family;
            font-size: 12px;
            font-weight: 300 !important;
            color: $corporate_club;
          }

          &:-ms-input-placeholder { /* IE 10+ */
            font-family: $text_family;
            font-size: 12px;
            font-weight: 300 !important;
            color: $corporate_club;
          }

          &:-moz-placeholder { /* Firefox 18- */
            font-family: $text_family;
            font-size: 14px;
            font-weight: 300 !important;
            color: $corporate_club;
          }

          &:-webkit-autofill,
          &:-webkit-autofill:hover,
          &:-webkit-autofill:focus {
            -webkit-box-shadow: 0 0 0px 1000px white inset;
            -webkit-text-fill-color: $corporate_club;
          }
        }

        .club_send_password_wrapper {
          input {
            width: calc(100% - 14px);
          }

          #submit_recovery_club {
            background: $corporate_club;
            border: none;
            color: white;
            font-family: $text_family;
            font-weight: 500;
            text-transform: none;
            border-radius: 3px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            padding: 0 35px;
            height: 55px;
            width: calc(100% - 70px);
          }
        }
      }

      &.hide_login {
        #register_form_wrapper {
          margin: 0 auto;
          float: none;
          display: block;
        }

        #login_form_wrapper_v1 {
          display: none;
        }
      }
    }
  }
}

div#step-1 {
  .contTipoHabitacion {
    .preciosHabitacion {
      .listadoHabsTarifas {
        .prices_detail_popup.tax_inc_wrapper_info {
          right: 0;
          left: auto;
          transform: translate(0%, -100%);
        }

        .lock_board {
          position: relative;
          min-width: 206px;

          .lock_board_wrapper:not(.toggle_discount) {
            background-color: white !important;
            border: 1px solid $corporate_club;
            display: inline-flex;
            padding: 25px 15px 10px 60px;
            margin-top: 10px;
            min-width: 206px;
            box-sizing: border-box;
            border-radius: 0;

            .club_lock_logo {
              max-height: 25px;
              position: absolute;
              top: 50%;
              left: 20px;
              transform: translateY(-50%);
            }

            .lock_tooltip {
              background-color: $corporate_club;
              color: white;
              font-size: 12px;
              font-weight: 300;
              z-index: 2;

              &::before {
                border-color: $corporate_club transparent transparent transparent;
              }
            }

            .precioTachadoDiv {
              position: absolute !important;
              top: 8px;
              left: 60px;
              color: $black;

              .tPrecioTachado {
                font-size: 14px;
                font-weight: bold;
                color: $black;
              }
            }

            .precioGeneralDiv {
              .tPrecioOferta {
                color: $corporate_1;
              }

              .precioGeneral {
                color: $black;
              }

              .monedaConv {
                font-size: 12px;
                font-weight: 400;
              }

              .currencyValue {
                font-size: 18px;
                font-weight: bold;
                padding-right: 0 !important;
              }

              .night_label {
                font-size: 14px;
                font-weight: 400;
                margin-left: 5px;
                color: $black;
              }
            }

            .lock_ico {
              font-size: 15px;
              position: absolute;
              top: -10px;
              left: 20px;
              z-index: 2;
              margin: 0;
              color: $corporate_club;
            }
          }

          .lock_top_elements {
            position: absolute;
            top: -8px;
            left: 13px;
            background: white;
            padding: 0 0 0 30px;
            z-index: 1;
            display: flex;
            justify-content: space-around;
            align-items: center;
            gap: 10px;

            .lock_mini_label {
              color: $corporate_club;
              font-family: $text_family;
              font-size: 10px;
              line-height: 11px;
              font-weight: 500;
            }

            .prices_detail_tooltip {
              font-size: 16px;
              color: $corporate_club;
            }
          }
        }

        .precioNocheColumn.daily_prices_normal_rate {
          width: 22%;
          padding-left: 0px !important;
          padding-right: 0px !important;

          .priceValues {
            margin-left: 0;

            .normal_rates_mini_label {
              font-size: 11px;
              font-weight: 500;
              display: block;
              text-align: left;
              color: $corporate_club;
            }

            .precioTachadoDiv {
              text-align: left;
              font-size: 14px;
              color: $black;
              font-weight: bold;
              line-height: 22px;

              .tPrecioTachado {
                color: $black;
                font-weight: bold;
                font-size: 14px;
              }
            }

            .precioGeneralDiv {
              text-align: left;
              color: $black;
              width: max-content;

              .monedaConv {
                font-size: 14px;
                font-weight: 400;
              }

              .currencyValue {
                font-size: 20px;
                font-weight: bold;
              }

              .night_label {
                font-size: 14px;
                font-weight: 400;
              }

              .prices_detail_tooltip {
                font-size: 16px;
                color: $corporate_club;
                margin-left: 5px;
              }
            }
          }
        }
      }
    }
  }
}
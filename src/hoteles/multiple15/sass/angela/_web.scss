#paraty_widget_container {
  padding: 0;
  float: none;
  width: auto;
  @include center_xy;
  z-index: 1001 !important;
  &.with_paraty_widget {
    position: relative;
    top: auto;
    left: auto;
    -webkit-transform: none;
    transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    #widget_paraty.inner_widget {
      margin: auto;
      #full_wrapper_booking #full-booking-engine-html-7 .booking_form .rooms_number_wrapper .selectricWrapper .selectricItems {
        top: 100%;
      }
    }
  }
}

.content_top_margin {
  &.with_paraty_widget {
    margin-top: 20px !important;
  }
}

.full_width {
  &.with_paraty_widget {
    z-index: 99999;
    .full_section_inner.with_paraty_widget {
      z-index: 30;
      &.active {
        z-index: 999999;
      }
    }
    &.active {
      z-index: 999999;
    }
  }
}

@media (max-width: 1080px) {
  .content.content_top_margin {
    z-index: 120;
  }
}
import copy
import fileinput
import os
import re
import sys

sys.path.append(os.path.join(os.path.dirname(__file__), ".."))

from devs.compilers.sass_compiler import compile_sass

TEMPLATE_INFO = '''
@app.route('/css/@@folder_short_name@@/<path:filename>')
def custom_static_css_@@folder_short_name@@(filename):
    actual_path = os.path.dirname(os.path.abspath(__file__))
    final_path = os.path.join(actual_path, '@@css_path@@')
    return send_from_directory(final_path, filename)


@app.route('/js/@@folder_short_name@@/<path:filename>')
def custom_static_js_@@folder_short_name@@(filename):
    actual_path = os.path.dirname(os.path.abspath(__file__))
    final_path = os.path.join(actual_path, '@@js_path@@')
    return send_from_directory(final_path, filename)


@app.route('/img/@@folder_short_name@@/<path:filename>')
def custom_static_img_@@folder_short_name@@(filename):
    actual_path = os.path.dirname(os.path.abspath(__file__))
    final_path = os.path.join(actual_path, '@@img_path@@')
    return send_from_directory(final_path, filename, conditional=False)
  '''

IS_CLOUD_BUILD = False

def buildTemplateInfo(folderName):

    configPy = open('%s/config.py' % folderName).read()

    templatesStrings = re.findall("templates = (\[[^]]*])", configPy)
    
    if not templatesStrings:
        raise Exception("WARNING!!!!!! Configuration is missing templates variable")
        
    templates = eval(templatesStrings[0])
    
    result = ""
    for template in templates:
        shortPath = template[:4] + template[-1:]
        template_paths = copy.copy(TEMPLATE_INFO)
        template_paths = template_paths.replace("@@folder_short_name@@", shortPath)
        base_path = os.path.join("webs", template, "template")
        template_paths = template_paths.replace("@@css_path@@", os.path.join(base_path, "css"))
        template_paths = template_paths.replace("@@js_path@@", os.path.join(base_path, "js"))
        template_paths = template_paths.replace("@@img_path@@", os.path.join(base_path, "img"))
        result += template_paths

    return result


def findApplicationId(folderName):
    
    configPy = open('%s/config.py' % folderName).read()
    result = re.findall("applicationId = \"(.*)\"\n", configPy)
    
    if not result:
        raise Exception("WARNING!!!!!! Configuration is missing applicationId variable")
    
    return result[0]

def find_server_type(folderName):

    #By default everything is a F1
    server_type = "F1"

    configPy = open('%s/config.py' % folderName).read()
    result = re.findall("^server_type = \"(.*)\"\n", configPy, flags=re.MULTILINE)

    if result:
        server_type = result[0]

    return server_type

def hsts_mandatory(folderName):
    hsts_header = ''
    ssl_always = ''

    configPy = open('%s/config.py' % folderName).read()
    result = re.findall("ssl_mandatory = (.*)\n", configPy)

    if result:
        ssl_always = 'secure: always\n  redirect_http_response_code: 301'
        hsts_header = """http_headers:\n    Strict-Transport-Security: max-age=16070400; includeSubDomains\n    Vary: Accept-Encoding"""

    else:
        hsts_header = '''http_headers:\n    Vary: Accept-Encoding\n    Access-Control-Allow-Origin: "*"'''

    return ssl_always, hsts_header

def prepareProject(folderName):
    
    #Just in case, reset
    # os.system("git checkout ../app.yaml")
    # os.system("rm ../app.yaml")
    # os.system("cp ../app.yaml.bk ../app.yaml")

    if not IS_CLOUD_BUILD:
        #Put the hotel's folder into the main folder
        file_folder_path = os.path.dirname(os.path.abspath(__file__))
        hotel_folder_path = os.path.join(file_folder_path, folderName)
        source_code_folder_path = os.path.join(file_folder_path, "..")
        print("Copying files from %s to %s" % (hotel_folder_path, source_code_folder_path))
        os.system("rsync -avC --exclude 'sass' --exclude 'config.rb' %s/ %s" % (hotel_folder_path, source_code_folder_path))

        # Build styles for mobile version and put it on the static_1/css folder
        os.chdir(hotel_folder_path)
        compile_sass(f'hoteles/{folderName}')
        os.chdir("..")
    
    #Add template information
    for line in fileinput.input("../app.yaml", inplace=True):
        #Replace appId in app.yaml
        if "@@@APP-ID@@@@" in line:
            print(line.replace("@@@APP-ID@@@@", findApplicationId(folderName)))
        elif "@@@TEMPLATE@@@" in line:
            print(line.replace("@@@TEMPLATE@@@", buildTemplateInfo(folderName)))
        elif "@@@SERVER-TYPE@@@" in line:
            print(line.replace("@@@SERVER-TYPE@@@", find_server_type(folderName)))
        elif "@@@SSL_MANDATORY@@@" in line:
            ssl_string, hsts_string = hsts_mandatory(folderName)
            print(line.replace("@@@SSL_MANDATORY@@@", ssl_string))
        elif "@@@HSTS_MANDATORY@@@" in line:
            ssl_string, hsts_string = hsts_mandatory(folderName)
            print(line.replace("@@@HSTS_MANDATORY@@@", hsts_string))
        else:
            print(line.replace('\n', ''))

    content = open('../routes_by_application.py').read()
    content = content.split("#dynamic_routes_build")
    content = content[0] + "#dynamic_routes_build" + buildTemplateInfo(folderName)

    with open('../routes_by_application.py', 'w') as f:
        f.write(content)


def execute_synchronize(folder_name):
    prepareProject(folder_name)


if __name__ == "__main__":
    
    if len(sys.argv) < 2:
        print('Expected 1 parameter: folderName  (i.e. python synchronize2.py puentereal)')
    else:
        if len(sys.argv) == 3:
            IS_CLOUD_BUILD = True

        prepareProject(sys.argv[1])




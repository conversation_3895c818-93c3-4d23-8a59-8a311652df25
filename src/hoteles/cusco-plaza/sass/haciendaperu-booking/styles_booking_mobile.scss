.step_item {
    &::after {
      background-color: $corporate_5 !important;
      border: none !important;
    }
   &::before{
        width: calc(100% - 14px)!important;
        left: calc(-50% + 7px )!important;
      }
  }

  .step_item.active {
    &::after {
      border: 2px solid $corporate_1 !important;
      background-color: transparent !important;
    }

  }

  .booking_step_sentence {
    font-weight: 300 !important;
  }

  .content_title {
    .title {
      font-size: 15px !important;
      text-transform: none;
    }
  }

  .dates {
    p {
      font-size: 14px;
      line-height: 26px;
    }
  }

  .search_text {
    p {
      font-size: 14px;
      line-height: 26px;
    }
  }

  .double_button_wrapper {
    padding: 10px;
    justify-content: space-between;

    .modify_search, .show_calendar, .back_button {
      font-family: $text_family !important;
      border: 1px solid $corporate_5 !important;
      width: 48.5% !important;
      padding: 0 15px 0 40px !important;
      color: $corporate_5!important;
    }
  }

  .tabs {
    padding: 0 10px !important;

    li {
      width: 50% !important;
      margin: 0 0 5px !important;
      background: $white !important;
      border-style: none !important;

      .tab_btn {
        background: $white !important;
        border: 1px solid $grey-1;
        color: #777777 !important;
        font-weight: 500 !important;

        &::before {
          display: none !important;
        }

        &.active {
          border: 1px solid $corporate_1;

          .tab_text {
            display: inline;
            color: $corporate_1 !important;

            &:before {
              display: none;
            }
          }
        }

        .tab_text {
          display: inline !important;

          &:before {
            content: "";
            width: 9px;
            height: 9px;
            background: $corporate_1;
            border-radius: 50%;
            opacity: 0.75;
            display: inline-block;
          }
        }
      }
    }
  }

  .room_option_block {
    .has_modal {
      .room_name {
        .title {
          font-size: 15px !important;
        }

        .info_btn {
          font-weight: 300 !important;
          font-size: 0.6rem !important;
        }
      }
    }

    .rate_selected_title {

      span {
        font-family: $text_family;
        font-size: 11px !important;
      }

      .modal_launch {
        color: $black !important;
      }
    }

    .last_day_cancellation_text {
      color: $corporate_1 !important;
      &::before {
        color: $corporate_1 !important;
      }
    }

    .regime_description {
      .regime_title {
        font-size: 14px !important;
      }

      .regime_offer_detail, .price_through {
        color: $red !important;;
      }
    }

    .discount_percentage {
      background-color: $red !important;
    }

    .final_price {
      color: $black !important;
    }

    .submit {
      left: -20px !important;

      span {
        background: $corporate_7 !important;
        border-radius: 0 !important;
        width: 135px !important;
      }
    }
  }

  footer > div:first-child {
    display: none !important;
  }

  .main_content_wrapper.step_0 .tabs_wrapper .tabs li + li:before {
    display: none;
  }

  .main_content_wrapper{

    &.step_2{
      .personal_details_form_wrapper{
       .personal_details_form{
         .bottom_button_wrapper{
            #btn-finish-booking{
              background-color: $corporate_7;
            }
         }
       }
      }
    }
  }

  .booking_widget_wrapper{

    .double_button_wrapper{
      .close_button{
        background: $corporate_5;
        font-family: $font_1;
      }
    }
    .input_wrapper{
      &::before{
        background-color: $corporate_1;
      }
    }
    #departure_date_popup,
    #entry_date_popup{
      .header_wrapper{
        .banner_title{
          i{
            color: $corporate_1;
          }
        }
      }
      .end_datepicker,
      .start_datepicker{
        .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before{
            background: $corporate_1;
          }
        .ui-datepicker-inline{
          .ui-datepicker-header{
            color: $corporate_1;
          }
        }
      }
    }
  }

  .btn.btn_primary{
    background-color: $corporate_1;
    font-family: $font_1;
    border-color: $corporate_1;
  }

  .label.label_secondary{
    background-color: $red;
  }
  .continue_booking{
    background: $corporate_7;
    font-family: $font_1;
  }

  .main_content_wrapper {
      &.step_2 {
        .reservation_summary {
          .option_selected {
            .rate {
                .conditions {
                    color: $corporate_1;
                }
            }
            .price {
              &:not(.with_custom_taxes_details),
              &:not(.with_accomodation_tax_extra_info) {
                color: $corporate_1;
              }
            }
          }
        }
      }
  }

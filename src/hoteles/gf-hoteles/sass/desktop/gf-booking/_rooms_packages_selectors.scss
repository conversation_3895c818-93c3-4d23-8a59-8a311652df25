#wizard {
  min-width: 1140px;

  div#step-1 {
    @media only screen and (max-width: 1140px) {
      .rooms_packages_selectors_wrapper{
        width: 1077px !important;
      }
    }
    .rooms_packages_selectors_wrapper.has_packages {
      @include display_flex;
      justify-content: space-between;
      border: none;
      margin-bottom: 30px;
      overflow: hidden;

      .button_package_room {
        display: block;
        width: calc(50% - 5px); // 10px between tabs
        height: 73px;
        padding: 20px 50px;
        padding-left: 225px;
        margin: 0;
        background-color: transparent;
        border: none;
        box-sizing: border-box;
        opacity: 1;
        overflow: visible;
        z-index: 1;


        font-family: $title_family;
        font-weight: 300;
        font-size: 21px;
        letter-spacing: 1.15px;
        line-height: 25px;
        text-align: left;
        text-transform: capitalize;
        color: $corporate_2;
        @include transition(all, 0.3s);

        &.rooms_selector,
        &.packages_selector,
        &.club_rates_viewer {
          z-index: 2;
          color: #777777;

          &::before {
            position: absolute;
            content: '';
            top: 0;
            left: 0;
            pointer-events: none;
            right: 0;
            height: 75px;
            width: 1140px;
            background-image: url(https://storage.googleapis.com/cdn.paraty.es/coronado/files/tab_left_active_%23B19261.svg);
            background-repeat: no-repeat;
            background-size: contain;
            opacity: 0;
          }

          &::after {
            content: "";
            @include center_y;
            left: 160px;
            display: block;
            color: #777777;
            font-size: 35px;
            font-family: "Font Awesome 6 Pro";
          }

          &.active {
            z-index: 1;
            background-color: transparent !important;
            border: none !important;
            color: $corporate_2;

            &::before {
              opacity: 1;
            }

            &:after {
              color: $corporate_2;
              font-weight: 600;
            }
          }
        }

        &.rooms_selector, &.club_rates_viewer {
            &:after {
              content: "\f236";
            }
          }

        &.packages_selector {
          &::before {
            background-image: url(https://storage.googleapis.com/cdn.paraty.es/coronado/files/tab_right_active_%23B19261.svg);
            transform: translateX(calc(-50% - 5px));
          }

          &:after {
            content: "\f06b";
          }

          &.with_number .packages_number:before {
            background: $corporate_1;
          }
        }
      }
    }
  }
}

.map_hotels_list_wrapper {

  .hotel_poi_info {
    padding: 0;
    min-height: 70px;

    .picture_poi {
      width: 80px;
      max-width: initial;
      height: 80px;
    }

    .poi_tit_desc_wrapper {
      font-family: $font_2;
      color: $corporate_1;

      .booking_poi_button {
        background-color: $corporate_1;
        padding: 10px 20px;
      }
    }
  }
  .gm-style-iw-d{
    .hotel_poi_title{
      font-family: $font_2!important;
      font-weight: 400!important;
      color: $corporate_3;
      line-height: 24px;
      letter-spacing: .32px!important;
    }
    .hotel_price_regimen_name{
      font-family: $font_2;
      font-weight: 300!important;
      color: $black!important;
      line-height: 24px;
      letter-spacing: 0.32px!important;
    }
    .not_available_prices, .no_availability_hotel_button{
        span{
          font-family: $font_2;
          font-weight: 300;
          color: $black;
          line-height: 24px;
          letter-spacing: .32px;
        }
      }
    .hotel_price_regimen_name{
      font-family: $font_2;
      font-weight: 300;
      color: $black;
      line-height: 24px;
      letter-spacing: .32px;
    }
    .booking_poi_button{
      background: $corporate_3!important;
      letter-spacing: 1.25px!important;
      text-transform: capitalize;
      font-weight: 100!important;
      color: white!important;
    }
    .price_btn_wrapper{
      .price_wrapper{
        .currencyValue, .monedaConv, .price_text{
          color: $black;
          font-family: $font_2;
          font-weight: 100;
          letter-spacing: .32px!important;
          line-height: 24px;
        }
        .price_text{
          font-weight: 300!important;
        }
      }
    }
  }
  .gm-style-iw-a{
    &::before{
      content: "";
      width: 95px;
      height: 28px;
      background: transparent;
      border: 2px solid $corporate_3;
      position: absolute;
      border-radius: 10px;
      top: -52px;
      left: -50px;
    }
  }

  .gm-style-iw-chr{
        height: 0;
  }

}
.popup_maps_wrapper{
  .content_wrapper{
    .hotels_info_wrapper{
      background: white;
      .info_wrapper{
        .hotels_found_wrapper{
          .hotels_found_translation, .total_hotels{
            font-family: $font_1;
            color: $corporate_3;
            font-weight: 400;
          }
        }
        .show_option{
          font-family: $font_2;
          letter-spacing: .32px!important;
          color: $black;
          font-weight: 300!important;
          line-height: 24px;
        }
      }
    }
  }
  .hotels_info_wrapper{
    .info_wrapper{
      .top{
        .view_list_button{
          font-family: $font_2;
          font-weight: 300;
          color: $black;
          line-height: 24px;
          letter-spacing: .32px;
          text-decoration: none;
          span{
            text-decoration: underline;
          }
          i{
            font-family: "Font Awesome 5 Pro";
            text-decoration: none;
          }
        }
      }
    }
    .hotels_wrapper{
      .hotel_element{
        border-radius: 10px;
        &.selected, &:hover{
          border-bottom: 3px solid $corporate_3;
        }
        .hotel_info_wrapper{
          .hotel_name{
            font-family: $font_2;
            font-weight: 300;
            color: $black;
            line-height: 24px;
            letter-spacing: .32px;
          }
          .hotel_desc{
            .hotel_price_regimen_name{
              font-family: $font_2;
              font-weight: 300;
              color: $black;
              line-height: 24px;
              letter-spacing: .32px;
            }
          }
          .hotel_booking_wrapper{
            .booking_content{
              .hotel_price{
                .currencyValue, .monedaConv{
                  font-family: $font_2;
                  color: $black;
                  line-height: 24px;
                  letter-spacing: .32px;
                }
                .price_information{
                  font-family: $font_2;
                  font-weight: 300;
                  color: $black;
                  line-height: 24px;
                  letter-spacing: .32px;
                }
              }
              .booking_button{
                background: $corporate_3;
                letter-spacing: 1.25px;
                text-transform: capitalize;
                width: 187px;
                font-weight: 100;
                color: white;
              }
              .not_available_prices, .no_availability_hotel_button{
                span{
                  font-family: $font_2;
                  font-weight: 300;
                  color: $black;
                  line-height: 24px;
                  letter-spacing: .32px;
                }
              }
            }
          }
        }
        .left_wrapper{
          .label_gallery{
            font-family: $font_2;
            font-weight: 300;
            font-size: 16px;
            color: $black;
            line-height: 24px;
            letter-spacing: .32px;
          }
        }
      }
    }
  }
}

.hotel_content_popup {
  .hotel_name {
    font-family: $font_2;
    color: $corporate_1;

  }
}
&:not(.complete_calendar_mobile) {
  .datepicker_wrapper_element,
  .datepicker_wrapper_element_2,
  .datepicker_wrapper_element_3 {
    display: none !important;
  }
}

&.complete_calendar_mobile .datepicker_wrapper_element,
&.complete_calendar_mobile .datepicker_wrapper_element_2,
&.complete_calendar_mobile .datepicker_wrapper_element_3 {
  z-index: 1000000 !important;
  position: fixed !important;
  min-width: unset;
  top: 0 !important;
  left: 0 !important;
  width: 100vw;
  height: 100vh;
  overflow-y: scroll;

  .specific_month_selector, .go_back_button {
    display: none !important;
  }

  .header_datepicker {
    background: transparent;
    color: $grey_dark;

    .close_button_datepicker {
      width: 30px;
      height: 30px;
      top: 15px;
      right: 15px;
      border: none;
    }

    .specific_date_selector {
      &::after, &::before {
        content: unset;
      }
    }
  }

  .datepicker_ext_inf_sd,
  .datepicker_ext_inf_ed {
    padding: 0 20px;

    .ui-datepicker-next, .ui-datepicker-prev {
      display: none !important;
    }

    .ui-widget-content {
      width: 100% !important;

      & > div {
        width: 100%;
      }

      .ui-widget-header {
        margin-right: 0;
        background: transparent;

        .ui-datepicker-title {
          color: $grey_dark;
        }
      }

      .ui-datepicker-calendar {
        td {
          height: 45px;
          background: transparent !important;
          border: none;

          a.ui-state-active {
            background: $corporate_1 !important;
            color: white;
          }
        }

        td.ui-datepicker-start_date {
          span {
            background: $corporate_1 !important;
            color: white;
          }
        }

        td.last-highlight-selection {
          a {
            background: white !important;
            color: $corporate_2 !important;
          }
        }
      }
    }
  }
}
.site-header {
  background-image: url(https://storage.googleapis.com/cdn.paraty.es/fives-beach/files/header%20beach%20hotel.png);
}

div#full_wrapper_booking .boking_widget_inline .booking_form .wrapper_booking_button button.submit_button,
div#step-1 #rooms_b1_wrapper .preciosHabitacion .listadoHabsTarifas .regimen_tr_element .botonReservarColumn button,
div#step-3 #personal-details-form .booking_details_prices_wrapper .booking-button,
div#step-3 #personal-details-form .personal_details_payment_wrapper .personal_details_grid .lock_rates_wrapper .lock_price .price_bullet,
.price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .btn_booking.active::after {
  background: $booking_beach !important;

  &:hover {
    background: $black !important;
  }
}

div#full_wrapper_booking .boking_widget_inline .booking_form .wrapper_booking_button button.submit_button:hover {
  background: $black !important;
}

div#step-1 #rooms_b1_wrapper .preciosHabitacion .listadoHabsTarifas .regimen_tr_element .precioTotalColumn .priceValues .precioGeneralDiv .tax_inc_info {
  color: $secondary_beach !important;
}

div#step-1 #rooms_b1_wrapper .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_description_name_wrapper .cabeceraNombreHabitacion .just_booking_message,
div#step-1 #rooms_b1_wrapper .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_description_name_wrapper .cabeceraNombreHabitacion .very_asked_message,
div#step-1 #rooms_b1_wrapper .preciosHabitacion .listadoHabsTarifas .regimen_tr_element .precioTotalColumn .priceValues .promotion_percentage_square {
  background: $secondary_beach !important;
}

.fancybox-overlay {
  .fancybox-outer {
    .fancyboxContent_second_v {
      .room_services {
        .service_element {
          width: 85%;
        }
      }
    }
  }

  .container_popup_booking {
    .gif_wrapper {
      .default_line_loading {
        background-color: $black !important;
      }
    }
  }
}

div#reservation {
  .footer_buttons_extra_functionality {
    .booking-button {
      background: $booking_beach;
    }
  }
}

div#step-2 {
  .booking-box--search , .hidden_booking_summary{
    .booking-box__content {
      .booking-search-results__new-search {
        .booking-button {
          background: $booking_beach;
        }
      }
    }
  }

  .additional_services_total_wrapper{
    .perform_additional_services_booking{
      background: $booking_beach;
    }
  }
   .booking_button_wrapper {
    .booking-button {
      background: $booking_beach;
    }
  }
}

div#step-1 {
  #rooms_b1_wrapper {
    .preciosHabitacion {
      .listadoHabsTarifas {
        .regimen_tr_element {
          .precioTotalColumn {
            .priceValues {
              .promotion_percentage_square {
                background: $booking_beach !important;

                .promotion_discount {
                  background: $booking_beach !important;
                }
              }
            }
          }
        }
      }
    }
  }
}

div#step-2 .hidden_booking_summary .booking-box__content .booking-search-results__new-search .booking-button,
div#step-2 .additional_services_total_wrapper .perform_additional_services_booking {
  background: $booking_beach !important;
  border-radius: 0!important;
  transition: none;
  text-transform: none;
  font-weight: 600;

  &:hover {
    background: #000 !important;
    opacity: 1;
  }
}
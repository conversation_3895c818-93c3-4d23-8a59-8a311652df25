.site-header {
    height: 75px;
    padding: 10px 0;
    .site-header__logo{
      img {
        display: inline-block;
        vertical-align: middle;
        height: 60px;
        max-height: 100px !important;
        margin-top: 5px;
      }
      span.hotel_name {
        display: inline-block;
        vertical-align: middle;
        color: #222;
        font-size: 16px;
        padding: 10px 10px 0;
      }
    }
    .site-header__ticks {
      margin-top: 5px;
      margin-right: 50px;

      .site-header__tick-item {
        margin-top: 5px;
        float: none;
        display: inline-block;
        vertical-align: middle;
        width: 120px;
        margin-right: 0;

        p {
          width: 65px;
          font-size: 9px;
          line-height: 12px;
          letter-spacing: 1px;
          text-transform: uppercase;
          font-weight: 300;
          margin-left: -10px;
          text-align: center;
        }
        .icon-checkmark {
          font-size: 50px;
          margin-top: -10px;
          display: inline-block;

          &:before {
            font-size: 42px !important;
            line-height: 42px !important;
          }
        }
        &:nth-child(2) {
          p {
            width: 50px;
          }
        }
        &:first-of-type {
          margin-top: 5px !important;
          .icon-checkmark{
            padding: 5px !important;
            &:before {
              font-family: 'icomoon' !important;
              content: "\e93b" !important;
            }
          }
          p {
            top: 0 !important;
          }
        }
        &:nth-of-type(2) .icon-checkmark:before {
          font-family: 'icomoon' !important;
          content: "\e98b" !important;
        }
      }
    }
    .language_header_selector_booking_process {
      border: none;
      width: 150px;
      text-transform: uppercase;
      .selected_language {
        font-size: 16px;
        margin-top: 0;
        color: $corporate_1 !important;
        font-weight: 400;
        @extend .fa-caret-down;
        &:before {
          @extend .fa;
          position: absolute;
          top: 50%;
          right: 0;
          -webkit-transform: translate(0%,-50%);
          -moz-transform: translate(0%,-50%);
          -ms-transform: translate(0%,-50%);
          -o-transform: translate(0%,-50%);
          transform: translate(0%,-50%);
          color: $corporate_1;
          font-size: 18px;
        }
        i {
          font-size: 20px;
        }
      }
    }
  }
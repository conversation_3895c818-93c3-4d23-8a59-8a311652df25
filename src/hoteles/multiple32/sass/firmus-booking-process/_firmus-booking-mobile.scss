@import "confirmations";
@import "booking0_mobile";

.phone_mobile_dropdown {
  display: none;
}
.room_rates_list {
  .modal_launch {
    color: #375636 !important;
    font-size: 12px !important;
    font-weight: 500;
  }
}

.buttons_bottom .btn_option {
  background: $black !important;
}

.matcher-main-wrapper.rate-check-v4 .matcher-header-wrapper {
  background: $black !important;
}

.matcher-main-wrapper.rate-check-v4 .matcher-our-price-container {
  background: $black !important;
}

.matcher-main-wrapper.rate-check-v4 .matcher-ourprice-wrapper {
  background: #BE9800 !important;
}

.matcher-main-wrapper.rate-check-v4 .matcher-full-body-wrapper .matcher-body-wrapper .matcher-board-selector .background-board-image {
  background: #BE9800 !important;
}


footer {
  .row {
    flex-direction: column;

    .info {
      text-align: center;
    }
  }
}

.search-resume-wrapper {
  font-weight: 400;
  background-color: $grey;
}


.btn {
  font-weight: 400 !important;
}

.double_button_wrapper {
  .modify_search, .show_calendar, .back_button {
    margin: 5px;
    font-family: $title_family;
    text-transform: none;
    font-size: 16px;
  }

  .modify_search, .back_button {
    color: white;
    border: 1px solid $corporate-1;
    background-color: $corporate-1;
  }

  .show_calendar,
  .back_button {
    background: white;
    color: $black;
    border-color: $black;
  }

  .re-search_button {
    font-family: $title_family;
    font-size: 16px;
    text-transform: none;
    background: $corporate_1;
    margin: 8px 5px;
  }

  .close_button {
    font-family: $title_family;
    font-size: 16px;
    text-transform: none;
    background: $black;
    margin: 8px 5px;
  }
}

.price_calendar_wrapper.is_mobile .bottom_wrapper .bottom .right_wrapper .btn_booking {
  border-radius: 0;
  background: #FF5B5A;
  font-size: 20px;
  font-weight: 500;
  font-family: $title_family;
  text-transform: none;
  color: white;
}

#btn-finish-booking {
  font-family: $title_family;
  font-size: 20px;
  text-transform: none;
}

.booking_widget_wrapper {
  background: $light_grey;

  form {
    .input_wrapper {
      &::before {
        background-color: $corporate-1;
      }
    }
  }

  #entry_date_popup, #departure_date_popup {
    .menu_controller .dates_button {
      height: 56px;
    }
  }
  .flexible_dates_wrapper {
    &.active {
        top: 116px;
    }
  }
}

.counter_box .control {
  border-color: $corporate_1;

  &:not(.add)::before {
    color: $corporate_1;
  }

  &.add {
    background: $corporate_1;
  }
}

.main_content_wrapper.step_0 {
  .tabs_wrapper {
    .tabs {
      li {
        width: 45% !important;

        .tab_btn.club.active {
          .tab_text {
            padding-left: 0;

            img {
              height: 20px;
            }
          }
        }
      }
    }
  }

  .room_list {
    .room_pack_option {
      .very_asked {
        background: $corporate-1;
        text-transform: inherit;
        font-weight: 500;
      }

      .has_modal {
        .room_name {
          .title {
            font-family: $title_family;
            text-transform: none;
            font-weight: 500;;
            font-size: 20px;
          }
        }
      }

      .rates_details_wrapper {
        .rate_selected_title {
          background-color: $grey;

          .modal_launch {
            color: $black !important;
            font-weight: 400;
          }
        }

        .regime_item_content {
          .regime_description {
            .regime_offer_detail {
              color: $green !important;
            }

            .prices_options {
              .price_through {
                color: $green !important;

                span {
                  color: $green !important;
                }
              }

              .final_price {
                color: $black !important;
              }
            }
          }

          .regime_price_wrapper {
            .submit {
              span {
                border-radius: 0;
                background: $corporate-1;
                font-size: 20px;
                font-weight: 500;;
                font-family: $title_family;
                text-transform: none;
                min-width: max-content;
              }
            }
          }
        }
      }
    }
  }
}

.main_content_wrapper.step_1 {
  .additional_service_form {
    .continue_booking {
      border-radius: 0;
      background: $corporate-1;
    }

    .additional_services_wrapper {
      .counter_box {
        .add {
          background: $black;
          border-color: $black;
        }

        .subtract {
          border-color: $black;

          &::before {
            color: $black;
          }
        }
      }

      .supplement_element {
        .product_details {
          .add_service {
            background: $corporate-1;
          }
        }
      }
    }
  }
}

.all_additional_services_wrapper .category_wrapper:not(.upgrade) .additional_services_wrapper .additional_service_element.selected .add_service_button {
  background: #d5c4bb !important;
}

.main_content_wrapper.step_2 {
  #btn-finish-booking {
    background-color: $corporate-1;
    border-radius: 0 !important;
  }
}

#main_modal {
  .body_modal.rooms_features_modal_wrapper .body_modal_content .modal_container.room_content {
    .content_title .title {
      font-family: $title_family;
      color: $black;
      font-size: 20px;
      text-transform: none;
    }

    .icons_room {
      .room_services {
        .service_element {
          i {
            font-size: 20px;
          }

          .service_element {
            font-size: 12px;
            font-weight: 400;
          }
        }
      }
    }

    .room_description {
      font-weight: 400;
      line-height: 21px;
    }
  }
}

.loading_animation_popup {
  background-color: rgba(255, 91, 90, 0.9) !important;

  .spinner_animation {
    > div {
      background-color: white !important;
    }
  }
}

#reservation {
    h3 {
        font-weight: bold;
        margin-top: 10px;
    }
}
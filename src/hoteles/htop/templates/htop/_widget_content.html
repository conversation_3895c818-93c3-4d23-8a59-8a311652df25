{% if popup %}
    <a href="#top_section_popup" class="top_popup_link"><i class="fal fa-phone"></i>{{ popup.description|safe }}</a>
{% endif %}

{% if logo %}
    <div class="logotype_widget">
        <img src="{{ logo.servingUrl }}" alt="Logo">
    </div>
{% endif %}

{% if popup %}
    <div id="top_section_popup" class="top_section_popup">
        <div class="popup_content_wrapper">
            <span class="close_popup"><i class="fal fa-times"></i></span>
            {% if popup_contact and popup_contact.subtitle %}
                <div class="popup_title">
                    {{ popup_contact.subtitle|safe }}
                </div>
            {% endif %}
            {% if popup_contact and popup_contact.pictures %}
                <ul class="popup_list">
                    {% for x in popup_contact.pictures %}
                        <li>
                            {% if x.link %}
                                <a href="{{ x.link }}"></a>
                            {% endif %}
                            {% if x.icon %}
                                <i class="{{ x.icon|safe }}"></i>
                            {% endif %}
                            <div class="info_contact">
                                {% if x.title %}
                                    <span class="title_link_popup">
                                    {{ x.title|safe }}
                                </span>
                                {% endif %}
                                {% if x.description %}
                                    <span class="description_link_popup">
                                    {{ x.description|safe }}
                                </span>
                                {% endif %}
                            </div>
                        </li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
    </div>
{% endif %}

{% if booking_steps %}
    <div class="booking_steps">
        <div class="content_wrapper">
            <div class="step step_1">
                <div class="number_step">
                    <span>1</span>
                    <i class="fa-light fa-check"></i>
                </div>
                <span class="booking-step">
                    {{ T_seleccionar_hotel }}
                </span>
            </div>
            <div class="step step_2">
                <div class="number_step">
                    <span>2</span>
                    <i class="fa-light fa-check"></i>
                </div>
                <span class="booking-step">
                    {{ T_seleccionar_fechas }}
                </span>
            </div>
            <div class="step step_3">
                <div class="number_step">
                    <span>3</span>
                    <i class="fa-light fa-check"></i>
                </div>
                <span class="booking-step">
                    {{ T_seleccionar_ocupacion }}
                </span>
            </div>
        </div>
    </div>
{% endif %}

{% if widget_buttons %}
    <div class="widget_buttons">
        <div class="content_wrapper">
            {% for button in widget_buttons %}
                <a class="button" href="{{ button.external_link|safe }}" {% if button.datalayer_name %}data-datalayer-name="{{ button.datalayer_name|safe }}"{% endif %} target="_blank">
                    {{ button.description|safe }}
                </a>
            {% endfor %}
        </div>
    </div>
{% endif %}

{% if applicationIds %}
    <input name='applicationIds' type='hidden' value='{{ applicationIds }}'>
{% endif %}

<script>
    let popup_link = $('.top_popup_link');
    let popup_wrapper = $('#top_section_popup');
    let close_popup = popup_wrapper.find('.close_popup');

    popup_link.on('click', function (e) {
        e.preventDefault();
        popup_wrapper.fadeIn().addClass('active');
        $(".booking_steps").css("opacity", ".5");
    })

    close_popup.on('click', function () {
        popup_wrapper.fadeOut().removeClass('active');
        $(".booking_steps").css("opacity", "1");
    })
</script>
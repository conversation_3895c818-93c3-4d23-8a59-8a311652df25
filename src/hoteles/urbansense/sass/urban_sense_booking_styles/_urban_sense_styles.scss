@import "calendar_disco_styles";

@include booking_header(white, $corporate_1, $corporate_1, $corporate_1, white);

@import "site-header";
@import "booking_widget";
@import "booking0";
@import "club";


div.site-header {
  .site-header__logo {
    img {
      margin: 10px;
    }
  }
}

#step-3 .booking_details_prices_wrapper .booking_info_element.pet_element {
  color: $corporate_1;
}

.clearfix {
  padding-bottom: 20px;
}

.no-availability-section {
  min-height: 110px;
}

div#step-1 .regimen-description-v2 {
  top: -13px;
}

.custom_pictures_range {
  margin: 0 auto 30px;
}

.rooms_packages_selectors_wrapper {
  position: relative;
  border: none;
  margin-bottom: 30px;
  margin-top: 30px;
  display: flex;
  //flex-direction: row-reverse;

  .button_package_room {
    position: relative;
    font-family: $title_family;
    padding: 20px 15px;
    margin: 0;
    height: auto;
    font-size: 24px;
    font-weight: 400;
    display: inline-block;
    vertical-align: bottom;
    text-transform: capitalize;
    width: calc(50% - 5px);
    text-align: center;
    opacity: 1;
    background: $grey-room;
    border-width: 2px 2px 0 2px;
    border-style: solid;
    border-color: transparent;
    color: grey;
    overflow: visible;
    border-radius: 10px 10px 0 0;

    &:last-of-type {
      margin-right: 10px;

      &:after {
        content: '\f06b';
        font-weight: 300;
        font-family: "Font Awesome 5 Pro" !important;
        @include center_y;
        left: 120px;
        font-size: 42px;
        color: $black;
      }
    }

    &:last-of-type {
      &:after {
        content: '\f02b';
        font-weight: 300;
        font-family: "Font Awesome 5 Pro" !important;
        @include center_y;
        left: 60px;
        font-size: 42px;
        color: $corporate_4;
      }
    }

    &:before {
      content: '';
      height: 2px;
      position: absolute;
      bottom: 0;
      left: -2px;
      right: -12px;
      background: $black;
    }

    big {
      font-size: 48px;
      line-height: 45px;
      display: inline-block;
      vertical-align: middle;
      padding: 7px 20px 7px 10px;
      margin: -10px 20px -10px 0;
      border-right: 1px solid $corporate_1;
    }

    .extra_info {
      display: none;
      padding: 20px;
      left: 0;
      font-size: 20px;
      font-weight: lighter;

      b {
        font-weight: bold;
      }
    }

    &.active {
      height: auto;
      background: white;
      color: $black;
      border-color: $black;
      font-weight: bold;
      border-radius: 10px 10px 0 0;

      &:before {
        background: transparent;
      }

      &:after {
        color: $corporate_2;
      }

      big {
        border-right: 1px solid $corporate_1;
      }

      .extra_info {
        display: none;
      }
    }
  }
}


#packages_b1_wrapper {

  .package_element_wrapper {
    .package_room_pictures_selector {
      .picture_selector {
        background-color: $black;
      }
    }

    .package_description_wrapper {
      .package_title {
        font-family: $title_family;
        color: $black;
      }

      .package_description {
        font-family: $text_family;
        color: $black;
      }
    }
  }
}

#wizard {

  ul.actual_wizard_step {
    padding-top: 20px;
    text-align: center;

    .wizard-tab--small,
    .wizard-tab--big {
      float: none;
      width: 25%;
      display: inline-grid;
      position: relative;
      margin-top: 7px;

      &:last-of-type {
        &:after {
          display: none;
        }
      }

      a, a.disable {
        font-family: $font_family;
        background-color: transparent;
        border: none;
        font-weight: normal;
        font-size: 10px;
        padding-top: 3px;
        letter-spacing: 2px;

        &:after, &:before {
          display: none;
        }
      }

      a {
        color: $black;
      }

      a.disable {
        color: lightgray;
        margin-top: 5px;
      }

      &:before {
        content: "";
        color: white;
        z-index: 1;
        text-align: center;
        border: 1px solid lightgray;
        justify-self: center;
        width: 5px;
        height: 5px;
        padding: 5px;
        border-radius: 50%;
        font-weight: 300;
        margin: 5px auto;
      }

      &:after {
        content: "";
        width: 267px;
        border: 1px solid lightgray;
        position: absolute;
        left: 150px;
        top: 14px;
      }
    }
  }

  &.step_0 {
    ul.actual_wizard_step {
      .wizard-tab--small:nth-of-type(1) {
        margin-top: auto;

        &:before {
          content: "\f236";
          border: none;
          height: auto;
          font-family: "Font Awesome 5 Pro";
          font-size: 16px;
          font-weight: 300;
          background: $corporate_3;
          width: 20px;
        }

        &:after {
          top: 20px;
        }
      }
    }
  }

  &.step_1 {
    ul.actual_wizard_step {
      .wizard-tab--small:nth-of-type(2) {
        margin-top: auto;

        &:before {
          content: "\f4fc";
          border: none;
          height: auto;
          font-family: "Font Awesome 5 Pro";
          font-size: 16px;
          background: $corporate_3;
          width: 20px;
        }

        &:after {
          top: 20px;
        }
      }
    }
  }

  &.step_2 {
    ul.actual_wizard_step {
      .wizard-tab--small:nth-of-type(3) {
        margin-top: auto;

        &:before {
          content: "\f4fc";
          border: none;
          height: auto;
          font-family: "Font Awesome 5 Pro";
          font-size: 16px;
          background: $corporate_3;
          width: 20px;
        }

        &:after {
          top: 20px;
        }
      }
    }
  }

  &.step_3 {
    ul.actual_wizard_step {
      .wizard-tab--small:nth-of-type(4) {
        margin-top: auto;

        &:before {
          content: "\f518";
          border: none;
          height: auto;
          font-family: "Font Awesome 5 Pro";
          font-size: 16px;
          background: $corporate_3;
          width: 20px;
          padding: 5px;
          margin-bottom: 0;
        }

        &:after {
          top: 20px;
        }
      }
    }
  }
}

.booking_engine_wrapper_process #booking.boking_widget_inline {
  .entry_date_wrapper .entry_date, .stay_selection .departure_date_wrapper {
    font-weight: 700;
  }

  .guest_selector .placeholder_text {
    font-weight: 500;
    font-size: 22px;

    span {
      font-weight: 500;
    }
  }
}

.selection_price_wrapper {
  .total_price_label {
    margin-bottom: 6px !important;
    font-family: $text_family;
  }

  .total_price_value {
    &.currencyValue {
      font-family: $text_family;
      font-size: 30px;
      font-weight: normal;
    }
  }

  .monedaConv {
    font-family: $text_family;;
    font-size: 21px;
    font-weight: normal;
  }
}

@import "resume_styles";

.no_availability_message_booking0 {
  font-family: $text_family;
  background: $corporate_1;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

.search_other_dates_button {
  background: $corporate_2;
}

@import "rooms_styles";
@import "additional_services_styles";

div#step-1 {
  background-color: transparent;
  font-family: $text_family;
}

div#step-1 .precioNocheColumn .precioTachadoDiv {
  color: red;
}

@import "cards_banners";



div#step-1 {
  .booking-search-results.booking-box__content {
    padding: 0 0 10px 0 !important;
  }

  table.listadoHabsTarifas {
    td.precioTotalColumn,
    td.precioNocheColumn,
    td.regimenColumn {
      .tTextoOferta,
      .tPrecioOferta,
      .tPrecioOferta {
        color: $corporate_2;
      }

      .tTextoOferta {
        color: $red;
        font-size: 13px;
        font-weight: normal;
      }
    }

    td.precioTotalColumn,
    td.precioNocheColumn {
      color: $black;
    }
  }

  .contTipoHabitacion {
    .booking-button {
      color: white;

      &:hover {
        color: white;
      }
    }
  }

  .precioTotalColumn {
    .tPrecioTotal {
      font-weight: 300;
    }

    .priceTitle {
      font-weight: 300;
    }
  }
}

div#step-1 {
  .regimen-popup-v2{
    position: relative;

    img{
      opacity:0;
    }

    &:before{
      position: absolute;
      font-family: 'Font Awesome 5 Pro';
      font-size: 15px;
      width: 15px;
      height: 15px;
      content: '\f059';
      top: 5px;
      left: 5px;
      color: #00a1c6;
    }
  }

  .contTipoHabitacion {
    margin-top: 0;
    padding: 19px;

    .descripcionHabitacion {
      .desc {
        strong {
          font-weight: 700;
        }
      }
    }

    .contFotoHabitacion .occupancy {
      b {
        text-transform: uppercase;
      }
    }

    .contDescHabitacion {
      .room_description_name_wrapper {
        overflow: visible;
      }

      .just_booking_message {
        background: $black;
        font-weight: 500;
        color: white;
        margin-right: -40px;

        &:after {
          display: none;
        }
      }
    }

    .room_services {
      margin-left: 0;
      border-top-style: dotted;
      border-bottom-style: dotted;
      border-top-width: 1px;
      border-bottom-width: 1px;
      padding: 1px 0;
      bottom: -30px;

      .service_element {
        border-right-width: 1px;
        border-right-style: dotted;
        text-align: center;

        &:last-of-type {
          border-right: none;
        }
      }
    }
  }

  .precioTotalColumn .priceValues .promotion_percentage_square {
    width: 40px;
    line-height: 39px;
    position: absolute;
    top: 0px;
    right: -52px;
    margin: 0;

    .promotion_discount {
      width: auto;
      padding: 5px;
      right: 120%;
      left: auto;
      background: $green;
    }
  }
}

.rates_conditions_popup {
  .rate_description_content {
    overflow: visible;
    font-size: 14px;
    font-weight: 400 !important;
    max-height: 350px;
    padding: 15px 15px 0 15px;

    strong {
      font-weight: 700;
    }

    div {
      margin: 0;
    }

    ul {
      margin: 7px 0;

      li {
        list-style-type: disc;
        margin-left: 30px;
      }
    }
  }
}

div#step-2 {
  .booking-3-info, .booking-3-info--title, .booking-3-info--price {
    color: $corporate_1;
  }

  .booking_button_wrapper .booking-button {
    background-color: $corporate_1;
    transition: all .4s;

    &:hover {
      background-color: $corporate_3;
      color: white;
    }
  }

  .supplements_list_wrapper {
    .booking-2-service-container {
      .booking-2-container-description {
        color: $black;

        .booking-2-service-title {
          font-family: $title_family;
          color: $black;
          margin-left: 20px;
          text-transform: capitalize;
        }

        .booking-2-service-description {
          font-family: $text_family;
          color: $black;
        }

        .booking-2-service-item {
          .price_per_service_label,
          .currencyValue,
          .monedaConv {
            color: $black;
          }
        }

        .see_more_supplements_v2 {
          span {
            color: $black;
            background-color: transparent !important;

            &:hover {
              background-color: transparent !important;
              color: $corporate_1;
            }
          }
        }

        .booking-2-services-list {
          .services_buttons_wrapper {
            .add_service_element {
              background-color: $corporate_3;

              &:after {
                display: none;
              }

              &:hover {
                background-color: $corporate_1 !important;
                color: white !important;
                opacity: 1;
              }
            }
          }
        }
      }
    }
  }
}


div#step-3 {
  background: white;

  .personal_details_payment_wrapper,
  .booking_details_prices_wrapper {
    display: table-cell;
    min-height: 500px;
    position: relative;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.15);
  }

  .booking-button {
    font-family: $text_family;
    background-color: $corporate_1;

    &:hover {
      color: white;
    }

    &:before {
      background-color: $corporate_3;
    }
  }
}

.site-footer {
  background: white;
  color: $black;
  font-family: $text_family;
  font-size: 12px;
  padding-bottom: 15px;

  #footer_bottom_text p {
    font-size: 12px;
    font-weight: 300;
    letter-spacing: 1px;
  }
}

.fancybox-wrap.fancy-booking-search_v2 {
  .container_popup_booking {
    border: 1px solid $corporate_1;
  }

  .gif_wrapper .default_line_loading {
    background-color: $corporate_1;
  }

  .dots_loader .dot {
    background: #00235A;
  }

  .description_bottom_popup_booking {
    color: #00235A;
    font-family: $font_1;
    font-weight: bolder;
  }
}

#calendar_price_availability {
  * {
    font-family: 'Open Sans', sans-serif;
  }

  .header_wrapper_calendar_availability {
    .graph_calendar_selector {
      .calendar_button, .graph_button {
        font-family: 'Open Sans', sans-serif;
        background: #D7D7D7;
        color: #333;
        text-transform: uppercase;
        -webkit-box-shadow: 0px 3px 39px -13px rgba(0, 0, 0, 0.5);
        -moz-box-shadow: 0px 3px 39px -13px rgba(0, 0, 0, 0.5);
        box-shadow: 0px 3px 39px -13px rgba(0, 0, 0, 0.5);

        span {
          background: transparent;
        }

        &.active {
          background: white;
        }
      }

      .calendar_button:before, .graph_button:before {
        content: "\e9a1";
        position: absolute;
        top: 50%;
        -webkit-transform: translate(-50%, -50%);
        -moz-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        font-family: "icomoon", sans-serif;
        font-size: 30px;
        speak: none;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }

      .calendar_button:before {
        content: "\e9a1";
      }

      .graph_button:before {
        content: "\f080";
        font-family: "Font Awesome 5 Pro", sans-serif;
      }
    }
  }

  #prices-calendar {
    background: white;
    -webkit-box-shadow: 0px 30px 50px -23px rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 0px 30px 50px -23px rgba(0, 0, 0, 0.5);
    box-shadow: 0px 30px 50px -23px rgba(0, 0, 0, 0.5);

    .overlay_loading {
      background: rgba(white, .8);
    }

    .entitySelector {
      label {
        color: #333;
      }
    }

    .calendars-section {
      .calendars {
        .calendars_wrapper {
          table.calendar .first-selection .available-day, table.calendar .first-selection .day-content, table.calendar .first-selection .day, table.calendar .end-selection .available-day, table.calendar .end-selection .day-content, table.calendar .end-selection .day {
            background: $corporate_1 !important;
          }

          table.calendar .first-selection .day-content:before {
            border-color: transparent transparent transparent $corporate_1;
          }

          table.calendar .end-selection .day-content:before {
            border-color: transparent $corporate_1 transparent transparent;
          }

          table.calendar .selected-cell-parent:not(.first-selection):not(.end-selection) .available-day, table.calendar .selected-cell-parent:not(.first-selection):not(.end-selection) .day-content {
            background-color: $corporate_2 !important;
          }

          .calendar {
            * {
              border-color: white;
            }

            .day.available-day {
              background-color: $green;
            }

            tbody {
              tr {
                th:not(.day_label_element) {
                  font-family: 'Open Sans', sans-serif;
                  background: $corporate_1;
                  font-size: 25px;
                  padding: 10px 0;
                  letter-spacing: 3px;
                }
              }
            }
          }
        }
      }

      .graphs_fields_wrapper {
        background: white;
      }

      .legend {
        .available-day-box {
          background-color: #34A853;
          border-color: #34A853;
        }

        ul li p {
          color: #333;
        }
      }

      .buttons-section {
        .actual_selection_info_wrapper {
          .nights_number_wrapper {
            background: $corporate_1;
          }

          .selection_price_wrapper {
            .total_price_label {
              margin-bottom: -1px !important;
              font-size: 11px;
            }

            .total_price_value {
              font-size: 23px;
            }
          }
        }

        .button.modifyButtonCalendar {
          font-family: 'Open Sans', sans-serif;
          background-color: $corporate_1 !important;
        }
      }
    }
  }
}

.booking_engine_wrapper_process.has_babies {
  .wrapper_booking_button {
    width: 440px !important;
  }

  .stay_selection {
    width: 35% !important;
  }
}

.hidden_booking_summary.showed {
  .booking-search-results__search-data {
    font-weight: 400;

    .booking-3-info, .booking-hotel-name {
      color: $black !important;
    }

    .booking-title-info:not(.booking-hotel-name) {
      font-weight: 400;
      color: $black;
    }
  }

  .booking-search-results__rooms-list {
    font-weight: 400;

    .search-item .booking-title-info {
      font-weight: 400;
    }

    b {
      font-weight: 400;
    }
  }

  .booking-search-results {
    .booking-button {
      margin: 0;
    }
  }

  .booking-search-results.has_web_support {
    width: 85%;

    .booking-search-results__rooms-list {
      padding-left: 65px;
      padding-top: 45px;
    }

    .booking-search-results__new-search .booking-button {
      max-width: 210px !important;
    }
  }

  .call_center_wrapper {
    top: 10px;

    .web_support_label_1 {
      margin: 0 auto !important;

      &::before {
        font-size: 30px;
      }
    }
  }
}

.call_center_wrapper {
  width: 200px;
  right: 10px;
  top: 10px;
  bottom: 10px;
  border-radius: 5px;
  border: 1px solid $black;

  .web_support_label_1 {
    width: 90% !important;
    color: $black !important;

    &:before {
      transform: rotate(-90deg);
    }
    .web_support_wrapper {
      .web_support_number {
        text-align: left;

        span {
          display: block;
        }
      }
    }
  }
}

#currencyDiv {
  padding: 0 44px;
  margin: 5px 10px 0 !important;
}

.package_element_wrapper .package_prices_wrapper {
  .promotion_default_price.active + .since_prices_values {
    color: $corporate_2;
  }


  .perform_package_booking_search {
    background: $corporate_1;
    border-radius: 15px;

    &:hover {
      background: $corporate_2;
    }
  }

  .conditions_package_info_wrapper {

    a {
      background-image: none !important;
      position: relative;

      &:hover {
        color: $corporate_2 !important;
      }

      &::after {
        display: inline-block;
        content: '\f05a';
        font-family: "Font Awesome 5 Pro";
        color: $black;
      }
    }
  }
}

div#step-2 .booking-2-services-list .add_service_element {
  &:after {
    background: $corporate_2;
  }
}

.fancyboxContent_second_v .room_popup_individual_element {
  padding-bottom: 40px;

  .popup_room_pictures {
    margin-top: 10px;
  }

  .popup_room_pictures_next, .popup_room_pictures_prev {
    color: black !important;
  }
}
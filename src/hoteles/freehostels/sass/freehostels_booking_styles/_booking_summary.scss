#wizard:not(.step_1) {
  .booking-box--search {
    margin: 20px auto 50px !important;
  }

  .clearfix .booking-box--search, .hidden_booking_summary {
    .booking-search-results {
      border: 0;
      padding: 0;
      background: transparent;

      &.has_web_support {
        width: 81% !important;
      }

      .title_booking_breakdown {
        font-family: $title_family;
        font-size: 31px;
        letter-spacing: 1.86px;
        line-height: 36px;
        font-weight: bold;
        position: relative;
        padding: 0;
        width: 200px;
        color: $black;

        &::before {
          content: "\f0f3";
          position: absolute;
          top: 20px;
          right: -30px;
          font-family: "Font Awesome 6 Pro";
          font-size: 45px;
          font-weight: 200;
        }

        &::after {
          content: "";
          position: absolute;
          width: 1px;
          height: 100%;
          right: -50px;
          top: 0;
          background: $black;
          opacity: .6;
        }
      }

      .booking-search-results__search-data {
        margin-left: 40px;

        .booking-3-info {
          font-size: 17px;
          font-weight: bold;
          letter-spacing: 0.85px;
          line-height: 20px;
          color: $black;
        }

        .booking-title-info, .notranslate {
          font-weight: 300;
        }

        i {
          color: $black;
          font-weight: 300;
          margin-right: 10px;
          font-size: 14px;
        }

        .fa-long-arrow-right {
          &::before {
            content: "\f061";
            font-family: "Font Awesome 6 Pro";
          }
        }

        .fa-long-arrow-left {
          &::before {
            content: "\f060";
            font-family: "Font Awesome 6 Pro";
          }
        }
      }

      .booking-search-results__rooms-list {
        right: 130px;
        top: 31px;

        .booking-title-info, span {
          font-weight: 300;
        }

        i {
          display: none;
        }

        .booking-title-info {
          &::before {
            content: "\f0c0";
            font-family: "Font Awesome 6 Pro";
            position: absolute;
            font-size: 14px;
            color: $black;
            font-weight: 300;
            left: 35px;
          }

          &.rooms_amount_label {
            &::before {
              content: "\f236";
            }
          }
        }
      }

      .booking-search-results__new-search {
        width: auto;
        top: 5px;

        &::before {
          display: none;
        }

        .booking-button {
          width: 200px;
          height: 72px;
          background: white;
          border: 1px solid $black;
          color: $black;
          padding: 10px 15px !important;
          transition: all .6s;
          font-size: 17px;
          font-family: $title_family;
          letter-spacing: 0.85px;
          line-height: 23px;
          font-weight: bold;

          &:hover {
            background: $black;
            color: white;
          }
        }
      }
    }
  }

  .hidden_booking_summary {
    .booking-search-results {
      &.has_web_support {
        width: 100% !important;
      }

      .booking-search-results__search-data {
        padding-left: 110px;
      }

      .booking-search-results__rooms-list {
        top: 5px;
        left: 0;

        .booking-title-info {
          &::before {
            left: 15px;
          }
        }
      }

      .booking-search-results__new-search {
        top: 40px;
      }
    }

    .call_center_wrapper {
      display: none;
    }
  }

  .call_center_wrapper {
    width: 209px;
    border-color: $black;
    height: 70px;
    top: 5px;

    .web_support_label_1 {
      width: 100%;

      &::before {
        transform: rotate(270deg);
        color: $black;
        font-size: 32px;
        margin-top: 0;
        margin-bottom: 0;
        padding: 0 5px;
      }

      .web_support_wrapper {
        font-family: $text_family;
        font-size: 14px;
        letter-spacing: 0;
        line-height: 17px;
        color: $black;
      }
    }
  }
}

#wizard.step_1 {
  .booking-search-results.booking-box__content {
    border: 0 !important;
  }

  .booking-button.booking-button--action {
    border-radius: 0;
    background: $corporate_1;
  }
}
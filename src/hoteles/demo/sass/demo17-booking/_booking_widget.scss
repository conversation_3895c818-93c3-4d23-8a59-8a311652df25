div#full_wrapper_booking {
  width: 100%;
  min-width: 1140px;
  padding: 0;
  color: $black;
  background: transparent;

  .boking_widget_inline {
    height: 81px;
    background: white;
    width: 100%;
    padding: 0 calc((100% - 1140px)/2);
    box-sizing: border-box;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);

    .booking_form {
      width: 1140px;
      height: 90px;
      margin: 0 auto;
      position: relative;

      .entry_date_label,
      .departure_date_label,
      .guest_selector label,
      .children_label,
      .babies_selector label,
      .rooms_label,
      .adults_label,
      .promocode_label,
      .promocode_input {
        font-family: $text_family;
        font-weight: 300;
        font-size: 14px;
        letter-spacing: 0.7px;
        line-height: 18px;
        color: $black !important;
      }


      .entry_date_label,
      .departure_date_label,
      .guest_selector label {
        font-size: 14px;
        font-weight: 300;
        letter-spacing: 0.7px;
        line-height: 18px;
        text-transform: capitalize;
        font-family: $text_family;
      }

      .entry_date_wrapper,
      .departure_date_wrapper,
      .guest_selector {
        display: inline-flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: stretch;
        background: transparent;
      }

      .adults_label,
      .children_label,
      .babies_selector label {
        text-transform: none;
      }


      .stay_selection .date_day,
      .guest_selector .placeholder_text {
        font-family: $text_family;
        font-weight: 400;
        font-size: 16px !important;
        letter-spacing: 0.8px;
        line-height: 21px;
        color: $black;
      }

      .stay_selection .departure_date_wrapper,
      .guest_selector {
        &::after {
          content: '';
          display: block;
          @include center_y;
          height: 50px;
          width: 2px;
          background: rgba(black, 0.1);
        }
      }

      .stay_selection {
        width: 320px;
        height: 100%;
        display: inline-flex;

        .entry_date_wrapper,
        .departure_date_wrapper {
          border-bottom: none;
          height: 100%;
        }

        .entry_date_wrapper {
          padding: 15px 15px 15px 0;

          &::after {
            content: '';
            display: block;
            @include center_y;
            height: 50px;
            width: 2px;
            left: 85%;
            background: rgba(black, 0.1);
          }
        }

        .departure_date_wrapper {
          padding: 15px 0 15px 55%;

          &::after {
            right: 11px;
          }

          .date_box {
            background: none;
          }
        }
      }

      .guest_selector {
        width: 190px;
        height: 100%;
        padding: 15px 0;
        margin: 0;
        align-items: center;

        &::after {
          right: -10px;
        }

        .placeholder_text {
          margin-top: 0;
        }

        b.button {
          display: none;
        }
      }

      .buttons_container_guests {
        .save_guest_button {
          background: $black !important;
        }
      }

      .room_list_wrapper {
        top: 110%;
        left: 270px;
        width: 345px;
        border-radius: 10px;
        padding: 15px;
        box-sizing: border-box;

        .room_title {
          display: block;
          width: 100%;
          background: #F8F8F8;
          height: auto;
          align-items: center;
          padding: 0 20px;
          text-transform: none;
          font-size: 16px;
          font-weight: 700;
          letter-spacing: 0.88px;
          line-height: 33px;
          margin-bottom: 30px;
          box-sizing: border-box;
        }

        .room_list {
          padding: 0;

          .room {
            display: block;

            .adults_selector, .children_selector {
              float: none;
              border: 0;
              display: flex;
              width: 100% !important;
              align-items: center;
              padding: 0 20px;
              justify-content: space-between;

              .adults_label, .children_label {
                height: auto;
                text-transform: none;
                font-size: 14px;
                font-weight: 400;
                letter-spacing: 1.68px;
                line-height: 22px;

                .range-age {
                  display: none;
                }
              }

              .room_selector {
                margin-right: 40px;
                margin-top: -5px;
                width: auto !important;
              }

              .selectricItems {
                width: 80px !important;
                left: -20px;
                overflow: visible !important;
                height: auto !important;

                li:not(.selected) {
                  background: none;
                }
              }
            }

            .children_selector {
              margin: 30px 0;
            }
          }

          .full_ages_wrapper {
            box-sizing: border-box;
            margin-bottom: 30px;
            padding: 0 20px;

            .kids_ages_label {
              font-size: 14px;
              font-weight: 400;
              letter-spacing: 1.68px;
              line-height: 22px;
              margin-right: 15px;
            }

            .kids_age_selection {
              display: flex;
              margin-top: 10px;

              .kid_age_element_wrapper {
                border-radius: 50px;
                border-left: 1px solid #c3c3c3;
                margin: 0 5px;
                width: 40px !important;

                .selectricWrapper {
                  margin: 0;

                  .label {
                    text-align: center;
                  }

                  .selectricItems {
                    li:not(.selected) {
                      background: none;
                    }
                  }
                }
              }
            }
          }
        }

        .buttons_container_guests {
          display: none;
        }

        &:before {
          border-bottom: white;
        }

        .room_list {
          border: none;
        }

        .add_room_element {
          letter-spacing: 0.7px;
          font-size: 14px;
          font-weight: 500;
          text-decoration: underline;
          position: relative;
          width: 100%;
          box-sizing: border-box;
          margin-left: 0;
          padding-left: 20px;
          padding-bottom: 10px;
          margin-top: 15px;

          &::before {
            position: absolute;
            content: "";
            top: -20px;
            height: 1px;
            left: -15px;
            right: -15px;
            background: #707070;
          }
        }
      }

      .wrapper_booking_button {
        width: 630px;
        height: 100%;

        .promocode_wrapper {
          width: 35% !important;
          padding: calc((68px - -10px) / 2) 10px; // 68px - full widget height, 20px - promocode label line height

          .promocode_label {
            font-size: 14px;
            letter-spacing: 0.7px;
            line-height: 17px;
            color: $black;
            opacity: .44;
            transform: translateY(-15px);

            &.hide {
              display: none;
            }
          }

          .promocode_input {
            outline-color: $corporate_1;
          }
        }

        .modify-calendar-button {
          box-sizing: border-box;
          width: 197px;
          float: left;
          margin: calc((84px - 64px) / 2) 0;
          margin-right: 10px;
          position: relative;
          display: inline-flex;
          padding: 0 10px 0 55px;
          align-items: center;
          border-radius: 0;
          font-family: $text_family;
          font-weight: 700;
          font-size: 15px;
          letter-spacing: 0.33px;
          line-height: 20px;
          text-align: center;
          height: 62px;
          text-transform: none;
          background: transparent;
          border: 1px solid $black;
          color: $black;
          @include transition(all, .5s);

          &::before {
            content: '\f073';
            font-family: "Font Awesome 5 Pro";
            color: $black;
            font-weight: 300;
            font-size: 20px;
            @include center_y;
            left: 20px;
            -webkit-font-smoothing: antialiased;
            @include transition(all, .5s);
          }

          &:hover {
            background: $black;
            color: white;

            &::before {
              color: white;
            }
          }
        }

        button.submit_button {
          box-sizing: border-box;
          position: absolute;
          width: 197px;
          height: 62px;
          margin: calc((84px - 64px) / 2) 0; // 84px - full widget height, 64px - button height
          padding: 0 10px 0 50px;
          border-radius: 0;
          font-family: $text_family;
          background: $corporate_1;
          font-weight: 400;
          font-size: 16px;
          text-align: center;
          letter-spacing: 0.35px;
          line-height: 21px;
          color: white;
          text-transform: capitalize;
          transition: all .4s;

          &::before {
            content: '\f363';
            font-family: "Font Awesome 5 Pro";
            font-weight: 300;
            font-size: 20px;
            @include center_y;
            left: 20px;
            -webkit-font-smoothing: antialiased;
          }

          &:hover {
            background-color: $corporate_2;
          }
        }

        .spinner_wrapper {
          width: 203px;
          height: 64px;
          background-color: rgba($corporate_1, 0.5);
          border-radius: 10px;
          top: calc((90px - 64px) / 2);
          right: 0;
        }
      }
    }
  }

  &.booking_widget_step_-1 {
    .boking_widget_inline {
      .booking_form {
        .stay_selection {
          margin-left: 46px;
          width: 300px;
        }

        .guest_selector {
          width: 234px !important;
        }

        .wrapper_booking_button {
          width: 560px;

          .promocode_wrapper {
            width: 45% !important;
          }

          button.submit_button {
            width: 230px;
          }
        }

        .room_list_wrapper {
          left: 320px;
        }

        .spinner_wrapper {
          width: 230px;
          right: 77px;
        }
      }
    }
  }
}

#full_wrapper_booking.has_babies {
  .boking_widget_inline {
    .booking_form {
      .entry_date_label,
      .departure_date_label,
      .guest_selector label,
      .children_label,
      .babies_selector label,
      .rooms_label,
      .adults_label,
      .promocode_label,
      .promocode_input {
        font-size: 12px;
      }

      .room_list_wrapper {
        label {
          height: 25px;
          letter-spacing: 1.65px;
        }

        .room_list {
          .room {
            padding: 10px 0;
            margin-bottom: 15px;
            border-radius: 10px;
            background-color: $corporate_2;

            .adults_selector,
            .children_selector,
            .babies_selector {
              background: none;
              border: none;
              padding: 0 25px;
              width: calc(100% / 2) !important;
            }

            .adults_selector {
              border-right: 1px solid rgba(black, 0.2);
            }
          }

          .room.room_with_babies {
            .adults_selector,
            .children_selector,
            .babies_selector {
              width: calc(100% / 3);
            }

            .children_selector.range_label_enabled,
            .babies_selector {
              .range-age {
                display: block;
                font-size: 10px;
              }
            }

            .children_selector {
              border-right: 1px solid rgba(black, 0.2);
            }
          }

          .full_ages_wrapper {
            padding: 0;
            margin-bottom: 20px;

            .kids_age_selection,
            .babies_age_selection {
              justify-content: flex-start;
              align-items: center;

              .kid_age_element_wrapper,
              .baby_age_element_wrapper {
                position: relative;
                width: calc((100% - (5px * 3)) / 4) !important;
                padding: 10px 30px 10px 15px;
                border: 1px solid rgba(black, 0.2);
                border-radius: 10px;
                box-sizing: border-box;

                &::before {
                  content: '\f107';
                  @include center_y;
                  right: 10px;
                  font-family: "Font Awesome 5 Pro";
                  font-weight: 300;
                  font-size: 25px;
                  color: $black;
                  -webkit-font-smoothing: antialiased;
                }

                .selectricWrapper {
                  margin: 0;

                  .selectric {
                    margin: 0;

                    .label {
                      margin: 0;
                    }
                  }
                }
              }

              .kid_age_element_wrapper + .kid_age_element_wrapper,
              .baby_age_element_wrapper + .baby_age_element_wrapper {
                margin-left: 5px;
              }
            }
          }
        }
      }

      .guest_selector {
        label {
          margin-bottom: 0;
        }
      }

      .wrapper_booking_button {
        .promocode_wrapper {
          width: 165px !important;
        }

        .modify-calendar-button {
          padding: 0 5px 0 45px;
        }

        button.submit_button {
          width: 180px !important;
          float: left !important;
        }
      }
    }
  }
}

.datepicker_wrapper_element {
  border-radius: 0;
  border-top: initial !important;

  &:before {
    display: none;
  }

  &:after {
    left: -405px !important;
  }

  .header_datepicker, .specific_month_selector {
    background: transparent !important;
    color: $black !important;

    .close_button_datepicker {
      border: 1px solid $black;
    }

    strong {
      color: $black !important;
    }
  }

  .datepicker_ext_inf_sd, .datepicker_ext_inf_ed {
    .ui-datepicker-inline {
      width: 670px !important;

      .ui-datepicker-group {
        .ui-datepicker-header {
          .ui-datepicker-prev {
            transform: none;
            margin-top: 21px;
            margin-left: 5px;

            span {
              &::before {
                font-size: 18px;
                content: "\f053";
                font-family: 'Font Awesome 5 Pro';
              }
            }
          }

          .ui-datepicker-next {
            transform: none;
            margin-top: 21px;
            margin-right: 5px;

            span {
              &::before {
                font-size: 18px;
                content: "\f054";
                font-family: 'Font Awesome 5 Pro';
              }
            }
          }

          .ui-datepicker-title {
            margin: 0 8px;
            line-height: 2.8em;
            background: $corporate-2;
            color: white !important;
          }
        }

        .ui-datepicker-calendar {
          tbody {
            tr {
              td {
                border: 5px solid transparent;

                &.ui-state-disabled {
                  background: transparent !important;
                }

                &.ui-datepicker-week-end {
                  background: $corporate-3 !important;
                }

                &.highlight {
                  background: rgba($corporate-1, 0.5);
                }

                .ui-state-active {
                  background: $corporate-1 !important;
                }

                .ui-state-hover {
                  color: white !important;
                }
              }
            }
          }
        }
      }
    }
  }
}

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
  font-family: $text_family;
  z-index: 1000;

  .header_datepicker {
    .specific_date_selector {
      font-family: $text_family;
    }
  }

  .datepicker_ext_inf_sd,
  .datepicker_ext_inf_ed {
    .ui-datepicker-title, .ui-datepicker-calendar, .ui-state-default {
      font-family: $text_family;
    }

    .ui-state-hover, .ui-state-active {
      background: $corporate-2 !important;
      color: white !important;
    }

    .ui-datepicker-start_date, .ui-datepicker-end_date, td.highlight, {
      .ui-state-default {
        background: $corporate-2 !important;
        color: white !important;
      }
    }

    .ui-datepicker {
      td.highlight, {
        a {
          background: $corporate-2 !important;
          color: white !important;
        }
      }
    }
  }
}
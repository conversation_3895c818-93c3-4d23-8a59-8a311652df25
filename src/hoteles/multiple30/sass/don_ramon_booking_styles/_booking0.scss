#step-0 {
  .booking_0_buttons_controller .see_list_button,
  .booking_0_buttons_controller .see_map_button {
    border-radius: 10px 10px 0 0;
    border-top-color: $corporate_1;
    border-left-color: transparent;
    border-right-color: transparent;
    background: #DDD;
    color: #333;
    font-weight: 300;
    i.fa {
      font-size: 150%;
      vertical-align: middle;
    }
    &.active {
      background: #f8f8f8;
    }
  }
  .hotels_list_wrapper {
    .center_block {
      .u-row {
        box-shadow: 0 0 20px rgba(0,0,0,.3);
        color: #333;
        .booking-box--default {
          font-family: $font_1, sans-serif;
          .booking-box__title {
            background: transparent;
            padding: 20px 20px 0;
            color: #333;
            h3 {
              font-weight: 700;
              font-size: 22px;
              line-height: 25px;
              color: #333;
            }
          }
          .booking-box--action {
            .booking-box__title {
              font-weight: 700;
            }
            .hotel-promotion {
              font-weight: 300;
            }
            .hotel-price__current {
              color: $corporate_2;
            }
          }
        }
        .booking-box__content {
          .hotel_description_container {
            strong {
              font-weight: 700;
            }
          }
          .flexible_days {
            color: $corporate_2;
          }
        }
        .booking-button--action {
          font-family: $font_1, sans-serif;
          background-color: $corporate_3 !important;
          color: white;
          padding: 10px 30px 10px 10px;
          font-size: 16px;
          position: relative;
          -webkit-transition: all 0.6s;
          -moz-transition: all 0.6s;
          -ms-transition: all 0.6s;
          -o-transition: all 0.6s;
          transition: all 0.6s;
          display: inline-block;
          vertical-align: middle;
          font-weight: bold;
          text-transform: uppercase;
          z-index: 1;
          @extend .icon-longarrow;
          &:before {
            @extend .fa;
            font-family: 'icomoon' !important;
            position: absolute;
            top: 50%;
            right: 5px;
            -webkit-transform: translate(0%,-50%);
            -moz-transform: translate(0%,-50%);
            -ms-transform: translate(0%,-50%);
            -o-transform: translate(0%,-50%);
            transform: translate(0%,-50%);
            color: white;
            font-size: 22px;
            z-index: 1;
          }
          &:after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            left: auto;
            background: $corporate_1;
            width: 0;
            -webkit-transition: width 0.6s;
            -moz-transition: width 0.6s;
            -ms-transition: width 0.6s;
            -o-transition: width 0.6s;
            transition: width 0.6s;
            z-index: -1;
          }
          &:hover {
            color: white;
            &:after {
              left: 0;
              width: 100%;
            }
          }
        }
        .no_availability_hotel_button {
          background-color: $corporate_2;
        }
      }
    }
  }
}
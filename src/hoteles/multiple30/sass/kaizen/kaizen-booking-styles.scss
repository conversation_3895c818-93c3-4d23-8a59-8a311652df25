div.site-header {
  .site-header__ticks {
    margin-right: 140px;
  }
  .language_header_selector_booking_process {
    .language_booking_selector_wrapper {
      top: 80%;
    }
    #currencyDiv {
      margin: 0 !important;
      width: auto;
      position: relative !important;
      top: 17px !important;
      right: 100px !important;
      background: transparent;

          .currency_selector_wrapper_v2 {
            width: 60px;

            .label {
              color: white;
              font-size: 13px;
              font-weight: 300;
            }

            .selector {
              font-size: 13px;
            }

            &.active {
              background: white;

              .label {
                color: #444444;
              }
            }

            &::before, &::after {
              font-family: 'Font Awesome 6 Pro' !important;
              content: "\f107";
              position: absolute;
              right: 20px;
              top: 9px;
              width: 13px;
              height: 13px;
              font-size: 18px;
              font-weight: 300;
              background-image: none;
              color: white;
            }
            &::after {
              transform: rotateX(180deg);
              top: 15px;
              color: #707173;
            }
          }

      #currencySelect {
        background: transparent;
        margin-top: 0;
        font-size: 13px;
        width: fit-content;
        font-family: $font-1;
        text-align: right;
        font-weight: 400;
        color: white;

        option {
          font-size: 16px;
          line-height: 18px;
          opacity: .5;
        }
      }
      &::after {
        content: "";
        position: absolute;
        right: 5px;
        bottom: 0;
        top: 0;
        width: 1px;
        background: white;
      }
    }
  }
}
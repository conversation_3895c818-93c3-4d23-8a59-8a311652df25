.site-header {
  .site-header__logo {
    width: 300px;
    height: 125px;
    position: relative;

    a {
      @include center_y;
      display: block;
      width: 100%;
      height: auto;

      img {
        height: 100%;
        object-fit: contain;
        max-height: 80px;
      }
    }
  }

  .header_right_block {
    .site-header__ticks {
      padding: 20px 100px 20px 0;

      .site-header__tick-item {
        display: inline-flex;
        align-items: center;
        width: 100px;

        .icon-checkmark {
          width: 25px;

          &::before {
            font-family: "Font Awesome 6 Pro" !important;
            font-weight: 300;
            font-size: 22px !important;
            line-height: 52px;
            color: $corporate_1 !important;
          }
        }

        p {
          flex: 1;
          font-family: $text_family;
          font-weight: 400;
          letter-spacing: 0.9px;
          top: 0 !important;
          color: $black;
        }
      }
      .site-header__tick-item:nth-of-type(1) {
        .icon-checkmark {
          &::before {
            content: "\f559" !important;
          }
        }
      }
      .site-header__tick-item:nth-of-type(2) {
        .icon-checkmark {
          &::before {
            content: "\f2f7" !important;
          }
        }
      }
      .site-header__tick-item:nth-of-type(3) {
        .icon-checkmark {
          &::before {
            content: "\f06b" !important;
          }
        }
      }
      .site-header__tick-item:nth-of-type(4) {
        .icon-checkmark {
          &::before {
            content: "\f0ac" !important;
          }
        }
      }
      .site-header__tick-item:last-of-type:not(.tick_customized) {
        display: none;
      }
    }

    .language_header_selector_booking_process {
      padding: 5px 20px 5px 0;
      @include center_y;
      bottom: auto;

      &::before {
        content: "\f107";
        font-weight: 300;
        font-size: 21px;
        color: $corporate_4;
      }

      .selected_language {
        position: static;
        @include transform(none);
        padding: 0;
        color: $grey;

        i {
          font-family: "Font Awesome 6 Pro";
          font-weight: 300;
          font-size: 18px;
          margin-right: 5px;
          vertical-align: middle;
        }

        .selected_language_code {
          font-family: $text_family;
          font-weight: 400;
          font-size: 14px;
          letter-spacing: 1.4px;
          text-transform: uppercase;
        }
      }
    }
  }
}
$color1: #E6BB5D;
$color2: #29655D;

@import "styles_mobile_base_2";
@import "styles_mobile_base_2_personalized";

nav#menu {
  background: $color1;
}

input#destino[type=submit], div.button, a[data-role=button] {
  background: $color2!important;
}

.gallery_filter_wrapper {
  .gallery_title {
    font-weight: bold;
    font-size: 38px;
    color: #4b4b4b;
    font-family: 'georgia', serif;
    text-align: center;
    text-transform: uppercase;
    margin: 50px 0 10px 0;
  }
  .gallery_photos {
    display: table;
    img {
      width: calc(100%/3);
      float: left;
    }
  }
}

.offers_wrapper {
  .promotions-item {
    .offer_link a {
      display: block;
      background-color: $color1;
      color:#4b4b4b;
      padding: 20px;
      font-size: 2em;
      margin-bottom: 10px;
      text-align: center;
      text-transform: uppercase;
    }
  }
}

.room_blocks_wrapper {
  padding: 30px 0 60px;
  background-size:cover;
  background-attachment: fixed;
  h3 {
    text-align: center;
    color:#4b4b4b;
    margin-bottom: 40px;
    font-family: 'georgia', serif;
    font-size: 2em;
    font-weight: bold;
  }
  .room_blocks {
    position: relative;
    margin: auto;
    .room {
      position:relative;
      height:500px;
      .room_image {
        position: absolute;
        top:0;
        left:40px;
        width: 100%;
        height: 100%;
        overflow:hidden;
        img {
          @include center_xy;
          width: auto;
          min-height: 100%;
          min-width: 100%;
          max-width: none;
        }
      }
      .room_desc {
        background-color: white;
        box-sizing: border-box;
        padding: 20px;
        position: absolute;
        bottom:0;
        left: 0;
        right: 40px;
        .room_title {
          text-align: left;
          color:#4b4b4b;
          font-family: 'georgia', serif;
          font-size: 3em;
          line-height: 1.5em;
          font-weight: bold;
          text-transform: uppercase;
          margin-bottom: 40px;
        }
        .room_text {
          text-align: left;
          font-size: 2em;
          line-height: 1.5em;
          color:#909090;
        }
        a {
          display: inline-block;
          background-color: $color2;
          color: white;
          position: absolute;
          padding:10px 25px;
          right:20px;
          font-size: 2em;
          bottom: 20px;
        }
      }
    }
    .owl-prev, .owl-next {
      position: absolute;
      top:50%;
      left:0;
      background-color:$color1;
      color:white;
      width: 60px;
      padding: 15px;
      box-sizing: border-box;
      font-size: 3em;
      text-align: center;
    }
    .owl-next {
      right:0;
      left:auto;
    }
  }
}
/*=== Minigallery ===*/
.minigallery_wrapper {
  display: inline-block;
  width: 100%;

  .gallery_element {
    position: relative;
    height: 32em;
    overflow: hidden;

    img {
      position: absolute;
      top: 50%;
      left: 50%;
      -webkit-transform: translate(-50%, -50%);
      -moz-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
      -o-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      min-width: 100%;
      min-height: 100%;
      max-width: none;
    }
  }
}

.contact_content_element div {
  float: none !important;
  width: 100% !important;
}

/*=== Rooms ===*/
.room_element {
  .rooms_slider {
    .slides {
      li {
        height: 25em;

        img {
          position: absolute;
          top: 50%;
          left: 50%;
          -webkit-transform: translate(-50%, -50%);
          -moz-transform: translate(-50%, -50%);
          -ms-transform: translate(-50%, -50%);
          -o-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
          min-width: 100%;
          min-height: 100%;
        }
      }
    }
  }

  .room_link {
    display: inline-block;
    width: 100%;
    text-align: center;
    margin-bottom: 2em;

    a {
      display: inline-block;
      width: 100%;
      color: white;
      background: $color1;
      text-transform: uppercase;
      font-size: 1.5em;
      padding: 1em;
      box-sizing: border-box;
      outline: .5em solid rgba(white, .5);
      outline-offset: -7px;
      font-family: "Montserrat";
    }
  }
}

.popup_inicio {
  img {
    width: 100%;
  }
}
div#step-1 {
  .contTipoHabitacion {
    box-shadow: 0 0 20px rgba(0, 0, 0, .15);

    .overlay_picture_text {
      position: absolute;
      z-index: 2;
      text-align: center;
      top: 30px;
      left: 52px;
      font-size: 9px;
      font-weight: bold;
      width: 99px;

      strong {
        display: block;
        font-size: 27px;
        margin-bottom: 15px;
      }

      .s_tag {
        color: #E75354;
      }

      .m_tag {
        color: green;
      }

      .l_tag {
        color: #7CCFF4;
      }

      .xl_tag {
        color: black;
      }
    }
    .contDescHabitacion .room_services i {
      font-size: 25px;
    }
    .contFotoHabitacion {
      .occupancy {
        display: table;
        width: 100%;
        background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.4));
        position: absolute;
        bottom: 0;
        right: 0;
        z-index: 100;
        padding: 5px;
        text-align: right;
        font-family: $text_family;
        font-weight: 700;
        font-size: 12px;
        line-height: 12px;
        color: white;
        i {
          display: inline-block;
          vertical-align: bottom;
          padding: 0;
          color: white;
          font-size: 12px;
          line-height: 12px;
          &.adult {
            font-size: 22px;
            line-height: 22px;
            padding-right: 5px;
          }
          &.kid {
            font-size: 18px;
            line-height: 18px;
            padding-right: 5px;
          }
          &.baby {
            font-size: 14px;
            line-height: 14px;
            margin-bottom: 0;
            padding-left: 2px;
          }
        }
        b {
          position: absolute;
          bottom: 15px;
          right: 0;
          opacity: 0;
          display: inline-block;
          padding: 10px;
          font-weight: 400;
          text-transform: lowercase;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          border-radius: 5px;
          @include transition(all, .6s);
          &:after {
            position: absolute;
            top: 100%;
            right: 30px;
            content: '';
            border: 5px solid transparent;
            border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
          }
        }
        &:hover {
          b {
            bottom: 30px;
            opacity: 1;
          }
        }
      }
    }

    .tipoHabitacion {
      color: $black !important;
      font-size: 30px;
      font-family: $title_family;
    }

    .descripcionHabitacion {
      color: $black !important;
    }

    .see_more_rooms_v2 {
      .plus_sign {
        color: #333 !important;
      }
      .see_more {
        text-decoration: none !important;
        text-transform: uppercase !important;
        color: #333 !important;
      }
      .lupa {
        left: 10px;
        font-size: 30px;
      }
    }

    .very_asked_message {
      background-color: #c23329 !important;
      text-transform: uppercase;
      &:after {
        border-left: 15px solid #c23329 !important;
      }
    }

    .room_services {
      border-top-width: 2px;
      border-top-style: dashed;
      border-bottom-width: 2px;
      border-bottom-style: dashed;
      border-color: $grey;
      .service_element {
        border-right-width: 2px;
        border-right-style: dashed;
        border-color: $grey;
        .service_description {
          color: $black;
          font-weight: 700;
        }
      }
    }

    .booking-button {
      font-family: $text_family;
      background-color: $corporate_1;
      color: white;
      padding: 10px 40px;
      font-size: 16px;
      font-weight: 300;
      position: relative;
      -webkit-transition: all 0.6s;
      -moz-transition: all 0.6s;
      -ms-transition: all 0.6s;
      -o-transition: all 0.6s;
      transition: all 0.6s;
      display: inline-block;
      vertical-align: middle;
      text-transform: uppercase;
      letter-spacing: 1px;
      z-index: 1;
      &:hover {
        background-color: $corporate_2;
      }
    }
    .last_day_cancellation_text {
      background: transparent;
      position: relative;
      @extend .fa-check;
      &:before {
        @extend .fa;
        position: absolute;
        top: 50%;
        left: 3px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        color: $red;
        border: 1px solid $red;
        padding: 0 3px;
        font-size: 8px;
        line-height: 14px;
        border-radius: 50%;
      }
    }
    .cheapest_rate_message {
      .rate_conditions_link {
        color: $corporate_1;

        &:before {
          border-color: $corporate_1;
          color: $corporate_1;
        }

        &:hover {
          color: lighten($corporate_1, 10%);
        }
      }
    }

    .rate_conditions_link {
      background: transparent;
      position: relative;
      @extend .fa-info;
      &:before {
        @extend .fa;
        width: 3px;
        height: 14px;
        padding-left: 6px;
        position: absolute;
        top: 50%;
        right: 3px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        border: 1px solid $corporate_2;
        background: transparent;
        color: $corporate_2;
        padding: 0 5px;
        font-size: 10px;
        line-height: 14px;
        border-radius: 50%;
      }
      &:hover {
        color: $corporate_3;
      }
    }
    .contTitTipoTarifa {
      background-color: $grey;
      color: $black;
      font-family: $text_family;
      .cheapest_rate_message {
        background-color: $corporate_2;
        color: white;

        &:before {
          border-left: 19px solid #f4f4f4
        }

        .before_block {
          border-left-color: $corporate_1;
        }
      }
      .advice_rate_message {
        background-color: $corporate_1;
        color: white;
        &:before {
          border-left-color: $grey;
        }
      }
      > .conditions_info_wrapper {
        a {
          color: $corporate_3;
          font-size: 9px;
          text-transform: uppercase;
          text-align: left;
          box-sizing: border-box;
        }
      }
    }
  }

  .package_element_wrapper {
    .package_prices_wrapper {
      .perform_package_booking_search {
        background: $corporate_1 !important;
        font-weight: lighter;
        &:hover {
          background: $corporate_2 !important;
        }
      }
    }

    .package_image_selector_wrapper .package_room_pictures_selector .picture_selector {
      background: $corporate_2;
    }
  }
}

div#step-1 .conditions_info_wrapper .rate_conditions_link {
  color: $corporate_3;
  font-size: 9px;
  text-transform: uppercase;
  text-align: left;
  box-sizing: border-box;
  letter-spacing: 1px;
  font-weight: bolder;
  padding: 0 30px 0 0;
}

.popup_see_more_rooms_second {
  .fancybox-inner {
    background-color: white;
  }
  .room_popup_individual_element {
    .popup_title {
      background: white;
      font-family: $title_family;
      font-size: 25px;
      font-weight: 700;
      color: #333;
      top: auto;
      bottom: calc(100% - 400px);
      padding-right: 35%;
      padding-bottom: 10px;
      border-top: 10px solid white;
    }
    .close_button_element {
      font-family: $text_family;
      font-size: 18px;
      font-weight: 700;
      color: white;
      background-color: $corporate_1;
      width: 50px;
      line-height: 50px;
      top: 0;
      right: 0;
    }
    .popup_carousel {
      .element_carousel_pictures {
        .exceded {
          height: 400px;
          img {
            width: 100%;
          }
          a {
            display: block !important;
            position: absolute;
            top: 270px;
            right: 0;
            background: radial-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
            padding: 10px;
            color: white;
            i.fa {
              font-size: 25px;
            }
            &:hover {
              color: $corporate_1;
            }
          }
        }
      }
      .flex-direction-nav {
        .flex-nav-prev, .flex-nav-next {
          width: 50px;
          height: 50px;
          @include transition(width, .6s);
          &:hover {
            width: 60px;
          }
          a {
            background-color: $corporate_2;
            @extend .icon-longarrow;
            &:before {
              font-family: "icomoon", sans-serif;
              font-size: 30px;
              color: white;
              position: absolute;
              top: 50%;
              left: 50%;
              -webkit-transform: translate(-50%, -50%);
              -moz-transform: translate(-50%, -50%);
              -ms-transform: translate(-50%, -50%);
              -o-transform: translate(-50%, -50%);
              transform: translate(-50%, -50%);
              @include transition(margin, .6s);
            }
            img {
              display: none;
            }
          }
        }
        .flex-nav-prev {
          a:before {
            -webkit-transform: translate(-50%, -50%) rotate(-180deg);
            -moz-transform: translate(-50%, -50%) rotate(-180deg);
            -ms-transform: translate(-50%, -50%) rotate(-180deg);
            -o-transform: translate(-50%, -50%) rotate(-180deg);
            transform: translate(-50%, -50%) rotate(-180deg);
          }
        }
      }
    }

    .popup_room_pictures {
      display: none !important;
    }
    .room_services {
      margin-left: 0 !important;
      width: 100% !important;
      padding: 0 !important;
      border-top-style: dashed;
      border-bottom-style: dashed;
      border-top-color: $grey;
      border-bottom-color: $grey;
      border-top-width: 2px;
      border-bottom-width: 2px;
      background: transparent;

      .service_element {
        border-right-style: dashed;
        border-right-color: $grey;
        border-right-width: 2px;
        height: auto !important;
        i {
          font-size: 25px;
          display: inline-block;
          padding-bottom: 0;
          margin-right: 10px;
        }
        .service_description {
          font-size: 12px;
          font-weight: 700;
        }
      }
    }
    .popup_room_description {
      margin-top: 10px;
      margin-bottom: 10px;
      padding-top: 20px;
      padding-bottom: 20px;
      margin-left: 0 !important;
      width: 100% !important;
      font-family: $text_family;
      background: white;
      .desc, .list {
        display: inline-block;
        vertical-align: top;
        width: calc(100% / 3);
        box-sizing: border-box;
      }
      .desc {
        width: calc(100% / 3 * 2);
        padding-right: 20px;
        strong {
          font-weight: 700;
        }
      }
      .list {
        padding: 0 0 0 25px;
        li {
          font-weight: 700;
          padding: 5px 0;
          @extend .icon-longarrow;
          &:before {
            font-family: "icomoon", sans-serif;
            margin-right: 10px;
            color: $corporate_2;
          }
        }
      }
    }
  }
}
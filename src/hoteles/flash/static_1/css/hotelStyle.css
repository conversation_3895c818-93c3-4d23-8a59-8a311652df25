#stickyMain #header{
    background: rgb(255,255,255);
}
#stickyWrap{
    font-family: 'Open Sans', arial;
    background: rgb(255,255,255) !important;
}
.bestText{
    color: rgb(255,28,156);
}
#logo img {
    max-height: 120px;
}
.stepContainer button{
    border: none;
    border-radius: 5px;
    font-weight: normal;
	padding: 6px 10px;
	background-color: rgb(255,28,156);
	color: rgb(255,255,255);
	font-size: 18px;
	cursor: pointer;
	overflow: visible;
}
.stepContainer button:hover{
    background-color: rgb(120,120,120);
}
.swMain ul.anchor li a.selected{
    background: rgb(255,28,156);
    border: 1px solid rgb(255,28,156);
}
.swMain ul.anchor li a.selected:hover{
    background: rgb(255,28,156);
    border: 1px solid rgb(255,28,156);
}
#bg_top{
    background: rgb(230,230,230);
}


/********************* Motor de reserva *********************/

#stickyMain #motor #closeButton{
    cursor: pointer;
}
#stickyMain #motorBusqueda{
    background: rgb(255,255,255);
    color: rgb(120,120,120);
}
#stickyMain #motorBusqueda .bgFecha{
    height: 18px !important;
	width: 88px !important;
	border: 1px solid #464646 !important;
    border-radius: 4px !important;
	cursor: pointer;
	background: rgb(255,255,255) url(/img/flasl/date_icon.jpg) no-repeat 72px !important;
	padding: 0 !important;
}
.titSeccionesMotor {
    color: rgb(120,120,120);
}
#stickyMain #motorBusqueda #configuraHabitaciones select{
    border: 1px solid #464646 !important;
    border-radius: 4px !important;
}

#stickyMain #motorBusqueda input{
    width: 90px !important;
    border: 1px solid #464646 !important;
    border-radius: 4px !important;
}
#stickyMain #motorBusqueda button{
    border: none;
    border-radius: 5px;
    font-weight: normal;
	padding: 6px 10px;
	background-color: rgb(255,28,156);
	color: rgb(255,255,255);
	font-size: 18px;
	cursor: pointer;
	overflow: visible;
}
#stickyMain #motorBusqueda button:hover{
    background-color: rgb(120,120,120);
}
#stickyMain #motorBusqueda .contButton{
    margin: 0 15px 0 0 !important;
    width: auto !important;
}
#stickyMain #motorBusqueda span{
    float: none !important;
}
#stickyMain #motorBusqueda #divPromocode{
    float: none !important;
}
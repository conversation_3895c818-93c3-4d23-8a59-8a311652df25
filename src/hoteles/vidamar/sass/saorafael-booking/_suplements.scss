div#step-2 {
  .supplements_list_wrapper {
    .booking-2-service-container {
      .booking-2-container-description {
        .booking-2-service-title {
          font-size: 20px;
          font-weight: bold;
          color: #161C21;
          margin-bottom: 16px;
          line-height: 23px;
          margin-left: 20px;
        }

        .booking-2-service-description {
          max-height: 47px;
          overflow: hidden;
          padding-right: 30px;
          text-overflow: ellipsis;
        }
      }

      .booking-2-service-price {
        span {
          color: $corporate_1;
        }
      }

      .booking-2-services-list {
        .services_buttons_wrapper {
          .add_service_element {
            background-color: $corporate_1;
            font-family: $title_family;
            font-weight: 400;
            font-size: 18px;
          }
        }
      }
    }
  }

  .booking_button_wrapper {
    .booking-button {
      position: relative;
      width: 275px;
      height: 35px;
      padding: 0 10px;
      margin: 0;
      border-radius: 2px;
      background-color: $corporate_1;
      font-family: $title_family;
      font-weight: 400;
      font-size: 16px;
      text-align: center;
      line-height: 21px;
      text-transform: none;
      white-space: nowrap;
      color: white;
      @include transition(all, 0.6s);
    }
  }

  .all_additional_services_wrapper {
    .additional_service_element {
      .service_selection_wrapper {
        margin: 0 auto;

        .price_service .price_wrapper {
          margin-left: 0 !important;
        }

        .service_select {
          float: initial !important;
          width: 100%;
          justify-content: center;
        }

        .add_service_button {
          max-width: 60px;
        }
      }
        .service_content {
          .service_title{
            overflow: visible;
            display: block;
          }
        }
    }

    &.with_tabs_scroll {
      margin: 110px auto 25px;
      overflow: visible;
      border: 0.75px solid #CDCFD0;
      position: relative;
      padding: 0 40px 29px;
      border-radius: 15px;

      .additional_services_tabs_wrapper {
        position: relative;
        margin-bottom: 0;
        top: -80px;
        left: -40px;

        .additional_services_tabs_list {
          border-bottom: 0;

          .additional_services_tab {
            background: transparent;
            border: 2px solid #000000;
            border-radius: 5px;
            margin-right: 20px;

            .main_title {
              font-size: 20px;
              line-height: 27px;
              color: #000000;
              font-weight: 600;

              small {
                display: none;
              }
            }
          }
        }

        &.fixed {
          position: fixed;
          top: 0;
          left: 0;
        }
      }

      .category_wrapper {
        .title_category {
          margin-top: -20px;
        }

        .additional_services_wrapper {
          .additional_service_element {
            background: transparent;
            border: 1px solid #707070;
            border-radius: 10px;

            .service_content {
              .price_per_night_person {
                color: #000000;

                .label_price {
                  display: none;
                }
              }
            }

            .service_selection_wrapper {
              width: 26%;
              display: flex;
              justify-content: space-around;

              .service_select {
                flex-direction: column-reverse;
              }
            }
          }
        }
      }
    }
  }
}

.full_service_popup_info {
  padding: 20px;
}
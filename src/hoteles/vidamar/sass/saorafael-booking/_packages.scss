div#step-1 {
  #packages_b1_wrapper {
    .package_element_wrapper {
      grid-template-columns: 25% 40% 35%;
      padding: 20px 20px 0 20px;
      min-height: 400px;
      position: relative;

      &.custom_package {
        .package_room_pictures_selector {
          &:before, .rates_selector_wrapper:before, .regimens_selector_wrapper:before {
            top: 20px;
            left: 20px;
            width: 77px;
            height: 49px;
          }

          .rates_selector_wrapper:after, .regimens_selector_wrapper:after {
            top: 35px;
          }

          .rates_selector_wrapper:before, .regimens_selector_wrapper:before {
            left: 0;
          }

          .regimens_selector_wrapper select, .rates_selector_wrapper select, .package_select {
            border-left-width: 77px;
          }
        }

        .package_prices_wrapper {
          &:not(.package_booking) {
            padding-top: 220px;
            margin-bottom: 0;

            .conditions_package_info_wrapper {
              text-align: right;
            }
          }

          &.package_booking {
            flex-direction: row;
            padding-bottom: 20px;

            .since_price_label {
              display: inline-block;
              margin-bottom: 10px;
              font-size: 17px;
              margin-right: 25px;
              text-transform: uppercase;
            }
            .since_prices_values {
              font-size: 38px;
              margin-right: 20px;
              font-weight: bold;
              line-height: 50px;
            }
          }
        }
      }

      .package_image_selector_wrapper {
        .image_viewer {
          margin-bottom: 0;
          padding-bottom: 30px;

          img {
            position: static;
            margin: 0;
            @include cover_image;
          }
        }
      }

      .package_description_wrapper {
        width: auto;
        padding: 20px;
        border-right: 1px solid rgba(black, 0.1);

        .package_title {
          font-family: $title_family;
          font-weight: 700;
          font-size: 23px;
          line-height: 1;
          text-transform: capitalize;
          color: $grey-2;
        }

        .package_description {
          font-family: $text_family;
          font-weight: 400;
          font-size: 14px;
          line-height: 21px;
          color: $grey-2;
        }
        .see_more_packages_v2 {
          .see_more {
            font-family: $text_family;
            font-weight: 500;
            font-size: 13px;
            line-height: 1;
            color: $black;
            text-transform: uppercase;
            text-decoration: none;
          }
          .plus_sign {
            background: none;
            border: none;
            color: $black;
          }
        }
      }

      .package_room_pictures_selector {
        padding-left: 20px;
        height: auto;

        &,
        .rates_selector_wrapper,
        .regimens_selector_wrapper {
          &::before {
            display: block;
            width: 20%;
            line-height: 50px;
            float: left;
            border-top-left-radius: 10px;
            border-bottom-left-radius: 10px;
            background-color: $black;
            font-family: "Font Awesome 6 Pro";
            font-weight: 300;
            font-size: 30px;
            color: white;
            text-align: center;
            -webkit-font-smoothing: antialiased;
          }
        }

        .package_select,
        .package_rate_select,
        .package_regimen_select {
          width: 80%;
          height: 50px;
          float: left;
          margin: 0;
          border-top-right-radius: 10px;
          border-bottom-right-radius: 10px;
          background-color: white;

          font-family: $text_family;
          font-weight: 400;
          font-size: 17px;
          color: $black;
        }

        .rates_selector_wrapper,
        .regimens_selector_wrapper {
          padding-top: 20px;
        }

        &::before {
          content: "\f236";
        }

        .rates_selector_wrapper::before {
          content: "\f02b";
        }

        .regimens_selector_wrapper::before {
          content: "\f7b6";
        }

        .picture_selector {
          display: none;
        }
      }

      .package_prices_wrapper {
        @include display_flex(nowrap);
        flex-direction: column;
        width: 100%;
        height: auto;
        padding-left: 20px;

        .since_price_label,
        .promotion_default_price {
          display: none;
        }

        .conditions_package_info_wrapper {
          order: 1;
          border: none;
          text-align: center;

          &::before {
            content: "\f05a";
            font-family: "Font Awesome 6 Pro";
            font-weight: 300;
            font-size: 16px;
            -webkit-font-smoothing: antialiased;
          }

          .rate_conditions_link {
            background: none;
            padding: 0;
            font-family: $text_family;
            font-weight: 400;
            font-size: 16px;
            color: $grey-2;
          }
        }

        .price_per_room_label {
          order: 2;
          font-family: $text_family;
          font-style: normal;
          font-weight: 400;
          font-size: 17px;
          line-height: 20px;
          color: $grey-2;
          text-align: right;
        }

        .since_prices_values {
          order: 3;
          font-family: $text_family;
          font-weight: 400;
          font-size: 19px;
          line-height: 20px;
          color: $grey-2;
          text-align: right;
        }

        .perform_package_booking_search {
          order: 4;
          background-color: $corporate_1;
          border-radius: 0;
          outline: none;
          font-family: $title_family;
          font-weight: 700;
          font-size: 18px;
          transition: all .6s;

          &:hover {
            background-color: lighten($corporate_1, 10%);
          }
        }
      }
    }
  }
}
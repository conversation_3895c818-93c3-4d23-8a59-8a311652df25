@import url('https://fonts.googleapis.com/css2?family=Kumbh+Sans:wght@100;200;300;400;500;600;700;800;900&display=swap');

//Base web (change too in templateHandler and in config.rb)
$base_web: "ecoco";
// colors definitions
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);

// corporative colors definitions
$corporate_1: #53A1BD;
$corporate_2: #4B4B4B;
$corporate_3: #514864;
$corporate_4: #CD8415;

$title_family: "Kumbh Sans", sans-serif;
$text_family: "Kumbh Sans", sans-serif;
$title_size: 1.7rem;
$description_size: 1rem;
$line_height: 1.6rem;



// colors for booking widget
$booking_widget_color_1: $white;//body back ground & year input text color
$booking_widget_color_2: $corporate_1;//header background & input texts
$booking_widget_color_3: gray;//label texts
$booking_widget_color_4: gray;//not used, but must be defined

$text_color_1: #4D4D4D;
$text_color_2: #727272;
$grey: #BCBCBC;
$lightgrey: #F6F6F6;
$separator_color: rgba(#8D8D8D, 0.5);

$height: 95px;
$vertical_padding: 25px;
$horizontal_padding: 20px !default;
$width: 200px;
$box_shadow: 0px 3px 6px rgba(black, 0.2);

@mixin box_shadow() {
  -webkit-box-shadow: $box_shadow;
  -moz-box-shadow: $box_shadow;
  box-shadow: $box_shadow;
}

@mixin input_base_styles() {
  position: relative;
  background-color: transparent;
  height: $height;
  padding: $vertical_padding $horizontal_padding;
  margin: 0;
}

@mixin label_styles() {
  display: block;
  font-family: $title_family;
  font-weight: 700;
  font-size: 13px;
  letter-spacing: 0.25px;
  line-height: 1;
  color: white;
  text-align: left;
  text-transform: uppercase;
}


@mixin option_styles($fz: 16px) {
  margin: 0;
  font-family: $text_family;
  font-weight: 400;
  font-size: $fz;
  letter-spacing: 0.25px;
  line-height: 22px;
  color: white !important;
  cursor: pointer;
}
@mixin separator() {
  position: relative;
  margin: 0;

  &:after {
    content: '';
    position: absolute;
    top: 15px;
    bottom: 15px;
    left: -1px;
    width: 1px;
    background: $separator_color;
  }
}

@mixin promocode_placeholder_styles($size, $color: $corporate_1) {
  color: $color;
  font-size: $size;
  font-weight: 700;
  text-transform: uppercase;
  font-family: $title_family;
  letter-spacing: 1px;
  text-align: center;
  margin: 0;
  border: 0;
  padding: 0;
  box-shadow: none;
}

@mixin promocode_styles($size, $color) {
  @include promocode_placeholder_styles($size, $color);

  &::-webkit-input-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
  &::-moz-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
  &:-ms-input-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
  &:-moz-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
}

@import "plugins/lightbox";
@import "plugins/fancybox_2_1_5";
@import "plugins/_jquery_ui_1_8_16_custom";
@import "plugins/owlcarousel";
@import "plugins/mixins";
@import "plugins/fontawesomemin";
/*@import "plugins/iconmoon";*/
@import "plugins/effects";

@import "booking/selectric";
@import "../booking_widget";
@import "../responsive";
@import "../fs_datepicker";
@import "../datepicker";
@import "plugins/fontawesome5injectionfix";


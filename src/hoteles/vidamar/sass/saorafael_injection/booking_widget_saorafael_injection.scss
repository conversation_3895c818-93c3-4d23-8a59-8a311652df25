@import "plugins/iconmoon";
@import "plugins/fontawesome5injectionfix";
@import "plugins/fontawesome5pro_inj";
@import "plugins/mixins";
@import "plugins/fancybox_2_1_5";
@import "plugins/spiners/all_spiners";

@font-face {
  font-family: 'Cera Pro';
  src: url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Regular.eot');
  src: local('Cera Pro Regular'), local('CeraPro-Regular'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Regular.eot?#iefix') format('embedded-opentype'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Regular.woff') format('woff'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
}

@font-face {
  font-family: 'Cera Pro';
  src: url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Light.eot');
  src: local('Cera Pro Light'), local('CeraPro-Light'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Light.eot?#iefix') format('embedded-opentype'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Light.woff') format('woff'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Light.ttf') format('truetype');
  font-weight: 300;
  font-style: normal;
}

@font-face {
  font-family: 'Cera Pro';
  src: url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Black.eot');
  src: local('Cera Pro Black'), local('CeraPro-Black'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Black.eot?#iefix') format('embedded-opentype'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Black.woff') format('woff'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Black.ttf') format('truetype');
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Cera Pro';
  src: url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Medium.eot');
  src: local('Cera Pro Medium'), local('CeraPro-Medium'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Medium.eot?#iefix') format('embedded-opentype'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Medium.woff') format('woff'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Medium.ttf') format('truetype');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Cera Pro';
  src: url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Bold.eot');
  src: local('Cera Pro Bold'), local('CeraPro-Bold'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Bold.eot?#iefix') format('embedded-opentype'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Bold.woff') format('woff'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Bold.ttf') format('truetype');
  font-weight: bold;
  font-style: normal;
}

@font-face {
  font-family: 'Cera Pro';
  src: url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Thin.eot');
  src: local('Cera Pro Thin'), local('CeraPro-Thin'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Thin.eot?#iefix') format('embedded-opentype'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Thin.woff') format('woff'),
  url('https://www3.paratytech.com/wysiwyg/fonts/cera-pro/CeraPro-Thin.ttf') format('truetype');
  font-weight: 100;
  font-style: normal;
}

$corporate_1: #92b5cb;
$corporate_2: lighten($corporate_1, 8%);
$widget_bg: white;
$grey: #878787;
$label_color: #000000;
$options_color: #000000;
$black: #333333;

$title_family: "Cera Pro", sans-serif;
$text_family: $title_family;
$labels_family: $title_family;

$height: 100px;
$vertical_padding: 20px;
$horizontal_padding: 40px !default;
$width: 200px;
$box_shadow: 1px 1px 15px 3px rgba(0, 0, 0, 0.3);

@mixin input_base_styles() {
  position: relative;
  text-align: center;
  height: $height;
  padding: $vertical_padding $horizontal_padding;
  margin: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;

  &::after {
    position: absolute;
    content: '';
    top: 20px;
    bottom: 20px;
    right: 0;
    width: 1px;
    background-color: $corporate_1;
  }
}

@mixin label_styles() {
  display: block;
  text-align: left;
  font-size: 16px;
  color: $label_color !important;
  font-weight: 300;
  text-transform: uppercase;
  font-family: $labels_family;
  margin: 0 0 7px;
}


@mixin option_styles() {
  display: inline-block;
  font-size: 16px;
  line-height: 17px;
  color: $options_color;
  font-weight: 500;
  font-family: $text_family;
  cursor: pointer;
  margin: 0;
}

@mixin option_styles_big() {
  display: inline-block;
  font-size: 20px;
  line-height: 22px;
  color: $options_color !important;
  font-weight: 700 !important;
  font-family: $text_family;
  cursor: pointer;
  margin: 0;
}

@mixin promocode_placeholder_styles($size, $color) {
  color: $options_color;
  font-size: $size;
  font-weight: 700;
  text-transform: uppercase;
  font-family: $title_family;
  letter-spacing: 1px;
  text-align: center;
  margin: 0;
  border: 0;
  padding: 0;
  box-shadow: none;
}

@mixin promocode_styles($size, $color) {
  @include promocode_placeholder_styles($size, $color);

  &::-webkit-input-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
  &::-moz-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
  &:-ms-input-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
  &:-moz-placeholder {
    @include promocode_placeholder_styles($size, $color);
  }
}

@import "booking_widget";
@import "web";
@import "responsive";
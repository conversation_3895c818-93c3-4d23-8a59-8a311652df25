@import "calendar_disco_styles";

@include booking_header(white, #989898, #989898, $corporate_1, white);

@import "site-header_styles";
@import "booking_widget_styles";
@import "booking0";


div.site-header {
  .site-header__logo {
    img {
      margin: 10px;
    }
  }
}
#step-3 .booking_details_prices_wrapper .booking_info_element.pet_element {
  color: $corporate_1;
}
.clearfix {
  padding-bottom: 20px;
}

.no-availability-section {
  min-height: 110px;
}

div#step-1 .regimen-description-v2 {
  top: -13px;
}

.custom_pictures_range {
  margin: 0 auto 30px;
}

.rooms_packages_selectors_wrapper {
  position: relative;
  border: none;
  .button_package_room {
    position: relative;
    padding: 15px 0 15px 230px;
    margin: 0;
    height: auto;
    font-size: 18px;
    width: calc(50% - 10px);
    letter-spacing: 2px;
    opacity: 1;
    font-weight: 400;
    background: $grey;
    border-top: 4px solid transparent;
    color: grey;
    &:first-of-type {
      &:before {
        content: '\e9f8';
        font-family: "icomoon" !important;
        @include center_y;
        left: 160px;
        font-size: 22px;
        color: grey;
      }
    }
    &:last-of-type {
      float: right;
      margin-left: 10px;
      &:before {
        content: '\ea1f';
        font-family: "icomoon" !important;
        @include center_y;
        left: 120px;
        font-size: 22px;
        color: grey;

      }
    }
    &.active {
      height: auto;
      background: $grey;
      color: black;
      border-top-color: $corporate_1;
      &:before {
        color: $corporate_1;
      }
    }
  }
}

.actual_wizard_step .wizard-tab--small, .actual_wizard_step .wizard-tab--big {
  font-family: $text_family;
  a {
    background-color: $corporate_2;
    letter-spacing: 1px;
    &:before, &:after {
      border-left-color: $corporate_2;
    }

    &.disable {
      background-color: $grey;
      color: grey;
      font-weight: 400;
      &:before, &:after {
        border-left-color: $grey;
      }
    }
  }
}

.booking_engine_wrapper_process #booking.boking_widget_inline {
  .entry_date_wrapper .entry_date, .stay_selection .departure_date_wrapper {
    font-weight: 700;
  }
  .guest_selector .placeholder_text {
    font-weight: 500;
    font-size: 22px;
    span {
      font-weight: 500;
    }
  }
}
.selection_price_wrapper {
  .total_price_label {
    margin-bottom: 6px !important;
    font-family: $text_family;
  }
  .total_price_value {
      &.currencyValue {
          font-family: $text_family;
          font-size: 30px;
          font-weight: normal;
      }
  }
  .monedaConv {
      font-family: $text_family;;
      font-size: 21px;
      font-weight: normal;
  }
}

@import "resume_styles";

.no_availability_message_booking0 {
  font-family: $text_family;
  background: $corporate_2;
  border-radius: 10px;
  box-shadow: 0 0 20px rgba(0,0,0,0.3);
}

@import "rooms_styles";
@import "additional_services_styles";

div#step-1 {
  background-color: white;
  font-family: $text_family;
}
div#step-1 .precioNocheColumn .precioTachadoDiv {
  color: red;
}
div#step-1 {
  .booking-search-results.booking-box__content{
    padding: 0 0 10px 20px !important;
  }
  .booking-box--search .booking-box__content {
    .title_booking_breakdown {
      font-weight: 400;
    }
    .booking-search-results__search-data {
      .booking-title-info.booking-hotel-name {
        color: $black;
        display: inline-block;
        padding: 0;
        margin-bottom: 10px;
      }
      .booking-3-info {
        font-weight: bold;
        font-size: 16px;
      }
      .booking-title-info, .notranslate {
        color: #707173;
        font-weight: 400;
      }
    }
    .booking-search-results__rooms-list {
      padding-top: 20px;
      font-weight: 400;
      color: #707173;
      padding-left: 35px;

      .booking-title-info {
        color: #707173;
      }
    }
  }
  table.listadoHabsTarifas {
    td.precioTotalColumn,
    td.precioNocheColumn,
    td.regimenColumn {
      .tTextoOferta,
      .tPrecioOferta,
      .tPrecioOferta {
        color: $corporate_2;
      }
      .tTextoOferta {
        color: $red;
        font-size: 13px;
        font-weight: normal;
      }
    }
    td.precioTotalColumn,
    td.precioNocheColumn {
      color: black;
    }
  }
  .contTipoHabitacion {
    .booking-button {
      color: white;
      &:hover {
        color: white;
      }
    }
  }
  .precioTotalColumn {
    .tPrecioTotal {
      font-weight: 300;
    }
    .priceTitle {
      font-weight: 300;
    }
  }
}

div#step-1 {
  .contTipoHabitacion {
    margin-top: 0;
    padding: 19px;
    .descripcionHabitacion {
      .desc {
        strong {
          font-weight: 700;
        }
      }
    }
    .contFotoHabitacion .occupancy {
      b {
        text-transform: uppercase;
      }
    }
    .contDescHabitacion {
      .room_description_name_wrapper {
        overflow: visible;
      }
      .just_booking_message {
        background: $red;
        font-weight: 500;
        color: white;
        margin-right: -40px;

        &:after {
          display: none;
        }
      }
    }

    .room_services {
      margin-left: 0;
      border-top-style: solid;
      border-bottom-style: solid;
      border-color: $grey;
      border-top-width: 1px;
      border-bottom-width: 1px;
      padding: 1px 0;
      .service_element {
        border-right-width: 1px;
        border-right-style: solid;
        border-color: $grey;
        &:last-of-type {
          border-right: none;
        }
      }
    }
  }
  .precioTotalColumn .priceValues .promotion_percentage_square {
    background-color: $red;
    width: 40px;
    border-radius: 50px;
    line-height: 39px;
    margin-top: -35px;
    margin-right: -50px;
    .promotion_discount {
      width: auto;
      padding: 5px;
      right: 120%;
      left: auto;
      background: $corporate_2;
    }
  }
}

.rates_conditions_popup {
  .rate_description_content {
    overflow: visible;
    font-size: 14px;
    font-weight: 400 !important;
    max-height: 350px;
    padding: 15px 15px 0 15px;
    strong {
      font-weight: 700;
    }
    div {
      margin: 0;
    }
    ul {
      margin: 7px 0;
      li {
        list-style-type: disc;
        margin-left: 30px;
      }
    }
  }
}


div#step-2 {
  .booking-3-info, .booking-3-info--title,.booking-3-info--price {
    color: $corporate_1;
  }
  .booking_button_wrapper .booking-button {
    background-color: $corporate_3;
  }
}

div#step-3 {
  background: white;
  .personal_details_payment_wrapper,
  .booking_details_prices_wrapper {
    display: table-cell;
    min-height: 500px;
    position: relative;
    box-shadow: 0 0 15px rgba(0,0,0,0.15);
  }

  .booking-button {
    font-family: $text_family;
    background-color: $corporate_3;
    &:before {
      background-color: $corporate_1;
    }
  }
}

.site-footer {
  background: $corporate_2;
  color: white;
  font-family: $font_1;
  #footer_bottom_text p {
    font-size:15px;
    font-weight: 300;
    letter-spacing: 1px;
  }
}
.fancybox-wrap.fancy-booking-search_v2 {
  .container_popup_booking {
    border: 1px solid $corporate_1;
  }
  .gif_wrapper .default_line_loading {
    background-color: $corporate_1;
  }
  .description_bottom_popup_booking div {
    color: $black !important;
  }
  .dots_loader .dot {
    background: #00235A;
  }
  .description_bottom_popup_booking {
    color: #00235A;
    font-family: $font_1;
    font-weight: bolder;
    div {
      color: #00235A !important;
    }
  }
}

#calendar_price_availability {
  * {
    font-family: 'Open Sans', sans-serif;
  }
  .header_wrapper_calendar_availability {
    .graph_calendar_selector {
      .calendar_button, .graph_button {
        font-family: 'Open Sans', sans-serif;
        background: #D7D7D7;
        color: #333;
        text-transform: uppercase;
        -webkit-box-shadow: 0px 3px 39px -13px rgba(0, 0, 0, 0.5);
        -moz-box-shadow: 0px 3px 39px -13px rgba(0, 0, 0, 0.5);
        box-shadow: 0px 3px 39px -13px rgba(0, 0, 0, 0.5);
        span {
          background: transparent;
        }
        &.active {
          background: white;
        }
      }
      .calendar_button:before, .graph_button:before {
        content: "\e9a1";
        position: absolute;
        top: 50%;
        -webkit-transform: translate(-50%, -50%);
        -moz-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        font-family: "icomoon", sans-serif;
        font-size: 30px;
        speak: none;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
      }
      .calendar_button:before {
        content: "\e9a1";
      }
      .graph_button:before {
        content: "\f080";
        font-family: "Font Awesome 5 Pro", sans-serif;
      }
    }
  }
  #prices-calendar {
    background: white;
    -webkit-box-shadow: 0px 30px 50px -23px rgba(0, 0, 0, 0.5);
    -moz-box-shadow: 0px 30px 50px -23px rgba(0, 0, 0, 0.5);
    box-shadow: 0px 30px 50px -23px rgba(0, 0, 0, 0.5);
    .overlay_loading {
      background: rgba(white, .8);
    }
    .entitySelector {
      label {
        color: #333;
      }
    }

    .calendars-section {
      .calendars {
        .calendars_wrapper {
          table.calendar .first-selection .available-day, table.calendar .first-selection .day-content, table.calendar .first-selection .day, table.calendar .end-selection .available-day, table.calendar .end-selection .day-content, table.calendar .end-selection .day {
            background: $corporate_1 !important;
          }
          table.calendar .first-selection .day-content:before {
            border-color: transparent transparent transparent $corporate_1;
          }
          table.calendar .end-selection .day-content:before {
            border-color: transparent $corporate_1 transparent transparent;
          }
          table.calendar .selected-cell-parent:not(.first-selection):not(.end-selection) .available-day, table.calendar .selected-cell-parent:not(.first-selection):not(.end-selection) .day-content {
            background-color: $corporate_2 !important;
          }
          .calendar {
            * {
              border-color: white;
            }

            .day.available-day {
              background-color: #34A853;
            }

            tbody {
              tr {
                th:not(.day_label_element) {
                  font-family: 'Open Sans', sans-serif;
                  background: $corporate_1;
                  font-size: 25px;
                  padding: 10px 0;
                  letter-spacing: 3px;
                }
              }
            }
          }
        }
      }
      .graphs_fields_wrapper {
        background: white;
      }
      .legend {
        .available-day-box {
          background-color: #34A853;
          border-color: #34A853;
        }
        ul li p {
          color: #333;
        }
      }
      .buttons-section {
        .actual_selection_info_wrapper {
          .nights_number_wrapper {
            background: $corporate_1;
          }
          .selection_price_wrapper {
            .total_price_label {
              margin-bottom: -1px !important;
              font-size: 11px;
            }
            .total_price_value {
              font-size: 23px;
            }
          }
        }
        .button.modifyButtonCalendar {
          font-family: 'Open Sans', sans-serif;
          background-color: $corporate_1;
        }
      }
    }
  }
}

.booking_engine_wrapper_process #booking.boking_widget_inline .wrapper_booking_button .submit_button {
  color: white;
}
.booking_engine_wrapper_process.has_babies {
  .wrapper_booking_button {
    width: 440px !important;
  }
  .stay_selection {
    width: 35% !important;
  }
}
.hidden_booking_summary.showed {
  .booking-search-results__search-data {
    font-weight: 400;
    .booking-3-info, .booking-hotel-name {
      font-weight: bold;
      color: $black !important;
    }

    .booking-title-info:not(.booking-hotel-name) {
      font-weight: 400;
      color: $black;
    }
  }
  .booking-search-results__rooms-list {
    padding-top: 20px;
    font-weight: 400;
    .search-item .booking-title-info {
      font-weight: 400;
    }
    b {
      font-weight: 400;
    }
  }
  .title_booking_breakdown {
    font-weight: 400;
  }
  .booking-search-results.has_web_support {
    width: 85%;
    .booking-search-results__rooms-list {
      padding-left: 25px;
    }
    .booking-search-results__new-search .booking-button {
      max-width: 210px;
    }
  }
  .call_center_wrapper {
    top: 0;
    max-height: 85px !important;
  }
}


div#step-1 .booking-box--search .booking-search-results.has_web_support {
  width: 85% !important;
  .title_booking_breakdown {
    padding: 0 30px 0 10px !important;
  }
  .booking-search-results__search-data {
    padding-left: 45px !important;
  }
  .booking-search-results__rooms-list {
    padding-left: 15px !important;
  }
  .booking-search-results__new-search .booking-button {
    max-width: 180px;
    padding: 10px 20px 20px 50px !important;
    &:before {
      font-size: 25px;
    }
  }
}
.call_center_wrapper {
  width: 14%;
  display: inline-block;
  border: 1px solid $corporate_2;
  max-height: initial !important;

  .web_support_label_1 {
    width: 85%;

    &:before {
      margin-right: 0;
    }

    .web_support_wrapper {
      line-height: 18px;
      text-align: center;

      strong {
        position: relative;
        right: 15px;
      }
    }
  }
}

#currencyDiv {
  padding: 0 44px;
  margin: 5px 10px 0 !important;
}
.package_element_wrapper .package_prices_wrapper {
  .promotion_default_price.active + .since_prices_values {
    color: $corporate_2;
  }
  .perform_package_booking_search {
    background: $corporate_1;
    border-radius: 15px;
    &:hover {
      background: $corporate_2;
    }
  }
}
div#step-2 .booking-2-services-list .add_service_element {
  &:after {
    background: $corporate_2;
  }
}

@media only screen and (device-width: 768px) {
  body.booking_process_version_1 .actual_wizard_step .wizard-tab--small, .actual_wizard_step .wizard-tab--big {
    a {
      &:before, &:after {
        left: calc(100% - 1px);
      }
    }
  }
}
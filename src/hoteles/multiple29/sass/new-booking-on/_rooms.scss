div#step-1 {
  .custom_pictures_range {
    position: relative;
    z-index: 1;
  }

  #rooms_b1_wrapper {
    position: relative;
  }

  .contTipoHabitacion {
    padding: 20px;
    box-shadow: 0px 3px 10px #00000029;
    margin-bottom: 50px;
    border-radius: 20px;

    .contFotoDescripcion {
      padding: 0;

      .contFotoHabitacion {
        width: 306px;
        min-height: 205px;
        border-radius: 3px;

        a {
          &::before {
            display: none;
          }
        }

        .see_more_rooms_v2 {
          img {
            @include cover_image;
            position: static;
          }

          .lupa {
            right: auto;
            left: 0;
            padding: 15px;
            font-family: "Font Awesome 6 Pro";
            font-size: 30px;
            font-weight: 300;
          }
        }
      }

      .contDescHabitacion {
        margin: 0;
        width: 780px;
        box-sizing: border-box;
        height: 205px;

        .room_description_name_wrapper {
          max-height: 115px;
          overflow: visible;

          .very_asked_message,
          .just_booking_message {
            font-family: $text_family;
            font-weight: 400;
            font-size: 12px;
            letter-spacing: 1.2px;
            line-height: 22px;
            color: $black;
            text-transform: uppercase;
            padding: 5px 15px;
            right: -30px;

            &::after, &::before {
              display: none;
            }
          }

          .very_asked_message {
            background-color: $corporate-4;
            border-radius: 5px 0 0 5px;
          }

          .just_booking_message {
            background-color: $corporate-3;
            border-radius: 0 5px 5px 0;
          }
        }

        .cabeceraNombreHabitacion {
          margin-bottom: 20px;
        }

        .tipoHabitacion {
          font-family: $title_family;
          font-weight: 400;
          font-size: 26px;
          letter-spacing: 0;
          line-height: 27px;
          text-transform: none;
          color: $black;
          max-width: 430px;
          margin-top: 15px;
          min-width: 430px;
        }

        .descripcionHabitacion {
          font-family: $text_family;
          font-weight: 400;
          font-size: 16px;
          letter-spacing: 0.35px;
          line-height: 26px;
          color: $black;
          height: 55px;
          padding: 0 20px;
        }

        .see_more_rooms_v2 {
          bottom: 50px;

          .plus_sign {
            color: $black;
            padding: 3px 7px 3px 0;
            font-size: 16px;
          }

          .see_more {
            font-family: $text_family;
            font-weight: 700;
            font-size: 16px;
            letter-spacing: 0.8px;
            line-height: 23px;
            color: $black;
          }
        }

        .room_services {
          margin: 0 auto;
          border-top: 0;
          border-bottom: 0;

          .service_element {
            border-right: none;

            i {
              color: $black !important;
            }

            .service_description {
              font-size: 12px !important;
              letter-spacing: 0.7px !important;
              line-height: 15px !important;
              color: $black !important;
            }
          }

          .service_element + .service_element {
            border-left: 0;
          }
        }
      }
    }

    .preciosHabitacion {
      .listadoHabsTarifas {
        border-spacing: 0 8px;

        .contTitTipoTarifa {
          background: $grey-1;
          height: 43px;

          .titTipoTarifa {
            font-family: $text_family;
            font-weight: 400;
            font-size: 14px;
            letter-spacing: 0.7px;
            line-height: 24px;
            color: $black;
            text-transform: capitalize;
          }

          .advice_rate_message {
            background-color: rgba($corporate_2, 0.35) !important;
            color: $grey-2;

            .best_price_label_info {
              font-family: $text_family;
              font-weight: 600;
              font-size: 12px;
              letter-spacing: 0.75px;
              line-height: 1;
              color: $grey-2;
            }

            .conditions_info_wrapper {
              height: auto;

              a {
                color: $black;
                text-transform: none;
                font-weight: 300;
                font-size: 14px;
                letter-spacing: 0.31px;
                padding-right: 45px;

                &:after {
                  content: "\f05a";
                  font-family: 'Font Awesome 6 Pro';
                  font-size: 16px;
                  color: $black;
                  position: absolute;
                  right: 20px;
                  font-weight: 300;
                }
              }
            }
          }

          .cheapest_rate_message {
            background: transparent !important;

            &::before {
              display: none;
            }

            .best_price_label_info {
              font-size: 12px;
              letter-spacing: 1.2px;
              line-height: 22px;
              color: $black;
              background: white !important;
              border-radius: 14px;
              padding: 5px 15px 5px 20px !important;
              position: relative;

              &::before {
                content: "\f005";
                font-family: "Font Awesome 6 Pro";
                font-size: 16px;
                color: $corporate-1;
                margin-right: 5px;
                font-weight: bold;
                border-left: 0;
              }
            }

            &.has_conditions {
              display: flex;
              top: 3px !important;
              padding: 1px 30px 5px 40px !important;

              .best_price_label_info {
                width: max-content;
                margin-right: 20px;
              }

              .conditions_info_wrapper {
                width: max-content;
                margin: 5px 0 !important;
                padding: 0 !important;
              }
            }
          }
        }

        .conditions_info_wrapper {
          position: initial;
          padding: 0 25px 0 0 !important;
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: auto;
          border-bottom: 0;
          margin: 10px 0;
          box-sizing: border-box;

          .last_day_cancellation_text {
            font-size: 14px;
            letter-spacing: 0.8px;
            line-height: 24px;
            color: $green;
            font-family: $text_family;
          }

          a {
            color: $black;
            text-transform: none;
            font-weight: 300;
            font-size: 16px;
            letter-spacing: 0.35px;
            padding-right: 25px;
            background: transparent;
            position: relative;


            &:after {
              content: "\f05a";
              font-family: 'Font Awesome 6 Pro';
              font-size: 16px;
              color: $black;
              position: absolute;
              right: 0;
              font-weight: 300;
            }
          }
        }

        .regimenColumn {
          width: 35%;
          font-family: $text_family;
          font-weight: 400;
          font-size: 16px;
          letter-spacing: 0.8px;
          line-height: 24px;

          .regimenColumnContent {
            .regimen_name_wrapper {
              font-size: 16px;
              line-height: 24px;
              letter-spacing: 0.8px;
              color: $black;
            }

            .regimen-popup-v2 {
              position: relative;

              img {
                display: none;
              }

              &::before {
                content: url(https://storage.googleapis.com/cdn.paraty.es/onhotel/files/icono-interrogacion.png);
                position: absolute;
                top: -2px;
                left: 5px;
                right: 0;
                bottom: 0;
              }
            }

            .tTextoOferta {
              font-weight: 700;
              font-size: 16px;
              margin-left: 3px;
              color: $green;

              .offer_name_element {
                font-weight: 700;
                font-size: 16px;
                margin-left: 3px;
                letter-spacing: 0.8px;
                line-height: 24px;
                color: $green;
              }
            }
          }
        }

        .precioNocheColumn {
          width: 10%;

          .priceValues {
            display: none;
          }
        }

        .precioTotalColumn {
          width: 35%;
          font-family: $text_family;

          .precioTachadoDiv {
            text-align: right;
            color: $green !important;

            .tPrecioTachado {
              font-weight: 400;
              font-size: 13px;
              letter-spacing: 0.78px;
              line-height: 24px;
              color: $green !important;
            }
          }

          .precioGeneralDiv {
            .tPrecioTotal,
            .tPrecioOferta {
              font-weight: 900;
              font-size: 20px;
              color: $black !important;
              letter-spacing: 1px;
              line-height: 24px;
            }
          }

          .promotion_percentage_square {
            border-radius: 50%;
            background: $green !important;

            .promotion_discount {
              border-radius: 10px;
              background-color: $green;
            }
          }

          .priceTitle {
            font-weight: 400;
            font-size: 12px;
            color: $black;
            letter-spacing: 0.24px;
            line-height: 14.4px;
          }
        }

        .botonReservarColumn {
          button {
            width: max-content;
            padding: 12px 40px;
            border-radius: 10px;
            outline: none;
          }

          .booking-button {
            background: $corporate_1;
            border-radius: 25px;
            font-family: $title_family;
            text-align: center;
            font-size: 22px;
            letter-spacing: 0;
            line-height: 24px;
            text-transform: none;
            font-weight: 700;
            @include transition(all, .5s);

            &:hover {
              background: $corporate_2;
            }
          }
        }
      }
    }
  }
}

.fancybox-overlay {
  .room_popup_individual_element {
    .popup_title {
      font-family: $text_family;
      font-weight: 500;
      font-size: 21px;
      letter-spacing: 1.15px;
      line-height: 1;
      text-transform: capitalize;
      color: white;
    }

    .popup_carousel {
      .exceded {
        padding: 0;

        .popup_image {
          @include cover_image;
          position: static;
        }
      }
    }

    .popup_room_description {
      font-family: $text_family;
      font-weight: 400;
      font-size: 13px;
      letter-spacing: 0.75px;
      line-height: 21px;
      color: $black;
    }
  }

  .popup_see_more_rooms_second {
    .room_desc_wrapper {
      .room_name {
        font-family: $title_family;
      }

      .room_desc {
        font-family: $text_family;
      }
    }
  }
}

.room_popup_individual_element .room_services,
div#step-1 .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_services {
  display: grid !important;
  padding: 0 !important;
  grid-template-columns: repeat(4, auto);

  .service_element {
    width: 100%;
    height: auto;
    @include display_flex(nowrap);
    justify-content: center;
    align-items: center;
    padding: 10px;
    text-align: center;

    i {
      font-size: 25px;
      margin-right: 10px;
      color: $black;
      font-weight: 100;
    }

    .service_description {
      font-family: $text_family;
      font-weight: 400;
      font-size: 12px;
      letter-spacing: 0.7px;
      line-height: 1;
      color: $black;
    }
  }
}

.rate_conditions_popup_wrapper h3.rate_name, .package_hidden_popup .popup_title {
  font-family: $text_family;
}

#packages_b1_wrapper.v2 {
  .ui-datepicker {
    .ui-widget-header .ui-datepicker-title, table {
      font-family: $text_family;
    }

    .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
      font-family: $text_family;
    }
  }
}
{{ widget_controller_injection|safe }}

bookingWidgetController.add_widget_html = function () {
    var paraty_widget = bookingWidgetController.config.widget_html;
    if($("#widget_paraty").length) {
        $("#widget_paraty").html(paraty_widget);
        $("#widget_paraty #full_wrapper_booking .booking_form_title").append($(".cancel_booking_link"));

    }
};

bookingWidgetController.occupancy_format_html = function() {
    return "<span class='adults'>@@N_R@@ @@T_R@@</span>, <span class='adults'>@@N_A@@</span><span class='kids'>-@@N_C@@</span>";
};

bookingWidgetController.adding_room_tag_selector = function () {
    $("select.rooms_number option").each(function (index, element) {
        $(element).text($(element).text() );
    });
    $("select.rooms_number").selectric("refresh");
};

bookingWidgetController.open_widget = function () {
    $("#floating_button").click(function () {
        $("#full_wrapper_booking").fadeToggle();
    });
};

bookingWidgetController.custom_functions = function () {
    var add_room_html = "<div class='add_remove_room_wrapper clearfix'><div class='add_room'>"+$.i18n._("T_anadir")+"</div><div class='remove_room'>"+$.i18n._("T_eliminar")+"</div></div>";
    $(".room_list_wrapper").append(add_room_html);
    $(".room_list_wrapper").on("click", ".add_room", function(){
        add_room();
    });
    $(".room_list_wrapper").on("click", ".remove_room", function(){
        remove_room();
    });
    bookingWidgetController.config.languages = {
        "es": "SPANISH",
        "en": "ENGLISH",
    };

    try {
        create_link_booking();
    } catch(error) {
        console.log("[Paraty Widget] Error trying to get the utl to booking engine");
    }

    $py = $;
    $.getScript(bookingWidgetController.config.base_url + "/static_1/scripts_injections/hotel_selector_2.js?v=1.1");

    $(".hotel_selector li").click(function () {
        var hotel_name = $(this).find(".title_selector").html();
        $(".destination_wrapper .destination_field .destination").html(hotel_name);
    });
    $(window).on( "load", function () {
        bookingWidgetController.prepare_guest_selector();
    })
};

function create_link_booking() {
    var link_booking = bookingWidgetController.config.base_url + "/booking1?numRooms=1&adultsRoom1=2" +
        "&adultsRoom2=0&adultsRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&babiesRoom1=0&" +
        "babiesRoom2=0&babiesRoom3=0&fromCountry=&language=" +
        bookingWidgetController.config.languages[bookingWidgetController.config.language];

    $(".paraty-booking-link").each(function(){
        $(this).attr("href", link_booking);
    });
}
function add_room(){
   var number_rooms = parseInt($("select.rooms_number").val());
   console.log(number_rooms);
   if (number_rooms < 3) {
       $($(".rooms_number_wrapper .selectricItems li").get(number_rooms)).trigger("click");
       bookingWidgetController.set_occupancy_number();
   }
}
function remove_room(){
   var number_rooms = parseInt($("select.rooms_number").val());
   if (number_rooms > 1) {
       $($(".rooms_number_wrapper .selectricItems li").get(number_rooms - 2)).trigger("click");
       bookingWidgetController.set_occupancy_number();
   }
}
$(window).on( "load", function () {
    bookingWidgetController.init();
    var widget_cont = $("#widget_paraty");
    if (widget_cont.width() < 895 && widget_cont.width() >= 490 ) {
        widget_cont.addClass("medium");
    } else if (widget_cont.width() < 490) {
        widget_cont.addClass("minimal");
    }
})
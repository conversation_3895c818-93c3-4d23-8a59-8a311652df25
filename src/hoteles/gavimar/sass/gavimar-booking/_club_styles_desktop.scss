$club_color_1: #D6896E;
$club_color_2: #0E4A77;

#login_wrapper_element.v3 {
  background: white;
  border: 1px solid $club_color_2;

  .logo_wrapper {
    &::after {
      content: '';
      border-right: 1px solid $club_color_2;
      position: absolute;
      right: 0;
      top: 10%;
      bottom: 10%;
      height: 80%;
    }
  }

  .content_login_wrapper {
    .club_icons_wrapper {
      margin: 0 30px 0 15px;

      .club_icon_element {
        .icon_image_wrapper {
          img {
            max-height: 65px;
            margin-top: 12px;
          }
        }

        .club_icon_description {
          color: $club_color_2;
          font-weight: 400;
          font-family: $text_family;
          font-size: 16px;
          line-height: 26px;
        }
      }
    }

    .users_buttons_wrapper {
      margin-top: 16px;
      font-size: 14px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .join_button_wrapper {
        width: 100%;

        .want_join {
          background: $club_color_1;
          border-radius: 14px;
          letter-spacing: 0.98px;
          font-weight: 500;
          font-size: 14px;
          min-width: 220px;
          padding: 5px;
          width: 100%;
          transition: all .3s;
          text-transform: uppercase;

          &:hover {
            background: $club_color_2;
          }
        }
      }

      .already_member_wrapper {
        border: 1px solid $club_color_2;
        color: $club_color_2 !important;
        border-radius: 14px;
        letter-spacing: 0.98px;
        font-weight: 500;
        font-size: 14px;
        min-width: 220px;
        padding: 5px;
        width: 96%;
        transition: all .3s;
        text-transform: uppercase;

        &:hover {
          background: $club_color_2 !important;
          color: white !important;
        }
      }
    }
  }
}

#logged_user_info_wrapper {
  background: white;
  border: 1px solid $club_color_2;

  .logo_wrapper {
    &::after {
      content: '';
      border-right: 1px solid $club_color_2;
      position: absolute;
      right: 0;
      top: 10%;
      bottom: 10%;
      height: 80%;
    }
  }

  .content_logged_wrapper {
    .center_content_wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .logged_user_text {
        max-width: 45%;
        text-align: left;
      }

      .user_category_image {
        display: none;
      }

      .extra_logged_user_info {
        margin: 10px auto 0;
        font-size: 15px;
      }

      .logged_user_text, .extra_logged_user_info, .user_points {
        color: $club_color_2;
      }

      .logout_button_wrapper {
        i, span {
          color: $club_color_2;
        }
      }
    }
  }
}

div#step-1 {
  table.listadoHabsTarifas {
    tr {
      .lock_board {
        padding: 15px 10px !important;

        .lock_board_wrapper {
          border: 1px solid $club_color_1;
          padding: 10px !important;
          border-radius: 50px !important;
          position: relative;
          width: 135px !important;
          background: transparent !important;

          .lock_mini_label {
            position: absolute;
            top: -10px;
            background: white;
            left: 25px;
            padding: 0 5px;
            color: $club_color_1;
            font-size: 11px;
          }

          .club_lock_logo {
            max-height: 22px;
          }

          span {
            color: $club_color_2;
          }

          span.currencyValue {
            font-size: 16px;
            letter-spacing: 0;
            line-height: 24px;
          }

          .lock_ico {
            position: absolute;
            margin-left: 0 !important;
            top: -10px;
            left: 10px;
            background: white;
            padding: 0 5px;
            font-size: 10px !important;
            color: $club_color_1;

          }
        }
      }
    }
  }
}


.modal_content {
  font-family: $text_family;

  #register_form_wrapper {
    border-radius: 14px;

    #signup_form {
      .register_title_form_wrapper {
        background: $club_color_2;

        .main_form_title {
          color: white;
          background: $club_color_2;
          font-family: $title_family;
          font-weight: 500;
          font-size: 27px;
          line-height: 30px;
        }
      }

      .inputs_wrapper {
        .buttons_wrapper_signup {
          .sign_up_button {
            background: $club_color_1;
            border-radius: 14px;
            font-weight: 500;
            font-family: $title_family;
            letter-spacing: .98px;
            transition: all .3s;

            &:hover {
              background-color: $club_color_2;
              opacity: 0.8
            }
          }
        }
      }
    }
  }

  #login_form_wrapper_v1 {
    border-radius: 14px;

    .header_login_wrapper {
      background: $club_color_2;

      .login_title {
        color: white;
        font-family: $title_family;
        font-weight: 500;
        padding: 0 10px;
        font-size: 27px;
        line-height: 30px;
      }
    }

    .login_block {
      .login_button_element {
        background: $club_color_1 !important;
        border-radius: 14px;
        font-weight: 500;
        font-family: $title_family;
        letter-spacing: .98px;
        transition: all .3s;

        &:hover {
          background-color: $club_color_2 !important;
          opacity: 0.8
        }
      }
    }
  }
}

.club_send_password_wrapper {
  #club-send-password-form {
    #submit_recovery_club {
      background: $club_color_1 !important;
      border-radius: 14px;
      font-weight: 500;
      font-family: $title_family;
      letter-spacing: .98px;
      transition: all .3s;
      text-transform: uppercase;
      padding: 5px;
      min-width: 150px;
      text-align: center;

      &:hover {
        background-color: $club_color_2 !important;
        opacity: 0.8
      }
    }
  }
}

#step-3 #personal-details-form .lock_rates_wrapper {
  background: $club_color_2;
  border-radius: 14px;
}
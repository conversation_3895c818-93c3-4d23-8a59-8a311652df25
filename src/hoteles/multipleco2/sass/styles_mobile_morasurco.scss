$color1: #57120d;
$color2: #a17326;
$description_size: 12px;

@import "styles_mobile_base_2";
@import "styles_mobile_base_2_personalized";

nav#menu {
  background: $color1;
}

input#destino[type=submit], div.button, a[data-role=button] {
  background: $color2!important;
}

/*=== Minigallery ===*/
.minigallery_wrapper {
  display: inline-block;
  width: 100%;

  .gallery_element {
    position: relative;
    height: 32em;
    overflow: hidden;

    img {
      position: absolute;
      top: 50%;
      left: 50%;
      -webkit-transform: translate(-50%, -50%);
      -moz-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
      -o-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      min-width: 100%;
      min-height: 100%;
      max-width: none;
    }
  }
}

/*=== Cycle Banners ===*/
.cycle_wrapper {
  display: inline-block;
  width: 100%;

  .cycle_element {
    display: inline-block;
    width: 100%;
    line-height: 1;
    margin-bottom: 2em;
    padding-bottom: 2em;
    border-bottom: 1px solid $color2;

    &:last-child {
      margin-bottom: 0;
      padding-bottom: 0;
      border-bottom: 0;
    }

    .cycle_title {
      font-size: 2.2em;
      font-weight: bold;
      margin-bottom: 1em;
    }

    .cycle_image {
      width: 100%;
      height: 32em;
      position: relative;
      overflow: hidden;

      img {
        position: absolute;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        -moz-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }

    .cycle_description {
      font-size: 1.7em;
      margin-top: 1em;
      line-height: 2;
    }

    .cycle_link {
      display: inline-block;
      width: 100%;
      text-align: center;

      a {
        display: inline-block;
        width: 100%;
        color: white;
        background: $color1;
        text-transform: uppercase;
        font-size: 1.5em;
        padding: 1em;
        box-sizing: border-box;
        outline: .5em solid rgba(white, .5);
        outline-offset: -7px;
        font-family: "Montserrat";
      }
    }
  }
}

.contact_content_element div {
  float: none !important;
  width: 100% !important;
}

/*=== Rooms ===*/
.room_element {
  .rooms_slider {
    .slides {
      li {
        height: 25em;

        img {
          position: absolute;
          top: 50%;
          left: 50%;
          -webkit-transform: translate(-50%, -50%);
          -moz-transform: translate(-50%, -50%);
          -ms-transform: translate(-50%, -50%);
          -o-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
          min-width: 100%;
          min-height: 100%;
        }
      }
    }
  }

  .room_link {
    display: inline-block;
    width: 100%;
    text-align: center;
    margin-bottom: 2em;

    a {
      display: inline-block;
      width: 100%;
      color: white;
      background: $color1;
      text-transform: uppercase;
      font-size: 1.5em;
      padding: 1em;
      box-sizing: border-box;
      outline: .5em solid rgba(white, .5);
      outline-offset: -7px;
      font-family: "Montserrat";
    }
  }
}
.section_title {
    span {
        display: block;
    }
}

/*=== Icons Wrapper ===*/
.icons_wrapper {
  display: inline-block;
  width: 100%;

  .icon_element {
    display: inline-block;
    vertical-align: middle;
    width: calc(100%/3);
    margin-bottom: 1em;
    float: left;

    .icon_image {
      display: inline-block;
      vertical-align: middle;

      img {
        vertical-align: bottom;
      }
    }

    .icon_title {
      display: inline-block;
      vertical-align: middle;
    }
  }
}

/*=== Offers ===*/
.promotions-item {
  .offer_link {
    display: inline-block;
    width: 100%;
    text-align: center;
    margin-bottom: 2em;

    a {
      display: inline-block;
      width: 100%;
      color: white;
      background: $color1;
      text-transform: uppercase;
      font-size: 1.5em;
      padding: 1em;
      box-sizing: border-box;
      outline: .5em solid rgba(white, .5);
      outline-offset: -7px;
      font-family: "Montserrat";
    }
  }
}
/*=== Language Selector ===*/
footer{
     .language {
        display: none;
     }
}

/*======= Banners x3 ======*/
.bannersx3_wrapper {
  display: table;
  width: 100%;
  text-align: center;

  .title_bannerx3 {
      position: absolute;
      width: 100%;
      text-align: center;
      top: 0;
      color: white;
      font-weight: lighter;
      bottom: 0;
      height: 55px;
      margin: auto;
      z-index: 5;

      strong {
        display: block;
        font-weight: bolder;
        font-size: 30px;
        line-height: 1em;
      }

    }

  .bannersx3_element {
    width: 100%;
    height: 250px;
    margin-right: 10px;
    margin-bottom: 10px;
    overflow: hidden;
    position: relative;
    display: inline-block;

    .bannerx3_image {
        @include center_xy();
        min-width: 100%;
        min-height: 100%;
        max-width: inherit;
    }

  }
}

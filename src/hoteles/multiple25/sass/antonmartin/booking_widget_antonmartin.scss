@font-face {
  font-family: 'icomoon';
  src: url('/static_inj/fonts/iconmoon/icomoon.eot?khhs1w');
  src: url('/static_inj/fonts/iconmoon/icomoon.eot?khhs1w#iefix') format('embedded-opentype'),
  url('/static_inj/fonts/iconmoon/icomoon.ttf?khhs1w') format('truetype'),
  url('/static_inj/fonts/iconmoon/icomoon.woff?khhs1w') format('woff'),
  url('/static_inj/fonts/iconmoon/icomoon.svg?khhs1w#icomoon') format('svg');
  font-weight: normal;
  font-style: normal;
}

@import "plugins/mixins";
@import "plugins/fancybox_2_1_5";
@import "plugins/spiners/all_spiners";
@import "plugins/fontawesomemin";
@import "plugins/iconmoon";
@import "plugins/_booking_loading_popup";

$corporate_1: #357FAD;
$corporate_2: #333333;

@import url('https://fonts.googleapis.com/css?family=Roboto:300,400,700');

@mixin label() {
  color: rgba(white, .4);
  text-transform: uppercase;
  font-size: 18px;
  margin-bottom: 7px;
  margin-top: 30px;
  display: inline-block;
}

@import "widget";
@import "web";
@import "responsive";
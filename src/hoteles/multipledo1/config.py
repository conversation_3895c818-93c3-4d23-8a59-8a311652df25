import os

from webs.arrecife.templateHandler import Template<PERSON>and<PERSON> as ArrecifeHandler

host = os.environ.get('HTTP_HOST')

applicationId = "secure-booking-do"

extra_routing = {
	'.*localhost.*': {
		'.*': ArrecifeHandler,
	},
	'.*greenland.*': {
		'.*': ArrecifeHandler
	},
	'.*': {
		'.*': ArrecifeHandler
	},
}

#These templates get added to app.yaml (css, images)
templates = ['arrecife']
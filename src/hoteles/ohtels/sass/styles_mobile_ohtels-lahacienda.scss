$color1: #509FCE;
$color2: #fbbc1e;

@import "_styles_mobile_base_2";
@import "_styles_mobile_base_2_personalized";

.booking_form_title h4.best_price{
  display: none!important;
}

.destac_blocks_wrapper{

  margin-bottom:20px;

  h2.destac_blocks_title {
    text-align:center;
    margin: 20px auto;
    width: 100%;
    color: white;
    background:$color1;
    font-size: 2.5em;
    font-weight: lighter;
    text-transform: uppercase;
    padding: 13px 20px;
    font-family: 'Montserrat', sans-serif;
    box-sizing:border-box;
  }

  .destac_block_element{
    text-align:center;
    background:$color2;
    color:white;
  }

  .image_title_destac{
    box-sizing:border-box;
    padding: 20px;
  }
  .title_destac{
    font-size: 2.5em;
    font-weight: lighter;
    margin-top:20px;
  }
  a{
    background:$color1;
    color:white;
    display:inline-block;
    width:100%;
    padding:10px 0px;
    text-align:center;
    margin-top:3px;
    font-size: 2.2em;
    font-weight: lighter;
  }

}


iframe{
  width:100%;
}

#contact_usuario {
  #contact-button {
      background-color: #008BAC;
      margin: 0 auto;
      width: 100%;
      border: none;
      border-radius: 0;
      color: white;
      font-size: 2.625em;
      font-weight: lighter;
      text-transform: uppercase;
      padding: 13px 0;
      font-family: 'Montserrat', sans-serif;
      -webkit-appearance: none;
      display: block;
      text-align: center;
      line-height: 1em;
  }
  .info {
    .selectric-hide-select {
      display: none;
    }
    .selectric span.label {
      float: left;
      line-height: 1em;
      color: #333;
      font-size: 1.87em;
      position: absolute;
      top: 0;
      bottom: 0;
      text-align: left;
      display: table;
      margin: auto
    }
    .selectric b.button {
      float: right;
      position: absolute;
      right: 0;
      top: 0;
      color: #eaeaea;
      background: #eaeaea url(/static_1/images/mobile_img/renovation/arrow_darkgray.png) no-repeat center;
      background-size: 1.5em;
      height: 100%;
      width: 4.8em
    }
    #country_container {
      border: none;
      overflow: hidden;
      margin-bottom: 2em;
    }
    .selectric {
      display: block;
      position: relative;
      min-height: 5.93em;
      background: white;
      padding: 0 2em;
      box-sizing: border-box;
      text-align: center;
      border: 0.5em solid #F9F9F9
    }
    .selectric-items {
      width: 100% !important;
      //max-height: 0;
      height: auto !important;
      -webkit-transition: all 0.5s;
      -moz-transition: all 0.5s;
      -ms-transition: all 0.5s;
      -o-transition: all 0.5s;
      transition: all 0.5s;
    }
    #birthday_container {
      .selectric-wrapper.selectric-booking-form-control.selectric-select-birthday {
        width: 33%;
        display: inline-block;
        padding-bottom: 5px;
        .selectric-items {
          position: absolute;
          width: 29%!important;
        }
      }
    }
     .selectric-items {
      display: none;
    }
    .selectric-hover .selectric-items {
      display: block;
    }
    .selectric-items .selectric-scroll ul li {
          text-align: left;
          display: block;
          position: relative;
          font-size: 1.37em;
          background: white;
          padding: 0.9em 1.2em;
          box-sizing: border-box;
          border-bottom: 2px solid #BFBFBF;
          border-left: 2px solid #BFBFBF;
          border-right: 2px solid #BFBFBF;
          color: #333;
          margin: 0;
    }
    .selectric-input {
      display: none;
    }
    #response_alta {
      background: #509FCE;
      color: white;
      padding: 10px 10px;
      margin-bottom: 10px;
      text-align: left;
      font-size: 2em;
      line-height: 1.2em;
    }
  }
}

.normal_section_mobile .club_descripcion {
    box-sizing: border-box;
}


.text_loading_popup {
  font-size: 0 !important;
  strong {
    font-size: 25px !important;
  }
}
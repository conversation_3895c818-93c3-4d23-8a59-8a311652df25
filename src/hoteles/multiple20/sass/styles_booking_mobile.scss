@import "booking_mobile/booking.scss";
@import "booking_mobile/v1/mixin_colores";


/********** <PERSON> *********/

.simon-sevilla.booking_process{
    $corporate_1: #870023;
    $corporate_4: #ff7302;
    $color1: $corporate_1;
    $background_header: $corporate_1;
    @import "booking_mobile/custom_booking";
    div#header {
        height: 85px;
    }
}

.singular-la-escondida {
    $corporate_1: #d9cf42;
    $corporate_2: rgb(45, 48, 71);
    $corporate_3: #666;
    $corporate_4: #686215;
    
    .modify_search,
    .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
        background-color: $corporate_1!important;
    }
    
    .show_calendar,
    .back_button {
        background-color: $corporate_2!important;
    }
}



.singular-montico {
    $corporate_1: #1a1a1a;
    $corporate_2: #333333;
    $corporate_3: #666;
    $corporate_4: #686215;
    
    .modify_search,
    .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
        background-color: $corporate_1!important;
    }
    
    .show_calendar,
    .back_button {
        background-color: $corporate_2!important;
    }
}

/********** Antonio Hoteles *********/

.antonio-hoteles-4.booking_process{
    #content {
        #logo-container {
            #logo {
                a {
                    img {
                        display: none !important;
                    }
                }
            }
        }
    }
}

.antonio-hoteles-4 {
    $color1: #019ed3;
    &.booking_process {
      @import "booking_mobile/custom_booking";
    }
}

/********** Singular Corpo *********/

.singular-corporativa.booking_process {
    div#header {
        background: black;
    }
}

/********** Singular Toloriu *********/

.singular-toloriu {
    /*&.booking_process {
      $color1: #777;
      $background_header: black;
      @import "booking_mobile/custom_booking";
    }
    &.booking_process .booking3 form#personalDetailsForm>div select {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
    }*/
    $corporate_1: #1a1a1a;
    $corporate_2: #333333;
    $corporate_3: #666;
    $corporate_4: #686215;
    
    .modify_search,
    .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
        background-color: $corporate_1!important;
    }
    
    .show_calendar,
    .back_button {
        background-color: $corporate_2!important;
    }
}

/********** Singular Villa Auristela *********/

.singular-auristela {
    &.booking_process {
      $color1: #777;
      $background_header: #c2ab56;
      @import "booking_mobile/custom_booking";
    }
}

/********** Singular Torre del manresa *********/

.singular-manresa {
    &.booking_process {
      $color1: #F6B401;
      $background_header: black;
      @import "booking_mobile/custom_booking";
    }
}

/********** Singular Beauty Llivia *********/

.singular-llivia {
    &.booking_process {
      $color1: #777777;
      $background_header: #1a1a1a;
      @import "booking_mobile/custom_booking";
    }
}

/********** Singular Peixos *********/

.singular-peixos {
    &.booking_process {
      $color1: #666;
      $background_header: #1a1a1a;
      @import "booking_mobile/custom_booking";
    }
}

/********** Singular La Marina *********/

.singular-la-marina {
    /*&.booking_process {
      $color1: #0D5586;
      $background_header: white;
      $orange: #FFBC82;
      @import "booking_mobile/custom_booking";
      .busqueda {
        color: $color1;
      }
      .booking3 button#btn-finish-booking {
        background: $color1;
      }
      .footer_control_wrapper {
        color: $color1;
      }
    }*/
    
    $corporate_1: #0D5586;
    $corporate_2: rgb(45, 48, 71);
    $corporate_3: #666;
    $corporate_4: #686215;
    
    .modify_search,
    .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
        background-color: $corporate_1!important;
    }
    
    .show_calendar,
    .back_button {
        background-color: $corporate_2!important;
    }
}

/********** Singular tempus *********/

.singular-tempus {
    /*&.booking_process {
      $color1: #0D5586;
      $background_header: white;
      $orange: #FFBC82;
      @import "booking_mobile/custom_booking";
      .busqueda {
        color: $color1;
      }
      .booking3 button#btn-finish-booking {
        background: $color1;
      }
      .footer_control_wrapper {
        color: $color1;
      }
    }*/
    
    $corporate_1: rgba(0, 0, 0, 0.8);
    $corporate_2: rgb(45, 48, 71);
    $corporate_3: #666;
    $corporate_4: #686215;
    
    .modify_search,
    .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
        background-color: $corporate_1!important;
    }
    
    .show_calendar,
    .back_button {
        background-color: $corporate_2!important;
    }
}

/********** Singular Garza Real *********/

.singular-garza {
    /*&.booking_process {
      $color1: #653004;
      $background_header: white;
      @import "booking_mobile/custom_booking";
    }*/
    
    $corporate_1: #653004;
    $corporate_2: rgb(45, 48, 71);
    $corporate_3: #666;
    $corporate_4: #686215;
    
    .modify_search,
    .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
        background-color: $corporate_1!important;
    }
    
    .show_calendar,
    .back_button {
        background-color: $corporate_2!important;
    }
}

/********** Singular Fonte *********/

.singular-fonte {
    /*&.booking_process {
      $color1: #653004;
      $background_header: white;
      @import "booking_mobile/custom_booking";
    }*/
    
    $corporate_1: rgba(0, 0, 0, 0.8);
    $corporate_2: rgb(45, 48, 71);
    $corporate_3: #666;
    $corporate_4: #686215;
    
    .modify_search,
    .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
        background-color: $corporate_1!important;
    }
    
    .show_calendar,
    .back_button {
        background-color: $corporate_2!important;
    }
}


/********** Unique Spaces *********/

.unique-spaces {
    $color1: #86BFCE;
    $background_header: #232325;
    &.booking_process {
      @import "booking_mobile/custom_booking";
    }
}

/********** Eibar rooms *********/

.eibar-rooms {
    $color1: #118acb;
    $background_header: #e0e0e0;
    &.booking_process {
      @import "booking_mobile/custom_booking";
    }
}


/********** SINGULAR FINCA FABIOLA *********/

.singular-finca-fabiola {
    /*$color1: #9A0B32;
    $background_header: white;
    &.booking_process {
      @import "booking_mobile/custom_booking";
    }*/
    
    $corporate_1: #9A0B32;
    $corporate_2: rgb(45, 48, 71);
    $corporate_3: #666;
    $corporate_4: #686215;
    
    .modify_search,
    .room_list .room_pack_option .rates_details_wrapper .regime_item_content .regime_price_wrapper div.submit span {
        background-color: $corporate_1!important;
    }
    
    .show_calendar,
    .back_button {
        background-color: $corporate_2!important;
    }
}
.elimar.booking_process{
    $corporate_1: #FFFFFF;
    $corporate_2: #1C6077;
    $corporate_3: #99afb6;
    $corporate_4: #DBDBDB;

    .step_indicator{
        background: $corporate_3;
    }
    .modify_search{
        background: $corporate_2;
    }
    #footer{
        background: $corporate_2;
    }
    img.arrow_room {
      content: url("/static_1/images/booking_3/flecha-der.png");
      background-color: #c7b64f;
      border-radius: 50%;
      width: 30px;
      padding: 5px 7px;
      box-sizing: border-box;
    }
}
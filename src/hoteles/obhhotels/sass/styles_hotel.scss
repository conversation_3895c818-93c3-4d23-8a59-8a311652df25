$fontawesome5: true;
@import "booking/booking_process_v1/booking_header";
@import "booking/booking_all_styles";
@import "plugins/mixins";


@import url('https://fonts.googleapis.com/css2?family=Niramit:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;1,200;1,300;1,400;1,500;1,600;1,700&display=swap');
@mixin individual_colors($corporate_1, $corporate_2) {
  .color1 {
    color: $corporate_2;
  }
  .bgc1 {
    background-color: $corporate_2;
  }
  #full_wrapper_booking {
    .boking_widget_inline .booking_form .wrapper_booking_button {
      .promocode_wrapper {
        height: 100%;
        padding: 25px 10px !important;

        .promocode-imput {
          height: 60px;
          border-radius: 20px;
          border: solid 2px $corporate_1;
        }
      }

      .modify-calendar-button:hover {
        background: $corporate_2;
      }

      .submit_button {
        background-color: $corporate_1;
      }
    }
  }
  .fancybox-overlay .room_popup_individual_element .room_services .service_element i, div#step-1 .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_services .service_element i {
    color: $corporate_1;
  }
  .actual_wizard_step .wizard-tab--small a.actual::before {
    background-color: $corporate_2;
  }
  .share_links_wrapper {
    .share_links_prev {
      background-color: $corporate_2;
    }

    .share_links_cont .share_link {
      background-color: $corporate_2;
    }
  }

  #wizard {
    div#step-1 {
      .rooms_packages_selectors_wrapper.has_packages {
        .button_package_room {
          background-color: transparent;
          border: none;
          padding-left: 225px;
          text-align: left;

          &.rooms_selector,
          &.packages_selector,
          &.club_rates_viewer{
            z-index: 2;
            color: #777777;

            &:after {
              content: "";
              @include center_y;
              left: 160px;
              display: block;
              color: #777777;
              font-size: 35px;
              font-family: "Font Awesome 5 Pro";
            }

            &.active {
              z-index: 1;
              background-color: transparent !important;
              border: none !important;
              color: $corporate_1;

              &::before {
                opacity: 1;
              }

              &:after {
                color: $corporate_1;
                font-weight: 600;
              }
            }
          }

          &.rooms_selector, &.club_rates_viewer {
            &::before {
            }

            &:after {
              content: "\f236";
            }
          }

          &.packages_selector {
            &:after {
              content: "\f6ec";
              font-weight: 900;
              animation: jiggle ease-in-out 1500ms infinite;
              top: 25px;
            }

            &.with_number .packages_number:before {
              background: $corporate_1;
            }
          }
        }
      }

      .contTipoHabitacion .preciosHabitacion .listadoHabsTarifas .botonReservarColumn .booking-button {
        background: $corporate_1;
        @include transition(all, .5s);

        &:hover {
          background: $corporate_2;
        }
      }
    }
  }
  #packages_b1_wrapper.v2 {
    .package_element_wrapper.custom_package .package_supplements_wrapper {
      .package_supplement_title {
        color: $corporate_1;
      }

      .guest_counter_wrapper .guest_element .tag .guest_tag {
        color: $corporate_1;
      }

      .counter_wrapper .counter .plus {
        background-color: $corporate_2;
      }
    }

    .package_element_wrapper .package_prices_wrapper .perform_package_booking_search {
      background-color: $corporate_1 !important;
    }

  }
  .fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking img {
    max-height: 220px;
  }
  .rate_conditions_popup_wrapper h3.rate_name, .package_hidden_popup .popup_title {
    background: $corporate_1;
  }
  .shopping_cart_summary.v2 .full_body_wrapper .scrollable_content_wrapper .items_wrapper .item:not(:last-child) {
    border-color: $corporate_2;
  }
}

body.obh-maestro {
  $corporate_1: #262626;
  $corporate_2: #CC0000;
  $corporate_3: rgba(#EB8A23, 0.3);
  $corporate_4: #104E65;
  $corporate_5: #E3F5F5;

  $red: #EC6363;
  $green: #2CA96E;
  $black: #161C21;
  $grey-1: #333333;
  $grey-2: #444444;
  $grey-3: #F5F5F5;

  $title_family: 'Niramit', sans-serif;
  $text_family: 'Niramit', sans-serif;
  $font_1: 'Niramit', sans-serif;

  font-family: $text_family;
  color: $grey-1;

  @import "casa_maestro_booking/booking_process";

  &.booking_process_version_1 {
    .site-main .booking_engine_wrapper_process .boking_widget_inline {
      .booking_form{
        display: flex;
        align-items: center;
        justify-content: space-between;
        .wrapper_booking_button {
          display: flex;
          align-items: center;
          justify-content: space-between;
          .promocode_wrapper {
            width: 60% !important;
          }
          .spinner_wrapper {
            display: none;
          }
        }
      }
    }

    .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-datepicker .ui-widget-header .ui-datepicker-prev,
    .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-datepicker .ui-widget-header .ui-datepicker-next {
      &.ui-state-hover {
        background: transparent!important;
      }
    }
  }

  .color1 {
    color: $corporate_1;
  }

  .bgc1 {
    background-color: $corporate_1;
  }

  .remove_room_element {
    top: 25px !important;
  }

  .shopping_cart_summary.v2 {
    font-family: $text_family;

    .full_body_wrapper {
      .footer_wrapper {
        .total_text {
          width: 120px;
        }
      }
    }
  }

  @include individual_colors($corporate_1, $corporate_2);

  .cards_banners_wrapper,
  .cards_extended_wrapper {
    .card_element_wrapper {
      .card_description {
        @include ellipsis(2);
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 14px;
        margin-bottom: 14px;
      }
    }
  }

  #booking {
    &.boking_widget_inline {
      .stay_selection {
        display: inline-flex;
      }
    }
  }

  &.shopping_cart_process_v2 {
    div#step-3 {
      #personal-details-form {
        .booking_details_prices_wrapper {
          .booking-3-info--title, .tax_element {
            margin-left: -25px !important;
            margin-right: -25px !important;
          }
        }
      }
    }
  }
  &.logged_agency{
    #step-3 .booking_details_prices_wrapper .booking_details_wrapper .total_booking_wrapper .total_label small{
      font-size: 11px;
    }
  }
}

@keyframes jiggle {
  0% {
    transform: none;
  }
  12% {
    transform: rotateZ(6deg);;
  }

  25% {
    transform: rotateZ(-27deg)
  }
  37% {
    transform: rotateZ(20deg)
  }
  50% {
    transform: rotateZ(-6deg)
  }
  60% {
    transform: rotateZ(6deg)
  }
  70% {
    transform: none;
  }
  100% {
    transform: none;
  }
}
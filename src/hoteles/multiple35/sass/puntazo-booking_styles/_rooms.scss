div#step-1 {
  .contTipoHabitacion {
    padding: 0;
    margin-bottom: 30px;

    .overlay_picture_text {
      position: absolute;
      z-index: 2;
      text-align: center;
      bottom: 10px;
      left: 0;
      padding: 8px 8px 8px 30px;
      font-weight: bold;
      background: rgba($corporate_1, 0.67);
      font-size: 13px;
      width: 140px;
      top: auto;
      color: white;
      box-sizing: border-box;

      &:before {
        content: '\f236';
        position: absolute;
        top: 7px;
        left: 15px;
        font-family: 'Font Awesome 5 Pro';
        font-size: 20px;
        font-weight: 500;
      }
    }

    .contFotoDescripcion {
      padding: 0;

      .room_button_controls {
        right: 100%;

        .open_see_more_room_v2 {
          background: white !important;
          -webkit-box-shadow: -1px 0px 3px rgba(0, 0, 0, 0.2);
          -moz-box-shadow: -1px 0px 3px rgba(0, 0, 0, 0.2);
          box-shadow: -1px 0px 3px rgba(0, 0, 0, 0.2);
          cursor: pointer;

          i {
            color: $corporate_1 !important;
          }
        }
      }

      .contFotoHabitacion {
        width: 320px;
        min-height: 245px !important;

        .see_more_rooms_v2 {
          img {
            @include cover_image;
            position: static;
          }

          .lupa {
            right: auto;
            left: 14px;
            top: 15px;
            font-size: 25px;

            &:before {
              font-family: "Font Awesome 5 Pro";
              font-weight: lighter;
            }
          }

          &:hover {
            .lupa {
              font-size: 30px;
            }
          }
        }
      }

      .contDescHabitacion {
        margin: 0;
        width: 820px;
        padding-top: 10px;
        box-sizing: border-box;
        height: 245px;

        .see_more_rooms_v2 {
          bottom: 81px;
        }

        .room_description_name_wrapper {
          max-height: 120px;
        }

        .cabeceraNombreHabitacion {
          margin-bottom: 13px;

          .tipoHabitacion {
            font-family: $title_family;
            font-size: 21px;
            letter-spacing: 1.1px;
            line-height: 1;
            text-transform: capitalize;
            color: $corporate_3;
            font-weight: bold;
            //width: 414px !important;
          }
        }

        .descripcionHabitacion {
          font-family: $text_family;
          font-weight: 400;
          font-size: 15px;
          letter-spacing: 0.9px;
          line-height: 21px;
          color: $black;
          height: 65px;
        }

        .see_more_rooms_v2 {
          .plus_sign {
            color: $black;
          }

          .see_more {
            font-family: $text_family;
            font-weight: 700;
            font-size: 13px;
            letter-spacing: 1.55px;
            line-height: 1;
            color: $black;
            text-transform: uppercase;
            text-decoration: none;
          }
        }

        .room_services {
          margin: 0 auto;
          border-top: 1px solid $corporate_2;
          border-bottom: 1px solid $corporate_2;

          .service_element {
            border-right: none;
          }

          .service_element + .service_element {
            border-left: 1px solid $corporate_2;
          }
        }
      }
    }

    .preciosHabitacion {
      .listadoHabsTarifas {
        border-spacing: 0;

        td {
          height: 50px;
        }

        .regimen_tr_element {
          padding: 6px 0;

          &.regimen_for_users {
            .regimenColumn {
              .tTextoOferta {
                width: 270px;
                line-height: 16px;
              }
            }

            .precioTotalColumn {
              .promotion_percentage_square {
                margin-right: -55px;
              }
            }
          }

          &.withMarketingLogoPopup {
            .precioTotalColumn {
              width: 230px;
            }
          }
        }

        .conditions_info_wrapper {
          .last_day_cancellation_text {
            margin-left: 22px;
            color: #5CBCB9;
            background: transparent !important;
            font-style: normal;
            font-family: $title_family;
            font-size: 13px;
            letter-spacing: 0.75px;
            padding-left: 0;

            &:before {
              color: #5CBCB9;
              content: '\f00c';
              font-family: "Font Awesome 5 Pro";
              -moz-osx-font-smoothing: grayscale;
              line-height: 1;
              display: inline-block;
              font-size: 16px;
              text-rendering: auto;
              -webkit-font-smoothing: antialiased;
              vertical-align: middle;
              margin-right: 5px;
              font-weight: bold;
              padding-left: 0;
            }
          }

          .rate_conditions_link {
            font-family: $title_family;
            font-size: 12px;
            color: #161C21;
            margin-top: 6px;
            text-transform: none;
            margin-right: 30px;
            padding-right: 0;
            background: transparent;
          }
        }

        .contTitTipoTarifa {
          background: #FAFAF7;

          .advice_rate_message {
            background: transparent;
            color: #161C21;
            font-weight: 600;
            font-family: $title_family;
          }

          .rate_conditions_link {
            font-weight: 300;
            margin-right: 30px !important;
            margin-top: 3px !important;
          }

          .titTipoTarifa {
            font-family: $text_family;
            font-weight: 600;
            font-size: 14px;
            letter-spacing: 1.4px;
            color: $black;
            text-transform: initial;
            padding-left: 27px;
          }

          .cheapest_rate_message {
            background-color: $corporate_3;
            width: 350px;
            padding-top: 10px !important;
            padding-bottom: 10px !important;

            &::before {
              border-top: 19px solid #FAFAF7;
              border-right-width: 7px;
              border-left: 7px solid #FAFAF7;
            }

            .best_price_label_info {
              font-family: $text_family;
              font-weight: bold;
              font-size: 12px;
              letter-spacing: 0.6px;
              line-height: 1;
              position: relative;
            }

            &.has_conditions {
              .conditions_info_wrapper {
                bottom: 10px;
              }

              .conditions_info_wrapper a {
                color: white;
              }
            }
          }
        }

        .regimenColumn {
          font-family: $text_family;
          font-weight: 500;
          font-size: 14px;
          letter-spacing: 0.4px;
          padding-left: 27px;
          vertical-align: middle;

          .regimenColumnContent {
            position: relative;
            letter-spacing: 0.8px;

            .regimen_name_wrapper {
              color: $black;
            }
          }

          .tTextoOferta {
            font-weight: 500;
            font-size: 14px;
            color: $offer;

            span.offer_name_element {
              color: $offer;
            }
          }
        }

        .precioNocheColumn {
          width: 5%;

          .priceValues {
            display: none;
          }
        }

        .lock_board {
          .lock_board_wrapper {
            width: 90px;
            margin-left: 25px;
            margin-right: -110px;
          }
        }

        .custom_board_message {
          .message_element {
            width: 190px;
            margin-left: -110px;
            margin-right: -100px;
          }
        }

        .precioTotalColumn {
          width: 300px;
          font-family: $text_family;
          display: flex;
          justify-content: flex-end;

          .precioTachadoDiv {
            text-align: right;
            color: $offer;

            .tPrecioTachado {
              font-weight: 400;
              font-size: 11px;
              color: $offer;
            }
          }

          .precioGeneralDiv {
            .tPrecioTotal,
            .tPrecioOferta {
              font-weight: 700;
              font-size: 18px;
              letter-spacing: 1px;
            }

            .tPrecioOferta {
              color: rgb(37, 37, 37) !important;
            }
          }

          .promotion_percentage_square {
            border-radius: 50%;
            background-color: $offer !important;

            .promotion_discount {
              background: $offer;
            }
          }

          .priceTitle {
            font-weight: 400;
            font-size: 9px;
            color: $grey;
            line-height: 8px;
          }
        }

        .botonReservarColumn {
          button {
            width: 185px;
            height: 44px;
            border-radius: 50px;
            font-family: $title_family;
            font-size: 18px;
            letter-spacing: 1.9px;
            font-weight: bold;
            padding: 0;
            margin-top: -5px;
            transition: background-color .5s;

            &:hover {
              background: $corporate_2;
              color: white;
            }
          }
        }
      }
    }
  }
}

.popup_see_more_rooms_second {
  .room_popup_individual_element {
    @include display_flex(nowrap);
    flex-direction: column;
    overflow: hidden;

    .close_button_element {
      display: block;
      width: 50px;
      line-height: 50px;
      top: 0;
      right: 0;
      background-color: $corporate_3;
      color: white;
    }

    .popup_title {
      position: static;
      background: none;
      font-family: $title_family;
      font-weight: 700;
      font-size: 22px;
      letter-spacing: 1.1px;
      line-height: 1;
      color: $corporate_3;
      order: 2;
    }

    .popup_carousel {
      order: 1;

      .exceded {
        padding: 0;

        .popup_image {
          @include cover_image;
          position: static;
        }
      }
    }

    .popup_room_pictures {
      top: 380px;
      background: none;

      .popup_room_pictures_next {
        color: $corporate_2;
      }
    }

    .room_services {
      order: 3;
      background: none;

      .service_element {
        border-right-color: $corporate_2;
      }
    }

    .popup_room_description {
      font-family: $text_family;
      font-weight: 500;
      font-size: 15px;
      letter-spacing: 0.9px;
      line-height: 21px;
      color: $black;
      order: 4;
      height: 380px;
      overflow: scroll;

      strong {
        font-weight: bold;
      }
    }
  }
}


.room_popup_individual_element .room_services,
div#step-1 .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_services {
  padding: 0 !important;
  border-top-width: 1px;
  border-bottom-width: 1px;
  background: transparent;
  display: flex !important;

  .service_element {
    height: auto !important;

    i {
      color: $corporate_2;
      font-size: 25px;
      display: block;
      padding-bottom: 0;
      margin-right: 0;
    }

    .service_image {
      display: block !important;
      margin: auto !important;
    }

    .service_description {
      font-family: $text_family;
      font-weight: 500;
      font-size: 12px;
      letter-spacing: 0.7px;
      line-height: 1;
      color: $black;
    }
  }
}

.room_popup_individual_element {
  .service_description {
    font-size: 11px !important;
  }

  .room_services {
    border-top: 1px solid #5CBCB9;
    border-bottom: 1px solid #5CBCB9;
  }
}

div#step-1 .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_services {
  margin-bottom: 9px;
}

.rate_conditions_popup_wrapper {
  .rate_name {
    font-family: $text_family;
  }

  .rate_description_content {
    font-weight: 300;
    font-family: $text_family;
  }
}

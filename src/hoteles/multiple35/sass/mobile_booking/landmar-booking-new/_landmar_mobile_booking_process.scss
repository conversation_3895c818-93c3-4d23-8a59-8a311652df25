h1 {
  font-family: $text_family;
  color: $black;
}

header {
  #sideMenu.side_menu_wrapper {
    .logo-wrapper {
      width: 165px;
      height: 60px;
      background-image: url(https://cdn2.paraty.es/landmar-gigantes/images/883c16afc5c09e1);
      background-size: contain;
      background-repeat: no-repeat;

      img {
        display: none;
      }
    }

    .ticks_wrapper {
      margin-top: 70px;
    }

    .social_networks_wrapper {
      position: absolute;
      top: 0;
      left: 10px;
    }
  }
}

footer {
  .row:first-of-type {
    display: block;
    text-align: center;
  }

  .logo_wrapper {
    display: block;
    margin: 0 auto 15px;
    width: 165px;
    height: 60px;
    background-image: url(https://cdn2.paraty.es/landmar-gigantes/images/883c16afc5c09e1);
    background-size: contain;
    background-repeat: no-repeat;

    img {
      display: none;
    }
  }

  .info {
    display: block;
    width: 100%;
  }
}

.booking_widget_wrapper {
  .has_pad {
    padding: 10px;
  }

  #entry_date_popup, #departure_date_popup {
    .header_wrapper {
      .banner_title {
        i {
          color: $corporate_1;
        }
      }
    }

    .menu_controller {
      .dates_button {
        height: 52px;
      }
    }

    .start_datepicker,
    .end_datepicker {
      * {
        font-family: $text_family;
      }
    }
  }
}

.booking_widget_wrapper .double_button_wrapper {
  display: block;

  .submit_button, .close_button {
    display: block;
    width: 100%;
  }

  .close_button {
    background: $corporate_2;
    color: white;
    border: 1px solid $corporate_2;
  }
}

.modify_search {
  background-color: white;
  color: $corporate_2;
  border: 1px solid $corporate_2;
  margin: 5px;
  border-radius: 50px;
}

.btn.add_service {
  border-radius: 0;
  background-color: $corporate_1;
}

.continue_booking,
.back_booking {
  border-radius: 50px;
  background: $corporate_2;
}

.show_calendar, .back_button {
  background-color: white;
  color: $corporate_2;
  border: 1px solid $corporate_2;
  margin: 5px;
  border-radius: 50px;
}

.promocode_error {
  color: $red;
  padding: 10px;
}

.main_content_wrapper.step_-1 {
  .booking_0_buttons_wrapper {
    font-family: $text-family !important;

    .hotels_found_wrapper {
      width: 45%;

      span {
        font-family: $text-family;
      }
    }

    .bottom_content_wrapper {
      .filter_selector_wrapper {
        display: none;
      }

      .order_selector_wrapper {
        .selector {
          .scroll_wrapper {
            .option_wrapper {
              &.distance, &.city, &.country, &.brand {
                display: none;
              }
            }
          }

          .footer {
            .btn_show_results {
              background: $corporate_1 !important;
              border-radius: 50px !important;
              font-family: $text-family !important;
              font-size: 16px;
              font-weight: 600;
              color: white;
              height: 50px;
              width: auto;
              padding: 0 30px;
            }
          }
        }
      }
    }
  }

  .booking_0_hotels_wrapper {
    .hotels {
      .hotel_element {
        .hotel_picture_wrapper {
          .hotel_price {
            flex-direction: column;
            align-items: flex-start;

            .price_title {
              display: flex;
              align-items: flex-end;

              .hotel_promotion {
                color: $red;
                position: relative;
                margin-left: 7px;

                .hotel_price_promotion span {
                  font-size: 16px;
                }

                &::before {
                  content: "";
                  position: absolute;
                  top: 50%;
                  left: -3px;
                  right: -3px;
                  background: $red;
                  height: 1px;
                }
              }
            }

            .hotel_price_regimen_name {
              display: none;
            }
          }
        }

        .content_wrapper {
          font-family: $text-family;

          .hotel_desc_wrapper {
            .hotel_name {
              font-family: $text-family;
              color: $corporate_2;
              font-weight: bold;
            }
          }

          .hotel_booking_wrapper {
            .booking_content {
              .booking_button_mobile {
                background: $corporate_1 !important;
                border-radius: 50px !important;
                font-family: $text-family !important;
                font-size: 16px;
                font-weight: 600;
                color: white;
                height: 50px;
                width: auto;
                padding: 0 30px;
              }
            }
          }
        }
      }
    }
  }
}

.hotel_popup {
  .hotel_desc_wrapper {
    font-family: $text_family;

    .hotel_name {
      font-family: $text_family;
      color: $corporate_2;
      font-weight: bold;
    }

    .services_wrapper {
      li {
        i {
          color: $corporate_1;
        }
      }
    }
  }
}

.main_content_wrapper.step_0 {
  .room_list {
    .room_pack_option {
      .tour_button_wrapper {
        z-index: 3;
        background: $corporate_2;
        padding: 4px 45px 4px 11px;

        &::before {
          content: "\ea3c";
          position: absolute;
          right: 10px;
          top: 0;
          font-family: 'icomoon';
          font-size: 23px;
        }
      }

      .room_services.owl-carousel {
        padding: 10px;

        .service_element {
          display: flex;

          i {
            margin-right: 5px;
          }
        }
      }
    }
  }

  + footer {
    padding-bottom: 100px;
  }
}

.main_content_wrapper.step_0 div.room_list div.room_pack_option div.rates_details_wrapper {
  .rate_selected_title {
    font-size: .8rem;
    padding: 9px;

    span {
      color: $black;
    }
  }

  div.regime_item_content {
    div.prices_options.no_promo,
    div.prices_options {
      p.final_price {
        color: $black;
      }

      .price_through {
        color: $corporate_2;

        .tPrecioTachado {
          position: relative;
          float: none;
        }
      }
    }

    .regime_description {
      .regime_offer_detail {
        color: $corporate_2;

        .room_list_name_offer {
          .exceded {
            width: 180px;
          }
        }
      }

      .discount_percentage {
        background-color: $corporate_2;
      }
    }

    .regime_price_wrapper {
      div.submit {
        span {
          white-space: nowrap;
          display: flex;
          justify-content: center;
          border-radius: 50px;
        }
      }
    }
  }
}

.main_content_wrapper.step_0 {
  .buttons_bottom {
    bottom: 95px;
  }

  .tabs_wrapper {
    .tabs {
      li {
        background-color: $corporate_2;

        &.active {
          //color: $black;
          border-color: $corporate_2;

          a {
            color: white;
          }
        }

        .tab_btn {
          background-color: $corporate_2;
        }
      }

      li + li {
        &::before {
          background-color: $corporate_2;
        }
      }
    }
  }

  .login_wrapper_element {
    .logo_wrapper {
      img {
        image-rendering: pixelated;
      }
    }
  }
}

.main_content_wrapper.step_2 {
  .reservation_summary {
    .option_selected {
      .details {
        .rate {
          .conditions {
            color: $corporate_3;
          }
        }
      }

      .price {
        width: auto;
      }
    }
  }

  .personal_details_form_wrapper {
    .personal_details_form {
      .bottom_button_wrapper {
        #btn-finish-booking {
          border-radius: 50px;
          background: $corporate-1;
        }
      }

      .inputs_wrapper {

        #birthdate {
          margin-top: 5px;

          label {
            font-size: 12px;
            margin-left: 17px;
          }

          #birthdate_inputs_container {
            display: flex;
            flex-wrap: wrap;

            select {
              width: 100px;

              &#birthDay {
                order: 1;
              }

              &#birthMonth {
                order: 2;
              }

              &#birthYear {
                order: 3;
              }
            }

            label.error {
              width: 100px;
              display: block;
              margin: 0;

              &[for="birthDay"] {
                order: 4;
              }

              &[for="birthMonth"] {
                order: 5;
              }

              &[for="birthYear"] {
                order: 6;
              }
            }
          }
        }
      }

      .input_element {
        label.error {
          color: $red !important;
        }
      }

      .inputs_wrapper .input_element.with_label {
        label {
          color: #383838;
        }

        .form_control {
          border: 1px solid #c5c5c5;
        }

        .selected_prefix {
          display: none;
        }

        &.telephone {
          label {
            z-index: 1;
          }

          .form_control {
            &[name='telephone'] {
              display: inline-block;
              width: calc(100% - 105px);
              position: absolute;
              top: -2px;
              right: 0;
              margin-bottom: 0;
            }
          }

          .select2.select2-container {
            width: 100px !important;
            margin-top: -3px;
            font-size: 13px;

            .selection .select2-selection.select2-selection--single {
              height: 33px;
              margin-top: 2px;
            }
          }
        }
      }

      .billing_adress_wrapper {
        display: none;
      }
    }

    .payment_gateway {
      .summary_section_title {
        margin-bottom: 10px;
      }
      .payments_wrapper {
        .payment_type_element_wrapper {
          padding-top: 15px;

          label.error {
            position: absolute;
            top: -5px;
          }
        }
      }
    }
  }
}

.select2-container.select2-container--default.select2-container--open {
  min-width: 100%;
}


.owl-carousel.custom_carousel .owl-item .price_label {
  background-color: $corporate_1;
}

#departure_date_popup {
  &.dates_popup.active {
    .ui-datepicker-title {
      font-family: $text_family !important;
    }

    .ui-datepicker-calendar {
      font-family: $text_family !important;
    }
  }

  .end_datepicker * {
    font-family: $text_family !important;
  }
}

.share_links_wrapper {
  .share_links_prev {
    background-color: $corporate_1;
  }

  .share_links_cont .share_link {
    background-color: $corporate_1;
  }
}

.sectoption {
  width: calc(100% - 30px);
  margin: 0 auto 20px;
  border-color: $corporate_1;

  .titSelOpt {
    color: $corporate_1;
  }

  input[type='radio']:checked::before {
    background-color: $corporate_1;
  }
}

.tabs_wrapper {
  .club_logo_image {
    display: none;
  }

  .tab_text {
    font-size: 11px;
  }
}

.cards_banners_wrapper {
  .card_element_wrapper.only_image {
    display: inline-block;
    transform: none;
    padding: 10px;
    background-color: $corporate_2 !important;
    border-radius: 20px !important;
    color: white;
    text-align: center;

    .card_image_wrapper {
      width: 50px;
      height: 50px;
      margin: 0 auto 5px;

      .card_image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }

    .card_description {
      display: block !important;
      max-width: 100px;
      margin-bottom: 5px;
      font-size: 11px;

    }

    .promocodelink {

    }
  }

  .card_element_wrapper.only_image + .close_card_button {
    top: 0;
    right: 5px;
  }
}

.hotels_availability_wrapper {
  .choice_element {
    .hotels_carousel_wrapper {
      .hotels_list {
        .hotel_element {
          .product_details {
            .hotel_submit {
              background-color: $corporate_2;
            }
          }
        }
      }
    }
  }
}

.additional_services {
  .perform_additional_services_booking {
    background: $corporate_1;
    border-radius: 50px;
  }

  .all_additional_services_wrapper.with_tabs_scroll {
    .additional_services_tabs_wrapper {
      .additional_services_tabs_list {
        .additional_services_tab {
          &.active {
            border-color: $corporate-2;

            .main_title {
              color: $corporate-2;
            }
          }
        }
      }
    }

    .category_wrapper {
      .title_category {
        .main_title {
          color: $corporate-2;
        }
      }

      .additional_services_wrapper {
        .additional_service_element {
          .price_tag {
            background: $corporate_2;
          }

          .supplement_complete_content {
            .service_selection_wrapper {
              .service_select {
                .nights_selection {
                  .button-selection {
                    background: $corporate_2;

                    span {
                      top: 45%;
                    }
                  }
                }

                .add_service_button {
                  background: $corporate_2;
                }
              }
            }
          }

          &.selected {
            &::before {
              background: $corporate_2;
            }
          }
        }
      }
    }
  }
}

.button.load_hotelverse {
  display: none;
}

#step-3.gift_bono.mobile {
  .booking_details_prices_wrapper {
    .gift_wrapper {
      height: 125px !important;
      width: 55% !important;

      .title_bono {
        font-size: 12px;
      }

      .total_bono {
        top: unset;
        left: unset;
        display: block;
      }
    }
  }

  .payment_type_grid {
    .payments_wrapper {
      display: flex !important;
      justify-content: space-around;
      flex-wrap: wrap;
      min-height: 100px;

      .payment_option_element {
        contain: none !important;

        .icon_pasarela_pago {
          position: absolute;
          bottom: -30px;
        }

        .bizum_text {
          display: block;
        }
      }
    }
  }
}

#main_modal {
  .rooms_features_modal_wrapper {
    .body_modal_content {
      .room_pictures_thumbnail_modal {
        .owl-item.selected {
          border-bottom-color: $corporate-1;
        }
      }

      .room_content {
        .icons_room {
          .room_services.owl-carousel {
            display: block !important;

            .service_element {
              width: auto !important;
              margin-right: 15px;
            }
          }
        }
      }
    }
  }
}

.tour_video_wrapper {
  iframe {
    width: 100% !important;
  }
}


@import "hotelverse_popup_mobile";
@import "club-styles/_club_styles_mobile";
@import "b0_maps_v2_mobile";
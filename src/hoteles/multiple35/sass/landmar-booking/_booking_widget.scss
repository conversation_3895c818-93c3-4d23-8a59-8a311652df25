div#full_wrapper_booking {
  width: 100%;
  min-width: 1140px;
  padding: 0;
  background-color: white !important;
  color: $corporate_2;
  height: 90px;
  border-bottom: 1px solid #80808042;
  margin: 10px 0 0 0;

  .boking_widget_inline {
    .booking_form {
      width: 1140px;
      height: 60px;
      margin: 0 auto;
      position: relative;

      .entry_date_label,
      .departure_date_label,
      .guest_selector label,
      .children_label,
      .babies_selector label,
      .rooms_label,
      .adults_label,
      .promocode_label,
      .promocode_input {
        font-family: $text_family;
        font-weight: 300;
        font-size: 16px;
        letter-spacing: 1px;
        color: $corporate_2 !important;
      }

      .adults_label,
      .children_label,
      .babies_selector label {
        color: $grey-1 !important;
      }

      .promocode_label {
        font-size: 14px;
      }

      .entry_date_label,
      .departure_date_label,
      .guest_selector label {
        //display: none;
      }

      .entry_date_wrapper,
      .departure_date_wrapper,
      .guest_selector {
        display: inline-flex;
        flex-direction: column;
        justify-content: space-evenly;
        align-items: stretch;
        background: none;
      }

      .adults_label,
      .children_label,
      .babies_selector label {
        text-transform: none;
      }


      .stay_selection .date_day,
      .guest_selector .placeholder_text {
        font-family: $text_family;
        font-weight: 500;
        font-size: 23px !important;
        letter-spacing: 1px;
        line-height: 20px;
        color: $corporate_2;
      }

      .stay_selection .departure_date_wrapper,
      .guest_selector {
        &::after {
          content: '';
          display: block;
          @include center_y;
          background: #9f959559;
          top: 60%;
          height: 60px;
          left: 335px;
          width: 1px;
        }
      }

      .stay_selection {
        width: 320px;
        height: 100%;

        .entry_date_wrapper,
        .departure_date_wrapper {
          border-bottom: none;
          height: 100%;

          .date_box {
            margin-top: 0;
          }
        }

        .entry_date_wrapper {
          padding: 15px 15px 15px 0;

          &::after {
            content: '\f105';
            font-family: "Font Awesome 5 Pro";
            font-weight: 300;
            font-size: 35px;
            @include center_y;
            top: 55%;
            right: -5px;
            color: #E4B594;
            -webkit-font-smoothing: antialiased;
          }
        }

        .departure_date_wrapper {
          padding: 15px 0 15px 57%;

          &::after {
            right: 0;
          }

          .date_box {
            background: none;
          }
        }
      }

      .guest_selector {
        width: 235px;
        height: 100%;
        padding: 10px 0 0 40px;
        margin: 0;

        &::after {
          left: 190px;
          top: 70%;
        }

        .placeholder_text {
          margin-top: 0;
          letter-spacing: 0;
          text-align: left;
          text-transform: lowercase;

          span {
            display: none;
            font-size: 15px;
            letter-spacing: 0.5px;
          }
        }

        b.button {
          display: none;
        }
      }

      .room_list_wrapper {
        top: 140%;
        width: 370px;
        left: 220px;

        &:lang(en) {
          width: 400px;

          .adults_selector {
            width: 40% !important;
          }

          .children_selector {
            width: 60% !important;
          }
        }
      }

      .wrapper_booking_button {
        width: 585px;
        height: 100%;

        .promocode_wrapper {
          width: 200px !important;
          padding: 30px 10px 20px 10px !important;

          .promocode_input {
            outline-color: $corporate_1;
            text-transform: uppercase;
            margin-top: 10px;

            &:focus {
              outline: none;
            }

            &::placeholder {
              visibility: hidden;
              font-size: 13px;
              color: white;
              opacity: 0.7;
            }

            &::-webkit-input-placeholder {
              visibility: hidden;
              font-size: 13px;
              color: white;
              opacity: 0.7;
            }
          }
        }

        .modify-calendar-button {
          box-sizing: border-box;
          width: 200px;
          float: left;
          margin: calc((60px - 42px) / 2) 0; // 60px - full widget height, 42px - button height
          margin-right: 10px;
          position: relative;
          display: inline-flex;
          padding: 0 10px;
          align-items: center;
          font-family: $text_family;
          font-weight: 700;
          font-size: 14px;
          letter-spacing: 1.15px;
          line-height: 18px;
          text-align: center;
          height: 42px;
          text-transform: uppercase;
          background: transparent;
          border: 1px solid white;
          color: white;
          @include transition(all, 0.5s);

          &:lang(es) {
            padding: 0 20px;
          }

          &::before {
            content: none;
          }

          &:hover {
            background: $corporate_2;
            color: white;

            &::before {
              color: white;
            }
          }
        }

        button.submit_button {
          position: relative;
          width: 170px;
          height: 42px;
          margin: calc((60px - 42px) / 2) 0; // 60px - full widget height, 42px - button height
          padding: 0 10px 0 50px;
          font-family: $text_family;
          font-weight: 700;
          font-size: 18px;
          text-align: center;
          letter-spacing: 1.15px;
          line-height: 22px;

          &:before {
            position: absolute;
            top: 50%;
            left: 40px;
            transform: translateY(-50%);
            content: "\f0e2";
            font-family: "Font Awesome 5 Pro";
            font-weight: 300;
            font-size: 26px;
            color: white;
          }
        }

        .spinner_wrapper {
          width: 235px;
          height: 60px;
          background-color: rgba($corporate_1, 0.5);
          top: calc((60px - 42px) / 2);
          right: 0;
          border-radius: 10px;
        }
      }
    }
  }
}

div#full_wrapper_booking:not(.booking_widget_step_0) {
  .boking_widget_inline {
    .booking_form {
      .stay_selection {
        width: 460px;
      }

      .guest_selector {
        width: 200px !important;
      }

      .wrapper_booking_button {
        width: 620px !important;

        .modify-calendar-button {
          display: none;
        }

        .promocode_wrapper {
          width: calc(100% - 235px) !important;
        }
      }
    }
  }
}

#full_wrapper_booking.has_babies {
  .boking_widget_inline {
    .booking_form {
      .entry_date_label,
      .departure_date_label,
      .guest_selector label,
      .children_label,
      .babies_selector label,
      .rooms_label,
      .adults_label,
      .promocode_label,
      .promocode_input {
        font-size: 13px;
      }

      .stay_selection {
        width: 320px !important;
      }

      .room_list_wrapper {
        color: $grey-1;

        label {
          height: 25px;
          letter-spacing: 1.65px;
        }

        .room_list {
          .room {
            padding: 10px 0;
            margin-bottom: 15px;
            background-color: $grey-3;

            .adults_selector,
            .children_selector,
            .babies_selector {
              background: none;
              border: none;
              padding: 0 25px;
              width: calc(100% / 2) !important;
            }

            .adults_selector {
              border-right: 1px solid rgba(black, 0.2);
            }
          }

          .room.room_with_babies {
            .adults_selector,
            .children_selector,
            .babies_selector {
              width: calc(100% / 3) !important;
            }

            .children_selector.range_label_enabled,
            .babies_selector {
              .range-age {
                display: block;
                font-size: 10px;
              }
            }

            .children_selector {
              border-right: 1px solid rgba(black, 0.2);
            }
          }

          .full_ages_wrapper {
            padding: 0;
            margin-bottom: 20px;

            .kids_age_selection,
            .babies_age_selection {
              justify-content: flex-start;
              align-items: center;

              .kid_age_element_wrapper,
              .baby_age_element_wrapper {
                position: relative;
                width: calc((100% - (15px * 3)) / 4) !important;
                padding: 10px 30px 10px 15px;
                border: 1px solid rgba(black, 0.2);
                border-radius: 10px;
                box-sizing: border-box;

                &::before {
                  content: '\f107';
                  @include center_y;
                  right: 10px;
                  font-family: "Font Awesome 5 Pro";
                  font-weight: 300;
                  font-size: 25px;
                  color: $grey-2;
                  -webkit-font-smoothing: antialiased;
                }

                .selectricWrapper {
                  margin: 0;

                  .selectric {
                    margin: 0;

                    .label {
                      margin: 0;
                    }
                  }
                }
              }

              .kid_age_element_wrapper + .kid_age_element_wrapper,
              .baby_age_element_wrapper + .baby_age_element_wrapper {
                margin-left: 15px;
              }
            }
          }
        }
      }

      .guest_selector {
        width: 200px !important;

        label {
          margin-bottom: 0;
        }
      }

      .wrapper_booking_button {
        width: 600px !important;

        .promocode_wrapper {
          width: 140px !important;
        }

        .modify-calendar-button {
          width: 215px;
          height: 60px;
          color: $corporate_2;
          border: 1px solid #9f959559;
          border-radius: 10px;
        }

        button.submit_button {
          width: 235px !important;
          float: left !important;
          height: 60px;
          text-align: center;
          background: $corporate_2;
          border: 1px solid #9f959559;
          color: white;
          border-radius: 10px;
        }
      }
    }
  }
}

&.landmar .datepicker_wrapper_element .specific_month_selector{
  display: none !important;
}
div#full_wrapper_booking {
  width: 100%;
  min-width: 1140px;
  border-top: 1px solid $corporate_2;
  border-bottom: 1px solid $corporate_2;
  padding: 0;
  color: $corporate_2;

  .boking_widget_inline {
    .booking_form {
      width: 1140px;
      height: 90px;
      margin: 0 auto;
      position: relative;

      .entry_date_label,
      .departure_date_label,
      .guest_selector label,
      .children_label,
      .rooms_label,
      .adults_label,
      .promocode_label,
      .promocode_input {
        font-family: $text_family;
        font-weight: 300;
        font-size: 13px;
        letter-spacing: 1.3px;
        line-height: 20px;
      }

      .entry_date_label,
      .departure_date_label,
      .guest_selector label {
        margin-bottom: 2px;
        margin-top: 7px;
        color: #888888 !important;
        text-transform: initial;
      }

      .stay_selection .date_day,
      .guest_selector .placeholder_text {
        font-family: $title_family;
        font-weight: 400;
        font-size: 20px !important;
        letter-spacing: 1.2px;
        line-height: 25px;
        color: $black;
      }

      .stay_selection .departure_date_wrapper,
      .guest_selector {
        &::after {
          content: '';
          display: block;
          @include center_y;
          height: 50px;
          width: 1px;
          background: $corporate_2;
        }
      }

      .stay_selection {
        width: 345px !important;
        height: 100%;

        .entry_date_wrapper,
        .departure_date_wrapper {
          border-bottom: none;
          height: 100%;
        }

        .entry_date_wrapper {
          padding: 15px 15px;
          width: 40%;

          &::after {
            content: '\f054';
            font-family: "Font Awesome 5 Pro";
            font-size: 20px;
            @include center_y;
            right: -11px;
            color: $corporate_2;
            -webkit-font-smoothing: antialiased;
            font-weight: lighter;
          }
        }

        .departure_date_wrapper {
          padding: 15px 15px 15px 58%;

          &::after {
            right: 0;
          }

          .date_box {
            background: none;
          }
        }
      }

      .guest_selector {
        width: 345px !important;
        height: 100%;
        padding: 15px 15px;
        margin: 0;

        &::after {
          right: 0px;
        }
      }

      .room_list_wrapper {
        top: 100%;
      }

      .wrapper_booking_button {
        width: 450px !important;
        height: 100%;

        .promocode_wrapper {
          width: calc(100% - 275px);
          padding: 25px 10px; // 90px - full widget height, 20px - promocode label line height

          .promocode_label {
            color: $black;
          }

          .promocode_input {
            color: $black;
            outline-color: $corporate_1;
          }
        }

        button.submit_button {
          position: relative;
          width: 275px;
          height: 64px;
          margin: calc((90px - 64px) / 2) 0; // 90px - full widget height, 64px - button height
          padding: 0 60px 0 80px;
          font-family: $title_family;
          font-weight: 500;
          font-size: 17px;
          letter-spacing: 0.85px;
          line-height: 22px;

          &::before {
            content: '\f0e2';
            font-family: "Font Awesome 5 Pro";
            font-weight: 300;
            font-size: 25px;
            @include center_y;
            left: 30px;
            -webkit-font-smoothing: antialiased;
          }

          &:hover {
            background-color: $corporate_2;
          }
        }

        .spinner_wrapper {
          width: 247px;
          height: 64px;
          background-color: transparent;
          top: calc((90px - 64px) / 2);
          right: 0;
        }
      }
    }
  }

  &.has_babies {
    .spinner_wrapper {
      width: 247px;
    }
  }
}
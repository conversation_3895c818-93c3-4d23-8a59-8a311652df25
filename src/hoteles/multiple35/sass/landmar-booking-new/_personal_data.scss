div#step-3 {
  #personal-details-form {
    .personal_details_payment_wrapper, .booking_details_prices_wrapper {
      box-shadow: 0px 3px 40px #00000014;
    }

    .personal_details_payment_wrapper {
      .personal_details_grid {
        .booking-box--form {
          .booking-box__content {
            .booking-form-field {
              label {
                font-size: 13px;
                top: -6px;
              }


              .select2-container {
                .selection {
                  .select2-selection {
                    .select2-selection__rendered {
                      padding-left: 10px;
                    }
                  }
                }
              }

              textarea {
                padding: 15px 12px;
              }
            }
          }
        }

        .billing_adress_wrapper {
          display: none;
        }
      }
      .payment_type_grid {
        .booking-box--form {
          .booking-box__content {
            .clearfix.booking-3-agree.booking-form-field.agreee_checked {
              label {
                &.error {
                  display: block;
                }
              }
            }
          }
        }
      }
    }

    .booking_details_prices_wrapper {
      padding: 5px 25px;

      .booking_3_hotel_name {
        font-family: $text-family;
        padding: 0;
      }

      .booking3_amount_info {
        .total_booking_wrapper {
          left: -25px;
          right: -25px;
        }
      }

      .booking-button {
        font-family: $text-family;
        font-size: 18px;
        font-weight: bold;
        overflow: hidden;

        &::before {
          border-radius: 50px;
        }
      }
    }
  }

  .booking-form-field {
    display: table;
    width: 100%;

    input, textarea {
      font-family: $text_family;

      &::placeholder {
        font-family: $text_family;
      }
    }
    .checkbox_additional {
      label {
        display: contents;
      }
    }
    &.clearfix.booking-3-agree.agreee_checked {
      input {
        position: sticky;
      }
    }
  }
}

.mail_wrapper {
  font-family: $text_family !important;

  &:lang(fr) {
    .title_message {
      font-size: 19px !important;
    }
  }
}
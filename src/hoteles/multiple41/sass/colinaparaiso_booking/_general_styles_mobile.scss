.booking_widget_wrapper {
  .input_wrapper {
    &::before {
      background-color: $corporate_1;
    }
  }

  .re-search_button {
    color: white;
  }
}

.continue_booking,
.back_booking {
  background-color: $corporate_1;
}

.continue_booking,
.back_booking,
.btn.add_service {
  border-radius: 0 !important;
}

.main_content_wrapper.wizard {
  .booking_steps_wrapper {
    .booking_step_sentence {
      font-weight: 300;
      letter-spacing: 1px;
      color: $black;
    }
  }

  .search-resume-wrapper {
    .dates,
    .search_text {
      font-size: 14px;
      letter-spacing: 1px;
      line-height: 18px;
      color: $black;
    }
  }
}

.double_button_wrapper {
  button {
    margin: 5px;
    border-color: $corporate_1;
    color: $corporate_1;
    font-weight: 700;
    font-size: 11px;
    letter-spacing: 0.75px;

    &.btn_secondary {
      color: white;
    }

    &.modify_search {
      background: $corporate_1;
      color: white;
      border: 0;
    }
  }
}

.main_content_wrapper {
  &.step_0 {
    .tabs_wrapper {
      .tabs {
        li {
          border-radius: 0;

          .tab_btn {
            background-color: $corporate_1;
            border-radius: 0;
            font-size: 12px;

            &.active {
              background-color: white;
              color: $black;
            }
          }

          .tab_btn.packages {
            .tab_text {
              font-weight: 700;
              text-transform: uppercase;
            }
          }
        }

        li.active {
          &::before {
            left: -22px;
          }
        }
      }
    }

    .room_list {
      .room_pack_option {
        .just_booking {
          background: $corporate_1;
        }

        .room_name.content_title {
          .title {
            font-family: $title_family;
            font-weight: 700;
            color: $black;
          }

          .info_btn {
            color: $black;

            &::before {
              border-color: $black;
            }
          }
        }

        .rates_details_wrapper {
          .rate_selected_title {
            span {
              font-size: 11px;
              color: $black;
            }
          }

          .regime_item_content {
            .discount_percentage {
              background-color: $red;
            }

            .regime_description {
              .regime_offer_detail {
                color: $black;
              }
            }

            .prices_options {
              .final_price {
                color: $black;
              }
            }

            .prices_options {
              .price_through {
                color: $red;
              }
            }

            .regime_price_wrapper {
              div.submit {
                span {
                  border-radius: 27px;
                }
              }
            }
          }
        }
      }
    }
  }

  &.step_2 {
    .option_selected {
      .price {
        color: $black !important;
      }
    }

    #btn-finish-booking {
      border-radius: 27px !important;
    }
  }
}

.shopping_cart_summary.v2.mobile {
  font-family: $text_family;

  .full_body_wrapper .scrollable_content_wrapper .items_wrapper .item:not(:last-child) {
    border-color: $corporate_2;
  }

  .start_wrapper .book_button {
    background-color: $corporate_1;
  }
}

&.shopping_cart_process_v2 .booking_details_wrapper {
  .book_type_wrapper {
    background: $corporate_1;
  }

  .rooms_breakdown_wrapper .room_selected_element .room_details_wrapper .rate_name {
    color: $corporate_1;
  }
}

.hotels_availability_wrapper .choice_element .hotels_carousel_wrapper .hotels_list .hotel_element .product_details .flexible_days {
  color: $corporate_2;
}

.share_links_wrapper {
  .share_links_prev {
    background-color: $corporate_2;
  }

  .share_links_cont .share_link {
    background-color: $corporate_2;
  }
}

#main_modal {
  &.active {
    .body_modal {
      &.rooms_features_modal_wrapper {
        .body_modal_content {
          overflow: scroll;
          .modal_container {
            &.room_content {
              height: 43%;

              .content_title {
                .title {
                  font-size: 20px;
                }
              }

              .icons_room {
                .room_services {
                  .service_element {
                    i {
                      font-weight: 400;
                    }
                  }
                }
              }

              .room_description {
                font-size: 15px !important;
                font-weight: 400 !important;
              }
            }
          }
        }
      }
    }
  }
}

.booking_widget_wrapper {
  .occupancy_popup {
    top: 0;
  }
}

.flexible_dates_wrapper.active {
  bottom: 35px;
}

/* Safari (from 6.1 to 10.0) */
@media screen and (min-color-index:0) and(-webkit-min-device-pixel-ratio:0) {
  .flexible_dates_wrapper.active {
    bottom: 0;
  }
}

/* Safari (10.1+) */
@media not all and (min-resolution:.001dpcm) {
  .flexible_dates_wrapper.active {
    bottom: 0;
  }
}
.double_button_wrapper button.show_calendar {
  background-color: white;
}

@mixin individual_colors($corporate_1, $corporate_2) {
  .color1 {
    color: $corporate_2;
  }
  .bgc1 {
    background-color: $corporate_2;
  }
  #full_wrapper_booking {
    .boking_widget_inline .booking_form .wrapper_booking_button {
      .promocode_wrapper {
        width: 34% !important;
        height: 100%;
        padding: 25px 10px !important;

        .promocode-imput {
          height: 60px;
          border-radius: 20px;
          border: solid 2px $corporate_1;
        }
      }

      .modify-calendar-button:hover {
        background: $corporate_2;
      }

      .submit_button {
        background-color: $corporate_1;
      }
    }
  }
  .fancybox-overlay .room_popup_individual_element .room_services .service_element i, div#step-1 .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_services .service_element i {
    color: $corporate_1;
  }
  .share_links_wrapper {
    .share_links_prev {
      background-color: $corporate_2;
    }

    .share_links_cont .share_link {
      background-color: $corporate_2;
    }
  }

  #wizard {
    div#step-1 {
      .rooms_packages_selectors_wrapper.has_packages {
        &:before {
          display:none;
        }
        .button_package_room {
          background-color: transparent;
          border: none;
          padding-left: 225px;
          text-align: left;

          &.rooms_selector,
          &.packages_selector,
          &.club_rates_viewer {
            z-index: 2;
            color: #777777;

            &::before {
              position: absolute;
              content: '';
              top: 0;
              left: 0;
              pointer-events: none;
              right: 0;
              height: 75px;
              width: 1130px;
              background-image: url(https://storage.googleapis.com/cdn.paraty.es/mucura/files/tab_left_active.svg);
              background-repeat: no-repeat;
              background-size: contain;
              opacity: 0;
            }

            &:after {
              content: "";
              @include center_y;
              left: 160px;
              display: block;
              color: #777777;
              font-size: 35px;
              font-family: "Font Awesome 5 Pro";
            }

            &.active {
              z-index: 1;
              background-color: transparent !important;
              border: none !important;
              color: $corporate_1;

              &::before {
                opacity: 1;
              }

              &:after {
                color: $corporate_1;
                font-weight: 600;
              }
            }
          }

          &.rooms_selector, &.club_rates_viewer {
            &::before {
            }

            &:after {
              content: "\f236";
            }
          }

          &.packages_selector {
            &::before {
              background-image: url(https://storage.googleapis.com/cdn.paraty.es/mucura/files/tab_right_active.svg);
              transform: translateX(-50%);
            }

            &:after {
              content: "\f6ec";
              font-weight: 900;
              animation: jiggle ease-in-out 1500ms infinite;
              top: 25px;
            }

            &.with_number .packages_number:before {
              background: $corporate_1;
            }
          }
        }
      }
    }
  }

  #packages_b1_wrapper.v2 {
    .package_element_wrapper.custom_package .package_supplements_wrapper {
      .package_supplement_title {
        color: $corporate_1;
      }

      .guest_counter_wrapper .guest_element .tag .guest_tag {
        color: $corporate_1;
      }

      .counter_wrapper .counter .plus {
        background-color: $corporate_2;
      }
    }

    .package_element_wrapper .package_prices_wrapper .perform_package_booking_search {
      background-color: $corporate_1 !important;
    }

  }
  .fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking img {
    max-height: 220px;
  }
  .rate_conditions_popup_wrapper h3.rate_name, .package_hidden_popup .popup_title {
    background: $corporate_1;
  }
  .shopping_cart_summary.v2 .full_body_wrapper .scrollable_content_wrapper .items_wrapper .item:not(:last-child) {
    border-color: $corporate_2;
  }
}


  div#step-1 #rooms_b1_wrapper .preciosHabitacion .listadoHabsTarifas .regimen_tr_element .botonReservarColumn button {
    font-weight: 400;
  }
  div#step-1 #rooms_b1_wrapper .preciosHabitacion .listadoHabsTarifas .regimen_tr_element .precioNocheColumn .priceValues .precioGeneralDiv .precioGeneral {
    font-weight: 700 !important;
  }

  #wizard {
      div#step-1 {
        .rooms_packages_selectors_wrapper {
          &.has_packages {
            .button_package_room {
              &.rooms_selector{
                  color: $black;
                  &::before {
                      background-image: url(https://storage.googleapis.com/cdn.paraty.es/seleqtta-alba/files/tab_left_active_%23482D00.svg);
                  }
                  &::after {
                    color: $black;
                  }
              }
              &.packages_selector {
                  color: $black;
                  &::before {
                    background-image: url(https://storage.googleapis.com/cdn.paraty.es/seleqtta-alba/files/tab_right_active_%23482D00.svg);
                  }
                  &::after {
                    color: $black;
                  }

              }
            }
          }
        }
      }
  }

  div#full_wrapper_booking .boking_widget_inline .booking_form .wrapper_booking_button button.submit_button {
    &:hover {
        background: $corporate_5;
    }
  }

    div#step-2 {
      .booking_button_wrapper {
        .booking-button {
          text-transform: none;
          padding: 15px 0;
          font-weight: 500;
          width: 213px;
          margin-right: 19px;
          transition: all 0.6s;
        }
      }

      .booking-box--search {
        .booking-box__content {
          .booking-search-results__new-search {
            .booking-button {
              text-transform: none;
              padding: 15px 30px;
              background: $corporate_1;
              color: white;
              font-family: $font_1;
              font-weight: 500;
              height: 67px;
              font-size: 15px;
            }
          }
        }
      }
      .booking-2-service-container {
          .booking-2-container-description {
            .services_buttons_wrapper {
              .add_service_element {
                background: $black;
                color: white;
                text-transform: none;
                font-weight: 500;
                border: 0;
                font-family: $font_2;
                font-size: 14px;
                &:hover {
                    background: $corporate_5;
                }
              }
            }
          }
      }
    }

    div#step-3 #personal-details-form .booking_details_prices_wrapper .booking-button {
        &:hover {
            background: $hover_button;
        }
        &::before {
            display: none;
        }
    }


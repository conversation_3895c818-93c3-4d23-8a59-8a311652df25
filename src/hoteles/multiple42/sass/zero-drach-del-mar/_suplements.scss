div#step-2 {
  .supplements_list_wrapper {
    .booking-2-service-container {
      .booking-2-container-description {
        .booking-2-service-title {
          font-family: $title_family;
          font-weight: 400;
          font-size: 22px;
          letter-spacing: 1.1px;
          line-height: 1;
          text-transform: capitalize;
          color: $black;
        }
      }

      .booking-2-service-price {
        span {
          color: $corporate_2;
        }
      }

      .booking-2-services-list {
        .services_buttons_wrapper {
          .add_service_element {
            background-color: $corporate_1;
            font-family: $title_family;
            font-weight: 400;
            font-size: 18px;
            letter-spacing: 1.5px;
          }
        }
      }
    }
  }

  .booking_button_wrapper {
    .booking-button {
      position: relative;
      width: 275px;
      height: 35px;
      padding: 0 10px;
      margin: 0;
      border-radius: 2px;
      background-color: $corporate_2;

      font-family: $title_family;
      font-weight: 400;
      font-size: 16px;
      text-align: center;
      letter-spacing: 0.85px;
      line-height: 21px;
      text-transform: none;
      white-space: nowrap;
      color: white;
      @include transition(all, 0.6s);
    }
  }
}
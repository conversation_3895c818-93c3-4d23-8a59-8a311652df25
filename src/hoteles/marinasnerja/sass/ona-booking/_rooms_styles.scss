div#step-1 {
  .room_step_block {
    .room_step_label {
      font-size: 20px;
      letter-spacing: 0.4px;
      line-height: 20px;
      color: $black;
      border-radius: 8px;
      border: 2px solid #CDCFD0;

      .room_step_occupancy {
        display: none;
      }

      &::before {
        font-size: 40px;
        color: #7A7E81;
      }
    }
  }

  .contTipoHabitacion {
    border: 1px solid #CDCFD0 !important;
    border-radius: 0 16px 16px 16px;
    display: block;

    .overlay_picture_text {
      position: absolute;
      z-index: 2;
      text-align: center;
      top: 30px;
      left: 52px;
      font-size: 9px;
      font-weight: bold;
      width: 99px;

      strong {
        display: block;
        font-size: 27px;
        margin-bottom: 15px;
      }

      .s_tag {
        color: #E75354;
      }

      .m_tag {
        color: green;
      }

      .l_tag {
        color: #7CCFF4;
      }

      .xl_tag {
        color: black;
      }
    }

    .contDescHabitacion .room_services i {
      font-size: 25px;
    }

    .contFotoHabitacion {
      min-height: 220px;
      width: 340px;

      .occupancy {
        display: table;
        width: 100%;
        background: -webkit-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.4));
        background: -moz-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.4));
        background: -o-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.4));
        background: -ms-linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.4));
        background: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.4));
        position: absolute;
        bottom: 0;
        right: 0;
        z-index: 100;
        padding: 5px;
        text-align: right;
        font-family: $text_family;
        font-weight: 700;
        font-size: 12px;
        line-height: 12px;
        color: white;

        i {
          display: inline-block;
          vertical-align: bottom;
          padding: 0;
          color: white;
          font-size: 12px;
          line-height: 12px;

          &.adult {
            font-size: 22px;
            line-height: 22px;
            padding-right: 5px;
          }

          &.kid {
            font-size: 18px;
            line-height: 18px;
            padding-right: 5px;
          }

          &.baby {
            font-size: 14px;
            line-height: 14px;
            margin-bottom: 0;
            padding-left: 2px;
          }
        }

        b {
          position: absolute;
          bottom: 15px;
          right: 0;
          opacity: 0;
          display: inline-block;
          padding: 10px;
          font-weight: 400;
          text-transform: lowercase;
          background: rgba(0, 0, 0, 0.8);
          color: white;
          border-radius: 5px;
          @include transition(all, .6s);

          &:after {
            position: absolute;
            top: 100%;
            right: 30px;
            content: '';
            border: 5px solid transparent;
            border-color: rgba(0, 0, 0, 0.8) transparent transparent transparent;
          }
        }

        &:hover {
          b {
            bottom: 30px;
            opacity: 1;
          }
        }
      }
    }

    .contDescHabitacion {
      width: 755px !important;
      margin-top: 15px !important;

      .room_description_name_wrapper {
        .cabeceraNombreHabitacion {
          .tipoHabitacion {
            font-size: 22px;
            letter-spacing: 0.88px;
            line-height: 29px;
            font-weight: 700;
          }

          .just_booking_message {
            background-color: $corporate_5;

            &::before {
              display: none;
            }
          }

          .very_asked_message {
            font-family: $text_family !important;
            background-color: white !important;
            font-size: 14px;
            line-height: 24px;
            letter-spacing: 0.21px;
            bottom: 15px;
            font-weight: 400;
            text-transform: uppercase;
            padding: 5px 10px 5px 35px !important;
            box-shadow: 0px 3px 6px #00000029;
            color: $corporate_5;

            &::after {
              content: "\f005";
              display: inline-block;
              color: $corporate_5;
              font-family: "Font Awesome 6 Pro";
              left: -140px;
              bottom: 35%;
              border-left: 0 !important;
              font-weight: 700;
            }
          }

          &.booking_message_width {
            .just_booking_message {
              overflow: hidden;
            }

            .very_asked_message {
              bottom: 0;
              line-height: 19px;
            }
          }
        }
      }
    }

    .tipoHabitacion {
      color: $corporate_2 !important;
      font-size: 30px;
      font-family: $title_family;
      margin-bottom: 0;
    }

    .descripcionHabitacion {
      color: $black !important;
      height: 45px;
      font-size: 16px;
      letter-espacing: 0;
      line-height: 22px;
      color: $black;
    }

    .see_more_rooms_v2 {
      bottom: 60px;

      .plus_sign {
        color: #333 !important;
      }

      .see_more {
        text-decoration: none !important;
        text-transform: uppercase !important;
        color: #333 !important;
      }

      .lupa {
        top: 5px;
        left: 5px;
        font-size: 30px;
        font-family: "Font Awesome 6 pro", sans-serif;
        font-weight: 300;
        text-shadow: 0 0 5px rgba(0, 0, 0, .5);
      }
    }

    .very_asked_message {
      background-color: $red !important;
      color: white;
      letter-spacing: 1px;
      text-transform: uppercase;
      margin-right: -15px;

      &:after {
        border-left-color: transparent;
      }
    }

    .just_booking_message + .very_asked_message {
      &:after {
        border-left-color: $red;
      }
    }

    .room_services {
      border-top-width: 2px;
      border-top-style: dashed;
      border-bottom-width: 2px;
      border-color: $grey;
      width: 97%;

      .service_element {
        border-right: 0 !important;
        height: 40px !important;

        .service_description {
          color: $black;
          font-weight: 400;
          font-size: 12px;
        }
      }
    }

    .preciosHabitacion {
      margin: 60px 0 0 0;

      table.listadoHabsTarifas {
        tr.rate_tr_element {
          margin-top: 0 !important;

          .conditions_info_wrapper {
            right: 15px;
            top: 10px;
          }

          .rate_offer + .minium_price_rate + .conditions_info_wrapper {
            position: relative;
            float: right;
          }

          .contTitTipoTarifa {
            background-color: #F8F8F8;
            color: $black;
            font-family: $text_family;

            .titTipoTarifa {
              font-weight: 700;
              font-size: 15px;
              letter-spacing: 1.5px;
              line-height: 16px;
              color: $black;
              text-transform: uppercase;
            }

            .cheapest_rate_message {
              background-color: $corporate_3;
              color: $corporate_2;
              font-size: 12px;
              letter-spacing: 0;
              padding: 9px 30px 10px 40px !important;

              &:before {
                border-left: 19px solid #f5f5f5;
                border-bottom: 50px solid transparent;
              }

              .before_block {
                border-left-color: $corporate_1;
              }
            }

            .advice_rate_message {
              background-color: $corporate_1;
              color: white;

              &:before {
                border-left-color: $grey;
              }
            }

            > .conditions_info_wrapper {
              a {
                color: $corporate_2;
                font-weight: lighter;
                padding: 0 10px;
                text-decoration: none;
                position: relative;
                font-size: 10px;
                font-weight: 600;
                letter-spacing: 0;
                text-transform: none;

                &:after {
                  content: '';
                  position: absolute;
                  bottom: -1px;
                  left: 0;
                  right: 0;
                  margin: auto;
                  height: 1px;
                  width: 80%;
                  background-color: $corporate_2;
                }

                &:before {
                  display: none;
                }
              }
            }
          }
        }

        tr.regimen_tr_element {
          position: relative;
          margin: 10px 5px;
          width: calc(100% - 10px);
          border-bottom: 1px solid transparent;

          &:last-of-type {
            &:after {
              display: none;
            }
          }

          &:after {
            content: '';
            position: absolute;
            width: calc(100% - 20px);
            height: 1px;
            background: $corporate_4;
            bottom: -11px;
            left: 0;
            right: 0;
            margin: auto;
          }
        }

        tr .lock_board {
          .lock_board_wrapper {
            display: flex;
            width: 150px;
            justify-content: space-between;
          }
        }

        td.regimenColumn {
          width: 40%;

          .regimen_name_wrapper {
            font-size: 16px;
            letter-spacing: 0.48px;
            line-height: 16px;
            color: $black;
            text-transform: none;
          }

          .tTextoOferta {
            font-size: 15px;
            letter-spacing: 0.45px;
            line-height: 16px;
            color: $corporate_5;
            text-transform: none;
            font-weight: 700;

            .separator:first-child {
              display: none;
            }
          }
        }

        .precioNocheColumn  {
          display: none;
        }

        .precioTotalColumn {
          .priceValues {
            margin-left: -30px;
            width: 125px;

            .precioGeneralDiv {
              span {
                font-size: 22px;
                letter-spacing: 0;
                line-height: 32px;
                font-weight: bold;
                color: $corporate_2;
              }
            }

            .promotion_percentage_square {
              background: $corporate_5;

              .promotion_discount {
                background: $corporate_5;
              }
            }
          }
        }

        .botonReservarColumn {
          .booking-button {
            font-family: $text_family;
            background-color: #11CCC7;
            color: $corporate_2;
            padding: 12px 0;
            border-radius: 24px;
            width: 186px;
            margin-right: 0;
            font-size: 20px;
            line-height: 25.2px;
            font-weight: 700;
            position: relative;
            -webkit-transition: all 0.6s;
            -moz-transition: all 0.6s;
            -ms-transition: all 0.6s;
            -o-transition: all 0.6s;
            transition: all 0.6s;
            display: inline-block;
            vertical-align: middle;
            text-transform: uppercase;
            letter-spacing: 2px;
            z-index: 1;

            &:hover {
              background-color: $corporate_2;
              color: white;
            }
          }
        }
      }
    }

    .last_day_cancellation_text {
      background: transparent;
      position: relative;
      color: $forest;
      @extend .fa-check;

      &:before {
        @extend .fa;
        position: absolute;
        top: 50%;
        left: 3px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        color: $forest;
        border: 1px solid $forest;
        padding: 0 3px;
        font-size: 8px;
        line-height: 14px;
        border-radius: 50%;
      }
    }

    .cheapest_rate_message {
      .conditions_info_wrapper {
        display: inline-block;
        vertical-align: middle;
        width: auto;
        position: unset;
      }

      .rate_conditions_link {
        color: $corporate_2;
        font-weight: 600;
        padding: 0 10px;
        text-decoration: none;
        position: relative;
        font-size: 10px;
        letter-spacing: 0;
        text-transform: none;

        &:after {
          content: '';
          position: absolute;
          bottom: -1px;
          left: 0;
          right: 0;
          margin: auto;
          height: 1px;
          width: 80%;
          background-color: lighten($black, 20%);
        }

        &:before {
          display: none;
        }
      }

      &.has_conditions {
        padding: 10px 30px 10px 40px !important;
      }
    }

    .rate_conditions_link {
      background: transparent;
      position: relative;
      @extend .fa-info;

      &:before {
        @extend .fa;
        width: 3px;
        height: 14px;
        position: absolute;
        top: 50%;
        right: 3px;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        border: 1px solid $black;
        background: transparent;
        color: $black;
        padding: 0 5px;
        font-size: 10px;
        line-height: 14px;
        border-radius: 50%;
      }

      &:hover {
        color: $corporate_3;
      }
    }

    .sectoption {
      border: none;
      padding: 5px 0;

      .titSelOpt {
        color: $black;
        font-size: 18px;
        font-family: $title_family;
      }

      .listaradio {
        li {
          input {
            display: inline-block;
            vertical-align: middle;
            margin: 0 5px 0;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: white;
            border: 1px solid $black;
            cursor: pointer;

            &:checked {
              background-color: $corporate_1;
            }

            &:focus {
              outline: 0;
            }
          }
        }
      }
    }
  }

  .total_price_wrapper {
    .total_price {
      background: #333333;
      border-radius: 8px 0 0 8px;
      padding: 3px 20px;

      .total_price_label {
        font-size: 14px;
        line-height: 24px;
      }

      .total_price_value {
        font-size: 16px;
        font-weight: bold;
      }
    }

    .rooms_submit_button {
      border-radius: 0 8px 8px 0;
      background: #CECECE;
      font-size: 20px;
      font-weight: bold;
      letter-spacing: 0.4px;
      line-height: 17px;
      padding: 12px 40px;
      margin: 0 0 0 5px;
      border: 0;
    }

  }
}

div#step-1 .conditions_info_wrapper .rate_conditions_link {
  font-size: 9px;
  text-transform: uppercase;
  box-sizing: border-box;
  letter-spacing: 1px;
  font-weight: normal;
  padding: 0 30px 0 0;
}

.popup_see_more_rooms_second {
  a.fancybox-nav {
    display: none;
  }

  .fancybox-inner {
    background-color: white;
  }

  .room_popup_individual_element {
    .popup_title {
      background: white;
      background: linear-gradient(to left, white 0%, white 33%, #ececec 33%, #ececec 100%);
      font-family: $title_family;
      font-size: 25px;
      font-weight: 700;
      color: #333;
      top: auto;
      bottom: calc(100% - 400px);
      padding-right: 35%;
      padding-bottom: 10px;
      border-top: 10px solid white;
      line-height: normal;
    }

    .close_button_element {
      font-family: $text_family;
      font-size: 18px;
      font-weight: 700;
      color: white;
      background-color: $corporate_1;
      width: 50px;
      line-height: 50px;
      top: 0;
      right: 0;
    }

    .popup_carousel {
      .element_carousel_pictures {
        .exceded {
          height: 400px;

          img {
            width: 100%;
          }

          a {
            display: block !important;
            position: absolute;
            top: 270px;
            right: 0;
            background: radial-gradient(rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
            padding: 10px;
            color: white;

            i.fa {
              font-size: 25px;
            }

            &:hover {
              color: $corporate_1;
            }
          }
        }
      }

      .flex-direction-nav {
        .flex-nav-prev, .flex-nav-next {
          width: 50px;
          height: 50px;
          @include transition(width, .6s);

          &:hover {
            width: 60px;

            a {
              &:before {
                margin-left: 8px;
              }
            }
          }

          a {
            background-color: $corporate_2;
            @extend .icon-longarrow;

            &:before {
              font-family: "icomoon", sans-serif;
              font-size: 30px;
              color: white;
              position: absolute;
              top: 50%;
              left: 50%;
              -webkit-transform: translate(-50%, -50%);
              -moz-transform: translate(-50%, -50%);
              -ms-transform: translate(-50%, -50%);
              -o-transform: translate(-50%, -50%);
              transform: translate(-50%, -50%);
              @include transition(margin, .6s);
            }

            img {
              display: none;
            }
          }
        }

        .flex-nav-prev {
          a:before {
            -webkit-transform: translate(-50%, -50%) rotate(-180deg);
            -moz-transform: translate(-50%, -50%) rotate(-180deg);
            -ms-transform: translate(-50%, -50%) rotate(-180deg);
            -o-transform: translate(-50%, -50%) rotate(-180deg);
            transform: translate(-50%, -50%) rotate(-180deg);
          }

          &:hover {
            a {
              &:before {
                margin-left: -8px;
              }
            }
          }
        }
      }
    }

    .popup_room_pictures {
      display: none !important;
    }

    .room_services {
      margin-left: 0 !important;
      width: 100% !important;
      padding: 0 !important;
      border-top-style: dashed;
      border-bottom-style: dashed;
      border-top-color: $grey;
      border-bottom-color: $grey;
      border-top-width: 2px;
      border-bottom-width: 2px;
      background: transparent;

      .service_element {
        border-right-style: dashed;
        border-right-color: $grey;
        border-right-width: 2px;
        height: auto !important;

        i {
          font-size: 25px;
          display: inline-block;
          padding-bottom: 0;
          margin-right: 10px;
        }

        .service_description {
          font-size: 12px;
          font-weight: 700;
        }
      }
    }

    .popup_room_description {
      margin-top: 10px;
      margin-bottom: 10px;
      padding-top: 20px;
      padding-bottom: 20px;
      margin-left: 0 !important;
      width: 100% !important;
      font-family: $text_family;
      //background: linear-gradient(to left, white, white 33%, #ececec 33%, #ececec);
      box-sizing: border-box;

      .desc, .list {
        display: inline-block;
        vertical-align: top;
        width: calc(100% / 3);
        box-sizing: border-box;
      }

      .desc {
        width: calc(100% / 3 * 2);
        padding-right: 20px;

        strong {
          font-weight: 700;
        }
      }

      .list {
        padding: 0 0 0 25px;

        li {
          font-weight: 700;
          padding: 5px 0;
          @extend .icon-longarrow;

          &:before {
            font-family: "icomoon", sans-serif;
            margin-right: 10px;
            color: $corporate_2;
          }
        }
      }
    }
  }
}
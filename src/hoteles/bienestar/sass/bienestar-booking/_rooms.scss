div#step-1 {
  //&:before {
  //  content: "";
  //  position: absolute;
  //  width: 100vw;
  //  z-index: 0;
  //  top: 75px;
  //  opacity: .9;
  //  height: 382px;
  //  background-color: $corporate_1;
  //  -webkit-mask-image: url(https://storage.googleapis.com/cdn.paraty.es/puertobahia-spa/files/olas%20proceso.svg);
  //  mask-image: url(https://storage.googleapis.com/cdn.paraty.es/puertobahia-spa/files/olas%20proceso.svg);
  //  -webkit-mask-repeat: no-repeat;
  //  mask-repeat: no-repeat;
  //  -webkit-mask-size: cover;
  //  mask-size: cover;
  //  pointer-events: none;
  //  -webkit-mask-position: 50% 50%;
  //  mask-position: 50% 50%;
  //  @include center_x;
  //}
  &:after {
    content: "";
    position: absolute;
    width: 520px;
    z-index: 0;
    pointer-events: none;
    opacity: .9;
    height: 695px;
    background-color: $corporate_1;
    -webkit-mask-image: url(https://storage.googleapis.com/cdn.paraty.es/puertobahia-spa/files/icon.svg);
    mask-image: url(https://storage.googleapis.com/cdn.paraty.es/puertobahia-spa/files/icon.svg);
    -webkit-mask-repeat: no-repeat;
    mask-repeat: no-repeat;
    -webkit-mask-size: cover;
    mask-size: cover;
    -webkit-mask-position: 50% 50%;
    mask-position: 50% 50%;
    @include center_y;
    right: -120px;
    top: 60%;
  }
  .contTipoHabitacion {
    padding: 0;
    position: relative;
    z-index: 1;
    box-shadow: 0 0 17px 0 rgba(0, 0, 0, .1);
    .contFotoDescripcion {
      padding: 0;

      .contFotoHabitacion {
        width: 320px;

        .see_more_rooms_v2 {
          img {
            @include cover_image;
            position: static;
          }

          .lupa {
            top: 10px;
            right: auto;
            left: 10px;
            font-family: "Font Awesome 5 Pro";
            font-weight: 300;
            font-size: 30px;
          }
        }
      }

      .contDescHabitacion {
        margin: 0;
        width: 820px;
        padding-top: 15px;
        height: 205px;
        box-sizing: border-box;

        .room_description_name_wrapper {
          overflow: visible;
        }

        .tipoHabitacion {
          font-family: $title_family;
          font-weight: 700;
          font-size: 22px;
          letter-spacing: 1.75px;
          line-height: 1;
          text-transform: capitalize;
          color: $corporate_2;
        }

        .descripcionHabitacion {
          font-family: $text_family;
          font-weight: 400;
          font-size: 16px;
          letter-spacing: 0px;
          line-height: 21px;
          color: $grey;
          height: 65px;
          padding-right: 120px;
        }

        .very_asked_message {
          background-color: $red;
          padding: 0 15px;
          margin-right: -15px;
          line-height: 30px;
          text-transform: uppercase;

          &::before {
            left: auto;
            right: 100%;
            border-top: 15px solid transparent;
            border-left: 5px solid transparent;
            border-right: 5px solid $red;
            border-bottom: 15px solid $red;
          }

          &::after {
            left: 100%;
            right: auto;
            border-top: 15px solid $red;
            border-left: 5px solid $red;
            border-right: 5px solid transparent;
            border-bottom: 15px solid transparent;
          }

          i {
            color: white;
            font-size: 16px;
            vertical-align: middle;
            margin-right: 10px;
          }

          span {
            font-weight: 400;
            font-size: inherit;
            letter-spacing: 0.2px;
          }
        }

        .see_more_rooms_v2 {
          .plus_sign {
            color: $grey;
          }

          .see_more {
            font-family: $text_family;
            font-weight: 700;
            font-size: 13px;
            letter-spacing: 1.55px;
            line-height: 1;
            color: $grey;
            text-transform: uppercase;
            text-decoration: none;
            padding: 2px 10px 15px 10px;
          }
        }

        .room_services {
          margin: 0 auto;
          border-top: 1px solid $corporate_1;
          border-bottom: 1px solid $corporate_1;

          .service_element {
            border-right: none;
          }
        }
      }
      .just_booking_message {
          background: $red;
          font-weight: 400;
          color: white;
          margin-right: -40px;
          border-radius: 12px;
          padding: 5px 10px 5px 40px;

          &:before {
              content: "\f005";
              font-family: "Font Awesome 5 Pro";
              color: white;
              font-size: 17px;
              border: none;
              display: inline-block;
              vertical-align: middle;
              width: 24px;
              margin-left: 9px;
              height: 24px;
              font-weight: 800;
          }

          &:after {
              display: none;
          }
      }
    }

    .preciosHabitacion {
      .listadoHabsTarifas {

        .contTitTipoTarifa {
          background-color: $corporate_3;

          .titTipoTarifa {
            font-family: $text_family;
            font-weight: 700;
            font-size: 15px;
            letter-spacing: 1.5px;
            color: $corporate_2;
            text-transform: uppercase;
          }

          .advice_rate_message:before  {
            border-left-color: $corporate_3;
            border-top-color: $corporate_3;
          }

          .cheapest_rate_message {
            background: $red;

            &::before {
              //border-top: 19px solid $corporate_3;
              //border-left: 7px solid $corporate_3;
              border-right-width: 7px;
            }

            .best_price_label_info {
              font-family: $text_family;
              font-weight: 700;
              font-size: 14px;
              letter-spacing: 0.7px;
              line-height: 1;
            }
          }
        }

        .regimenColumn {
          font-family: $text_family;
          font-weight: 400;
          font-size: 15px;
          letter-spacing: 0.45px;

          .tTextoOferta {
            font-weight: 700;
            font-size: 15px;
            color: $red;
          }
        }

        .precioNocheColumn {
          width: 5%;

          .priceValues {
            display: none;
          }
        }

        .precioTotalColumn {
          width: 35%;
          font-family: $text_family;

          .precioTachadoDiv {
            text-align: right;
            color: $red;

            .tPrecioTachado {
              font-weight: 700;
              font-size: 11px;
              letter-spacing: 0.65px;
              color: $red;
            }
          }

          .precioGeneralDiv {
            .tPrecioTotal,
            .tPrecioOferta {
              font-weight: 700;
              font-size: 18px;
              letter-spacing: 1px;
              color: $grey !important;
            }
          }

          .promotion_percentage_square {
            border-radius: 50%;
            font-weight: 700;
            font-size: 13px;
            letter-spacing: 0.2px;
          }

          .priceTitle {
            font-weight: 400;
            font-size: 9px;
            letter-spacing: 0.2px;
            color: $grey;
            line-height: 8px;
          }
        }

        .botonReservarColumn {
          button {
            width: 185px;
            height: 50px;
            border-radius: 10px;

            font-family: $title_family;
            font-weight: 400;
            font-size: 19px;
            letter-spacing: 1.9px;
            @include transition(all, .5s);
            &:hover {
              background: $corporate_2;
            }
          }
        }
      }
    }
  }
}

.fancybox-overlay {
  .room_popup_individual_element {
    .popup_title {
      font-family: $title_family;
      font-weight: 400;
      font-size: 22px;
      letter-spacing: 1.1px;
      line-height: 1;
      color: white;
    }

    .popup_carousel {
      .exceded {
        padding: 0;

        .popup_image {
          @include cover_image;
          position: static;
        }
      }
    }

    .popup_room_description {
      font-family: $text_family;
      font-weight: 300;
      font-size: 15px;
      letter-spacing: 0.9px;
      line-height: 21px;
      color: $grey;
    }
  }
}

.fancybox-overlay .room_popup_individual_element .room_services,
.popup_see_more_rooms_second .room_popup_individual_element .room_services,
div#step-1 .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_services {
  display: grid !important;
  padding: 0;
  grid-template-columns: repeat(4, auto);

  .service_element {
    width: 100%;
    height: auto;
    padding: 10px 5px;

    .service_description {
      font-family: $text_family;
      font-weight: 400;
      font-size: 12px;
      letter-spacing: 0px;
      line-height: 1;
      color: $grey;
    }
  }
}
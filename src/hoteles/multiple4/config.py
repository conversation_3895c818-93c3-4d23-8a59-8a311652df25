import os

from flask.views import MethodView

from utils.flask_requests import response_utils
from utils.web.web_seeker_utils import WebSeekerHandler
from webs.arillo.templateHandler import Template<PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>
from webs.benalmadenaplaya.templateHandler import Temp<PERSON><PERSON><PERSON><PERSON> as BenalmadenaPlayaHandler
from webs.byblaguna.templateHandler import Temp<PERSON><PERSON><PERSON><PERSON> as ByBLagunaHandler
from webs.dongustavo.templateHandler import Temp<PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>
from webs.donpaco.templateHandler import Template<PERSON>andler as pacoHandler
from webs.estrellanorte.templateHandler import Template<PERSON>andler as <PERSON><PERSON><PERSON><PERSON>
from webs.juanmontiel.templateHandler import Temp<PERSON><PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>and<PERSON>
from webs.torreon2.templateHandler import Template<PERSON>andler as TorreonNewHandler

host = os.environ.get('HTTP_HOST')

applicationId = "secure-web-booking"

class RedirectToHandler(TorreonNewHandler):
	def torreon(self, *args):
		response_utils.set_response_redirection("http://www.hoteltorreon.es", permanent=True)
		return ""


class MyHandler500(MethodView):
	def get(self):
		response_utils.set_response_status_code(503)
		return ""


class JuanMontielRedirectionHandler(JuanMontielHandler):
    def get(self, *args):
        response_utils.set_response_redirection("https://www.playasenator.com/es/hoteles/senator-aguilas/", permanent=True)
        return


extra_routing = {
	'.*localhost.*': {
		'.*': GustavoHandler
	},
	'.*zeus.*': {
		'.*': ZeusHandler
	},
	'.*citymartorreon.*': {
		'.*': RedirectToHandler
	},
	'.*torreon.*': {
		'.*': MyHandler500
	},
	'.*laguna.*': {
		'.*': ByBLagunaHandler
	},
	'.*ateneo.*': {
		'.*': WebSeekerHandler
	},
	'.*montiel.*': {
		'.*': JuanMontielRedirectionHandler
	},
	'.*gustavo.*': {
		'.*': GustavoHandler
	},
	'.*aleysa.*': {
		'.*': JuanMontielHandler
	},
	'.*paco.*': {
		'.*': pacoHandler
	},
	'.*arillo.*': {
		'.*': ArilloHandler
	},
	'.*benalmadena.*': {
		'.*': BenalmadenaPlayaHandler
	}
}

ssl_mandatory = False

server_type = "F2"

templates = ['estrellanorte', 'byblaguna', 'torreon2','juanmontiel', 'donpaco', 'arillo', 'benalmadenaplaya', 'dongustavo']
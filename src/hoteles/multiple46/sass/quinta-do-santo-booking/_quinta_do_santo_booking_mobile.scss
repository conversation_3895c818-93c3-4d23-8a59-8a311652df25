header {
    height: 70px !important;

    .logo_wrapper {
      img.main-logo {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }


  .main_content_wrapper.step_0 {
    .room_list {
      .room_pack_option {
        .labels_wrapper {
          .label {
            text-transform: capitalize;
            padding: 5px;
            font-weight: 400;
          }
        }

        .room_name {
          .info_btn {
            white-space: nowrap;
          }

          &.content_title {
            .title {
              margin-top: 20px;
            }
          }
        }
      }
    }
  }

  .top_continue_booking {
    width: 100%;

    .perform_additional_services_booking {
      background: $corporate_1;
      margin-bottom: 20px;
      font-weight: 400;
      font-size: 16px;
      width: initial;
      i {
        &::before {
            content: "\f061";
        }
      }
    }
  }

  footer {
    background: white !important;
  }

  .all_additional_services_wrapper {
    width: 100% !important;

    .additional_services_tabs_wrapper {
      text-align: center;
      .additional_services_tabs_list {
        .additional_services_tab {
          margin-right: 0;
        }
      }
    }
  }

  .additional_services_total_wrapper {
    width: 100% !important;

    .perform_additional_services_booking {
      background: $corporate_1;
      color: white;
      width: 187px;
      box-sizing: border-box;
      text-align: center;
      position: relative;
      margin-left: 40px;
      cursor: pointer;
      position: relative;
      left: 0;
      margin-top: 20px;
      margin-bottom: 20px;
      font-weight: 400;
      text-transform: math-auto;
      display: inline-block;

    }
  }

.price_calendar_wrapper {
  .full_container {
    .bottom_wrapper {
      .bottom {
        .right_wrapper {
          .btn_booking.active {
              svg {
                  display: none;
              }
            .text_wrapper {
                  transform: none;
            }
          }
        }
      }
    }
  }
}

.header_table .header_text {
  vertical-align: middle;
}

#reservation {
  .booking_information .value_booking {
    word-break: break-word;
  }
  .header_table {
    .header_text {
      vertical-align: middle;
    }
  }
}

.location_wrapper {
  .location_table {
    td:last-child {
      vertical-align: middle !important;
    }
  }
}

.footer_buttons_extra_functionality {
  display: flex;
  .booking-button {
    height: 35px;
    &.go-home-button {
      line-height: 34px;
    }
  }
}
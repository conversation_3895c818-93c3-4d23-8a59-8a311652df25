body.baia-cristal.booking_process_mobile_v1{
  @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');
  $corporate_1: #7ec7dc;
  $corporate_2: #7ec7dc;
  $corporate_3: #7ec7dc;
  $corporate_4: #7ec7dc;
  $black: black;
  $grey: #F4F4F4;
  $grey-2: #444444;
  $green: #3FAB38;
  $red: #BC1B36;
  $title_family: 'Poppins', sans-serif;
  $text_family: 'Poppins', sans-serif;
  $font_1: $text_family;
  $font_2: $text_family;

  .double_button_wrapper {
    button {
      margin: 5px;
      border-color: $grey-2;
      color: $grey-2;
      font-weight: 700;
      font-size: 11px;
      letter-spacing: 0.75px;
    }
  }

  .back_booking,
  .continue_booking{
    background-color: $corporate_1;
  }

  .booking_widget_wrapper{
    .double_button_wrapper{
      .close_button{
        background-color: $corporate_1;
        color: white;
      }
    }

    .input_wrapper:before{
      background-color: $corporate_1;
    }

    #departure_date_popup,
    #entry_date_popup{
      .header_wrapper{
        .banner_title{
          i{
            color: $corporate_1;
          }
        }
      }
      .end_datepicker,
      .start_datepicker{
        .ui-datepicker-inline{
          .highlight_day{
             background: $corporate_1;
          }
          .start_date_selection{
            span{
              &::before{
                background: $corporate_1;
              }
            }

            &::before{
              background: $corporate_1;
            }
          }
           .ui-datepicker-current-day{
             .ui-state-active:before{
               background: $corporate_1;
             }

             &::before{
               background: $corporate_1;
             }
          }
          .ui-datepicker-header{
             color: $corporate_1;
          }
        }
      }
    }
  }

  .main_content_wrapper{
    &.step_0{
      .room_list{
        .room_pack_option{
          .rates_details_wrapper{
            .rate_selected_title{
              span{
                color: $corporate_1;
              }
            }
          }
        }
      }
    }

    &.step_2{
      .reservation_summary{
        .option_selected{
          .rate{
            .conditions{
              color: $corporate_1;
            }
          }
          .price{
            color: $corporate_1;
          }
        }
      }
    }
  }

  footer{
    .logo_wrapper{
      display: none;
    }
    .info{
      text-align: center;
    }
  }
}
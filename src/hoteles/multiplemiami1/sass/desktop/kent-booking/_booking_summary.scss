div#step-1, div#step-2 {
  background-color: white;

  &#step-1 .booking-box--search {
    display: none;
  }

  &#step-2 .booking-box--search {
    display: block;
  }

  .hidden_booking_summary {
    display: none !important;
  }

  .booking-box--search {
    margin: 7px auto 20px !important;
    position: relative;
    padding: 10px 20px;
    box-sizing: border-box;
    border-radius: 16px;
    background: #F7F5F5;
    @media only screen and (max-width: 1140px) {
      width: 1077px !important;
    }

    .booking-box__content {
      width: 100% !important;
      border: 0 !important;
      padding: 0 !important;
      position: relative;

      .title_booking_breakdown {
        position: relative;
        z-index: 1;
        color: #222222;
        height: 95px;
        max-width: 40px;
        vertical-align: middle;
        font-family: $title_family;
        text-align: left;
        line-height: 32px;
        letter-spacing: 0.6px;
        font-size: 24px;
        font-weight: 400;
        padding: 0 80px 0 0;

        @media only screen and (max-width: 1140px) {
          font-size: 20px;
        }

        &:lang(ru) {
          max-width: 10px;

          &:before {
            right: 5px;

          }
        }

        &:before {
          content: "\f073";
          font-family: $fa6;
          position: absolute;
          top: 50%;
          -webkit-transform: translateY(-50%);
          -moz-transform: translateY(-50%);
          -ms-transform: translateY(-50%);
          -o-transform: translateY(-50%);
          transform: translateY(-50%);
          font-weight: 300;
          font-size: 40px;
          color: $corporate_2;
          z-index: -1;
          right: 15px;
        }

        &:after {
          content: '';
          position: absolute;
          height: 70px;
          top: 17px;
          right: -10px;
          width: 1px;
          background: $lightgrey;
          opacity: .5;
        }
      }

      .booking-search-results__search-data {
        color: black;
        padding-left: 50px;
        font-size: 14px;
        //padding-right: 30px;
        padding-top: 0;
        white-space: nowrap;
        max-width: 90px;

        .booking-title-info.booking-hotel-name, .booking-3-info {
          text-transform: none;
          font-size: 16px;
          font-family: $title_family;
          font-weight: 500;
          line-height: 30px;
          color: $corporate_2;
          display: none;
        }

        .booking-title-info.booking-hotel-name {
          display: none;
        }

        .booking-title-info {
          color: $black;
          font-size: 12px !important;
          margin-right: 5px;
        }

        i {
          color: $corporate_2;
          font-size: 18px;

          &.fa-long-arrow-left,
          &.fa-long-arrow-right {
            margin-left: -5px;
            margin-right: 5px;
            font-size: 14px;
            @extend .icon-longarrow;

            &:before {
              display: inline-block;
              font-family: "icomoon", sans-serif;
              color: rgba($corporate_2, 1);
            }
          }

          &.fa-long-arrow-left {
            &:before {
              -webkit-transform: rotate(180deg);
              -moz-transform: rotate(180deg);
              -ms-transform: rotate(180deg);
              -o-transform: rotate(180deg);
              transform: rotate(180deg);
            }
          }
        }

        .booking-title-info, .notranslate {
          font-family: $text_family;
          font-size: 16px;
          font-weight: 400;
          line-height: 20px;
          color: $black;
        }

        .notranslate {
          font-weight: 600;

          > span {
            display: inline-block;
            vertical-align: middle;
            padding: 0 2px;
          }
        }
      }


      .booking-search-results__rooms-list {
        color: #383838;
        max-width: fit-content;
        font-size: 14px;
        font-weight: 600;
        line-height: 20px;
        white-space: nowrap;
        @include center_y;
        top: 50%;
        left: 54%;
        transform: translate(-50%, -50%);
        padding: 0 15px;
        overflow-x: clip;
        overflow-y: scroll;
        height: 100%;

        .booking-title-info {
          font-family: $text_family;
          font-weight: 400;
          font-size: 12px;
          color: $black;
          margin-right: 5px;

          &:before {
            content: "\e9e4";
            display: inline-block;
            vertical-align: middle;
            margin-right: 5px;
            font-family: "icomoon", sans-serif;
            font-size: 16px;
            color: $corporate_2;
          }
        }

        .search-item {
          font-size: 14px;
          font-weight: 700;
          line-height: 20px;
          color: #383838;

          .booking-title-info {
            font-weight: 400;

            &:before {
              content: "\e9f8";
            }
          }
        }

        i {
          display: none;
        }

        b {
          font-weight: 400;
        }
      }


      .booking-search-results__new-search {

        .total_price_label {
          display: none;
        }

        .booking-button {
          display: block;
          position: relative;
          background-color: transparent;
          border: 1px solid $corporate-2;
          max-width: 277px;
          height: 81px;
          margin: 10px 0;
          border-radius: 10px !important;
          padding: 20px !important;
          font-family: $text_family;
          font-weight: 700;
          font-size: 16px;
          color: $corporate_2;
          text-transform: uppercase;
          line-height: 21px;
          transition: all 0.6s;
          z-index: 1;

          &:lang(ru) {
            max-width: 180px;
          }

          &:hover {
            opacity: 0.7;
          }

          /*&:before {
            content: "\f073";
            font-family: $fa6;
            font-weight: 300;
            position: absolute;
            top: 50%;
            left: 10px;
            -webkit-transform: translate(0%, -50%);
            -moz-transform: translate(0%, -50%);
            -ms-transform: translate(0%, -50%);
            -o-transform: translate(0%, -50%);
            transform: translate(0%, -50%);
            color: white;
            font-size: 35px;
            padding: 10px;
            border-radius: 50%;
            color: $corporate_2;
            z-index: 1;
          }*/
        }
      }

      #currencyDiv {
        display: none;
      }
    }

    .call_center_wrapper {
      width: 15%;
      border: none;

      .web_support_label_1 {

        .web_support_wrapper {
          text-align: center;
          font-size: 14px;
          font-weight: 600;
          color: $corporate_2;
          font-family: $text_family;

          strong, span {
            font-weight: 600;
          }
        }

        &::before {
          display: none;
        }
      }
    }
  }

  .hidden_booking_summary {
    border-bottom: none;
    box-shadow: 0 10px 25px -15px rgba(0, 0, 0, 0.30);
    z-index: 55;

    .booking-box__content {
      width: 95% !important;
      border: 0 !important;
      padding: 0 !important;
      position: relative;

      .title_booking_breakdown {
        position: relative;
        z-index: 1;
        color: $black;
        height: 95px;
        max-width: 155px;
        vertical-align: middle;
        font-family: $title_family;
        text-transform: none;
        text-align: left;
        line-height: 32px;
        font-size: 24px;
        letter-spacing: 0.6px;
        font-weight: 400;
        padding: 0 80px 0 0;
        @media only screen and (max-width: 1140px) {
          font-size: 20px;
        }

        &:lang(ru):before {
          right: 10px;
        }

        &:before {
          content: "\f073";
          font-family: $fa6;
          position: absolute;
          top: 50%;
          -webkit-transform: translateY(-50%);
          -moz-transform: translateY(-50%);
          -ms-transform: translateY(-50%);
          -o-transform: translateY(-50%);
          transform: translateY(-50%);
          font-weight: 300;
          font-size: 40px;
          color: $corporate_2;
          z-index: -1;
          right: 30px;
        }

        &:after {
          content: '';
          position: absolute;
          height: 70px;
          top: 17px;
          right: -10px;
          width: 1px;
          background: $lightgrey;
          opacity: .5;
        }
      }

      .booking-search-results__search-data {
        color: black;
        padding-left: 30px;
        font-size: 14px;
        padding-right: 30px;
        padding-top: 0;
        white-space: nowrap;
        max-width: 90px;

        .booking-title-info.booking-hotel-name {
          text-transform: none;
          font-size: 14px;
          font-family: $title_family;
          font-weight: 600;
          line-height: 20px;
          color: $corporate_2 !important;
        }

        .booking-title-info.booking-hotel-name, .booking-3-info {
          text-transform: none;
          font-size: 14px;
          font-family: $title_family;
          font-weight: 400;
          line-height: 30px;
          color: $corporate_2;
        }

        .booking-title-info.booking-hotel-name {
          display: none;
        }

        .booking-title-info {
          color: $black;
          font-size: 12px;
        }

        i {
          color: $corporate_2;

          &.fa-long-arrow-left,
          &.fa-long-arrow-right {
            margin-left: -5px;
            margin-right: 5px;
            font-size: 16px;
            @extend .icon-longarrow;

            &:before {
              display: inline-block;
              font-family: "icomoon", sans-serif;
              color: rgba($corporate_2, 1);
            }
          }

          &.fa-long-arrow-left {
            &:before {
              -webkit-transform: rotate(180deg);
              -moz-transform: rotate(180deg);
              -ms-transform: rotate(180deg);
              -o-transform: rotate(180deg);
              transform: rotate(180deg);
            }
          }
        }

        .booking-title-info, .notranslate {
          font-family: $text_family;
          font-size: 12px;
          font-weight: 400;
          line-height: 24px;
          color: #383838;
        }

        .notranslate {
          font-weight: 700;

          > span {
            display: inline-block;
            vertical-align: middle;
            padding: 0 2px;
          }
        }
      }

      .booking-search-results__rooms-list {
        color: #383838;
        max-width: fit-content;
        font-size: 14px;
        font-weight: 700;
        line-height: 20px;
        white-space: nowrap;
        padding: 0 25px;
        overflow-x: clip;
        overflow-y: scroll;
        height: 100%;
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);

        &:lang(ru) {
          padding-left: 90px !important;
        }

        .booking-title-info {
          font-family: $text_family;
          font-weight: 400;

          &:before {
            content: "\e9e4";
            display: inline-block;
            vertical-align: middle;
            margin-right: 5px;
            font-family: "icomoon", sans-serif;
            font-size: 14px;
            color: $corporate_2;
          }
        }

        .search-item {
          font-size: 14px;
          font-weight: 700;
          line-height: 20px;
          color: #383838;

          .booking-title-info {
            font-weight: 400;

            &:before {
              content: "\e9f8";
            }
          }
        }

        i {
          display: none;
        }

        b {
          font-weight: 400;
        }
      }

      .booking-search-results__new-search {
        .booking-button {
          display: block;
          font-family: $text_family;
          position: relative;
          background-color: $corporate_2 !important;
          text-transform: uppercase;
          max-width: 277px;
          height: 60px;
          margin: 10px 0;
          border-radius: 10px !important;
          padding: 0px 20px 0px 70px;
          font-size: 16px;
          line-height: 21px;
          font-weight: 700;
          -webkit-transition: all 0.6s;
          -moz-transition: all 0.6s;
          -ms-transition: all 0.6s;
          -o-transition: all 0.6s;
          transition: all 0.6s;
          z-index: 1;

          &:lang(ru) {
            max-width: 190px;
            padding: 10px 20px 20px 70px !important;
          }

          &:hover {
            background: $corporate_2 !important;
          }

          &:before {
            content: "\f073";
            font-family: $fa6;
            font-weight: 300;
            position: absolute;
            top: 50%;
            left: 10px;
            -webkit-transform: translate(0%, -50%);
            -moz-transform: translate(0%, -50%);
            -ms-transform: translate(0%, -50%);
            -o-transform: translate(0%, -50%);
            transform: translate(0%, -50%);
            color: white;
            font-size: 35px;
            padding: 10px;
            border-radius: 50%;
            z-index: 1;
          }
        }
      }

      #currencyDiv {
        display: none;
      }
    }

    .call_center_wrapper {
      width: 15%;
      border: none;

      .web_support_label_1 {

        .web_support_wrapper {
          text-align: center;
          font-size: 14px;
          font-weight: 600;
          color: $corporate_2;
          font-family: $text_family;

          strong, span {
            font-weight: 600;
          }
        }

        &::before {
          display: none;
        }
      }
    }
  }
}

@media only screen and (max-width: 1140px) {
  .booking-box--search {
    .booking-box__content {
      .title_booking_breakdown {
        padding: 0 55px 0 0 !important;
      }

      .booking-search-results__search-data {
        padding-left: 15px !important;
      }
    }
  }
}

#step-2 {
  .booking-box--search .booking-box__content .booking-search-results__new-search .booking-button:lang(ru) {
    max-width: 230px !important;
  }

  .hidden_booking_summary.showed {
    .booking-search-results__new-search {
      top: 50%;

      .total_price_label {
        width: 260px;
        margin-right: 0;
        margin-bottom: 0;
      }
    }
  }


}
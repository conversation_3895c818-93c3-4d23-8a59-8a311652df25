@media (min-width: 1091px) {
  #widget_paraty:not(.popup):not(.only_popup) {
    #full_wrapper_booking {
      display: block !important;
    }
  }
}

@media (max-width: 1090px) {
  .datepicker_wrapper_element .specific_month_selector,
  .datepicker_wrapper_element .go_back_button,
  .datepicker_wrapper_element_2 .specific_month_selector,
  .datepicker_wrapper_element_2 .go_back_button,
  .datepicker_wrapper_element_3 .specific_month_selector,
  .datepicker_wrapper_element_3 .go_back_button {
    display: none !important;
  }
  .datepicker_wrapper_element,
  .datepicker_wrapper_element_2,
  .datepicker_wrapper_element_3 {
    z-index: 100000002;
  }

  #paraty_widget_container {
    z-index: 1001 !important;
    position: relative;
    left: auto;
    top: auto;
    right: auto;
    transform: none;
    -ms-transform: none;
    -webkit-transform: none;
    -moz-transform: none;
    -o-transform: none;
  }


  #widget_paraty {
    background: none;
    padding: 0;
    margin: 0;
    @include transform(none);
    width: 0;
    height: 0;
    padding: 0 !important;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;

    &.fixed {
      #full_wrapper_booking {
        #full-booking-engine-html-7 {
          .booking_form {
            .room_list_wrapper {
              bottom: 60px !important;
            }
          }
        }
      }
    }

    #full_wrapper_booking {
      display: none;
      position: fixed;
      overflow: scroll;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      width: 100%;
      @include transform(none);
      background-color: white;
      z-index: 1100;

      #full-booking-engine-html-7 {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        @include flex_xy;
        width: calc(100% - 40px);

        .booking_form {
          max-width: 420px;
          width: 100%;
          flex-direction: column;
          flex-wrap: wrap;
          padding: 15px;
          border: none;
          background: #F8F7F3;

          .stay_selection {
            position: relative;
            display: inline-flex;
            width: 100%;
            border-bottom: 1px solid $corporate_1;
            padding-left: 50px;

            &::after {
              content: '\f073';
              @include center_y;
              left: 0;
              font-family: $fa;
              font-weight: 300;
              font-size: 25px;
              color: $corporate_1;
            }

            .entry_date_wrapper {
              border-right: 1px solid $corporate_1;
              padding: 20px 15px 20px 0;
            }

            .departure_date_wrapper {
              padding: 20px 0 20px 15px;
            }

            .entry_date_wrapper, .departure_date_wrapper {
              background-color: transparent;
              width: 50%;
              height: auto;

              label {
                margin-bottom: 15px;
              }


              &:after {
                display: none;
              }

              .date_box .date_day {
                color: $corporate_1;
              }
            }

            .departure_date_wrapper {
              &::after {
                display: none;
              }
            }

            &:before {
              left: 50% !important;
              transform: translateX(-100%);
            }
          }

          .rooms_number_wrapper {
            width: 100%;
            height: auto;
            background-color: transparent;
            padding: 40px 0 40px 50px !important;
            border-bottom: 1px solid $corporate_1;

            &:before {
              //bottom: 15px;
              content: "\f236";
              position: absolute;
              top: 50%;
              transform: translate(0%, -50%);
              left: 0;
              font-family: $fa;
              font-weight: 300;
              font-size: 25px;
              color: $corporate_1;
            }

            &:after {
              display: none;
            }

            .rooms_label {
              //font-size: 12px;
              //color: $corporate_1 !important;
              margin-bottom: 15px;
            }

            .selectricWrapper {
              width: 92%;

              &:before {
                font-size: 12px;
                color: $corporate_1;
              }

              .selectric {
                p.label {
                  //font-size: 28px;
                  &:before {
                    content: '\f107';
                    position: absolute;
                    transform: translate(0%, -50%);
                    top: 80%;
                    right: 0;
                    font-family: $fa;
                    font-weight: 300;
                    font-size: 45px;
                    color: $corporate_1;
                  }
                }
              }
            }
          }

          .guest_selector {
            position: relative;
            width: 100%;
            height: auto;
            background-color: transparent;
            padding: 40px 0 40px 50px;
            border-bottom: 1px solid $corporate_1;

            &::before {
              content: '\f0c0';
              @include center_y;
              left: 0;
              font-family: $fa;
              font-weight: 300;
              font-size: 25px;
              color: $corporate_1;
            }

            &:after {
              display: none;
            }

            label {
              margin-bottom: 15px;
            }

            .placeholder_text {
              position: relative;
              text-align: left;
              text-transform: none;
              width: 92%;

              &::before {
                content: '\f107';
                @include center_y;
                top: 80%;
                right: 0;
                font-family: $fa;
                font-weight: 300;
                font-size: 45px;
                color: $corporate_1;
              }
            }
          }

          .room_list_wrapper {
            z-index: 2;
            width: 100%;
            bottom: 250px;
            right: auto;
            left: 50%;
            top: 360px;
            padding: 0;
            @include transform(translateX(-50%));

            .room_list {
              padding: 0;
              background-color: white;
              box-shadow: 0px 10px 19px 0px rgba(0, 0, 0, 0.3);
              margin: 0;
              min-height: 100px;

              .full_ages_wrapper {
                label {
                  text-align: center;
                  font-size: 10px;
                }

                .kids_age_selection {
                  margin-left: 0;
                }
              }

              .room {
                margin: auto;
                justify-content: center;

                .adults_selector, .children_selector, .babies_selector {
                  > label {
                    font-size: 10px;
                    margin-top: 10px;
                    /* margin-bottom: 15px; */
                    min-height: 20px;

                    .range-age {
                      font-size: 9px;
                    }
                  }

                  .selectricWrapper {
                    &:before {
                      font-size: 10px;
                    }

                    .selectric {
                      .label {
                        font-size: 16px;

                        &:before {
                          margin-top: 0;
                        }
                      }

                      .button {
                        &:before {
                          margin-top: 0;
                        }
                      }
                    }
                  }
                }

                &.room_with_babies {
                  width: 100%;
                }

                &:first-of-type {
                  margin-bottom: 40px !important;
                  padding-top: 30px;

                  .room_title {
                    margin-top: 50px;
                  }
                }
              }
            }

            .add_remove_room_wrapper {
              background: white;
              margin-top: -10px;
            }
          }

          .wrapper_booking_button {
            display: block;
            width: 100%;
            position: relative;
            top: auto;
            bottom: auto;
            left: auto;
            right: auto;

            .promocode_wrapper {
              display: block;
              width: 100%;
              height: 60px;

              .promocode_input {
                @include full_size;
                @include flex_xy;
                @include promocode_styles;
                width: 100%;


                &::-webkit-input-placeholder {
                  @include promocode_styles;
                }

                &::-moz-placeholder {
                  @include promocode_styles;
                }

                &:-ms-input-placeholder {
                  @include promocode_styles;
                }

                &:-moz-placeholder {
                  @include promocode_styles;
                }
              }

              &:after {
                display: none;
              }
            }

            .submit_button {
              display: block;
              width: 100%;
              height: 60px;
              font-size: 16px;
              padding: 5px 0;
              margin: 0;
              top: 0;
              position: relative;

              &:before, &:after {
                display: none;
              }
            }
          }
        }
      }
    }

    .close_widget {
      position: fixed;
      top: 15px;
      right: 20px;
      text-align: right;
      color: $corporate_1;
      display: block;
      font-size: 24px;
      z-index: 5;
    }


    #floating_button {
      font-family: $text_family;
      display: inline-block;
      width: 100%;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      font-size: 20px;
      font-weight: 700;
      text-align: center;
      background-color: $corporate_1;
      color: white;
      border: 1px solid $corporate_1;
      cursor: pointer;
      z-index: 10;
      text-transform: uppercase;
      padding: 20px 10px;

      &.hidden {
        display: none !important;
      }
    }
  }


  .fancy-booking-search_v2 .container_popup_booking {
    max-width: 100%;
    width: 100%;

    img {
      width: 100%;
    }
  }
  .datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
    @include center_xy;
    top: 50% !important;
    left: 50% !important;
    position: fixed;
  }
  .absolute-footer {
    padding: 10px 0 60px;
  }

  .fancybox-opened.fancy-booking-search_v2 {
    left: 0 !important;
    width: 100% !important;

    .container_popup_booking {
      width: 60%;
      margin: 0 auto;

      .dots_loader {
        padding: 10px 0 30px;

        .dot {
          width: 17px;
          height: 17px;
        }
      }

      .description_bottom_popup_booking {
        font-size: 12px;
        font-weight: 700;
      }
    }
  }
}

@media screen and (max-width: 600px) {
  #widget_paraty {
    #full_wrapper_booking {
      #full-booking-engine-html-7 {
        .booking_form {
          max-width: 100%;
        }
      }
    }
  }
}
div#step-2 {
  background-color: white;

  .booking-2-service-container{
    .booking-2-service-price{
      span{
        color: $black2;
      }
    }
  }


  .additional_services_total_wrapper {
    .total_label, .total_prices {
      color: #383838;
    }

    .perform_additional_services_booking {
      background: transparent linear-gradient(90deg, #456ba7 0%, #0088cc 100%) 0 0 no-repeat padding-box;
      transition: all .6s;
      border-radius: 8px;

      &:hover {
        opacity: 0.7;
      }
    }
    @media only screen and (max-width: 1140px) {
      width: 1071px;
    }


  }
  .additional_services_total_wrapper {
    &.top:not(.fixed) {
      display: none;
    }

    &.fixed {
      .perform_additional_services_booking{
        right: calc((100% - 1120px) / 2);
        top: 5px;
        width: 190px;
      }
    }
  }

  .booking-2-service-container {
    .booking-2-container-description {
      height: auto !important;
      padding-bottom: 60px;

      .booking-2-service-title {
        font-family: $text_family;
        font-weight: 600;
        font-size: 22px;
        line-height: 1;
        text-transform: uppercase;
        color: $corporate_2;
        margin-left: 20px;
      }
      .booking-2-service-description{
        margin-bottom: 20px;
        .natural_height{
          color: $black2;
          font-size: 16px;
        }
      }
      .services_buttons_wrapper {
        .add_service_element,
        .remove_service_element {
          font-family: $title_family;
          //padding: 10px 5px;
          text-align: center;
          font-size: 18px;
          font-weight: bold;
        }

        .add_service_element {
          background-color: white;
          border: 1px solid $corporate_3;
          color: $corporate_3;
        }

        .remove_service_element {
          background-color: $corporate_2 !important;
        }
      }
    }

    .booking-2-service-price {
      .currencyValue.free {
        font-size: 40px;
        color: $corporate_3;
      }
    }
  }

  .booking_button_wrapper {
    .booking-button {
      background-color: $corporate_3;
      font-family: $title_family;
      font-weight: bold;
      letter-spacing: 2px;

    }
  }

  .all_additional_services_wrapper {
    border:.75px solid #CDCFD0;
    border-radius: 16px;
    margin-top: 10px;

    &.with_tabs_scroll {
      padding: 15px 40px;
      margin: 110px auto 25px;
      padding: 0 40px 29px;
      border-radius: 15px;
      border: 0.75px solid #CDCFD0;

      .additional_services_tabs_wrapper {
        position: relative;
        margin-bottom: 0;
        top: -80px;
        left: -40px;
        @media only screen and (max-width: 1140px) {
          left: -5px;
      }

        .additional_services_tabs_list {
          position: relative;

          &::before {
            display: none;
          }

          .additional_services_tab {
            transition: all .6s;
            border: 1px solid #000000;
            padding: 10px 15px;
            .main_title{
              font-weight: 400;
            }

            &.active, &:hover {
              border: 2px solid #000000;

              .main_title {
                font-weight: 500;
              }
            }

            &:hover {
              opacity: .7;
            }
          }
        }

        &.fixed {
          position: fixed;
          top: 0;
          left: 0;

          .additional_services_tabs_list {
            &::before {
              display: none;
            }

            .additional_services_tab {
              &.active {
                .main_title {
                  border-bottom: 1px solid $corporate-2;
                }
              }
            }
          }
        }
      }

      .category_wrapper {
        .title_category {
          .main_title {
            color: $corporate-2;

            small {
              font-weight: 500;
            }
          }
        }

        .additional_services_wrapper {
          .additional_service_element {
            background: transparent;
            border: 1px solid #707070;
            border-radius: 10px;

            &:hover {
              box-shadow: none;
            }

            .service_content {

              .service_title {
                margin-bottom: 10px;
                font-size: 20px;
              }
              .service_description {
                font-size:16px;
                div span{
                  font-size:16px !important;
                }
              }
            }

            .service_selection_wrapper {
              width: 25%;
              display: flex;
              justify-content: space-around;

              .price_service {
                color: #383838;
                width: 60%;
              }

              .add_service_button {
                i, .label {
                  color: $black2;
                }
              }
            }
          }
        }
        &.upgrade{
          .additional_service_element .service_selection_wrapper .quantity_selection_wrapper{
            display: none !important;
          }
        }
      }
    }
  }
}

.all_additional_services_wrapper .additional_service_element{
  &:before, &:after{
    background: $corporate_2;
  }
  &.selected{
    .service_selection_wrapper .add_service_button{
      i{
        font-weight: 100;
        color: $corporate_3 !important;
      }
      .remove_label{
        color: $corporate_3 !important;
      }
    }
  }
}

.full_service_popup_info .service_popup_info_description{
  font-size: 15px;
}

#booking2-app-root {
  .category-group {
    .category-services-wrapper {
      .additional-service-element {
        background: white;
        border-radius: 16px;
        border: 1px solid #707070;
        color: #161C21;

        label {
          font-family: $font_1;
          background: #F5F7FB;
          color: #4D4F5C;
        }

        input, select, option {
          font-family: $font_1;
          color: #4D4F5C
        }


        .add_service_button {
          .label {
            font-weight: 600;
          }

          .fa-circle-xmark:before {
            content: "\f00d";
            font-weight: 600;
            font-size: 22px;
            border-radius: 50%;
            border: 1px solid #C9C9C9;
            padding: 11px 15px;
          }
        }

        .service-title,
        .service-description,
        .price-service,
        .selection_title,
        .service-line-price,
        .subtotal-wrapper,
        .add-service-btn,
        .data-form-wrapper {
          font-family: $font_1;
          color: #161C21;
        }

        .data-form-wrapper {
          border: 1px solid #D7DAE2;
        }

        .service-content {
          border-right: 1px solid #D7DAE2;
        }

        .service-selection-wrapper{
          .signs_controlls{
            padding: 0 7px;
          }
        }

        .service-data-configuration {
          border-top: 1px solid #D7DAE2;

          .bottom-wrapper {
            border-top: 1px solid #D7DAE2;
          }

          .fa-trash {
            cursor: pointer;

            &::before {
              content: "\f2ed";
              color: #A4AFB7;
            }
          }
        }


        &:after {
          background: #F5F7FB;
          border-radius: 16px;
        }
      }
    }
  }
}

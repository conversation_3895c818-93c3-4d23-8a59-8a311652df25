.actual_wizard_step {
  width: 1140px;
  margin: 0 auto 30px;
  @include display_flex;
  .wizard-tab--big {
    width: 25%;
    a {
      padding-left: 0 !important;
      text-align: center;
      &:before {
        content: "\f236";
      }
    }
  }
  .wizard-tab--small, .wizard-tab--big {
    text-align: center;

    a {
      background: none !important;
      border: none !important;
      padding: 0;
      font-family: $text_family;
      font-weight: 400;
      font-size: 12px;
      letter-spacing: 0.95px;
      text-transform: uppercase;
      color: $black;

      &::before {
        position: relative;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        display: table !important;
        margin: 20px auto;
        width: 15px;
        line-height: 15px;
        background-color: white;
        border-radius: 50%;
        border: 1px solid rgba(black, 0.1);
        z-index: 2;

        font-family: "Font Awesome 5 Pro";
        font-weight: 300;
        font-size: 0px; // Hide icon but keep background
        color: white;
        text-align: center;
      }

      &::after {
        display: block !important;
        border: none !important;
      }
    }

    a.actual {
      &::before {
        width: 35px;
        line-height: 35px;
        margin: 10px auto;
        background-color: $corporate_2;
        border: none;
        font-size: 17px;
      }
    }
  }

  .wizard-tab--small:nth-of-type(1) {
    a::before {
      content: "\f236";
    }
  }

  .wizard-tab--small:nth-of-type(2) {
    a::before {
      content: "\f562";
    }
  }

  .wizard-tab--small:nth-of-type(3) {
    a::before {
      content: "\f505";
    }
  }

  .wizard-tab--small:nth-of-type(4) {
    a::before {
      content: "\f2f7";
    }
  }

  .wizard-tab--small + .wizard-tab--small, .wizard-tab--small + .wizard-tab--big {
    a:after {
      content: "";
      position: absolute;
      top: 8px;
      left: auto;
      right: 50%;
      width: 100%;
      height: 1px;
      background-color: rgba(black, 0.1);
      z-index: 1;
    }

    a.actual {
      &::after {
        top: 18px;
      }
    }
  }
}
#wizard.step_-1 {
  .actual_wizard_step {
    justify-content: center;
  }
}
from bouca_booking_widget import BoucaScript, BoucaWidgetHandler
from candano_booking_widget import candanoScript, candanoWidgetHandler
from tecina_booking_widget import TecinaScript, TecinaWidget
from utils.web.web_seeker_utils import WebSeekerHandler

applicationId = "secure-booking38"
template_handler = None


extra_routing = {
    '.*localhost.*': {
        '.*candanoscript.*': candanoScript,
        '.*candanowidget.*': candanoWidgetHandler,
        '.*tecinascript.*': TecinaScript,
        '.*tecinawidget.*': TecinaWidget,
        '.*': WebSeekerHandler,
    },
    '.*saylu.*': {
        '.*': WebSeekerHandler,
    },
    '.*bouca.*': {
        '.*boucascript.*': BoucaScript,
		'.*boucawidget.*': BoucaWidgetHandler,
        '.*': WebSeekerHandler,
    },
    '.*almeji.*': {
        '.*': WebSeekerHandler,
    },
    '.*berlin.*': {
        '.*': WebSeekerHandler,
    },
    '.*candano.*': {
        '.*candanoscript.*': candanoScript,
        '.*candanowidget.*': candanoWidgetHandler,
        '.*': WebSeekerHandler,
    },
    '.*(braga|terrocollection).*': {
        '.*': WebSeekerHandler,
    },
    '.*porto.*': {
        '.*': WebSeekerHandler,
    },
    '.*atarfe.*': {
        '.*': WebSeekerHandler,
    },
    '.*tecina.*': {
        '.*tecinascript.*': TecinaScript,
        '.*tecinawidget.*': TecinaWidget,
        '.*': WebSeekerHandler,
    },
    '.*miguel.*': {
        '.*': WebSeekerHandler,
    },
    '.*': {
        '.*': WebSeekerHandler
    }
}

server_type = "F2"

templates = ["express1"]

$mobile_height: 75px;
$mobile_padding: 15px 20px 0;

@media (max-width: 1367px) {
  #widget_paraty {
    top: 77vh;
  }
}

@media (max-width: 992px) {
  #widget_paraty {
    top: 49vh;
  }
}

@media (min-width:960px) {
  #widget_paraty {
    display: block !important;

    #full_wrapper_booking {
      display: inline-flex !important;
      justify-content: center;
    }
  }

  .datepicker_wrapper_element,
  .datepicker_wrapper_element_2,
  .datepicker_wrapper_element_3 {
    width: auto !important;
  }
}

@media (max-width: 959px) {
  .datepicker_wrapper_element .specific_month_selector,
  .datepicker_wrapper_element .go_back_button,
  .datepicker_wrapper_element_2 .specific_month_selector,
  .datepicker_wrapper_element_2 .go_back_button,
  .datepicker_wrapper_element_3 .specific_month_selector,
  .datepicker_wrapper_element_3 .go_back_button {
    display: none !important;
  }
  .datepicker_wrapper_element,
  .datepicker_wrapper_element_2,
  .datepicker_wrapper_element_3 {
    z-index: 10002;

    .ui-datepicker-group {
      width: 100% !important;
    }
  }

  #widget_paraty {
    z-index: 1000 !important;
    display: none;
    height: auto !important;

    &.auto-position {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      transform: none;
      -webkit-transition: none;
      -moz-transition: none;
      -ms-transition: none;
      -o-transition: none;
      transition: none;
    }

    #full_wrapper_booking {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: white;
      border-radius: 0;
      transform: none;
      height: 100vh !important;

      .custom_content{
        width:100%;
        bottom: 0;

        .widget_msg_wrapper{
          display:none;
        }
        .widget_buttons{
          bottom:0;
          width: 100%;
          padding: 10.5px 20px 14.5px;
        }
      }

      #full-booking-engine-html-7 {
        @include center_xy;
        top: calc(30% - 50px);
        width: 95%;
        text-align: center;
        border:none;

        .booking_form_title {
          .widget_top_links {
            top: 290px !important;

            a.custompromocode {
              position: absolute;
              left: 46%;
              transform: translate(-50%, 0%);

              &:hover {
                color: #444 !important;
                background-color: rgba(255, 255, 255, 0.8) !important;
              }
            }
          }
        }

        form.booking_form.paraty-booking-form {
          max-width: 400px;
          width: 100%;
          flex-direction: column;

          .destination_wrapper {
            width: 100% !important;
            padding: $mobile_padding;
            height: $mobile_height;

            label:before {
              right: 0;
            }
          }

          .hotel_selector {
            width: 100%;
            text-align: center;
          }

          .stay_selection {
            width: 90% !important;
            padding: $mobile_padding;
            height: $mobile_height;
            justify-content: space-around;
            align-items: center;
            border-bottom: 1px solid $separator_color;
            padding: 0;
            @include center_x;


            &:after{
              display:none;
            }

            label:before {
              right: 0;
            }

            .entry_date_wrapper, .departure_date_wrapper {
              .date_box {
                .date_day {
                  color: $corporate_3 !important;
                }
              }
            }

            .entry_date_wrapper {
              &::after {
                right: -32px;
              }
            }
          }

          .rooms_number_wrapper {
            width: 90% !important;
            padding: $mobile_padding;
            height: $mobile_height;
            align-items: center;
            border-bottom: 1px solid $separator_color;
            @include center_x;
            margin-top:75px;

            .rooms_label{
              text-align:center;
            }

            &:after {
              display: none;
            }

            .selectricWrapper {
              .selectricItems {
                top: auto;
                bottom: 50px;
                left: 50%;
                transform: translateX(-50%);
                width: calc(100% - 40px);
              }
            }

            .selectric {
              p.label {
                color: $corporate_3 !important;
                text-align: center;
              }
            }

            label:before {
              right: 0;
            }
          }

          .guest_selector {
            width: 90% !important;
            padding: $mobile_padding;
            height: $mobile_height;
            border-bottom: 1px solid $separator_color;
            align-items: center;
            @include center_x;
            margin-top:150px;

            &:after {
              display: none;
            }

            label:before {
              right: 0;
            }

            .placeholder_text {
              color: $corporate_3 !important;
            }
          }

          .room_list_wrapper {
            z-index: 2;
            width: calc(100% - 40px);
            left: 50%;
            transform: translateX(-50%);
            top: auto;
            bottom: -380px;

            .room_list {
              padding: 0;

              .full_ages_wrapper {
                label {
                  text-align: center;
                  font-size: 10px;
                }

                .kids_age_selection {
                  margin-left: 0;
                }
              }

              .room {
                width: auto !important;
                margin: auto;
                justify-content: center;

                .adults_selector, .children_selector, .babies_selector {
                  > label {
                    font-size: 10px;

                    .range-age {
                      font-size: 9px;
                    }
                  }

                  .selectricWrapper {
                    &:before {
                      font-size: 10px;
                    }

                    .selectric {
                      .label {
                        font-size: 16px;
                      }
                    }
                  }
                }

                &.room_with_babies {
                  width: 100%;
                }
              }
            }
          }

          .wrapper_booking_button {
            margin-top: 140px;
            display: inline-flex;
            flex-wrap: wrap;
            width: 100% !important;

            .promocode_wrapper {
              width: max-content;
              height: 60px;
              padding: 15px 35px 0;
              border-bottom: 1px solid $corporate_3;
              margin: 100px auto 20px;
              display: flex;

              .promocode_label {
                display: none;
              }

              input.promocode_input {
                width: 100%;
                @include promocode_styles(14px, $text_color_2);
                line-height: 30px;

                &::placeholder {
                  opacity: 1;
                  text-transform: none;
                  border-radius: 0;
                }

                &::-webkit-input-placeholder {
                  opacity: 1;
                  text-transform: none;
                  border-radius: 0;
                }

                &::-moz-placeholder {
                  opacity: 1;
                  text-transform: none;
                  border-radius: 0;
                }

                &:-ms-input-placeholder {
                  opacity: 1;
                  text-transform: none;
                  border-radius: 0;
                }

                &:-moz-placeholder {
                  opacity: 1;
                  text-transform: none;
                  border-radius: 0;
                }
              }

              &:after {
                display: none;
              }
            }

            .submit_button {
              width: auto;
              height: 58px;
              font-size: 22px;
              font-weight: bold;
              padding: 5px 0;
              top: 0;
              position: relative;
              background-color: $corporate_1;
              color: white;
              margin: 10px auto;

              &:before, &:after {
                display: none;
              }
            }

            &:before {
              display: none;
            }
          }
        }
      }
    }

    .close_widget {
      position: fixed;
      top: 25px;
      right: 25px;
      text-align: right;
      color: $corporate_1;
      display: block;
      font-family: "Font Awesome 5 Pro" !important;
      font-weight: 900 !important;
      font-size: 24px;
      cursor: pointer;
    }
  }

  #floating_button_paraty.auto-position {
    display: inline-block;
    width: 100%;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    background-color: $corporate_1;
    color: white;
    cursor: pointer;
    z-index: 100;
    font-family: $title_family;
    font-size: 24px;
    padding: 15px 10px;
    text-transform: uppercase;

    &.hidden {
      display: none !important;
    }
  }

  .fancy-booking-search_v2 .container_popup_booking {
    max-width: 100%;
    width: 100%;

    img {
      width: 100%;
    }
  }
  .datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
    top: 50% !important;
    left: 50% !important;
    bottom: auto !important;
    @include transform(translate(-50%, -50%) !important);

  }
  .absolute-footer {
    padding: 10px 0 60px;
  }
}

@media (max-width: 767px) {
  #widget_paraty #full_wrapper_booking #full-booking-engine-html-7 form.booking_form.paraty-booking-form .hotel_selector {
    bottom: auto;
    top: 70px;
    background: white;
    z-index: 2;
  }
}
.datepicker_wrapper_element,
.datepicker_wrapper_element_2,
.datepicker_wrapper_element_3 {
  border-radius: 0;
  border-top: 2px solid $lightgrey;
  margin-top: 0;
  @include transform(translateY(25px));

  &.datepicker_wrapper_up {
    @include transform(translateY(-38px));
    border-bottom: 1px solid $lightgrey;
  }

  .datepicker_ext_inf_sd, .datepicker_ext_inf_ed {
    font-family: $title_family;
    font-size: 16px;
    padding: 2px;
  }

  .header_datepicker {
    background: white !important;
    text-align: left !important;
    color: #424242 !important;
    border-bottom: 1px solid $lightgrey;
    margin: 0 !important;

    .specific_date_selector {
      text-align: left !important;
      font-size: 14px;
      font-family: $title_family;
      color: $text_color_1;
      letter-spacing: 0.6px;
      text-transform: uppercase;
      padding: 6px 6px 4px;
      font-weight: 400;

      &:before {
        display: none;
      }
    }

    .close_button_datepicker {
      border-width: 0;
      margin-top: 5px;

      &:before {
        @include center_xy;
        content: '\f073';
        top: 38%;
        color: $corporate_1;
        font-size: 18px;
        font-family: "Font Awesome 5 Pro", sans-serif;
        font-weight: 300;
      }
    }
  }

  .datepicker_ext_inf_sd, .datepicker_ext_inf_ed {
    .ui-widget-header {
      background: white !important;

      .ui-datepicker-title {
        color: $text_color_1 !important;
        font-style: italic;
        text-transform: none;
        font-weight: bold;
      }
    }

    .ui-datepicker-header {
      .ui-corner-all {
        background-color: transparent !important;

        &:before {
          @include center_xy;
          content: '\f107';
          font-family: "Font Awesome 5 Pro", sans-serif;
          font-size: 22px;
          font-weight: 400;
          color: $corporate_1;
        }

        span {
          background: transparent !important;
        }
      }
    }

    .ui-datepicker {
      background: transparent !important;

      thead th span {
        font-family: $title_family;
        font-weight: 700;
        color: $text_color_1;
        font-size: 16px;
      }

      td {
        border-color: white;
        width: auto !important;
        height: auto !important;
        border: 0 !important;
        background: none !important;

        &.ui-state-disabled {
          opacity: .15;
        }

        .ui-state-active, .ui-state-hover {
          color: white !important;
          background: transparent !important;

          &:before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background: $corporate_1;
            z-index: -1;
            height: 30px;
            width: 30px;
            border-radius: 20px;
            margin: auto;
          }
        }

        &.ui-datepicker-start_date {
          .ui-state-default {
            background: transparent !important;
            position: relative;

            &:before {
              content: '';
              position: absolute;
              top: 0;
              bottom: 0;
              left: 0;
              right: 0;
              background: $corporate_1;
              z-index: -1;
              height: 30px;
              width: 30px;
              border-radius: 20px;
              margin: auto;
              border: 0;
              @include transform(none);
            }
          }
        }

        &.highlight, &.last-highlight-selection {
          background: transparent !important;

          a {
            background: transparent !important;
          }
        }
      }
    }

    .ui-widget-content {
      .ui-state-active {
        color: white !important;
      }
    }
  }

  .specific_month_selector, .go_back_button {
    background-color: $corporate_1;
    color: white;
    font-size: 13px;
    letter-spacing: 0.4px;
    border-radius: 0;
    text-transform: none;
    text-decoration: none;

    strong {
      text-transform: uppercase;
      color: white;
    }
  }

  .ui-datepicker-group {
    box-sizing: border-box;
    min-height: 352px;

    &:first-of-type {
      border-right: 1px solid $lightgrey;
    }

    .ui-datepicker-header {
      margin: 0 3px;
      border-bottom: 1px solid $lightgrey !important;
    }
  }

  .current_nights_selector {
    display: none;
  }

  .ui-datepicker-calendar {
    margin-top: 10px !important;
  }

  .ui-state-default {
    font-size: 16px !important;
    font-family: $title_family;
    color: $text_color_1 !important;
  }
}

.datepicker_wrapper_element,
.datepicker_wrapper_element_2,
.datepicker_wrapper_element_3 {
  position: fixed !important;
  font-family: $text_family;
  top: auto !important;
  bottom: 100px;
  z-index: 1200;
  box-shadow: $box_shadow;
  @include transition(bottom, cubic-bezier(0.785, 0.135, 0.15, 0.86) 0.7s);

  .months_selector_container {
    width: 50%;
    margin: 0 auto;

    .cheapest_month_selector {
      background: $corporate_4;
    }
  }


  .header_datepicker {
    background-color: $corporate_1;
  }

  .ui-datepicker-header {
    background: transparent !important;
  }

  .ui-datepicker .ui-widget-header .ui-datepicker-title {
    color: $corporate_1 !important;
    font-family: $title_family;
    font-weight: 400;
  }

  .datepicker_ext_inf_sd,
  .datepicker_ext_inf_ed {
    .ui-widget-header {
      .ui-icon {
        background: url(/static_1/images/white_arrow.png?v=1) no-repeat center !important;
      }
    }

    .ui-widget-content {
      .ui-state-default {
        border: none !important;
      }
    }
  }
}

.datepicker_wrapper_element,
.datepicker_wrapper_element_2,
.datepicker_wrapper_element_3 {
  &.top {
    top: 60px !important;
    bottom: auto;
  }
}
$corporate_1_club: #005E81;
$corporate_2_club: #009EB5;

#login_wrapper_element.version_banner_v1.v5,
#logged_user_info_wrapper.version_banner_v1 {
  background: transparent;
  margin: 10px auto;
  width: calc(100% - 20px);
  border-radius: 20px;
  padding: 0;

  &::before {
    @include full_size;
    content: '';
    background-color: $corporate_2_club;
    opacity: 0.15;
    border-radius: 20px;
  }

  .content_login_wrapper {
    padding: 0 20px;
    background-color: transparent;
    position: relative;
    z-index: 2;
    margin: 0;
    display: flex;
    align-items: center;
    height: 110px;
    flex-direction: column;
    text-align: center;

    .logo_wrapper {
      background-color: transparent;
      width: 50%;
      float: none;
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      height: initial;
      padding: 0;
      margin: auto;

      img {
        position: relative;
        top: initial;
        left: initial;
        transform: none;
        min-width: initial;
        min-height: initial;
      }
    }

    .description_wrapper {
      margin-bottom: 20px;
      color: #005e81;
    }

    .overlay_element,
    .hidden_user_club_info,
    .square_plus {
      display: none;
    }

    .club_icons_wrapper, .button_wrapper {
      display: none;
    }
  }

  .users_buttons_wrapper {
    position: relative;
    z-index: 1;
    display: inline-flex;
    flex-direction: row-reverse;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 20px 20px;


    .join_button_wrapper,
    .already_member_wrapper {
      border-radius: 10px;
      width: calc(50% - 5px);
      height: 45px;
      font-family: $title_family;
      font-weight: 600;
      font-size: 11px;
      letter-spacing: .31px;
      text-transform: none;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .join_button_wrapper {
      background: linear-gradient(90deg, #005e81 0%, #009eb5 100%);
      color: white;
    }

    .already_member_wrapper {
      background-color: white;
      color: $corporate_1_club;
    }

  }

  .content_login_wrapper {
    height: 120px;
  }

  .content_logged_wrapper {
    background: transparent;

    .logged_user_text, .user_points .content_wrapper {
      color: #383838;
    }
  }
}

#logged_user_info_wrapper {
  .content_logged_wrapper {
    width: 70%;
    margin: 0;
    position: relative;

    .logged_user_text {
      padding: 10px 0 5px 35px;
      position: relative;
      text-align: left;
      border-color: #b9dae6;
      width: 100%;

      i {
        position: absolute;
        top: 10px;
        left: 0;
        color: $black;
      }

      .default_text {
        color: $black;
      }

      .username_text {
        display: block;
        font-weight: 500;
        color: $black;
      }
    }

    .user_category_image {
      padding: 10px 10px 10px 0;
      position: relative;
      border-color: #b9dae6;
    }

    .user_points {
      .content_wrapper {
        margin-top: 0;
        border-top: 0;

        span {
          color: $black;

          &.points_amount {
            font-weight: bold;
          }
        }
      }
    }
  }

  .logout_button_wrapper {
    background-color: $black;
    border-color: $black;

    i {
      font-size: 12px;
    }
  }
}

.modal_wrapper {
  #popup_login_information {
    .tabs_wrapper {
      padding: 0 15px;

      .register_tab,
      .login_tab {
        font-family: $text_family;
        font-size: 16px;
        letter-spacing: 0.8px;
        line-height: 19px;
        font-weight: 700;
        text-transform: uppercase;
        color: #444444;
        opacity: .44;

        &.active {
          background: white !important;
          border: 1px solid #444444;
          border-bottom-color: white;
          opacity: 1;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            background: white;
            bottom: -3px;
            left: 0;
            right: 0;
            height: 3px;
          }
        }
      }
    }
  }

  #signup_form, #login_form_wrapper_v1 {
    padding: 0 15px;
  }

  #login_form_wrapper_v1 {
    .main_form_title {
      display: none;
    }

    .login_block {
      padding: 0;

      .login_form_title {
        display: none;
      }

      .login_form {
        .input_block {
          padding: 7px 12px;

          label {
            font-family: $text_family;
            font-size: 15px;
            padding: 6px;
            letter-spacing: 0.82px;
            color: #444444;
          }

          input {
            &::placeholder {
              font-size: 13px;
            }

            &:-webkit-autofill,
            &:-webkit-autofill:hover,
            &:-webkit-autofill:focus {
              -webkit-box-shadow: 0 0 0px 1000px white inset;
              -webkit-text-fill-color: $color_text;
            }
          }
        }
      }
    }

    .club_send_password_wrapper {
      margin: 15px auto 20px;

      .content, .toggled_content {
        font-family: $text-family;
        font-weight: 500;
      }

      .toggled_content {
        font-weight: 400;
      }
    }

    .social_login_xee {
      position: relative;

      #facebook-login-xee, #google-login-xee {
        font-family: $text_family;
        font-size: 17px;
        letter-spacing: 0.37px;
        line-height: 20px;
      }

      &::before {
        content: "O";
        position: absolute;
        left: 50%;
        top: -2px;
        transform: translateX(-50%);
        width: 60px;
        height: 20px;
        background: white;
        font-family: $text_family;
        font-size: 13px;
        letter-spacing: 0.39px;
        line-height: 20px;
        color: #444444;
        text-align: center;
      }
    }
  }

  #register_form_wrapper_v1,
  #login_form_wrapper_v1 {
    .title_wrapper_block {
      background: white !important;
      border-top: 1px solid #444444;
      padding: 20px 0;

      .main_form_title {
        font-family: $text_family;
        font-weight: 500;
        text-transform: none;
        color: #444444;
      }

      .subtitle_register {
        color: #444444;
        font-weight: 300;
      }
    }

    .buttons_wrapper_signup {
      width: 100%;
    }

    .buttons_wrapper_signup .sign_up_button,
    .buttons_wrapper_signup .user_modification_button,
    .login_button_element {
      background: transparent linear-gradient(270deg, #009EB5 0%, #004F5B 100%) 0% 0% no-repeat padding-box;
      color: white;
      font-family: $text_family;
      font-size: 18px;
      letter-spacing: 0.4px;
      line-height: 21px;
      font-weight: 500;
      text-transform: none;
      border-radius: 3px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0 35px;
      height: 50px;
      width: 100%;
      border: 0;
    }

    .lopd_wrapper {
      display: none;
    }

    #user_info_form {
      padding: 0;

      .input_block {
        padding: 8px 0 !important;
      }
    }

    .promotions_checkbox {
      margin: 20px 10px;

      input {
        border: 1px solid $grey-1;
        width: 26px;
        height: 12px;

        &::before {
          width: 9px;
          height: 9px;
        }
      }
    }
  }

  #register_form_wrapper_v1 {
    .input_block.address, .input_block.city {
      display: none !important;
    }

    input {
      &:-webkit-autofill,
      &:-webkit-autofill:hover,
      &:-webkit-autofill:focus {
        -webkit-box-shadow: 0 0 0px 1000px white inset;
        -webkit-text-fill-color: $color_text;
      }
    }
  }
}


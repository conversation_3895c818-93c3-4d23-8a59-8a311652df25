{{widget_controller_injection|safe}}

bookingWidgetController.config.avoid_guest_autoclose_click = true;

bookingWidgetController.add_widget_html = function () {
    var paraty_widget = bookingWidgetController.config.widget_html;
    if (!$("#widget_paraty").length) {
        $("body").append($("<div id='widget_paraty'></div>"));
        $("#widget_paraty").addClass('auto-position');
    }
    $("#widget_paraty").html(paraty_widget);
    if ($("#widget_paraty input#extra_widget_class").length)
        $("body").addClass($("#widget_paraty input#extra_widget_class").val());
    $("#widget_paraty #full_wrapper_booking .booking_form_title").append($(".cancel_booking_link"));
    if ($("#widget_paraty .destination_wrapper").length) {
        $("#widget_paraty").addClass('has-hotel-selector');
    }
    $("#widget_paraty .booking_steps").detach().appendTo($('body'));

    customizeFlightHotel();
};

bookingWidgetController.add_button_mobile_version_html = function () {
    if (!$("#floating_button_paraty").length) {
        $("body").append($("<div id='floating_button_paraty'></div>").html($.i18n._("reserva_ahora")));
        $("#floating_button_paraty").addClass('auto-position');
    }
    $("#widget_paraty #full_wrapper_booking").append($("<i class='fa fa-times close_widget'></i>"));
}

bookingWidgetController.custom_format_date = function (dateComponents) {
    dateComponents = dateComponents.split("/");
    var html_date = "%d %m";

    let date = new Date();
    date.setMonth(dateComponents[1] - 1);

    let month = date.toLocaleString([], {
        month: 'short',
    });

    month = month.charAt(0).toUpperCase() + month.slice(1);

    return html_date.replace("%d", dateComponents[0]).replace("%m", month);
};

bookingWidgetController.occupancy_format_html = function () {
    return "<span class='adults'>@@N_A@@</span><span class='kids'>/@@N_C@@</span><span class='babies'>/@@N_B@@</span>";
};

bookingWidgetController.adding_room_tag_selector = function () {
    $("select.rooms_number option").each(function (index, element) {
        $(element).text($(element).text());
    });
    $("select.rooms_number").selectric("refresh");
};

bookingWidgetController.open_widget = function () {
    $("#floating_button_paraty").click(function (e) {
        e.preventDefault();
        $("#widget_paraty").fadeToggle();
    });
};

bookingWidgetController.close_widget = function () {
    $("i.fa-times.close_widget").click(function () {
        $("#widget_paraty").fadeOut();
    });
};

bookingWidgetController.bind_analytics_listeners = function () {
    let widget = $("#widget_paraty");

    widget.find('.widget_buttons a.button').on('click auxclick', function (e) {
        let datalayer_name = $(this).attr('data-datalayer-name');

        if (datalayer_name) {
            bookingWidgetController.send_datalayer_event({
                event: 'book now',
                service: datalayer_name
            });
        }
    });

    $('#widget_paraty .destination_wrapper, .booking_steps .step_1').on('click', function () {
        bookingWidgetController.send_datalayer_event({
            event: 'book now',
            service: 'hotel',
            title: 'paso 1'
        });
    });

    $('#widget_paraty .hotel_selector_inner li, .booking_steps .step_2').on('click', function () {
        if (!$(this).hasClass('current_step')) {
            let hotel_name = bookingWidgetController.get_selected_hotel_for_datalayer();

            if (hotel_name) {
                bookingWidgetController.send_datalayer_event({
                    event: 'book now',
                    service: 'hotel',
                    title: 'paso 2',
                    hotel: hotel_name
                });
            }
        }
    });

    $('.booking_steps .step_3').on('click', function () {
        if (!$(this).hasClass('current_step')) {
            let hotel_name = bookingWidgetController.get_selected_hotel_for_datalayer(),
                room_name = bookingWidgetController.get_selected_room_for_datalayer(),
                event_body = {
                    event: 'book now',
                    service: 'hotel',
                    title: 'paso 3',
                    hotel: hotel_name
                };

            if (room_name) {
                event_body.room = room_name;
            }


            if (hotel_name) {
                bookingWidgetController.send_datalayer_event(event_body);
            }
        }
    });

    widget.find(".wrapper_booking_button .submit_button").click(function () {
        if ($(".destination_fieldo input[name=destination]").val() !== "") {
            let hotel_name = bookingWidgetController.get_selected_hotel_for_datalayer(),
                room_name = bookingWidgetController.get_selected_room_for_datalayer(),
                occupancy = bookingWidgetController.get_selected_occupancy_for_datalayer(),
                promocode = $(this).closest('.wrapper_booking_button').find('.promocode_input').val(),
                event_body = {
                    event: 'book now',
                    service: 'hotel',
                    title: 'paso 4',
                    type: occupancy,
                    hotel: hotel_name
                };

            if (room_name) {
                event_body.room = room_name;
            }

            if (promocode) {
                event_body.coupon = promocode;
            }

            if (hotel_name) {
                bookingWidgetController.send_datalayer_event(event_body);
            }
        }
    });
};

bookingWidgetController.send_datalayer_event = function (data) {
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push(data);
};

bookingWidgetController.get_selected_hotel_for_datalayer = function () {
    let widget = $("#widget_paraty"),
        selected_namespace = widget.find('#namespace').val();

    if (!selected_namespace) {
        return
    }

    let datalayer_name = widget.find(`.hotel_selector .hotel_selector_option.${selected_namespace}`).attr('data-datalayer-name');

    if (datalayer_name) {
        return datalayer_name;
    } else {
        return '';
    }
};

bookingWidgetController.get_selected_room_for_datalayer = function () {
    let widget = $("#widget_paraty"),
        room_name = widget.find('#calendar-app-root .filter_selector .selector_label').text(),
        room_options = [];

    widget.find('#calendar-app-root .filter_selector .selector .option').each(function () {
        let option_text = $(this).text();
        if (option_text) {
            room_options.push(option_text);
        }
    });

    if (room_options.includes(room_name)) {
        return room_name.toLowerCase();
    } else {
        return '';
    }
};

bookingWidgetController.get_selected_occupancy_for_datalayer = function () {
    let widget = $("#widget_paraty"),
        num_rooms = widget.find('.booking_form [name="numRooms"]').val(),
        occupancies = [];

    if (num_rooms) {
        num_rooms = parseInt(num_rooms);
        for (let i = 1; i <= num_rooms; i++) {
            let room_wrapper = widget.find(`.room_list .room${i}`),
                adults = room_wrapper.find(`#adultsRoom${i}`).val(),
                kids = room_wrapper.find(`#childrenRoom${i}`).val(),
                babies = room_wrapper.find(`#babiesRoom${i}`).val(),
                occupancy_string = adults;

            if (kids) {
                occupancy_string += `,${kids}`;
            }

            if (babies) {
                occupancy_string += `,${babies}`;
            }

            occupancies.push(occupancy_string);
        }

        return occupancies.join(';');
    }
    return '';
};

bookingWidgetController.floating_widget = function(){
    var actual_position = $(window).scrollTop(),
        widget = $("#widget_paraty"),
        window_width = $(window).innerWidth();

    if (window_width >= 1280 && actual_position > 840) {
        $('body').addClass('widget_top');
        widget.addClass("top");
    } else {
        $('body').removeClass('widget_top');
        widget.removeClass("top");
    }
};

bookingWidgetController.custom_functions = function () {
    let widget = $("#widget_paraty");

    bookingWidgetController.config.languages = {
        "es": "SPANISH",
        "en": "ENGLISH"
    };

    bookingWidgetController.floating_widget();

    $(window).scroll(bookingWidgetController.floating_widget);

    //Get fontawesome 6
    var script_fa_tag = document.createElement('script');
    script_fa_tag.src = 'https://kit.fontawesome.com/d8b9925505.js';
    script_fa_tag.setAttribute("defer", "");
    document.head.appendChild(script_fa_tag);

    booking_engine_controller();
    prepare_guests_selector();
    set_occupancy_number();
    room_selector_dates();
    init_calendar();
    hotel_preselected();

    $("#full_wrapper_booking .children_selector select.room_selector").change(function () {
        check_kids_ages($(this));
    });
    if ($("#booking").length) {
        $("#full_wrapper_booking .kidAgesSelect").selectric();
    }

    let hotel_preselection = widget.attr('data-hotel-preselection');
    if (hotel_preselection) {
        $("#widget_paraty .hotel_selector_option#" + hotel_preselection).click();
    }

    let custom_source = widget.attr('data-source');
    if (custom_source) {
        let source_input = $('<input type="hidden" name="source">').val(custom_source);
        widget.find('.paraty-booking-form').prepend(source_input);
    }

    individual_hotel_personalization();

    bookingWidgetController.bind_analytics_listeners();
};

function hotel_preselected() {
    let url = window.location.href;
    let individual_hotel_url = url.substring(url.lastIndexOf("/", url.lastIndexOf("/") - 1));
    individual_hotel_url = individual_hotel_url.replace(/\//g, '');
    console.log('hotel_code = ' + individual_hotel_url);

    let hotel_namespace = '';
    const hotel_equivalencies = {
        'exmo-hotel': 'olivia-exmo',
        '1872-river-house': 'olivia-river1872',
        'marques-garden-house': 'olivia-marques'
    }

    $.each(hotel_equivalencies, function (key, value) {
        if (individual_hotel_url === key) {
            hotel_namespace = value;
        }
    });

    if (hotel_namespace) {
        $('#widget_paraty #namespace').val(hotel_namespace);
        $("#widget_paraty .hotel_selector_option").each(function () {
            let hotel_id = $(this).attr('id');
            let hotel_title_selector = $(this).find('.title_selector').html();

            if (hotel_namespace === hotel_id) {
                $('#widget_paraty .destination').attr('placeholder', hotel_title_selector);
                $("#widget_paraty .destination_fieldo input[name=destination]").val(hotel_title_selector);
            }
        })
    }
}

function init_calendar() {
    window.calendar_data.change_date_callback = function (date, isStartDateSelection) {
        function format(inputDate) {
            let date, month, year;

            date = inputDate.getDate();
            month = inputDate.getMonth() + 1;
            year = inputDate.getFullYear();

            date = date.toString().padStart(2, '0');
            month = month.toString().padStart(2, '0');

            return `${date}/${month}/${year}`;
        }

        let widget_form_wrapper = $('#full_wrapper_booking .paraty-booking-form');
        let date_formated = format(date);
        let day_month_format = $.datepicker.formatDate('dd M', date);
        let year_format = $.datepicker.formatDate('yy', date);

        if (isStartDateSelection) {
            widget_form_wrapper.find('input[name=startDate]').val(date_formated);
            widget_form_wrapper.find('.entry_date .date_day').html(day_month_format);
            widget_form_wrapper.find('.entry_date .date_year').html(year_format);
        } else {
            widget_form_wrapper.find('input[name=endDate]').val(date_formated);
            widget_form_wrapper.find('.departure_date .date_day').html(day_month_format);
            widget_form_wrapper.find('.departure_date .date_year').html(year_format);
            $(".booking_steps .step_3").trigger('click');
            $('.booking_steps .step_2').addClass('done');
        }

        load_new_room_dates();
    };
}

function prepare_guests_selector() {

    $("select.room_selector").unbind("change");
    $(".room_selector").selectric('destroy');
    $(".room_selector").selectric({disableOnMobile: false});
    $("select.room_selector, select.rooms_number").change(function () {
        set_occupancy_number();
    });

    $(".remove_room_element").click(function () {
        var actual_room_numbers = $("select.rooms_number").val();
        if (actual_room_numbers > 1) {
            var target_room_number = parseInt(actual_room_numbers) - 1;
            $("select.rooms_number option").removeAttr('selected');
            $("select.rooms_number option[value='" + target_room_number + "']").attr('selected', 'selected');
            $(".room" + actual_room_numbers).hide();
            $("select.rooms_number").val(target_room_number);
            $("select.rooms_number").selectric("refresh");
        }
        set_occupancy_number()
    });

    var add_room_html = "<div class='add_room'>" + $.i18n._('T_add_new_room') + "</div>",
        remove_room_html = "<div class='remove_room'>" + $.i18n._('T_remove_room') + "</div>",
        close_btn = "<div class='icon-xcross close_guest_selector'></div>",
        close_calendar_btn = "<div class='icon-xcross close_calendar_app'></div>",
        booking_btn = "<div class='wrapper_booking_button_guest'><div class='promocode_wrapper'>" +
            "<input autocomplete='off' type='text' class='promocode_input' placeholder='" + $(".wrapper_booking_button .promocode_input").attr("placeholder") + "' name='promocode' value='' tabindex='16'>" +
            "</div><button type='button' class='submit_button popup_button'>" + $(".wrapper_booking_button .submit_button").html() + "</button></div>";
    $(".room_list_wrapper").append(close_btn);
    $(".calendar_root_wrapper").append(close_calendar_btn);
    $(".room_list_wrapper .room_list .room").each(function () {
        $(this).append(add_room_html);
    });
    $(".room_list_wrapper .room_list .room2").append(remove_room_html);
    $(".room_list_wrapper .room_list .room3").append(remove_room_html);
    $(".add_room").click(add_room);
    $(".remove_room").click(remove_room);

    $(".room_list_wrapper .room_list").append(booking_btn);
    $(".wrapper_booking_button_guest .submit_button").click(function () {
        var promocode_val = $(".wrapper_booking_button_guest .promocode_input").val(),
            promocode_original = $(".wrapper_booking_button .promocode_input").val();
        if (promocode_val != '' && promocode_val != promocode_original) {
            $(".wrapper_booking_button .promocode_input").val(promocode_val)
        }
        $(".wrapper_booking_button .submit_button").click();
    });

    $(document).on("click", ".close_guest_selector, .close_calendar_app", function () {
        resetStepSelection();
        $("#full_wrapper_booking").removeClass("fixed");
        $("#widget_paraty").removeClass("open");
        $(".booking_steps").hide();
        $("body").removeClass("widget_paraty_open");
    });

    $(".adults_selector .selectric-room_selector .label").click(function () {
        change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 8, 0, 1);
    });
    $(".adults_selector .selectric-room_selector .button").click(function () {
        change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 9, 1, -1);
    });
    $(".children_selector .selectric-room_selector .label").click(function () {
        change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 4, -1, 1);
    });
    $(".children_selector .selectric-room_selector .button").click(function () {
        change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 5, 0, -1);
    });
    $(".babies_selector .selectric-room_selector .label").click(function () {
        change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 4, -1, 1);
    });
    $(".babies_selector .selectric-room_selector .button").click(function () {
        change_selectric_rooms($(this), ".selectric-room_selector", "select.room_selector", 5, 0, -1);
    });
}

function change_selectric_rooms(element, parent_class, select, max, min, operator) {
    var selectric_element = element.closest(parent_class).find(select),
        label_for = element.closest(".range_label_enabled");
    if (parseInt(selectric_element.val()) > min &&
        parseInt(selectric_element.val()) < max) {
        var new_select_val = parseInt(selectric_element.val()) + operator;
        selectric_element.val(new_select_val);
        selectric_element.selectric('refresh');
        set_occupancy_number();
    }
}

function add_room() {
    var number_rooms = parseInt($("select.rooms_number").val());
    if (number_rooms < 3) {
        $($(".selectric-rooms_number .selectricItems li").get(number_rooms)).trigger("click");
        set_occupancy_number();
    }

    if (number_rooms == 1) {
        $(".room1 .add_room").hide();
    }

    if (number_rooms == 2) {
        $(".add_room").hide();
        $(".room2 .remove_room").hide();
    }
}

function remove_room() {
    var number_rooms = parseInt($("select.rooms_number").val());
    if (number_rooms > 1) {
        $($(".selectric-rooms_number .selectricItems li").get(number_rooms - 2)).trigger("click");
        set_occupancy_number();
    }

    if (number_rooms == 2) {
        $(".room1 .remove_room").show();
        $(".room1 .add_room").show();
    }

    if (number_rooms == 3) {
        $(".room2 .remove_room").show();
        $(".room2 .add_room").show();
    }

    $("select.rooms_number").change(function (event) {
        var number = $(this).val(),
            _room1 = $(".room1"),
            _room2 = $(".room2"),
            _room3 = $(".room3"),
            _room_age1 = $(".room_ages_1"),
            _room_age2 = $(".room_ages_2"),
            _room_age3 = $(".room_ages_3");

        if (number == 1) {
            _room2.hide().promise().done(function () {
                _room2.css("overflow", "initial");
            });
            _room_age2.removeClass("show")
            _room_age3.removeClass("show")
            _room3.hide().promise().done(function () {
                _room3.css("overflow", "initial");
            });
            $(".horizontal_engine").css("height", "379px");
        } else if (number == 2) {
            _room2.show("fast").promise().done(function () {
                _room2.css("overflow", "initial");
            });
            _room_age3.removeClass("show")
            _room3.hide().promise().done(function () {
                _room3.css("overflow", "initial");
            });
            $(".horizontal_engine").css("height", "449px");
        } else {
            _room2.show("fast").promise().done(function () {
                _room2.css("overflow", "initial");
            });
            _room3.show().promise().done(function () {
                _room3.css("overflow", "initial");
            });
            $(".horizontal_engine").css("height", "518px");
        }
    });
}

function toggle_guest_selector() {
    var target_room_wrapper = $(".room_list_wrapper");
    if (!target_room_wrapper.hasClass('active')) {
        target_room_wrapper.addClass('active');
        target_room_wrapper.show();
        console.log("showing");
    } else {
        target_room_wrapper.removeClass('active');
        target_room_wrapper.hide();
    }
    set_occupancy_number();
}

function set_occupancy_number(){
   var number_of_rooms = parseInt($("select[name='numRooms']").val()),
       adults_number = 0,
       kids_number = 0,
       babies_number = 0;

   if (number_of_rooms){
      for (var room_loop = 1;room_loop <= number_of_rooms;room_loop++){
         var actual_select_adults = $("select[name='adultsRoom" + room_loop + "']").val(),
             actual_select_kids = $("select[name='childrenRoom" + room_loop + "']").val(),
             actual_select_baby = $("select[name='babiesRoom" + room_loop + "']").val();

         if(actual_select_adults || actual_select_kids){
            adults_number += parseInt(actual_select_adults);
            kids_number += parseInt(actual_select_kids);
            babies_number += parseInt(actual_select_baby);
         }
      }
   }

   var target_placeholder = $(".guest_selector .placeholder_text");

   adults_number = parseInt(adults_number);
   kids_number = parseInt(kids_number);
   babies_number = parseInt(babies_number);

   placeholder_string = "<span class='guest_adults'>" + adults_number + "</span>";

   if(!$(".adults_only_selector").length){
      placeholder_string += "/ " + kids_number;
   }

   if($(".babies_selector").length) {
      placeholder_string += " / " + babies_number;
   }

   target_placeholder.html(placeholder_string);
}

bookingWidgetController.after_load_booking_script = function () {
    _set_datepicker_regional($);
    $(".room_selector").selectric({disableOnMobile: false});
    $(".rooms_number").selectric({disableOnMobile: false});
    bookingWidgetController.prepare_guest_selector();
    bookingWidgetController.adding_room_tag_selector();
    bookingWidgetController.set_occupancy_number();
    bookingWidgetController.update_date_by_timezone();
};

bookingWidgetController.datepicker_configuration = function () {
    let is_mobile = ($(window).width() <= 1140);
    DP_extend_info.config.booking_version = '5';
    DP_extend_info.config.hotel_path_endpoint = bookingWidgetController.config.base_url;
    DP_extend_info.config.months_show = (is_mobile) ? 1 : 2;
    DP_extend_info.config.months_show_highlight = true;
    DP_extend_info.config.force_hightlight = true;

    DP_extend_info.config.custom_format_day_month = function (dateComponents) {
        dateComponents = dateComponents.split("/");
        var month_short = $.datepicker._defaults['monthNamesShort'][parseInt(dateComponents[1], 10) - 1];
        return "<span class='day'>" + dateComponents[0] + "</span><span class='month'>" + month_short + "</span>";
    };

    DP_extend_info.init();
};

function room_selector_dates() {
    var room_list_wrapper = $('.room_list_wrapper .room_list'),
        dates_wrapper = $('<div class="room_info_wrapper"><div class="hotel_name_rooms"></div><div class="dates_wrapper"></div></div>');

    if (room_list_wrapper.length) {
        room_list_wrapper.prepend(dates_wrapper);
    }
    $('.dates_wrapper, .stay_selection').on('click', function () {
        $(".booking_steps .step_2").trigger('click');
    });
}

function check_kids_ages(select_element) {
    var parent_list = select_element.closest("li"),
        selected_value = select_element.val(),
        target_age_selector = parent_list.next(".full_ages_wrapper"),
        childs_elements = target_age_selector.find(".kid_age_element_wrapper"),
        target_childs_elements = childs_elements.slice(0, parseInt(selected_value));

    if (parseInt(selected_value)) {
        childs_elements.css('display', 'none');
        target_childs_elements.css('display', 'block');
        target_age_selector.slideDown(function () {
            $(this).css("overflow", "inherit");
        });
    } else {
        childs_elements.css('display', 'none');
        target_age_selector.slideUp(function () {
            $(this).css("overflow", "inherit");
        });
    }
}

function load_new_room_dates() {
    if ($('.dates_wrapper').length) {
        var start_date = $.datepicker.parseDate("dd/mm/yy", $("input[name=startDate]").val()),
            start_date_format = $.datepicker.formatDate("dd MM", start_date),
            end_date = $.datepicker.parseDate("dd/mm/yy", $("input[name=endDate]").val()),
            end_date_format = $.datepicker.formatDate("dd MM", end_date),
            hotel_name = $('.destination_wrapper .destination').val();

        $(".room_info_wrapper .dates_wrapper").html(start_date_format + ' - ' + end_date_format);
        if (hotel_name) {
            $(".room_info_wrapper .hotel_name_rooms").html(hotel_name).addClass('with_name');
        }
    }
}

function booking_engine_controller() {
    let widget = $("#widget_paraty"),
        booking_form = widget.find('.booking_form'),
        hotel_preselection = widget.attr('data-hotel-namespace'); // TODO

    $(".destination_wrapper").click(function () {
        resetStepSelection();

        $(".hotel_selector").slideDown();
        $(".booking_steps .step_1").addClass("current_step");
        $("#widget_paraty").addClass("open");
        $('.booking_steps').show();
        $("body").addClass("widget_paraty_open");
        $("#full_wrapper_booking").addClass("fixed");
        load_new_room_dates();

        if ($("#filter_selector").length) {
            _filter_selector($(this));
        }
    });

    widget.find(".hotel_selector_option").click(function () {
        widget.find(".hotel_selector").hide("fast");
        widget.find(".hotel_selector_option").removeClass("selected");
        $(this).addClass("selected");

        let new_placeholder_html = $(this).find(".title_selector").html().replace("<br>", " ").replace("&amp;", "&").replace(/<.*?>/g, ""),
            id_hotel_4_url = $(this).attr("id"),
            new_namespace = widget.find("#namespace_" + id_hotel_4_url).val(),
            url_booking = widget.find("#url_booking_" + id_hotel_4_url).val(),
            hotel_value = url_booking.indexOf('http') > -1 ? url_booking : "https://" + id_hotel_4_url + url_booking;

        //Change calendar namespace
        window.calendar_data.update_namespace_calendar(new_namespace);
        $('.price_calendar_wrapper .toggle_chart').css('opacity', '1').css('pointer-events', 'auto');

        //change the value
        widget.find(".destination").val(new_placeholder_html);
        booking_form.attr("action", hotel_value).find("#namespace").val(new_namespace);
    });

    //$(".step_flight").on("click", FlightHotelWidgetController.openAirportSelectorWrapper);

    $(document).on("click", ".booking_steps .step_1", function () {
        resetStepSelection();

        $(".hotel_selector").slideDown();
        $(".booking_steps .step_1").addClass("current_step");
        $("#widget_paraty").addClass("open");
        $('.booking_steps').show();
        $("body").addClass("widget_paraty_open");
        $("#full_wrapper_booking").addClass("fixed");
        load_new_room_dates();
    });

    $(document).on("click", ".booking_steps .step_2:not(.current_step)", function () {
        resetStepSelection();
        $(".calendar_root_wrapper").slideDown();
        $(".booking_steps .step_2").addClass("current_step");
        $("#widget_paraty").addClass("open");
        $('.booking_steps').show();
        $("body").addClass("widget_paraty_open");
        $("#full_wrapper_booking").addClass("fixed");
        load_new_room_dates();
    });

    $(document).on("click", ".booking_steps .step_3:not(.current_step)", function () {
        $(".guest_selector").click();
    });

    $(".guest_selector").click(function () {
        $(".booking_steps").find(".step_flight, .step_1, .step_2, .step_3").removeClass("current_step");
        $(".hotel_selector").slideUp();
        $(".calendar_root_wrapper").slideUp();
        $(".airport_selector_container").slideUp();
        $(".room_list_wrapper").slideDown();
        $(".booking_steps .step_3").addClass("current_step");
        $("#widget_paraty").addClass("open");
        $('.booking_steps').show();
        $("body").addClass("widget_paraty_open");
        $("#full_wrapper_booking").addClass("fixed");
        load_new_room_dates();
    });

    $(".hotel_selector_inner li").click(function (e) {
        $(".booking_steps .step_1").removeClass("current_step");
        $('.booking_steps .step_1').addClass('done');
        if (!$("#widget_paraty").hasClass("dates_selected")) {
            $("#widget_paraty").addClass("open");
            $('.booking_steps').show();
            $("body").addClass("widget_paraty_open");
            $(".booking_steps .step_2").addClass("current_step");
            $(".calendar_root_wrapper").slideDown();
            load_new_room_dates();
        } else {
            if ($("#full_wrapper_booking").hasClass("fixed")) {
                $(".hotel_selector").hide();
                $(".booking_steps .step_2").removeClass("current_step");
                $("body").addClass("widget_paraty_open");
                if (!$('.calendar_app').is(':visible')) {
                    $("#widget_paraty").addClass("open");
                    $('.booking_steps').show();
                    $("body").addClass("widget_paraty_open");
                    $(".booking_steps .step_3").addClass("current_step");
                    $(".room_list_wrapper").slideDown();
                    load_new_room_dates();
                }
            }
        }
    });

    $(".hotel_selector .booking_0_hotel_selection").click(function () {
        $(".hotel_selector").slideUp();
        $(".booking_steps .step_1").removeClass("current_step");
        $('.booking_steps .step_1').addClass('done');
        $("#widget_paraty").addClass("open");
        $('.booking_steps').show();
        $("body").addClass("widget_paraty_open");
        $(".booking_steps .step_2").addClass("current_step");
        $(".calendar_root_wrapper").slideDown();
        window.calendar_data.update_namespace_calendar("");
        $('.price_calendar_wrapper .toggle_chart').css('opacity', '0.6').css('pointer-events', 'none');
        let new_placeholder_html = $(this).find('.title_booking0').html().trim();
        widget.find(".destination").val(new_placeholder_html);
    });

    $(".close_button_datepicker").unbind("click");
    $(".close_button_datepicker").click(function () {
        $("#widget_paraty").removeClass("open");
        $('.booking_steps').hide();
        $("body").removeClass("widget_paraty_open");
        $("#full_wrapper_booking").removeClass("fixed");
        $(".booking_steps .step_2").removeClass("current_step");
    });

    $(".hotel_selector .close").click(function (e) {
        e.preventDefault();
        $(this).toggleClass("active");
        $(".hotel_selector").slideUp();
        $("booking_steps .step_1").removeClass("current_step");
        $("#widget_paraty").removeClass("open");
        $('.booking_steps').hide();
        $("body").removeClass("widget_paraty_open");
        $("#full_wrapper_booking").removeClass("fixed");
    });

    $(".airport_selector_container .close").on("click", function (e) {
        e.preventDefault();
        resetStepSelection();
        $("#full_wrapper_booking").removeClass("fixed");
        $("#widget_paraty").removeClass("open");
        $(".booking_steps").hide();
        $("body").removeClass("widget_paraty_open");
    });

    $(".wrapper_booking_button .submit_button").click(function () {
        if ($(".destination_fieldo input[name=destination]").val() !== "") {
            $(".booking_steps .step_1, .booking_steps .step_2, .booking_steps .step_3").removeClass("current_step");
            $(".close_button_datepicker").click();
            $(".close_room_selector").click();
        } else {
            $(".booking_steps .step_1").click();
        }
    });
}

function individual_hotel_personalization() {
    const url_host = window.location.host, individuals_url = {
        "exmo-hotel.com": "olivia-exmo",
        "1872-river-house.com": "olivia-river1872",
        "marques-garden-house.com": "olivia-marques"
    }

    if (url_host !== "oliviahouses.com.com" && url_host !== 'localhost') {
        if (url_host in individuals_url) {
            let option = $("#widget_paraty .hotel_selector_option#" + individuals_url[url_host]);
            select_hotel_selector(option);

            $(".booking_steps .step_1").hide();
            $(".room_list_wrapper .room_list .hotel_name_rooms").hide();
        }
    }
}

function select_hotel_selector(option) {
    let widget = $("#widget_paraty");

    widget.find(".hotel_selector").hide("fast");
    widget.find(".hotel_selector_option").removeClass("selected");
    option.addClass("selected");

    let new_placeholder_html = option.find(".title_selector").html().replace("<br>", " ").replace("&amp;", "&").replace(/<.*?>/g, ""),
        id_hotel_4_url = option.attr("id"),
        new_namespace = widget.find("#namespace_" + id_hotel_4_url).val(),
        url_booking = widget.find("#url_booking_" + id_hotel_4_url).val(),
        hotel_value = url_booking.indexOf('http') > -1 ? url_booking : "https://" + id_hotel_4_url + url_booking;

    //Change calendar namespace
    window.calendar_data.update_namespace_calendar(new_namespace);
    $('.price_calendar_wrapper .toggle_chart').css('opacity', '1').css('pointer-events', 'auto');

    //change the value
    widget.find(".destination").val(new_placeholder_html);
    widget.find("form").attr("action", hotel_value).find("#namespace").val(new_namespace);
}

function resetStepSelection() {
    $(".booking_steps").find(".step_flight, .step_1, .step_2, .step_3").removeClass("current_step");
    $(".hotel_selector").slideUp();
    $(".calendar_root_wrapper").slideUp();
    $(".room_list_wrapper").slideUp();
    $(".airport_selector_container").slideUp();
}

// Flight + Hotel customization
function customizeFlightHotel() {
    if (typeof FlightHotelWidgetController === 'undefined') return;

    FlightHotelWidgetController.openAirportSelectorWrapper = function () {
        $(".booking_steps .step").removeClass("current_step");
        $(".close_button_datepicker").click();
        $(".occupancy_wrapper .close").click();
        $(".close_room_selector").click();
        $(".hotel_selector .close").click();
        $(".calendar_root_wrapper").slideUp();
        $(".airport_selector_container").slideDown();
        $(".booking_steps .step_flight").addClass("current_step");
        $("#widget_paraty").addClass("open");
        $(".booking_steps").show();
        $("body").addClass("widget_paraty_open");
        $("#full_wrapper_booking").addClass("fixed");
    }

    FlightHotelWidgetController.closeAirportSelectorWrapper = function () {
        $(".booking_steps .step_flight").removeClass("current_step");
        $(".close_button_datepicker").click();
        $(".occupancy_wrapper .close").click();
        $(".close_room_selector").click();
        $(".hotel_selector .close").click();
        $(".calendar_root_wrapper").slideUp();
    }

    FlightHotelWidgetController.setFlightSteps = function () {
        $(".booking_steps .step:not(.hidden)").each(function (index, element) {
            $(element).find(".number_step span").text(`${index + 1}`);
        });
    }
    
    $(window).on('flight_hotel.change_search_with_flight', function (event, active) {
        $(".booking_steps .step_flight").toggleClass("hidden", !active);
    
        if (!active) {
            $(".airport_selector_container .close").trigger("click");
        }
    
        FlightHotelWidgetController.setFlightSteps();
    });

    $(window).on('flight_hotel.continue_search', function () {
        if (!FlightHotelWidgetController.isValidFlightOrigin()) {
            $(FlightHotelWidgetController.config.airportSelectorWrapper).addClass("error");
            return;
        }

        $('.booking_steps .step_flight').addClass('done');
        
        if ($(".destination_wrapper").length) {
            $(".destination_wrapper").trigger("click");
        } else {
            $(".airport_selector_container .close").trigger("click");
        }
    });
}

bookingWidgetController.init();
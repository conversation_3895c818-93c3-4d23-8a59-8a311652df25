@import "booking_mobile/booking.scss";
@import "loyalty_club_mobile";

.cp-rocha {
  &.booking_process {
    #content {
      #expired-session-message {
        &:nth-child(2) {
          display: none;
        }
      }
    }
  }
}

.essence-donpaquito {
  &.booking_process {
    $color1: #d4b154;
    @import "booking_mobile/custom_booking";

    div#header {
      height: 84px;
    }
  }
}

.agaro {
  &.booking_process {
    $color1: #1C546F;
    $background_header: #AE8B2D;
    @import "booking_mobile/custom_booking";
  }
}

.toboso-almunecar, .jimesol, .toboso-aparturis {
  .top_custom_banner {
    color: #293C7F;
    padding: 10px;
    text-align: center;
    clear: both;
    margin: 0 -8px 10px;
    font-weight: 300;
    font-family: "Roboto", sans-serif;

    .desc {
      margin-bottom: 10px;
      letter-spacing: 1px;

      strong {
        font-weight: bold;
      }
    }
  }

  #login_wrapper_element.version_v1 .logo_wrapper img {
    max-height: 60px;
    margin-top: 8px;
  }


  #logged_user_info_wrapper.version_v1 {
    .logo_wrapper img {
      max-height: 70px;
      margin-top: 10px;
    }

    .content_logged_wrapper .user_category_image img {
      margin-top: -15px;
    }
  }

  .users_club_rates_login.tabs_login {
    border-bottom: 10px solid #293C7F;

    .login_rates_wrapper, .normal_rates_wrapper, .button_package_room {
      &.active {
        background: #293C7F;
      }
    }
  }

  .extra_info_tabs {
    background: #293C7F;
  }

  ul.result_list div[data-role="list-divider"] {
    .list_divider_title {
      width: 81%;
    }

    .offer_percentage_discount {
      font-size: 13px !important;
    }

    .free_cancellation_date {
      color: white;
    }
  }
}

.toboso-corporativa {
  $black: #343434;
  $corporate_1: #293C7F;
  $white_rgba: rgba(255, 255, 255, .7);
  $grey: #F5F5F5;
  $grey_2: #C2C2C2;
  $red: #C61C34;

  &.booking_process {
    $color1: #293C7F;
    $color2: #222;
    $color3: #E75354;
    @import "booking_mobile/custom_booking";

    span.modify_search {
      font-size: 16px;
    }

    div#header {
      background: white;

      div#logo,
      div#logo:not(.multi_logo) {
        a {
          width: 100%;
          @include center_xy;

          img#logo_1 {
            display: inline-block;
            vertical-align: middle;
            position: relative;
            top: auto;
            bottom: auto;
            left: auto;
            right: auto;
            -webkit-transform: translate(0, 0);
            -moz-transform: translate(0, 0);
            -ms-transform: translate(0, 0);
            -o-transform: translate(0, 0);
            transform: translate(0, 0);
            max-height: 60px;
          }

          span.hotel_name {
            display: inline-block;
            vertical-align: middle;
            color: #222;
            font-size: 16px;
            padding: 10px 10px 0;
          }
        }
      }
    }

    h2.busqueda {
      color: $color2;
    }

    .info_wrapper {
      background: white;
      border: 2px solid $color2;
      padding: 15px 10px 10px;
      color: #222;
      font-size: 12px;

      .hotel_name_wrapper {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        background: $color2;
        padding: 5px 0;
        text-align: center;
        text-transform: uppercase;
        font-size: 16px;
        color: white;
      }
    }

    ul.result_list div[data-role="list-divider"] {
      background: $color2;
      color: white;

      .list_divider_title a {
        color: $color1;
      }
    }

    .flex-direction-nav .flex-prev.flex-disabled,
    .flex-direction-nav .flex-next.flex-disabled {
      display: none !important;
    }

    #fancybox-title {
      table {
        &#fancybox-title-float-wrap {
          zoom: 1;
          display: table;
        }
      }
    }

    .newsletter_subscribe_query_top {
      background: #343434;
      border-radius: 0;
    }

  }

  .users_club_rates_login.has_packages {
    .normal_rates_wrapper {
      padding: 10px 15px !important;

      label {
        font-size: 12px !important;
      }
    }

    .button_package_room {
      padding: 25px 15px !important;

      label {
        font-size: 12px !important;
      }
    }
  }

  .package_element_wrapper {
    .package_prices_wrapper {
      .perform_package_booking_search {
        background: $corporate_1 !important;
      }
    }
  }
}


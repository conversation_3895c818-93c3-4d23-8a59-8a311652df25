div.site-header {
    height: 105px;
    padding: 0 30px 0 0;
    background: white !important;
    border-bottom:1px solid rgba($corporate_2, .3);

    .site-header__logo{
      position: relative;
      background: none;
      padding: 10px 150px 20px 100px;
      max-height: 76px;
      img {
        display: inline-block;
        vertical-align: middle;
        height: 80px;
        max-height: 100px !important;
      }
      &:before, &:after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
      }
      &:before {
        right: 1px;
        width: 8px;
        border-left: 3px solid white;
        border-right: 3px solid white;
      }
      &:after {
        right: 25px;
        width: 15px;
        border-left: 3px solid white;
        border-right: 3px solid white;
      }
      a {
        &:after {
          content: '';
          position: absolute;
          top: 0;
          bottom: 0;
          right: 45px;
          width: 20px;
          border-left: 3px solid white;
          border-right: 3px solid white;
        }
      }
    }
    .site-header__ticks {
      margin-top: 25px;
      margin-right: 50px;

      .site-header__tick-item {
        margin-top: 5px;
        margin-right: 0;
        float: none;
        display: inline-block;
        vertical-align: middle;
        width: 140px;

        p {
          width: 80px;
          font-size: 10px;
          line-height: 12px;
          text-transform: uppercase;
          font-weight: 300;
          text-align: center;
          margin-left: -10px;
          letter-spacing: 1px;
          color:$corporate_2 !important;
        }
        .icon-checkmark {
          font-size: 50px;
          margin-top: -10px;
          display: inline-block;

          &:before {
            font-size: 42px !important;
            line-height: 42px !important;
            color:$corporate_2 !important;
          }
        }
        &:first-of-type {
          margin-top: 5px !important;
          .icon-checkmark{
            padding: 5px !important;
            &:before {
              font-family: 'icomoon' !important;
              content: "\e93b" !important;
            }
          }
          p {
            top: 0 !important;
          }
        }
        &:nth-of-type(2) .icon-checkmark:before {
          font-family: 'icomoon' !important;
          content: "\e98b" !important;
        }
        &:last-of-type {
          display: none;
        }
      }
    }
    .language_header_selector_booking_process {
      border: none;
      width: 150px;
      text-transform: uppercase;
      .selected_language {
        font-size: 16px;
        margin-top: 0;
        color: $corporate_2 !important;
        font-weight: 300;
        @extend .fa-caret-down;
        &:before {
          @extend .fa;
          position: absolute;
          top: 50%;
          right: 0;
          -webkit-transform: translate(0%,-50%);
          -moz-transform: translate(0%,-50%);
          -ms-transform: translate(0%,-50%);
          -o-transform: translate(0%,-50%);
          transform: translate(0%,-50%);
          color: $corporate_2;
          font-size: 18px;
        }
        i {
          font-size: 20px;
        }
      }
    }
  }

.actual_wizard_step {
  li.wizard-tab--small a,
  li.wizard-tab--big a {
    border-left: 2px solid white;

    &:not(.disable) {
      &:before {
        display: block;
        border-left-color: white;
        left: calc(100% + 2px);
      }
    }

    &.disable {
      &:before {
        display: block;
        border-left-color: white;
        left: calc(100% + 2px);
      }
    }
  }
}


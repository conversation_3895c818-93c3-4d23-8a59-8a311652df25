@mixin onhotel_mixin($corporate_1, $corporate_2, $grey: #333333) {
  $font_1: "<PERSON>", sans-serif;
  $font_2: "Raleway", sans-serif;
  $button_personalize_1: #ce6c5e;
  $button_personalize_2: #b4a169;
  body {
    font-family: $font_2;
  }
  @include booking_header_v2($corporate_1, $corporate_2, $corporate_1, #F4F4F4, $corporate_1);
  .site-header {
    height: 130px;

    .site-header__logo {
      img {
        max-height: 120px;
      }
    }

    .site-header__ticks {
      margin-top: 30px;
      margin-right: 50px;

      .site-header__tick-item {
        margin-top: 5px;
        float: none;
        display: inline-block;
        vertical-align: middle;
        width: 150px;

        &:last-of-type:not(.tick_customized) {
          display: none !important;
        }

        p {
          width: 80px;
          font-size: 11px;
          line-height: 14px;
          font-family: $font_1;
          text-transform: uppercase;
          font-weight: 500;
          letter-spacing: 2px;
          color: white !important;
        }

        .icon-checkmark {
          font-size: 50px;
          margin-top: -10px;
          display: inline-block;

          &:before {
            font-size: 42px !important;
            color: white !important;
          }
        }
      }
    }

    .language_header_selector_booking_process {
      border: none;
      width: 150px;
      height: 130px;
      text-transform: uppercase;

      .selected_language {
        font-size: 16px;
        font-weight: 500;
        color: white !important;
        letter-spacing: 2px;

        i {
          font-size: 20px;
          color: white;
        }
      }

      &:before {
        color: white;
      }

      .language_booking_selector_wrapper {
        top: 65%;
      }
    }
  }

  #full_wrapper_booking {
    background: transparent;
    box-shadow: 0 10px 10px -2px rgba(0, 0, 0, .3);

    .stay_selection {
      border: 1px solid #F4F4F4;

      .entry_date_wrapper {
        position: relative;
        background-image: none;
        @extend .fa-chevron-right;

        &:before {
          @extend .fa;
          position: absolute;
          top: 50%;
          right: 10px;
          -webkit-transform: translate(0%, -50%);
          -moz-transform: translate(0%, -50%);
          -ms-transform: translate(0%, -50%);
          -o-transform: translate(0%, -50%);
          transform: translate(0%, -50%);
          color: #405968;
          font-size: 26px;
          padding: 5px;
          z-index: 1;
        }
      }

      .entry_date_wrapper, .departure_date_wrapper {
        border-bottom: none;

        .entry_date_label, .departure_date_label {
          font-size: 16px;
          color: $corporate_1;
          font-family: $font_1;
          font-weight: 100;
        }

        .date_box.departure_date {
          position: relative;
          background-image: none;
          @extend .fa-calendar;

          &:before {
            @extend .fa;
            position: absolute;
            top: 30%;
            right: 10px;
            -webkit-transform: translate(0%, -50%);
            -moz-transform: translate(0%, -50%);
            -ms-transform: translate(0%, -50%);
            -o-transform: translate(0%, -50%);
            transform: translate(0%, -50%);
            color: #F4F4F4;
            font-size: 32px;
            padding: 0;
            z-index: 1;
          }
        }

        .date_box {
          &.entry_date, &.departure_date {
            .date_day {
              font-family: $font_2;
              font-size: 18px !important;
              font-weight: 100;
            }
          }
        }
      }
    }

    .guest_selector {
      border: 1px solid #F4F4F4;
      height: 59.3px !important;
      font-weight: 100 !important;

      label {
        font-size: 16px !important;
        color: $corporate_1;
        font-family: $font_1;
        font-weight: 100 !important;
      }

      .placeholder_text {
        position: relative;
        background-image: none;
        font-family: $font_2 !important;
        font-size: 18px !important;
        font-weight: 100 !important;
        color: #405968;
        @extend .fa-users;

        &:before {
          @extend .fa;
          position: absolute;
          top: 50%;
          right: 0;
          -webkit-transform: translate(0%, -50%);
          -moz-transform: translate(0%, -50%);
          -ms-transform: translate(0%, -50%);
          -o-transform: translate(0%, -50%);
          transform: translate(0%, -50%);
          color: #F4F4F4;
          font-size: 24px;
          padding: 0;
          z-index: 1;
        }
      }
    }

    .wrapper_booking_button {
      .promocode_wrapper {
        .promocode_label {
          color: $corporate_1;
          font-size: 13px;
          font-family: $font_1;
        }

        .promocode_input {
          border: 1px solid #F4F4F4;
          height: 57px;
          color: $corporate_1;
          font-family: $font_1 !important;
          font-size: 15px !important;

        }
      }

      .submit_button {
        font-family: $font_2;
        border-radius: 30px;
        font-weight: 400;
      }

      .spinner_wrapper {
        border-radius: 30px;
      }
    }
  }

  .actual_wizard_step {
    .wizard-tab--small {
      .booking-step {
        background-color: $corporate_1;

        &:after, &:before {
          border-left-color: $corporate_1;
        }

        &.disable {
          background-color: #EAEAEA;

          &:after, &:before {
            border-left-color: #EAEAEA;
          }
        }
      }
    }
  }

  div#wizard {
    background-image: url("/static_1/images/pattern-onhotels.png");
    background-attachment: fixed;
  }


  div#step-1 {
    background-image: url("/static_1/images/pattern-onhotels.png");
    background-attachment: fixed;

    div.clearfix {
      .booking-box--search {
        .booking-box__content {
          min-width: 1140px;
          box-shadow: 0 0 10px 0 grey;
          border: none;
          padding: 0;
          background-image: none;
          background-color: white;

          .title_booking_breakdown {
            background-color: $corporate_1;
            color: white;
            height: 90px;
            font-family: $font_1;
            max-width: 120px;
            vertical-align: middle;
            text-align: left;
            background-image: url("/static_1/images/llave-05.png");
            background-repeat: no-repeat;
            background-position: right;
            line-height: 25px;
            font-size: 20px;
            font-weight: 300;
            padding: 0 10px 0 20px;
            letter-spacing: 2px;
            display: table-cell;
          }

          .booking-search-results__search-data {
            color: black;
            padding: 15px 0 0 40px;

            .booking-title-info.booking-hotel-name {
              margin-left: -21px;
              text-transform: uppercase;
              font-size: 14px;
              font-weight: 500;
              color: $corporate_1;
              letter-spacing: 2px;
              font-family: $font_1;
            }

            .booking-3-info {
              color: #333;
              font-size: 14px;
              font-weight: 500;
              letter-spacing: 2px;
              font-family: $font_1;
            }

            .booking-title-info {
              color: #333;
              font-family: $font_1;
            }

            i {
              color: $corporate_1;
            }

            span {
              font-family: $font_1;
              color: #333;
            }
          }

          .booking-search-results__rooms-list {
            position: relative;
            color: black;
            max-width: 340px;
            padding: 10px 0 0 65px;
            font-family: $font_1;
            top: auto;
            right: auto;

            .booking-title-info {
              color: #333;
              padding-top: 15px;
            }

            i {
              color: $corporate_1;
              padding-top: 30px;
            }
          }

          .booking-search-results__new-search {
            width: auto;

            &:before {
              display: none;
            }

            .booking-button {
              font-family: $font_1;
              position: relative;
              background-color: $corporate_2;
              text-transform: uppercase;
              margin: 15px 230px 15px 15px;
              padding: 10px 10px 10px 60px;
              font-size: 14px;
              font-weight: 300;
              border-radius: 30px;
              -webkit-transition: all 0.6s;
              -moz-transition: all 0.6s;
              -ms-transition: all 0.6s;
              -o-transition: all 0.6s;
              transition: all 0.6s;
              max-width: 200px;
              height: auto;
              letter-spacing: 0;
              z-index: 1;
              @extend .fa-calendar;

              &:before {
                @extend .fa;
                position: absolute;
                top: 50%;
                left: 10px;
                -webkit-transform: translate(0%, -50%);
                -moz-transform: translate(0%, -50%);
                -ms-transform: translate(0%, -50%);
                -o-transform: translate(0%, -50%);
                transform: translate(0%, -50%);
                color: $corporate_2;
                font-size: 22px;
                padding: 10px;
                border-radius: 50%;
                background-color: white;
                z-index: 1;
              }

              &:lang(en) {
                padding: 20px 10px 20px 60px;
              }
            }
          }
        }

        .call_center_wrapper {
          max-height: none !important;
          width: 200px;
          border: none;
          background-color: $corporate_1;
          background-image: url("/static_1/images/call-02_onhotels.png?v=1.2");
          background-repeat: no-repeat;
          background-position: left;

          .web_support_label_1 {
            width: 85%;

            .web_support_wrapper {
              font-family: $font_2;
              color: white;
              text-align: right;
              font-size: 14px;

              strong {
                font-weight: 400;
              }

              .web_support_number {
                font-size: 12px !important;
              }
            }

            &:before {
              display: none;
            }
          }
        }
      }
    }

    .rooms_packages_selectors_wrapper.has_packages {
      @include display_flex;
      justify-content: space-between;
      border: none;
      margin-bottom: 30px;
      overflow: hidden;

      .button_package_room {
        display: block;
        width: calc(50% - 5px); // 10px between tabs
        height: 73px;
        padding: 20px 50px;
        margin: 0;
        background-color: transparent;
        border: none;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        box-sizing: border-box;
        opacity: 1;
        overflow: visible;

        font-family: $font_1;
        font-weight: 300;
        font-size: 21px;
        letter-spacing: 1.15px;
        line-height: 25px;
        text-align: center;
        text-transform: uppercase;
        color: $grey;
        @include transition(all, 0.3s);

        &.rooms_selector,
        &.packages_selector {
          z-index: 2;
          color: #777777;

          &::before {
            position: absolute;
            content: '';
            top: 0;
            left: 0;
            pointer-events: none;
            right: 0;
            height: 75px;
            width: 1130px;
            background-repeat: no-repeat;
            background-size: contain;
            opacity: 0;
          }

          &:after {
            content: "";
            @include center_y;
            left: 160px;
            display: block;
            color: #777777;
            font-size: 35px;
            font-family: "Font Awesome 5 Pro";
          }

          &.active {
            z-index: 1;
            background-color: transparent !important;
            border: none !important;
            color: $corporate_1;

            &::before {
              opacity: 1;
            }

            &:after {
              color: $corporate_1;
              font-weight: 600;
            }
          }
        }

        &.rooms_selector {
          &::before {
            background-image: url('https://storage.googleapis.com/cdn.paraty.es/onhotel/files/left-active.png');
          }

          &:after {
            content: "\f236";
          }
        }

        &.packages_selector {
          &::before {
            background-image: url('https://storage.googleapis.com/cdn.paraty.es/onhotel/files/right-active.png');
            transform: translateX(-50%);
          }

          &:after {
            content: "\f49d";
            left: 70px;
          }

          &.with_number .packages_number:before {
            background: $corporate_1;
          }
        }

        &.with_number {
          display: flex;
          flex-flow: column;
          justify-content: center;

          .packages_number {
            font-size: 15px;
            letter-spacing: 0.8px;

            &:before {
              content: "";
              width: 11px;
              height: 11px;
              background: $corporate_1;
              border-radius: 50%;
              opacity: 0.75;
              display: inline-block;
            }
          }
        }

        &.active {
          background-color: transparent;

          &.with_number .packages_number:before {
            display: none;
          }
        }
      }
    }

    .rates_filters_tabs {
      height: 50px;
      border-radius: 25px;
      border: none;
      overflow: hidden;
      box-sizing: border-box;
      margin: 20px auto;

      .rate_tab_filter {
        width: 50%;
        padding: 5px 20px;
        margin: 0;
        box-sizing: border-box;
        background-color: white;
        font-family: $font-1;
        font-weight: 700;
        font-size: 17px;
        letter-spacing: 3.4px;
        line-height: 40px;
        color: $corporate_1;
        border-top: 2px solid #EAEAEA;
        border-bottom: 2px solid #EAEAEA;

        &.active {
          background-color: $corporate_1;
          border-color: $corporate_1 !important;
          color: white;
        }

        &:first-of-type {
          border-left: 2px solid #EAEAEA;
          border-top-left-radius: 25px;
          border-bottom-left-radius: 25px;
        }

        &:last-of-type {
          border-right: 2px solid #EAEAEA;
          border-top-right-radius: 25px;
          border-bottom-right-radius: 25px;
        }
      }
    }

    .contTipoHabitacion {
      box-shadow: 0 0 10px 0 grey;
      font-family: $font_1;
      padding: 12px 0;

      .contFotoDescripcion {
        width: 1143px;
        padding: unset;
      }

      .tipoHabitacion {
        color: $corporate_1 !important;
        font-weight: 300 !important;
        letter-spacing: 2px;
        font-family: $font_1;
      }

      .descripcionHabitacion {
        margin: 0;
        font-family: $font_2;
        font-size: 14px;
        letter-spacing: 1px;
        font-weight: 300;
        color: #333 !important;
      }

      .see_more_rooms_v2 {
        .plus_sign {
          color: #333 !important;
        }

        .see_more {
          text-decoration: none !important;
          text-transform: uppercase !important;
          color: #333 !important;
          font-family: $font_1;
          font-weight: 400;
        }
      }

      .before_block {
        border-left-color: $corporate_1;
      }

      .just_booking_message {
        background-color: $corporate_1 !important;

        &:after {
          border-left-color: $corporate_1 !important;
        }
      }

      .very_asked_message {
        background-color: $corporate_2 !important;
        text-transform: uppercase;

        &:after {
          border-left-color: $corporate_2;
        }
      }

      .promotion_discount {
        background-color: $corporate_2 !important;
      }

      .room_services {
        border-top-style: dashed;
        border-bottom-style: dashed;

        .service_element {
          border-right-style: dashed;

          .service_description {
            font-family: $font_1;
            text-transform: none;
            font-size: 12px;
          }
        }
      }

      .booking-button {
        font-family: $font_2;
        border-radius: 30px;
        padding: 15px 50px;
      }
    }

    .hidden_booking_summary {
      background-color: white !important;

      .booking-search-results__search-data {
        padding-top: 10px;
      }

      .web_support_number {
        font-size: 10px !important;
      }
    }

    .advice_rate_message {
      background-color: $corporate_1;

      &:before {
        border-left-color: #EAEAEA !important;
      }
    }
  }
  .popup_carousel .exceded img, #step-0 .booking-0-hotel-item .booking_picture img {
    height: 100%;
    width: 100%;
    object-fit: cover;
  }
  #step-0 .booking-button--action {
    background: $button_personalize_1;
  }
  #step-0 .hotel_description .bottom_buttons_links .flexible_days {
    color: $button_personalize_1;
  }
  #step-0 .booking-box--default .booking-box__title.hotel-title {
    background: $button_personalize_2;
  }

  .cards_extended_wrapper {
    .card_element_wrapper {
      border-radius: 30px;

      .card_image_wrapper {

      }

      .card_description {
        padding: 20px 50px 20px 130px;
      }

      .close_card_button {
        top: 5px;
        right: 10px;
      }
    }
  }
}
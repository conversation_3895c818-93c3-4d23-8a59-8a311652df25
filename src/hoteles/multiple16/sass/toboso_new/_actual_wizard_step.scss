.actual_wizard_step {
  width: 1140px;
  margin: 0 auto 30px;
  display: flex;
  justify-content: center;
  padding-top: 30px;

  .wizard-tab--small {
    margin-right: 55px;
    position: relative;

    a {
      background: white!important;
      border: none;
      padding: 0;
      font-family: $text_family;
      font-weight: 300;
      font-size: 13px;
      letter-spacing: 0.65px;
      text-transform: none;
      color: $black;

      &::before,
      &::after {
        all: initial;
      }

      .step-inner--small {
        &::before {
          position: relative;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          vertical-align: middle;
          margin: 0 10px 0 0;
          width: 30px;
          height: 30px;
          font-size: 15px;
          line-height: 15px;
          background-color: white;
          border-radius: 50%;
          border: 1px solid $black;
          z-index: 2;
          font-weight: 300;
          color: $black;

        }

        &::after {
          display: none;
        }
      }
    }


    a.actual {
      .step-inner--small::before {
        background-color: $corporate_2;
        border-color: $corporate_2;
        color: white;
      }
    }

    &::after {
      position: absolute;
      top: 20px;
      left: 100%;
      content: '';
      height: 1px;
      width: 55px;
      background: #D8D8D8;
    }
  }

  .wizard-tab--small:nth-of-type(1) {
    a .step-inner--small::before {
      content: "1";
    }
  }

  .wizard-tab--small:nth-of-type(2) {
    a .step-inner--small::before {
      content: "2";
    }
  }

  .wizard-tab--small:nth-of-type(3) {
    margin: 0;

    a .step-inner--small::before {
      content: "3";
    }

    &::after {
      display: none;
    }
  }

  .wizard-tab--small:nth-of-type(4) {
    display: none;
  }
}

#wizard.step_-1 {
  .actual_wizard_step {
    display: none;
  }
}

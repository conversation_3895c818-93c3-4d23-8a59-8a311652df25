div#step-1 {
  .contTipoHabitacion {
    padding: 0;
    box-shadow: 2px 10px 16px 0px rgba(0,0,0,0.15);
    margin-bottom: 50px;
    position: relative;
    z-index: 1;

    .just_booking_message {
      background: #D5C4BB;

      &::after {
        border-left: 15px solid #D5C4BB;
      }
    }

    .contFotoDescripcion {
      padding: 0;

      .contFotoHabitacion {
        width: 320px;
        min-height: 215px;

        .see_more_rooms_v2 {
          img {
            @include cover_image;
            position: static;
          }

          .lupa {
            right: auto;
            left: 0;
            padding: 15px;

            font-family: "Font Awesome 6 Pro";
            font-size: 30px;
            font-weight: 300;
          }
        }
      }

      .contDescHabitacion {
        margin: 0;
        width: 820px;
        padding-top: 10px;
        box-sizing: border-box;
        height: 215px;

        .room_description_name_wrapper {
          max-height: 115px;
        }

        .cabeceraNombreHabitacion {
          margin: 0 !important;
        }

        .tipoHabitacion {
          font-family: $title_family;
          font-weight: 500;
          font-size: 23px;
          letter-spacing: 1.15px;
          line-height: 1;
          text-transform: none;
          color: $corporate-1;
        }

        .just_booking_message{
          position: relative;
          background: $corporate-1;

            &::after {
                border-left: 15px solid $corporate-1;

            }

            &::before{
                border-color: $corporate-1;
                border-radius: 100% 0 0 100%;
                background: $corporate-1;
            }
        }

        .very_asked_message {
          background: #D5C4BB;
          border-radius: 50px 0 0 50px;

          &::after {
            border-left: 15px solid #D5C4BB;
          }

          &::before{
            border: none;
            border-radius: 100% 0 0 100%;
            content: "\f005";
            position: absolute;
            top: 6px;
            left: 8%;
            font-family: "Font Awesome 6 Pro";
            font-weight: 900;
          }
        }

        //.very_asked_message ~ .just_booking_message {
        //  background: red !important;
        //  &::before {
        //    border-radius: 0;
        //  }
        //}

        .descripcionHabitacion {
          font-family: $text_family;
          font-weight: 400;
          font-size: 16px;
          letter-spacing: 0.75px;
          line-height: 21px;
          color: $black;
          height: 70px !important;
          margin-right: 110px;
          margin-top: 20px;
        }

        .see_more_rooms_v2 {
          .plus_sign {
            color: $grey-1;
          }

          .see_more {
            font-family: $text_family;
            font-weight: 500;
            font-size: 13px;
            letter-spacing: 0.75px;
            line-height: 1;
            color: $black;
            text-transform: uppercase;
            text-decoration: none;
          }
        }

        .room_services {
          margin: 0 auto;
          border-top: 1px solid rgba(black, 0.1);
          border-bottom: 1px solid rgba(black, 0.1);

          .service_element {
            border-right: none;

            .service_image {
              margin: 0 10px !important;
            }
          }

          .service_element + .service_element {
            border-left: 1px solid rgba(black, 0.1);
          }
        }
      }
    }

    .preciosHabitacion {
      .listadoHabsTarifas {
        padding-bottom: 10px;

        .contTitTipoTarifa {
          background: #f5f5f5;

          .titTipoTarifa {
            font-family: $text_family;
            font-weight: 500;
            font-size: 14px;
            letter-spacing: 0.75px;
            color: $grey-1;
            text-transform: capitalize;
          }

          .cheapest_rate_message,
          .advice_rate_message {
            color: $grey-2;
            background-color: #D5C4BB !important;

            .best_price_label_info {
              font-family: $text_family;
              font-weight: 600;
              font-size: 12px;
              letter-spacing: 0.75px;
              line-height: 1;
              color: white;
            }

            .conditions_info_wrapper {
              a {
                color: white;
              }
            }
          }

          .cheapest_rate_message:before {
            border-left: 19px solid #f8f7f1;
          }
        }

        .regimenColumn {
          width: 35%;
          font-family: $text_family;
          font-weight: 500;
          font-size: 14px;
          letter-spacing: 0.75px;

          .tTextoOferta {
            font-weight: 500 !important;
            font-size: 14px;
            margin-left: 3px;
            color: $red;

            span.offer_name_element {
              color: #BA7684;
            }
          }
        }

        .precioNocheColumn {
          width: 10%;

          .priceValues {
            //display: none;
          }
        }

        .precioTotalColumn {
          width: 20%;
          font-family: $text_family;

          .precioTachadoDiv {
            text-align: right;
            color: #BA7684;

            .tPrecioTachado {
              font-weight: 400;
              font-size: 11px;
              letter-spacing: 0.65px;
              color: #BA7684;
            }
          }

          .precioGeneralDiv {
            .tPrecioTotal,
            .tPrecioOferta {
              font-weight: 700;
              font-size: 16px;
              color: $grey-2 !important;
              letter-spacing: 0.35px;
            }
          }

          .promotion_percentage_square {
            border-radius: 50%;
            background: #D5C4BB !important;

            .promotion_discount {
              border-radius: 10px;
              background-color: #D5C4BB;
            }
          }

          .priceTitle {
            font-weight: 400;
            font-size: 9px;
            color: $grey-2;
            letter-spacing: 0.2px;
            line-height: 8px;
          }
        }

        .botonReservarColumn {
          button {
            width: 185px;
            height: 50px;
            outline: none;
            font-family: $title_family;
            font-weight: 700;
            font-size: 18px;
            letter-spacing: 1.35px;
          }

          .booking-button {
            background: $corporate_1;
            padding: 10px 5px;
            text-align: center;
            font-size: 20px;
            font-weight: 500;
            @include transition(all, .5s);
            text-transform: none;

            &:hover {
              background: $corporate_2;
            }
          }
        }
      }
    }
  }
}

.fancybox-overlay {
  .room_popup_individual_element {
    .popup_title {
      font-family: $title_family;
      font-weight: 500;
      font-size: 21px;
      letter-spacing: 1.15px;
      line-height: 1;
      text-transform: none;
      color: white;
    }

    .popup_carousel {
      .exceded {
        padding: 0;

        .popup_image {
          @include cover_image;
          position: static;
        }
      }
    }

    .popup_room_description {
      font-family: $text_family;
      font-weight: 300;
      font-size: 14px;
      letter-spacing: 0.75px;
      line-height: 21px;
      color: $black;

      strong{
        font-weight: 700;
      }
    }
  }
}

.room_popup_individual_element .room_services,
div#step-1 .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_services {
  display: grid !important;
  padding: 0 !important;
  grid-template-columns: repeat(4, auto);
  background-color: transparent !important;

  .service_element {
    width: 100%;
    height: auto;
    @include display_flex(nowrap);
    justify-content: center;
    align-items: center;
    padding: 10px;
    text-align: center;

    i {
      font-size: 25px;
      margin-right: 10px;
      color: $corporate_1;
    }

    .service_description {
      font-family: $text_family;
      font-weight: 500;
      font-size: 13px;
      letter-spacing: 0.7px;
      line-height: 1;
      color: $black;
    }
  }
}

.rate_conditions_popup_wrapper h3.rate_name, .package_hidden_popup .popup_title {
  font-family: $text_family;
}

#packages_b1_wrapper.v2 {
  .ui-datepicker {
    .ui-widget-header .ui-datepicker-title, table {
      font-family: $text_family;
    }

    .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
      font-family: $text_family;
    }
  }
}
#wizard {
  .booking-title-info, .notranslate, .rooms_amount_number, .search-item {
    font-weight: 500 !important;
  }

  .hidden_booking_summary.showed {
    .booking-box--search, .booking-search-results {
      border: none !important;
      display: flex;
      align-items: center;

      .title_booking_breakdown {
        color: $corporate_1 !important;
        font-family: $title_family;
        text-transform: none;
        font-weight: 500;
      }

      .booking-search-results__rooms-list {
        padding-top: 0;

        i.fa {
          display: none;
        }

        .booking-title-info {
          color: #2F3035;

          &.rooms_amount_label {
            &::before {
              content: "\f236";
              font-family: "Font Awesome 6 Pro";
              font-weight: 300;
              position: absolute;
              left: 10%;
            }
          }

          &:nth-child(1) {
            &::before {
              content: "\f0c0";
              font-family: "Font Awesome 6 Pro";
              font-weight: 300;
              position: absolute;
              left: 10%;
              top: 50%;
            }
          }
        }

        .rooms_amount_number {
          font-weight: 500;
          font-size: 15px;
        }

        .search-item {
          width: 300px;
        }
      }

      .booking-search-results__search-data {
        i {
          color: #D0CBC8;
          font-size: 16px;

          &:before {
            font-weight: 400;
          }
        }
      }

      .booking-search-results__new-search {
        height: auto !important;
        top: 50% !important;
        transform: translateY(-50%) !important;

        &::before {
          color: $corporate-1 !important;
          font-weight: 300 !important;
        }

        .booking-button {
          background-color: white !important;
          color: $corporate-1 !important;
          border: 1px solid $corporate-1;
          height: auto !important;
          font-weight: 600 !important;
        }
      }
    }

    &::before {
      content: "";
      height: 50px;
      width: 2100px;
      position: absolute;
      display: flex;
      left: 0;
      top: 53px;
      background: linear-gradient(180deg, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.5970763305322129) 44%, rgba(0, 0, 0, 0) 100%);
    }

    //display: none;
  }

  .clearfix {
    .booking-search-results {
      border: none !important;
      display: flex;
      align-items: center;

      .title_booking_breakdown {
        color: $corporate_1 !important;
        font-family: $title_family;
        text-transform: none;
      }

      .booking-search-results__rooms-list {
        top: 34px !important;

        i.fa {
          display: none;
        }

        .booking-title-info.rooms_amount_label {
          font-weight: 500;
          font-size: 15px;

          &::before {
            content: "\f236";
            font-family: "Font Awesome 6 Pro";
            font-weight: 300;
            position: absolute;
            left: 10%;
            top: 2%;
          }
        }

        .booking-title-info:nth-child(1) {
          &::before {
            content: "\f0c0";
            font-family: "Font Awesome 6 Pro";
            font-weight: 300;
            position: absolute;
            left: 10%;
            top: 50%;
          }
        }
      }

      .booking-search-results__new-search {
        height: auto !important;
        top: 50% !important;
        transform: translateY(-50%) !important;

        &::before {
          color: $corporate-1 !important;
          font-weight: 300 !important;
        }

        .booking-button {
          background-color: white !important;
          color: $corporate-1 !important;
          border: 1px solid $corporate-1;
          height: auto !important;
          font-weight: 600 !important;
        }
      }
    }
  }
}
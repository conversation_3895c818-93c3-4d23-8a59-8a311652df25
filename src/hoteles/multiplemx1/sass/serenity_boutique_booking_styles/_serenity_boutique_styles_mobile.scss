.main_wrapper {
  font-family: $font_2;

  .modify_search,
  .show_calendar,
  .back_button {
    color: $corporate_3;
    border: 1px solid $corporate_3;
  }

  .btn.btn_secondary,
  #calendar_price #prices-calendar .calendars-section .buttons-section .bottom_buttons_wrapper .btn_secondary.button,
  #calendar_price_availability #prices-calendar .calendars-section .buttons-section .bottom_buttons_wrapper .btn_secondary.button {
    background-color: $corporate_1;
  }

  .continue_booking {
    background-color: $corporate_2;
    border-radius: 0;
  }

  .double_button_wrapper {
    .btn_primary {
      background-color: $corporate_3;
    }
  }

  .room_list .room_pack_option .rates_details_wrapper {
    .rate_selected_title span {
      color: $corporate_2;
    }

    .regime_item_content {
      .discount_percentage {
        background-color: $corporate_4;
      }

      .regime_description .regime_offer_detail {
        color: $corporate_4;
      }

      .regime_description {
        .previously_selected {
          background-color: #18B4A7;
        }
      }

      .prices_options .price_through {
        color: $corporate_4;
      }

      .prices_options .final_price {
        color: $black;
      }

      .regime_price_wrapper div.submit span {
        background-color: $corporate_2;

        &:hover {
          background-color: $corporate_3;
        }
      }
    }
  }

  .room_pack_option {
    .just_booking {
      background-color: #2D2D2D;
    }

    .very_asked {
      background-color: #18B4A7;
    }
  }
}

.step_2 {
  .reservation_summary .option_selected {
    .rate .conditions {
      color: $corporate_3;
    }

    .price {
      color: $corporate_4;
    }
  }

  .personal_details_form_wrapper .personal_details_form {
    .payments_wrapper {
      display: flex;
      justify-content: space-evenly;
      .payment_type_element_wrapper {
        flex-direction: column;

        img {
          align-self: center;
        }
      }
    }

    #destination_cobrador_form {
      padding: 20px;
    }

    .bottom_button_wrapper #btn-finish-booking {
      color: $white;
      background-color: $corporate_2;
      border: 1px solid $corporate_2;
      border-radius: 0;

      &:hover {
        background-color: $corporate_3;
        border: 1px solid $corporate_3;
      }
    }
  }
}

.main_content_wrapper.wizard.step_3 {
  .reservation_summary.booking4 {
    .email_header_logo {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .booking-button--confirmed-booking {
      width: 50%;
      height: 30px;
      margin: 5px 100px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .double_button_wrapper {
    width: 100%;
  }
}

#main_modal.active{
  .body_modal{
    &.regime_conditions_modal_wrapper, &.iframe_modal_wrapper{
      .body_modal_content iframe{
        height: 69vh;
      }
    }
  }
}

.booking_widget_wrapper{
  .occupancy_popup .occupancy_head.content_title::before, .dates_popup .header_wrapper .banner_title i{
    color: $corporate_1 !important;
  }
}

#reservation{
  .rate_conditions + div{
    padding: 0 20px;
  }
}
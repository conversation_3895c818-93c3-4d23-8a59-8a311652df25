#full_wrapper_booking {
  box-shadow: 0 0 0 rgba(0,0,0,0);
  padding: 0;
}

.booking_engine_wrapper_process {
  background-color: transparent;

  #booking.boking_widget_inline {
    width: 100%;
    background: white;
    border-bottom: 1px solid lighten($corporate_1, 10%);
    padding-top:3px;
    padding-bottom:0;
    .booking_form.paraty-booking-form {
      width: 1140px;
      margin: auto;
      position: relative;
    }
    .stay_selection {
      width: calc(35% + 30px);
      .date_box.departure_date {
        background-image: none !important;
      }
      .departure_date_wrapper, .entry_date_wrapper {
        border-bottom: none !important;
        height: 85px;
        padding: 15px 15px;
        text-transform: uppercase;
        .date_box {
          margin-top: 10px;
        }
      }
      .departure_date_wrapper {
        background: white;
        padding-left: 50%;
        &:after {
          content: '';
          position: absolute;
          top: 10px;
          height: 65px;
          bottom: 5px;
          right: 35px;
          width: 1px;
          display: block;
          background: $corporate_1;
          opacity: .3;
        }
      }
      .entry_date_wrapper {
        background: transparent;
        &:after {
          content: '\f105';
          font-family: "Font Awesome 5 Pro";
          font-size: 35px;
          position: absolute;
          top: 30px;
          height: 45px;
          bottom: 5px;
          font-weight: 300;
          right: 20px;
          display: block;
          color: $corporate_1;
          opacity: .3;
        }
      }

      label {
        font-family: $text_family;
        color: $corporate_2;
        font-size: 12px;
        font-weight: normal;
        letter-spacing: 1.2px;
        text-transform: uppercase;
      }

      .date_day {
        font-family: $title_family;
        font-weight: normal;
        font-style: normal;
        font-size: 18px !important;
        letter-spacing: 2px;
        color: black;
        span {
          display: inline-block;
          vertical-align: baseline;
          &.day {
            font-weight: 700;
            font-size: 25px;
          }
          &.month {
            padding: 0 5px;
          }
        }
      }
    }
    .guest_selector {
      margin: 0;
      height: 85px;
      padding: 15px 15px;
      width: 300px;
      background: white;
      &:after {
        content: '';
        position: absolute;
        top: 10px;
        height: 65px;
        bottom: 5px;
        right: 0;
        width: 1px;
        display: block;
        background: $corporate_1;
        opacity: .3;
      }
      label {
        font-family: $text_family;
        color: $corporate_2;
        text-transform: uppercase;
        font-size: 12px;
        font-weight: normal;
        letter-spacing: 1.2px;
      }

      .placeholder_text {
        font-family: $title_family;
        font-style: normal;
        font-weight: lighter;
        font-size: 25px;
        letter-spacing: 2px;
        color: black;
        margin-top: 8px;
        span {
          font-size: 18px;
          font-weight: lighter;
          text-transform: lowercase;
        }
      }
    }

    .room_list_wrapper{
      top: 80px;
    }

    .room_list_wrapper .buttons_container_guests .save_guest_button {
      background: $corporate_1 !important;
    }
    .wrapper_booking_button {
      background: white;
      height: 85px;
      width: 410px;
      .promocode_wrapper {
        height: 100%;
        width: calc(50% - 70px);
        .promocode_label {
          @include center_xy;
          color: $corporate_2;
          font-family: $title_family;
          font-weight: normal;
          line-height: 9px;
          letter-spacing: 1px;
          opacity: 0;
          strong {
            font-weight: normal;
            position: relative;
            color: transparent;
            &:after {
              content: 'PROMOCODE?';
              @include center_x;
              color: $corporate_2;
            }
          }
        }
        .promocode_input {
          color: $corporate_2 !important;
          font-weight: 300;
          text-transform: uppercase;
          min-height: 60px;
          &::-webkit-input-placeholder {
            color: $corporate_2;
            font-size: 10px;
            letter-spacing: 2px;
            white-space:pre-line;
            position:relative;
            top:-7px;
          }
          &::-moz-placeholder {
            color: $corporate_2;
            font-size: 10px;
            letter-spacing: 2px;
            white-space:pre-line;
            position:relative;
            top:-7px;
          }
          &:-ms-input-placeholder {
            color: $corporate_2;
            font-size: 10px;
            letter-spacing: 2px;
            white-space:pre-line;
            position:relative;
            top:-7px;
          }
          &:-moz-placeholder {
            color: $corporate_2;
            font-size: 10px;
            letter-spacing: 2px;
            white-space:pre-line;
            position:relative;
            top:-7px;
          }
        }
      }
      .submit_button {
        position: relative;
        font-family: $text_family !important;
        font-weight: normal !important;
        font-size: 14px;
        letter-spacing: 2px;
        height: 80%;
        text-align: center;
        padding: 0 40px 0 70px;
        width: calc(45% + 70px);
        margin: 7px;
        float: right;
        background-color: $corporate_3;
        @include transition(all, .6s);
        @extend .fa-undo;
        &:before {
          @extend .fa;
          position: absolute;
          top: 50%;
          font-weight: 300;
          -webkit-transform: translateY(-50%);
          -moz-transform: translateY(-50%);
          -ms-transform: translateY(-50%);
          -o-transform: translateY(-50%);
          transform: translateY(-50%);
          left: 20px;
          font-size: 30px;
          color: white;
        }
        &:hover{
          opacity: 1;
          background: $corporate_5;
        }
      }

      .spinner_wrapper {
        width: calc(55% - 2px);
        height: 100%;
        background: $corporate_1;
      }

    }
  }

  .booking_footer_message {
    position: absolute;
    left: 0;
    bottom: -25px;
  }

  &.has_babies {
    .submit_button {
      width: 52% !important;
    }
  }
}

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
  .specific_month_selector, .go_back_button {
    background: $corporate_2 !important;
  }
}

.datepicker_wrapper_element .ui-datepicker .ui-widget-header{
  .ui-datepicker-next, .ui-datepicker-prev{
    &.ui-state-hover, &.ui-datepicker-next-hover, &.ui-datepicker-prev-hover{
      span:before{
        color: white;
    }
    }
  }
}
$fontawesome5: true;
@import "booking/booking_process_v1/booking_header";
@import "booking/booking_all_styles";
@import "booking/booking_colors";
@import "plugins/mixins";


$loader-size: 60px;
$loader-width: 250px;
$loader-color: linear-gradient(45deg, #5C1669 0%, #9A1F6E 100%);

@mixin individual_colors($corporate_1, $corporate_2) {
  .color1 {
    color: $corporate_2;
  }
  .bgc1 {
    background-color: $corporate_2;
  }
  #full_wrapper_booking {
    .stay_selection {
      width: 320px !important;
    }

    .boking_widget_inline .booking_form .wrapper_booking_button {
      .promocode_wrapper {
        width: 200px !important;
        height: 100%;
        padding: 25px 10px !important;

        .promocode-imput {
          height: 60px;
          border-radius: 20px;
          border: solid 2px $corporate_1;
        }
      }

      .modify-calendar-button:hover {
        background: $corporate_2;
        color: white;
      }

      .submit_button {
        background-color: $corporate_2;
      }
    }
  }
  .fancybox-overlay .room_popup_individual_element .room_services .service_element i, div#step-1 .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_services .service_element i {
    color: $corporate_1;
  }
  .share_links_wrapper {
    .share_links_prev {
      background-color: $corporate_2;
    }

    .share_links_cont .share_link {
      background-color: $corporate_2;
    }
  }

  #wizard {
    div#step-1 {
      .rooms_packages_selectors_wrapper.has_packages {
        .button_package_room {
          background-color: transparent;
          border: none;
          padding-left: 225px;
          text-align: left;

          &.rooms_selector,
          &.packages_selector {
            z-index: 2;
            color: #777777;

            &::before {
              position: absolute;
              content: '';
              top: 0;
              left: 0;
              pointer-events: none;
              right: 0;
              height: 75px;
              width: 1130px;
              background-image: url(https://storage.googleapis.com/cdn.paraty.es/abrigall-masella/files/tab_left_active_blue.svg);
              background-repeat: no-repeat;
              background-size: contain;
              opacity: 0;
            }

            &:after {
              content: "";
              @include center_y;
              left: 160px;
              display: block;
              color: #777777;
              font-size: 35px;
              font-family: "Font Awesome 5 Pro";
            }

            &.active {
              z-index: 1;
              background-color: transparent !important;
              border: none !important;
              color: $corporate_1;

              &::before {
                opacity: 1;
              }

              &:after {
                color: $corporate_1;
                font-weight: 600;
              }
            }
          }

          &.rooms_selector {
            &::before {
            }

            &:after {
              content: "\f236";
            }
          }

          &.packages_selector {
            &::before {
              background-image: url(https://storage.googleapis.com/cdn.paraty.es/abrigall-masella/files/tab_right_active_blue.svg);
              transform: translateX(-50%);
            }

            &:after {
              content: "\f06b";
            }

            &.with_number .packages_number:before {
              background: $corporate_1;
            }
          }
        }
      }

      .contTipoHabitacion .preciosHabitacion .listadoHabsTarifas .botonReservarColumn .booking-button {
        background: $corporate_1;
        @include transition(all, .5s);

        &:hover {
          background: $corporate_2;
        }
      }
    }
  }
  #packages_b1_wrapper.v2 {
    .package_element_wrapper.custom_package .package_supplements_wrapper {
      .package_supplement_title {
        color: $corporate_1;
      }

      .guest_counter_wrapper .guest_element .tag .guest_tag {
        color: $corporate_1;
      }

      .counter_wrapper .counter .plus {
        background-color: $corporate_2;
      }
    }

    .package_element_wrapper .package_prices_wrapper .perform_package_booking_search {
      background-color: $corporate_1 !important;
    }

  }
  .fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking img {
    max-height: 220px;
  }
  .rate_conditions_popup_wrapper h3.rate_name, .package_hidden_popup .popup_title {
    background: $corporate_1;
  }
  .shopping_cart_summary.v2 .full_body_wrapper .scrollable_content_wrapper .items_wrapper .item:not(:last-child) {
    border-color: $corporate_2;
  }
}

@keyframes loading {
  0% {
    transform: scale(.1);
  }

  45% {
    left: ($loader-width - $loader-size) / 2;
    transform: scale(1);
  }

  90% {
    left: $loader-width - $loader-size * 0.1;
    transform: scale(.1);
  }
}

@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap');
body.booking_process_version_1.alanda-marbella {
  $corporate_1: #1bacb5;
  $corporate_2: #76c9e1;
  $corporate_3: darken($corporate_1, 10%);
  $corporate_4: #1e2d33;
  $corporate_5: #E3F5F5;
  $corporate_6: rgba(#DCB596, 0.5);
  $corporate_7: $corporate_4;

  $red: #EC6363;
  $green: #2CA96E;
  $grey-1: $corporate_4;
  $grey-2: #444444;
  $grey-3: #F5F5F5;
  $grey-4: #393939;
  $black: $corporate_7;

  $title_family: 'Cormorant Garamond', serif;
  $text_family: 'Montserrat', sans-serif;

  font-family: $text_family;
  color: $black;

  @import "desktop/alanda-marbella-booking/alanda_booking_process";

  .color1 {
    color: $corporate_1;
  }

  .bgc1 {
    background-color: $corporate_1;
  }

  .remove_room_element {
    top: 25px !important;
  }

  .shopping_cart_summary.v2 {
    font-family: $text_family;
  }

  .cards_banners_wrapper .card_element_wrapper .card_description,
  .cards_extended_wrapper .card_element_wrapper .card_description {

  }

  .cards_banners_wrapper,
  .cards_extended_wrapper {
    .card_element_wrapper {
      .card_description {
        @include ellipsis(2);
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 14px;
        margin-bottom: 14px;
      }
    }
  }

  div#step-3 {
    #personal-details-form {
      .booking_details_prices_wrapper {
        .booking-button {
          &::before {
            display: none;
          }

          &:hover {
            background: #dadada;

            span {
              color: #262f38;
            }
          }
        }
      }
    }
  }
}

// Copied from Park Royal
@import url('https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;0,800;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
body.booking_process_version_1.demo-fitur, body.booking_process_version_1.demo-fitur2 {
  $color-process-1: rgb(128, 150, 199);
  $color-process-2: rgb(45, 72, 99);

  $corporate_1: #E4A470;
  $corporate_2: #456BA7;
  $corporate_3: #0088CC;
  $corporate_4: $corporate_2;
  $corporate_5: #092A5B;
  $corporate_6: #1B97CA;
  $corporate_7: #F8AE8B;
  $corporate_light: lighten($corporate_1, .5);

  $black: #333333;
  $lightgrey: #dedede;
  $red: #DB5A42;
  $green: #35CE8D;
  $pink: #cc5c6e;
  $grey-1: #D8D8D8;
  $grey-2: #141414;
  $grey-3: #333333;
  $grey-4: #1D1D1B;
  $grey-5: #707070;
  $grey-6: #ADADAD;
  $grey-7: #333;

  $body_text_color: $black;
  $links_color: $black;

  $font_1: 'Open Sans', sans-serif;
  $font_2: 'Open Sans', sans-serif;
  $fa6: "Font Awesome 6 Pro";

  $title_family: $font_1;
  $text_family: $font_2;

  $loader-size: 60px;
  $loader-width: 250px;
  $loader-color: linear-gradient(45deg, $corporate_6 0%, $corporate_4 100%);

  font-family: $font-2;
  font-weight: 400;

  &.rescue_seeker_visible{
    .booking_engine_wrapper_process{
      z-index: 101;
    }
  }

  .container_popup_booking {
    .description_top_popup_calendar {
      width: 100% !important;
    }

    #lottie_animation {
      top: 55% !important;
    }

    .gif_wrapper {
      .default_line_loading {
        background-color: #3484b2 !important;
      }
    }
  }

  .cards_banners_wrapper .card_element_wrapper.only_image .card_image_wrapper .card_image {
    width: 394px !important;
    position: fixed !important;
    margin: auto !important;
    top: 65px !important;
    right: -349px !important;
    transform: translateY(50%) !important;
    transition: all 0.3s;

    &:hover {
      right: -10px !important;
      transition: all 0.3s;
    }
  }

  .cards_banners_wrapper .card_element_wrapper.only_image + .close_card_button {
    display: none;
  }

  .tooltip-datepicker {
    display: none;
  }

  @import "desktop/demo-fitur-booking/demo-fitur-booking";

  .fancybox-overlay {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.5) !important;

    .fancybox-opened .fancybox-skin {
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
    }
  }

  .payment_radiobtn {
    -webkit-appearance: none !important;
  }

  .worldline_payment_form_container {
    .payment_option:first-child .cards_images img {
      width: calc((100% / 3) - 14px)
    }

    .payment_option .cards_images img {
      width: 40px;
    }
  }

  /*&:not(.iPad_user) {
    @media not all and (min-resolution:.001dpcm) {
      @supports (-webkit-appearance:none) {
        .payment_radiobtn {
          &:before {
            top: -9px;
          }

          &:after {
            top: -7px;
          }
        }
      }
    }
  }*/

  &.pt {
    .all_additional_services_wrapper.with_tabs_scroll {
      .category_wrapper {
        .additional_services_wrapper {
          .additional_service_element {
            .service_content {
              .price_per_night_person {
                .currencyValue {
                  float: left;
                }
              }
            }
          }
        }
      }
    }
  }
}


@font-face {
  font-family: 'Brandon Grotesque';
  src: url("https://www3.paratytech.com/wysiwyg/fonts/Brandon_Grotesque/BrandonGrotesque-Light.ttf");
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Brandon Grotesque';
  src: url("https://www3.paratytech.com/wysiwyg/fonts/Brandon_Grotesque/BrandonGrotesque-Medium.ttf");
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'Brandon Grotesque';
  src: url("https://www3.paratytech.com/wysiwyg/fonts/Brandon_Grotesque/BrandonGrotesque-Regular.ttf");
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'Brandon Grotesque';
  src: url("https://www3.paratytech.com/wysiwyg/fonts/Brandon_Grotesque/BrandonGrotesque-Bold.ttf");
  font-weight: 700;
  font-style: normal;
}

@font-face {
  font-family: 'Brandon Grotesque';
  src: url("https://www3.paratytech.com/wysiwyg/fonts/Brandon_Grotesque/BrandonGrotesque-Black.ttf");
  font-weight: 900;
  font-style: normal;
}

@font-face {
  font-family: 'Australia';
  src: url('https://www3.paratytech.com/wysiwyg/fonts/Australia_regular/australia-script.otf') format('otf'),
  url('https://www3.paratytech.com/wysiwyg/fonts/Australia_regular/australia-script.woff2') format('woff2'),
  url('https://www3.paratytech.com/wysiwyg/fonts/Australia_regular/australia-script.ttf') format('ttf');
  font-weight: normal;
  font-style: normal;
}

body.puerta-de-javalambre.booking_process_version_1 {
  $corporate_1: #7C0B0C;
  $corporate_2: #1C1C1C;
  $corporate_3: rgba(#1C1C1C, 0.3);
  $corporate_4: #0BCB89;
  $corporate_5: #E3F5F5;
  $corporate_6: rgba(#DEC2A6, 0.5);

  $red: #EC6363;
  $green: #2CA96E;
  $grey-1: #333333;
  $grey-2: #444444;
  $grey-3: #F5F5F5;
  $black: $grey-2;

  $title_family: 'Poppins', sans-serif;
  $text_family: 'Nunito', sans-serif;

  font-family: $text_family;
  color: $grey-1;

  @import "desktop/puerta-javalambre/puerta_javalambre_booking_process";

  .color1 {
    color: $corporate_1;
  }

  .bgc1 {
    background-color: $corporate_1;
  }

  .remove_room_element {
    top: 25px !important;
  }

  .ui-datepicker-next-hover, .ui-datepicker-prev-hover {
    background: $corporate-1 !important;
  }

  .shopping_cart_summary.v2 {
    font-family: $text_family;
  }

  @include individual_colors($corporate_1, $corporate_2);

  .cards_banners_wrapper .card_element_wrapper .card_description,
  .cards_extended_wrapper .card_element_wrapper .card_description {

  }

  div#full_wrapper_booking.booking_widget_step_0 {
    .boking_widget_inline {
      .booking_form {
        .wrapper_booking_button {
          .spinner_wrapper {
            transform: translateX(83px);
          }
        }
      }
    }
  }

  .cards_banners_wrapper,
  .cards_extended_wrapper {
    .card_element_wrapper {
      .card_description {
        @include ellipsis(2);
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 14px;
        margin-bottom: 14px;
      }
    }
  }

  div#full_wrapper_booking .boking_widget_inline .booking_form .stay_selection {
    display: inline-flex;
  }

  .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.closed {
    background: transparent;
  }
}

body.booking_process_version_1.maria-del-mar {
  $color-process-1: #333333;
  $color-process-2: #921725;

  $corporate_1: $color-process-1;
  $corporate_2: $color-process-2;
  $corporate_3: $color-process-2;
  $corporate_4: $color-process-2;
  $corporate_5: $color-process-2;
  $corporate_6: $color-process-2;
  $corporate_7: $color-process-2;
  $corporate_light: lighten($corporate_1, .5);

  $black: $corporate_1;
  $lightgrey: #dedede;
  $red: $corporate_1;
  $green: #35CE8D;
  $pink: #cc5c6e;
  $grey-1: #D8D8D8;
  $grey-2: #141414;
  $grey-3: #333333;
  $grey-4: #1D1D1B;
  $grey-5: #707070;
  $grey-6: #ADADAD;
  $grey-7: #333;

  $body_text_color: $black;
  $links_color: $black;

  $font_1: 'Cabin', sans-serif;
  $font_2: 'Cabin', sans-serif;
  $font_3: 'Australia', serif;
  $fa6: "Font Awesome 6 Pro";

  $title_family: $font_1;
  $text_family: $font_2;

  $loader-size: 60px;
  $loader-width: 250px;
  $loader-color: linear-gradient(45deg, $corporate_6 0%, $corporate_4 100%);


  .container_popup_booking {
    .gif_wrapper {
      .default_line_loading {
        background-color: #3484b2 !important;
      }
    }
  }

  .cards_banners_wrapper .card_element_wrapper.only_image .card_image_wrapper .card_image {
    width: 394px !important;
    position: fixed !important;
    margin: auto !important;
    top: 65px !important;
    right: -349px !important;
    transform: translateY(50%) !important;
    transition: all 0.3s;

    &:hover {
      right: -10px !important;
      transition: all 0.3s;
    }
  }

  .cards_banners_wrapper .card_element_wrapper.only_image + .close_card_button {
    display: none;
  }

  .tooltip-datepicker {
    display: none;
  }

  @import "desktop/maria-del-mar-booking/maria-del-mar-booking";

  .fancybox-overlay {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.5) !important;

    .fancybox-opened .fancybox-skin {
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
    }
  }

  .payment_radiobtn {
    -webkit-appearance: none !important;
  }

  .worldline_payment_form_container {
    .payment_option:first-child .cards_images img {
      width: calc((100% / 3) - 14px)
    }

    .payment_option .cards_images img {
      width: 40px;
    }
  }

  .ui-datepicker-header {
    .ui-state-hover.ui-datepicker-prev-hover,
    .ui-state-hover.ui-datepicker-next-hover {
      &:hover {
        span::before {
          color: white !important;
        }

      }
    }
  }
}

body.demo-fitur.booking_process_version_1.iPad_user,
body.demo-fitur2.booking_process_version_1.iPad_user,
body.maria-del-mar.booking_process_version_1.iPad_user {
  .site-main .booking_engine_wrapper_process #booking.boking_widget_inline .wrapper_booking_button {
    width: 560px !important;
  }

  @media screen and (-webkit-min-device-pixel-ratio: 0) {
    .worldline_payment_form_container .payment_option .payment_radiobtn {
      &:before {
        top: 7px;
        left: -3px;
      }

      &:after {
        top: 9px;
        left: -1px;
      }
    }
  }
}


@import url('https://fonts.googleapis.com/css2?family=Spartan:wght@100;300;400;500;700&display=swap');
body.playamaro {
  @font-face {
    font-family: 'georgia';
    src: url("/static_inj/fonts/georgia/v2/GEORGIA.ttf");
    font-weight: normal;
    font-style: normal;
  }

  @font-face {
    font-family: 'georgia';
    src: url("/static_inj/fonts/georgia/v2/GEORGIAB.ttf");
    font-weight: bold;
    font-style: normal;
  }
  @font-face {
    font-family: 'georgia';
    src: url("/static_inj/fonts/georgia/v2/GEORGIAI.ttf");
    font-weight: normal;
    font-style: italic;
  }
  @font-face {
    font-family: 'georgia';
    src: url("/static_inj/fonts/georgia/v2/GEORGIAZ.ttf");
    font-weight: bold;
    font-style: italic;
  }

  $corporate_1: #206372;
  $corporate_2: #F2A400;
  $corporate_3: #FAFAF7;

  $red: #E75354;
  $black: #222222;
  $grey: #696969;
  $grey-1: #F1EDE4;
  $green: #F2A400; //its orange now :-O
  $offer: $corporate_1;

  $title_family: 'Spartan', sans-serif;
  $text_family: $title_family;
  $extra_family: "georgia", sans-serif;

  font-family: $text_family;
  color: $black;

  @import "desktop/playamaro-styles/playamaro_styles";
  .custom_board_message{
    padding: 0px;
    .message_element{
      padding: 0px 0px 0px 0px;
      display: flex;
      align-items: center;
      justify-content: space-evenly;
      text-align: left;
      gap: 20px;
      position: absolute;
      left: -330px;
      width: 110px;
      bottom: 10px;
      white-space: nowrap;
    }
    .marketing_logo_info_wrapper{
      left: -220px;
    }
  }

}

@import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;700&family=PT+Serif:wght@400;700&display=swap');
body.booking_process_version_1.hotel-ninays {

  $corporate_1: #003580;
  $corporate_2: #454e59;
  $corporate_3: #003580;
  $corporate_4: $corporate_1;
  $corporate_5: #66b8d9;
  $lightblue: #66b8d9;
  $black: rgb(112, 113, 115);
  $grey: #F4F4F4;
  $grey_2: #989898;
  $green: #3FAB38;
  $red: #e75354;
  $black: rgb(74, 74, 74);
  $title_family: 'PT Serif', serif;
  $text_family: 'Open Sans', sans-serif;
  $font_1: $text_family;
  $font_2: $text_family;

  b, strong {
    font-weight: bold;
  }

  @import "desktop/ninays/ninays_booking_process";

  @media only screen and (min-device-width: 768px) and (max-device-width: 1024px) and (orientation: portrait) {
    .hidden_booking_summary.showed .booking-search-results__rooms-list {
      padding-left: 110px;
    }
  }

  .fancybox-overlay{
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
  .fancybox-wrap.fancy-booking-search_v2 .gif_wrapper .default_line_loading{
    background: $black!important;
  }

}


// Copied from Demo Fitur
@font-face {
  font-family: 'Nigelina';
  src: url('/static_1/fonts/Nigelina/Nigelina-Regular.eot');
  src: url('/static_1/fonts/Nigelina/Nigelina-Regular.eot?#iefix') format('embedded-opentype'),
  url('/static_1/fonts/Nigelina/Nigelina-Regular.woff2') format('woff2'),
  url('/static_1/fonts/Nigelina/Nigelina-Regular.woff') format('woff'),
  url('/static_1/fonts/Nigelina/Nigelina-Regular.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@import url('https://fonts.googleapis.com/css2?family=Hanken+Grotesk:wght@300;400;700&display=swap');
body.booking_process_version_1.coronado {
  $corporate_1: #B19261;
  $corporate_2: #93774A;
  $corporate_3: #F6F1EA;

  $black: #212121;
  $lightgrey: #F5F5F5;
  $red: #EC6363;

  $font_1: 'Nigelina', sans-serif;
  $font_2: 'Hanken Grotesk', sans-serif;
  $fa6: "Font Awesome 6 Pro";

  $title_family: $font_1;
  $text_family: $font_2;

  .fancybox-skin {
    background: rgba(177, 146, 97, 0.9) !important;
  }

  .cards_banners_wrapper .card_element_wrapper.only_image .card_image_wrapper .card_image {
    width: 394px !important;
    position: fixed !important;
    margin: auto !important;
    top: 65px !important;
    right: -349px !important;
    transform: translateY(50%) !important;
    transition: all 0.3s;

    &:hover {
      right: -10px !important;
      transition: all 0.3s;
    }
  }

  .cards_banners_wrapper .card_element_wrapper.only_image + .close_card_button {
    display: none;
  }

  .tooltip-datepicker {
    display: none;
  }

  @import "desktop/coronado-booking/coronado-booking";

  .fancybox-overlay {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    background-color: rgba(255, 255, 255, 0.5) !important;

    .fancybox-opened .fancybox-skin {
      box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
    }
  }

  .payment_radiobtn {
    -webkit-appearance: none !important;
  }

  .worldline_payment_form_container {
    .payment_option:first-child .cards_images img {
      width: calc((100% / 3) - 14px)
    }

    .payment_option .cards_images img {
      width: 40px;
    }
  }

  &.pt {
    .all_additional_services_wrapper.with_tabs_scroll {
      .category_wrapper {
        .additional_services_wrapper {
          .additional_service_element {
            .service_content {
              .price_per_night_person {
                .currencyValue {
                  float: left;
                }
              }
            }
          }
        }
      }
    }
  }

  #wizard {
    div#step-1 {
      .rooms_packages_selectors_wrapper.has_packages {
        .button_package_room {
          padding-left: 260px;

          &.rooms_selector {
            &::after {
              left: 190px;
            }
          }

          &.packages_selector {
            &::after {
              left: 200px;
            }
          }
        }
      }

      #rooms_b1_wrapper {
        .contTipoHabitacion {
          .contFotoDescripcion {
            .contDescHabitacion {
              .room_description_name_wrapper {
                .cabeceraNombreHabitacion {
                  .tipoHabitacion {
                    line-height: 26px;
                  }
                }

                .descripcionHabitacion {
                  height: 80px !important;
                }
              }
            }
          }
        }
      }

      .hidden_booking_summary {
        display: none !important;
      }
    }
  }

  //.fancybox-inner {
  //background: white;
  //}


  .room_popup_individual_element {
    .popup_title {
      background: white;
    }

    .popup_room_description {
      background: white;
    }

    .popup_carousel .flex-direction-nav {
      .flex-nav-next a:before,
      .flex-nav-prev a:before {
        color: #B19261;
      }
    }

    .room_services {
      .service_element {
        i {
          color: #B19261 !important;
        }
      }
    }
  }

  div#step-2 {
    .booking_button_wrapper {
      .booking-button {
        background-color: #B19261;
        padding: 12px 25px;
        font-family: "Nigelina", sans-serif;
        font-size: 20px;
        line-height: 20px;
        letter-spacing: 0.4px;
        text-transform: inherit;
      }
    }
  }

  div#step-3 {
    #personal-details-form {
      .booking_details_prices_wrapper {
        .booking-button {
          background-color: #B19261;
          padding: 12px 25px;
          font-family: "Nigelina", sans-serif;
          font-size: 20px;
          line-height: 20px;
          letter-spacing: 0.4px;
          height: 50px;
          text-transform: inherit;
        }
      }
    }
  }

  .fancybox-wrap.popup_see_more_rooms_second.fancybox-opened {
    .fancybox-title-float-wrap .child {
      display: none;
    }
  }

  #logged_user_info_wrapper.version_v1 {
    .center_content_wrapper {
      .logged_user_text {
        max-width: none;
        width: calc(100% - 150px);
        display: flex;
        align-items: center;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);

        &:before {
          content: "\f2bd";
          font-family: 'Font Awesome 5 Pro';
          font-size: 39px;
          margin-right: 10px;
          line-height: normal;
        }

        .extra_logged_user_info {
          margin-left: 50px;
          text-align: left;
          letter-spacing: 0.35px;
          font-weight: 200;
        }
      }

      .logout_button_wrapper {
        .fal.fa-sign-out {
          font-size: 23px;
        }

        span {
          font-size: 12px;
          text-decoration: none;
        }
      }

      .user_points, .user_category_image {
        display: none;
      }
    }
  }

  div#full_wrapper_booking {
    &.has_babies {
      .boking_widget_inline {
        .booking_form {
          .room_list_wrapper {
            .room_list {
              .room {
                background: white;
                padding: 0;
                margin-bottom: 0;

                .adults_selector,
                .children_selector,
                .babies_selector {
                  float: none;
                  border: 0 !important;
                  display: flex;
                  width: 100% !important;
                  align-items: center;
                  padding: 0 20px;
                  justify-content: space-between;
                }

                .babies_selector {
                  margin: 30px 0;

                  label {
                    height: auto;
                    text-transform: none;
                    font-size: 14px;
                    font-weight: 400;
                    letter-spacing: 1.68px;
                    line-height: 22px;
                  }

                  .room_selector {
                    margin-right: 40px;
                    margin-top: -5px;
                    width: auto !important;
                  }
                }
              }
            }
          }
        }
      }
    }

    .boking_widget_inline {
      .booking_form {
        .wrapper_booking_button {
          width: 555px !important;
        }
      }
    }
  }

  #step-1 {
    .custom_pictures_range {
      display: none;
    }

    .rooms_packages_selectors_wrapper {
      &:before {
        content: '';
        background-image: url("https://storage.googleapis.com/cdn.paraty.es/hotansa-magic-and/files/5af9f58e368de18%3Ds1900.jpg");
        width: 1140px;
        height: 135px;
        background-repeat: no-repeat;
        margin-bottom: 25px;
        background-size: contain;
      }
    }
  }

  @media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    div#step-1 {
      #rooms_b1_wrapper {
        .contTipoHabitacion {
          .contFotoDescripcion {
            .contDescHabitacion {
              width: 730px;
            }
          }
        }
      }
    }
  }
}

@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Lato:wght@100;300;400;700;900&display=swap');
body.booking_process_version_1.regency-salgados {
  $corporate_1: #00445E;
  $corporate_2: #C9BFB1;
  $corporate_3: rgba(#C9BFB1, 0.3);
  $corporate_4: #00445E;
  $corporate_5: #E3F5F5;
  $corporate_6: rgba(#C9BFB1, 0.5);
  $corporate_7: #436a91;

  $red: #EC6363;
  $green: #2CA96E;
  $grey-1: #333333;
  $grey-2: #444444;
  $grey-3: #F5F5F5;
  $grey-4: #393939;
  $black: $corporate_1;

  $title_family: 'Playfair Display', serif;
  $text_family: 'Lato', sans-serif;

  font-family: $text_family;
  color: $black;

  @import "desktop/regency-salgados/salgados_booking_process";

  .color1 {
    color: $corporate_1;
  }

  .bgc1 {
    background-color: $corporate_1;
  }

  .remove_room_element {
    top: 25px !important;
  }

  .shopping_cart_summary.v2 {
    font-family: $text_family;
  }

  @include individual_colors($corporate_1, $corporate_2);

  .cards_banners_wrapper .card_element_wrapper .card_description,
  .cards_extended_wrapper .card_element_wrapper .card_description {

  }

  .cards_banners_wrapper,
  .cards_extended_wrapper {
    .card_element_wrapper {
      .card_description {
        @include ellipsis(2);
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 14px;
        margin-bottom: 14px;
      }
    }
  }

  .phone_mobile_dropdown {
    display: none;
  }
}

body.lago-montargil.booking_process_version_1 {
  @import url('https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600;700&display=swap');

  $corporate_1: #1F2A44;
  $corporate_2: #e0ebf3;
  $corporate_3: #506D85;
  $corporate_light: lighten($corporate_1, .5);


  $black: #000000;
  $lightgrey: #dedede;
  $red: #f7b89f;
  $green: #35CE8D;
  $grey-1: #D8D8D8;
  $grey-2: #141414;
  $grey-3: #333333;
  $grey-4: #1D1D1B;
  $grey-5: #707070;
  $grey-6: #ADADAD;

  $pink: #cc5c6e;

  $body_text_color: $black;
  $links_color: $black;

  $font_1: 'Open Sans', sans-serif;
  $font_2: 'Open Sans', sans-serif;

  $title_family: $font_1;
  $text_family: $font_2;

  $shadow: 0px 3px 12px 3px rgba(0, 0, 0, 0.22);

  $loader-size: 60px;
  $loader-width: 250px;
  $loader-color: linear-gradient(45deg, #5C1669 0%, #9A1F6E 100%);


  @keyframes loading {
    0% {
      transform: scale(.1);
    }

    45% {
      left: ($loader-width - $loader-size) / 2;
      transform: scale(1);
    }

    90% {
      left: $loader-width - $loader-size * 0.1;
      transform: scale(.1);
    }
  }

  @import "desktop/montargil-booking/montargil_booking_styles";

  @media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .site-main {
      .booking_engine_wrapper_process {
        #booking.boking_widget_inline {
          .booking_form.paraty-booking-form {
            width: calc(1140px - 20px) !important;

            .wrapper_booking_button {
              width: 600px !important;
            }
          }
        }
      }
    }

    #step-2 {
      .all_additional_services_wrapper {
        .additional_service_element {
          .service_selection_wrapper {
            .price_service {
              .price_service_title {
                margin-bottom: 10px;
              }
            }
          }
        }
      }
    }
  }

  #step-3 .billing_data_wrapper {
      .billing_cif.error {
        position: relative;
      }
      .text_information_billing .icon_question {
        position: absolute;
        top: -17px;
        right: 0px;
      }
  }
}

@import url('https://fonts.googleapis.com/css2?family=Libre+Baskerville:ital,wght@0,400;0,700;1,400&family=Open+Sans:wght@300;400;600;700&display=swap');
body.booking_process_version_1.peso-village {
  $corporate_1: #542b0b;
  $corporate_2: #444444;
  $corporate_3: #c68439;
  $corporate_4: $corporate_1;
  $lightblue: #6CB9FF;
  $black: #3D3D3C;
  $grey: #F4F4F4;
  $grey_2: #989898;
  $green: #01B951;
  $red: #EC6363;
  $title_family: 'Open Sans', sans-serif;
  $text_family: 'Open Sans', sans-serif;
  $font_1: 'Open Sans', sans-serif;
  $font_2: 'Open Sans', sans-serif;

  $title_rooms: 'Libre Baskerville', serif;

  b, strong {
    font-weight: bold;
  }

  &.iPad_user {
    div.site-header {
      .header_center_wrapper {
        padding-right: 150px;
      }

      .language_header_selector_booking_process {
        right: 0;
      }
    }
  }

  @import "desktop/peso-village/peso_village_booking_process";
}

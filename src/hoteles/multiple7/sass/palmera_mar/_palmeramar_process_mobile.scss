.double_button_wrapper {
  .modify_search, .re-search_button {
    border-color: $corporate_1 !important;
    color: $corporate_1;
    background: white;
  }

  .show_calendar, .back_button, .close_button {
    background: $corporate_1;
    color: white;
    border-color: $corporate_1;
  }
}

.just_booking {
  background: $corporate_1;
}

.very_asked {
  background: $corporate_2;
}

.main_content_wrapper.step_0 {
  .room_list {
    .room_pack_option {
      .rates_details_wrapper {
        .conditions_info_wrapper {
          .last_day_cancellation_text {
            color: $corporate-2;

            &::before {
              color: $corporate-2;
            }
          }
        }

        .regime_item_content {
          .regime_description {
            .regime_title {
              font-size: 14px;
              margin-bottom: 5px;
            }

            .prices_options {
              .final_price {
                color: $black;
              }
            }
          }

          .regime_price_wrapper {
            .discount_percentage {
              background: $corporate_2;
            }

            div.submit {
              span {
                background: $corporate_1;
                border-radius: 0;
              }
            }
          }
        }

        .rate_selected_title {
          span {
            color: $corporate_2;
          }
        }
      }
    }
  }
}

.main_content_wrapper.step_1 {
  .all_additional_services_wrapper {
    .category_wrapper {
      .additional_services_wrapper {
        .additional_service_element.active {
          .preview_wrapper {
            .preview_content {
              display: none;
            }
          }
        }
      }
    }
  }

  .additional_service_form {
    .additional_services_wrapper {
      .supplement_element {
        .product_details {
          .btn {
            background: $corporate-1;
          }
        }
      }
    }

    .continue_booking {
      background: $corporate-1;
    }
  }
}

.main_content_wrapper.step_2 .personal_details_form_wrapper .personal_details_form .bottom_button_wrapper #btn-finish-booking {
  background: $corporate_1;
}

#main_modal.active .body_modal.regime_conditions_modal_wrapper .body_modal_content iframe,
#main_modal.active .body_modal.iframe_modal_wrapper .body_modal_content iframe,
#main_modal.active .body_modal.marketing_modal_wrapper .body_modal_content iframe,
#main_modal.active .body_modal.extra_room_info_modal_wrapper .body_modal_content iframe,
#main_modal.active .body_modal.closed_hotel_popup_modal_wrapper .body_modal_content iframe {
  height: calc(80vh - 70px);
}

#main_modal.active .body_modal.regime_conditions_modal_wrapper .body_modal_content,
#main_modal.active .body_modal.iframe_modal_wrapper .body_modal_content,
#main_modal.active .body_modal.marketing_modal_wrapper .body_modal_content,
#main_modal.active .body_modal.extra_room_info_modal_wrapper .body_modal_content,
#main_modal.active .body_modal.closed_hotel_popup_modal_wrapper .body_modal_content {
  max-height: calc(100vh - 170px);
}



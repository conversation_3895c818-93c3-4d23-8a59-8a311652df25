div#full_wrapper_booking {
  width: 100%;
  min-width: 1140px;
  border-top: 1px solid rgba(black, 0.1);
  border-bottom: 1px solid rgba(black, 0.1);
  padding: 0;
  color: $grey-1;
  background: white;

  .boking_widget_inline {
    .booking_form {
      width: 1140px;
      height: 90px;
      margin: 0 auto;
      position: relative;

      .entry_date_label,
      .departure_date_label,
      .guest_selector label,
      .children_label,
      .babies_selector label,
      .rooms_label,
      .adults_label,
      .promocode_label,
      .promocode_input {
        font-family: $text_family;
        font-weight: 300;
        font-size: 16px;
        letter-spacing: 1.28px;
        line-height: 20px;
        color: $grey-1 !important;
        text-transform: none !important;
      }

      .entry_date_label,
      .departure_date_label,
      .guest_selector label {
        font-size: 16px;
        text-transform: uppercase;
      }

      .entry_date_wrapper,
      .departure_date_wrapper,
      .guest_selector {
        display: inline-flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: stretch;
      }

      .adults_label,
      .children_label,
      .babies_selector label {
        text-transform: none;
      }


      .stay_selection .date_day,
      .guest_selector .placeholder_text {
        font-family: $title_family;
        font-weight: 600;
        font-size: 18px !important;
        letter-spacing: 1.44px;
        line-height: 20px;
        color: $grey-1;
      }

      .stay_selection .departure_date_wrapper,
      .guest_selector {
        &::after {
          content: '';
          display: block;
          @include center_y;
          height: 50px;
          width: 2px;
          background: rgba(black, 0.1);
        }
      }

      .stay_selection {
        width: 320px;
        height: 100%;

        .entry_date_wrapper,
        .departure_date_wrapper{
          border-bottom: none;
          height: 100%;
        }

        .entry_date_wrapper {
          padding: 20px 15px 20px 0;

          &::after {
            content: '\f105';
            font-family: "Font Awesome 6 Pro";
            font-weight: 300;
            font-size: 35px;
            @include center_y;
            right: 7px;
            color: rgba(black, 0.1);
            -webkit-font-smoothing: antialiased;
          }
        }

        .departure_date_wrapper {
          padding: 20px 0 20px 55%;

          &::after {
            right: 11px;
          }

          .date_box {
            background: none;
          }
        }
      }

      .guest_selector {
        width: 235px;
        height: 100%;
        padding: 20px 15px;
        margin: 0;

        &::after {
          right: 0px;
        }

        .placeholder_text {
          margin-top: 0;
          text-transform: lowercase;
        }
        
        b.button {
          display: none;
        }
      }

      .room_list_wrapper {
        top: 100%;
        width: 370px;

        &:lang(en) {
          width: 400px;

          .adults_selector {
            width: 40% !important;
          }

          .children_selector {
            width: 60% !important;
          }
        }
      }

      .wrapper_booking_button {
        width: 585px;
        height: 100%;

        .promocode_wrapper {
          width: 200px !important;
          padding: calc((90px - 20px) / 2) 10px; // 90px - full widget height, 20px - promocode label line height

          .promocode_input {
            outline-color: $corporate_1;

            &::-webkit-input-placeholder {
              color: $black;
              font-size: 14px;
              letter-spacing: 1.12px;
              line-height: 20px;
            }
          }

          .promocode_label {
            display: none;
          }
        }


        .modify-calendar-button {
          width: 128px;
          float: left;
          margin: calc((90px - 64px) / 2) 0;
          margin-right: 10px;
          position: relative;
          display: inline-flex;
          padding: 0 10px 0 55px;
          align-items: center;
          font-family: $title_family;
          font-weight: 500;
          font-size: 12px;
          letter-spacing: 0.96px;
          line-height: 20px;
          text-align: left;
          height: 64px;
          text-transform: uppercase;
          background: transparent;
          border-radius: 0;
          border: 1px solid $corporate-1;
          color: $corporate-1;
          @include transition(all, .5s);

          &::before {
            content: '\f073';
            font-family: "Font Awesome 6 Pro";
            color: inherit;
            font-weight: 300;
            font-size: 25px;
            @include center_y;
            left: 15px;
            -webkit-font-smoothing: antialiased;
          }
          &:hover {
            background: $corporate_1;
          }
        }
        button.submit_button {
          position: relative;
          width: 180px;
          height: 64px;
          margin: calc((90px - 64px) / 2) 0; // 90px - full widget height, 64px - button height
          padding: 0 10px 0 60px;
          font-family: $title_family;
          font-weight: 700;
          font-size: 14px;
          text-align: left;
          letter-spacing: 1.12px;
          line-height: 20px;
          background-color: $corporate_1 !important;
          transition: all .6s;

          &::before {
            content: '\f0e2';
            font-family: "Font Awesome 6 Pro";
            font-weight: 300;
            font-size: 25px;
            @include center_y;
            left: 20px;
            -webkit-font-smoothing: antialiased;
          }

          &:hover {
            opacity: .7;
          }
        }

        .spinner_wrapper {
          display: none;
        }
      }
    }
  }
}

div#full_wrapper_booking:not(.booking_widget_step_0) {
  .boking_widget_inline {
    .booking_form {
      .stay_selection {
        width: 460px;
      }

      .guest_selector {
        width: 320px;
      }

      .wrapper_booking_button {
        width: 360px;

        .modify-calendar-button {
          display: none;
        }

        .promocode_wrapper {
          width: calc(100% - 181px) !important;
        }
      }
    }
  }
}

#full_wrapper_booking.has_babies {
  .boking_widget_inline {
    .booking_form {
      .entry_date_label,
      .departure_date_label,
      .guest_selector label,
      .children_label,
      .babies_selector label,
      .rooms_label,
      .adults_label,
      .promocode_label,
      .promocode_input {
        font-size: 12px;
      }

      .room_list_wrapper {
        label {
          height: 25px;
          letter-spacing: 1.65px;
        }

        .room_list {
          .room {
            padding: 10px 0;
            margin-bottom: 15px;
            border-radius: 10px;
            background-color: $corporate_2;

            .adults_selector,
            .children_selector,
            .babies_selector {
              background: none;
              border: none;
              padding: 0 25px;
              width: calc(100% / 2) !important;
            }

            .adults_selector {
              border-right: 1px solid rgba(black, 0.2);
            }
          }

          .room.room_with_babies {
            .adults_selector,
            .children_selector,
            .babies_selector {
              width: calc(100% / 3) !important;
            }

            .children_selector.range_label_enabled,
            .babies_selector {
              .range-age {
                display: block;
                font-size: 10px;
              }
            }

            .children_selector {
              border-right: 1px solid rgba(black, 0.2);
            }
          }

          .full_ages_wrapper {
            padding: 0;
            margin-bottom: 20px;

            .kids_age_selection,
            .babies_age_selection {
              justify-content: flex-start;
              align-items: center;

              .kid_age_element_wrapper,
              .baby_age_element_wrapper {
                position: relative;
                width: calc((100% - (15px * 3)) / 4) !important;
                padding: 10px 30px 10px 15px;
                border: 1px solid rgba(black, 0.2);
                border-radius: 10px;
                box-sizing: border-box;

                &::before {
                  content: '\f107';
                  @include center_y;
                  right: 10px;
                  font-family: "Font Awesome 6 Pro";
                  font-weight: 300;
                  font-size: 25px;
                  color: $grey-1;
                  -webkit-font-smoothing: antialiased;
                }

                .selectricWrapper {
                  margin: 0;

                  .selectric {
                    margin: 0;

                    .label {
                      margin: 0;
                    }
                  }
                }
              }

              .kid_age_element_wrapper + .kid_age_element_wrapper,
              .baby_age_element_wrapper + .baby_age_element_wrapper {
                margin-left: 15px;
              }
            }
          }
        }
      }

      .guest_selector {
        width: 350px !important;
        label {
          margin-bottom: 0;
        }
      }

      .wrapper_booking_button {
        width: 380px !important;

        .promocode_wrapper {
          width: 200px !important;
        }

        .modify-calendar-button {
          padding: 0 5px 0 45px;
        }

        button.submit_button {
          width: 180px !important;
          float: left !important;
        }
      }
    }
  }
}

.datepicker_wrapper_element .ui-datepicker .ui-widget-header{
  .ui-datepicker-next, .ui-datepicker-prev{
    &.ui-state-hover, &.ui-datepicker-next-hover, &.ui-datepicker-prev-hover{
      span:before{
        color: white;
    }
    }
  }
}
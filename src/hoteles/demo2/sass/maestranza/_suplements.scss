div#step-2 {
  .additional_services_total_wrapper {
    .perform_additional_services_booking {
      background: $corporate_1;
    }
  }

  .all_additional_services_wrapper {
    .category_wrapper {
      .title_category {
        i {
          color: $corporate_1;
        }
      }

      .additional_services_wrapper {
        .additional_service_element {
          &::before {
            background: $corporate_1;
          }

          &::after {
            background: $corporate_1;
          }

          .service_selection_wrapper.counter_service_selection {
            .price_service {
              color: $corporate_1;
            }

            .add_service_button {
              i {
                color: $corporate_1;
              }

              .remove_label, .add_label {
                color: $corporate_1;
              }
            }

            .service_select {
              .days_selection_wrapper {
                color: $corporate_1;
              }
            }
          }

          &.selected {
            .service_selection_wrapper {
              .add_service_button {
                i {
                  color: #EC6363 !important;
                }
              }
            }
          }
        }
      }
    }
  }

  .supplements_list_wrapper {
    .booking-2-service-container {
      .booking-2-container-description {
        .booking-2-service-title {
          font-family: $text_family;
          font-weight: 400;
          font-size: 22px;
          letter-spacing: 1.1px;
          line-height: 1;
          text-transform: capitalize;
          color: $black;
        }
      }

      .booking-2-service-price {
        span {
          color: $corporate_1;
        }
      }

      .booking-2-services-list {
        .services_buttons_wrapper {
          .add_service_element {
            background-color: $corporate_1;
            font-family: $text_family;
            font-weight: 400;
            font-size: 18px;
            letter-spacing: 1.5px;
          }
        }
      }
    }
  }

  .booking_button_wrapper {
    .booking-button {
      position: relative;
      width: 275px;
      height: 35px;
      padding: 0 10px;
      margin: 0;
      border-radius: 0;
      background-color: $corporate_1;

      font-family: $text_family;
      font-weight: 400;
      font-size: 16px;
      text-align: center;
      letter-spacing: 0.85px;
      line-height: 21px;
      text-transform: none;
      white-space: nowrap;
      color: white;
      @include transition(all, 0.6s);
    }
  }
}

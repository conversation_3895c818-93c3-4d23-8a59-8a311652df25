div#step-2 {
  background: transparent;

  .booking-2-service-container {
    .booking-2-container-description {
      .booking-2-service-title {
        font-weight: 500;
        color: $black;
      }

      .booking-2-service-price {
        .price_per_service_label, .currencyValue, .monedaConv {
          color: $black;
        }
      }

      .booking-2-services-list {
        .services_buttons_wrapper {
          .add_service_element {
            background: $corporate_1;
            font-size: 18px;
            font-weight: 500;;
            text-transform: none;
            letter-spacing: 0.9px;
            line-height: 24px;
            @include transition(all, .5s);

            &:hover {
              background: #074F8B;
              opacity: 1;
            }
          }
        }
      }
    }
  }

  .booking_button_wrapper {
    .booking-button {
      background: $corporate_1;
      @include transition(all, .5s);

      &:hover {
        background: #074F8B;
        opacity: 1;
      }
    }
  }
}
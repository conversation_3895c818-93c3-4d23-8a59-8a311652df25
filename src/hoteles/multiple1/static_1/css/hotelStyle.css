.frigiliana #bg_top, .frigiliana {
	background: none;
	background-color: #ede8de;
}

.frigiliana #header{
	background-color: #ede8de;
	background: url(/img/lacoa/muestracolor.png) repeat-x;
}

.frigiliana #logo {
	padding-top: 20px;
	height: 125px;
	width: 165px;
}

.frigiliana #logo img {
	width: 162px;
	margin-left: 3px;
	height: 73px;
	margin-top: 27px;
	height: 70px;
}

.frigiliana #bg_top, .frigiliana {
	background: none;
	background-color: #ede8de;
}

.frigiliana #header{
	background-color: #ede8de;
	background: url(/img/lacoa/muestracolor.png) repeat-x;
	padding-bottom: 70px !important;
}

.frigiliana #logo {
	padding-top: 20px;
	height: 125px;
	width: 165px;
	background: url(/img/lacoa/ornament_big2.png) no-repeat;
}

.frigiliana #logo img {
	width: 162px;
	margin-left: 3px;
	height: 73px;
	margin-top: 27px;
	height: 70px;
}

.frigiliana #motorBusqueda{
	background: none !important;
	background-color: #fcf9f0 !important;
}


.frigiliana #searchForm label,.frigiliana #hab0,.frigiliana #hab1,.frigiliana #hab2,.frigiliana #promocodeP span {
	color: black !important;
}

.frigiliana .titSeccionesMotor {
	color: black !important;
}

.frigiliana .bgFecha {
	background: url(/img/lacoa/date_icon.jpg) no-repeat 100px white !important;
	height: 8px !important;
}

.frigiliana .no_widget .bestPrice {
	padding-top: 0px;
}

.frigiliana .no_widget #ticks{
	margin-top: 0px !important;
}

.frigiliana .no_widget .bestPrice {
	padding-top: 4px;
	max-width: 181px !important;
	min-width: 100px;
	margin-right: 10px !important;
}

.frigiliana .bestText, .bestImage {
	font-size: 11px !important;
}

.frigiliana .bestPrice img {
	height: 20px;
	margin-top: 7px;
}

/* ------------------------------------------ */

.correa #bg_top, .correa {
	background: none;
	background-color: #ede8de;
}

.correa #header{
	background-color: #ede8de;
	background: url(/img/lacoa/muestracolor.png) repeat-x;
}

.correa #contactContent{
	margin-right: 20px;
}

.correa #logo {
	padding-top: 20px;
	height: 125px;
	width: 165px;
}

.correa #logo img {
	width: 162px;
	margin-left: 3px;
	height: 73px;
	margin-top: 27px;
	height: 70px;
}

.correa #bg_top, .frigiliana {
	background: none;
	background-color: #ede8de;
}

.correa #header{
	background-color: #ede8de;
	background: url(/img/lacoa/muestracolor.png) repeat-x;
	padding-bottom: 70px !important;
}

.correa #logo {
	padding-top: 20px;
	height: 125px;
	width: 165px;
	background: url(/img/lacoa/ornament_big2.png) no-repeat;
}

.correa #logo img {
	width: 162px;
	margin-left: 3px;
	height: 73px;
	margin-top: 27px;
	height: 70px;
}

.correa #motorBusqueda{
	background: none !important;
	background-color: #fcf9f0 !important;
}


.correa #searchForm label,.frigiliana #hab0,.frigiliana #hab1,.frigiliana #hab2,.frigiliana #promocodeP span {
	color: black !important;
}

.correa .titSeccionesMotor {
	color: black !important;
}

.correa .bgFecha {
	background: url(/img/lacoa/date_icon.jpg) no-repeat 100px white !important;
	height: 8px !important;
}

.correa .no_widget .bestPrice {
	padding-top: 0px;
}

.correa .no_widget #ticks{
	margin-top: 0px !important;
}

.correa .no_widget .bestPrice {
	padding-top: 4px;
	max-width: 181px !important;
	min-width: 100px;
	margin-right: 10px !important;
}

.correa .bestText, .bestImage {
	font-size: 11px !important;
}

.correa .bestPrice img {
	height: 20px;
	margin-top: 7px;
}

/* ------------------------------------------ */

.fondonegro{
	background: black !important;
}

.fondonegro body{
	background: none;
}

.fondonegro .body1 {
	background: transparent url(/static_1/images/bg_reduced_quality2.jpg) top center repeat-x;
}

.fondonegro #headerWrapper {
	background: none;
	height: 0px;
}

.fondonegro #motorBusqueda {
	background: rgb(136, 190, 226);
}

.fondonegro #menu > li > a {
	color: black;
}

.fondonegro #menu {
	background: white;
}

.fondonegro .bgFecha {
	background: url(/static_1/images/booking/buscarFechaMejorado.png) no-repeat 0 0 !important;
}

.fondonegro #search-button2, .fondonegro .bigBlueButton {
	background-color: darkblue;
}

.fondonegro #motorBusqueda hr {
	border-bottom: solid 1px darkblue;
}

.fondonegro #content {
	padding-top: 0px !important;
}

.fondonegro .main .wrapper{
	background-color: white !important;
	color: black;
}

.fondonegro .top_nav a {
	color: black !important;
	margin-left: 15px;
}

.fondonegro #customer-support{
	padding-left: 15px;
}

.fondonegro #selectedLanguageText{
	color: black;
}

.fondonegro #footer {
	padding: 20px;
	margin-top: 10px;
	position: absolute;
	width: 850px !important;
}

.fondonegro .col1{
	margin-top: -40px;
}


.img-bordered {
	border: 4px solid #4979B7 !important;
}

#page1 h1 {
	font-size: 22px;
}

/* fix pictures in slider */

.pic {
	height: 426px !important;
	width: 600px !important;
}

/*  booking steps */

.fondonegro #headerWrapper, .zerbinetta #stickyMain #header {
	background: url(/static_1/images/newheader3.jpg) top center no-repeat;
}

.zerbinetta #logo_1 {
	display: none;
}

.zerbinetta .bestText {
	color: black;
}

.zerbinetta #bg_top {
	background: transparent;
}

.zerbinetta #footer_bottom_text {
	color: white;
}

.zerbinetta #footer_bottom_text a {
	color: white;
}

.zerbinetta #stickyFooter {
	background: black;
	width: 100%;
}

.zerbinetta #footer{
	background: black;
	text-align: center;
	width: 100%;
}

.zerbinetta #footer_top_text {
	display:none;
}

.zerbinetta #stickyWrap {
	background: white;
}

/* ------------------------------------------ */
/* Cotillo  */

.cotillo #main-sections {
	text-align: center;
}

.cotillo #stickyWrap {
	margin-bottom: 10px;
}

.cotillo #stickyWrap #header {
	background: transparent;
}

.cotillo #stickyWrap #bg_top {
	background: transparent;
}

.cotillo .bestText {
	color: #113397;
}

.cotillo #stickyFooter {
	background: #103399;
}

.cotillo #footer_bottom_text {
	background: transparent;
	color: white;
}

.cotillo #footer_bottom_text a {
	color: white;
}

/* ------------------------------------------ */
/* Sevilla Almeria  */

.sevillaalmeria #logoDiv {
	width: 100px;
	height: 95px;
}

.sevillaalmeria #logoImg {
	width: 100px;
}

.sevillaalmeria #ticks {
	width: 1020px;
	margin: 0 auto;
}

.sevillaalmeria #ticks .tick {
	background: url(/static_1/images/tick_blue_small.png) left center no-repeat;
	padding-left: 20px;
	margin-right: 200px;
}

.sevillaalmeria #topMenuDiv {
	width: auto;
	float: right;
}

.sevillaalmeria #topDiv {
	height: 160px;
}

.sevillaalmeria #reserva-facil {
	margin-right: 0 !important;
}

.sevillaalmeria #reservationTitle{
	font-size: 18px;
}

/* ------------------------------------------ */
/* Don Manuel */

.donmanuel #top, .donmanuel #footer, .donmanuel #main-sections {
	background-color: black;
}

.donmanuel #social-links-box p {
	color: #aa6d14;
	text-transform: uppercase;
}

.donmanuel #language-selector .selected{
	color: #ab6b11;
}

.donmanuel #ticks {
	color: #a96b14;
}

.donmanuel #search-button2 {
	background-color: #a96c13;
}

.donmanuel #reservationTitle,
.donmanuel #searchForm label,
.donmanuel #hab0,
.donmanuel #hab1,
.donmanuel #hab2,
.donmanuel #promocodeP span {
	color: #a96c13;
	font-weight: bolder;
}

.donmanuel .tick {
	background: url(/img/losj2/tick_black.png) no-repeat;
}

.donmanuel #contFechaEntrada input, .donmanuel #contFechaSalida input {
	background: url(/img/losj2/date_icon_brown.jpg) no-repeat 95px white;
	height: 25px;
}

/* booking steps don manuel */

.donmanuel #stickyMain #contFechaEntrada input,
.donmanuel #stickyMain #contFechaSalida input {
	background: url(/img/losj2/date_icon_brown.jpg) no-repeat 90px white !important;
	height: 25px;
	border: 0 !important;
}

.donmanuel #stickyMain #header {
	background: white;
}

.donmanuel #stickyMain #motorBusqueda {
	background: #dfdfdf !important;
	border-radius: 0 !important;
}

.donmanuel #stickyMain #search-button2 {
	border-radius: 0 !important;
	border: 0 !important;
}

.donmanuel #stickyMain .bestText {
	color: #a96b14;
}

.donmanuel #stickyFooter, .donmanuel #stickyFooter a {
	color: white;
}

.donmanuel #stickyFooter #footer_top_text {
	display:none;
}

.donmanuel #stickyFooter #footer {
	margin-top: 10px;
}

.donmanuel #stickyMain #promocodeP input {
	border: 0 !important;
}

/* mexico vera */

body.mexicovera {
	background: url(/static_1/images/bg_mexicovera.jpg) 0px 20px repeat-x
}

.mexicovera #logo {
	top: -10px;
}

.mexicovera .bigger {
	color: #00833f;
}

.mexicovera #language-selector .selected {
	color: #00833f;
}

.mexicovera #top-sections {
	margin-right: 50px;
	float: right;
	text-decoration: none;
	font-size: 14px;
}

.mexicovera #top-sections a {
	text-decoration: none;
	color: black;
}


.mexicovera #main-sections li {
	float: right;
	margin-top: 15px;
}

.mexicovera #main-sections a:hover {
	border-bottom-color: #00833f;
	border-bottom: 3px solid #00833f;
}

.mexicovera .section-active {
	color: #00833f;
	border-bottom: 3px solid #00833f;
}

.mexicovera #footer {
	background: #ae0001;
}

/* mexico vera booking widget */

.mexicovera #reservationTitle {
	border-bottom: 2px solid white;
	margin-left: 0;
}

.mexicovera #contFechaEntrada input, .mexicovera #contFechaSalida input {
	border: 2px solid #ad0101;
	background: url(/static_1/images/date_icon_mexicovera.jpg) no-repeat 95px white;
	height: 20px;
	padding-left: 10px;
	width: 110px;
}

.mexicovera #promocodeP input {
	border: 2px solid #ad0101 !important;
}

.mexicovera #search-button2 {
	color: #DC021A;
	background: rgb(246,234,234); /* Old browsers */
	background: -moz-linear-gradient(top, rgba(246,234,234,1) 0%, rgba(246,234,234,1) 49%, rgba(234,200,199,1) 53%, rgba(230,188,189,1) 100%); /* FF3.6+ */
	background: -webkit-gradient(linear, left top, left bottom, color-stop(0%,rgba(246,234,234,1)), color-stop(49%,rgba(246,234,234,1)), color-stop(53%,rgba(234,200,199,1)), color-stop(100%,rgba(230,188,189,1))); /* Chrome,Safari4+ */
	background: -webkit-linear-gradient(top, rgba(246,234,234,1) 0%,rgba(246,234,234,1) 49%,rgba(234,200,199,1) 53%,rgba(230,188,189,1) 100%); /* Chrome10+,Safari5.1+ */
	background: -o-linear-gradient(top, rgba(246,234,234,1) 0%,rgba(246,234,234,1) 49%,rgba(234,200,199,1) 53%,rgba(230,188,189,1) 100%); /* Opera 11.10+ */
	background: -ms-linear-gradient(top, rgba(246,234,234,1) 0%,rgba(246,234,234,1) 49%,rgba(234,200,199,1) 53%,rgba(230,188,189,1) 100%); /* IE10+ */
	background: linear-gradient(to bottom, rgba(246,234,234,1) 0%,rgba(246,234,234,1) 49%,rgba(234,200,199,1) 53%,rgba(230,188,189,1) 100%); /* W3C */
	filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#f6eaea', endColorstr='#e6bcbd',GradientType=0 ); /* IE6-9 */
}

.mexicovera #newsletter {
	margin-top: 60px;
	width: 189px;
	margin: 0 auto;
	margin-top: 30px;
	background-color: whiteSmoke;
	padding-left: 15px;
	border: 3px solid #DEDEDE;
	padding-right: 15px;
}

.mexicovera #newsletter h2 {
	color: #AE0001;
	font-weight: lighter;
	font-style: oblique;
	padding-bottom: 10px;
	border-bottom: 3px dotted #DEDEDE;
	padding-top: 15px;
	margin-bottom: 15px;
}

.mexicovera #newsletter form div {
	width: auto;
	margin-bottom: 10px;
	margin-top: 10px;
}

.mexicovera #newsletter input {
	width: 100% !important;
	border: 1px solid #DEDEDE !important;
	margin-left: 0 !important;
}

.mexicovera #form-newsletter div a div {
	cursor: pointer;
	border: 0 !important;
	background: whiteSmoke !important;
	margin: 0 !important;
	padding: 0 !important;
	text-align: right !important;
	display: block !important;
	width: 100% !important;
	color: #AE0001 !important;
}

.mexicovera .no_widget #logo{
	top: 10px !important;
}

.mexicovera .no_widget .bestText{
	color: black !important;
}

.mexicovera #header.no_widget {
	background: white !important;
}

/* granada centro */

.granadacentro #stickyWrap  #header {
	background: #EEEEEE;;
}

.granadacentro #stickyWrap .bestText {
	color: black;
}

.granadacentro input.promocode {
	color: black;
}

.hostaltoril #header.no_widget {
	background: #4a4a4a;
}

.hostaltoril #header.no_widget #logo img {
max-width: none;
max-height: none;
}

/* ------------------------------------------ */
/* Gala Noja */

.galanoja #stickyMain #header{
    background:#f2f2f2;
}
.galanoja #stickyWrap{
    font-family: arial !important;
}
.galanoja .bestText{
    color: black !important;
}
.galanoja #bestPrice2{
    color:black !important;
}
.galanoja#logo img {
    max-height: 120px;
}
.galanoja.stepContainer button{
    border: none;
    border-radius: 5px;
    font-weight: normal;
	padding: 6px 10px;
	background-color: #0086f7;
	color: rgb(255,255,255);
	font-size: 18px;
	cursor: pointer;
	overflow: visible;
}
.galanoja.stepContainer button:hover{
    background-color: rgb(204,204,51);
}
.galanoja .swMain ul.anchor li a.selected{
    background-color: #0086f7;
    border: 1px solid #0086f7;
}
.galanoja.swMain ul.anchor li a.selected:hover{
    background: #0078de;;
    border: 1px solid #0078de;;
}
.galanoja #bg_top{
    background: rgb(90,90,90);
    opacity: 0.8;
}

.galanoja.calendar th {
    background: #0086f7;
}



/********************* Motor de reserva *********************/

.galanoja #stickyMain #motor #closeButton{
    cursor: pointer;
}
.galanoja #stickyMain #motorBusqueda{
    background: #0086f7;
    color: rgb(255,255,255);
}
.galanoja #stickyMain #motorBusqueda .bgFecha{
    height: 18px !important;
	width: 88px !important;
	border: 1px solid #464646 !important;
    border-radius: 4px !important;
	cursor: pointer;
	background: rgb(240,240,240) url(/img/galaa/date_icon.png) no-repeat 75px !important;
	padding: 0 !important;
}
.galanoja .titSeccionesMotor {
    color: rgb(255,255,255);
}
.galanoja #stickyMain #motorBusqueda #configuraHabitaciones select{
    border: 1px solid #464646 !important;
    border-radius: 4px !important;
}

.galanoja #stickyMain #motorBusqueda input{
    width: 90px !important;
    border: 1px solid #464646 !important;
    border-radius: 4px !important;
}
.galanoja #stickyMain #motorBusqueda button{
    border: none;
    border-radius: 5px;
    font-weight: normal;
	padding: 6px 10px;
	background-color: rgb(190,190,51);
	color: rgb(255,255,255);
	font-size: 18px;
	cursor: pointer;
	overflow: visible;
}
.galanoja #stickyMain #motorBusqueda button:hover{
    background-color: #0078de;;
}
.galanoja #stickyMain #motorBusqueda .contButton{
    margin: 0 15px 0 0 !important;
    width: auto !important;
}
.galanoja #stickyMain #motorBusqueda span{
    float: none !important;
}
.galanoja #stickyMain #motorBusqueda #divPromocode{
    float: none !important;
}


/*******************************************************************/
/****************** booking process css for Velad ******************/
/*******************************************************************/

.velad #stickyMain #header{
    background: rgb(79,79,63);
}
.velad #stickyWrap{
    font-family: 'Rokkitt', arial;
    background: rgb(255,255,255) !important;
}
.velad .bestText{
    color: rgb(255,255,255);
}
.velad #logo img{
    max-height: 110px;
}
.velad .stepContainer button{
    border: none;
    border-radius: 5px;
    font-weight: normal;
	padding: 6px 10px;
	background-color: rgb(79,79,63);
	color: rgb(255,255,255);
	font-size: 18px;
	cursor: pointer;
	overflow: visible;
}
.velad .stepContainer button:hover{
    background-color: rgb(185,154,116);
}
.velad .swMain ul.anchor li a.selected{
    background: rgb(185,154,116);
    border: 1px solid rgb(185,154,116);
}
.velad .swMain ul.anchor li a.selected:hover{
    background: rgb(185,154,116);
    border: 1px solid rgb(185,154,116);
}
.velad #bg_top{
    background: rgb(255,255,255);
}


/********************* Motor de reserva *********************/

.velad #stickyMain #motor #closeButton{
    cursor: pointer;
}
.velad #stickyMain #motorBusqueda{
    background: rgb(255,255,255);
    color: rgb(120,120,120);
}
.velad #stickyMain #motorBusqueda .bgFecha{
    height: 18px !important;
	width: 88px !important;
	border: 1px solid #464646 !important;
    border-radius: 4px !important;
	cursor: pointer;
	background: rgb(240,240,240) url(/img/velad/date_icon.png) no-repeat 72px !important;
	padding: 0 !important;
}
.velad .titSeccionesMotor{
    color: rgb(120,120,120);
}
.velad #stickyMain #motorBusqueda #configuraHabitaciones select{
    border: 1px solid #464646 !important;
    border-radius: 4px !important;
}

.velad #stickyMain #motorBusqueda input{
    width: 90px !important;
    border: 1px solid #464646 !important;
    border-radius: 4px !important;
}
.velad #stickyMain #motorBusqueda button{
    border: none;
    border-radius: 5px;
    font-weight: normal;
	padding: 6px 10px;
	background-color: rgb(79,79,63);
	color: rgb(255,255,255);
	font-size: 18px;
	cursor: pointer;
	overflow: visible;
}
.velad #stickyMain #motorBusqueda button:hover{
    background-color: rgb(185,154,116);
}
.velad #stickyMain #motorBusqueda .contButton{
    margin: 0 15px 0 0 !important;
    width: auto !important;
}
.velad #stickyMain #motorBusqueda span{
    float: none !important;
}
.velad #stickyMain #motorBusqueda #divPromocode{
    float: none !important;
}
.velad #logo_2{
    display: none;
}


/*******************************************************************/
/************ booking process css for Virgen del Camino ************/
/*******************************************************************/

.virgendelcamino #stickyMain #header{
    background: rgb(255,255,255);
}
.virgendelcamino #stickyWrap{
    font-family: arial !important;
    background: rgb(255,255,255) !important;
}
.virgendelcamino .bestText{
    color: #e78400;
}
.virgendelcamino #logo img{
    max-height: 110px;
}
.virgendelcamino .stepContainer button{
    border: none;
    border-radius: 5px;
    font-weight: normal;
	padding: 6px 10px;
	background-color: #FF9C00;
	color: rgb(255,255,255);
	font-size: 18px;
	cursor: pointer;
	overflow: visible;
}
.virgendelcamino .stepContainer button:hover{
    background-color: #e78400;
}
.virgendelcamino .swMain ul.anchor li a.selected{
    background: #e78400;
    border: 1px solid #e78400;
}
.virgendelcamino .swMain ul.anchor li a.selected:hover{
    background: #e78400;
    border: 1px solid #e78400;
}
.virgendelcamino #bg_top{
    background: rgb(255,255,255);
}


/********************* Motor de reserva *********************/

.virgendelcamino #stickyMain #motor #closeButton{
    cursor: pointer;
}
.virgendelcamino #stickyMain #motorBusqueda{
    background: rgb(255,255,255);
    color: rgb(120,120,120);
}
.virgendelcamino #stickyMain #motorBusqueda .bgFecha{
    height: 18px !important;
	width: 88px !important;
	border: 1px solid #464646 !important;
    border-radius: 4px !important;
	cursor: pointer;
	background: rgb(240,240,240) url(/img/virgo/date_icon.png) no-repeat 72px !important;
	padding: 0 !important;
}
.virgendelcamino .titSeccionesMotor{
    color: rgb(120,120,120);
}
.virgendelcamino #stickyMain #motorBusqueda #configuraHabitaciones select{
    border: 1px solid #464646 !important;
    border-radius: 4px !important;
}

.virgendelcamino #stickyMain #motorBusqueda input{
    width: 90px !important;
    border: 1px solid #464646 !important;
    border-radius: 4px !important;
}
.virgendelcamino #stickyMain #motorBusqueda button{
    border: none;
    border-radius: 5px;
    font-weight: normal;
	padding: 6px 10px;
	background-color: #FF9C00;
	color: rgb(255,255,255);
	font-size: 18px;
	cursor: pointer;
	overflow: visible;
}
.virgendelcamino #stickyMain #motorBusqueda button:hover{
    background-color: #e78400;
}
.virgendelcamino #stickyMain #motorBusqueda .contButton{
    margin: 0 15px 0 0 !important;
    width: auto !important;
}
.virgendelcamino #stickyMain #motorBusqueda span{
    float: none !important;
}
.virgendelcamino #stickyMain #motorBusqueda #divPromocode{
    float: none !important;
}
.virgendelcamino #logo_2{
    display: none;
}
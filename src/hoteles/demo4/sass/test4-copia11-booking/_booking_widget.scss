div#full_wrapper_booking {
  width: 100%;
  min-width: 1140px;
  border-top: 1px solid rgba(black, 0.1);
  border-bottom: 1px solid rgba(black, 0.1);
  padding: 0;
  color: $black;

  .boking_widget_inline {
    .booking_form {
      width: 1140px;
      height: 90px;
      margin: 0 auto;
      position: relative;

      .entry_date_label,
      .departure_date_label,
      .guest_selector label,
      .children_label,
      .babies_selector label,
      .rooms_label,
      .adults_label {
        font-family: $text_family;
        font-weight: 400;
        font-size: 16px;
        letter-spacing: 1.28px;
        line-height: 20px;
        color: $corporate_2 !important;
      }

      .entry_date_label,
      .departure_date_label,
      .guest_selector label {
        font-size: 16px;
        text-transform: capitalize;
      }

      .entry_date_wrapper,
      .departure_date_wrapper,
      .guest_selector, .promocode_wrapper {
        padding: 15px;
        display: inline-flex;
        flex-direction: column;
        justify-content: space-between;
        align-items: stretch;
        height: 90% !important;
      }

      .adults_label,
      .children_label,
      .babies_selector label {
        text-transform: none;
      }


      .stay_selection .date_day,
      .guest_selector .placeholder_text {
        font-family: $title_family;
        font-weight: 400;
        font-size: 19px !important;
        letter-spacing: 1.52px;
        line-height: 20px;
        color: $black;
      }

      .date_day, .placeholder_text {
        font-weight: 700 !important;
      }

      .stay_selection .departure_date_wrapper,
      .guest_selector {
        &::after {
          content: '';
          display: block;
          @include center_y;
          height: 50px;
          width: 2px;
          background: rgba(black, 0.1);
        }
      }

      .guest_selector {
        .placeholder_text {
          span {
            display: none;
          }
        }
      }

      .stay_selection {
        width: 320px;
        height: 100%;

        .entry_date_wrapper,
        .departure_date_wrapper {
          border-bottom: none;
          height: 100%;
        }

        .entry_date_wrapper {
          padding: 15px 15px 15px 0;

          &::after {
            content: '\f105';
            font-family: "Font Awesome 5 Pro";
            font-weight: 300;
            font-size: 35px;
            @include center_y;
            right: 7px;
            color: rgba(black, 0.1);
            -webkit-font-smoothing: antialiased;
          }
        }

        .departure_date_wrapper {
          padding: 15px 0 15px 55%;

          &::after {
            right: 11px;
          }

          .date_box {
            background: none;
          }
        }
      }

      .guest_selector {
        width: 170px;
        height: 100%;
        padding: 15px 15px;
        margin: 0;

        &::after {
          right: 0px;
        }

        .placeholder_text {
          margin-top: 0;
          text-transform: lowercase;
        }

        b.button {
          display: none;
        }
      }

      .room_list_wrapper {
        top: 100%;
        width: 370px;

        &:lang(en) {
          width: 400px;

          .adults_selector {
            width: 40% !important;
          }

          .children_selector {
            width: 60% !important;
          }
        }
      }

      .wrapper_booking_button {
        width: 650px;
        height: 100%;

        .promocode_wrapper {
          width: 33.3% !important;
          //padding: calc((90px - 20px) / 2) 10px !important; // 90px - full widget height, 20px - promocode label line height

          .promocode_label, .promocode_input {
            font-size: 14px;
            line-height: 20px;
            letter-spacing: 1.12px;
            font-weight: 400;
            font-family: $title_family;
            color: $corporate_2;
          }

          .promocode_input {
            outline-color: $corporate_1;
          }
        }

        .modify-calendar-button {
          width: 150px;
          float: left;
          margin: calc((90px - 64px) / 2) 0;
          margin-right: 12px;
          position: relative;
          display: inline-flex;
          padding: 0 8px 0 40px;
          align-items: center;
          font-family: $title_family;
          font-weight: bold !important;
          font-size: 14px;
          letter-spacing: 1.15px;
          line-height: 17px !important;
          text-align: center;
          height: 60px;
          text-transform: uppercase;
          background: transparent;
          border: 1px solid $black;
          color: $black;
          @include transition(all, .5s);

          &::before {
            content: '\f073';
            font-family: "Font Awesome 5 Pro";
            color: $black;
            font-weight: 300;
            font-size: 25px;
            @include center_y;
            left: 15px;
            -webkit-font-smoothing: antialiased;
          }

          &:hover {
            background: $corporate_1;
          }
        }

        button.submit_button {
          position: relative;
          width: 200px;
          height: 62px;
          margin: calc((90px - 64px) / 2) 0; // 90px - full widget height, 64px - button height
          padding: 0 10px 0 60px;
          font-family: $title_family;
          font-weight: bold !important;
          font-size: 14px;
          text-align: center;
          border-radius: 2px;
          letter-spacing: 1.12px !important;
          line-height: 20px !important;
          background: $corporate_2 !important;

          &::before {
            content: '\f0e2';
            font-family: "Font Awesome 5 Pro";
            font-weight: 300;
            font-size: 25px;
            @include center_y;
            left: 30px;
            -webkit-font-smoothing: antialiased;
          }
        }

        .spinner_wrapper {
          width: 200px;
          height: 62px;
          background-color: rgba($corporate_2, 0.5);
          top: calc((90px - 64px) / 2);
          right: 2px;
          border-radius: 2px;
        }
      }
    }
  }
}

div#full_wrapper_booking:not(.booking_widget_step_0) {
  .boking_widget_inline {
    .booking_form {
      .stay_selection {
        width: 400px;
      }

      .guest_selector {
        width: 230px;
      }

      .wrapper_booking_button {
        width: 360px;

        .modify-calendar-button {
          display: none;
        }

        .promocode_wrapper {
          width: calc((90px - 20px) / 2) 10px !important;
        }

        .submit_button {
          position: absolute !important;
          right: -150px;
          width: 230px;
        }

        .spinner_wrapper {
          width: 230px;
          right: -150px;
        }
      }
    }
  }
}
div#full_wrapper_booking.booking_widget_step_2 {
  .wrapper_booking_button.special {
    float: right;
  }
}

#full_wrapper_booking.has_babies {
  .boking_widget_inline {
    .booking_form {
      /*.entry_date_label,
      .departure_date_label,
      .guest_selector label,
      .children_label,
      .babies_selector label,
      .rooms_label,
      .adults_label,
      .promocode_label,
      .promocode_input {
        font-size: 12px;
      }*/

      .stay_selection {
        width: 30% !important;
      }

      .guest_selector {
        width: 180px !important;
      }

      .room_list_wrapper {
        left: 230px !important;

        label {
          height: 25px;
          letter-spacing: 1.65px;
        }

        .room_list {
          .room {
            padding: 10px 0;
            margin-bottom: 15px;
            border-radius: 10px;
            background-color: $corporate_3;

            .adults_selector,
            .children_selector,
            .babies_selector {
              background: none;
              border: none;
              padding: 0 25px;
              width: calc(100% / 2) !important;
            }

            .adults_selector {
              border-right: 1px solid rgba(black, 0.2);
            }
          }

          .room.room_with_babies {
            .adults_selector,
            .children_selector,
            .babies_selector {
              width: calc(100% / 3) !important;
            }

            .children_selector.range_label_enabled,
            .babies_selector {
              .range-age {
                display: block;
                font-size: 10px;
              }
            }

            .children_selector {
              border-right: 1px solid rgba(black, 0.2);
            }
          }

          .full_ages_wrapper {
            padding: 0;
            margin-bottom: 20px;

            .kids_age_selection,
            .babies_age_selection {
              justify-content: flex-start;
              align-items: center;

              .kid_age_element_wrapper,
              .baby_age_element_wrapper {
                position: relative;
                width: calc((100% - (15px * 3)) / 4) !important;
                padding: 10px 30px 10px 15px;
                border: 1px solid rgba(black, 0.2);
                border-radius: 10px;
                box-sizing: border-box;

                &::before {
                  content: '\f107';
                  @include center_y;
                  right: 10px;
                  font-family: "Font Awesome 5 Pro";
                  font-weight: 300;
                  font-size: 25px;
                  color: $grey-2;
                  -webkit-font-smoothing: antialiased;
                }

                .selectricWrapper {
                  margin: 0;

                  .selectric {
                    margin: 0;

                    .label {
                      margin: 0;
                    }
                  }
                }
              }

              .kid_age_element_wrapper + .kid_age_element_wrapper,
              .baby_age_element_wrapper + .baby_age_element_wrapper {
                margin-left: 15px;
              }
            }
          }
        }
      }

      /*.guest_selector {
        label {
          margin-bottom: 0;
        }
      }*/

      .wrapper_booking_button {
        width: 610px !important;

        .promocode_wrapper {
          width: 32.3% !important;
        }

        .modify-calendar-button {
          width: 160px !important;
          height: 60px !important;
        }

        button.submit_button {
          width: 31% !important;
          float: left !important;
        }

        .spinner_wrapper {
          width: 31% !important;
        }
      }
    }
  }

  &:not(.booking_widget_step_0) {
    .boking_widget_inline {
      .booking_form {
        .stay_selection {
          width: 35% !important;
        }

        .guest_selector {
          width: 200px !important;
        }

        .wrapper_booking_button {
          width: 390px !important;

          .promocode_wrapper {
            width: calc(100% - 210px) !important;
          }

          .submit_button {
            position: absolute !important;
            right: 0;
            width: 50% !important;
          }

          .spinner_wrapper {
            width: 50% !important;
            right: 0 !important;
          }
        }
      }
    }
  }
}
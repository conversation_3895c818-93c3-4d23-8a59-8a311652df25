[data-role="header"] {
    background: white;
    border-bottom: 1px solid #e1e1e1;
    text-shadow:  rgb(255, 255, 255) 0px 0px 0px;
}

[data-role="footer"] {
    background: white;
    border-top: 1px solid #e1e1e1;
    border-bottom: 0;
}

[data-role="footer"] {
    text-shadow:  rgb(255, 255, 255) 0px 0px 0px;
}

[data-role="footer"] p {
    color: #2489ce;
}

#contact-button-wrapper a{
    color: white !important;
    text-shadow: 0;
    font-weight: normal;
}

iframe {
    max-width: 275px !important;
}
.phone-in-header {
  padding-bottom: 10px;
}
.phone-in-header span {
  color: black;
}

#logo {
  max-height: 100px !important;
}

#logo #logo_1 {
  float: left;
}

#logo h2 {
  float: right;
  color: rgb(111,19,83);
  font-size: 16px;
  width: 65%;
}

.phone-in-header {
  clear: both;
}
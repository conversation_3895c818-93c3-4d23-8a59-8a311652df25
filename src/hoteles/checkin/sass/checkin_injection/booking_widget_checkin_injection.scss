@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import "plugins/mixins";
@import "plugins/fancybox_2_1_5";
@import "plugins/spiners/all_spiners";


$primary_font: "Poppins", sans-serif;
$text_color: #000000;
$box_shadow: 0px 3px 15px rgba(#000000, 0.2);
$horizontal_padding: 15px;
$label_color: #000000;
$height: 70px;
$vertical_padding: 25px;
$black: $text_color;
$gray-1: #606163;

$corporate_1: #FDCC68;
$corporate_2: #000000;
$option_color: $corporate_2;
$white: rgb(255, 255, 255);

$booking_widget_color_1: $white; //body back ground & year input text color
$booking_widget_color_2: $corporate_1; //header background & input texts
$booking_widget_color_3: gray; //label texts
$booking_widget_color_4: gray; //not used, but must be defined

body.checkin-group {
  //variables which reasigns depending on namespace
  $widget_backgroud: #FFFFFF;
  $separator_color: #707070;
  $booking_btn_bg: #FDCC68;
  $booking_btn_bg_hover: #000000;
  $booking_btn_text: #000000;
  $booking_btn_text_hover: #FFFFFF;
  $widget_border: $black;

  @import "booking_widget_defaults";
  @import "datepicker";
  @import "booking_widget_styles";
  @import "responsive";
  @import "additional_styles";
}

body.checkin-hotels {
  $widget_backgroud: #68B9CE;
  $separator_color: #000000;
  $booking_btn_bg: #FFFFFF;
  $booking_btn_bg_hover: #000000;
  $booking_btn_text: #028AAD;
  $booking_btn_text_hover: #FFFFFF;
  $widget_border: #68B9CE;

  @import "booking_widget_defaults";
  @import "datepicker";
  @import "booking_widget_styles";
  @import "responsive";
  @import "additional_styles";
}

body.dwo-hotels {
  $widget_backgroud: #BFBBB7;
  $separator_color: #000000;
  $booking_btn_bg: #FFFFFF;
  $booking_btn_bg_hover: #000000;
  $booking_btn_text: #000000;
  $booking_btn_text_hover: #FFFFFF;
  $widget_border: #BFBBB7;

  @import "booking_widget_defaults";
  @import "datepicker";
  @import "booking_widget_styles";
  @import "responsive";
  @import "additional_styles";
}

body.bakour-hotels-and-resorts {
  $widget_backgroud: #CCEAE9;
  $separator_color: #000000;
  $booking_btn_bg: #FFFFFF;
  $booking_btn_bg_hover: #000000;
  $booking_btn_text: #000000;
  $booking_btn_text_hover: #FFFFFF;
  $widget_border: #CCEAE9;

  @import "booking_widget_defaults";
  @import "datepicker";
  @import "booking_widget_styles";
  @import "responsive";
  @import "additional_styles";
}



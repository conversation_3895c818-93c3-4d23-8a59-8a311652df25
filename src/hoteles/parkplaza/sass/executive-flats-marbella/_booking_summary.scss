.hidden_booking_summary {
  background: white !important;
  border-bottom: 1px solid $corporate_1 !important;

  .center_container {
    .booking-search-results {
      color: $black;

      .title_booking_breakdown {
        color: $corporate_1;
        font-family: $title_family;
        text-transform: none;
        font-size: 25px;
        letter-spacing: 0;
        line-height: 32px;
        width: 185px;

      }

      .booking-search-results__rooms-list{
          padding-top: 0 !important;
        }

      .booking-search-results__search-data, .booking-search-results__rooms-list {
        i {
          color: $black;
          font-weight: 400;
          font-size: 16px;
          &.fa-users{
            font-weight: 300;
            &:before{
              content: "\f500";
            }
          }
        }

        .booking-title-info, span {
          color: $black;
          font-size: 14px;
          font-weight: 400;
        }
      }
      &.booking-box__content.has_web_support{
        width: 80%;
      }
    }
  }

  .booking-button.booking-button--action {
    background: white !important;
    border: 1px solid $corporate_1;
    text-transform: uppercase;
    letter-spacing: 1.5px;
    font-weight: 400;
    color: $corporate_1 !important;
    transition: all 0.6s;
    border-radius:10px;

    &:hover {
      background: $corporate_1 !important;
      color: white !important;
    }
  }
  .call_center_wrapper{
    border-left-color: $corporate_1;
    width: 19%;
    .wrapper-new-web-support .web_support_label_1{
      margin-right: 0 !important;
      margin-left: 5px !important;
      width: 100%;
    }
  }
}

.clearfix {
  margin-bottom: 20px;

  .booking-box--search {
    .booking-search-results {
      background-image: none !important;
      border: none !important;
      padding-left: 0 !important;

      .title_booking_breakdown {
        font-family: $title_family;
        font-size: 25px !important;
        font-weight: 400 !important;
        letter-spacing: 0 !important;
        line-height: 32px !important;
        color: $corporate_1 !important;
        text-transform: none;
        width: 170px !important;
        padding: 0 70px 0 0 !important;
        &:before{
          top: 50%;
          right: 15px;
          transform: translateY(-50%);
          font-weight: 100;
          font-size: 42px;
          color: $corporate_1;
          position: absolute;
          font-family: "Font Awesome 6 Pro";
          content: '\f0f3';
        }
        &:after{
          content: '';
          position: absolute;
          height: 80%;
          top: 10px;
          right: -10px;
          width: 1px;
          background: $black;
        }
      }

      .booking-search-results__search-data, .booking-search-results__rooms-list {

        i {
          color: $black !important;
        }

        .booking-title-info, span, .search-item {
          color: $black !important;
          font-family: $text_family;
          letter-spacing: 0;
          line-height: 20px;
        }
      }

      .booking-search-results__rooms-list{
        top: 10px !important;
        i{
            display: none;
        }

        .booking-title-info{
              letter-spacing: 1px;
                font-size: 16px;
            &::before{
                content: "\f500";
                font-family: "Font Awesome 6 Pro";
                margin-right: 10px;
                color: $black;
                font-weight:300;
            }
        }
        .search-item{
              letter-spacing: 1px;
                font-size: 16px;
            .booking-title-info{

                &::before{
                    content: "\f236";
                }
            }
        }
      }

     .booking-search-results__search-data{
         .booking-title-info{
               letter-spacing: 1px;
                font-size: 16px!important;
             }
         .notranslate{
               letter-spacing: 1px;
                font-size: 16px!important;
             }
        }


      .booking-search-results__new-search {
        &::before {
          content: "\f133" !important;
          font-weight: 300 !important;
          color: $corporate_1 !important;
          font-size: 34px !important;
          font-family: "Font Awesome 5 Pro"!important;
        }

        .booking-button {
          background:white!important;
          font-family: $title_family;
          font-weight:400 ;
          padding-left: 70px;
          color: $corporate_1!important;
          transition: all 0.6s;
          text-transform: none;
          font-size: 18px;
          border-radius: 0;
          border: 1px solid $corporate_1;
        }
        &:hover {
          .booking-button{
            background: $corporate_1!important;
            color: white!important;
          }
          &:before{
            color: white!important;
          }
        }
      }
      &.has_web_support{
        width: 100% !important;
        margin-top: 40px !important;
      }
    }
    .call_center_wrapper{
      position: absolute;
      top: -20px;
      right: 0;
      height: 50px;
      width: 270px;
      border-color: $corporate_1;
      .wrapper-new-web-support .web_support_label_1{
        width: calc(100% - 50px);
        text-align: center;
      }
    }
  }
}

div#step-2 {
  background: white;

  .clearfix .booking-box--search .booking-search-results .booking-search-results__new-search {
        &::before {
          display: none;
        }

        .booking-button {
          border: none;
          background: $corporate_1!important;
          color: white!important;
          &:hover{
            background: $corporate_2!important;

          }
        }
    }

  .hidden_booking_summary .booking-button {
    background: $black;
    border-radius:10px;
  }
  .booking-2-service-container{
    .booking-2-service-item{
        .price_per_service_label,
        .currencyValue,
        .monedaConv{
            color: $black;
        }
    }
    .booking-2-service-title{
        color: $black;
    }
  }
}
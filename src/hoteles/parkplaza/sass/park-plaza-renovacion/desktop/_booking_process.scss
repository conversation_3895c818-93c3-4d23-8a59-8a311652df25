@import "site-header";
@import "actual_wizard_step";
@import "booking_widget";
@import "booking_summary";
@import "rooms";
@import "confirmations";


div#step-1 {
  display: flex;
  flex-direction: column;

  .clearfix {
    order: 2;
  }

  .banner-booking1-text {
    order: 1;
  }

  #rooms_b1_wrapper {
    order: 3;
  }

  .custom_pictures_range {
    margin: 15px auto;
  }

  .rooms_packages_selectors_wrapper.club_tabs_enable {
    order: 3;
    display: flex;
    align-items: flex-end;
    margin-bottom: 25px;
    border-bottom: 0;
    position: relative;

    &::before {
      content: "";
      position: absolute;
      height: 1px;
      background: $grey_medium;
      bottom: 0;
      left: 0;
      right: 0;
      z-index: 1;
    }

    .button_package_room {
      height: 60px;
      border-radius: 10px 10px 0 0;
      background: #F0F0F0;
      opacity: 1;
      align-items: center;

      .button_text {
        font-weight: 500;
      }

      &.active {
        background: white;
        height: 72px;
        border: 1px solid $grey_medium;
        border-bottom: 0;

        .button_text {
          font-weight: 700;
        }

        &::before {
          content: "";
          position: absolute;
          background: white !important;
          left: 0;
          bottom: -1px;
          right: 0;
          height: 3px;
          z-index: 2;
        }
      }

      &.club_rates_viewer {
        order: 1;
        margin-right: 20px;

        .button_text {
          font-size: 23px;
          font-family: $text_family;
          letter-spacing: 2.3px;
          display: flex;
          align-items: center;
          color: $grey_medium;

          img {
            height: 50px;
            margin: 0 5px;
          }

          span {
            margin-left: 10px;
          }
        }
      }

      &.rooms_selector {
        order: 2;
        margin-right: 15px;
        font-size: 22px;
        color: $grey_medium;
        font-weight: bold;

        .button_text {
          text-transform: none;
        }

        &:after {
          display: none;
        }
      }
    }

    .button_package_room.rooms_selector:nth-child(1n):before {
      left: -2px;
      right: -18px;
    }

    .button_package_room.club_rates_viewer:nth-child(1n):before {
      left: -2px;
      right: -2px;
    }
  }
}

div#step-2 {
  .upgrade_text{
    background: $corporate_1;
  }
  .booking-2-services-list {
    .services_buttons_wrapper {
      .add_service_element {
        background: $corporate_1;
      }
    }
  }

  .booking_button_wrapper .booking-button {
    background: $corporate_1;
    font-family: $text_family;
    border-radius:10px;
    padding: 0 15px !important;
    font-size: 15px !important;
    text-transform: uppercase;
    font-weight: 700;
  }
  .clearfix .booking-box--search .booking-search-results .booking-search-results__new-search .booking-button{
    padding-left: 15px !important;
    font-size: 15px !important;
    text-transform: uppercase;
    border-radius: 10px !important;
  }
}

div#step-3 {
  .booking-button--confirm-booking{
    font-weight:700;
    border-radius:10px;
    background:$corporate_1;
    font-family:$text_family;

    &:hover{
      background:$corporate_2;
    }
    &:before{
      border-radius:10px !important;
    }
  }
}


.swMain.step_3 {
  #step-4 {
    .legal_text_wrapper {
      div img {
        display: none;
      }
    }
  }
}
.matcher-start-v4{
  background: $black !important;
}
.rate-check-v4 {
  .matcher-header-wrapper {
    background: $black;
  }

  .matcher-full-body-wrapper {
    .matcher-ourprice-wrapper {
      background: $corporate-1 !important;
    }

    .matcher-body-wrapper {
      .white_wrapper {
        .matcher-board-selector {
          .background-board-image {
            background: $corporate-1;
          }
        }

        .matcher-our-price-container {
          background: $black;
        }
      }
    }
  }
}

.cards_banners_wrapper {
  .card_element_wrapper {
    background: $grey !important;
  }
}

.site-footer{
  background: white;
  padding: 50px 0;
  color: $black;
  border: 0 solid $grey;
  border-top-width:2px;
  #footer_bottom_text{
    color: $black;
    font-size: 16px;
    font-weight: 100;
    line-height: 30px;
    p, .extra_footer_content{
      font-size: 16px;
    }
  }
}

.price_calendar_wrapper{
  .full_container{
    .title{
      color:$black;
    }
    .bottom_wrapper{
      .bottom{
        .right_wrapper{
          .btn_booking{
            color:$corporate_1;
            border:1px solid $corporate_1;
            border-radius:10px;
          }
        }
      }
    }
  }
}
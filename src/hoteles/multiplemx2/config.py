from flask import request

from alito_booking_widget import AlitoScript, AlitoWidgetHandler
from golden_parnassus_booking_widget import GoldenParnassusScript, GoldenParnassusWidgetHandler
from kore_tulum_booking_widget import KoreTulumWidgetHandler, KoreTulumScript
from namaste_booking_widget import NamasteWidgetHandler, NamasteScript
from utils.flask_requests import response_utils
from utils.web.web_seeker_utils import WebSeekerHandler
from zereno_booking_widget import ZerenoScript, ZerenoWidget

applicationId = "secure-mexico2"


class CancunBayRedirectionsHandler(WebSeekerHandler):
	def get(self, *args):
		currentHost = request.host

		if 'cancunbayresort' in currentHost:
			response_utils.set_response_redirection("https://www.cancunbayhotel.com/", permanent=True)
			return ""

		return super(CancunBayRedirectionsHandler, self).get(self)


extra_routing = {
	'.*localhost.*': {
		'.*namastescript.*': NamasteScript,
		'.*namastewidget.*': NamasteWidgetHandler,
		'.*korescript.*': KoreTulumScript,
		'.*korewidget.*': KoreTulumWidgetHandler,
        '.*zerenoscript.*': ZerenoScript,
		'.*zerenowidget.*': ZerenoWidget,
		'.*parnassusscript.*': GoldenParnassusScript,
		'.*parnassuswidget.*': GoldenParnassusWidgetHandler,
		'.*': WebSeekerHandler,
	},
	'.*namaste-beach.*': {
		'.*namastescript.*': NamasteScript,
		'.*namastewidget.*': NamasteWidgetHandler,
		'.*': WebSeekerHandler,
	},
	'.*kore-tulum.*': {
		'.*korescript.*': KoreTulumScript,
		'.*korewidget.*': KoreTulumWidgetHandler,
		'.*': WebSeekerHandler,
	},
	'.*poza.*': {
		'.*': WebSeekerHandler,
	},
	'.*tanat.*': {
		'.*': WebSeekerHandler,
	},
	'.*golden-parnassus.*': {
		'.*parnassusscript.*': GoldenParnassusScript,
		'.*parnassuswidget.*': GoldenParnassusWidgetHandler,
		'.*': WebSeekerHandler,
	},
	'.*cancun.*': {
		'.*': CancunBayRedirectionsHandler,
	},
	'.*marisa.*': {
		'.*': WebSeekerHandler,
	},
	'.*raiz.*': {
		'.*': WebSeekerHandler,
	},
	'.*alito.*': {
		'.*alitoscript.*': AlitoScript,
		'.*alitowidget.*': AlitoWidgetHandler,
		'.*': WebSeekerHandler,
	},
	'.*zereno.*': {
		'.*zerenoscript.*': ZerenoScript,
		'.*zerenowidget.*': ZerenoWidget,
		'.*': WebSeekerHandler,
	},

	'.*': {
		'.*': WebSeekerHandler
	}
}

server_type = "F2"

#These templates get added to app.yaml (css, images)
templates = ['express1']
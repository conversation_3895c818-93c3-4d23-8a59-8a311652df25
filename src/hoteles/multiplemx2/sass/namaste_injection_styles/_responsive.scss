$mobile_height: 75px;
$mobile_padding: 15px 20px;

@media (min-width: 931px) {
  #widget_paraty:not(.popup):not(.only_popup),
  #widget-paraty:not(.popup):not(.only_popup) {
    #full_wrapper_booking {
      display: block !important;
    }
  }
  #widget_paraty.floating_widget #full_wrapper_booking,
  #widget-paraty.floating_widget #full_wrapper_booking {
    position: fixed;
    top: auto;
    bottom: 0;
  }

  // Inner sections
  body:not(.home) {
    #widget_paraty #full_wrapper_booking,
    #widget-paraty #full_wrapper_booking {
      position: fixed;
      top: auto;
      bottom: 0;
    }
  }
}

@media (max-width: 930px) {
  .datepicker_wrapper_element .specific_month_selector,
  .datepicker_wrapper_element .go_back_button,
  .datepicker_wrapper_element_2 .specific_month_selector,
  .datepicker_wrapper_element_2 .go_back_button,
  .datepicker_wrapper_element_3 .specific_month_selector,
  .datepicker_wrapper_element_3 .go_back_button {
    display: none !important;
  }
  .datepicker_wrapper_element,
  .datepicker_wrapper_element_2,
  .datepicker_wrapper_element_3 {
    z-index: 100000002;
  }

  #paraty_widget_container {
    z-index: 1001 !important;
    position: relative;
    left: auto;
    top: auto;
    right: auto;
    transform: none;
    -ms-transform: none;
    -webkit-transform: none;
    -moz-transform: none;
    -o-transform: none;
  }

  #widget-paraty {
    z-index: 1101;
  }

  #widget_paraty, #widget-paraty {
    background: none;
    padding: 0;
    margin: 0;
    @include transform(none);
    width: 0;
    height: 0;
    padding: 0 !important;
    -webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;

    #full_wrapper_booking {
      display: none;
      position: fixed;
      overflow: scroll;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      width: 100%;
      @include transform(none);
      z-index: 11000;
      background: white;

      #full-booking-engine-html-7 {
        position: absolute;
        top: 0;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        @include flex_xy;
        width: calc(100% - 40px);

        .booking_form {
          max-width: 420px;
          width: 100%;
          flex-direction: column;
          flex-wrap: wrap;
          padding: 15px;

          .stay_selection {
            position: relative;
            display: inline-flex;
            width: 100%;
            margin-bottom: 10px;
            padding: 5px 35px;
            margin-right: 0;

            &::after {
              bottom: 15px;
            }

            .start_end_label {
              &::after {
                right: -215px;
              }
            }

            .entry_date_wrapper {
              &::before {
                right: -400%;
              }
            }
          }

          .rooms_number_wrapper {
            width: 100%;
            height: auto;

            &:before {
              bottom: 15px;
            }

            &:after {
              display: none;
            }

            .rooms_label {
              font-size: 12px;
              color: $corporate_1 !important;
            }

            .selectricWrapper {
              &:before {
                font-size: 12px;
                color: $corporate_1;
              }

              .selectric {
                p.label {
                  font-size: 28px;
                }
              }
            }
          }

          .guest_selector {
            position: relative;
            width: 100%;
            height: auto;
            margin-bottom: 10px;
            padding: 5px 35px;
            margin-right: 0;
            min-height: 82px;
          }

          .room_list_wrapper {
            z-index: 2;
            bottom: 230px;
            right: auto;
            left: 50%;
            top: auto;
            @include transform(translateX(-50%));

            .room_list {
              .room {
                justify-content: space-around;
              }
            }
          }

          .wrapper_booking_button {
            display: block;
            width: 100%;
            position: relative;
            top: auto;
            bottom: auto;
            left: auto;
            right: auto;

            .promocode_wrapper {
              display: block;
              width: 100%;
              height: 82px;
              margin-bottom: 10px;
              padding: 5px 35px;
              margin-right: 0;

              .promocode_input {
                @include full_size;
                @include flex_xy;
                @include promocode_styles;
                width: 100%;


                &::-webkit-input-placeholder {
                  @include promocode_styles;
                }

                &::-moz-placeholder {
                  @include promocode_styles;
                }

                &:-ms-input-placeholder {
                  @include promocode_styles;
                }

                &:-moz-placeholder {
                  @include promocode_styles;
                }
              }

              &:after {
                display: none;
              }
            }

            .submit_button {
              display: block;
              width: 100%;
              height: 82px;
              margin-bottom: 10px;
              padding: 5px 35px;
              margin-right: 0;
            }
          }
        }
      }
    }

    .close_widget {
      position: fixed;
      top: 75px;
      right: 40px;
      text-align: right;
      color: $corporate_1;
      display: block;
      font-size: 24px;
      z-index: 5;
    }


    #floating_button {
      display: inline-block;
      width: 100%;
      position: fixed;
      bottom: 0;
      left: 0;
      right: 0;
      font-family: $title_family;
      font-size: 26px;
      font-weight: 700;
      text-align: center;
      background-color: $corporate_1;
      color: white;
      border: 1px solid $corporate_1;
      cursor: pointer;
      z-index: 1100;
      padding: 20px 10px;

      &.hidden {
        display: none !important;
      }
    }
  }


  .fancy-booking-search_v2 .container_popup_booking {
    max-width: 100%;
    width: 100%;

    img {
      width: 100%;
    }
  }
  .datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
    @include center_xy;
    top: 50% !important;
    left: 50% !important;
    position: fixed !important;
  }
  .absolute-footer {
    padding: 10px 0 60px;
  }
}

@media (max-width: 600px) {
  #widget-paraty {
    #full_wrapper_booking {
      #full-booking-engine-html-7 {
        .booking_form {
          .stay_selection {
            .entry_date_wrapper::before {
              right: -240%;
            }

            .start_end_label::after {
              right: -115px;
            }
          }

          .room_list_wrapper {
            bottom: 140px;
          }
        }
      }
    }
  }
}
@import "plugins/iconmoon";
@import "plugins/fontawesome5injectionfix";
@import "plugins/fontawesome5pro_inj";
@import "plugins/mixins";
@import "plugins/fancybox_2_1_5";
@import "plugins/spiners/all_spiners";

@import url('https://fonts.googleapis.com/css2?family=DM+Sans:wght@400;500;700&display=swap');

$black: #122A61;
$corporate_1: #122A61;
$corporate_2: $black;
$widget_bg: white;
$grey: #878787;
$separator_color: $corporate_1;
$label_color: $black;


$title_family: 'DM Sans', sans-serif;
$text_family: $title_family;

$fa: "FontAwesome";

$height: 75px;
$vertical_padding: 15px;
$horizontal_padding: 30px !default;
$width: 200px;
$box_shadow: 1px 1px 15px 3px rgba(0, 0, 0, 0.3);

@mixin box_shadow() {
  -webkit-box-shadow: $box_shadow;
  -moz-box-shadow: $box_shadow;
  box-shadow: $box_shadow;
}

@mixin input_base_styles() {
  position: relative;
  text-align: center;
  //background-color: $widget_bg;
  height: $height;
  //width: $width;
  padding: $vertical_padding $horizontal_padding;
  margin: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

@mixin label_styles() {
  display: block;
  text-align: left;
  font-family: $title_family;
  font-weight: 400;
  font-size: 15px !important;
  letter-spacing: 0.9px;
  color: $label_color !important;
  text-transform: capitalize;
  margin: 0 0 5px;
}


@mixin option_styles() {
  display: inline-block;
  font-family: $title_family;
  font-weight: 400;
  font-size: 20px;
  letter-spacing:0.4px;
  line-height: 22px;
  color: $label_color !important;
  cursor: pointer;
  margin: 0;
}


@mixin option_separator {
  &::after {
    position: absolute;
    top: 50%;
    right: 0;
    transform: translateY(-50%);
    content: '';
    width: 2px;
    height: 58px;
    background-color: $corporate_1;
  }
}

@mixin promocode_styles {
  color: $black;
  font-size: 16px;
  line-height: 18px;
  font-weight: 400;
  text-transform: uppercase;
  font-family: $title_family;
  letter-spacing: 1px;
  text-align: center;
  margin: 0;
  border: 0;
  padding: 0;
  box-shadow: none;
}

@import "booking_widget";
@import "booking_widget_popup";
@import "web";
@import "responsive";

.full_ages_wrapper {
  display: none !important;
}



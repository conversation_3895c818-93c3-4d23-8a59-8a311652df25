@media (min-width: 1091px) {
  #widget_paraty.popup {
    &::before {
      content: '';
      @include full_size;
      position: fixed;
      background-color: rgba(black, 0.5);
      z-index: 99;
      opacity: 0;
      @include transition(all, 0.7s);
      pointer-events: none;
    }

    &.opened {
      &::before {
        opacity: 1;
        pointer-events: auto;
      }
    }

    .close_widget {
      position: fixed;
      right: 0;
      bottom: calc(100% + 5px);
      display: block;
      font-family: "Font Awesome 5 Pro";
      font-weight: 300;
      font-size: 26px;
      text-align: right;
      color: $widget_bg;
      z-index: 5;
      cursor: pointer;
    }

    #full_wrapper_booking {
      position: fixed;
      width: 100%;
      max-width: 420px;
      top: 40%;
      @include transform(translate(-50%, -50%));
      z-index: 100;

      #full-booking-engine-html-7 {
        .booking_form {
          width: 100%;
          flex-direction: column;
          flex-wrap: wrap;
          padding: 25px;

          .stay_selection {
            position: relative;
            display: inline-flex;
            width: 100%;
            border-bottom: 1px solid $corporate_1;
            padding-left: 50px;

            &::after {
              content: '\f073';
              @include center_y;
              left: 0;
              font-family: "Font Awesome 5 Pro";
              font-weight: 300;
              font-size: 25px;
              color: $corporate_1;
            }

            .entry_date_wrapper {
              border-right: 1px solid $corporate_1;
              padding: 20px 20px 20px 0;
            }

            .departure_date_wrapper {
              padding: 20px 0 20px 20px;
            }

            .entry_date_wrapper, .departure_date_wrapper {
              background-color: transparent;
              width: 50%;
              height: auto;

              label {
                margin-bottom: 15px;
              }


              &:after {
                display: none;
              }

              .date_box .date_day {
                color: $corporate_1;
              }
            }

            .departure_date_wrapper {
              &::after {
                display: none;
              }
            }

            &:before {
              left: 50% !important;
              transform: translateX(-100%);
            }
          }

          .rooms_number_wrapper {
            width: 100%;
            height: auto;
            background-color: transparent;

            &:before {
              bottom: 15px;
            }

            &:after {
              display: none;
            }

            .rooms_label {
              font-size: 12px;
              color: $corporate_1 !important;
            }

            .selectricWrapper {
              &:before {
                font-size: 12px;
                color: $corporate_1;
              }

              .selectric {
                p.label {
                  font-size: 28px;
                }
              }
            }
          }

          .guest_selector {
            position: relative;
            width: 100%;
            height: auto;
            background-color: transparent;
            padding: 40px 0 40px 50px;
            border-bottom: 1px solid $corporate_1;

            &::before {
              content: '\f0c0';
              @include center_y;
              left: 0;
              font-family: "Font Awesome 5 Pro";
              font-weight: 300;
              font-size: 25px;
              color: $corporate_1;
            }

            &:after {
              display: none;
            }

            label {
              margin-bottom: 15px;
            }

            .placeholder_text {
              position: relative;
              text-align: left;
              text-transform: none;

              &::before {
                content: '\f107';
                @include center_y;
                top: 80%;
                right: 0;
                font-family: "Font Awesome 5 Pro";
                font-weight: 300;
                font-size: 45px;
                color: $corporate_1;
              }
            }
          }

          .room_list_wrapper {
            z-index: 1;
            width: 100%;
            bottom: 250px;
            right: auto;
            left: 50%;
            @include transform(translateX(-50%));

            .room_list {
              padding: 0;
              background-color: white;
              box-shadow: 0px 10px 19px 0px rgba(0,0,0,0.3);

              .full_ages_wrapper {
                label {
                  text-align: center;
                  font-size: 10px;
                }

                .kids_age_selection {
                  margin-left: 0;
                }
              }

              .room {
                margin: auto;
                justify-content: center;

                .adults_selector, .children_selector, .babies_selector {
                  > label {
                    font-size: 10px;
                    margin-top: 10px;
                    margin-bottom: 15px;

                    .range-age {
                      font-size: 9px;
                    }
                  }

                  .selectricWrapper {
                    &:before {
                      font-size: 10px;
                    }

                    .selectric {
                      .label {
                        font-size: 16px;
                      }
                    }
                  }
                }

                &.room_with_babies {
                  width: 100%;
                }
              }
            }
          }

          .wrapper_booking_button {
            display: block;
            width: 100%;
            position: relative;
            top: auto;
            bottom: auto;
            left: auto;
            right: auto;

            .promocode_wrapper {
              display: block;
              width: 100%;
              height: 60px;

              .promocode_input {
                @include full_size;
                @include flex_xy;
                @include promocode_styles;
                width: 100%;



                &::-webkit-input-placeholder {
                  @include promocode_styles;
                }

                &::-moz-placeholder {
                  @include promocode_styles;
                }

                &:-ms-input-placeholder {
                  @include promocode_styles;
                }

                &:-moz-placeholder {
                  @include promocode_styles;
                }
              }

              &:after {
                display: none;
              }
            }

            .submit_button {
              display: block;
              width: 100%;
              height: 60px;
              font-size: 16px;
              padding: 5px 0;
              margin: 0;
              top: 0;
              position: relative;

              &:before, &:after {
                display: none;
              }
            }
          }
        }
      }
    }
  }

  #widget_paraty.only_popup:not(.popup) {
    #full_wrapper_booking {
      display: none;
    }
  }
}




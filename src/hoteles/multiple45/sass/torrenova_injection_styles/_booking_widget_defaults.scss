@import "plugins/mixins";
@import "plugins/fancybox_2_1_5";
@import "plugins/spiners/all_spiners";

// colors definitions
$text_color: #1A1818;
$separator_color: #1A1818;
$box_shadow: 0px 3px 6px rgba(#000000, 0.29);
$horizontal_padding: 15px;
$label_color: #a3a3a3;
$height: 50px;
$vertical_padding: 25px;


@mixin promocode_placeholder() {
  color: #444;
  font-size: 14px;
  letter-spacing: 0.7px;
  line-height: 18px;
  font-weight: 300;
  font-family: $primary_font;
  text-align: center;
  text-indent: 0;
  white-space: normal;
}

@mixin promocode_styles() {
  @include promocode_placeholder();

  &::-webkit-input-placeholder {
    @include promocode_placeholder();
  }
  &::-moz-placeholder {
    @include promocode_placeholder();
  }
  &:-ms-input-placeholder {
    @include promocode_placeholder();
  }
  &:-moz-placeholder {
    @include promocode_placeholder();
  }
}

@mixin input_base_styles() {
  position: relative;
  background-color: transparent;
  height: $height;
  padding: $vertical_padding $horizontal_padding;
  margin: 0;
}

@mixin label_styles() {
  font-family: $primary_font;
  font-weight: 400;
  font-size: 14px;
  letter-spacing: 0.7px;
  line-height: 18px;
  color: $text_color;
  text-align: left;
  margin-bottom: 10px;
  display: block;
}


@mixin option_styles($fz: 18px, $ls: 1.5px, $ff: $primary_font) {
  margin: 0;
  font-family: $ff;
  font-weight: 300;
  font-size: $fz;
  letter-spacing: $ls;
  line-height: 25px;
  color: $text_color !important;
  cursor: pointer;
}

@mixin label_icon($content: '\f107') {
  position: relative;

  &:before {
    @include center_y;
    right: 0;
    content: $content;
    font-family: "Font Awesome 6 Pro";
    font-size: 18px;
    font-weight: 300;
    color: $text_color;
    z-index: 1;
  }
}

@mixin box_shadow() {
  -webkit-box-shadow: $box_shadow;
  -moz-box-shadow: $box_shadow;
  box-shadow: $box_shadow;
}

@mixin separator() {
  position: relative;
  margin: 0;

  &:after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: -1px;
    width: 1px;
    background: $separator_color;
    opacity: 0.25;
  }
}

@mixin selectric_styles($corporate_1) {
  .selectricWrapper {
    .selectricHideSelect, .selectricInput {
      display: none;
    }

    &.selectricOpen {
      .selectricItems {
        display: block;
        border: 0;
        box-shadow: $box_shadow;
      }
    }

    .selectricItems {
      position: absolute;
      top: calc(100% + 10px);
      background-color: white;
      display: none;
      z-index: 9999;

      ul {
        margin: 0;
        list-style: none;


        li {
          margin: 0;
          background-color: white;
          text-align: center;
          //color: $color_text;
          padding: 10px 5px;
          font-weight: 600;
          cursor: pointer;
          background-image: none;
          @include transition(all, .4s);

          &:hover {
            background-color: rgba($corporate_1, .6);
            color: white;
          }

          &.selected {
            background-color: $corporate_1;
            color: white;
          }
        }
      }
    }
  }
}

@mixin datepicker_styles($corporate_1, $corporate_2) {
  .datepicker_wrapper_element,
  .datepicker_wrapper_element_2,
  .datepicker_wrapper_element_3 {
    box-shadow: 0 5px 10px rgba($text_color, .3);
    border-radius: 0;
    z-index: 1100;

    .header_datepicker {
      background-color: $corporate_1;
      margin: 0;

      .close_button_datepicker {
        top: 9px;
        background: white;
        color: $text_color;
      }

      .specific_date_selector {
        text-transform: uppercase;
        font-family: $primary_font;

        &:before {
          display: none;
        }
      }
    }

    .datepicker_ext_inf_sd,
    .datepicker_ext_inf_ed {
      padding: 0;

      table.ui-datepicker-calendar {
        margin: 0 15px;
        width: calc(100% - 30px);
        min-height: 271px;

        th span {
          font-weight: bold;
          color: $corporate_2;
        }

        td {
          border-width: 0;
          background: transparent !important;
          height: 40px;
          padding: 5px 0;

          .ui-state-default {
            line-height: 30px;
            height: 30px;
            width: 30px;
            border-radius: 50%;
          }

          .ui-state-active {
            background: $corporate_1 !important;
          }

          .ui-state-hover {
            background: $corporate_1;
          }

          &.highlight, &.ui-datepicker-highlighted {
            .ui-state-default {
              background: rgba($corporate_1, .4) !important;
              color: white !important;
            }
          }

          &.ui-datepicker-start_date, &.last-highlight-selection {
            .ui-state-default {
              background: $corporate_1 !important;

              &:before {
                display: none;
              }
            }
          }
        }
      }
    }

    .ui-datepicker-header.ui-widget-header {
      border-bottom: 1px solid $text_color;
      font-family: $primary_font;

      .ui-datepicker-prev, .ui-datepicker-next {
        background: transparent;

        .ui-corner-all {
          background-color: transparent !important;

          &:before {
            @include center_xy;
            content: '\f107';
            font-family: "Font Awesome 5 Pro", sans-serif;
            font-size: 22px;
            font-weight: 400;
            color: $corporate_1;
          }

          span {
            background: transparent !important;
          }
        }

        .ui-icon {
          //background: transparent;
          //color: transparent;
          position: relative;
          @extend .fa-arrow-down;

          &:before {
            @extend .fal;
            color: $text_color;
            font-size: 20px;
            @include center_xy;
          }
        }

        &:hover, .ui-state-hover {
          background: transparent !important;

          .ui-icon:before {
            color: $corporate_2;
          }
        }
      }
    }

    .specific_month_selector {
      margin: 0;
      background: transparent;
      color: $corporate_2;

      strong {
        color: $text_color;
      }
    }
  }
}

@keyframes hotel_option {
  0% {
    text-align: left;
    transform: translateX(-100px);
    white-space: nowrap;
  }

  100% {
    text-align: left;
    transform: translateX(0);
    white-space: nowrap;
  }
}
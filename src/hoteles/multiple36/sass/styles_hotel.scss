$fontawesome5: true;
@import "booking/booking_process_v1/booking_header";
@import "booking/booking_all_styles";
@import "plugins/mixins";
@import "aucanada/font_clashDisplay";

@mixin individual_colors($corporate_1, $corporate_2) {
  .color1 {
    color: $corporate_2;
  }
  .bgc1 {
    background-color: $corporate_2;
  }

  #full_wrapper_booking {
    .boking_widget_inline .booking_form .wrapper_booking_button {
      .promocode_wrapper {
        width: 200px !important;
        height: 100%;
        padding: 25px 10px !important;

        .promocode-imput {
          height: 60px;
          border-radius: 20px;
          border: solid 2px $corporate_1;
        }
      }

      .modify-calendar-button:hover {
        background: $corporate_2;
        color: white;
      }

      .submit_button {
        background-color: $corporate_1
      }
    }
  }
  .fancybox-overlay .room_popup_individual_element .room_services .service_element i, div#step-1 .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_services .service_element i {
    color: $corporate_1;
  }
  .share_links_wrapper {
    .share_links_prev {
      background-color: $corporate_2;
    }

    .share_links_cont .share_link {
      background-color: $corporate_2;
    }
  }

  #wizard {
    div#step-1 {
      .rooms_packages_selectors_wrapper.has_packages {
        .button_package_room {
          background-color: transparent;
          border: none;
          padding-left: 225px;
          text-align: left;

          &.rooms_selector,
          &.packages_selector {
            z-index: 2;
            color: #777777;

            &::before {
              position: absolute;
              content: '';
              top: 0;
              left: 0;
              pointer-events: none;
              right: 0;
              height: 75px;
              width: 1130px;
              background-image: url(https://storage.googleapis.com/cdn.paraty.es/abrigall-masella/files/tab_left_active_blue.svg);
              background-repeat: no-repeat;
              background-size: contain;
              opacity: 0;
            }

            &:after {
              content: "";
              @include center_y;
              left: 160px;
              display: block;
              color: #777777;
              font-size: 35px;
              font-family: "Font Awesome 5 Pro";
            }

            &.active {
              z-index: 1;
              background-color: transparent !important;
              border: none !important;
              color: $corporate_1;

              &::before {
                opacity: 1;
              }

              &:after {
                color: $corporate_1;
                font-weight: 600;
              }
            }
          }

          &.rooms_selector {
            &::before {
            }

            &:after {
              content: "\f236";
            }
          }

          &.packages_selector {
            &::before {
              background-image: url(https://storage.googleapis.com/cdn.paraty.es/abrigall-masella/files/tab_right_active_blue.svg);
              transform: translateX(-50%);
            }

            &:after {
              content: "\f06b";
            }

            &.with_number .packages_number:before {
              background: $corporate_1;
            }
          }
        }
      }

      .contTipoHabitacion .preciosHabitacion .listadoHabsTarifas .botonReservarColumn .booking-button {
        background: $corporate_1;
        @include transition(all, .5s);

        &:hover {
          background: $corporate_2;
        }
      }
    }
  }
  #packages_b1_wrapper.v2 {
    .package_element_wrapper.custom_package .package_supplements_wrapper {
      .package_supplement_title {
        color: $corporate_1;
      }

      .guest_counter_wrapper .guest_element .tag .guest_tag {
        color: $corporate_1;
      }

      .counter_wrapper .counter .plus {
        background-color: $corporate_2;
      }
    }

    .package_element_wrapper .package_prices_wrapper .perform_package_booking_search {
      background-color: $corporate_1 !important;
    }

  }
  .fancybox-wrap.fancy-booking-search_v2 .description_top_popup_booking img {
    max-height: 220px;
  }
  .rate_conditions_popup_wrapper h3.rate_name, .package_hidden_popup .popup_title {
    background: $corporate_1;
    padding-right: 50px;
  }
  .shopping_cart_summary.v2 .full_body_wrapper .scrollable_content_wrapper .items_wrapper .item:not(:last-child) {
    border-color: $corporate_2;
  }
}

@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
body.booking_process_version_1.lesbrases {
  $corporate_1: #F29400;
  $corporate_2: #623814;
  $corporate_3: rgba(#623814, 0.3);
  $corporate_4: #0BCB89;
  $corporate_5: #E3F5F5;
  $corporate_6: rgba(#DEC2A6, 0.5);

  $red: #EC6363;
  $green: #2CA96E;
  $grey-1: #333333;
  $grey-2: #444444;
  $grey-3: #F5F5F5;
  $black: $grey-2;

  $title_family: 'Poppins', sans-serif;
  $text_family: 'Nunito', sans-serif;

  font-family: $text_family;
  color: $grey-1;

  @import "lesbrases-booking/lesbrases_booking_process";

  .color1 {
    color: $corporate_1;
  }

  .bgc1 {
    background-color: $corporate_1;
  }

  .remove_room_element {
    top: 25px !important;
  }

  .shopping_cart_summary.v2 {
    font-family: $text_family;
  }

  @include individual_colors($corporate_1, $corporate_2);

  .cards_banners_wrapper .card_element_wrapper .card_description,
  .cards_extended_wrapper .card_element_wrapper .card_description {

  }

  .cards_banners_wrapper,
  .cards_extended_wrapper {
    .card_element_wrapper {
      .card_description {
        @include ellipsis(2);
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 14px;
        margin-bottom: 14px;
      }
    }
  }

  .ui-state-hover {
    background: $corporate-1 !important;
  }
}


@import "arcos-montemar/main_montemar";


@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&display=swap');
body.booking_process_version_1.noa-boutique {
  $corporate_1: #31314c;
  $corporate_2: #DCB596;
  $corporate_3: rgba(#DCB596, 0.3);
  $corporate_4: #31314c;
  $corporate_5: #E3F5F5;
  $corporate_6: rgba(#DCB596, 0.5);
  $corporate_7: #436a91;

  $red: #EC6363;
  $green: #2CA96E;
  $grey-1: #333333;
  $grey-2: #444444;
  $grey-3: #F5F5F5;
  $grey-4: #393939;
  $black: $corporate_1;

  $title_family: 'Cormorant Garamond', serif;
  $text_family: 'Montserrat', sans-serif;

  font-family: $text_family;
  color: $black;

  @import "noa-booking/noa_booking_process";

  .color1 {
    color: $corporate_1;
  }

  .bgc1 {
    background-color: $corporate_1;
  }

  .remove_room_element {
    top: 25px !important;
  }

  .shopping_cart_summary.v2 {
    font-family: $text_family;
  }

  @include individual_colors($corporate_1, $corporate_2);

  .cards_banners_wrapper .card_element_wrapper .card_description,
  .cards_extended_wrapper .card_element_wrapper .card_description {

  }

  .cards_banners_wrapper,
  .cards_extended_wrapper {
    .card_element_wrapper {
      .card_description {
        @include ellipsis(2);
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 14px;
        margin-bottom: 14px;
      }
    }
  }
}

@import url('https://fonts.googleapis.com/css2?family=Advent+Pro:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,300;0,400;0,700;1,300;1,400;1,700&display=swap');
body.booking_process_version_1.marbella-inn {
  $corporate_1: #f6bf5a;
  $corporate_2: #a59282;
  $corporate_3: rgba(#DCB596, 0.3);
  $corporate_4: #31314c;
  $corporate_5: #E3F5F5;
  $corporate_6: rgba(#DCB596, 0.5);

  $red: #EC6363;
  $green: #2CA96E;
  $grey-1: #333333;
  $grey-2: #444444;
  $grey-3: #F5F5F5;
  $black: #333333;

  $title_family: 'Advent Pro', serif;
  $text_family: 'Nunito Sans', sans-serif;

  font-family: $text_family;
  color: $black;

  @import "marbella-inn/marbella_inn_booking_process";

  .color1 {
    color: $corporate_1;
  }

  .bgc1 {
    background-color: $corporate_1;
  }

  .remove_room_element {
    top: 25px !important;
  }

  .shopping_cart_summary.v2 {
    font-family: $text_family;
  }

  @include individual_colors($corporate_1, $corporate_2);

  .cards_banners_wrapper .card_element_wrapper .card_description,
  .cards_extended_wrapper .card_element_wrapper .card_description {

  }

  .cards_banners_wrapper,
  .cards_extended_wrapper {
    .card_element_wrapper {
      .card_description {
        @include ellipsis(2);
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 14px;
        margin-bottom: 14px;
      }
    }
  }

  .ui-datepicker-prev-hover, .ui-datepicker-next-hover {
    background: $corporate_1 !important;
  }

  &.de {
    .site-header__tick-item:nth-of-type(3) {
      word-break: break-word;
    }
  }
}


@import "booking/booking_colors";
@import "booking/booking_all_styles";
@import "plugins/fontawesome5pro";
@import "plugins/mixins";

@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;700&family=Poppins:wght@300;400;700&display=swap');

body.ourabay.booking_process_version_1 {

  $corporate_1: #5bc5c0;
  $corporate_2: #507d83;
  $corporate_3: #545454;
  $corporate_4: #63ac3c;

  $black: $corporate_3;
  $grey: #C4BEB6;
  $lightgrey: #F6F6F6;

  $body_text_color: $black;
  $links_color: $black;

  $font_1: 'Poppins', sans-serif;
  $font_2: "Montserrat", sans-serif;

  $title_family: $font_1;
  $text_family: $font_2;

  $shadow: 0px 3px 12px 3px rgba(0, 0, 0, 0.22);

  @import "ourabay/ourabay_booking_process";
  color: $body_text_color !important;
}

@import url('https://fonts.googleapis.com/css2?family=Be+Vietnam+Pro:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display&display=swap');
body.booking_process_version_1.torre-nunez {
  $corporate_1: #612C51;
  $corporate_2: #75654C;
  $corporate_3: rgba(#DCB596, 0.3);
  $corporate_4: #31314c;
  $corporate_5: #E3F5F5;
  $corporate_6: rgba(#DCB596, 0.5);

  $red: #EC6363;
  $green: #2CA96E;
  $grey-1: #333333;
  $grey-2: #444444;
  $grey-3: #F8F7F6;
  $black: #333333;

  $title_family: 'Be Vietnam Pro', sans-serif;
  $text_family: 'Be Vietnam Pro', sans-serif;
  $font-2: 'DM Serif Display', serif;

  font-family: $text_family;
  color: $black;

  @import "torre-nunez/torre_nunez_booking_process";

  .color1 {
    color: $corporate_1;
  }

  .bgc1 {
    background-color: $corporate_1;
  }

  .remove_room_element {
    top: 25px !important;
  }

  .shopping_cart_summary.v2 {
    font-family: $text_family;
  }

  @include individual_colors($corporate_1, $corporate_2);

  .cards_banners_wrapper .card_element_wrapper .card_description,
  .cards_extended_wrapper .card_element_wrapper .card_description {

  }

  .cards_banners_wrapper,
  .cards_extended_wrapper {
    .card_element_wrapper {
      .card_description {
        @include ellipsis(2);
        padding-top: 0;
        padding-bottom: 0;
        margin-top: 14px;
        margin-bottom: 14px;
      }
    }
  }

  .fancybox-wrap.fancy-booking-search_v2.full_screen_engine.custom_color_overlay .fancybox-skin {
    background: rgba(97, 44, 81, 0.9)
  }
}


@import url('https://fonts.googleapis.com/css2?family=Lekton:ital,wght@0,400;0,700;1,400&display=swap');
body.president-alcudia, body.sanroque-garden, body.inmood-aucanada {

  @import "aucanada/font_clashDisplay";

  $corporate_1: #211915;
  $corporate_2: #1A1A1A;
  $corporate_3: #937736;
  $corporate_4: #104E65;
  $corporate_5: #99989D;

  $red: #FC9C7F;
  $red2: #FC9C7F;
  $green: $corporate_2;
  $black: #1A1A1A;
  $black_2: #1A1A1A;
  $grey-1: #333333;
  $grey-2: #444444;
  $grey-3: #F5F5F5;
  $grey-4: #777777;

  $title_family: 'ClashDisplay', sans-serif;
  $text_family: 'ClashDisplay', sans-serif;
  $font_1: $text_family;

  color: $black;
  font-family: $text_family;

  @include booking_header_v2(white, $corporate_1, $grey-2, white, $red);

  @import "aucanada/site-header";
  @import "aucanada/booking_widget";
  @import "aucanada/actual_wizard_step";
  @import "aucanada/booking_summary";
  @import "aucanada/rooms_packages_selectors";
  @import "aucanada/rooms";
  @import "aucanada/packages";
  @import "aucanada/site-footer";
  @import "aucanada/suplements";
  @import "aucanada/personal_data";
  @import "aucanada/booking0";
  @import "aucanada/calendar_prices_popup_widget";
  @import "aucanada/datepicker";

  #login_wrapper_element {
    background: white !important;
    border: 2px solid #1a1a1a !important;
  }

  .lock_board .lock_board_wrapper:not(.toggle_discount) {
    width: 140px !important;
  }

  div#full_wrapper_booking .boking_widget_inline .booking_form .promocode_label {
    font-size: 14px;
  }

  .site-header .site-header__logo a img {
    height: 50px;
  }

  .modal_content {
    #register_form_wrapper #signup_form .register_title_form_wrapper,
    #login_form_wrapper_v1 .header_login_wrapper {
      background: $corporate_5 !important;

      .login_title, .main_form_title, .main_form_subtitle {
        color: white !important;
      }

    }
  }

  div#step-1 {
    .contTitTipoTarifa > .conditions_info_wrapper a {
      float: right;
      font-size: 12px;
      text-decoration: underline;
      margin-top: 7px;
      padding-right: 25px;
      margin-right: 7px;
      font-weight: 400;
      letter-spacing: 0;
      color: #211915;

      &:after {
        color: #211915 !important;
      }
    }

    .conditions_info_wrapper .rate_conditions_link {
      color: #211915;

      &:after {
        color: #211915 !important;
      }
    }

    .contTipoHabitacion {
      .contFotoDescripcion .contDescHabitacion
      .room_description_name_wrapper .cabeceraNombreHabitacion {
        .very_asked_message,
        span.just_booking_message {
          font-size: 12px;
        }
      }

      .preciosHabitacion .listadoHabsTarifas .botonReservarColumn .booking-button {
        background-color: $corporate_5;
      }
    }

    .last_day_cancellation_text {
      color: #00ac6b;

      &:before {
        color: #00ac6b;
      }
    }
  }

  div#step-2 {
    background-color: #f7f6ee;

    .all_additional_services_wrapper .additional_service_element {
      background-color: white;
    }
  }

  div#step-3 {
    background-color: #f7f6ee;

    .booking_details_prices_wrapper, .personal_details_payment_wrapper {
      box-shadow: none;
    }
  }

  .entry_date_wrapper {
    height: none !important;
  }

  .wrapper_booking_button {
    .promocode_wrapper {
      padding: calc((62px - 20px) / 2) 10px !important;
    }
  }

  .wizard-tab--small a {
    background: $corporate_1;

    &:after {
      border-left: 19px solid $corporate_1;
    }
  }

  .booking_engine_wrapper_process.has_babies {
    .stay_selection {
      width: 24% !important;

      .departure_date_wrapper:after {
        right: 0 !important;
      }
    }

    .wrapper_booking_button {
      width: 535px !important;
    }

    .guest_selector {
      width: 312px !important;
    }
  }

  #wizard {
    background: #f7f6ee;

    div#step-1 {
      .rooms_packages_selectors_wrapper.has_packages {
        .button_package_room {
          background-color: transparent;
          border: none;
          padding-left: 225px;
          text-align: left;

          &.rooms_selector,
          &.packages_selector {
            z-index: 2;
            font-family: $title_family;
            font-weight: 700;
            letter-spacing: 1.15px;
            line-height: 33px;
            font-size: 21px;
            color: $grey-4;


            &::before {
              position: absolute;
              content: '';
              top: 0;
              left: 0;
              pointer-events: none;
              right: 0;
              height: 75px;
              width: 1140px;
              background-image: url(https://storage.googleapis.com/cdn.paraty.es/golf-resort-river/files/tab_left_active.svg);
              background-repeat: no-repeat;
              background-size: contain;
              opacity: 0;
            }

            &:after {
              content: "";
              @include center_y;
              left: 160px;
              display: block;
              color: #777777;
              font-size: 35px;
              font-family: "Font Awesome 5 Pro";
            }

            &.active {
              z-index: 1;
              background-color: transparent !important;
              border: none !important;
              color: $corporate_1;

              &::before {
                opacity: 1;
              }

              &:after {
                color: $corporate_1;
              }
            }
          }

          &.rooms_selector {
            &::before {
            }

            &:after {
              content: "\f236";
            }
          }

          &.packages_selector {
            &::before {
              background-image: url(https://storage.googleapis.com/cdn.paraty.es/golf-resort-river/files/tab_right_active.svg);
              transform: translateX(calc(-50% - 5px));
            }

            &:after {
              content: "\f06b";
              font-weight: 300;
            }

            &.with_number .packages_number:before {
              background: $corporate_1;
            }
          }
        }
      }
    }

    &.step_2 {
      .booking-button--confirm-booking {
        &:hover {
          background-color: $black;
        }

        &::before {
          display: none;
        }
      }
    }
  }

  .conditions_info_wrapper {
    .last_day_cancellation_text {
      font-family: $text_family;
      font-style: normal;
      font-weight: 400;
      font-size: 13px;
      letter-spacing: 0.75px;
      color: $corporate_3;
      background: none !important;

      &:before {
        color: $corporate_3;
        content: "\f00c";
        font-family: "Font Awesome 5 Pro";
        font-style: normal;
        font-size: 14px;
        position: relative;
        right: 10px;
        top: 1px;
      }
    }

    .rate_conditions_link {
      color: $corporate_3;
      background: none !important;

      &:hover {
        color: $corporate_3 !important;
        text-decoration: underline !important;
      }

      &:after {
        content: "\f05a";
        font-family: "Font Awesome 5 Pro";
        color: $corporate_3;
      }
    }
  }

  div#step-1 .contTipoHabitacion .preciosHabitacion .listadoHabsTarifas .contTitTipoTarifa .cheapest_rate_message .conditions_info_wrapper a:after,
  div#step-1 .contTitTipoTarifa .advice_rate_message .conditions_info_wrapper a:after {
    display: none !important;
  }

  .booking_engine_wrapper_process #booking.boking_widget_inline .submit_button {
    background: $corporate_5;
  }

  #step-3 .booking-button--confirm-booking {
    background: #99989D !important;
  }

  .contTipoHabitacion .preciosHabitacion .listadoHabsTarifas .contTitTipoTarifa .cheapest_rate_message .conditions_info_wrapper a:hover {
    color: white !important;
    text-decoration: underline !important;
  }

  .just_booking_message {
    background: transparent !important;
    font-family: $text_family;
    font-weight: 700;
    font-size: 14px;
    letter-spacing: 0.85px;
    color: $corporate_3;

    &:after {
      display: none;
    }
  }

  .no-availability-section {
    padding: 10px 100px !important;
  }

  .no-availability-message {
    position: relative !important;
    top: auto !important;
    transform: none !important;
  }

  #calendar_price_availability {
    .header_wrapper_calendar_availability {
      z-index: 1;
    }

    .calendar_button, .graph_button {
      background: #D7D7D7;
      padding-left: 40px;
      color: black;

      &.active {
        background: white;
        color: black;
      }

      .ico {
        display: inline-block;
        margin-right: 5px;
        vertical-align: sub;
        position: absolute;
        width: 30px;
        height: 30px;
        background: transparent;

        &::before {
          position: absolute;
          top: 100%;
          left: -80%;
          transform: translate(-50%, -50%);
          font-family: "icomoon", sans-serif;
          font-size: 30px;
          color: black;
        }
      }
    }


    #prices-calendar {
      .calendars-section {
        .calendars {
          .calendars_wrapper {
            .calendar {
              tr {
                td {
                  .day-number {
                    color: $black;
                  }
                }
              }
            }
          }
        }
      }
    }

    .header_wrapper_calendar_availability .graph_calendar_selector .calendar_button .ico:before {
      content: "\e9a1";
    }

    .header_wrapper_calendar_availability .graph_calendar_selector .graph_button .ico:before {
      content: "\e905";
    }

    .header_wrapper_calendar_availability .popup_helper_wrapper {
      background: white;
      border: 2px solid $corporate_1;
      color: black;
      padding-left: 15px !important;

      &.search_button {
        background: $corporate_1;
      }
    }

    .inline {
      background: white;
      box-shadow: 0 0 50px rgb(0, 0, 0);
      border-radius: 0 10px 10px 10px;
      margin-bottom: 40px;

      &::before {
        content: "";
        position: absolute;
        background-color: white;
        top: 0;
        left: 0;
        width: 30px;
      }

      th {
        background: $corporate_1;
        height: 40px;
        font-size: 20px;
        border: none;

        th.day_label_element {
          font-size: 17px;
        }
      }

      td {
        border: 4px solid #D7D7D7;

        .day.restricted-day {
          background-color: #90773C;

          .day-content {
            .restriction-message {
              color: #fbbc05;
            }
          }
        }

        .day.available-day {
          background-color: #b99b57;
        }

        .day.not-available-day {
          background-color: #EDE5D4;
        }
      }

      .legend {
        p {
          color: black;
        }

        .available-day-box {
          background-color: #b99b57;
        }

        .restricted-day-box {
          background-color: #90773C;
        }

        .not-available-day-box {
          background-color: #EDE5D4;
        }
      }

      .buttons-section {
        .label_actual_selection {
          background: #EDE5D4;
          color: black;
        }

        .nights_number_wrapper {
          background: #999;
        }

        .button.disabled-button {
          background: $corporate_1 !important;
        }

        .modifyButtonCalendar {
          background: #90773C;
        }
      }
    }
  }

  .fancyboxContent_second_v.v2 .room_popup_individual_element {
    .room_services {
      padding: 0 !important;

      .service_element:nth-of-type(-n+4) {
        display: inline-flex !important;
        min-height: 60px;
        max-height: 100%;
        align-items: center;
        justify-content: center;

      }
    }
  }

  @media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
    .site-header {
      padding: 0 30px 0 30px !important;
      box-sizing: border-box;

      .header_center_wrapper {
        width: 100% !important;
      }
    }

    .listadoHabsTarifas {
      .regimen_tr_element {
        .regimenColumn {
          width: 50% !important;
        }
      }
    }
  }

  .matcher-start-wrapper.matcher-start-v4.rate-check-v4, .matcher-start-wrapper.matcher-start-v4.rate-check-v4 .matcher-header-wrapper {
    background-color: #99989d !important;

    .matcher-our-price-container {
      background-color: $corporate_5 !important;
    }
  }

  &.logged_agency {
    #login_wrapper_element {
      display: none;
    }

    #step-0,
    #step-1,
    #step-2,
    #step-3 {
      .booking-step-title.logged_agency {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: $corporate_5;
        border-radius: 10px;
        padding: 10px !important;
        margin-bottom: 20px;


        h2 {
          margin: 0 0 0 20px;

          .modification_header {
            color: white;
            margin-right: 10px;
          }

          .commision_percentage {
            /*color: white;
            font-weight: 300;*/
            display: none;
          }
        }

        .agency_buttons_wrapper {
          cursor: pointer;
          border-radius: 10px;
          padding: 12px 25px;
          background-color: white;
          color: $corporate_2;
          position: relative;

          .agency_home_btn {
            position: absolute;
            right: 105%;
            background: white;
            border-radius: 10px;
            padding: 13px 25px;
            top: 0;
          }

          .agency_logout_btn {
            position: relative;
            font-weight: 500;

            &::before {
              display: inline-block;
              vertical-align: middle;
              content: "\f08b";
              font-family: "Font Awesome 5 Pro";
              font-size: 20px;
              color: $corporate_2;
              font-weight: 300;
              margin-right: 10px;
              transition: all .4s;
            }


            &:hover {
              &::before {
                color: $corporate_5;
                margin-right: 13px;
              }
            }
          }
        }
      }
    }
  }
}

@import url('https://fonts.googleapis.com/css2?family=Lekton:ital,wght@0,400;0,700;1,400&display=swap');


body.toctoc-olof.booking_process_version_1,
body.toctoc-canteras.booking_process_version_1 {
  $corporate_1: #d2a469;
  $corporate_2: #393939;
  $corporate_3: #11CCC7;
  $corporate_4: #F3DD92;
  $corporate_5: #FFF1C2;
  $corporate_6: #d2a469;

  $white: #FFFFFF;
  $red: #DB5A42;
  $green: #35CE8D;
  $black: #393939;
  $grey-1: #393939;
  $grey-2: #393939;
  $grey-3: #393939;
  $grey-4: #393939;

  $title_family: 'Montserrat';
  $text_family: 'Montserrat';
  $font_1: 'Montserrat';

  font-family: $text_family;
  color: $black;

  @import "toc-toc-booking/toc_toc_booking_process";
  @import "toc-toc-booking/club_styles";
  @include booking_header_v2(white, $corporate_1, $grey-2, white, $red);

  .cards_banners_wrapper {
    .card_element_wrapper.active {
      max-height: 325px !important;
    }

    .newsletter_banner .newsletter_suscription_banner .subscribe_button_newsletter {
      background: black;
    }
  }

  .cards_banners_wrapper .card_element_wrapper.active {
    max-height: 345px !important;
  }

  &.de {
    div#step-2 {
      .all_additional_services_wrapper {
        .additional_service_element {
          .service_selection_wrapper {
            margin-left: 80px;

            .service_select {
              .add_service_button {
                max-width: max-content;

                .label {
                  width: max-content;
                }
              }
            }
          }
        }
      }
    }
  }

  div#full_wrapper_booking:not(.booking_widget_step_0) .boking_widget_inline .booking_form .wrapper_booking_button .promocode_wrapper{
    &:lang(de){
      width: 50%!important;
    }
  }

 div#step-2:lang(de) .all_additional_services_wrapper .additional_service_element.visible{
      .service_selection_wrapper{
        margin-left: auto;
        position: relative;
        left: 15px;
        &.selector_service_selection .price_service{
          position: relative;
          left: -25px;
        }
        .service_select {
          position: relative;
          left: -30px;
          .add_service_button{
            max-width: 80px;
          }
        }
      }

  }

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
   div.site-header .header_center_wrapper{
     width: 1100px;
   }
  div#full_wrapper_booking .boking_widget_inline .booking_form{
    .wrapper_booking_button{
      &:lang(es){
        width: 400px!important;
      }
      &:lang(en){
        width: 385px!important;
      }
    }

    .guest_selector{
        &:lang(de){
          width: 410px!important;;

          .placeholder_text{
            font-size: 17px!important;
          }
        }
     }

  }

}

  div#step-1 .contTipoHabitacion .preciosHabitacion .listadoHabsTarifas .contTitTipoTarifa .cheapest_rate_message .conditions_info_wrapper a{
    &:lang(en){
      padding-right: 20px;
    }
  }

}

body.toctoc-canteras.booking_process_version_1 {
  .banner_floating_wrapper.bottom.left{
    display: none;
  }
}



/* Club Styles Inmood Hotels */

@import url('https://fonts.googleapis.com/css2?family=Lekton:ital,wght@0,400;0,700;1,400&display=swap');
body.sanroque-garden, body.president-alcudia, body.inmood-aucanada {
  $title_family: 'Lekton', sans-serif;
  $text_family: 'Lekton', sans-serif;
  $font_1: $text_family;
  $black: #1a1a1a;
  $club_corporate_1: #E3E0C0;

  .contTipoHabitacion .preciosHabitacion .listadoHabsTarifas .precioTotalColumn .precioGeneralDiv .tPrecioTotal,
  .contTipoHabitacion .preciosHabitacion .listadoHabsTarifas .precioTotalColumn .precioGeneralDiv .tPrecioOferta {
    font-weight: 400 !important;
  }

  #login_wrapper_element {
    background: $club_corporate_1;
    border: 1px solid $black;
    border-radius: 0;

    .logo_wrapper {
      &::after {
        border-right: 1px solid $black;
      }
    }

    .content_login_wrapper {
      .club_icons_wrapper {
        .club_icon_element {
          .club_icon_description {
            color: $black;
            font-weight: 400;
          }
        }
      }

      .users_buttons_wrapper {
        .join_button_wrapper {
          .square_plus {
            display: none;
          }

          .want_join {
            background: $black;
            border-radius: 0;
            color: #FFFFFF;
          }
        }

        .already_member_wrapper {
          border: 1px solid $black;
          color: $black !important;
          border-radius: 0;
        }
      }
    }
  }

  #logged_user_info_wrapper {
    background: $club_corporate_1;
    border: 1px solid $black;
    border-radius: 0;

    .content_logged_wrapper {
      .center_content_wrapper {
        .logged_user_text, .extra_logged_user_info, .user_points {
          color: $black;
        }

        .logout_button_wrapper {
          i, span {
            color: $black;
          }
        }
      }
    }
  }

 div#step-1 table.listadoHabsTarifas tr .lock_board {
    padding: 15px 10px !important;

    .lock_board_wrapper {
      &.toggle_discount {
        border: none;
        min-width: 240px;

        .price_info_wrapper {
          text-align: left;
        }

        .discount_activated {
          .price_info_wrapper {
            width: 290px;
          }
        }
      }
      &:not(.toggle_discount) {
        border: 1px solid $black;
      padding: 10px !important;
      border-radius: 0 !important;
      position: relative;
      width: 120px;
      background: transparent !important;

      img {
        float: right;
        margin-left: 10px;
        margin-right: 0 !important;
      }

      span {
        color: $black;
        font-weight: 700;
      }

      span.currencyValue {
        font-size: 18px;
        letter-spacing: 0.99px;
        line-height: 24px;
      }

      .lock_ico {
        position: absolute;
        margin-left: 0 !important;
        top: -10px;
        left: 10px;
        background: white;
        padding: 0 5px;
        font-size: 10px !important;
        color: $black;

        &::after {
          content: "Desbloquear";
          font-family: $text-family;
          font-size: 9px;
          line-height: 22px;
          margin-left: 5px;
          font-weight: 700;
        }

        &:lang(en) {
          &:after {
            content: 'Unlock';
          }
        }
      }
    }
   }
  }

  .modal_content {
    #register_form_wrapper {
      border-radius: 0;

      #signup_form {
        .register_title_form_wrapper {
          background: $club_corporate_1;

          .main_form_title, .main_form_subtitle {
            color: $black;
          }
        }

        .inputs_wrapper {
          .buttons_wrapper_signup {
            .sign_up_button {
              background: $black;
              border-radius: 0;
            }
          }
        }
      }
    }

    #login_form_wrapper_v1 {
      border-radius: 0;

      .header_login_wrapper {
        background: $club_corporate_1;

        .login_title, .main_form_subtitle {
          color: $black;
        }
      }

      .login_block {
        .login_button_element {
          background: $black;
          border-radius: 0;
        }
      }
    }
  }

  &.fr {
    .lock_board .lock_board_wrapper .lock_ico::after {
      content: "Déverrouiller";
    }
  }



}
body.sanroque-garden{
    div#reservation{
    .location_wrapper{
      .main_title_block{
        display: none!important;
      }
    }
  }
}

@import url('https://fonts.googleapis.com/css2?family=DM+Serif+Display&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Assistant:wght@200;300;400;500;600;700;800&display=swap');
body.flipflop-cala-romantica.booking_process_version_1,
body.flipflop-cala-mandia.booking_process_version_1,
body.flipflop-surfing.booking_process_version_1 {
  $corporate_1: #006e82;
  $corporate_2: #1A1A1A;
  $corporate_3: #F7F6EE;
  $corporate_4: #4C4371;

  $red: $corporate_4;
  $green: $corporate_2;
  $black: #1A1A1A;
  $black_2: #1A1A1A;
  $grey-1: #333333;
  $grey-2: #444444;
  $grey-3: #F5F5F5;
  $grey-4: #777777;

  $title_family: 'DM Serif Display', serif;
  $text_family: "Assistant", sans-serif;
  $font_1: $text_family;

  color: $black;
  font-family: $text_family;

  @include booking_header_v2(white, $corporate_1, $grey-2, white, $red);
  @import "flipflop/flipflop_booking_process";

  &.logged_agency {
    #login_wrapper_element {
      display: none;
    }

    #step-0,
    #step-1,
    #step-2,
    #step-3 {
      .booking-step-title.logged_agency {
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: $corporate_1;
        border-radius: 10px;
        padding: 10px !important;
        margin-bottom: 20px;


        h2 {
          margin: 0 0 0 20px;

          .modification_header {
            color: white;
            margin-right: 10px;
          }

          .commision_percentage {
            /*color: white;
            font-weight: 300;*/
            display: none;
          }
        }

        .agency_buttons_wrapper {
          cursor: pointer;
          border-radius: 10px;
          padding: 12px 25px;
          background-color: white;
          color: $corporate_2;
          position: relative;

          .agency_home_btn {
            position: absolute;
            right: 105%;
            background: white;
            border-radius: 10px;
            padding: 13px 25px;
            top: 0;
          }

          .agency_logout_btn {
            position: relative;
            font-weight: 500;

            &::before {
              display: inline-block;
              vertical-align: middle;
              content: "\f08b";
              font-family: "Font Awesome 5 Pro";
              font-size: 20px;
              color: $corporate_2;
              font-weight: 300;
              margin-right: 10px;
              transition: all .4s;
            }


            &:hover {
              &::before {
                color: $corporate_1;
                margin-right: 13px;
              }
            }
          }
        }
      }
    }
  }

  #wizard {
    #step-4 #reservation {
      .email_header_logo {
        .titulo {
          color: white !important;
        }
      }
    }
  }
}

body.flipflop-cala-mandia.booking_process_version_1 {
  #step-3 {
    #login_wrapper_element {
      display: none;
    }

    .lock_rates_wrapper {
      display: none;
    }
  }
}


body.flipflop-cala-mandia.booking_process_version_1,
body.flipflop-cala-romantica.booking_process_version_1,
body.sanroque-garden.booking_process_version_1,
body.president-alcudia.booking_process_version_1,
body.flipflop-surfing.booking_process_version_1,
body.inmood-aucanada.booking_process_version_1 {
  div#step-1 {
    .contTipoHabitacion {
      .contFotoDescripcion {
        .contFotoHabitacion {
          .floating_messages {

            .just_booking_message {
              background-color: rgba(0, 0, 0, 0.8) !important;

              &::after {
                position: absolute;
                left: 100%;
                margin: 0 auto;
                right: 0;
                bottom: 0;
                content: "";
                z-index: 9;
                width: 0;
                height: 0;
                display: block;
                border-top: 15px solid rgba(0, 0, 0, 0.8);
                border-left: 15px solid rgba(0, 0, 0, 0.8);
                border-right: 15px solid transparent;
                border-bottom: 15px solid rgba(0, 0, 0, 0.8);
              }
            }

            .very_asked_message {
              background-color: #009cda87;

              &::after {
                content: '';
                border-top: 15px solid #009cda87;
                border-left: 15px solid #009cda87;
                border-right: 15px solid transparent;
                border-bottom: 15px solid #009cda87;
              }
            }
          }
        }

        .contDescHabitacion {
          &.with_rates_dropdown {
            .room_from_price_wrapper {
              .prices_dropdown_toggle {
                background-color: #1a1a1a;
              }
            }
          }
        }
      }
    }
  }
}

html[lang="de"]{
  body.flipflop-surfing.booking_process_version_1 #full_wrapper_booking.has_babies .boking_widget_inline .booking_form .guest_selector .placeholder_text{
    font-size: 16px!important;
  }
}


body.president-alcudia.booking_process_version_1,
body.inmood-aucanada.booking_process_version_1,
body.sanroque-garden.booking_process_version_1 {
  div#full_wrapper_booking.booking_engine_wrapper_process{
    .boking_widget_inline{
      .booking_form{
        .stay_selection{
          width: 25%!important;
          .entry_date_wrapper{
            padding: 15px 15px 15px 0;
            &::after{
                right: -5px;
            }
          }
          .departure_date_wrapper{
            padding: 15px 0 15px 57%;
          }
        }
        .wrapper_booking_button{
          &:lang(en), &:lang(de){
            width: 515px!important;
          }
          &:lang(de){
            width: 440px!important;
          }
          &:lang(fr){
            width: 525px!important;
          }

          .promocode_wrapper{
            padding: 21px 0px !important;
            &:lang(en){
              width: 145px!important;
            }
          }
          button.submit_button{
            &:lang(de), &:lang(fr){
              position: absolute;
            }
          }
        }
        .guest_selector{
          &:lang(en), &:lang(de){
            width: 330px!important;
          }
          &:lang(de){
            width: 400px!important;
          }
          &:lang(fr){
            width: 330px!important;
          }
        }
      }
      .room_list_wrapper{
        left: 258px;
      }
    }
  }
  .datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3{
    &[datepicker=startDate], &[datepicker=startDate]{
      left: 200px!important;
      &::after, &::before{
        left: -10px;
      }
    }
    &[datepicker=endDate], &[datepicker=endDate]{
      left: 350px!important;
    }
  }
  @media only screen and (max-width: 1550px){
    .fancybox-overlay{
      .fancybox-wrap{
        .fancybox-skin{
          .close_btn{
            top: 0px;
            right: -40px;
          }
        }
      }
    }
  }
  .fancybox-overlay:has( .hidden_tour_virtual){
    .fancybox-inner{
      width: 740px;
      .hidden_tour_virtual{
        width: 740px;
        iframe{
          width: 740px;
        }
      }
    }
  }
  div#step-1{
    .contTipoHabitacion{
      .contDescHabitacion{
        .room_from_price_wrapper{
          .prices_dropdown_toggle{
            .prices_dropdown_text{
              max-width: 185px;
            }
          }
        }
      }
    }
  }
  div#step-2{
    .additional_services_total_wrapper.fixed{
      .perform_additional_services_booking{
        top: 0px;
      }
    }
    .all_additional_services_wrapper{
      .additional_service_element{
        .service_selection_wrapper{
          .add_service_button{
            max-width: 90px;
            .label{
              width: 90px;
            }
          }
          .service_select{
            .quantity_selection_wrapper{
              .quantity_selection{
                select{
                  text-align: center;
                  padding: 6px 31px 6px 13px;
                }
              }
            }
          }
        }
      }
    }
  }
  @media only screen and (min-device-width: 768px) and (max-device-width: 1024px){
    div#full_wrapper_booking.booking_engine_wrapper_process{
      .boking_widget_inline{
        .booking_form{
          .stay_selection{
            width: 24%!important;
            margin-left: 11px;
          }
        }
      }
    }
    .tooltip-datepicker{
      display: none!important;
    }

  }

}
.step_item {
  &::after {
    background-color: $corporate_5 !important;
    border: none !important;
  }
}

.step_item.active {
  &::after {
    border: 2px solid $corporate_1 !important;
    background-color: transparent !important;
  }
}

.booking_step_sentence {
  font-weight: 300 !important;
}

.content_title {
  .title {
    font-size: 15px !important;
    text-transform: none;
  }
}

.dates {
  p {
    font-size: 14px;
    line-height: 26px;
  }
}

.search_text {
  p {
    font-size: 14px;
    line-height: 26px;
  }
}

.booking_widget_wrapper {
  .input_wrapper, .ui-state-active, .ui-state-default {
    &::before {
      background-color: $corporate-1 !important;
    }
  }

  .ui-datepicker-header, i {
    color: $corporate-1 !important;
  }
}

.double_button_wrapper {
  padding: 10px;
  justify-content: space-between;

  .modify_search, .show_calendar, .back_button {
    font-family: $text_family !important;
    color: $black !important;
    border: 1px solid $black !important;
    width: 48.5% !important;
    padding: 0 15px 0 40px !important;

  }

  .re-search_button {
    background: $corporate-1 !important;
  }

  .close_button {
    background: $corporate-6 !important;
  }
}

.tabs {
  padding: 0 10px !important;

  li {
    width: 50% !important;
    margin: 0 0 5px !important;
    background: $white !important;
    border-style: none !important;

    .tab_btn {
      background: $white !important;
      border: 1px solid $grey-1;
      color: #777777 !important;
      font-weight: 500 !important;

      &::before {
        display: none !important;
      }

      &.active {
        border: 1px solid $corporate_1;

        .tab_text {
          display: inline;
          color: $corporate_1 !important;

          &:before {
            display: none;
          }
        }
      }

      .tab_text {
        display: inline !important;

        &:before {
          content: "";
          width: 9px;
          height: 9px;
          background: $corporate_1;
          border-radius: 50%;
          opacity: 0.75;
          display: inline-block;
        }
      }
    }
  }
}

.room_option_block {
  .has_modal {
    .room_name {
      .title {
        font-size: 15px !important;
      }

      .info_btn {
        font-weight: 300 !important;
        font-size: 0.6rem !important;
      }
    }
  }

  .just_booking {
    background: black;
  }

  .very_asked {
    background: $corporate-1;

  }

  .rate_selected_title {
    font-family: "Merriweather" !important;
    font-size: 14px !important;

    span {
      font-family: $text_family;
      font-size: 11px !important;
    }

    .modal_launch {
      color: $black !important;
    }
  }

  .regime_description {
    .regime_title {
      font-size: 14px !important;
    }

    .regime_offer_detail, .price_through {
      color: $red !important;;
    }

    .min_stay_info {
      background-color: #ff7400 !important;
      font-weight: 600;
    }
  }

  .discount_percentage {
    background-color: $red !important;
  }

  .final_price {
    color: $black !important;
  }

  .submit {
    left: -20px !important;

    span {
      background: $corporate_1 !important;
      border-radius: 0 !important;
      width: 135px !important;
    }
  }

  .sectoption {
    border-color: $corporate_1;

    .titSelOpt {
      color: $corporate_1;
    }

    input[type=radio]:not(.payment_radiobtn):checked::before {
      background-color: $corporate_1;
    }
  }
}

footer > div:first-child {
  display: none !important;
}

.main_content_wrapper.step_0 .tabs_wrapper .tabs li + li:before {
  display: none;
}

.main_content_wrapper.step_1 {
  .additional_services {
    .additional_service_form {
      .continue_booking {
        background-color: $corporate-6;
      }

      .additional_services_wrapper {
        .btn.add_service {
          background-color: $corporate-1;
        }
      }
    }

    .additional_services_wrapper {
      .btn.add_service {
        background-color: $corporate-1;
      }

      .additional_service_element {
        .supplement_complete_content {
          .service_selection_wrapper {
            .service_select {
              .add_service_button {
                .add_label {
                  width: 100%;
                }
              }
            }
          }
        }
      }
    }
  }
}

.main_content_wrapper.step_2 {
  .reservation_summary .option_selected {
    .rate .conditions, .price {
      color: $corporate_1;
    }
  }

  .personal_details_form_wrapper .personal_details_form .bottom_button_wrapper #btn-finish-booking {
    background: $corporate_1;
  }
}

.banner-toggle-club{
  border-radius: 30px;
  order: 1;
  position: relative;
  margin-bottom: 20px;
  z-index: 1;
  &::before{
    content: "";
    background: url(https://storage.googleapis.com/cdn.paraty.es/toctoc-canteras/files/bg_club.png) no-repeat;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-size: contain;
    z-index: 0;
    opacity: 0.2;
  }
  &::after{
    content: "";
    background: $corporate_1;
    position: absolute;
    width: 100%;
    height: 100%;
    mix-blend-mode: hard-light;
    opacity: .15;
    z-index: 0;
  }

  .logotype-wrapper{
    width: 45%;
  }
  .main-content{
    margin-right: 0px;
    position: relative;
    z-index: 2;
    .main-text{
      background: $corporate_1;
      br{
        display: none;
      }
      span{
         margin: 0;
        font-size: 16px!important;
        letter-spacing: 0px;
        line-height: 21px;
        font-weight: bold;
        font-family: $title_family;
        color: white;
      }
      p{
        margin: 0;
        font-size: 16px!important;
        letter-spacing: 0px;
        line-height: 21px;
        font-weight: 400;
        font-family: $title_family;
        color: white;
      }
    }

  }

  .toggle-discount-wrapper{
    position: relative;
    width: 33%;
    .label-text{
      color: $black;
      font-size: 16px;
      line-height: 19px;
      font-weight: 400;
    }
    .discount-switch-wrapper{
      .switch{
       .knobs{
         &::before{
           background: $green!important;
         }
       }
        .layer{
          background: white!important;
          border: 1px solid $black;
        }
      }
    }
    &.discount-disabled{
      .discount-switch-wrapper{
        .switch{
          .knobs{
            &::before{
              background: $black!important;
            }
          }
        }
      }
    }
  }
}
table.listadoHabsTarifas{
 tr{
   .lock_board_wrapper{
     transition: all .4s ease-in-out;
     &.toggle_discount{
       .discount_disabled{
         .price_info_wrapper{
           font-family: $title_family;
           color: $corporate_1;
           text-align: left;
           transition: all .6s ease-in-out;
           .bottom_label{
             color: $corporate_1;

           }
         }
         .discount_ico{
           i{
             color: $corporate_1;
           }
         }
       }
       &:hover{

         .price_info_wrapper{
           text-align: center;
           .price_club{
             display: inline!important;
             color: $corporate_1;
             text-align: center;
           }
            .monedaConv, .currencyValue{
              color: $corporate_1;
            }

           .loyalty_club{
             display: none;

           }
         }
         .lock_tooltip{
           box-shadow: 0px 3px 6px rgba(0,0,0, 0.16);
           background: white;
           color: $corporate_1;
           width: auto;
           white-space: nowrap;
           border-radius: 10px;
           font-size: 14px;
           position: absolute;
           left: 5px;
           top: -40px;
           display: flex;
           align-items: center;
           justify-content: center;
           text-align: center;
              &::before{
                border-color: white transparent transparent transparent;
              }
         }
        .discount_ico{
          i{
            color: $corporate_1;
          }
        }
       }
     }
   }
 }
}
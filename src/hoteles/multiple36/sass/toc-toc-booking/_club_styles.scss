 div#step-1, div#step-2, div#step-3{
    .banner-toggle-club{
      border-radius: 30px;
      order: 1;
      position: relative;
      margin-bottom: 20px;

      &::before{
        content: "";
        background: url(https://storage.googleapis.com/cdn.paraty.es/toctoc-canteras/files/bg_club.png) no-repeat;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-size: contain;
        z-index: 0;
        opacity: 0.2;
      }
      &::after{
        content: "";
        background: $corporate_1;
        position: absolute;
        width: 100%;
        height: 100%;
        mix-blend-mode: hard-light;
        opacity: .15;
        z-index: 0;
      }
      .main-text{
        display: flex;
        align-items: flex-start;
        justify-content: flex-start;
        flex-direction: column;
        br{
          display: none;
        }
        strong{
          color: $green;
          span{
            font-size: 18px!important;
            font-family: $title_family;
            line-height: 22px;
            letter-spacing: 0px;
            color: $corporate_2;
            font-weight: 600;
            text-transform: none;
          }
        }
        span{
          color: $corporate_1;
        }
        p{
          margin: 0;
          font-size: 16px!important;
          letter-spacing: 0px;
          line-height: 21px;
          font-weight: 400;
          font-family: $title_family;
          color: $corporate_2;
        }
      }
      .logotype-wrapper{
        border-color: $corporate_2;
        position: relative;
        width: 23%;
      }
      .main-content{
        border-color: $corporate_2;
        position: relative;
        width: 56%;
      }
      .toggle-discount-wrapper{
        position: relative;
        width: 33%;
        .label-text{
          color: $corporate_2;
          font-size: 16px;
          line-height: 19px;
          font-weight: 400;
        }
        .discount-switch-wrapper{
          .switch{
           .knobs{
             &::before{
               background: $green!important;
             }
           }
            .layer{
              background: white!important;
              border: 1px solid $corporate_2;
            }
          }
        }
        &.discount-disabled{
          .discount-switch-wrapper{
            .switch{
              .knobs{
                &::before{
                  background: $corporate_2!important;
                }
              }
            }
          }
        }
      }
    }
   table.listadoHabsTarifas{
     tr{
       .lock_board_wrapper{
         transition: all .4s ease-in-out;
         &.toggle_discount{
           .discount_disabled{
             .price_info_wrapper{
               font-family: $title_family;
               color: $corporate_1;
               text-align: left;
               transition: all .6s ease-in-out;
               .bottom_label{
                 color: $corporate_1;

               }
             }
             .discount_ico{
               i{
                 color: $corporate_1;
               }
             }
           }
           &:hover{

             .price_info_wrapper{
               text-align: center;
               .price_club{
                 display: inline!important;
                 color: $corporate_1;
                 text-align: center;
               }
                .monedaConv, .currencyValue{
                  color: $corporate_1;
                }

               .loyalty_club{
                 display: none;

               }
             }
             .lock_tooltip{
               box-shadow: 0px 3px 6px rgba(0,0,0, 0.16);
               background: white;
               color: $corporate_1;
               width: auto;
               white-space: nowrap;
               border-radius: 10px;
               font-size: 14px;
               position: absolute;
               left: 5px;
               top: -40px;
               display: flex;
               align-items: center;
               justify-content: center;
               text-align: center;
                  &::before{
                    border-color: white transparent transparent transparent;
                  }
             }
            .discount_ico{
              i{
                color: $corporate_1;
              }
            }
           }
         }
       }
     }
   }

  }


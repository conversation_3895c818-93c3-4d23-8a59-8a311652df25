&.pt {
  #step-2 .booking-box--search .booking-box__content .booking-search-result__pets-number {
    right: 270px !important;
    background-color: transparent !important;
    width: 132px !important;
  }
  #step-2 .hidden_booking_summary.showed .booking-search-result__pets-number {
    right: 270px !important;
    background-color: transparent !important;
    width: 132px !important;
  }
}

div#step-1 .booking-box--search {
  margin-top: 0;

  .booking-box__content {
    padding: 0 0 0 20px !important;
    position: relative;
    background: white;
    border: 1px solid $corporate_1;
    .title_booking_breakdown {
      position: relative;
      z-index: 1;
      color: $corporate_2;
      height: 95px;
      max-width: 105px;
      vertical-align: middle;
      font-family: $title_family;
      text-align: left;
      text-transform: none;
      line-height: 22px;
      font-size: 18px;
      font-weight: 300;
      letter-spacing: 1px;
      padding: 0 80px 0 20px;
      @extend .fa-key;
      &:before {
        @extend .fa;
        position: absolute;
        top: 50%;
        -webkit-transform: translateY(-50%);
        -moz-transform: translateY(-50%);
        -ms-transform: translateY(-50%);
        -o-transform: translateY(-50%);
        transform: translateY(-50%);
        font-weight: 300;
        font-size: 30px;
        color: $corporate_2;
        z-index: -1;
        right: 20px;
      }
      &:after {
        content: '';
        position: absolute;
        height: 80px;
        top: 12px;
        right: 0;
        width: 2px;
        background: $corporate_1;
        opacity: .3;
      }
    }
    .booking-search-results__search-data {
      color: black;
      padding-left: 30px;
      font-size: 14px;
      padding-right: 30px;
      white-space: nowrap;
      max-width: 90px;
      .booking-title-info.booking-hotel-name {
        text-transform: none;
        font-size: 14px;
        font-family: $text_family;
        font-weight: bold;
        letter-spacing: 2px;
      }
      .booking-3-info {
        color: $black;
        margin-bottom: 10px;
        margin-top: 20px;
        font-family: $text_family;
        letter-spacing: 2px;
        font-size: 14px !important;
        text-transform: none;
        display: inline-block;
        font-weight: bold !important;
        padding: 0 3px;
      }
      .booking-title-info, i {
        color: $corporate_2;
        font-size: 12px;
        letter-spacing: 2px;
        &.fa-long-arrow-left,
        &.fa-long-arrow-right {
          margin-left: -5px;
          margin-right: 5px;
          font-size: 14px;
          @extend .icon-longarrow;
          &:before {
            display: inline-block;
            font-family: "icomoon", sans-serif;
            color: rgba($corporate_1,1);
          }
        }
        &.fa-long-arrow-left {
          &:before {
            -webkit-transform: rotate(180deg);
            -moz-transform: rotate(180deg);
            -ms-transform: rotate(180deg);
            -o-transform: rotate(180deg);
            transform: rotate(180deg);
          }
        }
      }
      b {
        font-weight: 400;
        font-size: 12px;
      }
      .notranslate {
        letter-spacing: 2px;
        font-size: 12px;
        > span {
          display: inline-block;
          vertical-align: middle;
          padding: 0 2px;
        }
      }
    }
    .booking-search-results__rooms-list {
      color: black;
      max-width: 280px;
      font-size: 12px;
      white-space: nowrap;
      .booking-title-info {
        color: $corporate_2;
        letter-spacing: 2px;
        font-weight: 400;
        &:before {
          content: "\e9e4";
          display: inline-block;
          vertical-align: middle;
          margin-right: 5px;
          font-family: "icomoon", sans-serif;
          font-size: 14px;
          color: rgba($corporate_1,1);
        }
      }
      .search-item {
        .booking-title-info {
          &:before {
            content: "\e9f8";
          }
        }
      }
      i {
        display: none;
      }
      b {
        font-weight: 400;
      }
    }

    .booking-search-results__new-search {
      .booking-button {
        font-family: $title_family;
        position: relative;
        background-color: $corporate_2;
        text-transform: capitalize;
        max-width: 240px;
        height: 85px;
        margin: 10px 10px 0 0;
        border-radius: 0 !important;
        padding: 20px 20px 20px 80px !important;
        letter-spacing: 2px;
        font-size: 14px;
        line-height: 20px;
        font-weight: 400;
        -webkit-transition: all 0.6s;
        -moz-transition: all 0.6s;
        -ms-transition: all 0.6s;
        -o-transition: all 0.6s;
        transition: all 0.6s;
        z-index: 1;
        @extend .icon-specialcalendar;
        &:hover {
          background: $corporate_1;
        }
        &:before {
          @extend .fa;
          font-weight: 300;
          font-family: "icomoon", sans-serif;
          position: absolute;
          top: 50%;
          left: 10px;
          -webkit-transform: translate(0%, -50%);
          -moz-transform: translate(0%, -50%);
          -ms-transform: translate(0%, -50%);
          -o-transform: translate(0%, -50%);
          transform: translate(0%, -50%);
          color: white;
          font-size: 35px;
          padding: 10px;
          border-radius: 50%;
          z-index: 1;
        }
      }
    }
  }
}

.booking-box--search .booking-box__content, .hidden_booking_summary {
  .booking-search-result__pets-number {
    background-color: #F6F4F1;
    text-align: center;
    position: absolute;
    right: 240px;
    top: 0;
    bottom: 0;
    display: inline-block;
    vertical-align: middle;
    width: 120px;

    .center_block {
      position: absolute;
      top: 50%;
      left: 50%;
      -webkit-transform: translate(-50%, -50%);
      -moz-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
      -o-transform: translate(-50%, -50%);
      transform: translate(-50%, -50%);
      display: table;
    }

    .fa {
      color: rgba($corporate_2,.3);
      font-size: 32px;
      font-family: "Font Awesome 5 Pro";
      font-weight: 300;

      &:before {
        content: "\f6d3";
      }
    }

    & + .booking-search-results__new-search {
      #modify-button {
        max-width: 240px;
      }
    }
  }
}

.hidden_booking_summary {
  border: none;
  box-shadow: 0 0 20px rgba(0, 0, 0, .3);
  padding: 0;
  background: #F6F4F1;
  .title_booking_breakdown {
    position: relative;
    z-index: 1;
    color: $corporate_2;
    height: 95px;
    vertical-align: middle;
    font-family: $title_family;
    text-align: left;
    line-height: 22px;
    font-size: 18px;
    font-weight: 600;
    letter-spacing: 1px;
    width: 140px;
    padding: 0 40px 0 10px;
    @extend .fa-key;
    &:before {
      @extend .fa;
      position: absolute;
      top: 50%;
      -webkit-transform: translateY(-50%);
      -moz-transform: translateY(-50%);
      -ms-transform: translateY(-50%);
      -o-transform: translateY(-50%);
      transform: translateY(-50%);
      font-weight: 300;
      font-size: 30px;
      color: $corporate_2;
      z-index: -1;
      right: 20px;
    }
    &:after {
      content: '';
      position: absolute;
      height: 80px;
      top: 12px;
      right: 0;
      width: 2px;
      background: $corporate_1;
      opacity: .3;
    }
  }
  .booking-search-results__search-data {
    color: black;
    padding-left: 30px;
    font-size: 14px;
    padding-right: 30px;
    white-space: nowrap;
    max-width: 90px;
    .booking-title-info.booking-hotel-name {
      text-transform: none;
      font-size: 14px;
      font-family: $text_family;
      font-weight: bold;
      letter-spacing: 2px;
    }
    .booking-3-info {
      color: $black;
      margin-bottom: 10px;
      margin-top: 20px;
      font-family: $text_family;
      letter-spacing: 2px;
      font-size: 14px !important;
      text-transform: none;
      display: inline-block;
      font-weight: bold !important;
      padding: 0 3px;
    }
    .booking-title-info, i {
      color: $corporate_2;
      font-size: 12px;
      letter-spacing: 2px;
      &.fa-long-arrow-left,
      &.fa-long-arrow-right {
        margin-left: -5px;
        margin-right: 5px;
        font-size: 14px;
        @extend .icon-longarrow;
        &:before {
          display: inline-block;
          font-family: "icomoon", sans-serif;
          color: rgba($corporate_1,1);
        }
      }
      &.fa-long-arrow-left {
        &:before {
          -webkit-transform: rotate(180deg);
          -moz-transform: rotate(180deg);
          -ms-transform: rotate(180deg);
          -o-transform: rotate(180deg);
          transform: rotate(180deg);
        }
      }
    }
    b {
      font-weight: 400;
      font-size: 12px;
    }
    .notranslate {
      letter-spacing: 2px;
      font-size: 12px;
      > span {
        display: inline-block;
        vertical-align: middle;
        padding: 0 2px;
      }
    }
  }
  .booking-search-results__rooms-list {
    color: black;
    max-width: 280px;
    font-size: 12px;
    white-space: nowrap;
    .booking-title-info {
      color: $corporate_2;
      letter-spacing: 2px;
      font-weight: 400;
      &:before {
        content: "\e9e4";
        display: inline-block;
        vertical-align: middle;
        margin-right: 5px;
        font-family: "icomoon", sans-serif;
        font-size: 14px;
        color: rgba($corporate_1,1);
      }
    }
    .search-item {
      .booking-title-info {
        &:before {
          content: "\e9f8";
        }
      }
    }
    i {
      display: none;
    }
    b {
      font-weight: 400;
    }
  }
  .booking-search-results__new-search {
    .booking-button {
      font-family: $text_family;
      position: relative;
      background-color: black !important;
      text-transform: uppercase;
      max-width: 240px;
      height: 85px;
      margin: 10px 10px 0 0;
      border-radius: 0 !important;
      padding: 20px 20px 20px 70px !important;
      letter-spacing: 2px;
      font-size: 12px;
      line-height: 20px;
      font-weight: 700;
      -webkit-transition: all 0.6s;
      -moz-transition: all 0.6s;
      -ms-transition: all 0.6s;
      -o-transition: all 0.6s;
      transition: all 0.6s;
      z-index: 1;
      @extend .icon-specialcalendar;
      &:hover {
        background: $corporate_1 !important;
      }
      &:before {
        @extend .fa;
        font-weight: 300;
        font-family: "icomoon", sans-serif;
        position: absolute;
        top: 50%;
        left: 10px;
        -webkit-transform: translate(0%, -50%);
        -moz-transform: translate(0%, -50%);
        -ms-transform: translate(0%, -50%);
        -o-transform: translate(0%, -50%);
        transform: translate(0%, -50%);
        color: white;
        font-size: 35px;
        padding: 10px;
        border-radius: 50%;
        z-index: 1;
      }
    }
  }
}

.booking-search-results .booking-search-results__new-search .total_price_label + .booking-button {
  font-family: $text_family;
  position: relative;
  background: $corporate_3;
  text-transform: uppercase;
  max-width: 280px;
  margin: 0;
  border-radius: 0;
  letter-spacing: 1px;
  font-size: 15px;
  font-weight: 300;
  -webkit-transition: all 0.6s;
  -moz-transition: all 0.6s;
  -ms-transition: all 0.6s;
  -o-transition: all 0.6s;
  transition: all 0.6s;
  z-index: 1;
  &:hover {
    background: $corporate_1;
  }

}

.booking-search-results .booking-search-results__new-search .total_price_label {
  font-family: $text_family;
  letter-spacing: 1px;
  font-weight: 300;
  margin-bottom: 0;
  margin-top: 0;
}

#step-2 {
  .hidden_booking_summary {
    .total_price_label {
      display: none;
    }

    .booking-search-results__new-search {
      top: 0;
      -webkit-transform: none;
      -moz-transform: none;
      -ms-transform: none;
      -o-transform: none;
      transform: none;
    }
  }

  .booking-button.booking-button--action {
    padding: 20px !important;

    &:before {
      display: none;
    }
  }
}
#apartmentIframeAll {
  #full_wrapper_booking.mobile_version {
    min-width: auto;
  }

  #full_wrapper_booking.mobile_version:not(.floating_widget) {
    min-width: auto;
    position: relative;
    bottom: auto;

    #full-booking-engine-html-7 {
      .booking_form {
        .room_list_wrapper {
          background-color: white;
        }
      }
    }
  }
}

@media (max-width: 1024px) {
  .specific_month_selector {
    display: none!important;
  }

  #apartmentIframeAll {
    #full_wrapper_booking.mobile_version {
      #full-booking-engine-html-7 {
        margin: auto;

        .booking_form {
          .room_list_wrapper {
            background-color: white;
            top: 50px;
            right: -55px;
            left: auto;
            padding-top: 5px;
          }

          .wrapper_booking_button {
            .submit_button {
              position: relative;
              top: auto;
              left: auto;
              right: auto;
              bottom: auto;
              float: left;
            }
          }
        }
      }
    }
  }

  html[lang=fr], html[lang=fr-FR] {
    #apartmentIframeAll #full_wrapper_booking.mobile_version #full-booking-engine-html-7 {
      left: -20px;
    }
  }

  html[lang=de]{
    #apartmentIframeAll #full_wrapper_booking.floating_widget #full-booking-engine-html-7 .booking_form .wrapper_booking_button .submit_button, #apartmentIframeAll #full_wrapper_booking.mobile_version #full-booking-engine-html-7 .booking_form .wrapper_booking_button .submit_button {
      position: relative;
      left: 0;
    }
  }
}

@media (max-width: 880px) {
  #apartmentIframeAll {
    #full_wrapper_booking.mobile_version {
      #full-booking-engine-html-7 {
        padding: 10px !important;
        left: 0;

        .booking_form {
          text-align: center;

          .dates_selector_personalized {
            float: none;
            display: inline-block;
            vertical-align: middle;

            .start_end_date_wrapper {
              .start_date_personalized {
                padding-left: 0;
              }
            }
          }

          .rooms_number_wrapper {
            float: none;
            //display: inline-block;
            display: none;
            vertical-align: middle;
          }

          .room_list_wrapper {
            @include center_x;
            right: auto;
            z-index: 10;
            left: 0%;

            &:before, &:after {
              display: none;
            }
          }

          .guest_selector {
            float: none;
            display: inline-block;
            vertical-align: middle;
            border-right: 0;
          }

          .wrapper_booking_button {
            display: block;
            margin: 10px auto 0;
            float: none;
            height: auto;

            .promocode_wrapper {
              float: none;
              margin-bottom: 10px;

              .minimal-form-input {
                padding-top: 0;
              }
            }

            .submit_button {
              float: none;
            }
          }
        }
      }
    }
  }

  html[lang=fr], html[lang=fr-FR] {
    #apartmentIframeAll #full_wrapper_booking.mobile_version #full-booking-engine-html-7 {
      left: 0px;
    }

    #full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .start_date_personalized, #full_wrapper_booking .dates_selector_personalized .start_end_date_wrapper .end_date_personalized {
      width: 140px;
    }
  }
}

@media (max-width: 768px) {
  #apartmentIframeAll {
    #full_wrapper_booking.mobile_version {
      &.floating_widget {
        #full-booking-engine-html-7 {
          margin: auto;
        }
      }

      #full-booking-engine-html-7 {
        margin: auto;
      }
    }
  }
}

@media (max-width: 750px) {
  #apartmentIframeAll {
    #full_wrapper_booking.mobile_version {
      position: relative !important;
      top: 0 !important;

      #full-booking-engine-html-7 {
        padding: 10px !important;

        .booking_form {
          .hotel_selection_wrapper {
            display: block;
            margin-right: 0;
            margin-bottom: 10px;

            .hotel_selection_options_wrapper {
              z-index: 10;
              width: 300px;
              @include center_x;

              .hotel_element {
                background-color: lighten(#565656, 15%);

                &:nth-child(even) {
                  background-color: white;
                }
              }
            }
          }

          .guest_selector {

          }

          .wrapper_booking_button {
            .promocode_wrapper {
              .minimal-form-input {
                padding-top: 0;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 620px) {
  #apartmentIframeAll {
    #full_wrapper_booking.mobile_version {
      #full-booking-engine-html-7 {
        width: 350px;
        .booking_form {
          .dates_selector_personalized {
            display: block;
            margin-bottom: 10px;

            .start_end_date_wrapper {
            }
          }

          .rooms_number_wrapper {
            border-left: 0;
            display: none;
          }
        }
      }
    }
  }
}
@media (max-width: 500px) {
  #apartmentIframeAll {
    #full_wrapper_booking.mobile_version {
      .booking_form_title .cancel_booking_link {
        top: -30px;
        left: -18px;
        margin: 0;
        height: auto;
        line-height: 1;
      }
      #full-booking-engine-html-7 {
        width: 300px;
        .booking_form {
          .dates_selector_personalized {
            display: block;
            margin-bottom: 10px;

            .start_end_date_wrapper {
              width: auto;
              padding: 10px 33px 10px 33px;
              &:after {
                right: 10px;
              }
              .nights_number_wrapper_personalized {
                display: none;
              }
            }
          }


          .rooms_number_wrapper {
            border-left: 0;
            display: none;
          }
        }
      }
    }
  }
  .datepicker_wrapper_element {
    position: absolute;
    left: 50% !important;
    @include translate(-50%, 0%);
  }
}
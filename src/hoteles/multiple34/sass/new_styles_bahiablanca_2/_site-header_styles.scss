div.site-header {
  height: 150px;
  padding: 0 30px 0 0;
  background: white !important;

  .site-header__logo {
    position: relative;
    width: 200px;
    height: 150px;

    img {
      margin: calc(20px / 2) 10px !important;
      max-width: 200px;
      height: auto!important;
      max-height: 100px !important;
      position: absolute;
      top: 45%;
      left: 50%;
      transform: translate(-50%,-50%);
    }
  }

  .site-header__ticks {
    position: relative;
    margin-top: calc(95px / 2);
    margin-right: 90px;

    &:lang(de) {
      margin-right: 75px;
    }

    .site-header__tick-item {
      margin-top: 5px;
      margin-right: 0;
      float: none;
      display: inline-block;
      vertical-align: middle;
      width: max-content;

      p {
        width: 100px;
        font-size: 10px;
        line-height: 12px;
        font-weight: 500;
        text-align: left;
        letter-spacing: 0.4px;
        color: $corporate-2 !important;
      }

      .icon-checkmark {
        font-size: 50px;
        margin-top: -10px;
        margin-right: 5px;
        display: inline-block;

        &:before {
          font-size: 22px !important;
          line-height: 22px !important;
          color: white !important;
          vertical-align: middle;
        }
      }

      &:first-of-type {
        margin-top: 5px !important;

        .icon-checkmark {
          padding: 5px !important;

          &:before {
            font-family: 'Font Awesome 6 Pro' !important;
            content: "\f058" !important;
            font-weight: 300;
          }
        }

        p {
          top: 0 !important;
        }
      }

      &:nth-of-type(2) .icon-checkmark:before {
        font-family: 'Font Awesome 6 Pro' !important;
        content: "\f2f7" !important;
        font-weight: 300;
      }

      &:nth-of-type(3) .icon-checkmark:before {
        font-family: 'Font Awesome 6 Pro' !important;
        content: "\f153" !important;
        font-weight: 300;
      }

      &.tick_customized {
        margin-left: 10px;

        img {
          max-width: 50px;
          height: auto;
        }

        &.open_popup {
          cursor: pointer;
        }
      }
    }
  }

  .language_header_selector_booking_process {
    border-left: 0;
    border-right: 0;
    width: 175px;
    bottom: 0;
    right: 0;
    top: 0;
    text-transform: uppercase;
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    justify-content: center;

    .selected_language {
      position: relative;
      margin-top: 0;
      top: 0;
      color: #4A4A4A;
      width: auto;
      transform: none;

      i {
        display: none;
      }
    }

    .selected_language_code {
      font-family: $text_family;
      font-weight: 400;
      font-size: 20px;
      line-height: 17.86px;
      letter-spacing: 2px;
      background: transparent;
      margin-right: 5px;
      color: #4A4A4A;

      &:before {
        content: "";
        position: absolute;
        right: -20px;
        top: 24px;
        width: 13px;
        height: 13px;
        background-image: url(https://storage.googleapis.com/cdn.paraty.es/test5-copia8/files/flecha-black.png);
        background-size: contain;
        background-repeat: no-repeat;
      }
    }

    .language_booking_selector_wrapper {
      top: 65%;
      width: 120px !important;
      background: white !important;
      right: -10px !important;
      box-shadow: 0px 6px 6px #00000029;
    }

    #currencyDiv {
      margin: 0 !important;
      width: auto;
      position: relative !important;
      top: 0 !important;
      background: transparent;

      .currency_selector_wrapper_v2 {
        width: 60px;

        .label {
          color: #444444;
        }

        &.active {
          background: white;

          .label {
            color: #444444;
          }
        }

        &::before {
          background-image: url(https://storage.googleapis.com/cdn.paraty.es/test5-copia8/files/flecha-black.png);
          width: 13px;
          height: 13px;
        }
      }

      #currencySelect {
        color: #444444;
        background: transparent;
        margin-top: 0;
        font-size: 20px;
        line-height: 29px;
        text-transform: uppercase;
        font-weight: 400;
        width: 60px;

        option {
          font-size: 16px;
          line-height: 18px;
          opacity: .5;
        }
      }

      &::after {
        content: "";
        position: absolute;
        right: 10px;
        bottom: 0;
        top: 0;
        width: 1px;
        background: #4A4A4A;
      }
    }

    .language_booking_selector_wrapper {
      background: transparent;
      width: auto;
      right: 0;
      left: auto;
      text-align: left;
      border-radius: 11px;

      &.active {
        padding: 5px 0;
      }
    }
  }

  .banner_popup_booking1 {
    display: none;
    z-index: 102;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: white;

    .content_wrapper {
      width: 1070px;
      border-radius: 18px;
      padding: 50px 70px;
      background: $corporate-1;
      box-sizing: border-box;
      @include center_xy;

      .close_popup {
        position: absolute;
        top: 20px;
        right: 20px;
        color: white;
        font-size: 35px;
        cursor: pointer;
      }

      .title {
        margin-bottom: 20px;

        h3 {
          font-size: 34px;
          font-family: $text_family;
          color: $black;
          font-weight: 700;
        }

        &:after {
          content: '';
          display: block;
          margin-top: 10px;
          width: 35%;
          height: 10px;
          background: url("https://www.onahotels.com/img/onac2/wave-navy-small.png");
        }
      }

      .desc {
        font-weight: normal;
        font-family: "Cera Pro", sans-serif;
        font-size: 16px;
        color: $black;
        margin-bottom: 50px;
      }

      .icon {
        margin-bottom: 35px;
        display: inline-flex;
        vertical-align: middle;
        width: calc((100% - 10px) / 2);
        align-items: center;
        justify-content: center;
        margin-right: 5px;

        i, span {
          display: inline-block;
          vertical-align: top;
        }

        i {
          text-align: center;
          width: 50px;
          color: white;
          font-size: 35px;
          margin-right: 10px;
        }

        img {
          width: 50px;
          margin-right: 10px;
          vertical-align: middle;
        }

        span {
          width: calc(100% - 65px);
          font-weight: 600;
          font-size: 16px;
          padding-right: 20px;
          color: $black;
          text-transform: uppercase;

          small {
            font-weight: 400;
            font-size: 14px;
            text-transform: none;

            a {
              font-weight: 600;
              color: $black;
            }
          }
        }

        &:nth-child(2n) {
          margin-right: 0;

          span {
            padding-right: 0;
          }
        }
      }
    }
  }
}

.actual_wizard_step {
  li.wizard-tab--small a,
  li.wizard-tab--big a {
    border-left: 2px solid white;

    &:not(.disable) {
      &:before {
        display: block;
        border-left-color: white;
        left: calc(100% + 2px);
      }
    }

    &.disable {
      &:before {
        display: block;
        border-left-color: white;
        left: calc(100% + 2px);
      }
    }
  }
}

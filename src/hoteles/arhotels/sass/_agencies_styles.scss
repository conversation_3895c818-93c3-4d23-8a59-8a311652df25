@import url('https://fonts.googleapis.com/css2?family=Tenor+Sans&display=swap');

.booking-step-title.logged_agency {
  position: relative;
  background-repeat: no-repeat;
  background-position: center;
  background-size: cover;
  overflow: hidden;
  min-height: 100px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px !important;
  box-sizing: border-box;
  font-family: "karla", sans-serif;
  font-weight: 400;
  text-transform: none;
  color: white;
  margin: 30px auto 10px;

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba($corporate_1, 0.6);
  }

  strong {
    font-weight: 700;
  }

  .agency_logo_header {
    width: 130px;
    margin-right: 50px;
    margin-left: 10px;

    &:after {
      content: '';
      width: 1px;
      height: 70%;
      position: absolute;
      top: 15%;
      left: 210px;
      background: white;
    }

    img {
      width: inherit;
      position: relative;
    }
  }

  .booking_title {
    position: relative;
    width: auto;
    white-space: nowrap;
    font-size: 21px;
    letter-spacing: 1px;
    text-transform: uppercase;
    padding-right: 50px;

    &::after {
      content: "";
      position: absolute;
      top: 50%;
      @include transform(translateY(-50%));
      right: 0;
      height: 55px;
      width: 1px;
      background-color: white;
    }

    img {
      max-width: 100px;
    }
  }

  h2 {
    position: relative;
    margin: 0 40px;
    display: flex !important;
    flex-wrap: nowrap;
    justify-content: center;
    align-items: center;

    .modification_header {
      display: inline-block;
      width: auto;
      font-weight: 700;
      font-size: 22px;
      letter-spacing: 1.4px;
      white-space: nowrap;
      margin-right: 30px;
      color: white;
      font-family: 'Karla', sans-serif;
    }

    .booking_desc {
      display: inline-block;
      width: auto;
      font-size: 14px;
      letter-spacing: 0.65px;
    }

    .commision_percentage {
      display: none;
    }
  }

  .agency_buttons_wrapper {
    text-align: right;
    margin-left: auto;

    a {
      position: relative;
      font-weight: 400;
      font-size: 14px;
      letter-spacing: 0.75px;
      text-transform: uppercase;
      white-space: nowrap;
      margin-right: 15px;

      &::before {
        content: "\f2f5";
        font-family: "Font Awesome 5 Pro";
        font-size: 25px;
        //@include transform(rotate(180deg));
        font-weight: 300;
        line-height: 1;
        display: inline-block;
        vertical-align: sub; // Middle
        margin-right: 5px;
        -webkit-font-smoothing: antialiased;
      }

      &.agency_home_btn {
        &::before {
          content: "\f015";
        }
      }
    }
  }
}
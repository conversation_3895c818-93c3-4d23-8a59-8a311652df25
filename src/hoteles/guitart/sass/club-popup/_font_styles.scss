@font-face {
    font-family: 'Futura';
    src: url('/static_1/fonts/futura/FuturaBT-Medium.eot');
    src: url('/static_1/fonts/futura/FuturaBT-Medium.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/futura/FuturaBT-Medium.woff2') format('woff2'),
        url('/static_1/fonts/futura/FuturaBT-Medium.woff') format('woff'),
        url('/static_1/fonts/futura/FuturaBT-Medium.ttf') format('truetype'),
        url('/static_1/fonts/futura/FuturaBT-Medium.svg#FuturaBT-Medium') format('svg');
    font-weight: 500;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Futura';
    src: url('/static_1/fonts/futura/FuturaBT-Book.eot');
    src: url('/static_1/fonts/futura/FuturaBT-Book.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/futura/FuturaBT-Book.woff2') format('woff2'),
        url('/static_1/fonts/futura/FuturaBT-Book.woff') format('woff'),
        url('/static_1/fonts/futura/FuturaBT-Book.ttf') format('truetype'),
        url('/static_1/fonts/futura/FuturaBT-Book.svg#FuturaBT-Book') format('svg');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}

@font-face {
    font-family: 'Futura';
    src: url('/static_1/fonts/futura/Futura-Bold.eot');
    src: url('/static_1/fonts/futura/Futura-Bold.eot?#iefix') format('embedded-opentype'),
        url('/static_1/fonts/futura/Futura-Bold.woff2') format('woff2'),
        url('/static_1/fonts/futura/Futura-Bold.woff') format('woff'),
        url('/static_1/fonts/futura/Futura-Bold.ttf') format('truetype'),
        url('/static_1/fonts/futura/Futura-Bold.svg#Futura-Bold') format('svg');
    font-weight: bold;
    font-style: normal;
    font-display: swap;
}
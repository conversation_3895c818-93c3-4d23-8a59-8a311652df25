div#full_wrapper_booking {
  width: 100%;
  min-width: 1140px;
  border-top: 1px solid rgba(black, 0.1);
  border-bottom: 1px solid rgba(black, 0.1);
  padding: 0;
  color: $grey-2;

  .boking_widget_inline {
    .booking_form {
      width: 1140px;
      height: 90px;
      margin: 0 auto;
      position: relative;

      .entry_date_label,
      .departure_date_label,
      .guest_selector label,
      .children_label,
      .rooms_label,
      .adults_label,
      .promocode_label,
      .promocode_input {
        font-family: $text_family;
        font-weight: 300;
        font-size: 13px;
        letter-spacing: 1px;
        line-height: 20px;
        color: $grey-2 !important;
        white-space: nowrap;
      }

      .entry_date_label,
      .departure_date_label,
      .guest_selector label {
        margin-bottom: 15px;
        font-size: 16px;
        text-transform: capitalize;
      }

      .stay_selection .date_day,
      .guest_selector .placeholder_text {
        font-family: $title_family;
        font-weight: 400;
        font-size: 14px !important;
        letter-spacing: 1px;
        line-height: 25px;
        color: $grey-2;
      }

      .stay_selection .departure_date_wrapper,
      .guest_selector {
        &::after {
          content: '';
          display: block;
          @include center_y;
          height: 50px;
          width: 2px;
          background: rgba(black, 0.1);
        }
      }

      .stay_selection {
        width: 320px;
        height: 100%;

        .entry_date_wrapper,
        .departure_date_wrapper{
          border-bottom: none;
          height: 100%;
        }

        .entry_date_wrapper {
          padding: 15px 15px 15px 0;

          &::after {
            content: '\f105';
            font-family: "Font Awesome 5 Pro";
            font-weight: 300;
            font-size: 35px;
            @include center_y;
            right: 7px;
            color: rgba(black, 0.1);
            -webkit-font-smoothing: antialiased;
          }
        }

        .departure_date_wrapper {
          padding: 15px 0 15px 55%;

          &::after {
            right: 11px;
          }

          .date_box {
            background: none;
          }
        }
      }

      .guest_selector {
        width: 235px;
        height: 100%;
        padding: 15px 15px;
        margin: 0;

        .placeholder_text {
          font-size: 14px!important;
        }

        &::after {
          right: 0px;
        }

        .placeholder_text {
          text-transform: lowercase;
        }
      }

      .room_list_wrapper {
        top: 100%;
        width: 370px;

        &:lang(en) {
          width: 400px;

          .adults_selector {
            width: 40% !important;
          }

          .children_selector {
            width: 60% !important;
          }
        }
      }

      .wrapper_booking_button {
        width: 585px;
        height: 100%;

        .promocode_wrapper {
          width: 33% !important;
          padding: calc((90px - 20px) / 2) 10px; // 90px - full widget height, 20px - promocode label line height

          .promocode_input {
            outline-color: $corporate_1;
          }
        }
        .modify-calendar-button {
          width: 128px;
          float: left;
          margin: calc((90px - 64px) / 2) 0;
          margin-right: 10px;
          position: relative;
          display: inline-flex;
          padding: 0 10px 0 55px;
          align-items: center;
          border-radius: 10px;
          font-family: $title_family;
          font-weight: 500;
          font-size: 11px;
          letter-spacing: 1px;
          line-height: 22px;
          text-align: center;
          height: 64px;
          text-transform: uppercase;
          background: transparent;
          border: 1px solid $black;
          color: $black;
          @include transition(all, .5s);
          &::before {
            content: '\f073';
            font-family: "Font Awesome 5 Pro";
            color: $black;
            font-weight: 300;
            font-size: 25px;
            @include center_y;
            left: 15px;
            -webkit-font-smoothing: antialiased;
          }
          &:hover {
            background: $corporate_2;
          }
        }
        button.submit_button {
          position: relative;
          width: 180px;
          height: 64px;
          margin: calc((90px - 64px) / 2) 0; // 90px - full widget height, 64px - button height
          padding: 0 10px 0 60px;
          border-radius: 10px;
          font-family: $title_family;
          font-weight: 500;
          font-size: 14px;
          text-align: center;
          letter-spacing: 1.15px;
          line-height: 22px;

          &::before {
            content: '\f0e2';
            font-family: "Font Awesome 5 Pro";
            font-weight: 300;
            font-size: 25px;
            @include center_y;
            left: 30px;
            -webkit-font-smoothing: antialiased;
          }
        }

        .spinner_wrapper {
          width: 180px;
          height: 64px;
          background-color: rgba($corporate_1, 0.5);
          border-radius: 10px;
          top: calc((90px - 64px) / 2);
          right: 6px;
        }
      }
    }
  }
}

div#full_wrapper_booking:not(.booking_widget_step_0) {
  .boking_widget_inline {
    .booking_form {
      .stay_selection {
        width: 460px;
      }

      .guest_selector {
        width: 320px;
      }

      .wrapper_booking_button {
        width: 360px;

        .modify-calendar-button {
          display: none;
        }

        .promocode_wrapper {
          width: calc(100% - 181px) !important;
        }
      }
    }
  }
}

.datepicker_wrapper_element .ui-datepicker .ui-widget-header{
  .ui-datepicker-next, .ui-datepicker-prev{
    &.ui-state-hover, &.ui-datepicker-next-hover, &.ui-datepicker-prev-hover{
      span:before{
        color: white;
    }
    }
  }
}

div#full_wrapper_booking.booking_engine_wrapper_process{
  &.booking_widget_step_1, &.booking_widget_step_2{
    .boking_widget_inline .booking_form .wrapper_booking_button .spinner_wrapper{
      right: 1px;
    }
  }
}

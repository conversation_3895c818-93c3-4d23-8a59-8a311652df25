@import "booking_mobile/booking.scss";
@import "booking_mobile/v1/mixin_colores";

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Plus+Jakarta+Sans:wght@300;400;500;600;700&display=swap');

body.booking_process_mobile_v1:not(.old_design) {
  $corporate_1: #8B0027;
  $corporate_2: #8B0027;
  $corporate_3: #373736;

  $black: #373736;
  $lightgrey: #F5F5F5;
  $red: #C47676;

  $font_1: 'Plus Jakarta Sans', sans-serif;
  $font_2: 'Inter', sans-serif;
  $fa6: "Font Awesome 6 Pro";

  $title_family: $font_1;
  $text_family: $font_2;


  font-family: $font_2;
  color: $black;

  @import "new_design/mobile/luna_new_booking_styles";

  .login_information_wrapper {
    #popup_login_information {
      .tabs_wrapper {
        .register_tab, .login_tab {
          text-transform: uppercase;
        }
      }
    }
  }

  #login_wrapper_element.version_banner_v1.v4 {
    .content_login_wrapper {
      background: transparent !important;

      .logo_wrapper, .club_icons_wrapper {
        width: 45%;

        img {
          max-height: 40px;
        }
      }
    }

    .users_buttons_wrapper {
      padding: 10px 10px 10px 10px;

      .already_member_wrapper {
        background: transparent;
      }

      .join_button_wrapper {
        color: white;
      }
    }
  }

  #register_form_wrapper_v1.v4, #login_form_wrapper_v1.v4 {
    width: calc(100% - 8px);
    margin: 0 auto;

    .title_wrapper_block {
      font-size: 14px;
    }

    .lopd_wrapper, .promotions_checkbox {
      font-size: 13px;
    }

    .login_button_element {
      text-transform: none;
    }
  }

  .modal_wrapper.login_information_wrapper {
    z-index: 49;
  }

  .main_content_wrapper.step_0 {
    .room_list {
      .room_pack_option {
        .labels_wrapper {
          .label {
            background: #EFDBE0 !important;
          }
        }
      }
    }
  }

  .main_content_wrapper.step_2 {
    .personal_details_form_wrapper {
      .personal_details_form {
        .inputs_wrapper {
          input {
            border: 1px solid rgb(131, 131, 133);
          }
        }
      }
    }
  }

  .double_button_wrapper {
    .show_calendar, .back_button {
      text-align: center;
    }
  }

  .booking_widget_wrapper {
    .flexible_dates_wrapper.active {
      top: 120px;
    }
  }

  .price_calendar_wrapper {
    .full_container {
      .bottom_wrapper {
        .bottom {
          .right_wrapper {
            .btn_booking.active {
              svg {
                left: 0;
              }
            }
          }
        }
      }
    }
  }
}


body.booking_process_mobile_v1.old_design {
  $corporate_1: #991a2e;
  $corporate_2: #383839;
  $corporate-3: #991a2e;
  $black: #363a40;
  $font_1: "Montserrat", sans-serif;

  font-family: $font_1;

  .booking_process .booking3 #divPrecioTotal {
    margin: 0 !important;
  }

  .booking_process .booking3 #booking-button {
    margin: 0 !important;
  }

  .booking_process .continue_button {
    padding: 1px 6px !important;
  }

  .booking_process .booking3 form#personalDetailsForm > div select {
    appearance: none;
    -moz-appearance: none;
    -ms-progress-appearance: none;
    -webkit-appearance: none;
  }

  input, button, select {
    font-family: $font_1;
  }

  @include set_color_process($corporate_1, $corporate_2, $corporate_3, $black, white);

  .custom_pictures_range {
    margin: 10px auto;
    max-width: 100%;
  }

  #main_modal.active {
    height: 100vh;
  }

  #main_modal.active {
    .body_modal {
      &.regime_conditions_modal_wrapper .body_modal_content,
      &.iframe_modal_wrapper .body_modal_content,
      &marketing_modal_wrapper .body_modal_content,
      &extra_room_info_modal_wrapper .body_modal_content {
        height: 100%;
        max-height: 100%;
      }

      max-height: 95vh !important;

      &.rooms_features_modal_wrapper .body_modal_content .modal_container {
        height: calc(100% - 390px);
      }
    }
  }

  header {
    background: white;

    #sideMenu {
      background: white;

      .logo-wrapper {
        img {
          max-width: calc(100% - 40px);
          height: auto;
          max-height: 60px;
        }
      }

      .menu_collapse {
        .menu_collapse_item {
          background: lighten($black, 10%);

          .menu_collapse_element {
            color: white;
          }

          .item_selected {
            color: white;
            background: lighten($black, 10%);
          }
        }
      }

      .side_menu_content {
        .ticks_wrapper {
          .ticks_content {
            background: lighten($black, 10%);
            color: white;

            .ticks_list .ticks_list_item .font-icon-wrapper::before {
              color: white;
            }
          }
        }
      }
    }

    a.icon_toggle_wrapper.icon_toggle_active:before {
      color: #4d535c;
    }
  }

  footer {
    position: relative;
    background: none;

    .legal_info_wrapper a {
      color: $black;
    }

    .row:first-of-type {
      display: block;
      text-align: center;
    }

    .logo_wrapper {
      display: block;
      margin: 0 auto 15px;

      img {
        max-width: 100%;
      }
    }

    .info {
      display: block;
      width: 100%;
    }
  }

  .booking_widget_wrapper {
    #entry_date_popup,
    #departure_date_popup {
      .header_wrapper {
        .banner_title {
          i {
            color: $corporate_1;
          }
        }
      }
    }

    .occupancy_popup {
      height: auto;
      top: 0;
      bottom: 0;

      .occupancy_head {
        &.content_title {
          &::before {
            color: $corporate_1;
          }
        }
      }
    }
  }

  div.main_content_wrapper.step_0 {
    .room_list {
      position: relative;
      background: white;

      .room_pack_option {
        .room_name .info_icon {
          color: $corporate_2;
        }

        .room_from_price_wrapper {
          .room_from_price {
            .old_price {
              .old_price_value {
                color: $corporate_1;
              }
            }

            .room_from_price_value {
              .currencyValue,
              .monedaConv {
                color: $corporate_1;
              }
            }
          }

          .prices_dropdown_toggle {
            background-color: $corporate_1;

            &.active {
              background-color: $corporate_2;
            }
          }
        }
      }
    }
  }


  .loading_animation_popup {
    background: rgba(white, .9);
    opacity: 1 !important;

    .center_xy {
      .spincircle {
        border-color: transparent;

        &:after {
          border-bottom-color: white;
          animation-delay: .1s;
          -moz-animation-delay: .1s;
        }
      }

      span {
        color: $black;
      }
    }
  }

  .modify_search,
  .back_booking,
  .continue_booking {
    background-color: $corporate_1;
    color: white;
    border: 1px solid white;
  }

  .show_calendar, .back_button {
    background-color: $corporate_2;
    color: white;
    border: 1px solid white;
  }

  .main_content_wrapper.step_0 .room_list .room_pack_option .rates_details_wrapper {
    .rate_selected_title {
      font-size: .8rem;
      padding: 9px;
    }

    .regime_item_content {
      .prices_options {
        .final_price {
          color: $corporate_1;
          letter-spacing: 0;
        }

        .price_through {
          color: $corporate_1;
        }
      }

      .discount_percentage {
        background-color: $corporate_1;
      }

      .regime_description {
        .regime_offer_detail {
          color: $corporate_1;
        }

      }
    }
  }

  .owl-carousel.custom_carousel .owl-item .price_label {
    background-color: $corporate_1;
  }

  #departure_date_popup {
    &.dates_popup.active {
      .ui-datepicker-title {
        font-family: $font_1 !important;
      }

      .ui-datepicker-calendar {
        font-family: $font_1 !important;
      }
    }

    .end_datepicker * {
      font-family: $font_1 !important;
    }
  }


  .card_image_wrapper {
    .card_image {
      max-width: 150px;
    }
  }


  .card_element_popup_wrapper {
    text-align: center;

    .seguro {
      display: block;
      margin: 0 auto 30px;
    }

    b {
      font-weight: bold;
    }

    ul {
      margin-top: 20px;

      li {
        margin-bottom: 15px;
        padding-left: 30px;
        position: relative;
        font-weight: lighter;

        &:last-of-type {
          margin-bottom: 0;
        }

        &:before {
          content: "";
          width: 20px;
          height: 20px;
          display: inline-block;
          margin-right: 10px;
          background: url(https://cdn2.paraty.es/casual-vintage-valencia/images/193cf20a907ee32=s70);
          background-size: cover;
          vertical-align: middle;
          position: absolute;
          left: 0;
        }
      }
    }


    ul {
      text-align: left;
    }
  }

  .custom_message_rate .seguro_wrapper {
    background: #363a40;
    padding: 10px 20px;
    display: table;
    width: 100%;
    box-sizing: border-box;
    margin-top: 15px;
    border-radius: 7px;

    img {
      width: 70px;
    }

    span {
      width: 70%;
      float: right;
      text-align: center;
      color: white;
      font-size: 17px;
      font-weight: 300;
      margin-top: 8px;

      &:lang(nl) {
        font-size: 14px;
      }
    }
  }

  .main_content_wrapper.step_0 .room_list .club_choice_wrapper .club_element {
    &.club {
      label {
        strong {
          display: inline-block;
          padding: 0 5px;
        }

        img {
          display: none;
        }
      }
    }
  }

  .loading_animation_popup .spincircle .spinner_animation {
    top: 0;
    left: 0;
  }

  .cards_banners_wrapper .card_element_wrapper, .cards_extended_wrapper .card_element_wrapper {
    max-height: none;
  }

  #main_modal.active .body_modal.rooms_features_modal_wrapper {
    max-height: 100vh !important;

  }

  #main_modal.active .body_modal.iframe_modal_wrapper .body_modal_content iframe {
    height: 69vh;
  }


  .shopping_cart_summary.v2.mobile .start_wrapper .book_button.disabled {
    opacity: 0.5;
  }
}
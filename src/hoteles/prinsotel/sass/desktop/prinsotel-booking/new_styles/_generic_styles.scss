.cards_banners_wrapper .card_element_wrapper.only_image .card_image_wrapper .card_image {
  width: 394px !important;
  position: fixed !important;
  margin: auto !important;
  top: 65px !important;
  right: -349px !important;
  transform: translateY(50%) !important;
  transition: all 0.3s;

  &:hover {
    right: -10px !important;
    transition: all 0.3s;
  }
}

.cards_banners_wrapper .card_element_wrapper.only_image + .close_card_button {
  display: none;
}

.tooltip-datepicker {
  display: none;
}

.fancybox-overlay {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.5) !important;

  .fancybox-opened .fancybox-skin {
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
  }
}

.fancybox-wrap.popup_see_more_rooms_second.fancybox-opened {
  .fancybox-title-float-wrap .child {
    display: none;
  }
}

.payment_radiobtn {
  -webkit-appearance: none !important;
}

.payment_form_container {
  .payment_option:first-child .cards_images img {
    width: calc((100% / 3) - 14px)
  }

  .payment_option .cards_images img {
    width: 40px;
  }
}

#step-1 {
  .custom_pictures_range {
    display: none;
  }

  .rooms_packages_selectors_wrapper {
    &:before {
      content: '';
      background-image: url("https://storage.googleapis.com/cdn.paraty.es/hotansa-magic-and/files/5af9f58e368de18%3Ds1900.jpg");
      width: 1140px;
      height: 135px;
      background-repeat: no-repeat;
      margin-bottom: 25px;
      background-size: contain;
    }
  }
}

&.pt {
  .all_additional_services_wrapper.with_tabs_scroll {
    .category_wrapper {
      .additional_services_wrapper {
        .additional_service_element {
          .service_content {
            .price_per_night_person {
              .currencyValue {
                float: left;
              }
            }
          }
        }
      }
    }
  }
}

&.fr {
  .price_calendar_wrapper {
    .full_container {
      .bottom_wrapper {
        .top {
          .info_currency_wrapper {
            .right_wrapper {
              width: 200px;
            }
          }
        }
      }
    }
  }

  .datepicker_wrapper_element {
    .datepicker_ext_inf_ed, .datepicker_ext_inf_sd {
      .ui-datepicker-inline {
        .ui-datepicker-group {
          .ui-datepicker-header {
            .ui-datepicker-title {
              font-size: 11.44px;
            }
          }
        }
      }
    }

    .current_nights_selector {
      font-size: 13px;
    }
  }
}

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
  div#step-1 {
    #rooms_b1_wrapper {
      .contTipoHabitacion {
        .contFotoDescripcion {
          .contDescHabitacion {
            width: 730px;
          }
        }
      }
    }
  }
}


&.iPad_user {
  div#full_wrapper_booking:not(.has_babies) {
    .boking_widget_inline {
      .booking_form {
        .stay_selection {
          margin-left: 20px;
        }
      }
    }
  }

  #wizard {
    div#step-1 {
      #rooms_b1_wrapper {
        .contTipoHabitacion {
          .contFotoDescripcion {
            .contDescHabitacion {
              .room_description_name_wrapper {
                .cabeceraNombreHabitacion.booking_message_width {
                  .very_asked_message {
                    position: absolute;
                    right: -30px;
                    top: -43px;
                    min-width: 170px;
                  }
                }

                .descripcionHabitacion {
                  height: 50px !important;
                }
              }

              .room_services {
                display: flex !important;
                align-items: center;
                justify-content: space-around;

                .service_element {
                  .service_description {
                    font-size: 12px !important;
                  }

                }
              }
            }
          }
        }
      }

      table.listadoHabsTarifas {
        tr {
          .lock_board {
            .lock_board_wrapper {
              .club_lock_logo {
                max-height: 22px;
                margin-top: 5px;
              }
            }
          }
        }
      }
    }
  }

  #step-3 {
    #personal-details-form {
      display: flex;
      gap: 20px;
      align-items: flex-start;
      justify-content: center;

      .personal_details_payment_wrapper {
        width: 63%;
      }

      .booking_details_prices_wrapper {
        width: 30%;
      }
    }
  }


  .fancyboxContent_second_v.v2 {
    .room_popup_individual_element {
      .room_services {
        display: flex !important;
        align-items: center;
        justify-content: space-around;

        .service_element {
          .service_description {
            font-size: 11px;
          }

          border-right: none;
        }
      }
    }
  }

  div#invalid-promocode {
    width: calc(100% - 70px);
  }
}

#step-4 *:not(.header_text):not(.main_title_block) {
  font-family: $font-2, sans-serif !important;
}

#step-4 .header_table .header_text,
#step-4 .main_title_block {
  font-family: $font-1, serif !important;
}
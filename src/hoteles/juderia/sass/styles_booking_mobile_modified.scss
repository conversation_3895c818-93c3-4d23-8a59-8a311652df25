.casas-arenal {
  $color1: #3f250c;
  @import "booking_mobile/custom_booking";
}

.gremios-reales {
  $color1: #3f250c;
  @import "booking_mobile/custom_booking";
}

.juderia-cordoba {
  $color1: #04284E;
  @import "booking_mobile/custom_booking";

  #header {
    background: $color1;
    //padding: 0 8px 10px;
    margin: 0 0 10px !important;
  }
  .fancybox-outer {
    .fancybox-content {
      .description_wrapper {
        .title_room_wrapper {
          .title_room {
            height: auto;
          }
        }
      }
    }
  }
}

.juderia-corporativa {
  $color1: #87121a;
  @import "booking_mobile/custom_booking";
}

.juderia-sanfrancisco {
  $color1: rgb(224, 11, 59);
  @import "booking_mobile/custom_booking";
}

.juderia-trinidad {
  $color1: rgba(58, 58, 51, 0.70);
  @import "booking_mobile/custom_booking";
}

.casa-merced {
  $color1: #04284E;
  $background_header: white;
  @import "booking_mobile/custom_booking";
}

.basic-sevilla {
  $color1: #f9b43a;
  @import "booking_mobile/custom_booking";
}

.palacete.booking_process {
    .booking3 form#personalDetailsForm > div select {
        appearance: none;
        -ms-progress-appearance: none;
        -moz-appearance: none;
        -webkit-appearance: none;
    }
    .fancybox-title-float-wrap{
        bottom: -60px !important;
        .child{
            white-space: normal !important;
        }
    }
    span.modify_search:lang(fr){
        font-size: 16px !important;
    }
}

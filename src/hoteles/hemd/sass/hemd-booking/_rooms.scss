div#step-1 {
  .contTipoHabitacion {
    padding: 0;
    border-radius: 10px;
    box-shadow: 0px 3px 10px #00000029;
    margin-bottom: 50px;

    .very_asked_message{
      background: rgba(235,138,35,0.3) !important;
      &:after{
        border-left: 15px solid rgba(235,138,35,0.3);
      }
    }

    .just_booking_message{
      background: #ec6363 !important;

      &:after{
        border-left: 15px solid #ec6363;
      }
    }

    .contFotoDescripcion {
      padding: 0;

      .contFotoHabitacion {
        width: 320px;
        border-radius: 10px 0 0 0;
        min-height: 215px;

        .see_more_rooms_v2 {
          img {
            @include cover_image;
            position: static;
          }

          .lupa {
            right: auto;
            left: 0;
            padding: 15px;

            font-family: "Font Awesome 5 Pro";
            font-size: 30px;
            font-weight: 300;
          }
        }
      }

      .contDescHabitacion {
        margin: 0;
        width: 820px;
        padding-top: 10px;
        box-sizing: border-box;
        height: 215px;

        .room_description_name_wrapper {
          max-height: 115px;
        }

        .tipoHabitacion {
          font-family: $title_family;
          font-weight: 500;
          font-size: 21px;
          letter-spacing: 1.15px;
          line-height: 1;
          text-transform: capitalize;
          color: $black;
        }

        .descripcionHabitacion {
          font-family: $text_family;
          font-weight: 400;
          font-size: 13px;
          letter-spacing: 0.75px;
          line-height: 21px;
          color: $black;
          height: 75px;
        }

        .see_more_rooms_v2 {
          .plus_sign {
            color: $grey-1;
          }

          .see_more {
            font-family: $text_family;
            font-weight: 500;
            font-size: 13px;
            letter-spacing: 0.75px;
            line-height: 1;
            color: $black;
            text-transform: uppercase;
            text-decoration: none;
          }
        }

        .room_services {
          margin: 0 auto;
          border-top: 1px solid rgba(black, 0.1);
          border-bottom: 1px solid rgba(black, 0.1);

          .service_element {
            border-right: none;
          }

          .service_element + .service_element {
            border-left: 1px solid rgba(black, 0.1);
          }
        }
      }
    }

    .preciosHabitacion {
      .listadoHabsTarifas {
        padding-bottom: 10px;

        .contTitTipoTarifa {
          .titTipoTarifa {
            font-family: $text_family;
            font-weight: 400;
            font-size: 13px;
            letter-spacing: 0.75px;
            color: $grey-1;
            text-transform: capitalize;
          }

          .cheapest_rate_message,
          .advice_rate_message {
            color: $grey-2;
            background-color: $corporate_3 !important;

            .best_price_label_info {
              font-family: $text_family;
              font-weight: 600;
              font-size: 12px;
              letter-spacing: 0.75px;
              line-height: 1;
              color: $grey-2;
            }

            .conditions_info_wrapper {
              a {
                color: $grey-2;
              }
            }
          }
        }

        .regimenColumn {
          width: 35%;
          font-family: $text_family;
          font-weight: 400;
          font-size: 13px;
          letter-spacing: 0.75px;

          .tTextoOferta {
            font-weight: 600;
            font-size: 13px;
            margin-left: 3px;
            color: $red;
          }
        }

        .precioNocheColumn {
          width: 10%;

          .priceValues {
            display: none;
          }
        }

        .precioTotalColumn {
          width: 35%;
          font-family: $text_family;

          .precioTachadoDiv {
            text-align: right;

            .tPrecioTachado {
              font-weight: 400;
              font-size: 11px;
              letter-spacing: 0.65px;
              color: $red;
            }
          }

          .precioGeneralDiv {
            .tPrecioTotal,
            .tPrecioOferta {
              font-weight: 700;
              font-size: 16px;
              color: $grey-2 !important;
              letter-spacing: 0.35px;
            }
          }

          .promotion_percentage_square {
            border-radius: 50%;

            .promotion_discount {
              border-radius: 10px;
              background-color: $corporate_2;
            }
          }

          .priceTitle {
            font-weight: 400;
            font-size: 9px;
            color: $grey-2;
            letter-spacing: 0.2px;
            line-height: 8px;
          }
        }

        .botonReservarColumn {
          button {
            width: 185px;
            height: 50px;
            border-radius: 10px;
            outline: none;

            font-family: $title_family;
            font-weight: 700;
            font-size: 18px;
            letter-spacing: 1.35px;
          }

          .booking-button {
            background: $corporate_1;
            border-radius: 15px;
            padding: 10px 5px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            @include transition(all, .5s);

            &:hover {
              background: $corporate_2;
            }

            &.selected {
              background: $green !important;
            }

            &.remove {
              background: $red !important;
            }
          }
        }

        &.agencies_view_actived {
          .logged_agency_img_info {
            position: absolute;
            top: 50%;
            @include transform(translateY(-50%));
            right: 100%;
            background: none;
            width: 143px;
            height: 37px;
            padding: 5px;
            margin: 0;
            box-sizing: border-box;

            &::before {
              display: none;
            }

            &::after {
              display: block;
              content: '';
              background-image: url(https://cdn2.paraty.es/hotansa-encamp/images/85f98922ee3f93e);
              background-repeat: no-repeat;
              background-position: center center;
              background-size: contain;
              width: 100%;
              height: 100%;
            }

           /* &:lang(en)::after {
              background-image: url(https://cdn2.paraty.es/hotel-dynastic/images/0ac25e141e10efe);
            }

            &:lang(pt)::after {
              background-image: url(https://cdn2.paraty.es/hotel-dynastic/images/152147efec28644);
            }*/
          }

          .logged_agency_wrapper_info {
            width: auto;
            padding: 20px;
            background-color: rgba(#444444, 0.9);
            border-radius: 5px;
            position: absolute;
            top: auto;
            left: auto;
            right: calc(100% + 70px);
            bottom: 100%;
            @include transform(translateX(50%));

            &::before {
              content: "";
              position: absolute;
              top: 100%;
              left: 50%;
              border: 10px solid transparent;
              border-top-color: rgba(#444444, 0.9);
              @include transform(translateX(-50%));

            }

            .field {
              font-family: "Niramit",sans-serif;
              font-weight: 400;
              font-size: 15px;
              letter-spacing: 0.8px;
              line-height: 20px;
              color: white;
              white-space: nowrap;

              .value_elem,
              .monedaConv {
                font-weight: 700;
                color: white;
                margin-left: 5px;
              }
            }

            .logged_agency_close_button {
              width: 15px;
            }
          }
        }
      }
    }
  }

  .room_step_block {
    .room_step_label {
      border-width: 2px;
      border-color: $corporate_1;
      font-family: $text_family;
      color: $corporate_1;

      &::after {
        color: $corporate_1;
      }
    }
  }

  .total_price_wrapper {
    .total_price {
      background-color: $corporate_1;
    }

    .rooms_submit_button {
      background-color: $corporate_2;
      border-color: white;
      font-weight: 700;
      font-size: 15px;
      letter-spacing: 0.3px;

      &:hover {
        background-color: $corporate_1;
      }

      &.disabled {
        background-color: #eaeaea;
      }
    }
  }
}

.fancybox-overlay {
  .room_popup_individual_element {
    .popup_title {
      font-family: $title_family;
      font-weight: 500;
      font-size: 21px;
      letter-spacing: 1.15px;
      line-height: 1;
      text-transform: capitalize;
      color: white;
    }

    .popup_carousel {
      .exceded {
        padding: 0;

        .popup_image {
          @include cover_image;
          position: static;
        }
      }
    }

    .popup_room_description {
      font-family: $text_family;
      font-weight: 400;
      font-size: 13px;
      letter-spacing: 0.75px;
      line-height: 21px;
      color: $black;
    }
  }
}

.room_popup_individual_element .room_services,
div#step-1 .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_services {
  display: grid !important;
  padding: 0 !important;
  grid-template-columns: repeat(4, auto);

  .service_element {
    width: 100%;
    height: auto;
    @include display_flex(nowrap);
    justify-content: center;
    align-items: center;
    padding: 10px;
    text-align: center;

    i {
      font-size: 25px;
      margin-right: 10px;
      color: $corporate_1;
    }

    .service_description {
      font-family: $text_family;
      font-weight: 400;
      font-size: 12px;
      letter-spacing: 0.7px;
      line-height: 1;
      color: $black;
    }
  }
}

.rate_conditions_popup_wrapper h3.rate_name, .package_hidden_popup .popup_title {
  font-family: $text_family;
}

#packages_b1_wrapper.v2 {
  .ui-datepicker {
    .ui-widget-header .ui-datepicker-title, table {
      font-family: $text_family;
    }

    .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
      font-family: $text_family;
    }
  }
}
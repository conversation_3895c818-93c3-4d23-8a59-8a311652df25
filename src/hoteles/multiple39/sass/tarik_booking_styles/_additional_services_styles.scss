#step-2 {
  .booking-2-services-list {
    .add_service_element {
      font-family: $font_1, sans-serif;
      background: $corporate_2;
      color: white;
      padding: 10px 20px;
      font-size: 16px;
      font-weight: 300;
      position: relative;
      -webkit-transition: all 0.6s;
      -moz-transition: all 0.6s;
      -ms-transition: all 0.6s;
      -o-transition: all 0.6s;
      transition: all 0.6s;
      display: inline-block;
      vertical-align: middle;
      text-transform: uppercase;
      letter-spacing: 1px;
      z-index: 1;

      &:after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        left: 0;
        background: $corporate_1 !important;
        opacity: 0;
        -webkit-transition: opacity 0.6s;
        -moz-transition: opacity 0.6s;
        -ms-transition: opacity 0.6s;
        -o-transition: opacity 0.6s;
        transition: opacity 0.6s;
        z-index: -1;
      }

      &:hover {
        color: white;
        opacity: 1;
        &:after {
          opacity: 0.8;
        }
      }
    }
  }
  .booking_button_wrapper .booking-button{
    -webkit-transition: opacity 0.6s;
    -moz-transition: opacity 0.6s;
    -ms-transition: opacity 0.6s;
    -o-transition: opacity 0.6s;
    transition: opacity 0.6s;
    &:hover{
      background: $grey;
    }
  }
}


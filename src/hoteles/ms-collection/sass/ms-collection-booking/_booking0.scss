#step-0 {
  .no_availability_message_booking0 {
    background: $corporate_2;
    border-radius: 10px;
  }
  .search_other_dates_button {
    background: white;
    color: $grey-2;
    border: 2px solid $grey-2;
    border-radius: 15px;
    &:hover {
      background: $corporate_2;
      color: white;
    }
  }
  .booking_0_buttons_controller .see_list_button,
  .booking_0_buttons_controller .see_map_button {
    border-radius: 10px 10px 0 0;
    border-top: 4px solid #CACACA;
    border-left-color: transparent;
    border-right-color: transparent;
    background: white;
    color: #333;
    font-weight: 300;
    i.fa {
      font-size: 150%;
      vertical-align: middle;
    }
    &.active {
    border-top: 4px solid $corporate_2;
      background: #f8f8f8;
    }
  }
  .hotels_list_wrapper {
    .center_block {
      .u-row {
        box-shadow: 0 0 20px rgba(0,0,0,.3);
        color: white;
        .booking-box--default {
          font-family: $font_1, sans-serif;
          .booking-box__title {
            background: #444444;
            padding: 10px 20px;
            color: white;
            h3 {
              font-weight: 700;
              font-size: 22px;
              line-height: 25px;
              color: white;
              letter-spacing: 0.5px;
            }
          }
          .booking-box--action {
            .booking-box__title {
              font-weight: 300;
              background: transparent;
              font-family: $font_1;
              color: #444444;
              padding: 0;
            }
            .hotel-promotion {
              font-weight: 300;
              .hotel-price__promotion {
                color: $red;
                font-weight: 700;
                font-family: $font_1;
              }
            }
            .hotel-price__current {
              color: $grey-2;
            }
            .price_information, .included_tax {
              color: $grey-2;
              font-family: $font_1;
            }
          }
        }
        .booking-box__content {
          .hotel_description_container {
            color: #444444;
            font-family: $font_1;
            letter-spacing: 0.5px;
            font-size: 12px;
            strong {
              font-weight: 700;
            }
          }
          .bottom_buttons_links .buttons_booking0 a > div {
            background: $corporate_2;
          }
          .flexible_days {
            color: $corporate_1;
            font-size: 14px;
          }
        }
        .booking-button--action {
          font-family: $font_1, sans-serif;
          background-color: $corporate_1 !important;
          color: white;
          padding: 10px;
          border-radius: 10px;
          letter-spacing: 0.5px;
          font-size: 16px;
          position: relative;
          -webkit-transition: all 0.6s;
          -moz-transition: all 0.6s;
          -ms-transition: all 0.6s;
          -o-transition: all 0.6s;
          transition: all 0.6s;
          display: inline-block;
          vertical-align: middle;
          font-weight: 300;
          text-transform: uppercase;
          z-index: 1;
          &:after {
            content: '';
            position: absolute;
            border-radius: 10px;
            top: 0;
            right: 0;
            bottom: 0;
            left: auto;
            background: $corporate_2;
            width: 0;
            -webkit-transition: width 0.6s;
            -moz-transition: width 0.6s;
            -ms-transition: width 0.6s;
            -o-transition: width 0.6s;
            transition: width 0.6s;
            z-index: -1;
          }
          &:hover {
            color: white;
            &:after {
              left: 0;
              width: 100%;
            }
          }
        }
        .no_availability_hotel_button {
          background-color: $corporate_2;
        }
      }
    }
  }
}
div#full_wrapper_booking.booking_widget_step_-1 {
 .boking_widget_inline .booking_form .wrapper_booking_button {
   button.submit_button {
     float: right !important;
   }
   .spinner_wrapper {
     width: 175px;
   }
   .promocode_wrapper {
     width: 45% !important;
   }
 }
}

.fancybox-overlay {
  background-color: rgba(0, 0, 0, 0.7);
}
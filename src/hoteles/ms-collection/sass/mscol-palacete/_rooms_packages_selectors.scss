#wizard {
  min-width: 1140px;

  div#step-1 {
    .rooms_packages_selectors_wrapper.has_packages {
      @include display_flex;
      justify-content: space-between;
      border: none;
      margin-bottom: 30px;
      overflow: hidden;

      .button_package_room {
        display: block;
        width: calc(50% - 5px); // 10px between tabs
        height: 73px;
        padding: 20px 50px;
        margin: 0;
        background-color: $grey-3;
        border: 2px solid $grey-3;
        border-bottom-color: $corporate_1;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        box-sizing: border-box;
        opacity: 1;
        overflow: visible;

        font-family: $title_family;
        font-weight: 300;
        font-size: 21px;
        letter-spacing: 1.15px;
        line-height: 25px;
        text-align: center;
        text-transform: capitalize;
        color: $grey-2;
        @include transition(all, 0.3s);

        &.with_number {
          display: flex;
          flex-flow: column;
          justify-content: center;
          .packages_number {
            font-size: 15px;
            letter-spacing: 0.8px;
            &:before {
              content: "";
              width: 11px;
              height: 11px;
              background: $corporate_1;
              border-radius: 50%;
              opacity: 0.75;
              display: inline-block;
            }
          }
        }
        &.active {
          background-color: white;
          border-color: $corporate_1;
          border-bottom-color: white;
          &.with_number .packages_number:before {
            display: none;
          }
        }
      }

      .button_package_room + .button_package_room {
        &::before {
          content: "";
          display: block;
          position: absolute;
          top: 100%;
          right: 100%;
          width: 14px;
          height: 2px;
        }
      }
    }
  }
}
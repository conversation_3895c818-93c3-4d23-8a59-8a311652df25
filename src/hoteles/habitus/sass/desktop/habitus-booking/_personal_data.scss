div#step-3 {
  background: none;

  #personal-details-form {
    .booking_details_prices_wrapper {
      .booking-button {
        font-family: $text_family;
        border-radius: 15px;
        background: $corporate_1;
        overflow: hidden;
        font-weight: bold;
        font-size: 16px;
        letter-spacing: 1.5px;

        &::before {
          border-radius: 15px;
          opacity: 1;
          background: $corporate_2;
        }
      }
    }

    .personal_details_payment_wrapper {
      box-shadow: 0px 2px 7px #00000022;

      .personal_details_grid {

        .booking-box--form {

          .booking-form-field {
            label {
              background-color: white;
              line-height: 11px;
              top: -8px;
            }
            .booking-form-control {
              &:-webkit-autofill,
              &:-webkit-autofill:hover,
              &:-webkit-autofill:focus {
                -webkit-box-shadow: 0 0 0px 1000px white inset;
                -webkit-text-fill-color: #838385;
              }
            }
          }
        }

        .lock_rates_wrapper {
          border-radius: 8px;
          background: transparent;
          border: 1px solid $black;

          .lock_content {
            font-size: 14px;
            line-height: 1.2;
            color: $black;

            .banner_lock_club_booking3 {
              img {
                display: none;
              }
            }
          }

          .lock_price {
            .price_bullet {
              background: $corporate_1 !important;
            }
          }
        }

        .booking-form-control {
          border: 1px solid #c5c5c5;
          border-radius: 5px;

          &#country_code {
            background: url(https://storage.googleapis.com/cdn.paraty.es/parkroyal-corpo/files/arrow_down.svg) no-repeat 95% center
          }
        }
      }

      .payment_type_grid.tpv_payment {
        .booking-3-agree {
          font-size: 12px;

          .club_register_wrapper {
            display: none;
          }
          input[type='checkbox']:checked:before{
            background: $corporate_2;
          }
        }

        #destination_cobrador_form {
          .payment_option {
            .installments_quantity{
              padding-top: 10px;
            }
            &.selected-option {
              background-color: #FDF8FC !important;
              border-color: #9A1F6E !important;

              .msi_payment_select_wrapper .installments_quantity_label {
                background: #FDF8FC !important;
              }
            }

            .payment_option_description {
              padding-top: 5px;
              margin-top: 5px;
            }
          }
        }
      }
    }

    .booking_details_prices_wrapper {
      box-shadow: 0px 2px 7px #00000022;

      .booking_info_element {
        font-size: 14px;
        line-height: 22px;

        .conditionsMoreInfo {
          color: #333;
          margin: 5px 0 0;
          border-bottom-color: #333;
        }

        &.cancellation_date {
          font-size: 12px;
        }
      }

      .booking-3-info--title.accomodation_tax_wrapper, .accomodation_tax_info_popup_wrapper {
        vertical-align: bottom;
      }

      .booking-3-info--title.accomodation_tax_wrapper, .amount_supp_label {
        background-color: #788995;
        margin-left: -15px;
        margin-right: -15px;
        font-weight: 400;
        color: white;
      }

      .supplements_detailed .supplement_details_wrapper {
        margin-bottom: 10px;
        font-size: 15px;

        div .amount_supp_label {
          background-color: transparent;
          display: inline-block;
          color: inherit;
          padding: 0;
          font-size: 15px;
          margin: 0;
        }
      }

      .booking-3-info--title.accomodation_tax_wrapper {
        padding: 15px 15px 0;

        .accomodation_icon {
          font-weight: 400;
          color: white;
        }

        .booking-3-price--partial {
          font-size: 12px;
          float: none;
          margin-left: 8px;
        }
      }

      .agency-booking3-wrapper {
        .booking-3-info--title {
          padding: 8px 15px;
        }
      }

      .amount_supp_label {
        font-size: 12px;
        padding: 5px 15px;
        display: block;
        margin-bottom: -2px;
      }

      .booking_details_wrapper {
        &.with_extra_rate_message {
          margin-bottom: 20px;
        }

        .total_booking_wrapper {
          background: #788995;
        }

        .total_booking_wrapper.tax_broken_down.with_extra_rate_message {
          background: none;
          font-weight: 100;
          padding: 0 15px 15px;
          bottom: auto;

          .tax_inc_wrapper_info {
            .sub_total_field, .tax_field, .certificate_tax_field, .certificate_activation_field {
              font-style: normal;
              line-height: 20px;
              background: #788995;
              margin-left: -15px;
              margin-right: -15px;
              padding: 0 15px;
              font-weight: 400;
            }

            .tax_field {
              padding-bottom: 15px;
            }

            .certificate_tax_field {
              margin-top: -15px; // To cancel tax_field padding-bottom
              padding-bottom: 15px;
            }

            .certificate_activation_field {
              margin-top: -15px; // To cancel certificate_tax_field padding-bottom
              padding-bottom: 15px;
            }

            .monedaConv {
              margin-left: 3px;
            }
          }

          .exchange_message {
            margin-top: 10px;
            color: #383838;
            line-height: 1.5;
          }

          .total_field {
            font-weight: 900;
            color: #383838;
            font-size: 24px;

            .label_nam {
              color: #383838;
              text-transform: none;
            }
          }
        }

        .shopping_cart_datails_taxes_v2_wrapper {
          .accomodation_tax_info_wrapper {
            .accomodation_tax_wrapper {
              padding: 12px 15px 0;
            }

            .amount_supp_label {
              padding-bottom: 12px;
            }
          }

          .total_booking_wrapper.with_extra_rate_message {
            &.tax_broken_down {
              width: auto;
              padding: 0 !important;
              display: block !important;
              left: initial;
              right: initial;

              .tax_inc_wrapper_info .tax_field {
                padding-bottom: 7px;
              }
            }

            &:not(.tax_broken_down) {
              padding: 0 !important;
            }
          }
        }
      }

      .additional_service_element {
        background: transparent;
        border: 1px solid #707070;
        border-radius: 10px;

        .add_service_button {
          i, span {
            color: $corporate-2;
          }
        }
      }


    }
  }

  .booking-form-field {
    display: table;
    width: 100%;
  }

  .booking-3-gateway {
    margin-top: 25px;

    .payment_option_element {
      margin-bottom: 5px;

      .icon_pasarela_pago {
        display: none;
      }
    }
  }

  .bank_transfer_options_wrapper {
    padding: 5px;
    max-width: 400px;
    margin: 0 auto 20px;

    .bank_transfer_option {
      border-radius: 0 0 5px 5px;
      padding: 10px 12px;
      position: relative;
      border: 1px solid gray;
      transition: background .3s;
      display: flex;
      align-items: center;

      &:first-child {
        border-radius: 5px 5px 0 0;
      }

      &:not(:last-child) {
        top: 1px;
      }

      &:last-child {
        box-shadow: 2px 2px 20px 1px rgba(0, 0, 0, 0.2);
      }

      input {
        appearance: none;
        -webkit-appearance: none;
        position: relative;

        &:before {
          content: '';
          width: 10px;
          height: 10px;
          border: 1px solid #333333;
          position: absolute;
          top: -8px;
          left: -8px;
          border-radius: 50%;
        }

        &:checked {
          &:after {
            content: '';
            position: absolute;
            width: 8px;
            height: 8px;
            background: $corporate_2;
            border-radius: 50%;
            top: -6px;
            left: -6px;
          }
        }
      }

      label {
        font-size: 14px;
        margin-left: 5px;
      }

      &.selected {
        background-color: #FDF8FC !important;
        border-color: #9A1F6E !important;
        z-index: 1;
      }
    }

    .cards_images {
      display: inline-block;
      width: 90px;
      height: auto;
      position: absolute;
      right: 5px;
      top: 50%;
      text-align: right;
      transform: translateY(-50%);

      img {
        width: calc((100% / 3) - 14px);
        height: auto;
        object-fit: cover;
        margin-right: 5px;
      }
    }
  }
}

body.logged_agency {
  .total_booking_wrapper {
    display: none;
  }

  .agency-booking3-wrapper {
    display: block !important;
  }

  .booking_details_wrapper {
    margin-bottom: 20px !important;
    padding-bottom: 20px !important;
  }
}
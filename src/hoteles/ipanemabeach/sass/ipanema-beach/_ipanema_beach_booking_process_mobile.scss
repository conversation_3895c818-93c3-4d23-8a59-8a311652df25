.paraty-booking-form{
  .double_button_wrapper {
  .modify_search, .re-search_button {
    border-color: $corporate_1;
    color: $corporate_1;
    background: white;
  }

  .show_calendar, .back_button, .close_button {
    background: $corporate_1;
    color: white;
    border-color: $corporate_1;
  }
}
}


.just_booking {
  background: $corporate_1;
}

.very_asked {
  background: $corporate_2;
}

.main_content_wrapper.step_0 {
  .room_list {
    .room_pack_option {
      .rates_details_wrapper {
        .regime_item_content {
          .regime_price_wrapper {
            div.submit {
              span {
                background: $corporate_3;
              }
            }
          }
          .prices_options{
            .final_price{
              color: $corporate_2;
            }
          }
        }
        .rate_selected_title{
          span{
            color: $corporate_2;
          }
        }
      }
    }
  }
}

.main_content_wrapper.step_1 {
  .all_additional_services_wrapper {
    .category_wrapper {
      .additional_services_wrapper {
        .additional_service_element.active {
          .preview_wrapper {
            .preview_content {
              display: none;
            }
          }
        }
      }
    }
  }

  .continue_booking {
    background: $corporate_3;
  }
}

.main_content_wrapper.step_2 .personal_details_form_wrapper .personal_details_form .bottom_button_wrapper #btn-finish-booking{
  background: $corporate_2;
}

#main_modal.active .body_modal.regime_conditions_modal_wrapper .body_modal_content iframe,
#main_modal.active .body_modal.iframe_modal_wrapper .body_modal_content iframe,
#main_modal.active .body_modal.marketing_modal_wrapper .body_modal_content iframe,
#main_modal.active .body_modal.extra_room_info_modal_wrapper .body_modal_content iframe,
#main_modal.active .body_modal.closed_hotel_popup_modal_wrapper .body_modal_content iframe {
  height: calc(80vh - 70px);
}

#main_modal.active .body_modal.regime_conditions_modal_wrapper .body_modal_content,
#main_modal.active .body_modal.iframe_modal_wrapper .body_modal_content,
#main_modal.active .body_modal.marketing_modal_wrapper .body_modal_content,
#main_modal.active .body_modal.extra_room_info_modal_wrapper .body_modal_content,
#main_modal.active .body_modal.closed_hotel_popup_modal_wrapper .body_modal_content {
  max-height: calc(100vh - 170px);
}

.booking_widget_wrapper .input_wrapper:before {
    background: $corporate_1;
}

.booking_widget_wrapper #departure_date_popup .header_wrapper .banner_title i,
.booking_widget_wrapper #entry_date_popup .header_wrapper .banner_title i,
.booking_widget_wrapper .ui-datepicker .ui-datepicker-title,
.booking_widget_wrapper .occupancy_popup .occupancy_head.content_title::before{
    color: $corporate_1;
}

.booking_widget_wrapper .highlight_day,
.booking_widget_wrapper .start_date_selection:before,
.booking_widget_wrapper .ui-datepicker-current-day:before {
    background: $corporate_2 !important;
    opacity: .5;
}

.booking_widget_wrapper .occupancy_popup .confirm_ocupation button,
.booking_widget_wrapper #entry_date_popup .start_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before,
.booking_widget_wrapper #departure_date_popup .end_datepicker .ui-datepicker-inline .ui-datepicker-current-day .ui-state-active:before,
.booking_widget_wrapper #departure_date_popup .end_datepicker .ui-datepicker-inline .start_date_selection span:before,
.booking_widget_wrapper .occupancy_popup .occupancy_popup_body .counter_box .control.add {
    background-color: $corporate_2;
}

.booking_widget_wrapper {
  .occupancy_popup {
    .occupancy_popup_body {
      .counter_box {
        .control.subtract {
            border: 1px solid $corporate_2;
            &:before {
                color: $corporate_2;
            }
        }
      }
    }
  }
}


.main_content_wrapper.step_2 .reservation_summary .option_selected .price:not(.with_custom_taxes_details),
.main_content_wrapper.step_2 .reservation_summary .option_selected .rate .conditions {
    color: $black;
}

.body_modal_content {
  .modal_info.rooms_features {
    .modal_container.room_content {
      .icons_room {
        .room_services {
          .service_element {
            i {
              color: $corporate_3 !important;
            }

            .service_description {
              color: $corporate_2;
            }
          }
        }
      }
    }
  }
}

div#reservation{
  .email_header_logo{
    width: 100%;
    max-width: 100%!important;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
  }
  div:nth-child(2){
    div:last-child{
        a{
          position: relative;
          top: 25px;
        }
    }
  }
}

#full_wrapper_booking .paraty-booking-form.has_hotel_selector {
  .destination_wrapper {
    width: 240px;
    padding: 10px 15px;

    label {
      font-family: $text_family;
      color: $black;
      font-size: 13px;
      font-weight: normal;
      letter-spacing: 1.3px;
      line-height: 14.4px;
      text-transform: uppercase;
    }

    .destination_field {
      input {
        margin-top: 4px;
      }

      input, input::placeholder {
        font-family: $text_family;
        color: $black;
        font-weight: 500;
        font-size: 20px !important;
        letter-spacing: 1.2px;
        line-height: 24px;
      }

      &:before {
        content: '';
        position: absolute;
        right: 0px;
        top: 55%;
        width: 7px;
        height: 7px;
        border-right: 2px solid $corporate_1;
        border-bottom: 2px solid $corporate_1;
        transform: translate(0%, -50%) rotate(45deg);
      }
    }
  }


  .wrapper_booking_button {
    width: 405px !important;
  }

  .stay_selection {
    width: 33% !important;
  }

  .guest_selector {
    width: 135px !important;

    .placeholder_text span {
      display: none;
    }
  }

  .hotel_selector {
    width: max-content;
    padding: 30px;
    box-shadow: 0px 3px 6px #00000029;
    border: none;
    border-radius: 8px;

    &:before, &:after {
      display: none;
    }

    .hotel_selector_inner {
      .hotel_list_subtitle {
        font-family: $text_family;
        color: $corporate_1;
        font-size: 16px;
        line-height: 23px;
        font-weight: bold;
      }

      ul {
        padding-left: 10px;

        li.hotel_selector_option {
          font-family: $text_family;
          color: #111111;
          font-size: 16px;
          border: none;

          &:hover {
            background-color: transparent;
          }

          &.selected {
            font-weight: bold;
            color: $corporate_1;
          }
        }
      }
    }
  }
}

.booking_engine_wrapper_process.has_babies {
  #booking.boking_widget_inline {
    .booking_form.has_hotel_selector {
      .stay_selection {
        width: 31% !important;
      }

      .guest_selector {
        width: 160px !important;
      }

      .wrapper_booking_button {
        width: 405px !important;
      }
    }
  }
}



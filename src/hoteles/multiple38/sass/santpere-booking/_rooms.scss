div#step-1 {
  .contTipoHabitacion {
    padding: 0;
    border-radius: 0;
    box-shadow: 0px 3px 10px #00000029;
    margin-bottom: 50px;

    .contFotoDescripcion {
      padding: 0;

      .contFotoHabitacion {
        width: 320px;
        min-height: 215px;

        .see_more_rooms_v2 {
          img {
            @include cover_image;
            position: static;
          }

          .lupa {
            right: auto;
            left: 0;
            padding: 15px;

            font-family: "Font Awesome 5 Pro";
            font-size: 30px;
            font-weight: 300;
          }
        }
      }

      .contDescHabitacion {
        margin: 0;
        width: 820px;
        padding-top: 10px;
        box-sizing: border-box;
        height: 215px;

        .room_description_name_wrapper {
          max-height: 115px;
          .cabeceraNombreHabitacion  .tipoHabitacion{
              margin-bottom: -25px;
              margin-left: 3px;
          }
        }

        .tipoHabitacion {
          font-family: $title_family;
          font-weight: 400;
          font-size: 21px;
          letter-spacing: 1.15px;
          line-height: 29px ;
          text-transform: capitalize;
          color: $black;
        }

        .descripcionHabitacion {
          font-weight: 300!important;
          margin-top: 7px;
        }

        .occupancy_wrapper {
          display: none;
        }

        .very_asked_message {
          background: $pink;
          text-transform: uppercase;
          font-weight: 100;

          &::after {
            border-left: 15px solid $pink;
          }
        }

        .just_booking_message {
          background: $corporate_1;
          top: 10px;

          &::after {
            border-left: 15px solid $corporate_1;
          }
        }

        .descripcionHabitacion {
          font-family: $text_family;
          font-weight: 400;
          font-size: 16px;
          letter-spacing: 0.75px;
          line-height: 21px;
          color: $black;
          height: 70px !important;
          margin-top: 20px;
        }

        .see_more_rooms_v2 {

          .plus_sign {
            color: #333333;
            font-weight: 300;
          }

          .see_more {
            font-family: $text_family;
            font-weight: 300;
            font-size: 13px;
            letter-spacing: 0.75px;
            line-height: 1;
            color: #333333;
            text-transform: uppercase;
            text-decoration: none;
          }
          bottom: 65px;
        }

        .room_services {
          margin: 0 auto;
          border-top: 1px solid rgba(black, 0.1);
          border-bottom: 1px solid rgba(black, 0.1);

          .service_element {
            border-right: none;
          }

          .service_element + .service_element {
            border-left: 1px solid rgba(black, 0.1);
          }
        }
      }
    }

    .preciosHabitacion {
      .listadoHabsTarifas {
        padding-bottom: 10px;

        .contTitTipoTarifa {
          background: #d2d2d2 !important;
          .titTipoTarifa {
            font-family: $text_family;
            font-weight: 400;
            font-size: 13px;
            letter-spacing: 0.75px;
            color: $grey-1;
            text-transform: capitalize;
          }

          .cheapest_rate_message,
          .advice_rate_message {
            .best_price_label_info {
              font-family: $text_family;
              font-weight: 600;
              font-size: 12px;
              letter-spacing: 0.75px;
              line-height: 1;
              color: $black;
            }


            a {
              background: transparent;
              width: max-content;
              margin-right: 0px !important;
              color: #dad0bc !important;
              text-decoration: none;
            }
          }

          .cheapest_rate_message {
            background-color: $pink !important;
            .best_price_label_info {
              color: white;
              font-weight: 200;
            }
            &:before {
              border-left: 19px solid #d2d2d2 !important;
            }
          }

          .advice_rate_message {
            color: $grey-2;
            background-color: $corporate_2 !important;
          }
        }

        .conditions_info_wrapper {
          .last_day_cancellation_text {
            background: url(https://storage.googleapis.com/cdn.paraty.es/sant-pere-bosc/files/check%20%282%29.png) no-repeat left center;
            color: $pink !important;
          }

          a {
            position: relative;
            background: none;
            margin-right: 0px !important;
            width: max-content;


            &::before {
              display: inline-block;
              vertical-align: middle;
              font-family: "Font Awesome 5 Pro";
              content: '\f05a';
              font-weight: 100;
              margin-right: 5px;
              color: #dad0bc;
            }

            &:hover {
              color: $corporate_2;
            }
          }
        }

        .regimenColumn {
          width: 35%;
          font-family: $text_family;
          font-weight: 400;
          font-size: 13px;
          letter-spacing: 0.75px;

          .tTextoOferta {
            font-weight: 600;
            font-size: 13px;
            margin-left: 3px;
            color: $pink;
          }
        }

        .precioNocheColumn {
          width: 10%;
        }

        .precioTotalColumn {
          width: 35%;
          font-family: $text_family;

          .precioTachadoDiv {
            text-align: right;
            color: $pink;
            margin-right: 10px;

            .tPrecioTachado {
              font-weight: 400;
              font-size: 11px;
              letter-spacing: 0.65px;
              color: $pink;
            }
          }

          .precioGeneralDiv {
            .tPrecioTotal,
            .tPrecioOferta {
              font-weight: 700;
              font-size: 16px;
              color: $grey-2 !important;
              letter-spacing: 0.35px;
            }
          }

          .promotion_percentage_square {
            border-radius: 50%;

            .promotion_discount {
              border-radius: 10px;
              background-color: $corporate_2;
            }
          }

          .priceTitle {
            font-weight: 400;
            font-size: 9px;
            color: $grey-2;
            letter-spacing: 0.2px;
            line-height: 8px;
          }
          .priceValues {
            width: 100px;
          }
        }

        .botonReservarColumn {
          button {
            width: 185px;
            height: 50px;
            outline: none;
            font-family: $title_family;
            font-size: 18px;
            letter-spacing: 1.5px;
            font-weight: 500;
          }

          .booking-button {
            background: $corporate_1;
            padding: 10px 5px;
            text-align: center;
            font-size: 18px;
            @include transition(all, .5s);

            &:hover {
              background: $corporate_2;
            }
          }
        }
      }
    }
  }
  .mix_rooms_wrapper {
    .contFotoDescripcion {
        .contDescHabitacion {
            .descripcionHabitacion {
                height: 80px !important;
                display: block !important;
            }
        }
    }
  }
  .total_price_wrapper {
    .total_price {
        background: $corporate_1;
    }
    .rooms_submit_button {
        background: $corporate_1;
        &.disabled {
            background: #ccc;
        }
    }
  }
}

.fancybox-overlay {
  .room_popup_individual_element {
    .popup_title {
      font-family: $title_family;
      font-weight: 500;
      font-size: 21px;
      letter-spacing: 1.15px;
      line-height: 1;
      text-transform: uppercase;
      color: white;
    }

    .popup_carousel {
      .exceded {
        padding: 0;

        .popup_image {
          @include cover_image;
          position: static;
        }
      }
    }

    .popup_room_description {
      font-family: $text_family;
      font-weight: 400;
      font-size: 13px;
      letter-spacing: 0.75px;
      line-height: 21px;
      color: $black;

      strong {
        font-weight: 700;
      }
    }
  }
}

.room_popup_individual_element .room_services,
div#step-1 .contTipoHabitacion .contFotoDescripcion .contDescHabitacion .room_services {
  display: grid !important;
  padding: 0 !important;
  grid-template-columns: repeat(4, auto);
  background-color: transparent !important;

  .service_element {
    width: 100%;
    height: auto;
    @include display_flex(nowrap);
    justify-content: center;
    align-items: center;
    padding: 10px;
    text-align: center;

    i {
      font-size: 25px;
      margin-right: 10px;
      color: $corporate_1;
    }

    .service_description {
      font-family: $text_family;
      font-weight: 400;
      font-size: 12px;
      letter-spacing: 0.7px;
      line-height: 1;
      color: $black;
    }
  }
}

.rate_conditions_popup_wrapper h3.rate_name, .package_hidden_popup .popup_title {
  font-family: $text_family;
}

#packages_b1_wrapper.v2 {
  .ui-datepicker {
    .ui-widget-header .ui-datepicker-title, table {
      font-family: $text_family;
    }

    .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
      font-family: $text_family;
    }
  }
}
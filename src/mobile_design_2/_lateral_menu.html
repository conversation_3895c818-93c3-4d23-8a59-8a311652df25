<nav id="menu">
  <ul>
    {% for section in left_mobile_sections %}
        {% if section.subsections %}
            <li class="section_subcontent">
                    <span>
                        {{ section.title|safe }}
                    </span>
                    <img class="plus_menu_deploy" src="/static_1/images/mobile_img/renovation/plus_menu.png">

            </li>
            <ul class="submenu_list" id="submenu_element_{{ forloop.counter }}">
                        {% if not section.disabled %}<li><a href="{{host|safe}}/{{section.friendlyUrl}}">{{ section.title|safe }}</a></li>{% endif %}
                        {% for x in section.subsections %}
                            <li><a href="{{host|safe}}/{{x.friendlyUrl}}">{{ x.title|safe }}</a></li>
                        {% endfor %}
            </ul>
        {% else %}
            <li {% if sectionToUse.sectionName == section.sectionName %}class="active"{% endif %}>
                {% if section.sectionType == 'Inicio' and not initial_section %}
                  <a href="{{host|safe}}/">
                      {% if section.title_mobile %}
                          {{ section.title_mobile|safe }}
                      {% else %}
                        {{ section.title|safe }}
                      {% endif %}
                  </a>
                {% else %}
                  <a {% if section.replace_link %}href="{{ section.replace_link|safe }}" target="_blank"
                   {% else %}href="{{host|safe}}/{{section.friendlyUrl}}"{% endif %}>
                      {% if section.title_mobile %}
                              {{ section.title_mobile|safe }}
                      {% else %}
                        {{ section.title|safe }}
                      {% endif %}
                  </a>
                {% endif %}
            </li>
        {% endif %}
    {% endfor %}
  </ul>
</nav>
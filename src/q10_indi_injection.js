$ = jQuery;
{{ widget_controller_injection|safe }}

bookingWidgetController.add_widget_html = function () {
    var paraty_widget = bookingWidgetController.config.widget_html;
    if (!$("#widget-paraty").length) {
        $("body").append($("<div id='widget-paraty'></div>"));
    }

    $("#widget-paraty").html(paraty_widget);
    $("#widget-paraty #full_wrapper_booking .booking_form_title").append($(".cancel_booking_link"));

    const extra_widget_class = $("#widget-paraty input#extra_widget_class").val();
    if (extra_widget_class) {
        $("#widget-paraty").addClass(extra_widget_class);
    }
};

bookingWidgetController.occupancy_format_html = function () {
    return "<span class='adults'>@@N_A@@</span>/<span class='children'>@@N_C@@</span>/<span class='children'>@@N_B@@</span>";
};

bookingWidgetController.custom_format_date = function (dateComponents) {
    dateComponents = dateComponents.split("/");
    var month_names = $.datepicker._defaults.monthNamesShort,
        html_date = "<div class='day'>%d</div> <div class='month'>%m</div> <div class='year'>%y</div>",
        month = (parseInt(dateComponents[1]) - 1);

    return html_date.replace("%d", dateComponents[0]).replace("%m", month_names[month]).replace("%y", dateComponents[2]);
},

    bookingWidgetController.adding_room_tag_selector = function () {
        $("select.rooms_number option").each(function (index, element) {
            $(element).text($(element).text());
        });
        $("select.rooms_number").selectric("refresh");
    },

    bookingWidgetController.close_widget = function () {
        $("i.fa-times.close_widget").click(function () {
            let booking_wrapper = $("#full_wrapper_booking");
            booking_wrapper.parent('#widget-paraty.popup').removeClass('opened');
            booking_wrapper.fadeOut(function () {
                booking_wrapper.parent('#widget-paraty').removeClass('popup');
            });
        });
    };

function searchCookie(key) {
    var encontrado = -1;
    var x = document.cookie;
    if (x) {
        var y = x.split(";");
        for (var i = 0; i < y.length; i++) {
            encontrado = y[i].search(key);
            if (encontrado > -1) {
                resultado = y[i].split("=");
                return resultado[1];
            }
        }
    }
}

bookingWidgetController.custom_functions = function () {
    prepare_guests_selector();
    set_occupancy_number();

    bookingWidgetController.config.languages = {
        "es": "SPANISH",
        "en": "ENGLISH"
    };

    try {
        create_link_booking();
    } catch (error) {
        console.log("[Paraty Widget] Error trying to get the url to booking engine");
    }


    $('.open_booking_popup').on('click', function () {
        let widget_paraty = $('#widget-paraty')
        widget_paraty.addClass('popup').addClass('opened');
        widget_paraty.find('#full_wrapper_booking').fadeIn();
    });

    filter_selector_controller();
};

bookingWidgetController.config.avoid_guest_autoclose_click = true;

function create_link_booking() {
    var link_booking = bookingWidgetController.config.base_url + "/booking1?numRooms=1&adultsRoom1=2" +
        "&adultsRoom2=0&adultsRoom3=0&childrenRoom1=0&childrenRoom2=0&childrenRoom3=0&babiesRoom1=0&" +
        "babiesRoom2=0&babiesRoom3=0&fromCountry=&language=" +
        bookingWidgetController.config.languages[bookingWidgetController.config.language];

    $(".paraty-booking-link").each(function () {
        $(this).attr("href", link_booking);
    });
}

function filter_selector_controller() {
    $("#widget-paraty .filter_wrapper").click(function () {
        $("#widget-paraty .filter_selector").toggleClass("active");
        $(this).toggleClass("active");
    });

    $("#widget-paraty .filter_selector_option").click(function () {
        const filter_selector = $(this).closest(".filter_selector");
        filter_selector.removeClass("active");
        $("#widget-paraty .filter_wrapper").removeClass("active");

        filter_selector_click($(this));
    });
}

function filter_selector_click(filter) {
    let title_selector = filter.find('.title_selector').text(),
        data_filter = filter.attr('data-filters'),
        filters_input = $("#widget-paraty input[name='advanced_filters']"),
        filters_field = $("#widget-paraty .filters");

    if (filters_input) {
        filters_field.text(title_selector);
        filters_input.val(data_filter);
    }
}

function add_room() {
    var number_rooms = parseInt($("select.rooms_number").val());

    if (number_rooms < 3) {
        $($(".selectric-rooms_number .selectricItems li").get(number_rooms)).trigger("click");
        set_occupancy_number();
        $(".add_remove_room_wrapper .remove_room").removeClass("disabled");
    }
    if (number_rooms >= 2) {
        $(this).addClass("disabled");
    }
}

function remove_room() {
    var number_rooms = parseInt($("select.rooms_number").val());

    if (number_rooms > 1) {
        $($(".selectric-rooms_number .selectricItems li").get(number_rooms - 2)).trigger("click");
        set_occupancy_number();
        $(".add_remove_room_wrapper .add_room").removeClass("disabled");
    }
    if (number_rooms <= 2) {
        $(this).addClass("disabled");
    }
}

function prepare_guests_selector() {
    $('body').on('click', '.guest_selector, .close_room_selector', function () {
        toggle_guest_selector();
    });

    $("select.room_selector").unbind("change");
    $(".room_selector").selectric('destroy');
    $(".room_selector").selectric({disableOnMobile: false});
    $("select.room_selector, select.rooms_number").change(function () {
        set_occupancy_number();
    });

    var add_room_html = "<div class='add_remove_room_wrapper clearfix'><div class='add_room'>" + $.i18n._("add_room") + "</div><div class='remove_room disabled'>" + $.i18n._("T_eliminar") + "</div></div>",
        close_room_selector = "<span class='close_room_selector'></span>";
    $(".room_list_wrapper").append(add_room_html).append(close_room_selector);
    $(".add_remove_room_wrapper .add_room").click(add_room);
    $(".add_remove_room_wrapper .remove_room").click(remove_room);

    $(".adults_selector .selectric-room_selector .label").click(function () {
        plus_room_selector($(this), 0, 8);
    });
    $(".adults_selector .selectric-room_selector .button").click(function () {
        minus_room_selector($(this), 1, 9);
    });
    $(".children_selector .selectric-room_selector .label").click(function () {
        plus_room_selector($(this), -1, 4);
    });
    $(".children_selector .selectric-room_selector .button").click(function () {
        minus_room_selector($(this), 0, 5);
    });

    $(".babies_selector .selectric-room_selector .button").click(function () {
        minus_room_selector($(this), 0, 3);
    });
    $(".babies_selector .selectric-room_selector .label").click(function () {
        plus_room_selector($(this), -1, 2);
    });


}

function plus_room_selector(element, min_val, max_val) {
    var selectric_element = element.closest(".selectric-room_selector").find("select.room_selector");
    if (parseInt(selectric_element.val()) > min_val &&
        parseInt(selectric_element.val()) < max_val) {
        var new_select_val = parseInt(selectric_element.val()) + 1;
        selectric_element.val(new_select_val);
        selectric_element.selectric('refresh');
        set_occupancy_number();
    }
}

function minus_room_selector(element, min_val, max_val) {
    var selectric_element = element.closest(".selectric-room_selector").find("select.room_selector");
    if (parseInt(selectric_element.val()) > min_val &&
        parseInt(selectric_element.val()) < max_val) {
        var new_select_val = parseInt(selectric_element.val()) - 1;
        selectric_element.val(new_select_val);
        selectric_element.selectric('refresh');
        set_occupancy_number();
    }
}

function toggle_guest_selector() {
    var target_room_wrapper = $(".room_list_wrapper");
    if (!target_room_wrapper.hasClass('active')) {
        target_room_wrapper.addClass('active');
        target_room_wrapper.show();
    } else {
        target_room_wrapper.removeClass('active');
        target_room_wrapper.hide();
    }
    set_occupancy_number();
}

function set_occupancy_number() {
  var number_of_rooms = $("select[name='numRooms']").val(),
      adults_number = 0,
      kids_number = 0,
      babies_number = 0;

  if (number_of_rooms) {
    for (var room_loop = 1; room_loop <= number_of_rooms; room_loop++) {
      var actual_select_adults = $("select[name='adultsRoom" + room_loop + "']").val(),
          actual_select_kids = $("select[name='childrenRoom" + room_loop + "']").val(),
          actual_select_baby = $("select[name='babiesRoom" + room_loop + "']").val();

      if (actual_select_adults || actual_select_kids || actual_select_baby) {
        adults_number += parseInt(actual_select_adults);
        kids_number += parseInt(actual_select_kids);
        babies_number += parseInt(actual_select_baby);
      }
    }
  }

  var target_placeholder = $(".guest_selector .placeholder_text"),
      placeholder_string = "";

  adults_number = parseInt(adults_number);
  kids_number = parseInt(kids_number);
  babies_number = parseInt(babies_number);

  placeholder_string += "<span class='guests'>" + adults_number + " " + $.i18n._('T_adultos') + " / " + kids_number + " " + $.i18n._('T_ninos');

  if (babies_number) {
    placeholder_string += + " / " + babies_number + " " + $.i18n._('T_bebes');
  }

  placeholder_string += "</span>";

  target_placeholder.html(placeholder_string);
}

if (typeof (booking_widget_initialized) === 'undefined') {
    var booking_widget_initialized = true;
    bookingWidgetController.init();
}
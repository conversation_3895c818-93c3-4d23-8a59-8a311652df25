# -*- coding: utf-8 -*-
import os

from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name_with_properties
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.language.language_utils import get_web_dictionary
from utils.web.BaseInjectionHandler import InjectionScript, InjectionWidgetHandler


class q10IndiWidget(InjectionWidgetHandler):

    def getBookingWidgetOptions(self, language, selectOptions=None):
        options = super(q10IndiWidget, self).getBookingWidgetOptions(language)

        widget_config = get_section_from_section_spanish_name_with_properties("_widget_config", language)
        if widget_config:
            if widget_config.get('extra_widget_class'):
                options['ExtraElementBeforeRoomList'] = '<input type="hidden" id="extra_widget_class" value="%s"/>' % widget_config['extra_widget_class']

            widget_pics = get_pictures_from_section_name("_widget_config", language)
            filter_options = [x for x in widget_pics if x.get('title') == 'filter_option']
            options['filter_options'] = []
            for option in filter_options:
                option.update(get_properties_for_entity(option.get('key'), language))
                options['filter_options'].append(option)

        options['custom_promocode_label'] = get_web_dictionary(language).get('T_promocode')
        options['inline_ages'] = False
        options['namespace'] = ''

        return options


class q10IndiScript(InjectionScript):

    def params_base_script_controller(self):
        context = {
            "widget_url": "q10Indiwidget",
            "widget_css": "q10-indi",
            "static_version": "1.04",
            "booking_version": "7",
            "calendar_version": "5"
        }

        return context

    def template_controller_name(self):
        return "q10_indi_injection.js"

    def template_controller_path(self):
        return os.path.join(os.path.dirname(__file__), self.template_controller_name())

@import "plugins/jquery_ui_1_8_16_custom";

.dates {
  display: flex;
  flex-flow: row nowrap;

  > div {
    width: 50%;
  }

  .entry_date_wrapper {
    padding-right: $item-padding / 2;
  }

  .departure_date_wrapper {
    padding-left: $item-padding / 2;
  }
}

#departure_date_popup.active {
  -webkit-transition: all 0s;
  -moz-transition: all 0s;
  -ms-transition: all 0s;
  -o-transition: all 0s;
  transition: all 0s;
}

.flexible_dates_wrapper {
  position: absolute;
  top: 100%;
  bottom: 0;
  background: white;
  z-index: 2;
  transition: all .5s;
  overflow: auto;
  height: 100%;
  #calendar_price_availability #prices-calendar .calendars-section .bottom_buttons_wrapper {
    top: 100%;
  }

  &.active {
    top: 15%;
    width: 100%;

    #calendar_price_availability {
      #prices-calendar {
        margin-bottom: 75px;

        .calendars-section {
          .bottom_buttons_wrapper {
            top: auto;
          }
        }
      }
    }

    .calendar_app {
      display: block;

      .full_container {
        padding: 10px 0 200px 0;

        .calendar_wrapper {
          margin-top: 0;
        }

        .title, .date_suggestions_wrapper {
          display: none;
        }
      }

      .price_calendar_wrapper.is_mobile {
        border-radius: 0;
      }
    }
  }
}

#entry_date_popup, #departure_date_popup {
  position: fixed;
  left: 0;
  right: 0;
  top: 100%;
  bottom: 0;
  z-index: 100;
  background: white;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  .btn_calendar {
    @include center_xy;
  }
  .start_datepicker {
    .ui-datepicker-current-day:before {
      display: none;
    }
  }
  .buttons_bottom {
    position: absolute;
    bottom: 0;
    background: white;
    width: 100%;
    padding: 15px;
    box-shadow: 0px -3px 6px #00000029;
    border-top: 1px solid rgba(81, 81, 81, 0.26);
    font-size: 14px;
    @include display_flex;
    justify-content: center;
    align-items: center;
    z-index: 1;

    .message_element {
      width: 55%;
      text-align: left;
      font-size: 13px;
      display: inline-block;
      color: #303948;
      padding: 0 15px 0 10px;
      &.close_popup {
        text-align: center;
        font-size: 15px;
        text-transform: uppercase;
      }
      &:before {
        display: none;
      }
    }
    .button {
      width: 35%;
      display: inline-block;
      border-radius: 50px;
      border: none;
      font-size: 14px;
      text-transform: uppercase;
      text-align: center;
      padding: 10px 20px;
      color: white;
      font-weight: bold;
      text-decoration: none;
      margin: 0;
      background: $corporate_2;
      &.disabled {
        opacity: .5;
      }
    }
  }
  &.active {
    top: 0;
  }
  .header_wrapper {
    padding: 20px;

    .banner_title {
      display: inline-flex;
      text-transform: uppercase;
      font-size: 16px;
      font-weight: 500;
      color: $body-text-color;

      i {
        margin-right: 10px;
        font-size: 22px;
        vertical-align: middle;
        color: $corporate_2;
      }

      .nights_selected_wrapper {
        display: inline-flex;
        gap: 1vw;
        margin-left: 5vw;
      }
    }

    .close_popup {
      position: absolute;
      right: 25px;
      top: 15px;
      font-size: 29px;
      color: black;
    }
  }

  .menu_controller {
    border-top: 1px solid rgba(81, 81, 81, 0.26);
    border-bottom: 1px solid rgba(81, 81, 81, 0.26);
    display: table;
    width: 100%;
    height: 20px;

    .dates_button {
      padding: 10px 0;
      width: 50%;
      float: left;
      text-align: center;
      z-index: 4;
      position: relative;
      display: table;
      height: 100%;
      text-transform: uppercase;
      font-size: 13px;
      background-color: #FFF;

      span {
        display: table-cell;
        vertical-align: middle;
      }

      &.active {
        position: relative;

        &:after {
          content: '';
          position: absolute;
          left: 0;
          right: 0;
          top: calc(100% - 3.5px);
          height: 3px;
          background: black;
          width: 100%;
        }
      }
    }
  }

  .container_flexible_calendar {
    display: none;
    overflow-y: scroll !important;
    position: relative;
    max-height: calc(85vh - 15px);
  }

  .ui-datepicker-group {
    display: block;
    clear: both;
    width: 100%;
    margin-bottom: 30px;
  }

  .start_datepicker, .end_datepicker {
    max-height: 90vh;
    overflow: auto;
    overflow-x: hidden;

    .ui-datepicker-calendar * {
      border: none !important;
      border-collapse: collapse!important;
      padding: 0!important;
      margin: 0!important;
    }

    .ui-datepicker-inline {
      width: 100% !important;
      border: 0;

      .ui-datepicker-prev, .ui-datepicker-next {
        display: none;
      }

      .ui-datepicker-header {
        background: none;
        border: 0;
        color: $corporate_2;
        border-bottom: 1px solid rgba(81, 81, 81, 0.26);
        margin-bottom: 20px;
        padding: 12px 0;
        .ui-datepicker-title {
          font-family: inherit;
        }
      }

      .ui-state-default {
        text-align: center;
        background: none;
        border: 0;
        color: #646464;
        line-height: 41px;
        font-weight: bold;
        font-size: 15px;
      }

      .highlight_day {
        background: $corporate_2;
        opacity: .5;
        a {
          color: white !important;
        }
      }

      .ui-datepicker-current-day, .end_date_selection {
        position: relative;

        &:before {
          content: '';
          position: absolute;
          left: 0;
          width: 50%;
          top: 0;
          bottom: 0;
          background: $corporate_2;
          opacity: .5;
        }

        .ui-state-active, .date_active {
          background: transparent !important;
          color: white;
          border-radius: 50px;
          position: relative;
          z-index: 1;

          &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: $corporate_2;
            max-width: 41px;
            max-height: 41px;
            z-index: -1;
            margin: auto;
            border-radius: 41px;
          }
        }
      }

      .start_date_selection {
        position: relative;
        opacity: 1;

        &:has(> .date_active) {
            background: linear-gradient(to right, transparent 50%, rgba($corporate_2, 0.5) 50%);
        }

        &:before {
          content: '';
          position: absolute;
          right: 0;
          width: 50%;
          top: 0;
          bottom: 0;
          background: $corporate_2;
          opacity: .5;
          z-index: 0;
        }

        span {
          color: white !important;
          //background: #515151;
          border-radius: 50px;
          position: relative;
          z-index: 1;

          &:before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: $corporate_2;
            max-width: 41px;
            max-height: 41px;
            z-index: -1;
            margin: auto;
            border-radius: 41px;
          }
        }
      }

      thead span {
        color: #515151;
      }
    }
  }

  .step_label {
    position: absolute;
    bottom: 0;
    background: white;
    color: $corporate_2;
    text-align: right;
    width: 100%;
    padding: 15px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    font-size: 12px;
    &:after {
      content: '\f059';
      display: inline-block;
      vertical-align: middle;
      font-family: "Font Awesome 5 Pro", sans-serif;
      font-weight: 300;
      font-size: 16px;
      margin-left: 5px;
    }
  }

  &.active:has(.normal_calendar_button.active), &.active:has(.calendar_button.active) {
    .bottom_wrapper {
      display: block;
      border: none;

      .confirm_dates_button_wrapper {
        margin-right: 20px;
        position: fixed;
        width: 100%;
        left: 0;
        bottom: 0;
        box-shadow: 0px -3px 6px #5f5f5f19;
        padding: 20px 20px;
        background-color: white;
        z-index: 6;

        .confirm_dates_button {
          width: 100%;
          height: 52px;
          color: white;
          font-size: 20px;
          font-weight: bold;
          text-align: center;
          background-color: var(--booking_btn_color);
          border: 1px solid var(--booking_btn_color);
          overflow: hidden;
          cursor: pointer;

          &.disabled {
            color: var(--booking_btn_color);
            background-color: white;
            cursor: default;
          }
        }
      }
    }
  }
}
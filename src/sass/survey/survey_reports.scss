@import url('https://fonts.googleapis.com/css?family=Roboto:100,300,400,500,700');
@import url('https://fonts.googleapis.com/css?family=Oswald:300,400');
@import "plugins/_fontawesomemin";

body {
  //width: 840px;
  padding: 50px;
  margin: auto;
  font-family: 'Roboto', sans-serif;
}

#chartdiv {
  width: 100%;
  height: 270px;
  margin: auto;
}

.bars_chart {
  overflow: visible !important;
}

.header_wrapper {
  background: rgb(50, 50, 50);
  width: 100%;
  height: 100px;
  margin-top: 20px;
  position: relative;

  .paraty_logo {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 30%;
    background: lightgray;

    .corporate_logo {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
    }
  }

  .hotel_logo {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 70%;

    .logo_element {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      margin: auto;
      max-height: 95px;
    }
  }
}

.general_report_info {
  text-align: center;
  margin-bottom: 30px;

  .month_year_wrapper {
    font-weight: lighter;
    font-size: 55px;
    margin-bottom: 15px;
  }

  .answers_medium_info {
    &:before {
      content: '';
      width: 100%;
      height: 5px;
      display: block;
      background-image: linear-gradient(to right, black 23%, rgba(255, 255, 255, 0) 0%);
      background-position: top;
      background-size: 8px 1px;
      background-repeat: repeat-x;
      margin-bottom: 15px;
    }

    &:after {
      content: '';
      width: 100%;
      height: 5px;
      display: block;
      background-image: linear-gradient(to right, black 23%, rgba(255, 255, 255, 0) 0%);
      background-position: bottom;
      background-size: 8px 1px;
      background-repeat: repeat-x;
      margin-top: 15px;
    }

    .answer_number, .skipped_answer_number, .medium_review_answer {
      width: 33.33%;
      float: left;
      position: relative;
      min-height: 120px;

      &:after {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        width: 5px;
        height: 100%;
        display: block;
        background-image: linear-gradient(black 22%, rgba(255, 255, 255, 0) 0%);
        background-position: right;
        background-size: 1px 7px;
        background-repeat: repeat-y;
      }
    }

    .medium_review_answer:before {
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      right: 0;
      width: 5px;
      height: 100%;
      display: block;
      background-image: linear-gradient(black 22%, rgba(255, 255, 255, 0) 0%);
      background-position: right;
      background-size: 1px 7px;
      background-repeat: repeat-y;
    }

    .numbers_blocks {
      display: table;
      width: 100%;
    }

    .general_client_satisfaction {
      margin-top: 20px;
      color: white;
      background: #323232;
      padding: 20px 0;
      margin-bottom: 25px;
      font-weight: 300;
      font-size: 24px;

      b {
        font-weight: 400;
      }
    }
  }

  .number_wrapper {
    width: 170px;
    margin: auto;
    line-height: 60px;
    text-align: center;
    margin-top: 20px;
    background: rgb(50, 50, 50);
    color: white;
    font-size: 30px;
    font-family: 'Oswald', sans-serif;
    font-weight: 300;
    position: relative;
    overflow: hidden;

    &:before {
      content: '';
      position: absolute;
      left: 80%;
      top: -3px;
      background: rgba(255, 255, 255, 0.51);
      -webkit-transform: rotate(45deg);
      -moz-transform: rotate(45deg);
      -ms-transform: rotate(45deg);
      -o-transform: rotate(45deg);
      transform: rotate(45deg);
      width: 81px;
      height: 109px;
    }

    &:after {
      content: '';
      width: 0;
      height: 0;
      border-style: solid;
      border-width: 30px 0 30px 25px;
      border-color: transparent transparent transparent white;
      position: absolute;
      left: 0;
      top: 0;
    }

    .fa {
      position: absolute;
      bottom: 5px;
      right: 10px;
      font-size: 20px;
      color: orange;
    }
  }

  .smile_ico {
    width: 170px;
    margin: auto;
    line-height: 60px;
    text-align: center;
    margin-top: 20px;

    .face_image {
      margin-top: 0;
      height: 63px;
    }
  }
}

//Questions
.question_to_client {
  width: 50%;
  float: left;
  text-align: center;
  padding: 0 60px;
  box-sizing: border-box;
  height: 355px;

  h3.question_label {
    font-weight: 400;
    color: gray;
    margin-bottom: 0;
  }

  .bars_wrapper {
    padding: 0 20px;

    h4.label_answer {
      text-align: left;
      font-weight: 400;
      font-size: 13px;
      margin-bottom: 5px;
      color: gray;
    }

    .bar {
      width: 100%;
      height: 50px;
      background: #f1f1f1;
      position: relative;

      .block_visualize {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        background: #80ba84;
      }

      &.cent .block_visualize {
        width: 100%;
      }

      &.ninety .block_visualize {
        width: 90%;
      }

      &.eighty .block_visualize {
        width: 80%;
      }

      &.seventy .block_visualize {
        width: 70%;
      }

      &.sixty .block_visualize {
        width: 60%;
      }

      &.fifty .block_visualize {
        width: 50%;
      }

      &.fourty .block_visualize {
        width: 40%;
      }

      &.thirty .block_visualize {
        width: 30%;
      }

      &.twenty .block_visualize {
        width: 20%;
      }

      &.ten .block_visualize {
        width: 10%;
      }
    }
  }
}

//Faces
.faces_wrapper {
  .face_element {
    display: inline-block;
    margin-bottom: 10px;

    .face_label {
      display: block;
      font-weight: 400;
      font-size: 13px;
      margin-bottom: 5px;
      color: gray;
    }

    .face_image {
      width: 65px;
      height: 65px;
    }
  }
}

//Selection
.bars_chart {
  width: 100%;
  height: 250px;
}

.comments_wrapper {
  display: block;
  width: 100%;
  clear: both;

  &:before {
    content: '';
    width: 100%;
    height: 5px;
    display: block;
    background-image: linear-gradient(to right, black 23%, rgba(255, 255, 255, 0) 0%);
    background-position: top;
    background-size: 8px 1px;
    background-repeat: repeat-x;
    margin-bottom: 15px;
  }

  h2.question_label {
    font-weight: 400;
    color: gray;
    font-size: 18px;
  }

  .answer_wrapper_element {
    display: table;
    width: 100%;
    margin-bottom: 8px;

    .identifier_wrapper {
      width: 20%;
      float: left;
      text-align: center;
      color: white;
      background: #79bbdc;
      padding: 20px 0;
    }

    .comment_element {
      width: 79%;
      float: right;
      padding: 20px;
      box-sizing: border-box;
      background: #f1f1f1;
      color: gray;
      font-size: 16px;
      font-weight: 300;
    }
  }

}

h2.hotel_name {
  font-weight: 400;
  color: gray;
  font-size: 38px;
  text-transform: uppercase;
}

//Bars Chart
.amcharts-chart-div a[title="JavaScript charts"] {
  display: none !important;
}

.bars_chart {
  text {
    text-align: left;
    font-weight: 400;
    font-size: 13px;
    margin-bottom: 5px;
    color: gray;
    font-family: Roboto, sans-serif;
  }
}
.shopping_cart_process_v2 {
  .reservation_summary, .booking_details_prices_wrapper:not(.b3_alt_summary) {
    .booking_details_wrapper {
      padding-bottom: 0;
      margin-bottom: 0;

      .hotel_name {
        padding: 10px 25px;
        font-size: 16px;
        font-weight: 600;
        text-transform: uppercase;
      }

      .hotel_name_small {
        padding: 0 25px 10px;

        .label {
          font-weight: bold;
        }
      }

      .hide {
        display: none;
      }

      .b3_cart_resume_dropdown {
        border: 1px solid #D7DAE2;
        border-radius: 4px;
        width: 90%;
        margin: 10px auto;

        .resume_inner {
          padding: 24px;
          font-size: 14px;
          line-height: 19px;

          .dropdown_title {
            color: #707070;
            font-size: 18px;
            line-height: 24px;
            font-weight: 500;
          }

          div {
            margin: 5px 0;

            .resume_icon {
              margin-right: 5px;
              color: #4284d0;
            }

            &.toggle_resume_dropdown {
              margin-top: 10px;
              color: #4284d0;
              cursor: pointer;

              i {
                margin-left: 5px;
              }
            }
          }
        }
      }

      .book_type_wrapper,
      .services_wrapper,
      .include_services_wrapper {
        padding: 10px 25px;
        color: white;
        background-color: #2BB0A3;

        i {
          font-size: 20px;
          margin-right: 14px;
        }

        span {
          font-size: 12px;
          letter-spacing: .38px;
          font-weight: 600;
          text-transform: uppercase;
        }
      }

      .rooms_breakdown_wrapper {
        padding: 20px 25px;

        .room_selected_element {
          .room_name {
            font-size: 16px;
            font-weight: 600;
          }

          .room_details_wrapper {
            border-left: 1px solid #2BB0A3;
            padding-left: 13.5px;
            line-height: 22px;
            margin-top: 10px;

            .booking_info_element, .rate_name {
              margin-bottom: 0;
              font-size: 14px;
            }

            .booking_info_element {
              &.modified_highlight {
                display: inline-block;
                position: relative;
                background-color: white;
                outline: 3px solid white;
                z-index: 101;
              }

              .cross_out {
                margin-left: 5px;
                text-decoration: line-through;
                position: relative;
                font-size: 11px;
                color: #b7b7b7;
              }
            }

            .rate_name {
              cursor: pointer;
              text-decoration: underline;
              color: #2BB0A3;
            }
          }

          .room_price {
            display: flex;
            flex-wrap: nowrap;
            justify-content: flex-end;
            align-items: center;
            font-size: 20px;

            .price {
              margin-right: 5px;
              font-weight: 600;
            }
          }
        }

        .room_selected_element:not(.hide) + .room_selected_element {
          margin-top: 20px;
        }
      }

      .services_breakdown_wrapper,
      .advanced_services_breakdown_wrapper,
      .include_services_breakdown_wrapper {
        padding: 20px 25px;

        .service_selected_element {
          .service_name {
            font-size: 16px;
            font-weight: 600;
          }

          .service_details_wrapper {
            border-left: 1px solid #2BB0A3;
            padding-left: 13.5px;
            line-height: 22px;
            margin-top: 10px;

            .detail_text {
              margin-bottom: 0;
              font-size: 14px;
            }
          }

          .service_price {
            display: flex;
            flex-wrap: nowrap;
            justify-content: flex-end;
            align-items: center;
            font-size: 20px;

            .price {
              margin-right: 5px;
              font-weight: 600;
            }
          }
        }

        .service_selected_element:not(.hide) + .service_selected_element {
          margin-top: 20px;
        }
      }

      .additional_service_element.upgrade_booking_3_element {
        margin: 10px;
        width: calc(100% - 20px);
      }

      .agency-booking3-wrapper.shopping_cart {
        padding: 0 20px;
      }

      .cart_promocode_wrapper {
        display: none;
        padding: 0 25px;
        font-size: 18px;
        color: #333;
        text-align: right;

        .promocode_message {
          margin-right: 5px;
        }

        .promocode_value {
          text-transform: uppercase;
        }
      }

      .total_booking_wrapper {
        background-color: transparent;
        position: relative;
        height: auto;
        bottom: auto;
        left: auto;
        right: auto;
        padding: 31px 0 0;
        margin: 20px 25px 0;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        //border-top: 1px solid red;

        .total_label, .total_price {
          color: #333;
        }

        .total_price {
          position: relative;
          right: auto;
          top: auto;
          transform: none;
          margin-left: 15px;
          font-size: 30px;

          .price_without_bono_discount {
            text-decoration: line-through;
            font-size: 14px;
            text-align: right;
            padding-bottom: 5px;
          }

          .currencyValue {
            margin-right: 5px;
          }
        }
      }

      .accomodation_tax_info_wrapper {
        .booking-3-price--partial {
          .currencyValue {
            margin-right: 5px;
          }
        }
      }
    }
  }

  .main_content_wrapper.step_2 .reservation_summary .option_selected {
    padding: 0;

    .total_price_wrapper.content_title {
      padding: 15px;
      display: flex;
      justify-content: space-between;
      flex-wrap: nowrap;

      .title_wrapper {
        .title {
          margin: 0;
        }

        .tax_info {
          font-size: 10px;
          color: #333;
        }
      }

      .price {
        position: relative;
        top: auto;
        bottom: auto;
        right: auto;
      }
    }

    .cart_promocode_wrapper {
      display: none;
      padding: 0 15px;
      font-size: 18px;
      color: #333;
      text-align: right;

      .promocode_message {
        margin-right: 5px;
      }

      .promocode_value {
        text-transform: uppercase;
      }
    }

    .taxes_included_info {
      margin: 0 15px;

      .price {
        position: relative;
        width: auto;
        color: black;
        font-size: 13px;
        font-weight: lighter;
      }
    }
  }

  .site-main {
    #wizard {
      &.step_2 {
        #step-3 {
          #personal-details-form {
            .booking_details_prices_wrapper:not(.b3_alt_summary) {
              padding: 26px 0;

              * {
                box-sizing: border-box;
              }

              .booking-box__title {
                margin-top: 0;
                padding: 0 25px;
              }

              .booking_details_wrapper {
                padding: 0;

                .total_booking_wrapper {
                  background-color: transparent;
                  position: relative;
                  height: auto;
                  bottom: auto;
                  left: auto;
                  right: auto;
                  padding: 31px 0 0;
                  margin: 20px 25px 0;
                  display: flex;
                  align-items: center;
                  justify-content: flex-end;
                  //border-top: 1px solid red;

                  .total_label, .total_price {
                    color: #333;
                  }

                  .total_price {
                    position: relative;
                    right: auto;
                    top: auto;
                    transform: none;
                    margin-left: 15px;
                    font-size: 30px;
                  }
                }

                .shopping_cart_datails_taxes_v2_wrapper {
                  .total_booking_wrapper {
                    &.tax_broken_down {
                      display: block;
                      background: #5b5b5b;
                      height: auto;
                      position: unset;
                      width: 100%;
                      margin: 0px;
                      margin-top: 20px;
                      padding: 10px 25px;

                      * {
                        color: white;
                      }
                    }
                  }

                  .taxes_included_info {
                    display: flex;
                    flex-direction: column;
                    align-items: flex-end;
                    margin: 0px 20px;
                    background: #F4F4F4;
                    border-radius: 10px;
                    padding: 10px;

                    .subtotal {
                      display: flex;
                      margin-bottom: 10px;

                      .subtotal_label {
                        margin-right: 20px;
                      }

                      .price {
                        font-weight: bold;

                        .monedaConv {
                          margin-right: 5px;
                        }
                      }
                    }

                    .taxes_info {
                      display: flex;

                      .taxes_label {
                        margin-right: 25px;
                      }

                      .price {
                        font-weight: bold;

                        .monedaConv {
                          margin-right: 5px;
                        }
                      }
                    }
                  }
                }
              }

              .custom_taxes_details_wrapper {
                margin: 20px 25px;
              }

              .conditions_wrapper {
                .custom_message_rate, .changes_modification_policies, .cancellation_policies {
                  padding-left: 25px;
                  padding-right: 25px;
                }
              }

              .booking-button {
                margin: 18px auto 0;
                border-radius: 0;
                width: calc(100% - 50px);
                float: none;
                padding: 23px 10px;
                font-size: 16px;
                text-transform: uppercase;
                display: block;

                span {
                  position: relative;
                  top: auto;
                  left: auto;
                  transform: none;
                }
              }
            }
          }
        }
      }
    }
  }

  .fancybox-wrap.rates_conditions_popup {
    * {
      box-sizing: border-box;
    }

    .fancybox-inner {
      max-width: 700px;
    }

    .rate_conditions_hidden_wrapper {

      .rate_title {
        background-color: #333;
        padding: 20px;
        font-size: 22px;
        font-weight: lighter;
        color: white;
      }

      .rate_condition_description {
        padding: 20px;
      }
    }
  }

  &.booking_process_mobile_v1 {
    &.shopping_cart_active {
      padding-bottom: 80px; // When shopping cart popup is active it covers some part of the footer
    }

    .booking_details_wrapper {
      .hotel_name_small, .hotel_name {
        display: none;
      }

      .custom_taxes_details_wrapper {
        padding: 15px;

        .main_info {
          display: inline-flex;
          gap: 10px;

          .tax_name {
            font-weight: 600;
          }
        }

        .total_taxes_label {
          font-weight: 600;
        }
      }
    }

    .fancybox-wrap.rates_conditions_popup {
      bottom: 0 !important;
      top: auto !important;
      left: 0 !important;
      max-width: 100%;

      .fancybox-inner {
        max-width: 100%;
      }
    }

    .conditions_wrapper {
      .cancellation_policies, .changes_modification_policies {
        padding: 10px 25px
      }
    }
  }

  .floating_shopping_cart_summary.v2 {
    &.hidden_shopping_cart {
      display: none;
    }

    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    box-shadow: 0 -3px 6px #00000029;
    background-color: #fff;
    padding: 12px calc(50% - 570px);
    display: flex;
    flex-wrap: nowrap;
    align-items: center;
    justify-content: flex-end;
    line-height: 1;
    z-index: 10;

    .total_cart_wrapper {
      display: flex;
      flex-wrap: nowrap;
      align-items: center;

      .cart_icon_wrapper {
        position: relative;

        i {
          font-size: 30px;
        }

        .number_element {
          position: absolute;
          top: -50%;
          right: -50%;
          background-color: red;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 12px;
          width: 24px;
          height: 24px;
          color: white;
        }
      }

      .total_label {
        margin: 0 15px 0 25px;

        small {
          display: block;
          font-size: 12px;
          line-height: 16px;
        }
      }

      .total_price {
        font-size: 30px;

        .value_elem {
          font-weight: 600;
          margin-right: 5px;
        }
      }

      .booking_button {
        color: white;
        text-transform: uppercase;
        padding: 20px 43px;
        margin-left: 30px;
        font-size: 16px;
        cursor: pointer;
      }
    }
  }

  #main_modal {
    z-index: 110;
  }

  .promotion_applied.shopping_cart {
    padding: 20px;
    background: lightgray;
    position: relative;
    background: var(--booking_color_1);
    color: white;

    background: var(--booking_color_1);
    background: -moz-linear-gradient(156deg, var(--booking_color_1) 0%, var(--booking_color_1_-100) 100%);
    background: -webkit-linear-gradient(156deg, var(--booking_color_1) 0%, var(--booking_color_1_-100) 100%);
    background: linear-gradient(156deg, var(--booking_color_1) 0%, var(--booking_color_1_-100) 100%);
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr="var(--booking_color_1)", endColorstr="var(--booking_color_1_-100)", GradientType=1);

    .price_without_promotion {
      width: 100%;
      display: table;

      .label {
        float: left;
      }

      .price_no_promo {
        float: right;
        text-decoration: line-through;
      }
    }

    .price_with_promotion {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .price_promo {
        font-size: 22px;
        font-weight: 600;
        color: var(--booking_color_3);
      }

      span.with_promotion {
        text-transform: uppercase;
        font-size: 16px;
        padding-top: 6px;
        display: block;
      }

      .taxes_label {
        display: block;
      }
    }

    .percentage_tag {
      position: absolute;
      font-size: 11px;
      right: -14px;
      padding: 1px 6px;
      top: 18px;
      background: var(--booking_color_3);
    }
  }
}
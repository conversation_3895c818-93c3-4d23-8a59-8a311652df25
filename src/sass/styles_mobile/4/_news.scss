.news_wrapper {
    width: 100%;
    box-sizing: border-box;
    padding: 20px;
    .entry_widget {
        margin-bottom: 30px;
        border-radius: 5px;
        border: 1px solid #DDD;
        .image {
            img {
                border-radius: 5px 5px 0 0;
                width: 100%;
            }
        }
        .tags {
            position: absolute;
            display: inline-block;
            padding: 5px;
            background-color: rgba(0,0,0,0.6);
            color: white;
        }
        .title {
            text-align: left;
            font-weight: bold;
            padding: 0 10px 10px;
            font-size:150%;
            a {
                color: #4b4b4b;
            }
        }
        .date {
            text-align: left;
            padding: 0 10px;
            color: #CCC;
            font-size: 70%;
            i {
                display: inline-block;
                vertical-align: middle;
                font-size: 150%;
                margin-right: 5px;
            }
            span {
                display: inline-block;
                vertical-align: middle;
            }
        }
        .content {
            text-align: left;
            padding: 10px;
            font-size: 80%;
        }
        .read_more {
            position: relative;
            top:-25px;
            display: inline-block;
            background-color: $corporate_2;
            border-radius: 5px;
            color: white;
            padding: 10px;
            text-align: center;
            text-transform: uppercase;
            i {
              font-size: 20px;
              margin: 0 10px 0 0;
            }
        }
    }
}

.entry_wrapper {
    .sectionImages {
        position: relative;
        width: 100%;
        height: 300px;
        overflow: hidden;
        box-sizing: border-box;
        img {
            @inclide center_xy;
            width: 100%;
        }
        .background_overlay {
            background-color: rgba(0,0,0,0.3);
            position: absolute;
            top:0;left:0;right:0;bottom:0;
        }
        h1 {
            position: absolute;
            bottom:20px;
            left: 20px;
            color: white;
            margin: 0;
            font-weight: bold;
            font-size: 30px;
            text-shadow: 0 0 0 rgba(0,0,0,0.3);
            text-align: left;
        }
    }
    .date {
        text-align: left;
        padding: 10px 20px;
        color: #CCC;
        font-size: 70%;
        i {
            display: inline-block;
            vertical-align: middle;
            font-size: 150%;
            margin-right: 5px;
        }
        span {
            display: inline-block;
            vertical-align: middle;
        }
    }
    .sectionContent {
        padding: 0 20px;
        font-size: 80%;
    }
}
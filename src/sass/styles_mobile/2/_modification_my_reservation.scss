.modify_reservation_widget {
  border: 1px solid lightgray;
  padding: 15px 0;
  font-size: 15px;

  #booking_engine_title {
    display: none;
  }

  #contador_noches {
    display: none;
  }

  .colocar_fechas {
    font-size: 15px;

    label {
      margin-bottom: 8px;
      display: block;
    }

    input {
      text-align: center !important;
    }
  }

  #fecha_entrada {
    float: left;
    width: 48%;
  }

  #fecha_salida {
    float: right;
    width: 48%;
  }

  fieldset {
    border: 0;
  }

  #contenedor_habitaciones {
    width: 100%;
    display: table;
    box-sizing: border-box;
    padding: 20px;
    position: relative;

    &:before, &:after {
      content: '';
      position: absolute;
      top: 0;
      left: 20%;
      right: 20%;
      border-top: 1px solid lightgray;
    }

    &:after {
      top: auto;
      bottom: 0;
    }

    label {
      float: left;
      margin-top: 10px;
    }

    select {
      float: right;
      padding: 10px 50px;
      vertical-align: middle;
      text-align: right;
      background-color: #eaeaea;
      border: 0;
      border-radius: 5px;
    }
  }

  #contenedor_opciones {
    width: 100%;
    display: table;
    box-sizing: border-box;
    padding: 20px;
    position: relative;

    & > div[style='display: block;'] {
      //Bad trick to give styles
      display: table!important;
      width: 100%;
      padding-top: 15px;
    }


    .numero_habitacion {
      text-decoration: underline;
      margin-bottom: 10px;
      display: block;
    }

    .numero_personas {
      float: left;
      width: 33%;
      text-align: left;
      position: relative;

      select {
        display: block;
        padding: 10px 0;
        vertical-align: middle;
        text-align: right;
        background-color: #eaeaea;
        border: 0;
        border-radius: 5px;
        box-sizing: border-box;
        width: 90%;
      }

      #info_ninos {
        position: absolute;
        top: 4px;
        font-size: 10px;
        left: 50%;
      }
    }
  }

  #envio {
    padding-top: 20px;
    position: relative;

    &:before {
      content: '';
      position: absolute;
      top: 0;
      left: 20%;
      right: 20%;
      border-top: 1px solid lightgray;
    }
  }

  input[type=text] {
    padding: 15px 30px !important;
  }


  #search-button {
    display: block;
    padding: 10px 0;
    box-sizing: border-box;
    font-size: 22px;
    text-transform: uppercase;
    width: 100%;
    border-radius: 5px;
    margin: auto;
    background-color: #0A80A8;
    color: white;
    margin-bottom: 10px;
    border: 0;
  }
}

from utils.flask_requests import response_utils
from utils.web.web_seeker_utils import WebSeekerHandler

applicationId = "secure-booking30"
template_handler = None


class PuntazoRedirection(WebSeekerHandler):
    def get(self, *args):
        response_utils.set_response_redirection('https://www.hotelelpuntazo.com', permanent=True)
        return ""


extra_routing = {
    '.*localhost.*': {
        '.*': WebSeekerHandler
    },
    '.*marissal.*': {
        '.*': WebSeekerHandler
    },
    '.*vasco.*': {
        '.*': WebSeekerHandler
    },
    '.*pineda.*': {
        '.*': WebSeekerHandler
    },
    '.*loule.*': {
        '.*': WebSeekerHandler
    },
    '.*tracos.*': {
        '.*': WebSeekerHandler
    },
    '.*puntadelcantal.*': {
        '.*': PuntazoRedirection
    },
    '.*puntazo.*': {
        '.*': WebSeekerHandler
    },
    '.*landmar.*': {
        '.*': WebSeekerHandler
    },
    '.*': {
        '.*': WebSeekerHandler
    }
}

flexible_server_type = True
automatic_flexible_scaling = True
min_automatic_flex_instances = 2
max_automatic_flex_instances = 5
server_cpu = 1
server_memory = 4

templates = []

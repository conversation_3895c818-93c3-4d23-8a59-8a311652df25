@media screen and (max-width: 767px) {
  .package-element-wrapper.hostel-package {
    &.contracted {
      .hostel-packages-selector {
        display: none;
      }
    }

    .hostel-packages-selector .hostel-packages-selector__add__button {
      width: 100%;
      box-sizing: border-box;
      text-align: center;
    }

    .add-to-cart-wrapper {
      align-items: center;
      justify-content: flex-start;
      display: grid;
      grid-template-columns: auto auto 1fr;

      .total-price-hostel-package {
        width: min-content;
        margin-right: 10px;
      }

      .price-wrapper {
        margin-left: 0;

        .new-total-price {
          font-size: 22px;
          font-weight: bold;
        }
      }

      .add-to-cart-button-packages-hostel {
        align-self: flex-end;
        text-align: center;
      }
    }

    .hostel-packages-selector .hostel-packages-selector__item {
      .first-row, .second-row, .third-row {
        padding: 15px 0px;
      }

      .second-row {
        display: flex;
        flex-direction: column;

        & > * {
          border: 0;
        }

        .occupancy-room-package-hostel {
          padding-right: 12px!important;

          .occupancy-value-selector {
            display: flex;
            justify-content: space-between;

            i {
              font-size: 17px;
            }
          }
        }
      }

      .third-row {
        .config-package-room-wrapper {
          display: flex !important;
          flex-direction: column;
          position: relative;

          & > * {
            border-right: 0 !important;
          }

          .trash-wrapper {
            position: absolute;
            right: 0;
            top: 18px;
          }
        }

        &.bed-row {
          border-top: 1px solid lightgray;
          display: flex !important;
          flex-direction: column;

          .amount-selector, .date-selector {
            border: 1px solid lightgray;
            width: 100%;
            box-sizing: border-box;
            padding: 20px 0 10px 16px;
            border-radius: 7px;
            background-size: 19px;
            font-weight: lighter;
            font-size: 14px;
            position: relative;

            select {
              background-size: 19px;
              width: 100%;
              font-weight: 400;
            }

            .label-package-hostel-mini-element {
              position: absolute;
              background: white;
              top: -6px;
              left: 11px;
              padding: 0 6px;
              font-size: 10px;
            }
          }
        }

        .amount-selector {
          padding: 14px 0 4px 16px;
          padding-right: 2px!important;

          select {
            width: 100%;
            background-size: 18px;
          }
        }

        .date-selector {
          padding-right: 10px;
          padding-bottom: 0;

          .selected-dates-wrapper {
            font-weight: 400;
            display: flex;
            width: 100%;
            justify-content: space-between;
          }
        }

        .occupancy-selector {
          padding-right: 12px!important;

          .occupancy-room-package-hostel {
            .occupancy-value-selector {
              display: flex;
              justify-content: space-between;

              i {
                font-size: 17px;
              }
            }
          }
        }
      }
    }
  }
}
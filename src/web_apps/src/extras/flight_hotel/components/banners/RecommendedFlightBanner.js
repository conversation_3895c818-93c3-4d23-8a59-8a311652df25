import './recommended_flight_banner.scss';
import {useTranslation} from "../../../../utils/general_providers/translations_provider";
import {Price} from "../../../../apps/booking1/components/price_default";
import {getVisiblePrice} from "./recommendedFlightUtils";

const IndividualFlightData = ({flightInfo}) => {
    return(
        <div className="individual_flight_wrapper_data">
            <i className={`fa-regular fa-plane-${flightInfo.icon_class}`}></i>
            <span className="flight_itinerary">{flightInfo.flight_segment_info}</span>
        </div>
    )
}
export const RecommendedFlightBanner = ({bannerData}) => {
    const translations = useTranslation();
    return(
        <div className={`recommended_flight_banner ${bannerData.is_mobile ? 'mobile_banner' : ''}`}>
            <div className="flight_data_wrapper">
                <div className="banner_title">{translations['T_recommended_flight']}</div>
                <div className="flight_data">
                    <IndividualFlightData flightInfo={bannerData.recommended_flight_info.departure_flight}/>
                    <IndividualFlightData flightInfo={bannerData.recommended_flight_info.arrival_flight}/>
                    <div className="flight_next_step_message">{translations['T_flight_next_step']}</div>
                </div>
            </div>
            <div className="price_wrapper">
                <div className="price_info">
                    <div className="current_price">
                        <Price price={getVisiblePrice(bannerData)}/>
                    </div>
                </div>
                <div className="price_message">{translations['T_price_includes_flight_hotel']}</div>
            </div>
        </div>
    )
}



import {useTranslation} from "../../../../utils/general_providers/translations_provider";
import {useState} from "react";
import {useFiltersContext} from "../../providers/filtersProvider";

const buildDepartureTimeFilter = (currentSelected) => {
    const translations = useTranslation();
    return [
        {
            filterName: translations['T_morning'],
            filterIcon: 'fa-regular fa-sun-bright',
            value: 'morning',
            selected: currentSelected.includes('morning')
        },
        {
            filterName: translations['T_evening'],
            filterIcon: 'fa-light fa-sunset',
            value: 'evening',
            selected: currentSelected.includes('evening')
        },
        {
            filterName: translations['T_night'],
            filterIcon: 'fa-light fa-moon-stars',
            value: 'night',
            selected: currentSelected.includes('night')
        }
    ];
};

const OptionCard = ({iconClass, cardText, filterValue, selected, filterAction}) => {
    const [optionSelected, setOptionSelected] = useState(selected);

    const handleOptionSelection = (value) => {
        filterAction(value, !optionSelected);
        setOptionSelected(!optionSelected);
    };

    return(
        <div className={`option-card ${optionSelected ? 'selected': ''}`} onClick={()=>handleOptionSelection(filterValue)}>
            <i className={`card-icon ${iconClass}`}></i>
            <div className={'card-text'} dangerouslySetInnerHTML={{__html: cardText}}>{}</div>
        </div>
    )
}
export const HourDepartureFilter = ({filterTitle, filterType}) => {
    const filterContext = useFiltersContext();
    let currentSelected = {...filterContext.selectedFilters};

    const handleChangeFilter = (value, isActive) => {
        if(isActive){
            currentSelected[filterType].push(value);
        } else{
            currentSelected[filterType] = currentSelected[filterType].filter((elem) => elem !== value);
        }
        filterContext.setSelectedFilters(currentSelected);
    }
    const options = buildDepartureTimeFilter(currentSelected[filterType]);

    return(
        <div className={'filter-category'}>
            <div className={'filter-title'}>{filterTitle}</div>
            <div className={'filter-options card-container'}>
                {
                    options.map((option, index) =>
                        <OptionCard
                            key={index}
                            iconClass={option.filterIcon}
                            cardText={option.filterName}
                            filterValue={option.value}
                            selected={option.selected}
                            filterAction={handleChangeFilter}
                        />
                    )
                }
            </div>
        </div>
    )
}
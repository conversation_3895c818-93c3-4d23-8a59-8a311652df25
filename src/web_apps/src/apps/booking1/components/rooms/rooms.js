import { RatesList } from '../rates/rates';
import './rooms.scss';
import './rooms_mobile.scss';
import { Modal } from '../../../../utils/general_components/modals/modals_utils';
import { useState, useEffect, useRef } from 'react';
import { RoomMorePopup } from './room_more_popup';
import { useTranslation } from '../../../../utils/general_providers/translations_provider';
import { useBooking1Context } from '../../providers/general_b1_provider';
import { IconsBadges, RoomIconsCarousel, RoomTag } from './extras_rooms';
import { Price } from "../price_default";
import { TourVirtual } from "./tour_virtual";
import { hideRoom } from "./rooms_utils";


export const RoomElement = props => {
    const general_booking1 = useBooking1Context();

    const room_is_visible = !hideRoom(props.room_data);
    const room_name_color = props.room_data.rooms_list[0].room_name_color;

    const [ratesVisible, setRatesVisible] = useState(general_booking1.callcenter_data?.useLiteVersion || !general_booking1.rates_dropdown);
    const [showTourVirtual, setShowTourVirtual] = useState(false);
    const toggleRatesVisibility = () => setRatesVisible(!ratesVisible);

    return (
        <>
            {room_is_visible && (
                <div className={`room-element-wrapper ${props.room_data.rooms_list[0].specifically_searched_no_availability ? 'specifically-searched no-availability' : ''}`}>
                    <RoomInfo
                        room_data={props.room_data.rooms_list}
                        cheapest_regimen={props.room_data.cheapest_regimen}
                        cheapest_club_regimen={props.room_data.cheapest_club_regimen}
                        room_icons_carousel={props.room_icons_carousel}
                        toggleVisibilityCallback={toggleRatesVisibility}
                        ratesVisible={ratesVisible}
                        showTourVirtual={setShowTourVirtual}
                        room_name_color={room_name_color}
                    />
                    <RatesList
                        rates_list={props.room_data.rates_list}
                        room_key={props.room_data.rooms_list[0].roomKey}
                        ratesVisible={ratesVisible}
                    />
                    { showTourVirtual &&
                        <TourVirtual
                            tour_virtual={props.room_data.rooms_list[0].tour_virtual.content}
                            showTourVirtual={setShowTourVirtual}
                        />
                    }
                </div>
            )}
        </>
    );
};

const RoomInfo = props => {
    const [visibleSeeMore, setVisibleSeeMore] = useState(false);
    const [visibleGallery, setVisibleGallery] = useState(false);
    const translations = useTranslation();
    const general_booking1 = useBooking1Context();
    const club_context = general_booking1.extra_data.club_context;
    const {is_mobile} = general_booking1
    const {room_data: room_info} = props

    const roomNameRef = useRef(null);
    const roomDescriptionRef = useRef(null);

    const icons_render_condition = room_info && room_info[0].services_pictures && room_info[0].services_pictures.length > 0;
    let mobile_featured_icons = icons_render_condition && is_mobile
        ? room_info[0].services_pictures.filter(icon => icon.mobile_featured_icon)
        : [];

    if (is_mobile && icons_render_condition && room_info[0].custom_mobile_featured_services) {
        mobile_featured_icons = room_info[0].custom_mobile_featured_services
    }

    let room_name = props.room_data.map(room => room.roomName);

    if (props.room_data.length > 1) {
        room_name = room_name.join(' + ');
    }

    let room_description = props.room_data.map(room => room.roomDescription);
    let picture_overlay_label = props.room_data[0]['picture_overlay_text'];

    const room_extra_info = props.room_data.find(room => room.extra_info)?.extra_info;

    const just_booking = props.room_data.some(room => room.just_booking_room);
    const very_asked = props.room_data.some(room => room.very_asked_room);
    const onrequest_nodispo = props.room_data.some(room => room.onrequest_no_dispo);
    const specificallySearchedWithoutAvailability = props.room_data.some(room => room.specifically_searched_no_availability);

    const cheapest_regimen = props.cheapest_regimen;
    const cheapest_regimen_results = cheapest_regimen?.roomResults;
    const use_lock_prices_for_price_since = club_context?.club_info && club_context?.club_info.use_lock_prices_for_price_since;
    const lock_board_results = cheapest_regimen?.lock_board?.roomResults;
    const cheapest_club_regimen_results = props.cheapest_club_regimen?.roomResults;

    const roomResults =  lock_board_results && use_lock_prices_for_price_since ? lock_board_results : (cheapest_club_regimen_results || cheapest_regimen_results);

    const totalOrDailyPrice = (totalColumn, dailyColumn, text=null) => {
        const price_since_custom_message = club_context?.club_info?.price_since_custom_message;
        return general_booking1.noColumnNightPrice || props.room_data[0].has_lock_boards && !club_context.club_toggle_night_price ? totalColumn : text && price_since_custom_message ? price_since_custom_message : dailyColumn;
    }

    const adjustDescriptionLineClamp = () => {
        if (!roomNameRef.current || !roomDescriptionRef.current || is_mobile) return;

        const roomNameElement = roomNameRef.current;
        const roomDescriptionElement = roomDescriptionRef.current;

        const lineHeight = parseFloat(window.getComputedStyle(roomNameElement).lineHeight);
        const actualHeight = roomNameElement.scrollHeight;

        const isMultiLine = actualHeight > (lineHeight * 1.5);

        roomDescriptionElement.classList.add(isMultiLine ? 'room-description-reduced' : 'room-description-normal');
        roomDescriptionElement.classList.remove(isMultiLine ? 'room-description-normal': 'room-description-reduced');
    };

    useEffect(() => {
        if (is_mobile) return;

        const timeoutId = setTimeout(adjustDescriptionLineClamp, 100);

        return () => {
            clearTimeout(timeoutId);
        };
    }, [room_name]);

    return (
        <div className={'room-info-wrapper'}>
            {!general_booking1.callcenter_data?.useLiteVersion && (
                <>
                    {(just_booking || very_asked || onrequest_nodispo || specificallySearchedWithoutAvailability) && (
                        <div className="labels-wrapper">
                            {just_booking && !onrequest_nodispo && (
                                <span className="just-booking">{ translations['T_just_booking'] }</span>
                            )}
                            {very_asked && !onrequest_nodispo && (
                                <span className="very-asked">{ translations['T_very_asked'] }</span>
                            )}
                            {(onrequest_nodispo || specificallySearchedWithoutAvailability) && (
                                <span className={onrequest_nodispo ? 'on-request-nodispo' : 'specifically-searched-no-availability'}>
                                    { translations['T_no_disponible'] }
                                </span>
                            )}
                        </div>
                    )}

                    <div className="room-image-wrapper" onClick={() => setVisibleGallery(true)}>
                        { picture_overlay_label && <RoomPictureLabel roomProperties={props.room_data[0]}/> }
                        { props.room_data[0].tour_virtual && (
                            <div className={'tour-virtual-wrapper'}>
                                <div className="tour-virtual-icon" onClick={(e) => {e.stopPropagation(); props.showTourVirtual(true)}}>
                                    <i className="icon-360"></i>
                                    <span className="hidden-tooltip">
                                        {props.room_data[0].tour_virtual.button}
                                    </span>
                                </div>
                            </div>
                        )}
                        <img src={props.room_data[0].roomPicture} alt={room_name}/>
                        <div className='icon-gallery'>
                            <i className="fa-light fa-magnifying-glass"></i>
                        </div>
                    </div>
                </>
            )}

            <div className={"room-content" + (general_booking1.rates_dropdown ? ' with_rates_dropdown' : '')}>
                <div className="top-room-wrapper">
                    <div className={'top-room'}>
                        <div className={'room-name'} style={{ color: props.room_name_color }} ref={roomNameRef}>
                            <span dangerouslySetInnerHTML={{__html: room_name}}></span>
                            {!general_booking1.callcenter_data?.useLiteVersion && general_booking1.rates_dropdown && (
                                <a className="see-more-plus-room" onClick={() => setVisibleSeeMore(true)}>
                                    <span className="see-more-text">
                                        {
                                        !general_booking1.is_mobile ?
                                            translations['T_ver_mas'] :
                                            translations['T_ver_detalles']
                                        }
                                    </span>
                                    <i className="fal fa-plus"></i>
                                </a>
                            )}
                            {room_extra_info && (Object.keys(room_extra_info).length > 0) && <RoomTag extraInfo={room_extra_info}/>}
                        </div>
                        {!general_booking1.callcenter_data?.useLiteVersion && (
                            <>
                                {is_mobile && mobile_featured_icons.length > 0 && <IconsBadges icons={mobile_featured_icons}/>}
                                {!is_mobile && (
                                    <div className="room-description"
                                        ref={roomDescriptionRef}
                                        dangerouslySetInnerHTML={{__html: room_description}}></div>
                                )}
                            </>
                        )}
                    </div>

                    {!general_booking1.callcenter_data?.useLiteVersion && general_booking1.rates_dropdown && (
                        <div className={'room_from_price_wrapper ' + (props.room_data[0].room_has_loggin && !props.room_data[0].has_lock_boards ? 'club_active' : '')}>
                            <div className="room_from_price">
                                <div className="old_price">
                                    <span className="from_price_label">{translations['T_desde']}</span>
                                    {roomResults && roomResults.hasPromotion && (
                                        <span className="old_price_value">
                                            <Price
                                                price={totalOrDailyPrice(roomResults.total, roomResults.perDay)}/>
                                        </span>
                                    )}
                                </div>
                                <div className="room_from_price_value">
                                    {roomResults && (
                                        roomResults.promotionTotal && roomResults.promotionPerDay ? (
                                            <Price
                                                price={totalOrDailyPrice(roomResults.promotionTotal, roomResults.promotionPerDay)}/>
                                        ) : (
                                            <Price
                                                price={totalOrDailyPrice(roomResults.total, roomResults.perDay)}/>
                                        )
                                    )}
                                </div>
                                <div
                                  className="room_from_price_label"
                                  dangerouslySetInnerHTML={{
                                    __html: totalOrDailyPrice(
                                      translations['T_precio_total'],
                                      translations['T_precio_por_noche'],
                                      true
                                    ),
                                  }}
                                ></div>
                            </div>

                            <a className={"prices_dropdown_toggle" + (props.ratesVisible ? ' active' : '')}
                               onClick={props.toggleVisibilityCallback}>
                                <span className="prices_dropdown_text">{translations['T_ver_mas_precios']}</span>
                                <i className="fal fa-angle-down"></i>
                            </a>
                        </div>
                    )}
                </div>
                {!general_booking1.callcenter_data?.useLiteVersion && (
                    <>
                        <div className="bottom-room">
                            {room_description.join('') && !general_booking1.rates_dropdown && (
                                <a className={'see-more-button-room'} onClick={() => setVisibleSeeMore(true)}>
                                    {translations['T_ver_mas']}
                                    <i className="fa-light fa-circle-plus"></i>
                                </a>
                            )}
                            {!is_mobile && icons_render_condition && (
                                    <RoomIconsCarousel icons={props.room_data[0].services_pictures}
                                                    room_icons_carousel={props.room_icons_carousel}/>
                                )}
                        </div>

                        <Modal
                            custom_class={'room-more-popup'}
                            show={visibleSeeMore}
                            close_callback={() => setVisibleSeeMore(false)}>
                            {visibleSeeMore ? <RoomMorePopup room_data={props.room_data}
                                                            room_icons_carousel={props.room_icons_carousel}/> : null}
                        </Modal>

                        <Modal
                            custom_class={'room-gallery-popup'}
                            show={visibleGallery}
                            close_callback={() => setVisibleGallery(false)}>
                            {visibleGallery ? <RoomMorePopup room_data={props.room_data} only_gallery={!is_mobile}/> : null}
                        </Modal>
                    </>
                )}
            </div>
        </div>
    );
};

function RoomPictureLabel({roomProperties}) {
    const {picture_overlay_background, picture_overlay_text_color, picture_overlay_text} = roomProperties;

    return (
        <div className="picture-label-wrapper"
            style={{
                background: picture_overlay_background ?? '#8a8a8a',
                color: picture_overlay_text_color ?? "white"
            }}>
            <span
                className="picture-label">
                {picture_overlay_text}
            </span>
        </div>
    );
}

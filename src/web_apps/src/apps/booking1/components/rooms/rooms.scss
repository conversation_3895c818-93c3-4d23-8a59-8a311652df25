@import '../../../../utils/general_styles/mixins';
@import '../../variables.scss';

.rooms-list {
  width: 1140px;
  margin: auto;
}

.room-element-wrapper {
  margin-bottom: 50px;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);

  &.specifically-searched.no-availability {
    background: rgba(0, 0, 0, 0.15);

    .room-image-wrapper {
      filter: grayscale(90%);
    }
  }

  .room-info-wrapper {
    display: grid;
    grid-template-columns: 28% auto;
    margin-bottom: 20px;
    justify-items: stretch;
    position: relative;

    &:has(.with_rates_dropdown) {
      .labels-wrapper {
        left: 0;
        bottom: 0;
        right: auto;
      }
    }

    .labels-wrapper {
      position: absolute;
      z-index: 1;
      left: auto;
      bottom: auto;
      right: 0;
      text-align: left;

      .just-booking, .very-asked, .on-request-nodispo, .specifically-searched-no-availability {
        padding: 5px 8px;
        display: inline-block;
        width: fit-content;
        font-size: 13px;
        color: white;
        font-weight: 500;
        text-transform: uppercase;
      }

      .just-booking {
        background: var(--booking_color_1_100);
      }

      .very-asked {
        background: var(--booking_color_1_-80);
      }

      .on-request-nodispo {
        background: #FFCA19;
        color: black;
      }

      .specifically-searched-no-availability {
        background: #FF464C;
      }
    }

    .room-image-wrapper {
      height: 200px;
      overflow: hidden;
      display: inline-block;
      position: relative;
      cursor: pointer;

      .tour-virtual-wrapper {
        position: absolute;
        left: -42px;

        .tour-virtual-icon {
          position: relative;
          font-size: 27px;
          color: white;
          border-radius: 5px 0 0 5px;
          text-align: center;
          margin-bottom: 7px;
          transition: opacity 0.4s;
          background: $blue_1;
          padding: 5px 10px 5px 5px;

          .hidden-tooltip {
            position: absolute;
            right: calc(100% + 10px);
            min-width: 110px;
            display: table;
            font-size: 10px;
            background: $blue_1;
            padding: 2px 0;
            border-radius: 5px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 0;
            transition: opacity 0.4s;
          }

          &:hover {
            .hidden-tooltip {
              opacity: 1;
            }
          }
        }
      }

      .picture-label-wrapper {
        position: absolute;
        left: 0;
        bottom: 0;
        padding: 10px 15px;
        color: white;
        opacity: .85;
        text-align: center;
        background: #8a8a8a;
        font-weight: 600;
        font-size: 14px;
        max-width: 45%;
        line-height: 18px;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .icon-gallery {
        position: absolute;
        bottom: 10px;
        right: 10px;
        color: white;
        padding: 5px 10px;
        border-radius: 5px;
        cursor: pointer;
        transition: opacity .5s;
        font-size: 28px;

        &:hover {
          opacity: 0.6;
        }
      }
    }

    .room-content {
      padding: 25px 25px 0;
      display: flex;
      flex-direction: column;
      justify-content: space-between;

      .top-room-wrapper {
        display: flex;

        .room-name {
          display: grid;
          grid-template-columns: max-content min-content auto;
          justify-items: end;
          align-items: center;
          font-size: 22px;
          font-weight: 600;
          margin-bottom: 10px;
          text-transform: uppercase;
          color: var(--booking_color_1_20);
        }

        .room-description {
          overflow: hidden;
          text-overflow: ellipsis;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          font-size: 16px;
          color: #333333;
          font-weight: lighter;
          line-height: 20px;

          &.room-description-normal {
            -webkit-line-clamp: 2;
          }

          &.room-description-reduced {
            -webkit-line-clamp: 2;
          }
        }

        .room_from_price_wrapper {
          text-align: center;

          .old_price {
            line-height: 22px;
            margin-bottom: 5px;

            span.from_price_label {
              margin-right: 5px;
              vertical-align: middle;
              font-weight: 700;
              font-size: 12px;
            }

            span.old_price_value {
              vertical-align: middle;
              font-weight: 400;
              font-size: 15px;
              text-decoration: line-through;
            }
          }

          .room_from_price_value {
            font-weight: 700;
            font-size: 26px;
            margin-bottom: 7px;
          }

          .room_from_price_label {
            font-weight: 400;
            font-size: 14px;
            margin-bottom: 10px;
            text-transform: lowercase;
          }

          a.prices_dropdown_toggle {
            background-color: var(--booking_color_1_20);;
            display: inline-flex;
            flex-wrap: nowrap;
            align-items: center;
            justify-content: center;
            box-sizing: border-box;
            height: 40px;
            min-width: 40px;
            padding: 0 13px;
            font-size: 14px;
            color: white;
            white-space: nowrap;
            cursor: pointer;
            -webkit-transition: all .5s;
            -moz-transition: all .5s;
            -ms-transition: all .5s;
            -o-transition: all .5s;
            transition: all .5s;

            span.prices_dropdown_text {
              -webkit-transition: all .5s;
              -moz-transition: all .5s;
              -ms-transition: all .5s;
              -o-transition: all .5s;
              transition: all .5s;
            }

            i.fal.fa-angle-down {
              font-size: 26px;
              transform-origin: 50% 45%;
              -webkit-transition: all .5s;
              -moz-transition: all .5s;
              -ms-transition: all .5s;
              -o-transition: all .5s;
              transition: all .5s;
            }

            &.active i {
              -webkit-transform: rotate(180deg);
              -moz-transform: rotate(180deg);
              -ms-transform: rotate(180deg);
              -o-transform: rotate(180deg);
              transform: rotate(180deg);
            }
          }
        }

        .see-more-plus-room {
          color: var(--text_color);
          font-weight: bold;

          .see-more-text {
            display: none;
          }
        }

        .room-extra-info {
          position: relative;
          display: flex;
          align-items: center;
          justify-content: space-evenly;
          gap: 10px;
          box-sizing: border-box;
          padding: 5px 10px;
          background-color: var(--booking_color_1_100);
          border-radius: 20px;
          font-size: 16px;
          color: var(--booking_color_1_20);
          font-family: inherit;
          cursor: pointer;

          i {
            color: white;
          }
        }

        .modal-wrapper.room-extra-info-popup {
          .room-extra-info-modal-wrapper {
            display: flex;
            gap: 5vw;

            .room-extra-info-popup-content-wrapper {
              position: relative;

              .room-extra-info-popup-title {
                margin-bottom: 10px;
                display: block;
                font-weight: 700;
                font-size: 22px;
              }

              .room-extra-info-popup-content {
                font-size: 14px;
                text-transform: none;
              }

              .room-extra-info-popup-extra-text {
                font-size: 50px;
                font-weight: bold;
                opacity: 0.2;
                letter-spacing: 1px;
                position: absolute;
                right: 0;
                bottom: 5px;
                display: block;
              }
            }
          }
        }

        &.with_rates_dropdown .top-room-wrapper .top-room {
          margin-right: 5em;
        }
      }

      .bottom-room {
        .see-more-button-room {
          display: flex;
          text-transform: uppercase;
          font-size: 13px;
          font-weight: bold;
          cursor: pointer;
          transition: opacity .5s;
          margin-bottom: 13px;
          align-items: center;

          i {
            margin-left: 6px;
          }

          &:hover {
            opacity: 0.6;
          }
        }

        .icons-rooms-carousel {
          display: grid;
          grid-template-columns: repeat(4, 1fr);
          align-items: center;
          justify-content: space-around;
          border-top: 1px solid;
          border-bottom: 1px solid;
          min-height: auto;
          padding: 2px 0;

          .service-icon-element {
            display: flex;
            align-items: center;
            width: 100%;
            justify-content: center;
            height: 30px;

            &:last-of-type {
              border-right: none;
            }

            img, i {
              margin-right: 10px;
            }

            .service-icon-name {
              font-size: 12px;
              color: #212529;
            }
          }
        }

        .swiper_icon_carousel {
          position: relative;
          width: 720px;
          padding: 10px 30px;

          .swiper {
            height: 40px;
          }

          .swiper-slide {
            text-align: center;
          }

          .slider_navs {
            position: relative;
            width: 100%;

            .slider_prev {
              position: absolute;
              top: -40px;
              left: -30px;
            }

            .slider_next {
              position: absolute;
              top: -40px;
              right: -20px;
            }
          }
        }
      }
    }
  }
}

.room-more-popup,
.room-gallery-popup {
  .close-modal-button {
    color: white !important;
  }

  .modal-content-wrapper {
    width: 800px;
  }
}

.room-more-popup-wrapper {
  background: white;

  .pictures-wrapper {
    position: relative;

    .picture-element {
      width: 100%;
      height: 300px;
      overflow: hidden;
      z-index: 1;

      &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 25%;
        height: 100%;
        background: linear-gradient(to right, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0));
        z-index: 1;
      }

      &:after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 25%;
        height: 100%;
        background: linear-gradient(to left, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0));
        z-index: 1;
      }

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
    }

    .swiper-pagination-bullet {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background-color: white;
      opacity: 50%;
      display: none;
      z-index: 2;

      &.swiper-pagination-bullet-active {
        position: relative;
        opacity: 100%;

        &::before {
          position: absolute;
          content: '';
          border: solid 1px white;
          top: -4px;
          bottom: -4px;
          left: -4px;
          right: -4px;
          border-radius: 50%;
        }
      }
    }

    .slider_navs {
      position: absolute;
      display: flex;
      justify-content: space-between;
      width: 100%;
      z-index: 2;
      color: white;
      font-size: 46px;
      @include center_y;

      .slider_nav {
        border-radius: 5px;
        width: 38px;
        height: 38px;
        position: relative;
        cursor: pointer;

        i {
          @include center_xy;
        }

        &.slider_next {
          right: 15px;
        }

        &.slider_prev {
          left: 15px;
        }

        svg {
          width: 45%;
          @include center_xy;
        }
      }
    }
  }

  div.content-wrapper {
    display: block;
    overflow-y: auto;

    .room-info {
      min-width: 0;


      .swiper_icon_carousel {
        padding: 10px 40px;
        font-size: 12px;
      }

      .swiper {
        height: 45px;

        &.room-mini-gallery-slider {
          height: auto;
        }
      }

      .swiper-slide {
        text-align: center;
      }

      .slider_navs {
        position: relative;
        width: 100%;

        .slider_prev {
          position: absolute;
          top: -40px;
          left: -20px;
        }

        .slider_next {
          position: absolute;
          top: -40px;
          right: -20px;
        }
      }

      .room-mini-gallery {
        padding: 10px;
        margin-top: 10px;
        overflow-x: auto;
        white-space: nowrap;
        display: flex;
        justify-content: center;
        max-width: 100%;

        .room-mini-gallery-image {
          width: 70px;
          height: 70px;
          object-fit: cover;
          border-radius: 10px;
          cursor: pointer;
          transition: all 0.3s ease;
          opacity: 0.5;

          &.selected {
            opacity: 1;
          }
        }
      }
    }

    .room-name {
      padding: 15px;
      font-size: 24px;
      font-weight: 600;
    }

    .icons-rooms-carousel {
      padding: 10px 15px;
      display: grid;
      grid-template-columns: repeat(2, 1fr);

      .service-icon-element {
        display: grid;
        grid-template-columns: 40px auto;
        align-items: center;

        .service-icon-name {
          font-weight: 300;
          font-size: 13px;
        }

        i, img {
          margin: auto;
        }
      }
    }

    .room-description {
      padding: 0 15px 20px;
      font-size: 12px;
      line-height: 18px;
      color: #707173;
    }

    .room-icons-categorized {
      display: flex;
      flex-direction: column;
      gap: 30px;
      padding: 0 15px 50px;

      .category-wrapper {
        .icons-category {
          font-size: 16px;
          font-weight: 700;
          margin-top: 10px;
        }

        .icons-wrapper {
          display: flex;
          gap: 20px 10px;
          margin-top: 20px;
          flex-wrap: wrap;

          .icon-wrapper {
            align-items: center;
            display: flex;
            gap: 10px;
            width: 30%;

            i {
              font-size: 22px;
            }

            span {
              font-size: 13px;
              font-family: auto;
            }
          }
        }
      }
    }
  }
}

.room-gallery-popup {
  .room-more-popup-wrapper {
    width: 800px;
  }

  .pictures-wrapper {
    .picture-element {
      width: 800px;
      height: 500px;
    }
  }
}

.room_icons_carousel {
  position: relative;
}

.room_icons_carousel {
  position: relative;
}
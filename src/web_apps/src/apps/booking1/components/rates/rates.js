import {BoardList} from "../boards/boards";
import './rates.scss';
import './rates_mobile.scss';
import {useState} from "react";
import {Modal} from "../../../../utils/general_components/modals/modals_utils";
import {useTranslation} from "../../../../utils/general_providers/translations_provider";
import {useBooking1Context} from "../../providers/general_b1_provider";
import {RateProvider} from "../../providers/rates_b1_provider";
import {userIsLogged} from "../../../../extras/club/club_utils";
import { useClubContext } from "../../../../extras/club/providers/club_provider";
import { PromotionRateLabel, RateMarketingInfo } from "../../../../extras/marketing/rates";


export const RatesList = ({rates_list, room_key, ratesVisible}) => {
    const user_club_logged = userIsLogged();
    const clubContext = useClubContext();
    const clubToggleVersion = clubContext.clubToggleVersion;
    const discountActivated = clubContext.discountActivated;
    const general_booking1 = useBooking1Context();

    const showRate = rate => {
        if (clubToggleVersion) {
            return !(discountActivated ^ rate.logged_users);
        }

        return !user_club_logged || rate.logged_users;
    }

    return (
        <div className={'rates-list-wrapper' + (general_booking1.rates_dropdown ? ' rates_dropdown' : '') + (ratesVisible ? ' active' : '')}>
            {rates_list.map((rate, index) => {
                return showRate(rate) && <RateElement key={index} rate_data={rate} room_key={room_key}/>;
            })}
        </div>
    );
}


export const RateElement = (props) => {
    let rate_info = props.rate_data;
    const [showRateConditions, setShowRateConditions] = useState(false);
    const general_booking1 = useBooking1Context();
    const isMobile = general_booking1.booking_configs.is_mobile;

    let rate_conditions = general_booking1.rate_conditions && general_booking1.rate_conditions[rate_info.rateStructure[2]] || {}

    const conditions_link = <RateConditionsLink setShowRateConditions={setShowRateConditions} />

    return (
        !rate_info.hide_on_booking1 &&
        <RateProvider rate_data={rate_info}>
            <div className={'rate-element-wrapper'}>
                <div className="rate-name">
                    <div className="rate-name-title">{rate_info.rateStructure[0]} {rate_info.extra_description && <ExtraRateInfo extraInfo={rate_info.extra_description} rateTitle={rate_info.rateStructure[0]}/>}</div>

                    <div className="marketing-wrapper">
                        <RateMarketingInfo
                            rate_info={rate_info}
                            rate_conditions={(!rate_conditions.last_day || isMobile) && conditions_link}
                        />

                        {rate_info.promotion_rate_label && !isMobile && (
                            <PromotionRateLabel promotions_info={rate_info.promotion_rate_label} />
                        )}
                    </div>
                </div>

                {rate_info.promotion_rate_label && isMobile && (
                    <PromotionRateLabel promotions_info={rate_info.promotion_rate_label} />
                )}

                {rate_conditions.last_day && (
                    <div className="rate-conditions-row">
                        <div className="rate-conditions-info">
                            <i className="fa-solid fa-check"></i>
                            {rate_conditions.last_day}
                        </div>
                        {!isMobile && conditions_link}
                    </div>
                )}

                <Modal
                    show={showRateConditions}
                    close_callback={() => setShowRateConditions(false)}
                    custom_class={'rate-conditions-modal'}>
                    <div className="rate-conditions-popup">
                        <div className="rate-title-conditions">{rate_info.rateStructure[0]}</div>
                        <div
                            className="rate-description-conditions"
                            dangerouslySetInnerHTML={{ __html: rate_info.rateStructure[1] }}></div>
                    </div>
                </Modal>

                <BoardList
                    board_list={rate_info.regimenResult}
                    room_key={props.room_key}
                    rate_key={rate_info.rateStructure[2]}
                />
            </div>
        </RateProvider>
    );
}

const RateConditionsLink = ({setShowRateConditions}) => {
    const translations = useTranslation();
    const general_booking1 = useBooking1Context();
    const is_mobile = general_booking1.booking_configs.is_mobile;

    return (
        <div className="rate-conditions-link" onClick={() => setShowRateConditions(true)}>
            {is_mobile ?
                <>
                    {translations['T_condiciones']}
                </>
                :
                <>
                    {translations['T_condiciones_reserva']}
                    <i className={'fas fa-info-circle'}></i>
                </>
            }
        </div>
    )
}

const ExtraRateInfo = ({extraInfo, rateTitle}) => {
    const translations = useTranslation();
    const {btn_title = translations['T_ver_detalles'], description} = extraInfo;
    const [showExtraInfo, setShowExtraInfo] = useState(false);
    return (
        <div className="extra-rate-info">
            <div className="extra-rate-info-title" onClick={() => setShowExtraInfo(!showExtraInfo)}>
                {btn_title}
            </div>
            <Modal show={showExtraInfo} close_callback={() => setShowExtraInfo(false)}
                   custom_class={'rate-conditions-modal extra-rate-conditions-modal'}>
                <div className="rate-conditions-popup">
                    <div className="rate-title-conditions">{rateTitle}</div>
                    <div className="rate-description-conditions" dangerouslySetInnerHTML={{__html: description}}></div>
                </div>
            </Modal>
        </div>
    )
}
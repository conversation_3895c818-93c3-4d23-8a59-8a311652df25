# -*- coding: utf-8 -*-
import os

from booking_process.utils.prices.price_context import _get_prices_separators
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name_with_properties
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.constants.advance_configs_names import SHOW_BABIES, CUSTOM_PRICE_FORMAT, \
    VALID_KIDS_OPTIONS
from utils.flask_requests import response_utils
from utils.web.BaseInjectionHandler import InjectionScript, InjectionWidgetHandler


class TorrenovaScript(InjectionScript):

    def params_base_script_controller(self):
        context = {
            "widget_url": "torrenovawidget",
            "widget_css": "torrenova_injection_styles",
            "static_version": "1.05",
            "booking_version": "7-min",
            "calendar_version": "5"
        }

        return context

    def template_controller_name(self):
        return "torrenova_widget_controller.js"

    def template_controller_path(self):
        return os.path.join(os.path.dirname(__file__), self.template_controller_name())


class TorrenovaWidgetHandler(InjectionWidgetHandler):

    def get(self, *args):
        headers = response_utils.get_response_headers()
        headers['Access-Control-Allow-Origin'] = '*'
        headers['Access-Control-Allow-Headers'] = 'Origin, Content-Type, X-Auth-Token, X-CSRF-Token'
        response_utils.add_response_headers(headers)
        return super(InjectionWidgetHandler, self).get(*args)

    def getBookingWidgetOptions(self, language, selectOptions=None):
        options = super(TorrenovaWidgetHandler, self).getBookingWidgetOptions(language)
        options['custom_promocode_label'] = get_web_dictionary(language).get('T_promocode')

        widget_config = get_section_from_section_spanish_name_with_properties("_widget_config", language)
        if widget_config:
            mini_dict = dict(get_web_dictionary(language))
            mini_dict['booking_steps'] = True
            range_age_kids = get_config_property_value(VALID_KIDS_OPTIONS)
            if range_age_kids:
                values = range_age_kids.split(';')
                range_dict = {
                    'min': values[0],
                    'max': values[-1]
                }
                mini_dict['valid_kids_options'] = range_dict

            options['custom_html_before_wrapper'] = self.buildTemplate_2("torrenova/_widget_content.html", mini_dict, False)

        options['inline_ages'] = False
        options['namespace'] = ''

        options['caption_submit_book'] = True
        options['departure_date_select'] = True
        options['booking_no_hide'] = True
        options['showBabies'] = get_config_property_value(SHOW_BABIES)
        options['avoid_translations'] = True
        hide_room_index_in_agekids_selector = get_config_property_value("Hide room index in agekids selector", language)
        if hide_room_index_in_agekids_selector:
            options["hide_room_index_in_agekids_selector"] = True

        prices_format_config = get_config_property_value(CUSTOM_PRICE_FORMAT)
        if prices_format_config:
            options['thousands_separator'], options['decimal_separator'] = _get_prices_separators(
                prices_format_config, language)

        return options

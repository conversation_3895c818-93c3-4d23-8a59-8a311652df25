# -*- coding: utf-8 -*-

import datetime
import json
import logging
import re

from flask import request
from flask.views import MethodView
import requests

from booking_process.utils.currency.currencyUtils import get_currency_symbol
from booking_process.utils.queues.queues_utils import defer
from booking_process.libs.communication import directDataProvider
from models.reservations import Reservation
from booking_process.utils.auditing import auditUtils
from booking_process.utils.booking.rate_info_calculator import get_rate_name_from_reservation
from booking_process.utils.booking.selections.selection_utils import getSelectedRoom
from booking_process.constants.dates_standard import SEARCH_DATE_FORMAT
from booking_process.constants.advance_configs_names import CUSTOM_DOMAIN, MANAGEMENT_LANGUAGE, TRANSER_BANK_PAYMENT, \
	EMAIL_BOOKING, KEY_DOMAIN
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import getLogotypes
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.dates.dates_management import get_localized_date, get_num_days
from booking_process.utils.development.dev_booking_utils import DEV
from booking_process.utils.email.email_utils_third_party import notify_exception, send_email_attachment
from utils.flask_requests import response_utils
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.utils.namespaces.namespace_utils import get_namespace, set_namespace, get_all_namespaces, \
	namespace_exists
from booking_process.utils.templates.template_utils import buildTemplate

booking_simulation = Reservation()
booking_simulation.creditCard = 'BANK TRANSFER'
booking_simulation.startDate = datetime.datetime.strftime((datetime.datetime.now() + datetime.timedelta(5)), '%Y-%m-%d')#'2016-07-20'
booking_simulation.endDate = datetime.datetime.strftime((datetime.datetime.now() + datetime.timedelta(10)), '%Y-%m-%d')#'2016-07-21'
booking_simulation.timestamp = '2020-03-05 2020-03-05'
booking_simulation.language = 'SPANISH'
booking_simulation.identifier = '********'
booking_simulation.price = '100.0'
booking_simulation.name = 'Don Pepito'
booking_simulation.lastName = 'Don Jose'
booking_simulation.rate = "ahNkZXZ-cG9ydGFsLWRlbC1sYWdvchELEgRSYXRlGICAgICAgJwJDA"
booking_simulation.extraInfo = '{"numflight":"","wubook_id":"**********","birthday":"1960-3-1","numfactu":"","personalId":"13.963.304","check-allow-notifications":false,"prices_per_day":{"1: ahJzfnNlY3VyZS1hcmdlbnRpbmFyFQsSCFJvb21UeXBlGICAgNynmY0KDKIBD3BvcnRhbC1kZWwtbGFnbw":{"01/01/2020":[8814.0393,1149.*************,7664.382],"total":15328.764,"31/12/2019":[8814.0393,1149.*************,7664.382]}},"additional_services_keys":"ahJzfnNlY3VyZS1hcmdlbnRpbmFyFwsSClN1cHBsZW1lbnQYgICA4oeE_woMogEPcG9ydGFsLWRlbC1sYWdv - name: Picada, dos cervezas y snacks - cantidad: 1 - dias: 1 - precio: 215.0;ahJzfnNlY3VyZS1hcmdlbnRpbmFyFwsSClN1cHBsZW1lbnQYgICAspPSzQoMogEPcG9ydGFsLWRlbC1sYWdv - name: Merienda UMBA - cantidad: 1 - dias: 1 - precio: 380.0;ahJzfnNlY3VyZS1hcmdlbnRpbmFyFwsSClN1cHBsZW1lbnQYgICAkvSBkAkMogEPcG9ydGFsLWRlbC1sYWdv - name: Botella de champagne y bombones - cantidad: 1 - dias: 1 - precio: 640.0;","modification_timestamp":"2019-11-20 17:04:25"}'


class bank_transfer_remember(MethodView):
	def get(self):
		try:
			if request.values.get('specific_date'):
				actual_namespace = get_namespace()
				request_dict = {
					'email': request.values.get('email'),
					'specific_date': request.values.get('specific_date')
				}
				main_bank_transfer(actual_namespace, request_dict)
				return

			for namespace in get_all_namespaces():
				if (namespace and 'r__' in namespace) or not namespace_exists(namespace):
					continue

				# Specific namespace trigger
				specific_namespace = request.values.get('namespace')
				if specific_namespace and not specific_namespace == namespace:
					continue

				logging.info("Setting namespace to -> %s" % namespace)
				request_to_serialize = {
					'email': request.values.get('email')
				}

				if DEV:
					response_utils.add_to_response_content(main_bank_transfer(namespace, request_to_serialize))
					return ''

				defer(main_bank_transfer, namespace, request_to_serialize)
				#main_bank_transfer(namespace, request_to_serialize)

		except Exception as e:
			if not request.args.get("from_retry"):
				base_url = request.url
				query_append = "?"
				if "?" in base_url:
					query_append = "&"
				base_url += f"{query_append}from_retry=true"
				requests.get(base_url)

			else:
				message = auditUtils.makeTraceback()
				logging.critical(message)
				notify_exception("Alerta bank transfer remember cron job", message, add_hotel_info=True)




def main_bank_transfer(namespace, request):

	try:
		set_namespace(namespace)
		if not get_config_property_value(TRANSER_BANK_PAYMENT) and not request.get('specific_date'):
			logging.info("Configuration property disabled")
			return

		logging.info("Bank transfer remember initialized")

		email_and_days = get_remember_email_and_days()
		logging.info("Email and days -> %s" % email_and_days)

		cc_emails = get_config_property_value(EMAIL_BOOKING)
		hotel_transfer_mail = cc_emails.replace(';',',') + ',' + email_and_days.get('email', '')
		if request.get('email'):
			hotel_transfer_mail = request.get('email')
		days_to_check = email_and_days.get('days')
		transfers_to_send = get_reservation_remember(days_to_check, request.get('specific_date'))

		logging.info("Founded transfers: %s" % transfers_to_send)

		if transfers_to_send and hotel_transfer_mail:
			language_manager = get_config_property_value(MANAGEMENT_LANGUAGE)
			if language_manager:
				language_dictionary = get_web_dictionary(language_manager)
			else:
				language_dictionary = get_web_dictionary(SPANISH)

			for reservation_to_send in transfers_to_send:
				template_builded = build_template_remember(reservation_to_send, language_dictionary)
				if DEV:
					return template_builded
				logging.info("Send mail to: %s")

				send_email_attachment(hotel_transfer_mail, language_dictionary['T_remember_transfer_1'], template_builded, template_builded, bcc='<EMAIL>')

	except Exception as e:
		message = auditUtils.makeTraceback()
		logging.critical(message)

		if 'Email not configured' in str(e):
			message = 'Emails is not configured correctly at bank transfer'
			notify_exception("[Account] Bank transfer remember - Wrong configuration", message, add_hotel_info=True)
		else:
			message = auditUtils.makeTraceback()
			logging.critical(message)
			notify_exception("Alerta bank transfer remember cron job", message, add_hotel_info=True)


def get_remember_email_and_days():
	transfer_section = get_section_from_section_spanish_name('bank_transfer', SPANISH)
	transfer_section_properties = get_properties_for_entity(transfer_section.get('key'), SPANISH)

	if not transfer_section_properties.get('email'):
		raise Exception('Email not configured')

	hotel_mail = transfer_section_properties.get('email').split("; ")

	if DEV:
		hotel_mail = ['<EMAIL>']

	days_to_check = {}

	for property_transfer, value_transfer in transfer_section_properties.items():
		if 'rate_' in property_transfer:
			rate_name = property_transfer.replace('rate_', '')
			days_to_check[rate_name] = value_transfer

		if 'all_rates' == property_transfer:
			days_to_check[property_transfer] = value_transfer

		if 'specific_days_to_pay' == property_transfer:
			days_to_check[property_transfer] = value_transfer

	params = {
		'email': ','.join(hotel_mail),
		'days': days_to_check
	}

	return params


def get_reservation_remember(days_to_check, specific_date=None):
	transfer_resevations = []

	if not specific_date:
		today_query = datetime.datetime.now().strftime(SEARCH_DATE_FORMAT)
	else:
		today_query = specific_date

	if DEV:
		reservations_today_forward = [booking_simulation]
	else:
		reservations_today_forward = directDataProvider.get_using_query("SELECT * FROM Reservation WHERE startDate >= '%s'" % (today_query))

	if reservations_today_forward:
		reservations_today_forward = filter(lambda x: x.creditCard == 'BANK TRANSFER' and not x.cancelled, reservations_today_forward)
		all_rates_entities_names = get_all_rates_dict()
		for reservation in reservations_today_forward:
			logging.info("=="*6)

			#Rates name
			rate_name = all_rates_entities_names.get(reservation.rate)
			logging.info("Rate names list -> %s" % rate_name)

			#Get days to pay of rates
			number_of_days_to_pay = days_to_check.get(rate_name)

			#If the rate doesn't have some specific dates retreive the general one
			if not number_of_days_to_pay:
				number_of_days_to_pay = days_to_check.get('all_rates')

			#If it's setted a specific days to pay, this is mandatory
			if days_to_check.get('specific_days_to_pay'):
				number_of_days_to_pay = days_to_check.get('specific_days_to_pay')


			logging.info("Number of days to pay list %s" % number_of_days_to_pay)

			if number_of_days_to_pay:
				if not 'R' in number_of_days_to_pay and not days_to_check.get('specific_days_to_pay'):
					today = datetime.datetime.strptime(today_query, SEARCH_DATE_FORMAT)
					start_date = datetime.datetime.strptime(reservation.startDate, SEARCH_DATE_FORMAT)
					days_today_and_start_date = (start_date - today).days
				else:
					number_of_days_to_pay = number_of_days_to_pay.replace('R', '')
					today = datetime.datetime.strptime(today_query, SEARCH_DATE_FORMAT)
					timestamp_date = reservation.timestamp.split(" ")[0]
					timestamp_datetime = datetime.datetime.strptime(timestamp_date, SEARCH_DATE_FORMAT)
					days_today_and_start_date = (today - timestamp_datetime).days #This number should be negative but with datetime always is possitive

				days_to_pay_is_today = (int(number_of_days_to_pay) == int(days_today_and_start_date))

				price_supplements = get_total_price_supplements(reservation)
				total = float(reservation.price) + float(price_supplements)
				reservation.price = str(total)

				if reservation.creditCard and reservation.creditCard == 'BANK TRANSFER' and days_to_pay_is_today:
					logging.info("Reservation ACCEPTED")
					transfer_resevations.append(reservation)
				else:
					logging.info('Reservation not accepted')
					logging.info('reservation.creditCard --> %s' % reservation.creditCard)
					logging.info('Number of days to pay before --> %s' % number_of_days_to_pay)
					logging.info('Days between today and start date --> %s' % days_today_and_start_date)

	return transfer_resevations


def get_total_price_supplements(reservation):

	logging.info("get_total_price_supplements:")

	total = 0

	try:

		extra_info = json.loads(reservation.extraInfo)

		if extra_info:
			services = extra_info.get('additional_services_keys','')
			for service in services.split(";"):
				result = re.match("(.*)cantidad:\s*(\d*\.?\d*)\s*-\s*dias:\s*(\d*\.?\d*)\s*-\s*precio:\s*(\d*\.?\d*)", service)
				if result:
					quantity = result.group(2).strip()
					days = result.group(3).strip()
					price = result.group(4).strip()
					total = total + (int(quantity)*int(days)*float(price))

		logging.info("Price supplements: %s", total)
	except:
		return 0

	return total

def get_reservation_info(reservation, language):
	request_simulate = {'startDate': reservation.startDate, 'endDate': reservation.endDate}
	num_nights = get_num_days(request_simulate)
	reservation_dictionary = {
		'identifier': reservation.identifier,
		'num_nights': num_nights,
		'numRooms': reservation.numRooms,
		'startDate': get_localized_date(reservation.startDate, language),
		'endDate': get_localized_date(reservation.endDate, language),
		'roomTypes': [reservation.roomType1, reservation.roomType2, reservation.roomType3],
		'comments': reservation.comments,
		'price': reservation.price,
		'currency': get_currency_symbol()
	}

	reservation_dictionary['days_until_start'] = days_to_startdate(reservation)

	#Rates name
	rate_name = lambda x: get_rate_name_from_reservation(x, language) #  getSelectedRate([x], language)
	reservation_dictionary['rateType'] = rate_name(reservation) # rate_name(reservation.rate)

	#Rooms name
	room_name = lambda x: getSelectedRoom([['', x]], language)
	reservation_dictionary['roomTypes'] = [room_name(room_key) for room_key in reservation_dictionary['roomTypes'] if room_name(room_key)]

	#Offers Name
	promotion_name = lambda x: get_promotion_name(x, language)
	reservation_dictionary['promotionType'] = promotion_name(str(reservation.promotions).split(","))

	return reservation_dictionary


def days_to_startdate(reservation):
	start = datetime.datetime.strptime(reservation.startDate, SEARCH_DATE_FORMAT)
	today = datetime.datetime.now()
	today = datetime.datetime(today.year, today.month, today.day)
	delta = start - today
	return delta.days


def get_promotion_name(promotion_entity, language):
	promotion_names = []
	for promotion in promotion_entity:
		query_promotion = directDataProvider.get('WebPageProperty', {'languageKey': language, 'entityKey': promotion, 'mainKey': 'promotionName'})
		if query_promotion:
			promotion_names.append(query_promotion[0].value)

	return promotion_names


def build_template_remember(reservation, language_dictionary):
	logotype = getLogotypes('bank_transfer')
	if not logotype:
		logotype = getLogotypes('prestay')[0] if getLogotypes('prestay') else getLogotypes()[0]
	else:
		logotype = logotype[0]
	template = 'email/transfer_remember.html'
	booking_domain = get_config_property_value(CUSTOM_DOMAIN)
	language_manager = get_config_property_value(MANAGEMENT_LANGUAGE)

	params = {'logotype': logotype,
	          'domain': get_config_property_value(KEY_DOMAIN) if not booking_domain else booking_domain}

	if language_manager:
		params['reservation_info'] = get_reservation_info(reservation, language_manager)
	else:
		params['reservation_info'] = get_reservation_info(reservation, SPANISH)

	#Get logo background
	try:
		bank_transfer_section = get_section_from_section_spanish_name('bank_transfer', reservation.language)
		bank_transfer_advance_properties = get_properties_for_entity(bank_transfer_section.get('key', False), reservation.language)
		if bank_transfer_advance_properties.get('logo_background'):
			params['logo_background'] = bank_transfer_advance_properties['logo_background']

	except:
		logging.error("Something was wrong tryig to get bank transfer section information")

	context = dict(list(params.items()) + list(language_dictionary.items()))
	context['T_remember_transfer_2'] = context['T_remember_transfer_2'].replace('@@cliente@@', ('<b>' + reservation.name + ' ' + reservation.lastName + '</b>'))
	context['T_remember_transfer_2'] = context['T_remember_transfer_2'].replace('@@localizador@@', '<b>' + context['reservation_info']['identifier'] + '</b>')

	# Labels if singular or plural
	context['nights_label'] = context['T_noche'] if context['reservation_info']['num_nights'] == 1 else context['T_noches']
	context['rooms_label'] = context['T_habitacion'] if context['reservation_info']['numRooms'] == 1 else context['T_habitaciones']

	return buildTemplate(template, context, False)


def get_all_rates_dict():
	'''I need this to avoid a lot of queries'''
	webPageProperty = directDataProvider.get('WebPageProperty', {'languageKey': SPANISH, 'mainKey': 'rateName'})
	prepared_dict = {}
	for element in webPageProperty:
		prepared_dict[element.entityKey] = element.value

	return prepared_dict

import calendar
import datetime
import json
import logging
import re


from google.cloud.datastore import Key

from booking_process.constants.dates_standard import VISUAL_DATE_FORMAT, SEARCH_DATE_FORMAT
from booking_process.constants.web_configs_names import BONO_NIGHTS
from booking_process.utils.auditing import auditUtils
from booking_process.utils.currency.currencyUtils import get_currency_base_symbol, convert_price_to_currency, \
	get_currency_symbol, get_special_currencies_for_rates, get_currency
from booking_process.utils.data_management.hotel_data import get_hotel_name
from booking_process.utils.data_management.rates_data import get_rate_from_key
from booking_process.constants.advance_configs_names import FIX_BONO_IN_SHOPPING_CART, MINIFY_EMAIL_CSS
from booking_process.constants.advance_configs_names import CUSTOM_DOMAIN, KEY_DOMAIN, EMAIL_SENDER, \
	HOTEL_COUNTRY_LOCATION, CUSTOM_WALLET_DOMAIN
from booking_process.constants.web_configs_names import BONO_GIFT_CUSTOM
from booking_process.libs.communication import directDataProvider
from booking_process.libs.communication.data import remote_data_provider
from booking_process.utils.booking.common_data_provider import get_all_rates_entity_json_map
from booking_process.utils.booking.conditions.rate_conditions import NO_CANCELABLE
from booking_process.constants.session_data import ORIGINAL_PRICE_BEFORE_DISCOUNT, TOTAL_BONO_GIFT_DISCOUNT, \
	BONOGIFT_REMOTE, SHOPPING_CART_SELECTED, CURRENCY_BY_RATE, BONOGIFT_EXPIRED, SEARCH_KEY_PREFIX, BONOGIFT_EXPIRED_INVALID
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.integrations_utils import get_integration_configuration
from booking_process.utils.data_management.pictures_utils import getPicturesForKey, getLogotype
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.development.dev_booking_utils import DEV, DEV_EMAIL
from booking_process.utils.email.email_utils_third_party import send_email, notify_exception
from utils.gift_bono.constants import GIFT_BONO
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_code
from booking_process.utils.namespaces import namespace_utils
from booking_process.utils.namespaces.namespace_utils import set_namespace, get_namespace
from booking_process.utils.templates.template_utils import build_template_2, inline_template_css

from booking_process.utils.session import session_manager
from models.giftBono import GiftBono

# Constants
COLOR_REPLACE = "<span style='color: %s'>"
ENDSPAN_REPLACE = "</span>"


class GiftBonoPromocodeController:
	template_floating_popup = "banners/gift_bono/_floating_gift_bono_booking.html"
	template_banner_booking3 = "banners/gift_bono/_banner_gift_booking3.html"
	template_popup = "banners/gift_bono/_popup_gift_bono_cancel.html"
	bono_properties = {}
	template_email = {
		'default': 'email/gift_bono/gift_bono_email.html',
		'v1': 'email/gift_bono/gift_bono_email_v1.html',
		'wallet': 'email/wallet/wallet.html'
	}

	def __init__(self):
		self.bono_properties = get_web_configuration(GIFT_BONO)
		logging.info("[GiftBono] Getting properties from namespace %s:  %s ", get_namespace(), self.bono_properties)

	def get(self, identifier, only_actual_hotel=False, search=None):
		logging.info("[GiftBono] Getting %s bono", identifier)

		# #fmatheis, uncomment this for TEST PURPOSES ONLY
		# if DEV:
		# 	test_bono = GiftBono()
		# 	test_bono.create_promocode = True
		# 	test_bono.discount = '1000'
		# 	test_bono.discount_per_day = False
		# 	test_bono.created_manually = True
		# 	test_bono.bono_identifier = 'DISCOUNT1000'
		# 	test_bono.booking_identifier_canceled = 'TEST IDENTIFIER'
		# 	test_bono.status = u'unused'
		# 	test_bono.extra_info_2 = ''
		# 	return [test_bono]

		bonos_result = []

		prefix = self.bono_properties.get('prefix', '')
		automatic_prefix_ok = not prefix or prefix in identifier

		manual_prefix_ok = False
		manual_prefix = self.bono_properties.get('manual_prefix', '')
		if manual_prefix:
			for m_prefix in manual_prefix.replace(",", ";").split(";"):
				if m_prefix in identifier:
					manual_prefix_ok = True
					break

		dates_restriction = self.bono_properties.get('blocked_dates')
		dates_restriction_search = self.bono_properties.get('blocked_dates_search')

		if dates_restriction:
			if check_date_restriction(dates_restriction, search, restriction_type='actual_date'):
				return []

		if dates_restriction_search and search:
			if check_date_restriction(dates_restriction_search, search, restriction_type='search_date'):
				return []

		if automatic_prefix_ok or manual_prefix_ok:
			bonos = directDataProvider.get("GiftBono", {"bono_identifier": identifier})
			for bono in bonos:
				bonos_result.append((bono, ""))

			if self.bono_properties.get("hotels") and not only_actual_hotel:
				logging.info("[GiftBono] Bono not found. Searching in other hotels")
				actual_namespace = namespace_utils.get_namespace()

				try:
					for hotel in self.bono_properties.get("hotels").split(";"):
						logging.info("[GiftBono] Searching in %s", hotel)

						namespace_utils.set_namespace(hotel)
						bonos_remote = directDataProvider.get("GiftBono", {"bono_identifier": identifier})
						for bono in bonos_remote:
							bonos_result.append((bono, hotel))

				finally:
					namespace_utils.set_namespace(actual_namespace)

			_normalize_bonos_timestamp(bonos_result)
			bono = sorted(bonos_result, key=lambda x: x[0].timestamp, reverse=True)
			if bono and len(bono) > 0:
				self.check_bono_expiration(bono)
				bono = bono[0]
				session_manager.set(BONOGIFT_REMOTE, bono[1])
				return [bono[0]]

		if self.bono_properties.get('wallet_hotels'):
			return self.find_and_recovery_from_wallet(bonos_result, identifier)

		return []

	@staticmethod
	def change_discount_if_different_currency(bono, discount, reverse_discount=False):
		if bono.extra_info:
			extra_info = json.loads(bono.extra_info)
			if extra_info.get('currency'):
				bono_currency = extra_info.get('currency').upper()
				currency_base = get_currency_base_symbol()
				if currency_base == '€':
					currency_base = 'EUR'
				if bono_currency != currency_base and '%' not in str(discount):
					logging.info("[GiftBono] Bono discount currency is different from base currency")
					if reverse_discount:
						logging.info(f"[GiftBono] Converting bono discount ({discount}) from {currency_base} to {bono_currency}")
						discount = convert_price_to_currency(discount, currency_base, bono_currency)
					else:
						logging.info(f"[GiftBono] Converting bono discount ({discount}) from {bono_currency} to {currency_base}")
						discount = convert_price_to_currency(discount, bono_currency, currency_base)
					return str(discount)
				else:
					return discount
		return discount

	def find_and_recovery_from_wallet(self, bonos_result, identifier):
		actual_namespace = namespace_utils.get_namespace()
		try:
			for hotel in self.bono_properties.get("hotels").split(";"):
				logging.info("[GiftBono] Searching in %s", hotel)

				namespace_utils.set_namespace(hotel)
				bonos_remote = directDataProvider.get("GiftBono", {"bono_identifier": identifier})
				for bono in bonos_remote:
					extra_info = bono.extra_info
					extra_info = json.loads(extra_info)
					if extra_info.get('type') == "wallet":
						bonos_result.append((bono, hotel))

				_normalize_bonos_timestamp(bonos_result)
				bono = sorted(bonos_result, key=lambda x: x[0].timestamp, reverse=True)
				if bono and len(bono) > 0:
					self.check_bono_expiration(bono)
					bono = bono[0]
					session_manager.set(BONOGIFT_REMOTE, bono[1])
					return [bono[0]]

		finally:
			namespace_utils.set_namespace(actual_namespace)

		return []

	def check_bono_expiration(self, bono):
		try:
			for bono_tuple in bono:
				bono_obj = bono_tuple[0]
				if bono_obj.expiration_date:
					expiration_date = (datetime.datetime.strptime(bono_obj.expiration_date, "%Y-%m-%d")
									   if isinstance(bono_obj.expiration_date, str)
									   else bono_obj.expiration_date)
					today = datetime.datetime.now()

					if expiration_date and not expiration_date >= today:
						visual_expiration_date = expiration_date.strftime(VISUAL_DATE_FORMAT)
						session_manager.set(BONOGIFT_EXPIRED, visual_expiration_date)
		except Exception as e:
			logging.error(f'[GiftBono] Error in checking bono expiration: {e}')

	def get_reservation(self, identifier):
		reservation = directDataProvider.get("Reservation", {"identifier": identifier}, 1)

		if self.bono_properties.get("hotels") and not reservation:
			logging.info("[GiftBono] Reservation not found. Searching in other hotels")
			actual_namespace = namespace_utils.get_namespace()
			hotel_remote = session_manager.get(BONOGIFT_REMOTE)

			try:
				if hotel_remote:
					logging.info("[GiftBono] Searching in %s", hotel_remote)

					namespace_utils.set_namespace(hotel_remote)
					reservation = directDataProvider.get("Reservation", {"identifier": identifier}, 1)

				else:
					for hotel in self.bono_properties['hotels'].split(";"):
						logging.info("[GiftBono] Searching in %s", hotel)

						namespace_utils.set_namespace(hotel)
						reservation = directDataProvider.get("Reservation", {"identifier": identifier}, 1)

						if reservation:
							session_manager.set(BONOGIFT_REMOTE, hotel)
							break

			finally:
				namespace_utils.set_namespace(actual_namespace)

		return reservation[0] if reservation else []

	def create(self, properties, force_duplicated=True):

		if properties.get('bono_identifier'):
			#Note that we are adding a space, so that the identifier is not found during the search
			#Discount and everything else is to be controlled by the promocode.
			bono_identifier = properties.get('bono_identifier')
		else:
			bono_identifier = self.bono_properties.get("prefix") + properties['booking_identifier_canceled']

		if not self.get(bono_identifier, only_actual_hotel=True) or force_duplicated:
			new_bono = GiftBono()
			new_bono.bono_identifier = bono_identifier
			new_bono.booking_identifier_canceled = properties.get('booking_identifier_canceled')
			new_bono.discount = properties['discount']
			new_bono.status = "unused"
			new_bono.timestamp = str(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
			new_bono.expiration_date = ''
			new_bono.email_subject = properties.get('email_subject','')
			new_bono.extra_info = '{}'
			if properties.get('extra_info'):
				new_bono.extra_info_2 = properties.get('extra_info')
				self.copy_extra_info(new_bono, properties)

			# Properties only for Wallets
			new_bono.rates = properties.get('rates', '')
			new_bono.rooms = properties.get('rooms', '')
			new_bono.occupancy = properties.get('occupancy', '')
			new_bono.date_ranges = properties.get('date_ranges', '')
			new_bono.max_nights = properties.get('max_nights', '')
			new_bono.min_nights = properties.get('min_nights', '')
			new_bono.num_rooms = properties.get('num_rooms', '')


			extra_info_dict = {}

			if force_duplicated:
				extra_info_dict['force_duplicated'] = True

			if properties.get('personal_details'):
				extra_info_dict['personal_details'] = properties.get('personal_details')

			if self.bono_properties.get("expiration_date") and not properties.get('bono_custom'):
				new_bono.expiration_date = str(self.bono_properties['expiration_date'])

			if properties.get('expiration_date'):
				new_bono.expiration_date = properties.get('expiration_date')

			if self.bono_properties.get('expiration_month') and not properties.get('bono_custom'):
				expire_gift = int(self.bono_properties["expiration_month"])
				today = datetime.datetime.now()
				month = today.month
				year = today.year
				total_days = 0

				while expire_gift > 0:
					if month == 0:
						month = 12

					elif month == 1:
						year += 1

					month_days = calendar.monthrange(year, month)[1]
					total_days += month_days
					expire_gift -= 1
					month = (month + 1) % 12

				expire_date = today + datetime.timedelta(days=total_days)
				new_bono.expiration_date = expire_date.strftime("%Y-%m-%d")

			if properties.get('user_creator'):
				new_bono.user_creator = properties.get('user_creator')

			if properties.get("amount_paid"):
				extra_info_dict['amount_paid'] = str(properties['amount_paid'])

			if properties.get("rate"):
				extra_info_dict['rate'] = properties['rate']

			if properties.get("bono_nights"):
				extra_info_dict['bono_nights'] = properties['bono_nights']

			if properties.get('external_booking'):
				new_bono.created_manually = True
				new_bono.discount_per_day = properties.get('discount_per_day')
				new_bono.create_promocode = properties.get('create_promocode')
			else:
				#automatics are always bonos
				new_bono.create_promocode = True
				#for automaticalies lets calculate if it has to be discount_per_day or not
				if self.apply_discount_each_day(new_bono, identifier_cancelled=properties['booking_identifier_canceled'], creation=True):
					new_bono.discount_per_day = True
				else:
					new_bono.discount_per_day = False

			if extra_info_dict:
				new_bono.extra_info_2 = json.dumps(extra_info_dict)

			bono_properties = remote_data_provider.db_entity_to_json(new_bono)
			directDataProvider.add_entity('GiftBono', None, bono_properties, exclude_from_indexes=('extra_info', 'rates', 'rooms'))

			logging.info("[GiftBono] created - %s", bono_identifier)
			logging.info("[GiftBono] properties - %s", properties)
			logging.info("[GiftBono] properties - %s", bono_properties)

			return new_bono

		else:
			logging.info("[GiftBono] %s already exists", bono_identifier)

	def copy_extra_info(self, new_bono, properties):
		try:
			extra_info_json = json.loads(properties.get('extra_info'))
			if extra_info_json.get('rates'):
				new_bono.extra_info = json.dumps(extra_info_json)
			elif extra_info_json.get('type') == 'wallet':
				new_bono.extra_info = json.dumps(extra_info_json)
		except Exception as e:
			logging.warning("Wrong copy from extra_info in Bono %s", new_bono.bono_identifier)

	def set_used_timestamp(self, bono, identifier):
		bono.timestamp_used = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
		bono.status = "used"
		bono.new_booking_identifier_used = identifier

		hotel_remote = session_manager.get(BONOGIFT_REMOTE)
		if hotel_remote:
			actual_namespace = namespace_utils.get_namespace()

			try:
				namespace_utils.set_namespace(hotel_remote)
				#bono.put()

				k = Key.from_legacy_urlsafe(bono.key)
				bono_to_push = bono.__dict__
				if bono_to_push.get("key"):
					bono_to_push.pop("key")
				directDataProvider.add_entity("GiftBono", k.id, bono_to_push,
											exclude_from_indexes=('extra_info','rates', 'rooms'))

			finally:
				namespace_utils.set_namespace(actual_namespace)

		else:
			#bono.put()
			k = Key.from_legacy_urlsafe(bono.key)
			bono_to_push = bono.__dict__
			if bono_to_push.get("key"):
				bono_to_push.pop("key")
			directDataProvider.add_entity("GiftBono", k.id, bono_to_push,
										  exclude_from_indexes=('extra_info','rates', 'rooms'))
		logging.info("[GiftBono] %s bono used", bono.bono_identifier)

	def bono_is_valid(self, bono, search_result):

		#We are using an existing promocode, so we just provide the promocode and that is all
		#if bono.created_manually and not bono.create_promocode:
			#return False

		# try:
		# 	extra_info = json.loads(bono.extra_info)
		# 	if extra_info.get('force_duplicated'):
		# 		return True
		#
		# except Exception as e:
		# 	logging.info("Bono doesn't have extra info")

		actual_time = datetime.datetime.now().strftime("%Y-%m-%d")
		bono_valid = not self.bono_is_used(bono)

		if self.bono_properties.get("expiration_by_reservation"):
			end_date = search_result.get("search", {}).get("endDate")
			if bono.expiration_date and end_date and end_date > bono.expiration_date:
				bono_valid = False
				session_manager.set(BONOGIFT_EXPIRED_INVALID, True)

		elif bono.expiration_date and actual_time > bono.expiration_date:
			bono_valid = False
			session_manager.set(BONOGIFT_EXPIRED_INVALID, True)

		bono_rate_name = self.get_bono_rate_name(bono)
		if bono_rate_name and search_result:
			return self._check_rate_in_search(search_result, bono_rate_name)

		return bono_valid

	def bono_is_valid_wallet(self, bono, search_result):
		bono_valid = True

		if bono.num_rooms or bono.max_nights or bono.min_nights or bono.date_ranges:
			logging.info("[GiftBono] Checking wallet bono conditions")
			search = session_manager.get(SEARCH_KEY_PREFIX)
			start_date, end_date = search.get("startDate"), search.get("endDate")

			if not start_date or not end_date:
				bono_valid = False
			else:
				num_nights = (datetime.datetime.strptime(end_date, SEARCH_DATE_FORMAT) - datetime.datetime.strptime(start_date, SEARCH_DATE_FORMAT)).days
				if (bono.num_rooms and int(bono.num_rooms) != len(search.get("rooms", []))) or \
						(bono.max_nights and int(bono.max_nights) < num_nights) or \
						(bono.min_nights and int(bono.min_nights) > num_nights):
					bono_valid = False

				if bono.date_ranges:
					date_ranges = json.loads(bono.date_ranges)
					if date_ranges:
						for date_range in date_ranges:
							start_range, end_range = date_range.get('start_date'), date_range.get('end_date')
							if start_range <= start_date <= end_range and start_range <= end_date <= end_range:
								break
						else:
							bono_valid = False

		logging.info(f"[GiftBono] Wallet bono conditions are valid: {bono_valid}")
		return bono_valid

	def get_bono_rate_name(self, bono):
		if bono and hasattr(bono, 'extra_info_2') and bono.extra_info_2:
			extra_info = json.loads(bono.extra_info_2)
			bono_nights = extra_info.get('bono_nights')
			if bono_nights:
				bono_nights_config = get_web_configuration(BONO_NIGHTS)
				bono_rate_name = bono_nights_config.get(bono_nights)
				if bono_rate_name:
					return bono_rate_name

		return None


	def _check_rate_in_search(self, search_result, rate_name):
		for index in range(0, len(search_result['result'])):
				for room_key, room_info in search_result[u'result'][index].items():
					for rate_key, rate_info in room_info.items():
						rate_local_name = self._get_rate_local_name(rate_key)
						if rate_name == rate_local_name:
							return True

		return False


	def _get_rate_local_name(self, rate_key):
		selected_rate = get_all_rates_entity_json_map().get(rate_key)
		if selected_rate:
			return selected_rate.get('localName')

		return None


	def bono_currency_is_valid(self, bono):
		actual_base_currency = session_manager.get(CURRENCY_BY_RATE)
		if not actual_base_currency:
			actual_base_currency = get_currency_symbol()
		if get_config_property_value(HOTEL_COUNTRY_LOCATION):
			extra_info = json.loads(bono.extra_info_2)
			bono_currency = ''
			if extra_info and extra_info.get('rate'):
				rate_key = extra_info.get('rate')
				rate_currency_info = get_special_currencies_for_rates()
				if rate_currency_info.get(rate_key):
					bono_currency = rate_currency_info.get(rate_key).split(";")[0]

			if not bono_currency or bono_currency and bono_currency == actual_base_currency:
				return True
			return False
		else:
			return True

	def bono_is_used(self, bono):
		if bono and bono.timestamp_used:
			return True

		return False

	def rateIsNonRefundable(self, selected_rate_key, also_removed=False, bono=None):
		if not selected_rate_key and not bono:
			return False

		if bono:
			if bono.extra_info_2 or bono.extra_info:
				if bono.extra_info_2:
					selected_rate_key = json.loads(bono.extra_info_2).get("rate")
				else:
					selected_rate_key = json.loads(bono.extra_info).get("rate")

				if not selected_rate_key:
					return False

			else:
				return False

		hotel_remote = session_manager.get(BONOGIFT_REMOTE)
		if hotel_remote:
			actual_namespace = namespace_utils.get_namespace()

			try:
				namespace_utils.set_namespace(hotel_remote)
				logging.info("[GiftBono] Searching rate %s in %s", selected_rate_key, hotel_remote)
				current_rate = get_all_rates_entity_json_map(also_removed=also_removed).get(selected_rate_key)
				if not current_rate:
					return False

			finally:
				namespace_utils.set_namespace(actual_namespace)

		else:
			current_rate = get_all_rates_entity_json_map(also_removed=also_removed).get(selected_rate_key)
			if not current_rate:
				return False

		cancellation_policy = current_rate['cancellationPolicy']
		return cancellation_policy == NO_CANCELABLE

	def discount_with_percentage(self, bono):
		return "%" in bono.discount

	def apply_discount_each_day(self, bono, identifier_cancelled=None, creation=False):

		if bono and bono.created_manually and bono.discount_per_day:
			return True

		if bono and bono.created_manually and not bono.discount_per_day:
			return False

		if identifier_cancelled:
			identifier = identifier_cancelled
		else:
			identifier = bono.booking_identifier_canceled

		reservation = self.get_reservation(identifier)

		extra_info = reservation.extraInfo
		if extra_info:
			extra_info = json.loads(extra_info)

		if extra_info and type(extra_info) == dict and extra_info.get("payed") and self.bono_properties.get("refund_tpv_payment"):
			logging.info("reservation payed by TPV so always by stay!")
			return False

		type_rate = "NR" if self.rateIsNonRefundable(reservation.rate, also_removed=True) else "PVP"
		logging.info("[GiftBono] Type of rate: %s", type_rate)

		type_discount = self.bono_properties.get("%s_type" % type_rate)
		if type_discount and "once" in type_discount:
			return False

		if creation:
			return type_rate != "NR"

		return bono and bono.discount_per_day


	def get_discount_amount(self, reservation, is_nr_rate):
		type_discount = self.bono_properties.get("PVP_type",'')
		amount_discount = self.bono_properties.get("PVP_amount")
		total_discount = 0
		extra_info = reservation.extraInfo

		if extra_info:
			extra_info = json.loads(extra_info)

		if is_nr_rate and self.bono_properties.get("NR_type"):
			type_discount = self.bono_properties.get("NR_type")
			amount_discount = self.bono_properties.get("NR_amount")

		if "days" in type_discount:
			'''Always make an avarage, becasue take the day amount from extra_info[prices_per_day].items() it is not safe
			becasue 1. we dont know what day, and more important, we dont now de order of a dict'''

			total_discount = float(reservation.price) / reservation.nights * float(amount_discount)

		elif "price" in type_discount:
			total_discount = float(amount_discount)

		elif "nights_room" in type_discount:
			total_discount = reservation.nights * reservation.numRooms * float(amount_discount)

		elif "nights" in type_discount:
			total_discount = reservation.nights * float(amount_discount)

		elif "all" in type_discount:
			total_discount = float(reservation.price) + float(reservation.priceSupplements)

		elif "percentage" in type_discount:
			if "old" in type_discount:
				total_discount = (float(reservation.price) + float(reservation.priceSupplements)) * (float(amount_discount) / 100)

			else:
				logging.info("[GiftBono] Discount: %s - Type: %s", amount_discount + "%", type_discount)
				return amount_discount + "%"

		elif "TPV" in type_discount:
			if extra_info and extra_info.get("payed"):
				total_discount = float(extra_info['payed'])

		if extra_info and type(extra_info) == dict and extra_info.get("payed") and self.bono_properties.get("refund_tpv_payment"):
			total_discount = float(extra_info['payed'])
			type_discount = "Refund TPV"

		logging.info("[GiftBono] Discount: %s - Type: %s", total_discount, type_discount)
		return "%.2f" % total_discount

	def render_floating_popup(self, language):
		args = {
			"section": get_section_from_section_spanish_name(self.bono_properties.get("popup_booking1"), language),
			"bono_propeties": self.bono_properties
		}

		pictures_section = getPicturesForKey(language, args['section'].get("key"), [])
		for pic in pictures_section:
			if pic.get("title") == "gift":
				args['pic_gift'] = pic

		logging.info("[GiftBono] Rendering popup floating booking1")

		return build_template_2(self.template_floating_popup, args, False)

	def render_banner_booking3(self, bono, language):
		args = {
			"bono_propeties": self.bono_properties
		}
		section = get_section_from_section_spanish_name(self.bono_properties.get("popup_booking1"), language)

		pictures_section = getPicturesForKey(language, section.get("key"), [])
		for pic in pictures_section:
			if pic.get("title") == "gift":
				args['pic_gift'] = pic

		args.update(get_web_dictionary(language))
		bono_discount = session_manager.get(TOTAL_BONO_GIFT_DISCOUNT)
		total_discount = min(bono_discount, session_manager.get(ORIGINAL_PRICE_BEFORE_DISCOUNT))

		shopping_cart = session_manager.get(SHOPPING_CART_SELECTED)
		if shopping_cart:
			if get_config_property_value(FIX_BONO_IN_SHOPPING_CART):
				shopping_cart['discount'] = total_discount
				shopping_cart['apply_discount'] = True
				session_manager.set(SHOPPING_CART_SELECTED, shopping_cart)

			else:
				return ""

		if self.bono_properties.get("banner_booking3"):
			banner_booking3 = get_section_from_section_spanish_name(self.bono_properties['banner_booking3'], language)

			if banner_booking3.get("subtitle"):
				args['T_descuento_bono_regalo'] = banner_booking3['subtitle']

			if banner_booking3.get("content"):
				args['T_ahorro_bono_regalo'] = banner_booking3['content']

		actual_currency = session_manager.get(CURRENCY_BY_RATE)
		if not actual_currency:
			actual_currency = get_currency_symbol()

		args['T_ahorro_bono_regalo'] = args.get("T_ahorro_bono_regalo").replace("@@discount@@", str(total_discount)).replace("@@currency@@", actual_currency)

		logging.info("[GiftBono] Total discount: %s" % total_discount)
		logging.info("[GiftBono] Rendering banner booking3")

		return build_template_2(self.template_banner_booking3, args, False)

	def show_popup(self, reservation):

		#if it's a reservation in the pass, or it is alredy cacelled, never offer the bono!!
		if reservation.cancelled:
			logging.info("reservation cancelled. Not showing popup bono gift")
			return False
		startDate = reservation.startDate
		today = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

		if today > startDate:
			logging.info("reservation In the Past! start date:  %s Today: %s Not showing popup bono gift", startDate, today)
			return False

		if not self.bono_properties:
			return False

		if self.bono_properties.get("show_dates"):
			try:
				min_period, max_period = self.bono_properties["show_dates"].split("@@")
			except ValueError as e:
				error_message = f"[GiftBono] Error in show_dates format. Please, check {GIFT_BONO} configuration. {e}"
				logging.error(error_message)
				notify_exception(f"[Account] Error in {GIFT_BONO} configuration", error_message, add_hotel_info=True)
				return False

			if not DEV and not (min_period <= startDate <= max_period):
				return False

		if self.bono_properties.get("show_dates_reservation_creation"):
			reservation_ts = reservation.timestamp
			try:
				min_period, max_period = self.bono_properties["show_dates_reservation_creation"].split("@@")
			except ValueError as e:
				error_message = f"[GiftBono] Error in show_dates_reservation_creation format. Please, check {GIFT_BONO} configuration. {e}"
				logging.error(error_message)
				notify_exception(f"[Account] Error in {GIFT_BONO} configuration", error_message, add_hotel_info=True)
				return False

			if not DEV and not (min_period <= reservation_ts <= max_period):
				return False

		extra_info = reservation.extraInfo

		if extra_info:
			extra_info = json.loads(extra_info)

		if extra_info and type(extra_info) == dict and extra_info.get("payed") and self.bono_properties.get("refund_tpv_payment"):
			return True

		if not self.rate_is_included(reservation):
			return False

		return True

	def rate_is_included(self, reservation):
		rate_key = reservation.rate

		type_rate = "NR" if self.rateIsNonRefundable(rate_key) else "PVP"
		if self.bono_properties.get("check_by_condition"):
			from booking_process.utils.booking.bookings_management.bookings_modifier import booking_is_modifiable
			reservation_is_modificable = booking_is_modifiable(reservation, modify=False)
			if not reservation_is_modificable:
				type_rate = "NR"

		if not self.bono_properties.get("%s_type" % type_rate):
			logging.info("[GiftBono] Skipping because rate type '%s' is not enabled in bono config" % type_rate)
			return False

		if self.bono_properties.get("included_rates"):
			hotel_remote = session_manager.get(BONOGIFT_REMOTE)
			if hotel_remote:
				actual_namespace = namespace_utils.get_namespace()
				try:
					namespace_utils.set_namespace(hotel_remote)
					logging.info("[GiftBono] Searching rate %s in %s", rate_key, hotel_remote)
					current_rate = get_all_rates_entity_json_map().get(rate_key)
				finally:
					namespace_utils.set_namespace(actual_namespace)
			else:
				current_rate = get_all_rates_entity_json_map().get(rate_key)

			if not current_rate:
				logging.info('[GiftBono] Skipping because rate is not found')
				return False

			included_rate_ids = self.bono_properties.get("included_rates").split(';')
			rate_id = current_rate.get('localName')

			if rate_id not in included_rate_ids:
				logging.info('[GiftBono] Skipping because rate it is not included')
				return False

		return True

	def render_popup(self, reservation):
		language = reservation.language
		is_nr_rate = self.rateIsNonRefundable(reservation.rate)
		if self.bono_properties.get("check_by_condition"):
			from booking_process.utils.booking.bookings_management.bookings_modifier import booking_is_modifiable
			reservation_is_modificable = booking_is_modifiable(reservation, modify=False)
			if not reservation_is_modificable:
				is_nr_rate = True

		args = {
			"language_code": get_language_code(language),
			"section": get_section_from_section_spanish_name(self.bono_properties.get("section", ""), language),
			"bono_properties": self.bono_properties,
			"identifier": reservation.identifier,
			"email": reservation.email,
			"url": "/utils?action=cancelReservation"
		}

		booking_domain = get_config_property_value(CUSTOM_DOMAIN)
		if booking_domain and not DEV:
			args['url'] = booking_domain + args['url']

		if args.get("section"):
			args['section'].update(get_properties_for_entity(args['section'].get("key"), language))

			if is_nr_rate:

				if args['section'].get("NR_subtitle"):
					args['section']['subtitle'] = unescape(args['section']["NR_subtitle"])

				if args['section'].get("NR_content"):
					args['section']['content'] = unescape(args['section']["NR_content"])

			pictures_section = getPicturesForKey(language, args['section'].get("key"), [])

			for pic in pictures_section:
				if pic.get("title", "") == "header":
					args['section']['header_picture'] = pic

				elif pic.get("title", "") == "main":
					args['section']['main_picture'] = pic

		args['discount'] = self.get_discount_amount(reservation, is_nr_rate)
		if not "%" in args['discount']:
			discount_round = self.bono_properties.get("discount_round")
			if not discount_round or discount_round.lower() == "true":
				args['discount_rounded'] = "%.0f" % float(args['discount'])
			else:
				args['discount_rounded'] = "%.2f" % float(args['discount'])
		else:
			args['discount_rounded'] = args['discount']

		translations = get_web_dictionary(language)

		if self.apply_discount_each_day(None, reservation.identifier):
			discount_type_text = translations.get("T_por_dia", "")
		else:
			discount_type_text = translations.get("T_por_estacia", "")

		args['discount_type_text'] = discount_type_text

		args.update(translations)
		logging.info("[GiftBono] Rendering popup")
		return build_template_2(self.template_popup, args, False)

	def render_email(self, bono, language, mailing=False, discount_is_per_day=None, render_as_wallet=False):
		args = {
			"bono": bono.to_dict(),
			"bono_properties": self.bono_properties,
			"language_code": get_language_code(language),
			"logo": getLogotype(),
			"section": get_section_from_section_spanish_name(self.bono_properties.get("email", ""), language),
		}

		if not "%" in bono.discount:
			discount_round = self.bono_properties.get("discount_round")
			if not discount_round or discount_round.lower() == "true":
				args['bono_discount_rounded'] = "%.0f" % float(args['bono'].get("discount"))
			else:
				args['bono_discount_rounded'] = "%.2f" % float(args['bono'].get("discount"))
		else:
			args['bono_discount_rounded'] = args['bono'].get("discount")

		if mailing:
			url = get_config_property_value(KEY_DOMAIN)
			booking_domain = get_config_property_value(CUSTOM_DOMAIN)
			if booking_domain:
				url = booking_domain

			url += "/bono_gift/utilities?action=show_email&bono_identifier=%s&language=%s" % (args["bono"].get("bono_identifier"), language)
			args['url'] = url

		pictures_section = getPicturesForKey(language, args['section'].get("key"), [])

		for pic in pictures_section:
			if pic.get("title", "") == "header":
				args['header_picture'] = pic

			elif pic.get("title", "") == "main":
				args['main_picture'] = pic

			elif pic.get("title", "") == "logo":
				args['logo'] = pic.get("servingUrl")

			elif pic.get("title", "") == "gift":
				args['gift_picture'] = pic

			elif pic.get("title", "") == "custom_text_header":
				args['custom_text_header'] = pic.get("description")

		if args.get("section", {}).get("content"):
			args['section'].update(get_properties_for_entity(args['section'].get("key"), language))
			is_nr_rate = False
			reservation = directDataProvider.get("Reservation", {"identifier": bono.booking_identifier_canceled}, 1)
			if reservation:
				reservation = reservation[0]
				is_nr_rate = self.rateIsNonRefundable(reservation.rate)

			if is_nr_rate:
				if args['section'].get("NR_subtitle"):
					args['section']['subtitle'] = unescape(args['section']["NR_subtitle"])

				if args['section'].get("NR_content"):
					args['section']['content'] = unescape(args['section']["NR_content"])

			else:
				args['day_room_tag'] = self.apply_discount_each_day(bono)

			if self.bono_properties.get("color_1"):
				args["section"]['content'] = args["section"]['content'].replace("@@C1", COLOR_REPLACE % self.bono_properties['color_1'])
				args["section"]['content'] = args["section"]['content'].replace("C1@@", ENDSPAN_REPLACE)

			if self.bono_properties.get("color_2"):
				args["section"]['content'] = args["section"]['content'].replace("@@C2", COLOR_REPLACE % self.bono_properties['color_2'])
				args["section"]['content'] = args["section"]['content'].replace("C2@@", ENDSPAN_REPLACE)

			if "@@BUTTON@@" in args['section']["content"] or "@@BOUTON@@" in args['section']["content"]:
				args_button = {
					"main_color": self.bono_properties.get("color_1"),
					"picture": args.get("gift_picture"),
					"bono": bono.to_dict()
				}

				html_button = build_template_2("email/gift_bono/_gift_button.html", args_button, False)
				args['section']["content"] = args['section']["content"].replace("@@BOUTON@@","@@BUTTON@@").replace("@@BUTTON@@", html_button)

		if discount_is_per_day is not None:
			args['day_room_tag'] = discount_is_per_day


		bono_custom = get_web_configuration(BONO_GIFT_CUSTOM)
		actual_namespace = namespace_utils.get_namespace()
		if bono_custom.get("remote_sections"):
			set_namespace(bono_custom.get("remote_sections"))

		sectionMain = get_section_from_section_spanish_name(bono_custom.get("section"), language)
		sectionMainProperties = get_properties_for_entity(sectionMain.get("key"), language)
		section = get_section_from_section_spanish_name(sectionMainProperties.get("bono_custom_data"), language)
		section_properties = get_properties_for_entity(section.get("key"), language)
		section_pictures = getPicturesForKey(language, section.get("key"), [])

		if bono_custom.get("remote_sections"):
			set_namespace(actual_namespace)

		args.update({
			'description_concept': section_properties.get("discount_text"),
			'description_bono': section_properties.get("description_bono"),
			'link_reservation': get_link_bono(args['bono'].get('bono_identifier'), bono_custom.get('use_booking0')),
			'conditions': section_pictures,
			'color1': section_properties.get("color1"),
			'color2': section_properties.get("color2"),
			'custom_font_url': section_properties.get("custom_font_url"),
			'custom_font_title': section_properties.get("custom_font_title"),
			'custom_gift_color': section_properties.get("custom_gift_color"),
			'extra_form_text1': section_properties.get("extra_form_text1"),
			'extra_form_text2': section_properties.get("extra_form_text2"),
			'extra_bottom_image': section_properties.get("extra_bottom_image"),
			'extra_bottom_image_link': section_properties.get("extra_bottom_image_link")
		})

		if section_properties.get("extra_links"):
			extra_links_section = get_section_from_section_spanish_name(section_properties.get("extra_links"), language)
			if extra_links_section:
				extra_links_pics = getPicturesForKey(language, extra_links_section.get("key"), [])
				if extra_links_pics:
					for pic in extra_links_pics:
						pic.update(get_properties_for_entity(pic.get("key"), language))
					args['extra_links'] = extra_links_pics

		try:
			map_currencies_rate = get_integration_configuration("MULTICURRENCY RATES")
			extra_info_bono = json.loads(bono.extra_info_2)

			if extra_info_bono.get("rate") and map_currencies_rate:
				currency_bono = map_currencies_rate.get("rateMap").get(extra_info_bono['rate'])
				if currency_bono:
					args['currency'] = currency_bono
			else:
				reservation = directDataProvider.get("Reservation", {"identifier": bono.booking_identifier_canceled}, 1)
				if reservation:
					reservation = reservation[0]
					try:
						res_extra_info = json.loads(reservation.extraInfo)
					except:
						res_extra_info = {}

					if res_extra_info.get("currency"):
						args['currency'] = res_extra_info.get("currency")

		except Exception as e:
			logging.error("Imposible to get currency for bono")

		args.update(get_web_dictionary(language))
		logging.info("[GiftBono] Rendering email")

		if render_as_wallet:
			section = get_section_from_section_spanish_name(self.bono_properties.get("email_wallet"), language)
			section_properties = get_properties_for_entity(section.get("key"), language)
			section_pictures = getPicturesForKey(language, section.get("key"), [])

			args.update(self.build_context_wallet(language, section, section_properties, section_pictures, bono))
			return build_template_2(self.template_email.get('wallet'), args, False)

		version = self.bono_properties.get('email_version') if self.bono_properties.get('email_version') else 'default'
		logging.info("Bono email version: " + version)

		if version != 'default':
			section = get_section_from_section_spanish_name(self.bono_properties.get("section"), language)
			section_properties = get_properties_for_entity(section.get("key"), language)
			section_pictures = getPicturesForKey(language, section.get("key"), [])

			if version == "v1":
				args.update(self.build_context_email_v1(language, section, section_properties, section_pictures))

		return build_template_2(self.template_email.get(version), args, False)

	def render_email_custom(self, params, language, template=""):
		# Borrar luego de 2 semanas del commit si no hay fallos
		"""args = {
			"bono_properties": self.bono_properties,
			"language_code": get_language_code(language),
			"logo": getLogotype(),
			"section": get_section_from_section_spanish_name(self.bono_properties.get("email", ""), language),
		}

		args.update(get_web_dictionary(language))"""
		logging.info("[GiftBono] Rendering email")
		if params.get('custom'):
			rendered_email = build_template_2(template, params, params.get('custom'))
		rendered_email = build_template_2(template, params, False)

		if get_config_property_value(MINIFY_EMAIL_CSS):
			try:
				rendered_email = inline_template_css(rendered_email)
			except Exception as e:
				logging.error(f'Error trying to inline email css. Email: Gift Bono Custom')
				logging.error(e)

		return rendered_email

	def build_context_wallet(self, language, section, section_properties, section_pictures, bono):
		content = section.get('content') if section.get('content') else self.get_default_wallet_content(bono, language)
		
		discount = bono.discount
		if bono.extra_info:
			extra_info = json.loads(bono.extra_info)
			if extra_info.get('currency'):
				currency = extra_info.get('currency').upper()
			else:
				currency = get_currency()
		else:
			currency = get_currency()
		if "%" not in discount:
			discount = discount + " " + currency

		expiration_date = datetime.datetime.strptime(bono.expiration_date, '%Y-%m-%d')
		expiration_date = expiration_date.strftime('%d-%m-%Y')

		link_to_book = (
			get_config_property_value(CUSTOM_WALLET_DOMAIN) or
			get_config_property_value(CUSTOM_DOMAIN) or
			get_config_property_value(KEY_DOMAIN)
		)

		content = self._replace_wallet_content(
			content=content,
			bono=bono,
			discount=discount,
			expiration_date=expiration_date,
			link_to_book=link_to_book,
			section_properties=section_properties,
			language=language
		)

		url_logo = ''
		logo = [x for x in section_pictures if x.get('title') == 'logo']
		if logo:
			url_logo = logo[0].get('servingUrl')

		header_img = list(filter(lambda x: x.get('title') == 'header', section_pictures))

		context = {
			'title': section.get("subtitle"),
			'bono_image': [x for x in section_pictures if x.get('title') == 'bono'],
			'logo': url_logo,
			'header_img': header_img[0].get('servingUrl') if header_img else '',
			'custom_btn_bg': section_properties.get("button_background"),
			'description_bono': section_properties.get("description_bono"),
			'color1': section_properties.get("color1"),
			'color2': section_properties.get("color2"),
			'custom_font_url': section_properties.get("custom_font_url"),
			'custom_font_title': section_properties.get("custom_font_title"),
			'extra_bottom_image': section_properties.get("extra_bottom_image"),
			'extra_bottom_image_link': section_properties.get("extra_bottom_image_link"),
			'content': content
		}

		return context

	def get_default_wallet_content(self, bono, language):
		logging.info('[GiftBono]: Building default wallet email')
		mini_dict = {}

		try:
			mini_dict.update(get_web_dictionary(language))
			mini_dict['redeem_hotels_list'] = self.get_hotel_names_to_redeem_bono(bono)
			content = build_template_2('email/wallet/wallet_default_content.html', mini_dict, False)
			return content

		except Exception as e:
			logging.error(f'[GiftBono]: Error building default wallet email content: {e}')
			return ""

	def get_namespaces_to_redeem_bono(self, bono):
		namespaces_list = []
		try:
			if bono and bono.__dict__.get("extra_info"):
				extra_info = json.loads(bono.__dict__.get("extra_info") or '{}')
				rates = json.loads(extra_info.get("rates", '{}'))

				for namespace in rates.keys():
					namespaces_list.append(namespace)

		except (json.JSONDecodeError, KeyError, TypeError) as e:
			logging.error(f"[GiftBono] Error parsing namespaces for redeemable rates: {e}")

		logging.info(f'[GiftBono] Applicable namespaces for bono redemption: {namespaces_list}')

		return namespaces_list

	def get_hotel_names_to_redeem_bono(self, bono):
		actual_namespace = get_namespace()
		hotels_names = []
		try:
			namespaces_list = self.get_namespaces_to_redeem_bono(bono)
			for namespace in namespaces_list:
				set_namespace(namespace)
				name = get_hotel_name()
				if "|" in name:
					name = name.split("|")[0]
				hotels_names.append(name)
		except Exception as e:
			message = auditUtils.makeTraceback()
			logging.error(
				f'[GiftBono]: Failed to get hotel names to redeem bono. Exception: {e}')
			notify_exception('[GiftBono] Failed to get hotel names to redeem bono',
							 message, add_hotel_info=True)
		finally:
			set_namespace(actual_namespace)

		return hotels_names

	def _replace_wallet_content(self, **kwargs):
		content = kwargs.get('content')
		section_properties = kwargs.get('section_properties')
		wallet_info = kwargs.get('bono')
		dictionary = get_web_dictionary(kwargs.get('language'))
		discount_type = dictionary.get("T_por_dia") if wallet_info.discount_per_day else dictionary.get("T_por_estacia")

		replacement_dict = {
			'@@DISCOUNT@@': kwargs.get('discount'),
			'@@IDENTIFIER@@': wallet_info.bono_identifier,
			'@@EXPIRE_DATE@@': str(kwargs.get('expiration_date')),
			'@@LINK_TO_BOOK@@': str(kwargs.get('link_to_book')),
			'@@HOTEL_NAME@@': section_properties.get("hotel_name", ""),
			'@@PHONE@@': section_properties.get("phone", ""),
			'@@EMAIL@@': section_properties.get("email", ""),
			'@@COLOR1@@': section_properties.get("color1", "#cea888"),
			'@@DISCOUNT_TYPE@@': discount_type.lower()
		}
		

		if section_properties.get("custom_link"):
			replacement_dict['@@LINK_TO_BOOK@@'] = section_properties.get("custom_link")

		for key, value in replacement_dict.items():
			content = content.replace(key, value)

		return content

	def build_context_email_v1(self, language, section, section_properties, section_pictures):
		context = {
			'title': section.get("content"),
			'title_custom_styles': section_properties.get("title_custom_styles"),
			'price_custom_styles': section_properties.get("price_custom_styles"),
			'description': section.get("subtitle"),
			'bono_image': [x for x in section_pictures if x.get('title') == 'bono'],
			'email_logotype': filter(lambda x: x.get('title') == 'email_logotype', section_pictures),
			'custom_btn_bg': section_properties.get("button_background"),
			'btn_border_radius': section_properties.get("button_border_radius"),
			'card_border_radius': section_properties.get("card_border_radius"),
			'description_bono': section_properties.get("description_bono"),
			'color1': section_properties.get("color1"),
			'color2': section_properties.get("color2"),
			'custom_font_url': section_properties.get("custom_font_url"),
			'custom_font_title': section_properties.get("custom_font_title"),
			'extra_bottom_image': section_properties.get("extra_bottom_image"),
			'extra_bottom_image_link': section_properties.get("extra_bottom_image_link"),
			'external_reservation_link': section_properties.get("external_reservation_link"),
		}

		if section_properties.get("extra_links"):
			extra_links_section = get_section_from_section_spanish_name(section_properties.get("extra_links"), language)
			if extra_links_section:
				extra_links_pics = getPicturesForKey(language, extra_links_section.get("key"), [])
				if extra_links_pics:
					for pic in extra_links_pics:
						pic.update(get_properties_for_entity(pic.get("key"), language))
					context['extra_links'] = extra_links_pics

		return context

	def send_email(self, identifier, email, mailing=False):
		reservation = directDataProvider.get("Reservation", {"identifier": identifier}, 1)
		if reservation:
			reservation = reservation[0]
			language = reservation.language
			bono = self.get(self.bono_properties.get("prefix") + identifier)
			if bono:
				email_sender = get_config_property_value(EMAIL_SENDER)
				if " - " in email_sender:
					email_sender = email_sender.split(" - ")[0]

				bono_gift_tag = get_web_dictionary(language).get("T_bono_regalo", "")
				email_subject = "%s %s - %s" % (bono_gift_tag.upper(), bono[0].bono_identifier, email_sender)
				email_content = self.render_email(bono[0], language, mailing)

				if self.bono_properties.get("copy_email"):
					email += "," + self.bono_properties['copy_email']

				if self.bono_properties.get("copy_email_special"):
					email += "," + self.bono_properties['copy_email_special']

				logging.info("[GiftBono] Sending email to %s with bono %s", email, bono[0].bono_identifier)
				send_email(email, email_subject, email_content, email_content)

	def send_wallet_summary_to_hotel(self, language, bono):
		try:
			summary_html = self.build_bono_summary_for_hotel(language, bono)
			recipient_email = self.bono_properties.get("email_summary_wallet_to")
			if DEV and DEV_EMAIL:
				recipient_email = DEV_EMAIL

			summary_tag = get_web_dictionary(language).get("T_wallet_generated", "")
			summary_subject = f"{summary_tag} {bono.bono_identifier}"
			logging.info(f"[GiftBono] Sending wallet summary to {recipient_email}")

			send_email(recipient_email, summary_subject, summary_html, summary_html)

		except Exception as e:
			message = auditUtils.makeTraceback()
			logging.error(f"[GiftBono] Error sending wallet summary to hotel: {e}")
			notify_exception('[GiftBono] Error sending wallet summary to hotel',
							 message, add_hotel_info=True)


	def build_bono_summary_for_hotel(self, language, bono):
		try:
			bono_dict = bono.__dict__
			expiration_date = bono_dict.get("expiration_date")
			if isinstance(expiration_date, str):
				expiration_date = datetime.datetime.strptime(expiration_date, "%Y-%m-%d")

			extra_info = json.loads(bono_dict.get("extra_info") or '{}')
			rates = json.loads(extra_info.get("rates", '{}'))
			rates_to_apply = {}
			actual_namespace = get_namespace()
			try:
				for namespace, rates_key_list in rates.items():
					set_namespace(namespace)
					hotelname = get_hotel_name()
					if "|" in hotelname:
						hotelname = hotelname.split("|")[0]
					rates_to_apply[hotelname] = [
						get_rate_from_key(key, language).get("name") for key in rates_key_list
					]
			except Exception as e:
				logging.error(f"[GiftBono] Error getting rates names for wallet summary: {e}")
			finally:
				set_namespace(actual_namespace)

			dictionary = get_web_dictionary(language)
			discount = bono_dict.get('discount')
			if not "%" in discount:
				currency = extra_info.get('currency').upper()
				discount = f'{discount} {currency}'

			email_context = {
				"logo": getLogotype(),
				"redeem_hotels_list": self.get_hotel_names_to_redeem_bono(bono),
				"recipients_emails": extra_info.get("email"),
				"promocode": bono_dict.get("bono_identifier"),
				"expiration_date": expiration_date.strftime(VISUAL_DATE_FORMAT),
				"discount": discount,
				"discount_type": dictionary.get("T_por_dia") if bono_dict.get("discount_per_day") else dictionary.get("T_por_estacia"),
				"rates_to_apply": rates_to_apply

			}
			email_context.update(dictionary)
			email_content = build_template_2('email/wallet/wallet_summary_for_hotel.html', email_context, False)
			return email_content

		except Exception as e:
			logging.error(f"[GiftBono] Error building bono summary for hotel: {e}")
			return ""

	def pay_by_gateway_allowed(self):
		if self.bono_properties.get("no_gateway"):
			return False
		return True

	def _get_filter_by_policies(self):
		return self.bono_properties.get("filter_rates_by_policy", "")

	def bono_reactivate(self, reservation):
		if self.bono_properties.get("automatic bono reactivation"):
			try:
				extraInfo = json.loads(reservation.extraInfo)
			except Exception as e:
				logging.info("Error decoding json %s" % e)
				extraInfo = {}

			if extraInfo.get("bono_gift_used"):
				bono_identifier = extraInfo.get("bono_gift_used")
				bono = directDataProvider.get("GiftBono", {"bono_identifier": bono_identifier})
				if bono:
					is_nrf = False

					current_rate = get_all_rates_entity_json_map().get(reservation.rate)

					if current_rate:
						if current_rate['cancellationPolicy'] == NO_CANCELABLE:
							is_nrf = True
					else:
						is_nrf = True
					if not is_nrf:
						if len(bono) == 1:
							bono = bono[0]


							self._log_reactivated_bono(bono)
							bono.status = u"unused"
							bono.timestamp_used = None
							bono.put()
						elif len(bono) > 1:
							reservation_bono = list(filter(lambda x: x.new_booking_identifier_used == reservation.identifier, bono))
							if reservation_bono:
								bono.sort(key=lambda x: x.timestamp)
								reservation_bono = reservation_bono[0]
								bono_index = bono.index(reservation_bono)

								if bono_index < len(bono)-1:
									total_discount = float(reservation_bono.discount)
									self._log_reactivated_bono(reservation_bono)
									total_discount = total_discount - float(bono[bono_index+1].discount)
									bono[len(bono)-1].discount = str(float(bono[len(bono)-1].discount) + total_discount)
									bono[len(bono) - 1].put()
								else:
									self._log_reactivated_bono(reservation_bono)

									reservation_bono.status = u"unused"
									reservation_bono.timestamp_used = None
									reservation_bono.new_booking_identifier_used = None

								reservation_bono.put()

						extraInfo["old_bono_gift_used"] = extraInfo["bono_gift_used"]

						del extraInfo["bono_gift_used"]
						if extraInfo.get("final_discounted_price"):
							del extraInfo["final_discounted_price"]

						reservation.extraInfo = json.dumps(extraInfo)
						reservation.put()

	def _log_reactivated_bono(self, reservation_bono):
		try:
			if reservation_bono.extra_info_2:
				bono_extra_info = json.loads(reservation_bono.extra_info_2)
			else:
				bono_extra_info = json.loads(reservation_bono.extra_info)
		except:
			bono_extra_info = {}
		if not bono_extra_info.get("old_reservations"):
			bono_extra_info["old_reservations"] = []
		bono_extra_info["old_reservations"].append({"status": reservation_bono.status,
													"timestamp_used": reservation_bono.timestamp_used,
													"identifier": reservation_bono.new_booking_identifier_used})

		reservation_bono.extra_info_2 = json.dumps(bono_extra_info)

def get_link_bono(promocode, hotel_list=None):
	# Copied from source/handlers/bonos/bonos_utils.py because it is impossible to import it

	web_domain = get_config_property_value(CUSTOM_DOMAIN)
	if not web_domain:
		web_domain = get_config_property_value(KEY_DOMAIN)

	web_domain = re.search("(https://.*|http://.*)", web_domain)
	if web_domain:
		web_domain = web_domain.group(0)

	path_url = f"/booking1/?adultsRoom1=2&promocode={promocode}"

	if hotel_list:
		hotel_list = hotel_list.replace(';', '%3B')
		path_url = f"/booking0?applicationIds={hotel_list}&adultsRoom1=2&promocode={promocode}"
	else:
		namespace = get_namespace()
		if namespace:
			path_url += f"&namespace={namespace}"

	return web_domain + path_url


def check_date_restriction(dates_restriction, search, restriction_type):
	dates_restriction = dates_restriction.split(";")

	if dates_restriction:
		if restriction_type == 'actual_date':
			for dates in dates_restriction:
				start_date, end_date = dates.split("@@")

				if start_date <= datetime.datetime.now().strftime("%Y-%m-%d") <= end_date:
					logging.info("[GiftBono] Bono not found. It is restricted by dates")
					return True
		elif restriction_type == 'search_date':
			if search:
				start_date = search.get("startDate")
				end_date = search.get("endDate")

				if start_date and end_date:
					for dates in dates_restriction:
						start_restriction, end_restriction = dates.split("@@")

						if start_restriction <= start_date <= end_restriction or start_restriction <= end_date <= end_restriction:
							logging.info("[GiftBono] Bono not found. It is restricted by dates")
							return True

			else:
				logging.error("[GiftBono] Search not found")
				return True
		else:
			logging.error("[GiftBono] Restriction type not found")
			return True
	else:
		notify_exception('[Account] Giftbono error', 'Dates restriction wrong format', add_hotel_info=True)
		return False

	return False

def _normalize_bonos_timestamp(bono_list):
	for bono_tuple in bono_list:
		bono = bono_tuple[0]
		if bono.timestamp and isinstance(bono.timestamp, int):
			bono.timestamp = str(datetime.datetime.fromtimestamp(bono.timestamp//1000))
# pylint: skip-file

# This file has been migrated from python2 to python3 migrating utils handlers, after everything is migrated
# Will be deleted all unused methods

####
####   IGNORE ERRORS ON THIS FILE, MORE THAN HALF OF THIS FILE WILL BE DELETED
####   DONT WASTE YOUR TIME FIXING ERRORS OF METHODS THAT IS NOT USED AND NEVER WILL BE USED
####


import json
import logging
import re
from datetime import datetime
from urllib.parse import urlencode

import requests
from flask import request

from booking_process.constants import advance_configs_names
from booking_process.utils import language
from booking_process.utils.analytics.google_tag_manager import google_tag_manager_datalayer
from booking_process.utils.booking import bookingUtils
from booking_process.utils.booking.additional_services.additional_services_methods import \
    get_selected_advanced_supplements_from_reservation, rebuild_additional_services
from booking_process.utils.booking.additional_services.additional_services_seeker_utils import \
    perform_booking_post_to_additional_services_seeker
from booking_process.utils.booking.bookingTemplateUtils import buildReservationConfirmationTemplate
from booking_process.utils.booking.bookingUtils import sendAvailabilityEmails, sendAvailabilityEmailToManager, \
    getRegimen, getRate, build_html_status_message_for_reservation, _get_total_payed_amount_from_all_sources
from booking_process.utils.booking.additional_services.additional_services_utils import is_booking2_react_active
from booking_process.utils.booking.bookings_management.bookings_cancellation import get_cancellation_header, \
    get_cancellation_footer
from booking_process.utils.booking.reservations.reservation_utils import get_reservation_info, \
    _process_reservation_advanced_supplements
from booking_process.utils.booking.selections.selection_price_utils import get_room_key_from_prices_per_day
from booking_process.utils.currency.currencyUtils import get_currency_symbol, get_selected_currency_code, get_currency
from booking_process.utils.email.email_utils import is_valid_email
from booking_process.utils.queues.queues_utils import defer
from booking_process.utils.redirect_modification.redirect import RedirectModificationController
from booking_process.utils.web_seeker.emails.email_constants import EMAIL_BOOKING_CANCELLATION
from booking_process.utils.web_seeker.emails.emails_controller import build_email_from_ws
from components import render_hooks
from booking_process.libs.communication import directDataProvider, hotelManagerCommunicator
from paraty_commons_3.datastore.datastore_utils import alphanumeric_to_key
from paraty_commons_3.email_utils import sendEmail
from booking_process.utils.auditing.auditUtils import makeTraceback
from booking_process.utils.booking import rate_info_calculator
from booking_process.utils.booking.booking_context.search_engines_context import buildSearchEngine
from booking_process.utils.booking.booking_email_utils import send_transfer_service_provider_email, buildEmailHeaderParams
from booking_process.utils.booking.bookings_management.bookings_modifier import booking_is_modifiable
from booking_process.utils.booking.bookings_management.bookings_searcher import search_booking_response_at_multiple_namespaces, \
    search_booking_model_at_multiple_namespaces, booking_is_cancelled, transform_reservation_to_json
from booking_process.utils.booking.confirmations.context_builder import build_reservation_dictionary, confirmation_v7_context
from booking_process.utils.booking.extra_utils.reservation_encryption import _is_encrypted_booking
from booking_process.utils.booking.rate_info_calculator import get_rate_conditions_from_reservation
from booking_process.utils.booking.rates.rate_info import get_rate_model, rateIsNonRefundable
from booking_process.utils.booking.searchs.search_utils import buildSearch_2
from utils.clubs.club_constants import CLUB_CONFIGURATION
from booking_process.constants.advance_configs_names import NO_SEND_CANCEL_EMAIL, SAVE_CANCELLATION_REASON_IN_INCIDENTS, \
    NOT_SEND_EMAIL_REFUND_PAYMENT_NR, CHECK_POLICIES_BEFORE_SEND_CANCELLATION
from booking_process.utils.bookingConstants import ADMIN_MANAGER_SERVER_2
from booking_process.constants.session_data import CURRENCY, TPV_PAY_MESSAGE, CANCEL_RESERVATION_DICT, \
    MODIFICATION_BLOCKED_BY_SOURCE
from booking_process.constants.dates_standard import SEARCH_DATE_FORMAT
from booking_process.constants.integrationsConstants import RESERVATION_PROPERTIES
from booking_process.constants.web_configs_names import WHATSAPP_COMMUNICATION
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape, get_text_version
from booking_process.utils.data_management.hotel_data import get_hotel_name, get_hotel_manager_email
from booking_process.utils.data_management.integrations_utils import get_integration_configuration_properties
from booking_process.utils.data_management.pictures_utils import getLogotypes, getLogotype, get_pictures_from_section_name, \
    getPicturesForKey
from booking_process.utils.data_management.rooms_data import getRoom, get_room
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name_with_properties, \
    get_section_from_section_spanish_name
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.dates.dates_management import get_localized_date
from booking_process.utils.development.dev_booking_utils import DEV_EMAIL, DEV
from booking_process.utils.email.email_utils_third_party import notify_exception, send_email
from utils.external_integrations.external_main import ExternalController
from utils.flask_requests import response_utils
from utils.gift_bono.gift_bono_controller import GiftBonoPromocodeController
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_language_based_on_request, get_web_dictionary, \
    get_language_code, get_management_language, get_language_in_path, get_default_language
from utils.login import login_manager
from utils.managers_cache.manager_cache import managers_cache
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.namespaces.namespace_utils import set_namespace, get_namespace, get_application_id
from booking_process.utils.session import session_manager
from booking_process.utils.templates.template_utils import buildTemplate, build_template_2
from booking_process.utils.transfer.transfer_utils import cancel_transfers
from booking_process.utils.transfer_service.constants import TRANSFER_SERVICE
from booking_process.utils.users import users_methods
from booking_process.utils.whatsapp.whatsapp_constants import WHATSAPP_BOOKING_CANCELLATION
from booking_process.utils.whatsapp.whatsapp_methods import send_whatsapp_message


def get_destination_addresses(form_fields):

    # Test purposes
    if form_fields.get('comments') and '@@@TEST@@@' in form_fields.get('comments'):
        return [form_fields.get('email')]

    if DEV and DEV_EMAIL:
        return [DEV_EMAIL]

    # In case we put the destination addresses as a hidden input in the form
    destination_email = form_fields.get('destination_email', None)

    if destination_email:
        email_addresses = form_fields['destination_email'].split(",")
        return email_addresses

    action = request.values.get("action")

    if action == "work_with_us":
        email_contact = get_config_property_value(advance_configs_names.EMAIL_WORK_WITH_US_FORMS)

    else:
        email_contact = get_config_property_value(advance_configs_names.EMAIL_CONTACT_FORMS)

    if email_contact:
        return re.split(r'[,;]+', email_contact)
    else:
        return re.split(r'[,;]+', get_hotel_manager_email())


def build_fields_for_message(form_fields):
    """
    creates the subject and sender for emails sent
    """
    section = form_fields.get('section', '')
    emailSubject = form_fields.get('emailSubject', '')
    emailSubjectCustomized = form_fields.get('emailSubjectCustomized', '')
    hotel_name = get_config_property_value(advance_configs_names.EMAIL_SENDER)

    # Translate title email contact
    manager_language = get_config_property_value(advance_configs_names.MANAGEMENT_LANGUAGE)
    language_items = manager_language if manager_language else SPANISH
    form_contact_title = get_web_dictionary(language_items)["T_formulario_contacto"]
    form_section_title = get_web_dictionary(language_items)["T_formulario_seccion"]

    if emailSubject:
        message_subject = emailSubject + " en " + hotel_name
        email_sender = u"%s <%s>" % (get_config_property_value(advance_configs_names.EMAIL_SENDER), '<EMAIL>')

    elif emailSubjectCustomized:
        message_subject = emailSubjectCustomized
        # email_sender = u" %s %s <%s>" % (emailSubjectCustomized, get_config_property_value(advance_configs_names.EMAIL_SENDER), '<EMAIL>')
        if "-" in get_config_property_value(advance_configs_names.EMAIL_SENDER):
            reply_email = get_config_property_value(advance_configs_names.EMAIL_SENDER).split("-")
            email_sender = u" %s <%s>" % (emailSubjectCustomized, reply_email[1])
        else:
            email_sender = u" %s <%s>" % (emailSubjectCustomized, '<EMAIL>')

    elif section:
        message_subject = form_section_title + u" %s, %s " % (section, hotel_name)
        email_sender = form_section_title + u" %s %s <%s>" % (
            section, get_config_property_value(advance_configs_names.EMAIL_SENDER), '<EMAIL>')
    else:
        message_subject = form_contact_title + " " + hotel_name
        email_sender = form_contact_title + u" %s <%s>" % (
            get_config_property_value(advance_configs_names.EMAIL_SENDER), '<EMAIL>')

    return (message_subject, email_sender)


def recaptcha_confirmation(public_key):
    """ API for recaptcha of google https://developers.google.com/recaptcha/ """
    secret_captcha_key = get_config_property_value(advance_configs_names.SECRET_CAPTCHA_KEY)
    prepare_params = {'response': public_key,
                      'secret': secret_captcha_key,
                      'remoteip': request.remote_addr
                      }

    try:
        encoded_params = urlencode(prepare_params)
        target_endpoint = f'https://www.google.com/recaptcha/api/siteverify?{encoded_params}'
        response = requests.get(target_endpoint)
        response_json = response.json()
        logging.info(response_json)
    except Exception:
        logging.warning(makeTraceback())
        return False

    return response_json.get('success')


def _recaptcha_is_valid(form_fields):
    secret_captcha_key = get_config_property_value(advance_configs_names.SECRET_CAPTCHA_KEY)
    request_captcha_response = form_fields.get('g-recaptcha-response')

    if not secret_captcha_key:
        return True

    if secret_captcha_key and not request_captcha_response:
        logging.info("Not received the recaptcha response")
        return False

    if not recaptcha_confirmation(request_captcha_response):
        logging.info("Wrong verification of recaptcha response")
        return False

    return True


def _move_room_data(room_origin, room_destination, reservation):

    adults_origin = None
    logging.info("_move_room_data from %s to %s", room_origin, room_destination)

    if room_origin == 1:
        adults_origin = reservation.adults1
        kids_origin = reservation.kids1
        babies_origin = reservation.babies1
    elif room_origin == 2:
        adults_origin = reservation.adults2
        kids_origin = reservation.kids2
        babies_origin = reservation.babies2
    elif room_origin == 2:
        adults_origin = reservation.adults2
        kids_origin = reservation.kids2
        babies_origin = reservation.babies2

    if adults_origin:
        if room_destination == 1:
            reservation.adults1 = adults_origin
            reservation.kids1 = kids_origin
            reservation.babies1 = babies_origin
        elif room_destination == 2:
            reservation.adults2 = adults_origin
            reservation.kids2 = kids_origin
            reservation.babies2 = babies_origin
        elif room_destination == 3:
            reservation.adults3 = adults_origin
            reservation.kids3 = kids_origin
            reservation.babies3 = babies_origin


def _move_extra_info_room_data(room_origin, room_destination, reservation):

    logging.info("_move_extra_info_room_data from %s to %s", room_origin, room_destination)
    extra_info = reservation.extraInfo
    if extra_info:
        extra_info = json.loads(extra_info)

        # we have to move 3 things: 1. price_room_%s and 2. modification_room_%s and 3. prices_per_day

        origin_price_room = extra_info.get("price_room_%s" % room_origin)
        if origin_price_room:
            extra_info["price_room_%s" % room_destination] = origin_price_room
            extra_info.pop("price_room_%s" % room_origin, None)

        origin_modification_room = extra_info.get("modification_room_%s" % room_origin)
        if origin_modification_room:
            extra_info["modification_room_%s" % room_destination] = origin_modification_room
            extra_info.pop("modification_room_%s" % room_origin, None)

        price_per_day_dict = extra_info.get("prices_per_day", {})

        price_per_day_dict_to_move = {}
        room_key_prices_to_delete = ""

        for room_key_prices, room_price_per_day in price_per_day_dict.items():
            if "%s: " % room_origin in room_key_prices:
                # room found, change prices per day of this room
                # delete old one:
                price_per_day_dict_to_move = price_per_day_dict[room_key_prices]
                room_key_prices_to_delete = room_key_prices

        if room_key_prices_to_delete:
            price_per_day_dict.pop(room_key_prices_to_delete, None)

        # and move it!
        if price_per_day_dict_to_move:
            for room_key_prices, room_price_per_day in price_per_day_dict.items():
                if "%s: " % room_destination in room_key_prices:
                    # room found, change prices per day of this room
                    # delete old one:
                    price_per_day_dict[room_key_prices] = price_per_day_dict_to_move

        reservation.extraInfo = json.dumps(extra_info)
        logging.info("Updating new extraInfo for rooms: %s", extra_info)


def recalculate_table_per_days_average(prices_per_day, prices_per_room):

    for n, room in enumerate(sorted(prices_per_day)):
        total = prices_per_room[n]
        room_prices = prices_per_day[room]
        room_prices.pop("total")
        num_days = len(room_prices)
        for key, value in room_prices.items():
            price_daily = total / num_days
            room_prices[key] = [price_daily, 0, price_daily]
        room_prices["total"] = total
        prices_per_day[room] = room_prices

    return prices_per_day


def processReservationSearch(modify_reservation=False, wrapper_html_template="", template_path="", allow_mobile=True):
    email = request.values.get("email")
    localizador = request.values.get("localizador")
    if request.values.get('modify_reservation'):
        modify_reservation = True

    email = email.replace(" ", "") if email else email
    localizador = localizador.replace(" ", "") if localizador else localizador

    want_json_response = request.values.get('booking_json')

    actual_namespace = get_namespace()

    if not email or not localizador:
        logging.warning("Trying to search for a reservation with a missing email or localizator")
        return

    search_params = {"email": email.lower(), "identifier": localizador.strip().upper()}
    reservation = directDataProvider.get("Reservation", search_params, 1)
    show_details_only = request.values.get('show_details_only')
    ab_testing_main_hotel = get_config_property_value(advance_configs_names.AB_TESTING_MAIN_HOTEL)

    mix_multiple_hotels = get_config_property_value(advance_configs_names.MIX_MULTIPLE_HOTELS)
    if (request.values.get('namespaces_list') or mix_multiple_hotels) and not reservation:
        namespaces_to_search = mix_multiple_hotels or request.values.get('namespaces_list')
        namespaces_to_search = namespaces_to_search.split(";")
        multiple_namespaces_response = search_booking_response_at_multiple_namespaces(namespaces_to_search,
                                                                                      email,
                                                                                      localizador,
                                                                                      want_json_response,
                                                                                      modify_reservation=modify_reservation
                                                                                      )
        if multiple_namespaces_response:
            if type(multiple_namespaces_response) is int:
                response_utils.set_response_status_code(multiple_namespaces_response)
                return ""

            else:
                if modify_reservation:
                    reservation_object = search_booking_model_at_multiple_namespaces(namespaces_to_search, email,
                                                                                     localizador)
                    if reservation_object:
                        reservation_is_modificable = booking_is_modifiable(reservation_object)
                        if reservation_is_modificable:
                            search_engine = build_booking_engine_for_modification(reservation_object,
                                                                                       reservation_is_modificable,
                                                                                       template_path)

                            if want_json_response:
                                multiple_namespaces_response = json.loads(multiple_namespaces_response)
                                multiple_namespaces_response['modify_reservation'] = search_engine
                                multiple_namespaces_response = json.dumps(multiple_namespaces_response)
                            else:
                                if wrapper_html_template:
                                    language = reservation_object.language
                                    multiple_namespaces_response = buildTemplate(
                                        wrapper_html_template, {
                                            'language': get_language_code(language),
                                            'content': search_engine + multiple_namespaces_response,
                                            "scriptsToRenderDesktop": modification_scripts_to_render(language)}, allowMobile=allow_mobile)
                                else:
                                    if 'motor_reserva' not in multiple_namespaces_response:
                                        multiple_namespaces_response = search_engine + multiple_namespaces_response

                response_utils.add_to_response_content(multiple_namespaces_response)
                return ''

    external_hotel_reservations = None
    if ab_testing_main_hotel:
        try:
            external_hotel_reservations = ab_testing_main_hotel
            external_hotel_reservations = None if external_hotel_reservations == 'None' else external_hotel_reservations
            set_namespace(external_hotel_reservations)
            reservation = directDataProvider.get("Reservation", {"email": email.lower(),
                                                                 "identifier": localizador.strip().upper()}, 1)
        finally:
            set_namespace(actual_namespace)

    if booking_is_cancelled(reservation):
        response_utils.set_response_status_code(406)
        response_utils.add_to_response_content('')
        return ''

    if not reservation or len(reservation) == 0:
        logging.info("Reservation not found: " + email + " , " + localizador)
        response_utils.add_to_response_content('')
        return ''

    language = reservation[0].language
    language_for_messages = language
    if session_manager.get('from_hotel_manager'):
        language_for_messages = get_management_language()

    im_hotelier_from_manager = request.values.get("action", "") == "modifyReservationFromManager"

    rate_info = get_rate_model(reservation[0].rate)
    reservation_is_modificable = booking_is_modifiable(reservation[0], rate_info, modify=modify_reservation)
    logging.info("reservation_is_modificable: %s", reservation_is_modificable)

    search_engine = ""
    pre_advise = ""
    show_popup_bono_gift = False
    popup_cancel_modify_booking = get_config_property_value(advance_configs_names.POPUP_CANCEL_MODIFY_BOOKING)

    if not show_details_only:
        if modify_reservation:
            # check if is possible to modify
            if reservation_is_modificable or im_hotelier_from_manager:
                search_engine = build_booking_engine_for_modification(reservation[0], reservation_is_modificable, template_path)
            else:
                if session_manager.get(TPV_PAY_MESSAGE):
                    message_no_modify = get_web_dictionary(language_for_messages).get("T_no_posible_modificar_reserva_por_tpv", "impossible to modify")
                elif session_manager.get(MODIFICATION_BLOCKED_BY_SOURCE):
                    message_no_modify = get_modification_blocked_by_source_msg(language_for_messages)
                else:
                    message_no_modify = get_web_dictionary(language_for_messages).get("T_no_posible_modificar_reserva_por_condiciones", "impossible to modify")

                config_popup_mod = get_config_property_value(advance_configs_names.MODIFY_RESERVATION_POPUP)
                if config_popup_mod:
                    message_no_modify += "<br>" + get_section_from_section_spanish_name(config_popup_mod, language_for_messages).get("content", "")

                search_engine = "<div class='modify_reservation_widget alert-warning' style='padding: 20px;'>" + message_no_modify + "</div>"

        else:
            bono_controller = GiftBonoPromocodeController()
            show_popup_bono_gift = bono_controller.show_popup(reservation[0]) if not session_manager.get(MODIFICATION_BLOCKED_BY_SOURCE) else False
            if show_popup_bono_gift:
                search_engine += bono_controller.render_popup(reservation[0])

            popup_cancel_modify_booking = get_config_property_value(advance_configs_names.POPUP_CANCEL_MODIFY_BOOKING)
            if popup_cancel_modify_booking and not show_popup_bono_gift:
                startDate = reservation[0].startDate
                popup_cancel_modify_booking_section = get_section_from_section_spanish_name_with_properties(popup_cancel_modify_booking, language_for_messages)
                min_period = ""
                max_period = ""

                if popup_cancel_modify_booking_section.get("show_dates"):
                    min_period, max_period = popup_cancel_modify_booking_section.get("show_dates", "").split("@@")

                valid_dates = not popup_cancel_modify_booking_section.get("show_dates") or min_period <= startDate <= max_period
                is_nr_rate = False

                if not reservation_is_modificable:
                    is_nr_rate = True

                disable_nr = popup_cancel_modify_booking_section.get("disable_nr")
                valid_rate = not disable_nr or disable_nr and not is_nr_rate

                if valid_dates and valid_rate:
                    args = {
                        "popup_cancel_modify_booking": popup_cancel_modify_booking_section,
                        "reservation_identifier": reservation[0].identifier,
                        "rooms": buildRooms(reservation[0]),
                        "is_nr_rate": is_nr_rate
                    }

                    args.update(get_web_dictionary(language_for_messages))
                    search_engine += build_template_2("banners/_popup_cancel_modify_booking.html", args, False)

        if not show_popup_bono_gift and not reservation_is_modificable:
            args = {}
            args.update(get_web_dictionary(language_for_messages))

            if popup_cancel_modify_booking:
                args['hide_popup_not_modificable'] = True

            args['message'] = get_web_dictionary(language_for_messages).get(
                "T_description_popup_reservation_unmodifiable", "imposible to modify")
            if session_manager.get(TPV_PAY_MESSAGE):
                args['message'] = get_web_dictionary(language_for_messages).get(
                    "T_no_posible_modificar_reserva_por_tpv", "imposible to modify")
            elif session_manager.get(MODIFICATION_BLOCKED_BY_SOURCE):
                args['message'] = get_modification_blocked_by_source_msg(language_for_messages)

            search_engine += build_template_2("banners/_popup_reservation_not_modificable.html", args, False)

    if ab_testing_main_hotel:
        try:
            set_namespace(external_hotel_reservations)
            content = buildReservationContent(reservation[0])

        finally:
            set_namespace(actual_namespace)
    else:
        content = buildReservationContent(reservation[0])

    if wrapper_html_template:
        html_content = buildTemplate(wrapper_html_template,
                                                   {'language': get_language_code(language),
                                                    'content': search_engine + content,
                                                    "scriptsToRenderDesktop": modification_scripts_to_render(language)}, allowMobile=allow_mobile)
    else:
        html_content = search_engine + content

    if reservation and not reservation[0].cancelled and want_json_response:
        json_booking = transform_reservation_to_json(reservation[0])
        response_utils.add_to_response_content(json_booking)
        return ''

    response_utils.add_to_response_content(html_content)
    return ''


def build_booking_engine_for_modification(reservation, reservation_is_modificable, target_template=None):
    # TODO: All this need to be refactored to bookings_modifier.py,
    # but for this also need to be refactored self.buildSearchEngine

    if not target_template:
        target_template = "booking/booking_engine_2/motor_busqueda_modificaciones.html"

    pre_advise = ""

    overwriteProperties = {}
    webPageProperty = getRegimen(reservation.regimen, "SPANISH").get("regimenName")
    if webPageProperty and len(webPageProperty) > 0:
        selectedRegimen = webPageProperty[0].value
    else:
        selectedRegimen = ""

    location_reservation_modification_buttonsearch_config = get_config_property_value(advance_configs_names.LOCATION_RESERVATION_MODIFICATIONS_CUSTOMTEXT_BUTTONSEARCH)
    if location_reservation_modification_buttonsearch_config:
        location_reservation_modification_buttonsearch_section = get_section_from_section_spanish_name(location_reservation_modification_buttonsearch_config, reservation.language)
        if location_reservation_modification_buttonsearch_section:
            overwriteProperties['custom_text_search_button'] = location_reservation_modification_buttonsearch_section.get("subtitle", "")

    logging.info("reservation regimen filter: %s", selectedRegimen)

    rate_model = get_rate_model(reservation.rate)

    overwriteProperties["rate_filter_only_room"] = rate_model.localName
    overwriteProperties["board_filter_only_room"] = selectedRegimen.lower()
    if get_config_property_value(advance_configs_names.MODIFY_RESERVATION_IN_ADAPTER) and get_config_property_value(advance_configs_names.MODIFY_RESERVATION_IN_ADAPTER).lower() == "prestige":
        overwriteProperties["email_for_modification"] = reservation.email

        # this is the client from "mis reservas!"
        if request.values.get("action") == "modifyReservationFromIframe":
            overwriteProperties["force_target_form_to"] = "_parent"

        if request.values.get("action") == "modifyReservationFromManager":
            overwriteProperties['modifyReservationFromManager'] = True
            overwriteProperties['reservation_language'] = reservation.language

        redirect = RedirectModificationController()
        if redirect and redirect.hotels:
            overwriteProperties['hotels'] = redirect.getHotels()
            overwriteProperties['hotel_source'] = redirect.getHotelSource()

        pre_advise = ""

        im_hotelier_from_manager = request.values.get("action", "") == "modifyReservationFromManager"

        if im_hotelier_from_manager and not reservation_is_modificable:
            message_be_careful = get_web_dictionary(reservation.language).get(
                "T_atencion_modificacion_posible_gastos", "Reservation with cancellation fees")

            pre_advise = "<div class='modify_reservation_widget alert-warning' style='padding: 20px;'>" + message_be_careful + "</div>"

    overwriteProperties['language'] = reservation.language
    overwriteProperties['namespace'] = get_namespace()

    if get_config_property_value(advance_configs_names.DEFAULT_PROMOCODE) and get_config_property_value(advance_configs_names.DEFAULT_PROMOCODE_FOR_MODIFICATION):
        overwriteProperties['promocode'] = get_config_property_value(advance_configs_names.DEFAULT_PROMOCODE)

    if request.values.get("logged_agency"):
        overwriteProperties['logged_agency'] = request.values.get("logged_agency")

    booking_identifier = reservation.identifier

    search_engine = pre_advise + "<div class='modify_reservation_widget'>" + buildSearchEngine(reservation.language, overwriteProperties=overwriteProperties, template_name=target_template, location_reservation_modificacion=booking_identifier, allowMobile=False) + "</div>"

    return search_engine


def modification_scripts_to_render(language, html_render="scripts/scripts_to_render_desktop.html",
                                   dict_params={}):

    if user_agent_is_mobile():
        return ''

    params = {
        "allow_messages": True,
        "allow_jquery_i18": True,
        "allow_jquery_ui": True,
        "allow_datepicker": True,
        "allow_selectric": True,
        "allow_fancybox": True,
        "allow_lightbox": True,
        "allow_unveil": True,
        "allow_spin": True,
        "allow_revolution": True,
        "language_js": get_language_code(language)
    }

    if dict_params:
        for k, v in dict_params.items():
            params[k] = v

    return build_template_2(html_render, params)


def _get_internal_url():
    actual_namespace = get_namespace()
    actual_appid = get_application_id()

    if actual_namespace and (not actual_namespace == actual_appid):
        url_builded = "https://%s-dot-%s.appspot.com" % (actual_namespace, actual_appid)

    else:
        url_builded = "https://%s.appspot.com" % actual_appid

    logging.info("Internal appspot url built: %s", url_builded)

    return url_builded


def send_promocode_email(sectionBefore='promocode-text.html', sectionAfter='promocode-footer.html',
                         custom_data=None):
    """
    Build and send an email with a promocode to the client
    """

    # We check if the requets language is bigger than 3 characters, that means is not a path
    if custom_data:
        if custom_data.get("language") and len(custom_data.get("language")) < 3:
            language = get_language_in_path("/" + custom_data.get("language"))
        else:
            language = custom_data.get("language")
    else:
        if request.values.get("language") and len(request.values.get("language")) < 3:
            language = get_language_in_path("/" + request.values.get("language"))
        else:
            language = request.values.get("language")

    if not language:
        language = get_default_language()

    try:
        languageDict = get_web_dictionary(language)
    except:
        languageDict = get_web_dictionary(SPANISH)
        logging.warning('Exception: Language error in send_promocode_email()')
        notify_exception('[Hotel Webs] Exception: Language error in send_promocode_email()',
                                              "At application (%s) and namespace (%s) has occurred an error with the language in the function send_promocode_email()" % (
                                                  get_application_id(),
                                                  get_namespace()))

    if get_config_property_value(advance_configs_names.NEW_PROMOCODE_EMAIL):
        send_new_promocode_email(request.values.get("email"), language, languageDict, custom_data)
        return

    if custom_data:
        email = custom_data.get("email")
    else:
        email = request.values.get("email")
    if not email:
        logging.info("Ignoring promocode request with missing email")
        return

    subject_email = languageDict["T_envio_promocode"]

    promocode = get_config_property_value(advance_configs_names.PROMOCODE_IN_EMAIL)

    specialPresent = ""
    special_present = get_config_property_value(advance_configs_names.PRESENT_IN_EMAIL)
    if special_present:
        subject_email = languageDict["T_envio_regalo"] + ": " + special_present
        specialPresent = subject_email

    if not promocode and not specialPresent and (not custom_data or not request.values.get('empty_promocode')):
        logging.info("Ignoring promocode request with missing Promocode or Special Present")
        return

    try:
        promocode2_section = get_section_from_section_spanish_name('ultimate promocode', language)
        promocode2_section_properties = get_properties_for_entity(promocode2_section.get('key'), language)
        if promocode2_section_properties.get('subject'):
            subject_email = unescape(promocode2_section_properties['subject'])

    except:
        promocode2_section = False

    if promocode2_section:
        args = {
            'promocodeContent': promocode2_section.get('content', ''),
            'promocodeImages': promocode2_section.get('pictures', []),
            'promocode': promocode,
            'specialPresent': specialPresent
        }

        if not custom_data and not promocode and request.values.get('empty_promocode'):
            args['promocode'] = ''

        if (len(args['promocodeImages']) > 1):
            args['promocodeContent'] = args['promocodeContent'].replace('@@header_image@@',
                                                                        args['promocodeImages'][0])
            args['promocodeContent'] = args['promocodeContent'].replace('@@present_image@@',
                                                                        args['promocodeImages'][1])
        args['promocodeContent'] = args['promocodeContent'].replace('@@promocode@@', args['promocode'])

    else:
        args = {
            'promocodeText': get_section_from_section_spanish_name(sectionBefore, language).get('content', ''),
            'promocodeFooter': get_section_from_section_spanish_name(sectionAfter, language).get('content', ''),
            'promocode': promocode,
            'specialPresent': specialPresent
        }

    args = dict(list(args.items()) + list(languageDict.items()))
    if promocode2_section:
        content = buildTemplate('email/promocodeFormConfirmation2.html', args, allowMobile=False)
    else:
        content = buildTemplate('email/promocodeFormConfirmation.html', args)

    if not custom_data and request.values.get('subject_mail'):
        subject_email = request.values.get('subject_mail')

    send_email(email, subject_email, content, content)


def send_thanks_email(section_name):
    if request.values.get("language") and len(request.values.get("language")) < 3:
        language = get_language_in_path("/" + request.values.get("language"))
    else:
        language = request.values.get("language")

    try:
        languageDict = get_web_dictionary(language)
    except:
        languageDict = get_web_dictionary(SPANISH)
        logging.warning('Exception: Language error in send_thanks_email()')
        notify_exception('[Hotel Webs] Exception: Language error in send_thanks_email()',
                                              "At application (%s) and namespace (%s) has occurred an error with the language in the function send_thanks_email()" % (
                                                  get_application_id(),
                                                  get_namespace()))

    email = request.values.get('email')
    subject_email = languageDict["T_gracias_newsletter"]
    thanks_email_section = get_section_from_section_spanish_name(section_name, language)

    if email and thanks_email_section:
        logotype = getLogotype()
        title = thanks_email_section.get('subtitle', '')
        name = request.values.get('name')
        if name and "@@name@@" in title:
            title = title.replace("@@name@@", name)

        args = {
            'title': title,
            'content': thanks_email_section.get('content', ''),
            'logotype': logotype
        }
        args = dict(args.items() + languageDict.items())
        content = buildTemplate('email/newsletter_notification.html', args, allowMobile=True)

        sendEmail(email, subject_email, content, content)


#@timed_cache(minutes=5, key_builder=lambda x: "send_new_promocode_email_%s_%s" % (x[1], x[3] if len(x) > 3 else None))
@managers_cache(ttl_seconds=60*5, key_generator=lambda f, a, k: "send_new_promocode_email_%s_%s" % (a[1], a[3] if len(a) > 3 else None), only_thread_local_and_memory=True, entities='WebSection,Picture,ConfigurationProperty')
def send_new_promocode_email(email, language, languageDict, custom_data=None):
    if not language:
        language = "SPANISH"

    if custom_data:
        email = custom_data.get("email")

    if not email:
        logging.info("Ignoring promocode request with missing email")
        return

    if not custom_data and request.values.get("subject_mail"):
        subject_email = request.values.get('subject_mail')
    else:
        subject_email = languageDict["T_envio_promocode"]

    mini_dict = {}
    section_name = "_promocode_email"
    email_section = get_section_from_section_spanish_name_with_properties(section_name, language)
    if email_section:
        mini_dict["section"] = email_section
        if email_section.get("subject"):
            subject_email = email_section.get("subject")

        email_section_pics = get_pictures_from_section_name(section_name, language)
        for pic in email_section_pics:
            pic.update(get_properties_for_entity(pic.get('key', False), language))

            # Expected blocks
            if pic.get("title") == "logo_block":
                mini_dict["logo_block"] = pic
            elif pic.get("title") == "welcome_block":
                if pic.get("extra_text"):
                    pic["extra_text"] = unescape(pic.get("extra_text"))
                mini_dict["welcome_block"] = pic
            elif pic.get("title") == "promocode_block":
                if pic.get("extra_text"):
                    pic["extra_text"] = unescape(pic.get("extra_text"))
                if not pic.get("promocode"):
                    pic["promocode"] = get_config_property_value(advance_configs_names.PROMOCODE_IN_EMAIL)
                if pic.get("without_promocode"):
                    pic["without_promocode"] = True
                mini_dict["promocode_block"] = pic

        version = get_config_property_value(advance_configs_names.NEW_PROMOCODE_EMAIL)
        content = buildTemplate('email/newsletter_promocode/' + version + '/promocode_email.html', mini_dict,
                                     allowMobile=False)

        # For local testing only
        if not custom_data and request.values.get("view_html") and DEV:
            response_utils.add_to_response_content(content)
        else:
            send_email(email, subject_email, content, content)


def getFloatValue(value):
    if value:
        return float(value)
    else:
        return 0


def buildRooms(reservation):

    if reservation.extraInfo:
        reservation_extra_info = json.loads(reservation.extraInfo)
    else:
        reservation_extra_info = {}

    price_room_1 = 0
    price_room_2 = 0
    price_room_3 = 0

    if reservation_extra_info:
        price_room_1 = reservation_extra_info.get('price_room_1', 0)
        price_room_2 = reservation_extra_info.get('price_room_2', 0)
        price_room_3 = reservation_extra_info.get('price_room_3', 0)

    result = []
    if reservation_extra_info and reservation_extra_info.get("shopping_cart_human_read"):
        for room in reservation_extra_info["shopping_cart_human_read"]["rooms"]:
            ocupancy = room.get("occupancy", "0-0-0").split("-")
            result.append({
                'numAdults': ocupancy[0],
                'numKids': ocupancy[1],
                'numBabies': ocupancy[2],
                'price': room.get('price', 0)
            })
        return result

    if reservation.numRooms == 1:
        price_room_1 = reservation.price

    result = [{'numAdults': reservation.adults1, 'numKids': reservation.kids1, 'numBabies': reservation.babies1,
               'price': price_room_1}]

    if reservation.numRooms > 1:
        result.append(
            {'numAdults': reservation.adults2, 'numKids': reservation.kids2, 'numBabies': reservation.babies2,
             'price': price_room_2})

    if reservation.numRooms > 2:
        result.append(
            {'numAdults': reservation.adults3, 'numKids': reservation.kids3, 'numBabies': reservation.babies3,
             'price': price_room_3})

    return result


def buildReservationContent(reservation):

    # TODO provide the titles in different languages
    totalPrice = float(reservation.price) + getFloatValue(reservation.priceSupplements)

    language = get_language_based_on_request()

    if reservation.language:
        language = reservation.language
        logging.info("forcing language cancellation text to %s ", language)

    rooms = buildRooms(reservation)

    searchDictionary = {'startDate': reservation.startDate, 'endDate': reservation.endDate, 'rooms': rooms}
    if reservation.extraInfo:
        extra_info = json.loads(reservation.extraInfo) or {}
        searchDictionary['hidden_default_promocode'] =  extra_info.get('hidden_promocode_applied') or ''

    search = buildSearch_2(searchDictionary, language, True)


    hide_cancellation_conditions = get_config_property_value(advance_configs_names.HIDE_CANCELLATION_RATE_CONDITIONS)
    rate_conditions = get_rate_conditions_from_reservation(reservation, language, avoid_session=True)
    if rate_conditions and not hide_cancellation_conditions:
        # TODO: This need to be calculated automatically
        rate_conditions = rate_conditions.replace("@@cancellation_dates@@", "")

    else:
        rate_conditions = ""

    if reservation.extraInfo:
        reservation_extra_info = json.loads(reservation.extraInfo)
    else:
        reservation_extra_info = {}

    reservation_rooms = []

    if reservation_extra_info.get("shopping_cart_human_read", {}):
        shopping_cart = reservation_extra_info['shopping_cart_human_read']
        for room in shopping_cart.get("rooms", {}):
            reservation_room = {}
            reservation_room['roomName'] = room.get("room_name", "")
            reservation_room['regimenName'] = room.get("rate_name", "")
            reservation_room['rateName'] = room.get("board_name", "")
            reservation_rooms.append(reservation_room)
    else:
        webPageProperty = getRegimen(reservation.regimen, language).get("regimenName")
        if webPageProperty and len(webPageProperty) > 0:
            regimenName = webPageProperty[0].value
        else:
            regimenName = ""

        webPageProperty = getRate(reservation.rate, language).get("rateName")
        if webPageProperty and len(webPageProperty) > 0:
            rateName = webPageProperty[0].value
        else:
            rateName = ""

        rooms_list = [reservation.roomType1, reservation.roomType2, reservation.roomType3]
        for room in rooms_list:
            if room:
                reservation_room = {}
                webPageProperty = getRoom(room, language).get("roomName")
                if webPageProperty and len(webPageProperty) > 0:
                    reservation_room['roomName'] = webPageProperty[0].value
                else:
                    reservation_room['roomName'] = ""

                reservation_room['regimenName'] = regimenName
                reservation_room['rateName'] = rateName
                reservation_rooms.append(reservation_room)

    selectedServices = reservation_extra_info.get('additional_services_keys')
    if not selectedServices:
        selectedServices = unescape(reservation.additionalServices).split(";")
    else:
        selectedServices = unescape(selectedServices).split(";")

    supplements_list = [None] * len(selectedServices)

    if selectedServices:
        for i in range(len(selectedServices)):

            supplements_name_translated = False
            if " - name" in selectedServices[i]:
                keyService = selectedServices[i].split(" - name")
                supplements_name_translated = directDataProvider.get('WebPageProperty', {'languageKey': language,
                                                                                         'entityKey': keyService[0],
                                                                                         'mainKey': 'supplementName'})

            # Control 'bars' at title of supplement
            infoService = selectedServices[i].split("- cantidad")
            if "-" in infoService[0]:
                newService = infoService[0].replace("-", "@@")
                selectedServices[i] = selectedServices[i].replace(infoService[0], newService)

            infoSupplement = selectedServices[i].split(" - ")
            if len(infoSupplement) == 4:
                supplements_list[i] = {}
                if supplements_name_translated:
                    datastore_supp_name = supplements_name_translated[0].value
                    if '@@' in datastore_supp_name:
                        datastore_supp_name = datastore_supp_name.split("@@")[-1]
                    supplements_list[i]['name'] = unescape(datastore_supp_name)
                else:
                    supplements_list[i]['name'] = infoSupplement[0].replace('@@', '-')
                supplements_list[i]['amount'] = infoSupplement[1].split(":")[1]
                supplements_list[i]['days'] = infoSupplement[2]
                supplements_list[i]['price'] = infoSupplement[3].split(":")[1]

            else:
                del supplements_list[i]

    currency = get_selected_currency_code()
    session_manager.set(CURRENCY, currency)
    modification_blocked_by_source_msg = get_modification_blocked_by_source_msg(language)

    template = {'search': search,
                'totalPrice': totalPrice,
                'currency': reservation_extra_info.get('currency') or get_currency_symbol(),
                'rooms': reservation_rooms,
                'rateConditions': rate_conditions,
                'price': reservation.price,
                'priceSupplements': getFloatValue(reservation.priceSupplements),
                'supplements': supplements_list,
                'fitToMainPage': True,
                'reservationId': str(reservation.key),
                'hotel_namespace': get_namespace(),
                'cancelled': reservation.cancelled,
                'booking3_vat_included': get_config_property_value(advance_configs_names.BOOKING3_VAT_INCLUDED),
                'status_reservation_message': build_html_status_message_for_reservation(reservation)
                }

    if modification_blocked_by_source_msg:
        template['modification_blocked_by_source_msg'] = modification_blocked_by_source_msg

    if reservation.extraInfo:
        country_tax = json.loads(reservation.extraInfo).get("price_info", {}).get("taxes", {}).get("country", {})
        taxes_included = country_tax
        if isinstance(country_tax, dict):
            taxes_included = country_tax.get("included")
        template['taxes_included'] = taxes_included

    popup_cancel_section = get_config_property_value(advance_configs_names.MY_BOOKING_CANCELATION_POPUP)
    if popup_cancel_section:
        template['popup_not_cancel_section_properties'] = get_section_from_section_spanish_name_with_properties(
            popup_cancel_section, language)
        max_price = template['popup_not_cancel_section_properties'].get('max_price', '0')
        if getFloatValue(max_price) <= totalPrice:
            template['open_popup_info'] = True

    if request.values.get('hide_default_stylesheet'):
        template['hide_default_stylesheet'] = True

    if request.values.get('include_analytics_data'):
        conversion_label = get_config_property_value(advance_configs_names.GOOGLE_ADS_CONVERSION_LABEL)
        analytics_data = {
            'conversion_label': conversion_label,
            'namespace': template.get('hotel_namespace'),
            'identifier': reservation.identifier,
            'total_price': template.get('totalPrice'),
            'currency': template.get('currency'),
            'start_date': datetime.strptime(reservation.startDate, '%Y-%m-%d').strftime('%Y_%m_%d'),
            'end_date': datetime.strptime(reservation.endDate, '%Y-%m-%d').strftime('%Y_%m_%d')
        }

        template['analytics_data'] = json.dumps(analytics_data)


    popup_cancel_custom_configs = get_section_from_section_spanish_name_with_properties(
        '_my_bookings_popup_cancel_custom_configs', language)
    if popup_cancel_custom_configs:
        if popup_cancel_custom_configs.get('properties_from_corporate'):
            actual_namespace = get_namespace()
            try:
                set_namespace(popup_cancel_custom_configs.get('properties_from_corporate'))
                popup_cancel_custom_configs = get_section_from_section_spanish_name_with_properties(
                    '_my_bookings_popup_cancel_custom_configs', language)

            finally:
                set_namespace(actual_namespace)

        template['custom_properties_popup'] = popup_cancel_custom_configs

    myParams = {}
    myParams.update(template)
    myParams.update(get_web_dictionary(language))

    content = buildTemplate('booking_2/reservationSummary.html', myParams)

    return content


def buildCancellationConfirmation(reservation, isHtml, for_manager=False, forBrowser=False):
    language = get_language_based_on_request()

    if reservation.language:
        language = reservation.language
        logging.info("forcing language cancellation template to %s ", language)

    if isHtml and not for_manager:
        reservation_info = get_reservation_info(reservation, reservation_cancelation=True)
        email_content = build_email_from_ws(EMAIL_BOOKING_CANCELLATION, language, reservation_info=reservation_info)
        if email_content:
            return email_content

    if for_manager:
        management_language = get_config_property_value(advance_configs_names.MANAGEMENT_LANGUAGE)
        if management_language:
            language = management_language
        logging.info("forcing language cancellation template to %s ", language)

    rooms = buildRooms(reservation)
    endDate = reservation.endDate
    startDate = reservation.startDate
    searchDictionary = {'startDate': startDate,
                        'endDate': endDate,
                        'rooms': rooms
                        }

    search = buildSearch_2(searchDictionary, language, isHtml)

    selectedRoom = ""
    num_rooms = reservation.numRooms
    if reservation.extraInfo:
        extra_info = json.loads(reservation.extraInfo)
        extra_info_reservation_cart = extra_info.get("shopping_cart")
        if extra_info_reservation_cart:
            num_rooms = len(extra_info_reservation_cart)

    for room_number in range(1, num_rooms + 1):
        room_key = get_room_key_from_prices_per_day(room_number, reservation)
        selectedRoom += get_room(room_key, language).get('room_name', '') if room_number == 1 else f' || {get_room(room_key, language).get("room_name", "")}'

    webPageProperty = getRegimen(reservation.regimen, language).get("regimenName")
    if webPageProperty and len(webPageProperty) > 0:
        selectedRegimen = webPageProperty[0].value
    else:
        selectedRegimen = ""

    webPageProperty = getRate(reservation.rate, language).get("rateName")
    if webPageProperty and len(webPageProperty) > 0:
        selectedRate = webPageProperty[0].value
    else:
        selectedRate = ""

    totalPrice = float(reservation.price) + getFloatValue(reservation.priceSupplements)

    currency = get_selected_currency_code()
    session_manager.set(CURRENCY, currency)

    if for_manager and getLogotypes("logo_cancellation_manager"):
        logotype = getLogotypes("logo_cancellation_manager")[0]
    elif getLogotypes("logo_cancellation"):
        logotype = getLogotypes("logo_cancellation")[0]
    elif getLogotypes("email reserva"):
        logotype = getLogotypes("email reserva")[0]
    else:
        logotype = getLogotype()

    template = {
        'search': search,
        'currency': get_currency_symbol(),
        'room': selectedRoom,
        'regimen': selectedRegimen,
        'rate': selectedRate,
        'price': totalPrice,
        'roomPrice': reservation.price,
        'priceSupplements': getFloatValue(reservation.priceSupplements),
        'supplements': rebuild_additional_services(reservation),
        'advanced_supplements': get_selected_advanced_supplements_from_reservation(reservation, language, forManager=for_manager),
        'forBrowser': forBrowser,
        'reservation': reservation,
        'logotype': logotype,
        'domainLink': get_config_property_value(advance_configs_names.KEY_DOMAIN)
    }

    confirmation_logos = getLogotypes("email reserva")
    if confirmation_logos and len(confirmation_logos) > 0:
        template['confirmation_logo'] = confirmation_logos[0]

    webConfiguration = get_config_property_value(advance_configs_names.WEB_CONFIGURATION)
    if webConfiguration:
        configurationOptions = webConfiguration.split(';')
        for option in configurationOptions:
            if 'alternativeLinkLogo' in option:
                template['alternativeLinkLogo'] = option.split("=")[1]

    if for_manager:
        template['forManager'] = True
    if get_config_property_value(advance_configs_names.INCLUDE_CONDITIONS_IN_RESERVATION):
        template['rate_conditions'] = rate_info_calculator.get_rate_conditions_from_reservation(reservation, language, avoid_session=True)  # getSelectedRateConditions([reservation.rate], language)

    if get_config_property_value(advance_configs_names.CANCELLATION_EMAIL_HIDDEN_CONTENT):
        template['hide_cancellation_content'] = True

    if get_config_property_value(advance_configs_names.BOOKING_4_BANNER_IMAGES):
        section_booking4_banner_images = get_config_property_value(advance_configs_names.BOOKING_4_BANNER_IMAGES)
        template['booking4_banner_images_content'] = get_section_from_section_spanish_name(section_booking4_banner_images, language)
        template['booking4_banner_images'] = get_pictures_from_section_name(section_booking4_banner_images, language)

    reservationDictionary = build_reservation_dictionary(reservation)

    template['email_header'] = get_cancellation_header(language)
    template['email_header'] = template['email_header'].replace("@@transfer_payment@@", '')
    template['email_header'] = template['email_header'].replace("@@name@@", reservationDictionary['name'])
    template['email_header'] = template['email_header'].replace("@@surname@@", reservationDictionary['lastName'])

    rate_text = get_section_from_section_spanish_name("Email cancellation rate message", language)
    if rate_text:
        email_header_advance = get_properties_for_entity(rate_text['key'], language)
        rate_cancelation_text = ""
        for section_property_key in email_header_advance:
            if reservation.rate in section_property_key.split(";"):
                rate_cancelation_text += email_header_advance.get(section_property_key)
        template['email_header'] = template['email_header'].replace("@@rate_text@@", rate_cancelation_text)

    template['email_header_text'] = get_text_version(template['email_header'])

    template['email_footer'] = get_cancellation_footer(language)
    template['email_footer_text'] = get_text_version(template['email_footer'])

    template['hotel_name'] = get_hotel_name()
    template['applicationId'] = get_namespace()
    template['is_cancellation'] = True

    completeDictionary = {}
    completeDictionary.update(template)
    completeDictionary.update(reservationDictionary)
    completeDictionary.update(get_web_dictionary(language))

    completeDictionary['T_confirmacion_reserva'] = get_web_dictionary(language)['T_cancelacion_reserva']

    cancellation_on_request = get_config_property_value(advance_configs_names.CANCELLATION_ON_REQUEST)
    if cancellation_on_request:
        completeDictionary['T_confirmacion_reserva'] = get_web_dictionary(language).get("T_peticion_cancelacion")

    if not completeDictionary.get('incidents'):
        completeDictionary['incidents'] = request.values.get('comments')

    hotel_country_location = get_config_property_value(advance_configs_names.HOTEL_COUNTRY_LOCATION)
    if hotel_country_location:
        completeDictionary['default_currency'] = get_currency()

    reservation_properties = get_integration_configuration_properties(RESERVATION_PROPERTIES)
    completeDictionary.update(reservation_properties)

    if isHtml:
        reservation_confirmation_template = get_config_property_value(advance_configs_names.RESERVATION_CONFIRMATION_EMAIL)
        avoid_custom_template_for_cancellation_email_hotel = get_config_property_value(advance_configs_names.NOT_CUSTOM_EMAIL_FOR_CANCELLATION_HOTEL)
        if for_manager and avoid_custom_template_for_cancellation_email_hotel:
            avoid_custom_template = True
        else:
            avoid_custom_template = False
        if reservation_confirmation_template and not avoid_custom_template:
            completeDictionary['numRooms'] = reservation.numRooms
            completeDictionary['numDays'] = (datetime.strptime(endDate, SEARCH_DATE_FORMAT) - datetime.strptime(startDate, SEARCH_DATE_FORMAT)).days

            hide_cancellation_conditions = get_config_property_value(advance_configs_names.HIDE_CANCELLATION_RATE_CONDITIONS)
            if not hide_cancellation_conditions:
                completeDictionary['rate_conditions'] = rate_info_calculator.get_rate_conditions_from_reservation(reservation, language, True)
                if completeDictionary.get('rate_conditions'):
                    completeDictionary['rate_conditions'] = completeDictionary['rate_conditions'].replace("@@cancellation_dates@@", "")
            else:
                completeDictionary['rate_conditions'] = ''

            if not completeDictionary.get("occupancy_string"):
                completeDictionary['occupancy_string'] = ''
                adults_number = int(reservation.adults1)
                if reservation.adults2:
                    adults_number = adults_number + int(reservation.adults2)
                if reservation.adults3:
                    adults_number = adults_number + int(reservation.adults3)
                completeDictionary['occupancy_string'] = str(adults_number)
                completeDictionary['occupancy_string'] += " " + get_web_dictionary(language).get('T_adultos')
                kids_number = int(reservation.kids1)
                if reservation.adults2:
                    kids_number = kids_number + int(reservation.kids2)
                if reservation.adults3:
                    kids_number = kids_number + int(reservation.kids3)
                if kids_number:
                    completeDictionary['occupancy_string'] += ", " + str(kids_number)
                    completeDictionary['occupancy_string'] += " " + get_web_dictionary(language).get('T_ninos')
                babies_number = int(reservation.babies1)
                if reservation.adults2:
                    babies_number = babies_number + int(reservation.babies2)
                if reservation.adults3:
                    babies_number = babies_number + int(reservation.babies3)
                if babies_number:
                    completeDictionary['occupancy_string'] += ", " + str(babies_number)
                    completeDictionary['occupancy_string'] += " " + get_web_dictionary(language).get('T_bebes')

            email_header_with_properties = get_section_from_section_spanish_name_with_properties("Email Header", language)
            email_header_properties = ['email_header_title', 'forced_style_logo_cancellation',
                                       'forced_style_logo_img_cancellation', 'custom_label_styles',
                                       'contact_custom_title', 'hide_phone_label', 'cancellation_header_title',
                                       'phone_extra_text', 'logo_font_color']
            for prop_element in email_header_properties:
                if email_header_with_properties.get(prop_element):
                    completeDictionary[prop_element] = email_header_with_properties.get(prop_element)

            if reservation_confirmation_template == "v4":

                completeDictionary['username'] = reservation.name
                completeDictionary['userlastname'] = reservation.lastName
                completeDictionary['usermail'] = reservation.email
                completeDictionary['userphone'] = reservation.telephone

                completeDictionary['checkinDate'] = reservation.startDate
                completeDictionary['checkoutDate'] = reservation.endDate
                completeDictionary['v4_section'] = get_section_from_section_spanish_name("_confirmation_email_v4", language)
                if completeDictionary['v4_section']:
                    if completeDictionary['v4_section'].get('content'):
                        completeDictionary['v4_section']['content'] = completeDictionary['v4_section']['content'].replace("@@username@@", reservation.name)
                        completeDictionary['v4_section']['content'] = completeDictionary['v4_section']['content'].replace("@@userlastname@@", reservation.lastName)
                        formated_startdate = get_localized_date(reservation.startDate, language, "@day_name@ @day@ @month@ @year@")
                        completeDictionary['v4_section']['content'] = completeDictionary['v4_section']['content'].replace("@@arrival_date@@", formated_startdate)

                    v4_pics = getPicturesForKey(language, completeDictionary['v4_section'].get('key'))
                    for pic in v4_pics:
                        if pic.get('title') == "header":
                            completeDictionary['v4_header'] = pic
                        if pic.get('title') == "phone":
                            completeDictionary['v4_phone'] = pic
                        if pic.get('title') == "email":
                            completeDictionary['v4_email'] = pic
                        if pic.get('title') == "extra_banner":
                            pic['description'] = pic.get("description", "").replace("link_extra_booking_4", completeDictionary.get('link_extra_booking_4', ""))
                            completeDictionary['v4_extra_banner'] = pic
                        if pic.get('title') == "footer_logo":
                            completeDictionary['v4_footer_logo'] = pic
                        if pic.get('title') == "custom_datos_reserva_title":
                            completeDictionary['custom_datos_reserva_title'] = pic.get("description")
                    completeDictionary['v4_footer_links'] = filter(lambda x: x.get('title') == "footer_link", v4_pics)

            if reservation_confirmation_template == "v7":
                completeDictionary.update(confirmation_v7_context(language, reservation))

            content = buildReservationConfirmationTemplate(reservation_confirmation_template, completeDictionary, language, True)
        else:
            content = buildTemplate('booking_2/reservationConfirmation.html', completeDictionary)
    else:  # Text
        content = buildTemplate('email/reservationConfirmationText.html', completeDictionary)

    return content


def buildConfirmationAvailabilityClient(reservation, isHtml, direction):
    language = get_language_based_on_request()
    to_hotel = request.values.get("to_hotel")

    if to_hotel:
        language = get_config_property_value(advance_configs_names.DEFAULT_WEB_LANGUAGE)
        if not language:
            language = "SPANISH"

    elif reservation.language:
        language = reservation.language
        logging.info("forcing language cancellation template to %s ", language)

    completeDictionary = get_info_reservation_for_email(reservation, isHtml, language)

    completeDictionary['T_confirmacion_reserva'] = completeDictionary.get("T_confirmacion_solicitud")

    widget_only = get_config_property_value(advance_configs_names.WIDGET_ONLY)
    if not widget_only:
        url_link = get_config_property_value(advance_configs_names.KEY_DOMAIN)
    else:
        url_link = get_config_property_value(advance_configs_names.CUSTOM_DOMAIN)

    reservation_identifier = reservation.identifier
    token = request.values.get("token")

    if to_hotel:
        completeDictionary['no_welcome_message'] = True
        payment_gateway = get_config_property_value(advance_configs_names.USE_PAYMENT_GATEWAY)
        if payment_gateway and "payjp" in payment_gateway.lower():
            url_link += "/payjp/payment_screen?identifier=%s&token=%s" % (reservation_identifier, token)
            completeDictionary['email_header'] = completeDictionary.get("T_disponibilidad_confirmada_hotel") % url_link
    else:
        url_link += "/onrequest/availability_screen?identifier=%s&token=%s" % (reservation_identifier, token)
        completeDictionary['email_header'] = completeDictionary.get(
            "T_disponibilidad_confirmada_cliente") % url_link

    last_comment = request.values.get("comments")

    comments = _update_comments_onrequest_reservation(reservation, last_comment, direction)
    if comments:
        completeDictionary['email_header'] += "<br><br>%s: %s" % (completeDictionary.get("T_comentarios"), comments)

    completeDictionary["booking_on_request"] = True  # HIDE PRICES IN EMAILS!!!!!

    if isHtml:
        reservation_confirmation_template = get_config_property_value(advance_configs_names.RESERVATION_CONFIRMATION_EMAIL)
        if reservation_confirmation_template:
            content = buildReservationConfirmationTemplate(reservation_confirmation_template, completeDictionary, language, True)
        else:
            content = buildTemplate('booking_2/reservationConfirmation.html', completeDictionary)
    else:
        content = buildTemplate('email/reservationConfirmationText.html', completeDictionary)

    return content


def _update_comments_onrequest_reservation(reservation, last_comment, direction):

    comments = ""
    extra_info = {}

    if reservation.extraInfo:
        extra_info = json.loads(reservation.extraInfo)
        comments = extra_info.get("on_request_comments", "")

    if last_comment:
        today = datetime.strftime((datetime.now()), '%Y-%m-%d %H:%M:%S')

        # TODO: tranlate this
        who_comment = ""
        if direction == "tohotel":
            who_comment = "CLIENT WROTE"
        elif direction == "toclient":
            who_comment = "HOTEL WROTE"

        comment = "%s %s: %s" % (today, who_comment, last_comment)

        comments = "%s<br><br>%s" % (comments, comment)

        extra_info["on_request_comments"] = comments
        reservation.extraInfo = json.dumps(extra_info)
        reservation.put()

        logging.info("reservation updated with last comments: %s", comments)

    return comments


def get_info_reservation_for_email(reservation, isHtml, language):
    rooms = buildRooms(reservation)
    searchDictionary = {
        'startDate': reservation.startDate,
        'endDate': reservation.endDate,
        'rooms': rooms
    }

    search = buildSearch_2(searchDictionary, language, isHtml)

    webPageProperty = getRoom(reservation.roomType1, language).get("roomName")
    if webPageProperty and len(webPageProperty) > 0:
        selectedRoom = webPageProperty[0].value
    else:
        selectedRoom = ""

    webPageProperty = getRegimen(reservation.regimen, language).get("regimenName")
    if webPageProperty and len(webPageProperty) > 0:
        selectedRegimen = webPageProperty[0].value
    else:
        selectedRegimen = ""

    webPageProperty = getRate(reservation.rate, language).get("rateName")
    if webPageProperty and len(webPageProperty) > 0:
        selectedRate = webPageProperty[0].value
    else:
        selectedRate = ""

    totalPrice = float(reservation.price) + getFloatValue(reservation.priceSupplements)

    currency = get_selected_currency_code()
    session_manager.set(CURRENCY, currency)

    template = {
        'search': search,
        'currency': get_currency_symbol(),
        'room': selectedRoom,
        'regimen': selectedRegimen,
        'rate': selectedRate,
        'price': totalPrice,
        'roomPrice': reservation.price,
        'priceSupplements': getFloatValue(reservation.priceSupplements),
        'supplements': rebuild_additional_services(reservation),
        'advanced_supplements': _process_reservation_advanced_supplements(reservation),
        'forBrowser': False,
        'reservation': reservation,
        'logotype': getLogotypes("email reserva")[0] if getLogotypes("email reserva") else getLogotype(),
        'hotel_name': get_hotel_name()
    }

    if get_config_property_value(advance_configs_names.INCLUDE_CONDITIONS_IN_RESERVATION):
        template['rate_conditions'] = rate_info_calculator.get_rate_conditions_from_reservation(reservation,
                                                                                                language,
                                                                                                avoid_session=True)  # getSelectedRateConditions([reservation.rate], language)

    reservationDictionary = build_reservation_dictionary(reservation)
    completeDictionary = {}
    completeDictionary.update(template)
    completeDictionary.update(reservationDictionary)
    completeDictionary.update(get_web_dictionary(language))

    return completeDictionary


def loginUser():
    user = request.values.get("user")
    password = request.values.get("password")
    userType = request.values.get("userType")

    response_data = login_manager.login(user, password, userType)
    response_utils.add_to_response_content(response_data)
    return ""


def logout():
    login_manager.logout()


def sendCancellationEmails(reservationKey, cancellation_reasons, namespace=None, client_email=None):

    reservation = directDataProvider.getEntity('Reservation', reservationKey)

    if not reservation or _is_encrypted_booking(reservation):
        logging.critical("Reservation not found: " + reservationKey)
        return

    if cancellation_reasons:
        reservation.incidents = cancellation_reasons

    if cancellation_reasons == "SIBS MULTIBANCO CANCELATION" or cancellation_reasons == "SIBS CARD CANCELATION":
        extra_info = json.loads(reservation.extraInfo)
        if not extra_info:
            extra_info = {}
        extra_info['cancelled_by'] = "automatic SIBS"
        reservation.extraInfo = json.dumps(extra_info)

        # TODO, THIS SHOULD USE DIRECT DATA PROVIDER INSTEAD
        reservation.put()

    if get_config_property_value(SAVE_CANCELLATION_REASON_IN_INCIDENTS):
        reservation.put()

    actual_namespace = get_namespace()
    if namespace:
        set_namespace(namespace)

    try:
        contentHtml = buildCancellationConfirmation(reservation, True)
        contentTxt = buildCancellationConfirmation(reservation, False)
        # Allow to personalize email cancellation template for Hotel
        contentHtmlForManager = buildCancellationConfirmation(reservation, True, for_manager=True)
        contentTxtForManager = buildCancellationConfirmation(reservation, False, for_manager=True)
    finally:
        set_namespace(actual_namespace)

    if get_web_configuration(WHATSAPP_COMMUNICATION):
        reservation_info = get_reservation_info(reservation)
        if reservation_info['extra_info'].get('whatsapp_communication') or request.values.get('send_whatsapp'):
            target = request.values.get('custom_wa_number') or reservation.telephone
            send_whatsapp_message(WHATSAPP_BOOKING_CANCELLATION, reservation_info.get('language'), target, reservation_info=reservation_info)

    if reservation.extraInfo:
        extra_info = json.loads(reservation.extraInfo)
        paid_in_reservation = _get_total_payed_amount_from_all_sources(extra_info)

        send_email_refund = True
        if get_config_property_value(NOT_SEND_EMAIL_REFUND_PAYMENT_NR):
            logging.info("Checkin if we have to send the email in case it's refundable")
            send_email_refund = not rateIsNonRefundable(reservation.rate)
            logging.info("Rate is refundable: %s", send_email_refund)

        if paid_in_reservation and not extra_info.get("SIBS_MULTIBANCO") and not extra_info.get("gateway_type", "") == "SIBS_MULTIBANCO" and send_email_refund:
            logging.info(
                "reservation %s cancelled and with Payments already done. Sending an email to Manager hotel",
                reservation.identifier)

            logotype = ""
            logotypes = getLogotypes()
            if logotypes:
                logotype = logotypes[0]

            default_language_in_manager = get_config_property_value(advance_configs_names.MANAGEMENT_LANGUAGE)
            if not default_language_in_manager:
                default_language_in_manager = SPANISH

            languageDict = get_web_dictionary(default_language_in_manager)
            info_payment = languageDict.get("T_cancelacion_reserva_pagada_info", "").replace("@@LOCALIZADOR@@", reservation.identifier).replace("@@AMOUNT@@", str(paid_in_reservation)).replace("@@CURRENCY@@", get_currency_symbol())

            sups = 0
            if reservation.priceSupplements:
                sups = float(reservation.priceSupplements)

            reservation_info = {
                'logotype': logotype,
                'identifier': reservation.identifier,
                'numRooms': reservation.numRooms,
                'startDate': reservation.startDate,
                'endDate': reservation.endDate,
                'comments': reservation.comments,
                'price': float(reservation.price) + sups,
                'currency': extra_info.get('currency') or get_currency_symbol()
            }

            params = {"info_payment": info_payment,
                      "reservation_info": reservation_info}

            completeDictionary = {}
            completeDictionary.update(params)
            completeDictionary.update(languageDict)

            template = 'email/cancellation_with_payment.html'
            contentHtmlPaymentCancel = buildTemplate(template, completeDictionary)
            check_policies_before_send_cancellation = get_config_property_value(CHECK_POLICIES_BEFORE_SEND_CANCELLATION)
            if not request.values.get("html"):
                if not check_policies_before_send_cancellation:
                    bookingUtils.sendCancellationWithPaymentsEmailToManager(reservation, "", contentHtmlPaymentCancel, language=default_language_in_manager)
                else:
                    if not rateIsNonRefundable(reservation.key) and booking_is_modifiable(reservation, only_range=True):
                        bookingUtils.sendCancellationWithPaymentsEmailToManager(reservation, "", contentHtmlPaymentCancel, language=default_language_in_manager)

        if extra_info.get("agency_name"):
            agency_config = get_web_configuration("AgencyConfiguration")
            if agency_config:
                agency_hotel = actual_namespace
                if agency_config.get("remote login agencies"):
                    agency_hotel = agency_config['remote login agencies']

                url_base_login = ADMIN_MANAGER_SERVER_2
                if DEV:
                    url_base_login = "http://localhost:8080"

                url_base_login += "/agency_login"
                login_payload = {
                    "hotel_code": agency_hotel,
                    "username": extra_info['agency_name'],
                    "password": "booking_cancelation"
                }

                login_headers = {
                    "Content-Type": "application/json"
                }

                response_agency = requests.post(url_base_login, data=json.dumps(login_payload),
                                                headers=login_headers)
                if response_agency.status_code == 200:
                    if response_agency.text:
                        response_agency = json.loads(response_agency.text)
                        if client_email:
                            client_email += ",%s" % response_agency.get("email")

                        else:
                            client_email = response_agency.get("email")
                    else:
                        logging.warning(
                            f"Manager response code {response_agency.status_code} but there is not content. "
                            f"Can't add agency email to destinations emails\n"
                            f"Payload: {login_payload}\n"
                            f"Content: {response_agency.text}"
                        )

    if request.values.get("html"):
        response_utils.add_to_response_content(contentHtml)
        return ''

    bookingUtils.sendCancellationEmailToManager(reservation, contentTxtForManager, contentHtmlForManager)

    if not get_config_property_value(advance_configs_names.NOT_SEND_MAIL_TO_CUSTOMER):
        bookingUtils.sendCancellationEmails(reservation, contentTxt, contentHtml, email=client_email)


def sendAvailabilityConfirmationEmails(reservation, direction="", namespace=None):
    if not reservation:
        logging.critical("Reservation not found")
        return

    actual_namespace = get_namespace()
    if namespace:
        set_namespace(namespace)

    try:
        contentHtml = buildConfirmationAvailabilityClient(reservation[0], True, direction)
        contentTxt = contentHtml

    finally:
        set_namespace(actual_namespace)
    if request.values.get("to_hotel"):
        sendAvailabilityEmailToManager(reservation[0], contentTxt, contentHtml)
    else:
        sendAvailabilityEmails(reservation[0], contentTxt, contentHtml)


def processCancelReservation(language):

    render_hooks('cancell_reservation')

    reservationKey = request.values.get("reservationKey")
    forceCancellation = (request.values.get("forceCancellation", 'false').lower() == 'true')
    cancellation_reasons = request.values.get("comments", "")
    namespace = request.values.get("namespace", "")
    force_namespace = request.values.get('force_namespace', '')

    if reservationKey:
        key_attribute_model = alphanumeric_to_key(reservationKey)
        if key_attribute_model.namespace:
            logging.info(f"Setting namespace of key received: {key_attribute_model.namespace}")
            set_namespace(key_attribute_model.namespace)

    # Language overwrite
    url_from = request.referrer
    if not url_from:
        url_from = ''

    language_in_path = get_language_in_path(url_from)
    if language_in_path:
        logging.info("Language in path: %s", language_in_path)
        language = language_in_path

    if request.values.get('language'):
        language = request.values.get('language')
        logging.info("Language in request: %s", language)

    if force_namespace:
        logging.info("Forced namespace to: %s" % force_namespace)
        set_namespace(force_namespace)

    email = request.values.get("email")
    localizador = request.values.get("localizador")

    dictionary = get_web_dictionary(language)

    if request.values.get("create_gift_bono"):
        cancellation_reasons += "\n %s" % dictionary.get("T_bono_regalo")

    # Check if reservation was cancelled
    reservation = None

    if email and localizador:
        reservation = directDataProvider.get("Reservation", {"email": email.lower(), "identifier": localizador.strip().upper()}, 1)
        if reservation and reservation[0].cancelled:
            already_was_cancelled = dictionary['T_reserva_ya_cancelada']
            response_utils.add_to_response_content(already_was_cancelled)
            return ""

    # RERSERVATIONSEARCH
    if email and localizador:
        reservation = directDataProvider.get("Reservation", {"email": email.lower(), "identifier": localizador.strip().upper()}, 1)
        if reservation:
            reservationKey = str(reservation[0].key)
            logging.info("cancell reservation found for: %s and %s", email, localizador)

    # In case we have accepted to cancel this reservation, we have to force the cancellation
    if get_config_property_value(advance_configs_names.RATES_ALWAYS_CANCELABLE):
        forceCancellation = True

    if not reservation and not reservationKey:
        logging.warning('Missing reservation and not reservationKey given')
        return

    if not reservation and reservationKey:
        reservation = directDataProvider.getEntity('Reservation', reservationKey)

    if isinstance(reservation, list):
        logging.info(reservation)
        reservation = reservation[0]

    if reservation:
        session_manager.set(CANCEL_RESERVATION_DICT, reservation.to_dict())

    rate_info = get_rate_model(reservation.rate)
    # Check if reservation is cancellable
    if booking_is_modifiable(reservation, rate_info, modify=False):
        forceCancellation = True
    elif not forceCancellation:
        # If reservation is not cancellable and forceCancellation is not defined, we break execution here.
        logging.warning(f"Reservation {localizador} cannot be cancelled")
        response_utils.add_to_response_content(dictionary['T_no_posible_cancelar_reserva_por_condiciones'])
        return ""

    cancellation_on_request = get_config_property_value(advance_configs_names.CANCELLATION_ON_REQUEST)

    add_cancellation_reasons_to_extrainfo(cancellation_reasons, reservation)

    reactivate_reservation_bono(reservation)

    if not cancellation_on_request:
        if namespace:
            original_namespace = get_namespace()
            try:
                set_namespace(namespace)
                cancelled = hotelManagerCommunicator.cancelBooking(reservationKey, forceCancellation)
            finally:
                set_namespace(original_namespace)
        else:
            cancelled = hotelManagerCommunicator.cancelBooking(reservationKey, forceCancellation)

    else:
        cancelled = True

    message = ''

    cancel_transfers(language, reservation)

    target_email = request.values.get("target_email") or reservation.email
    if (invalid_email := not is_valid_email(target_email)):
        logging.warning(f'Invalid email address: {target_email}. We are not sending cancellation email.')

    no_send_cancel_email = get_config_property_value(NO_SEND_CANCEL_EMAIL) or invalid_email

    if cancelled or cancellation_on_request or DEV:
        message = dictionary['T_reserva_cancelada']
        if not no_send_cancel_email:
            try:
                sendCancellationEmails(reservationKey, cancellation_reasons, namespace, client_email=target_email)
            except Exception as e:
                error_message = makeTraceback()
                logging.error(error_message)
                notify_exception('Error trying to send cancellation email', error_message, add_hotel_info=True)

        if is_booking2_react_active() and not request.values.get('fromManager'):
            perform_booking_post_to_additional_services_seeker(reservation.identifier, action='cancellation')
    else:
        logging.info("identifier: %s, forceCancellation: %s", localizador, forceCancellation)
        logging.warning("Reservation can not be cancelled")
        message = dictionary['T_no_posible_cancelar_reserva_por_condiciones']

    if cancelled:
        external_controller = get_config_property_value(advance_configs_names.EXTERNAL_CONTROLLER)
        if external_controller:
            controller = ExternalController(external_controller, language)
            if hasattr(controller, "cancel_reservation"):
                controller.cancel_reservation('', reservation)

    if cancelled:
        try:
            if get_config_property_value(advance_configs_names.MEMBERS_CLUB):
                config = get_web_configuration(CLUB_CONFIGURATION)
                logging.info("Actual namespace: %s", get_namespace())
                logging.info("Config club: %s", config)
                if config and config.get("remove_only_with_discount"):
                    defer(users_methods.cancel_transaction_from_user, reservationKey)
                else:
                    defer(users_methods.remove_transaction_from_user, reservationKey)

            if get_config_property_value(advance_configs_names.DATALAYER_IN_BOOKING_CANCELLATION):
                google_tag_manager_id = get_config_property_value(advance_configs_names.GOOGLE_TAG_MANAGER_ID)
                if google_tag_manager_id:
                    datalayer_response_dict = {
                        'message': message,
                        'datalayer': google_tag_manager_datalayer({}, None, request, only_total_occupancy=True)
                    }
                    message = json.dumps(datalayer_response_dict)

        except Exception as e:
            logging.warning('No deferer cancel or remove transacation')
            logging.critical(e)

    if (cancelled or cancellation_on_request or DEV) and hasattr(reservation, 'extraInfo') and reservation.extraInfo:
        extra_info = json.loads(reservation.extraInfo)
        if extra_info.get(TRANSFER_SERVICE):
            send_transfer_service_provider_email(reservation.identifier, reservation.email, is_cancellation=True)

    if (cancelled or DEV) and request.values.get("create_gift_bono"):
        actual_namespace = get_namespace()
        if namespace:
            set_namespace(namespace)

        try:
            bono_controller = GiftBonoPromocodeController()
            params_bono = {
                "booking_identifier_canceled": localizador,
                "discount": request.values.get("discount")
            }

            bono_controller.create(params_bono)
            bono_controller.send_email(localizador, email, mailing=True)
        finally:
            set_namespace(actual_namespace)

    response_utils.add_to_response_content(message)
    return ""


def add_cancellation_reasons_to_extrainfo(cancellation_reasons, reservation):

    try:
        extra_info = reservation.extraInfo
        if not extra_info:
            extra_info = {}
        else:
            extra_info = json.loads(extra_info)
        extra_info['cancellation_reason'] = cancellation_reasons
        reservation.extraInfo = json.dumps(extra_info)
        reservation.put()
    except Exception as e:
        logging.warning("Cancellation reasons hasn't saved!")
        message = makeTraceback()
        logging.error(message)


def sendCancelReservation(language):

    email = request.values.get("email")
    localizador = request.values.get("localizador")
    cancellation_reasons = request.values.get("comments", "")

    # RERSERVATIONSEARCH
    if (email and localizador) or (localizador and request.values.get("html")):
        if request.values.get("html"):
            reservation = directDataProvider.get("Reservation", {"identifier": localizador.strip().upper()}, 1)
        else:
            reservation = directDataProvider.get("Reservation", {"email": email.lower(),
                                                                 "identifier": localizador.strip().upper()}, 1)
        if reservation:
            reservationKey = reservation[0].key

            sendCancellationEmails(reservationKey, cancellation_reasons)
            if not request.values.get("html"):
                response_utils.add_to_response_content("email enviados correctamente")

    return ''


def send_mail_bono():

    namespace = request.values.get("namespace", "")
    email = request.values.get("email", "")
    identifier = request.values.get("identifier", "")
    actual_namespace = get_namespace()
    if namespace:
        set_namespace(namespace)

    try:
        bono_controller = GiftBonoPromocodeController()
        bono_controller.send_email(identifier, email, mailing=True)
    finally:
        set_namespace(actual_namespace)


def reactivate_reservation_bono(reservation):
    bono_controller = GiftBonoPromocodeController()
    bono_controller.bono_reactivate(reservation)


def smart_truncate(content: str, remove_html_tags: bool = False, length: int = 100, suffix: str = '...') -> str:
    """
    Truncate the provided text. Optionally remove inner HTML.

    :param content: Text to truncate.
    :param remove_html_tags: If is True remove HTML tags inner text.
    :param length: Number of characters that the string will have.
    :param suffix: Suffix to add to the end of the string.
    :return: String truncated.
    """

    if remove_html_tags:
        cleanr = re.compile('<.*?>')
        content = re.sub(cleanr, '', content)

    return content if len(content) <= length else ' '.join(content[:length + 1].split(' ')[0:-1]) + suffix


def get_modification_blocked_by_source_msg(language: str) -> str:
    """
    Get the message to show when the reservation can't be modified because its source was blocked by advanced configuration.
    :param language: Language to use.
    :return: Message to show.
    """
    message = ""
    if session_manager.get(MODIFICATION_BLOCKED_BY_SOURCE):
        web_dictionary = get_web_dictionary(language)
        message = web_dictionary.get("T_not_modifiable_reservation_by_source","impossible to modify").replace("@@reservation_source@@", session_manager.get(MODIFICATION_BLOCKED_BY_SOURCE))
    return message

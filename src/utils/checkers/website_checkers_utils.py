import logging

import requests

CHECKER_ENDPOINT = "https://websites-checkers-n4wxnk5qqa-ew.a.run.app" # Project allocated on Test Seeker


def enqueue_checkers(hotel_code):
    """Enqueue all the checkers to be executed on the Test Seeker"""
    try:
        logging.info("Adding hotel to be checked")
        requests.post(CHECKER_ENDPOINT + "/admin_controller/test_specific_hotel", data={"target_hotel": hotel_code})
    except Exception as e:
        pass

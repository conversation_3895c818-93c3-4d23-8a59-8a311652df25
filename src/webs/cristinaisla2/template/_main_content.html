{# Top banners #}
{% if banners_top %}
    </div>
    <div class="banners_top_wrapper">
        {% for banner_element in banners_top %}
            <div class="banner_element">
                {% if banner_element.linkUrl %}<a href="/{{ language }}/{{ banner_element.linkUrl }}">{% endif %}
                <img class="background_image" src="{{ banner_element.servingUrl }}">

                <div class="overlay_top_banner"></div>
                <img class="corchet_image" src="/img/{{ base_web }}/corchete_border.png">
                {% if banner_element.title %}
                    <div class="banner_text_wrapper">
                        <h3 class="banner_title">{{ banner_element.title|safe }}</h3>
                    </div>
                {% endif %}
                {% if banner_element.linkUrl %}</a>{% endif %}
            </div>
        {% endfor %}
    </div>
    <div id="wrapper_content" class="container12">
{% endif %}

{% if Inicio %}
    {% if banner_ventajas and popup_ventajas %}
        </div>

        <div class="banner_ticks"{% if banner_ventajas_color %}
             style="background-color: {{ banner_ventajas_color|safe }};"{% endif %}>

            <div class="overlay"></div>

            <div class="banner_ticks_wrapper container12">
                {% if banner_ventajas_section.subtitle %}
                    <h2 class="title_background">{{ banner_ventajas_section.subtitle|safe }}</h2>
                {% endif %}
                {% for banner in banner_ventajas %}
                    {% if not 'ver_mas' in banner.title|safe|lower %}
                        <div class="tick1 ticks">
                            {% if banner.description %}
                                <i class="fa {{ banner.description|safe }}"><span class="overlay"></span></i>
                            {% else %}
                                <div class="tick_image_wrapper">
                                    <img class="tick" src="{{ banner.servingUrl|safe }}">
                                </div>
                            {% endif %}
                            <p>{{ banner.title|safe }}</p>
                        </div>
                    {% endif %}
                {% endfor %}
                {% for banner in banner_ventajas %}
                    {% if 'ver_mas' in banner.title|safe|lower %}
                        <a class="myFancyPopup_ventajas" href=".popup_ventajas" {% if banner_ventajas_colorBtn %}
                           style="background-color: {{ banner_ventajas_colorBtn|safe }};"{% endif %}>{{ banner.description|safe }}</a>
                    {% endif %}
                {% endfor %}

            </div>
        </div>
        <div style="display:none">
            <div class="popup_ventajas" style="position:relative;display:table;">
                <h3>{{ popup_ventajas.subtitle }}</h3>
                {% for banner in popup_ventajas_content %}
                    <div class="banner_list">
                        <img class="tick" src="{{ banner.servingUrl|safe }}">
                        <span class="banner_title">{{ banner.title|safe }}</span><br>
                        {% if banner.description %}
                            <span class="banner_desc">{{ banner.description|safe }}</span>{% endif %}
                    </div>
                {% endfor %}
                {% if popup_ventajas.content %}<p>{{ popup_ventajas.content|safe }}</p>{% endif %}
            </div>
        </div>

        <div id="wrapper_content" class="container12">
    {% endif %}
{% endif %}

{# Content by subtitle #}
{% if content_subtitle and not common_section %}
    <div class="content_subtitle_wrapper">
        <h1 class="section_title">{{ content_subtitle.subtitle|safe }}</h1>

        <div class="section_content">{{ content_subtitle.content|safe }}</div>
    </div>
{% endif %}


{# Rooms #}
{% if rooms_blocks %}
    {% include "rooms.html" %}
{% endif %}

{# Room details #}
{% if room_details %}
    {% include "rooms_individuals.html" %}
{% endif %}

{# Noticias #}
{% if news_section %}
    {% include "news_list.html" %}
{% endif %}

{# Offers #}
{% if offers %}
    {% include "offers_section.html" %}
{% endif %}

{# Automcatic content #}
{% if content_access or common_section %}
    <div class="content_access_wrapper">
        {{ content|safe }}
    </div>
{% endif %}

{# Work with us #}
{% if work_with_us %}
    </div>
    <div class="contact_iframe_background">
        <div class="contact_iframe_wrapper container12">
            <div class="contact_form">
                {% include "work_with_us.html" %}
            </div>
        </div>
    </div>
    <div id="wrapper_content" class="container12">
{% endif %}

{# Location #}
{% if content_location %}
    {% include "location_section.html" %}
{% endif %}

{# Agenda blocks #}
{% if agenda_blocks %}
    {% include "agenda_blocks.html" %}
{% endif %}

{# Icos text #}
{% if ico_banners %}
    <div class="icos_banner_wrapper">
        {% for ico_element in ico_banners %}
            <div class="ico_banner_element ico{{ forloop.counter }}">
                {% if ico_element.linkUrl %}<a href="{{ ico_element.linkUrl|safe }}">{% endif %}
                <img src="{{ ico_element.servingUrl }}" alt="{{ ico_element.title|safe }}" class="ico_element">

                <h2 class="ico_title">{{ ico_element.title|safe }}</h2>

                <div class="ico_element_description">
                    {{ ico_element.description|safe }}
                </div>
                {% if ico_element.linkUrl %}</a>{% endif %}
            </div>
        {% endfor %}
    </div>
{% endif %}


{# Cycle Banners #}
{% if cycle_banners %}
    </div>
    <div class="cycle_banners_wrapper {% if cycle_adjust %}cycle_adjusted{% endif %}">
        {% for cycle_element in cycle_banners %}
            <div class="cycle_banner_element {% cycle 'left_banner' 'right_banner' %} {% if cycle_element.flexslider %}has_flexslider{% endif %}">
                <ul class="slides">
                    {% if cycle_element.flexslider %}
                        <li>
                            {% include 'cycle_block.html' %}
                        </li>
                        {% for cycle_element in cycle_element.flexslider %}
                            <li>
                                {% include 'cycle_block.html' %}
                            </li>
                        {% endfor %}
                    {% else %}
                        <li>
                            {% include 'cycle_block.html' %}
                        </li>
                    {% endif %}
                </ul>
            </div>
        {% endfor %}
    </div>

    <script>
        $(function () {
            $(".cycle_banner_element.has_flexslider").flexslider({
                prevText: "<img src='/img/{{ base_web }}/right_room.png' class='cycle_banner_prev' style='position:absolute;bottom: 180px; left: 55px;width:auto;z-index:23;' alt='left' title='left'>",
                nextText: "<img src='/img/{{ base_web }}/right_room.png' class='cycle_banner_next' style='position:absolute;bottom: 180px; right: 55px;width:auto;z-index:23;' alt='right' title='rigth'>"
            });
        });
    </script>

    <div id="wrapper_content" class="container12">
{% endif %}


{# Gallery section #}
{% if images_section %}
    {% include "_gallery.html" %}
{% endif %}



{# Events elements #}
{% if event_blocks %}
    <div class="events_blocks_wrapper">
        <div class="events_blocks_overlay">
            <h2 class="events_general_title">{{ event_blocks.section.subtitle|safe }}</h2>

            <div class="events_info_wrapper">
                {% for event_info in event_blocks.pictures|slice:":3" %}
                    <div class="event_block_info">
                        {% if event_info.linkUrl %}<a href="{{ event_info.linkUrl }}">{% endif %}
                        <img class="background_image" data-url="{{ event_info.servingUrl }}">

                        <div class="overlay_top_banner"></div>
                        <img src="/img/{{ base_web }}/corchete_border.png" alt="" class="background_image_decorative">

                        <div class="banner_text_wrapper">
                            {% if event_info.date %}
                                <div class="event_date_wrapper">{{ event_info.date|safe }}</div>{% endif %}
                            <h3 class="banner_title">{{ event_info.title|safe }}</h3>
                        </div>
                        {% if event_info.linkUrl %}</a>{% endif %}
                    </div>
                {% endfor %}
            </div>

            {% if event_blocks.section.link_section %}
                <a href="{{ event_blocks.section.link_section }}" target="_blank" class="link_event_section">
                    <div class="button_link_events">{{ event_blocks.section.content|safe }}</div>
                </a>
            {% else %}
                <a href="/{{ language }}/{{ agenda_section.friendlyUrlInternational }}" class="link_event_section">
                    <div class="button_link_events">{{ event_blocks.section.content|safe }}</div>
                </a>
            {% endif %}
        </div>
    </div>
{% endif %}



{% if bottom_carousel %}
    <div class="bottom_carousel_wrapper">
        <div class="bottom_carousel">
            <ul class="slides">
                {% for bottom_carousel_element in bottom_carousel %}
                    <li class="bottom_carousel_element">
                        <img src="{{ bottom_carousel_element.servingUrl }}" alt="" class="bottom_carousel_image">
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <script>
        $(function () {
            $('.bottom_carousel_wrapper').flexslider({
                animation: "slide",
                controlNav: false,
                minItems: 2,
                maxItems: 2,
                itemWidth: (1140 / 2)
            });
        });
    </script>
{% endif %}


{# Banners x2 full bottom #}
{% if bannersx2_full %}
    </div>
    <div class="full_bannersx2_bottom_wrapper">
        <div class="iframe_wrapper">
            {{ bannersx2_full.0.description|safe }}
        </div>
        <div class="location_info_wrapper">
            <img data-url="{{ bannersx2_full.1.servingUrl }}" alt="" class="location_background_image">
            <img src="/img/{{ base_web }}/corchete_border.png" alt="" class="background_image_decorative">

            <div class="black_overlay"></div>
            <div class="centered_text_wrapper">
                <h1 class="title_location">{{ bannersx2_full.1.title|safe }}</h1>

                <div class="description_location">{{ bannersx2_full.1.description|safe }}</div>
            </div>
        </div>
    </div>
    <div id="wrapper_content" class="container12">
{% endif %}



{# Instagram pictures #}
{% if instagram_pictures %}
    <div class="instagram_wrapper">
        <div class="instagram_overlay">
            <div class="instagram_section">
                <h2 class="instagram_title">{{ instagram_section.subtitle|safe }}</h2>
            </div>

            <ul class="slides">
                {% for picture in instagram_pictures.data %}
                    <li>
                        <a href="{{ picture.link }}" target="_blank">
                            <div class="instagram_picture">
                                <img data-url="{{ picture.images.standard_resolution.url }}" alt=""
                                     class="instagram_img">

                                <div class="black_overlay"></div>
                                <div class="picture_info">
                                    <div class="comment_info"><img class="comment_ico"
                                                                   src="/img/{{ base_web }}/comments_ico.png"> {{ picture.comments.count }}
                                    </div>
                                    <div class="likes_info"><img class="likes_ico"
                                                                 src="/img/{{ base_web }}/likes_ico.png"> {{ picture.likes.count }}
                                    </div>
                                </div>
                            </div>
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>

    <script>
        $(function () {
            $(".instagram_wrapper .instagram_overlay").flexslider({
                prevText: "<img src='/img/{{ base_web }}/right_room.png' class='cycle_banner_prev' style='position:absolute;bottom: 20px; left: 0;width:auto;z-index:23;' alt='left' title='left'>",
                nextText: "<img src='/img/{{ base_web }}/right_room.png' class='cycle_banner_next' style='position:absolute;bottom: 20px; right: 0;width:auto;z-index:23;' alt='right' title='right'>",
                minItems: 5,
                maxItems: 5,
                itemWidth: (1140 / 5),
                animation: "slide",
                controlNav: false
            });
        });
    </script>
{% endif %}
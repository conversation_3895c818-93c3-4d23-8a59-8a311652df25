.rooms_wrapper {
  background-color: #FFFFFF;
  padding: 10px 0 10px;
  * {
    box-sizing: border-box;
  }
  .room {
    position: relative;
    border-radius: 0;
    margin: 40px 0;
    .overlay {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      z-index: 15;
      width: 100%;
      height: 100%;
      background-color: #FFFFFF;
      @include transition(width, 1.6s);
    }
    &:nth-child(odd) {
      .overlay {
        left: auto;
        right: 0;
      }
    }
    .room_services {
      position: relative;
      display: block;
      width: 100%;
      height: 40px;
      margin-top: 30px;
      margin-bottom: -70px;
      text-align: center;
      .center_x {
        width: 100%;
        text-align: left;
      }
      i.fa {
        font-size: 20px;
        color: $corporate_1;
      }
      .tooltip {
        position: relative;
        padding: 0 7px;
        display: inline-block;
        width: 30px;
        margin: 5px auto;
        border-left: 1px solid rgba($corporate_1,0.3);
        &:first-of-type {
          border-left-width: 0;
        }
        .tooltiptext {
          @include center_x;
          bottom: calc(100% + 15px);
          visibility: hidden;
          width: 170px;
          background-color: rgba(0, 0, 0, 0.9);
          color: #fff;
          text-align: center;
          padding: 5px 0;
          z-index: 1;
          opacity: 0;
          transition: opacity 0.3s;
          font-size: 15px;
          &:after {
            content: '';
              @include center_x;
              bottom: 0;
              margin-bottom: -10px;
              border-width: 5px;
              border-style: solid;
              border-color: rgba(0, 0, 0, 0.9) transparent transparent transparent;
          }
        }
        &:hover{
          i.fa {
            color: $corporate_2;
          }
          .tooltiptext {
            visibility: visible;
            opacity: 1;
          }
        }
    }
    }
    .room_image {
      position: relative;
      display: inline-block;
      vertical-align: top;
      width: 400px;
      height: 350px;
      overflow: hidden;
      box-shadow: 0 0 30px rgba(0,0,0,.1);
      img {
        @include center_image;
      }
      a:first-of-type {
        position: absolute;
        bottom: 20px;
        left: 20px;
        display: inline-block;
        background-color: $corporate_1;
        color: white;
        width: 50px;
        height: 50px;
        border-radius: 50%;
        box-shadow: 0 0 30px rgba(0,0,0,0.15);
        i.fa {
          @include center_xy;
          font-size: 20px;
        }
        &:hover {
          background-color: $corporate_1;
        }
      }
    }
    .room_info {
      position: relative;
      display: inline-block;
      vertical-align: top;
      width: calc(100% - 415px);
      margin: 140px 0 0 -40px;
      padding: 30px 20px 90px 30px;
      box-shadow: 0 0 30px rgba(0,0,0,0.3);
      background-color: white;
      @include transition(all, 2s);
      h2 {
        font-family: $title_family;
        font-size: 30px;
        color: $corporate_3;
        strong {
          font-weight: bold;
          letter-spacing: 2px;
        }
      }
      p {
        clear: both;
        color: #4B4B4B;
        letter-spacing: 1px;
        font-size: 14px;
        line-height: 20px;
        font-weight: lighter;
        strong {
          font-weight: bold;
        }
      }
      .room_links {
        position: absolute;
        bottom:20px;
        right: 20px;
        a {
          position: relative;
          display: inline-block;
          padding: 15px 25px;
          background-color: transparent;
          border: 1px solid $corporate_1;
          font-family: $title_family;
          color: $corporate_1;
          letter-spacing: 2px;
          text-transform: uppercase;
          font-size: 12px;
          @include transition(all, .6s);
          span {
            position: relative;
          }
          i.fa {
            font-size: 20px;
            vertical-align: middle;
            margin-right: 10px;
            line-height: 20px;
          }
          &:hover {
            background-color: rgba($corporate_1,.8);
            color: white;
          }
        }
      }
      .book_links {
        position: absolute;
        top:20px;
        right: 20px;
        a {
          position: relative;
          display: inline-block;
          padding: 10px 25px;
          background-color: $corporate_2;
          border: 1px solid $corporate_2;
          color: white;
          font-family: $title_family;
          letter-spacing: 2px;
          text-transform: uppercase;
          font-size: 12px;
          @include transition(all, .6s);
          span {
            position: relative;
          }
          i.fa {
            font-size: 20px;
            vertical-align: middle;
            margin-right: 10px;
            line-height: 20px;
          }
          &:hover {
            color: white;
            background-color: rgba($corporate_2,.6);
            border: 1px solid white;
          }
        }
      }
    }
  }
}
.width_0_100{-webkit-animation-name:width_0_100;-webkit-transform-origin:center bottom;animation-name:width_0_100;transform-origin:center bottom}
@-webkit-keyframes width_0_100 {
  0% {
    width: 100%;
  }
  100% {
    width: 0;
  }
}
.opacity_translate{-webkit-animation-name:opacity_translate;-webkit-transform-origin:center bottom;animation-name:opacity_translate;transform-origin:center bottom}
@-webkit-keyframes opacity_translate {
  0% {
    opacity: 1;
    transform: translate(0,0);
  }
  1% {
    opacity: 0;
    transform: translate(-20%,0);
  }
  100% {
    opacity: 1;
    transform: translate(0,0);
  }
}
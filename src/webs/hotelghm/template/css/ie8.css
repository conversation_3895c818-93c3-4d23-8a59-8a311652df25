/* line 1, ../sass/ie8.scss */
.tp-simpleresponsive .caption, .tp-simpleresponsive .tp-caption {
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled = false) !important;
}

/* line 5, ../sass/ie8.scss */
.top_header #top-sections {
  width: 300px;
}

/* line 9, ../sass/ie8.scss */
#full_wrapper_booking {
  background: #5d5d5d;
}

/* line 13, ../sass/ie8.scss */
.bannersx3_wrapper .banner_element .circle {
  background: #5d5d5d;
  padding-top: 50px;
}

/* line 18, ../sass/ie8.scss */
.my_bookings_in_widget {
  background: black;
}

/* line 22, ../sass/ie8.scss */
.best_price_wrapper .best_price_element {
  width: 280px;
}

/* line 26, ../sass/ie8.scss */
.paralax_wrapper .paralax_element.left {
  float: left;
}

/* line 30, ../sass/ie8.scss */
.paralax_wrapper .paralax_element.right {
  float: right;
}

/* line 34, ../sass/ie8.scss */
.hoteles-wrapper .block-hotel h3 {
  background: black;
}

/* line 38, ../sass/ie8.scss */
.hoteles-wrapper .block-hotel {
  width: 350px !important;
}

/* line 42, ../sass/ie8.scss */
.form-contact #contact .contInput textarea {
  border: 1px solid gray;
}

/* line 46, ../sass/ie8.scss */
.form-contact #contact .contInput input {
  border: 1px solid gray;
}

/* line 50, ../sass/ie8.scss */
.content_access #my-bookings-form-fields #hotelSelect {
  background: none;
}

/* line 54, ../sass/ie8.scss */
.location-info-and-form-wrapper {
  background: #e3e2e2;
}

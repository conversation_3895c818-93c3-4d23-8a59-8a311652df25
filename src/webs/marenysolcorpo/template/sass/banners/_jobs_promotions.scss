.job_applications_form {
    @include sec_pad;
    border-bottom: 2px solid white;
    overflow: hidden;
    background-color: $lightgrey;
    
    .content_title {
        text-align: center;
    }

    #contact {

        .info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: auto;
            grid-gap: 20px;
            position: relative;
        }

        .contInput {
            position: relative;

            label.error {
                display: none;
            }

            &.area {
                grid-column: 1 / span 3;
            }

            &.hotels_select {
                grid-column: 1 / span 2;
                position: relative;

                &::before {
                    position: absolute;
                    content: '\f078';
                    font-family: 'Font Awesome 5 Pro';
                    top: 50%;
                    right: 20px;
                    z-index: 1;
                    color: $grey;
                    font-size: 25px;
                    font-weight: 300;
                    transform: translateY(-50%);
                }
            }

            input:not([type="checkbox"]) {
                height: 75px;
                width: 100% !important;
            }

            select {
                height: 75px !important;
            }
            textarea {
                background-color: white;
                height: 150px;
                border-radius: 0;
                font-size: $font_body_size;
                width: 100%;
                border: none;
                box-shadow: none;
            }

            input,
            textarea,
            select, .file_wrapper {
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                padding: 15px;
                border-radius: 0;
                font-size: $font_body_size;
                width: 100%;
                height: 245px;
                border: none;
                box-shadow: none;
                background-color: white;
                font-family: $primary_font;

                &.input-error {
                    outline: 1px solid red;
                }

                &::placeholder {
                    font-family: $primary_font;
                    font-size: $font_body_size;
                }
            }

            #accept-term,
            &#privacity {
                width: auto;
                height: auto;
                display: inline-block;
                vertical-align: middle;
            }

            &.policy-terms {
                margin: 0;
            }
            input[type= "checkbox" ] {
                -webkit-appearance: none;
                border: solid 2px $corporate_1;
                border-radius: 0;
                height: 15px;
                width: 15px;
                vertical-align: middle;
                padding: 5px;

                &:checked {
                    background-color: $corporate_2;
                }
            }
            .file_wrapper {
                border: 1px solid $corporate_1;
                border-style: dashed;
                height: 75px;

                .no_cv, .cv_set {
                    display: none;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    height: 100%;
                    width: 100%;
                    pointer-events: none;
                    @include center_xy;
                    &.active {
                        @include display_flex;
                        .file_title {
                            font-family: $primary_font;
                            font-size: $font_body_size;
                            font-weight: normal;
                            pointer-events: none;
                            text-transform: unset;
                            display: inline-block;
                            cursor: pointer;
                        }
                        i {
                            pointer-events: none;
                            margin-left: 20px;
                            color: $corporate_1;
                        }
                    }
                }
                #file {
                    cursor: pointer;
                    opacity: 0;
                }
            }
        }
        .policy-terms {
            font-family: $primary_font;
            display: inline-block;
            width: auto;
            float: left;
            color: $grey_2;
            font-size: 12px;
            margin: 0;
            
            a {
                font-family: $primary_font;
            }
        }
        
        a.myFancyPopup {
            display: inline-block;
            vertical-align: middle;
            color: $grey_2;
        }

        #contact-button {
            width: 100%;
        }
    }

    label {
        display: block;
        text-align: center;
        text-transform: uppercase;
        font-weight: 100;
    }
}
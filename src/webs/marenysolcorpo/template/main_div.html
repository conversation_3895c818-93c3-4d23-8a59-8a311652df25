<ul itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner" class="container">
    {% for section in main_sections %}
        {% if section.sectionName == 'Hoteles' or section.sectionName == 'Ofertas' %}
            <div class="main-section-div-wrapper {% if section.subsections %}has_subsection{% endif %}"
                 {% if sectionToUse and sectionToUse.title == section.title %}id="section-active" {% endif %}>
                {% if section.subsections %}
                    <a>{{ section.title|safe }}</a>
                {% else %}
                    {% if section.title %}
                        <a itemprop="url" {% if section.replace_link %}href="{{ section.replace_link|safe }}"
                           target="_blank"
                           {% else %}href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}"{% endif %}>
                            <span itemprop="name">{{ section.title|safe }}</span>
                        </a>
                    {% endif %}
                {% endif %}
                {% if section.subsections %}
                    <ul>
                        {% for subsection in section.subsections %}
                            <li class="main-section-subsection {{ subsection.title|lower }}">
                                {% if subsection.title %}
                                    <a {% if subsection.replace_link %}href="{{ subsection.replace_link|safe }}"
                                       target="_blank"
                                       {% else %}href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrlInternational }}"{% endif %}
                                       {% if sectionToUse and sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                        {{ subsection.title|safe }}
                                    </a>
                                {% endif %}
                            </li>
                        {% endfor %}
                    </ul>
                {% endif %}
            </div>
        {% endif %}
    {% endfor %}
    <div class="main_menu_subgroup">
        {% for section in main_sections %}
            {% if section.sectionName != 'Hoteles' and section.sectionName != 'Ofertas' %}
                <div class="main-section-div-wrapper {% if loop.last %}last_item{% endif %}{% if section.subsections %} has_subsection {% endif %}"
                     {% if sectionToUse and sectionToUse.title == section.title %}id="section-active" {% endif %}>
                    {% if section.subsections %}
                        <a itemprop="url" {% if section.replace_link %}href="{{ section.replace_link|safe }}"
                               target="_blank"
                               {% else %}href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}"{% endif %}>
                                <span itemprop="name">{{ section.title|safe }}</span>
                            </a>
                    {% else %}
                        {% if section.title %}
                            <a itemprop="url" {% if section.replace_link %}href="{{ section.replace_link|safe }}"
                               target="_blank"
                               {% else %}href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}"{% endif %}>
                                <span itemprop="name">{{ section.title|safe }}</span>
                            </a>
                        {% endif %}
                    {% endif %}
                    {% if section.subsections %}
                        <ul>
                            {% for subsection in section.subsections %}
                                <li class="main-section-subsection {{ subsection.title|lower }}">
                                    {% if subsection.title %}
                                        <a {% if subsection.replace_link %}href="{{ subsection.replace_link|safe }}"
                                           target="_blank"
                                           {% else %}href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrlInternational }}"{% endif %}
                                           {% if sectionToUse and sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                            {{ subsection.title|safe }}
                                        </a>
                                    {% endif %}
                                </li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </div>
            {% endif %}
        {% endfor %}
        <div id="social">
            {%if facebook_id %}
                <a href="http://www.facebook.com/{{facebook_id}}" target="_blank" rel="nofollow">
                    <i class="fa fa-facebook" aria-hidden="true"></i>
                </a>
            {% endif %}
            {% if twitter_id %}
                <a href="https://twitter.com/{{twitter_id}}" target="_blank" rel="nofollow">
                    <i class="fa fa-twitter" aria-hidden="true"></i>
                </a>
            {% endif %}
            {% if google_plus_id %}
                <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="publisher" {# rel="nofollow" #}>
                    <i class="fa fa-google-plus" aria-hidden="true"></i>
                </a>
            {% endif %}
            {% if youtube_id %}
                <a href="https://www.youtube.com/{{youtube_id}}" target="_blank" rel="nofollow">
                    <i class="fa fa-youtube" aria-hidden="true"></i>
                </a>
            {% endif %}
            {% if pinterest_id %}
                <a href="http://es.pinterest.com/{{ pinterest_id }}" target="_blank" rel="nofollow">
                    <i class="fa fa-pinterest-p" aria-hidden="true"></i>
                </a>
            {% endif %}
            {% if instagram_id %}
                <a href="http://www.instagram.com/{{ instagram_id }}" target="_blank" rel="nofollow">
                    <i class="fa fa-instagram" aria-hidden="true"></i>
                </a>
            {% endif %}
        </div>
    </div>
</ul>

<div class="banner_image_full_wrapper">
    <div class="top_banner">
        {% if banner_image_sec.subtitle %}
        <div class="content_title">
            <h3 class="title">
                {{ banner_image_sec.subtitle|safe }}
            </h3>
        </div>
        {% endif %}
        {% if banner_image_sec.content %}
            <div class="desc">
                {{ banner_image_sec.content|safe }}
            </div>
        {% endif %}
    </div>
    <div class="banner_image_wrapper owl-carousel">
        {% for banner in banner_image_pics %}
            <div class="banner">
                {% if banner.servingUrl %}
                    <img src="{{ banner.servingUrl|safe }}=s600" {% if banner.altText %}alt="{{ banner.altText|safe }}" {% endif %}>
                {% endif %}
                <div class="img_content">
                    {% if banner.title %}
                        <span class="title">{{ banner.title|safe }}</span>
                    {% endif %}
                    <div class="hidden">
                        {% if banner.description %}
                            <span class="text">{{ banner.description|safe }}</span>
                        {% endif %}
                        {% if banner.linkUrl %}
                            <br>
                            <a href="{{ banner.linkUrl|safe }}" {% if "http" in banner.linkUrl %}target="_blank" {% endif %}
                               class="btn_white_outline">
                                {% if banner.btn_text %}
                                    {{ banner.btn_text|safe }}
                                {% else %}
                                    {{ T_ver_mas }}
                                {% endif %}
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<script>
    $(window).load(function () {
        $(".banner_image_wrapper.owl-carousel").owlCarousel({
            loop: true,
            nav: true,
            dots: false,
            items: 1,
            navText: ['<i class="fal fa-chevron-left"></i>', '<i class="fal fa-chevron-right"></i>'],
            navSpeed: 500,
            autoplay: false,
        });
    })
</script>
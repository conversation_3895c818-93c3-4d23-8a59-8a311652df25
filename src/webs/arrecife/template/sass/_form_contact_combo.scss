body .content_subtitle_wrapper {
    padding: 100px 200px 0 !important;
}

.contact_form_combo_wrapper {
  display: inline-block;
  width: 100%;
  float: left;
  padding: 0 0 100px;
  .combo_description {
    font-size: 30px;
    font-weight: 700;
    text-align: center;
    padding: 100px 200px;
  }


  h3 {
    text-transform: uppercase;
    text-align: center;
    font-family: $title_family;
    font-size: $title_size;
    margin-bottom: 40px;
    font-weight: 700;
  }

  #contact {
    width: 980px;
    margin: auto;

    * {
      box-sizing: border-box;
    }

    .contInput {
      display: inline-block;
      float: left;
      width: 100%;
      margin-bottom: 10px;
      position: relative;

      &:nth-of-type(-n+3), &:nth-of-type(6), &:nth-of-type(7), &:nth-of-type(8) {
        width: calc((100% - 20px)/3);
        margin-right: 10px;
      }

      &:nth-of-type(4), &:nth-of-type(5) {
        width: calc((100% - 10px)/2);
        margin-right: 10px;
        margin-bottom: 20px;
      }

      /*&:nth-of-type(6)  {
        &:after {
            content: '\f107';
            font-family: "fontawesome", sans-serif;
            font-size: 14px;
            padding: 5px 10px;
            border-left: 1px solid #444444;
            color: #444444;
            position: absolute;
            top: 50%;
            -webkit-transform: translate(0%, -50%);
            -moz-transform: translate(0%, -50%);
            -ms-transform: translate(0%, -50%);
            -o-transform: translate(0%, -50%);
            transform: translate(0%, -50%);
            right: 0;
        }
      }*/

      &:nth-of-type(3), &:nth-of-type(5), &:nth-of-type(8) {
        margin-right: 0;
      }

      .fa {
        width: 40px;
        height: 40px;
        color: $corporate_1;
        position: absolute;
        top: 0;
        left: 0;

        &:before {
          @include center_xy;
        }
      }

      input {
        width: 100%;
        height: 40px;
        padding-left: 40px;
        border: 0;
        border-bottom: 1px solid $corporate_1;

        &#accept-term {
          width: auto;
          height: auto;
          display: inline-block;
          vertical-align: middle;
        }
      }

      textarea {
        width: 100%;
        padding-left: 40px;
        padding-top: 13px;
        border: 1px solid $corporate_1;
      }
    }

    a.myFancyPopup {
      display: inline-block;
      vertical-align: middle;
      color: $corporate_1;
    }

    #contact-button {
      display: inline-block;
      width: 100%;
      background: $corporate_1;
      border-width:0;
      color: white;
      height: 40px;
      text-transform: uppercase;
      font-size: 16px;
      font-family: $title_family;
      font-weight: 100;
      margin-bottom: 10px;
      cursor: pointer;
    }
  }
}
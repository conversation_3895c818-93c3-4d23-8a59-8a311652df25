<div class="wrapper_content banner_features_wrapper">
    <div class="container12">
        <div class="carousel_wrapper">
            <div class="owl-carousel">
                {% for foo in banner_features_pics %}
                    <div class="icon_box animated" style="opacity: 0">
                        {% if foo.icon %}
                            <i class="{{ foo.icon }}"></i>
                        {% elif foo.altText %}
                            <i class="fal {{ foo.altText }}"></i>
                        {% elif foo.servingUrl %}
                            <img src="{{ foo.servingUrl }}=s210" alt="">
                        {% endif %}
                        {% if foo.title %}
                            <h4 class="title">{{ foo.title|safe }}</h4>
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">

    $(window).load(function () {

        {% if is_mobile %}

            $(".banner_features_wrapper .owl-carousel").owlCarousel({
                loop: true,
                nav: false,
                navText: ['<i aria-hidden="true"></i>', '<i aria-hidden="true"></i>'],
                dots: true,
                items: 1,
                margin: 15,
                autoplay: true,
                animateOut: 'fadeOut',
                touchDrag: true,
                mouseDrag: false,
            });

        {% else %}

            $(".banner_features_wrapper .owl-carousel").owlCarousel({
                loop: true,
                nav: true,
                navText: ['<i class="fal fa-chevron-left"></i>', '<i class="fal fa-chevron-right"></i>'],
                dots: false,
                items: 4,
                margin: 15,
                smartSpeed: 600,
                fluidSpeed: 600,
                navSpeed: 600,
                dotsSpeed: 600,
                dragEndSpeed: 600,
                autoplay: true
            });

        {% endif %}
        {% if not is_mobile and not user_isIpad %}
            function banner_icon_box_fx() {
                for (x = 1; x <= {{ banner_features_pics|length }}*2; x++) {
                    $(".owl-carousel .owl-item.active:nth-child(" + x + ") .icon_box").addnimation({parent:$(".banner_features_wrapper"), class:"fadeInUp", delay: x * 80, reiteration: false});
                }
            }
            banner_icon_box_fx();
        {% endif %}
    });

</script>
<div class="news_widget_wrapper">
    <div class="news_filter">
        {% for cattegory, news in news.items() %}
            <span class="filter {% if loop.first %}active{% endif %}" data-category="{{ news.class_name|safe }}">{{ cattegory|safe }}</span>
        {% endfor %}
    </div>
    {% for cattegory, news in news.items() %}
        <div class="new_block {{ news.class_name|safe }}" {% if not loop.first %}style="display: none;" {% endif %}>
            {% for item in news.news_list %}
                <div class="entry_widget">
                    {% if item.picture %}
                        <div class="image">
                            <a href="/{{ path }}/{{ item.friendlyUrl }}">
                                <img src="{{ item.picture|safe }}=s360" alt="{{ item.name|safe }}">
                            </a>
                        </div>
                    {% endif %}
                    <div class="new_content">
                        {% if item.name %}<a class="title" href="/{{ path }}/{{ item.friendlyUrl }}">{{ item.name|safe }}</a>{% endif %}
                        {% if item.shortDesc %}<div class="text">{{ item.shortDesc|striptags|safe }}</div>{% endif %}
                        <div class="links">
                            <div class="comments">
                                <i class="fal fa-comment"></i>
                                <b>{{ item.comments|length }}</b>
                            </div>
                            {% if item.author %}
                                <div class="author">
                                    <span>{{ item.author|safe }}</span>
                                </div>
                            {% endif %}
                            {% if item.creationDate %}
                                <div class="date">
                                    <span>{{ item.creationDate|safe }}</span>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    {% endfor %}
</div>

<script>
    $(window).load(function () {
        $(".news_filter .filter").click(function () {
            var category = $(this).data("category");
            $(".news_filter .filter").removeClass("active");
            $(this).addClass("active");
            $(".news_widget_wrapper .new_block").slideUp().promise().done(function () {
                $(".news_widget_wrapper .new_block." +  category).slideDown();
            });
        })
    })
</script>
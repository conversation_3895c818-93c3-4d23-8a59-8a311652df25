<div class="contact_form_wrapper effects_sass" sass_effect="slide_up_effect">
    <div class="container12">

        <form name="contact" id="contact" method="post" action="/utils/?action=contact">
            <input type="hidden" name="action" id="action" value="contact"/>

            <div class="top_form">
                {% if form_contact_cv %}
                    <h3>{{ form_contact_cv|safe }}</h3>
                {% else %}
                    <h3>{{ T_formulario_contacto }}</h3>
                {% endif %}
            </div>
            <div class="info">
                <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>

                <div class="contInput">
                    <i class="fa fa-h-square" aria-hidden="true"></i>
                    <select name="hotel_name" id="cv_hotel_selector">
                        {% for hotel_name in individual_hotel_names %}
                            <option value="{{ hotel_name|safe }}">{{ hotel_name|safe }}</option>
                        {% endfor %}
                    </select>
                    <i class="fa fa-caret-down" aria-hidden="true"></i>
                </div>

                <div class="contInput">
                    <i class="fa fa-briefcase" aria-hidden="true"></i>
                    <input type="text" id="job_desired" name="job_desired" class="bordeInput" value="" placeholder="{{ T_puesto_deseado }}"/>
                </div>

                <div class="contInput">
                    <i class="fa fa-user" aria-hidden="true"></i>
                    <input type="text" id="name" name="name" class="bordeInput" value="" placeholder="{{ T_nombre }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-user" aria-hidden="true"></i>
                    <input type="text" id="surname" name="surname" class="bordeInput" value=""
                           placeholder="{{ T_apellidos }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-phone" aria-hidden="true"></i>
                    <input type="text" id="telephone" name="telephone" class="bordeInput" value=""
                           placeholder="{{ T_telefono }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-envelope-o" aria-hidden="true"></i>
                    <input type="text" id="email" name="email" class="bordeInput" value="" placeholder="{{ T_email }}"/>
                </div>
                <div class="contInput area">
                    <i class="fa fa-comment" aria-hidden="true"></i>
                    <textarea style="height:150px;" type="text" id="comments" name="comments" class="bordeInput"
                              value=""
                              placeholder="{{ T_comentarios }}"></textarea>
                </div>
                <div class="contInput area">
                    <i class="fa fa-file-text-o " aria-hidden="true"></i>
                    <input id="uploadFile" placeholder="{{T_adjunta_cv}} (Max. 3Mb)" disabled="disabled" />
                    <input type="file" id="file_cv" name="file" value=""/>
                    <input type="hidden" id="url_4_upload" name="url_4_upload" value=""/>
                </div>
                <button id="contact-button" onClick="return false;">{{ T_enviar }}</button>
                <div class="contInput policy-terms">
                    <input type="checkbox" id="accept-term" name="accept_term"/>
                    <a class="myFancyPopup fancybox.iframe"
                       href="{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                </div>
            </div>
        </form>
    </div>
</div>

{% if mobile_version %}
    <script async type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
{% endif %}

<script type="text/javascript">
    $(window).load(function () {

        jQuery.validator.addMethod("phone", function (phone_number, element) {
            phone_number = phone_number.replace(/\s+/g, "");
            return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                    phone_number.match(/^[0-9 \+]\d+$/);
        }, "Please specify a valid phone number");


        $("#contact").validate({
            rules: {
                hotel: "required",
                name: "required",
                surname: "required",
                hotel_name: "required",
                job_desired: "required",
                email: {
                    required: true,
                    email: true
                },
                telephone: {
                    required: true,
                    phone: true
                },
                comments: "required",
                accept_term: "required"
            },
            messages: {
                hotel: "{{ T_campo_obligatorio}}",
                name: "{{ T_campo_obligatorio}}",
                surname: "{{ T_campo_obligatorio}}",
                job_desired: "{{ T_campo_obligatorio}}",
                email: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    email: "{{ T_campo_valor_invalido|safe }}"
                },
                emailConfirmation: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    email: "{{ T_campo_valor_invalido|safe }}",
                    equalTo: "{{T_not_confirmed_email_warning|safe}}"
                },
                telephone: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    phone: "{{ T_campo_valor_invalido|safe }}"
                },
                comments: "{{ T_campo_obligatorio|safe }}",
                accept_term: "{{ T_campo_obligatorio|safe }}"
            }

        });

        $("#contact-button").click(function () {
            if(!$("#contact").valid()) return;

            var data = {
                'name': $("#name").val(),
                'surname': $("#surname").val(),
                'telephone': $("#telephone").val(),
                'email': $("#email").val(),
                'comments': $("#comments").val(),
                'vacant_posts': $("#job_desired").val(),
                'hotel': $("#cv_hotel_selector").val()
            };

            var url_for_upload = "",
                url_cv_download = "",
                confirma_no_cv=1,
                send_email = 1;

            $(this).closest("form").find("#contact-button").css("display","none");

            formData = new FormData($("#contact")[0]);

            $.ajax({
                url: "/get_upload_url",
                type: 'GET',
                data: formData,
                async: false,
                cache: false,
                contentType: false,
                processData: false,
                success: function (returndata) {
                 url_for_upload = returndata;
                }
              });

            if ($(this).closest("form").find("#file_cv").length && url_for_upload && $(this).closest("form").find("#file_cv").val()) {
                confirma_no_cv = 0;
                var formData = new FormData($(this).closest("form")[0]);
                $.ajax({
                    url: url_for_upload,
                    type: 'POST',
                    data: formData,
                    async: false,
                    cache: false,
                    contentType: false,
                    processData: false,
                    success: function (returndata) {
                        url_cv_download = returndata;
                    }
                });
            }
            if (!confirma_no_cv && (!url_cv_download || url_cv_download=="NO_FILE_UPLOAD")){
                alert("ERROR UPLOADING CURRICULUM VITAE");
                window.location.reload();
                return false;
            }

            data['url_file_download'] = url_cv_download;

            if (confirma_no_cv )  {
                if (confirm($.i18n._("confirm_no_cv"))   ){

                }
                else{
                    send_email = 0;
                }
            }


            if (send_email){
                if ($("#contact").valid()) {
                    $.post(
                            "/utils/?action=work_with_us", data,
                            function (data) {
                                alert("{{ T_gracias_contacto }}");
                                $("#name").val("");
                                $("#surname").val("");
                                $("#telephone").val("");
                                $("#email").val("");
                                $("#emailConfirmation").val("");
                                $("#comments").val("");
                                location.reload();
                            }
                    );
                }
            }
        })
    });
</script>
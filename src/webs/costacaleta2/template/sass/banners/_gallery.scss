.gallery_filter_wrapper {
  @include banner_wrapper_styles;
  .gallery_block {
    margin-bottom: 50px;
    .gallery_title {
      padding: 15px 0;
      text-transform: uppercase;
      font-family: $title_family;
      font-size: 34px;
      font-weight: bold;
      color: $corporate_1;
      display: block;
    }

    .gallery_group_wrapper {
      display: table;
      width: 100%;

      .image_wrapper {
        position: relative;
        display: inline-block;
        height: 270px;
        float: left;
        margin-left: 10px;
        margin-bottom: 20px;
        width: calc(25% - 15px);
        overflow: hidden;
        cursor: pointer;
        @include border_radius;
        @include box_shadow;
        box-shadow: 10px 10px 20px -5px rgba(0, 0, 0, 0.3);
        @extend .fa-search-plus;
        &:before {
          @extend .fal;
          z-index: 10;
          font-size: 40px;
          color: white;
          opacity: 0;
          @include center_xy;
          @include transition(opacity, 1s);
        }
        &:after {
          content: '';
          @include full_size;
          background-color: rgba($corporate_1, .8);
          z-index: 5;
          opacity: 0;
          visibility: hidden;
          @include transition(all, .6s);
        }
        .svg_logo {
          @include center_xy;
          z-index: 7;
          height: 50%;
          opacity: 0;
          visibility: hidden;
          @include transition(all, .6s);
          * {
            fill: $svg_color_white;
          }
        }
        .img_info {
          @include center_xy;
          display: inline-block;
          color: white;
          font-size: 14px;
          opacity: 0;
          visibility: hidden;
          z-index: 8;
          @include transition(all, .6s);
        }
        img {
          @include center_image;
          max-width: 100%;
        }
        &:first-of-type {
          width: calc(50% - 10px);
          height: 560px;
          margin: 0 10px 0 0;
        }
        &:nth-of-type(3), &:nth-of-type(5) {
          margin-left: 20px;
        }
        &:hover {
          &:before, &:after, .svg_logo, .banner_info {
            opacity: 1;
            visibility: visible;
          }
        }
      }
      &:nth-of-type(odd) {
        .image_wrapper {
          margin-left: 0;
          margin-right: 10px;
          &:first-of-type {
            float: right;
            margin: 0 0 0 10px;
          }
          &:nth-of-type(2), &:nth-of-type(4) {
            margin-left: 0;
            margin-right: 20px;
            &:last-of-type {
              float: right;
            }
          }
        }
      }
    }
  }
}
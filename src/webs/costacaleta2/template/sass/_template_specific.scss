body {
  font-family: $text_family;

  * {
    box-sizing: border-box;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
  }

  a {
    text-decoration: none;
  }

  strong {
    font-weight: 700;
  }

  .aviso_cookie {
    position: fixed;
    top: auto;
    bottom: 10px;
    right: 10px;
    width: 530px;
    height: auto;
    padding: 20px 30px;
    background: rgba(0, 0, 0, 0.8);

    p {
      padding: 0;
      text-align: left;
      line-height: 20px;
    }
  }

  .calendar_popup_wrapper {
    background: transparent;
    padding: 0;

    .fancybox-close-small {
      color: white;
      top: 60px;
    }
  }

  #slider_container {
    position: relative;

    .caption.tp-caption {
      width: 100%;
      height: 100%;
      top: 0;
    }

    .cartela_slider {
      @include center_xy;
      color: white;
      text-align: center;
      background-color: rgba(black, .2);
      padding: 40px;
      @include border_radius;

      .text {
        font-family: $text_family;
        font-size: 22px;
        font-weight: 400;
      }

      .title {
        display: block;
        font-family: $title_family;
        font-weight: 700;
        font-size: 42px;
        line-height: 58px;
      }

      .with_svg_logo {
        position: relative;

        &:before, &:after {
          content: '';
          @include center_y;
          left: 0;
          height: 2px;
          width: calc(50% - 30px);
          background-color: white;
        }

        &:after {
          left: auto;
          right: 0;
        }

        .svg_logo {
          height: 35px;

          * {
            fill: white;
          }
        }
      }
    }

    .tparrows {
      position: absolute;
      opacity: 1 !important;
      top: auto !important;
      bottom: 130px !important;
      left: 15px !important;
      background: none !important;

      &:before {
        content: "\f30a";
        font-family: "Font Awesome 5 Pro";
        font-weight: 300;
        @include arrow_icon_styles;
        color: white;
      }

      &.tp-rightarrow {
        left: auto !important;
        right: 15px !important;

        &:before {
          content: "\f30b";
        }
      }

      &:hover {
        &:before {
          color: $corporate_2;
        }
      }
    }

    .tp-bullets {
      display: none;
    }

    .inner_slider {
      width: 100%;
      height: 400px;
      position: relative;
      overflow: hidden;

      img {
        @include center_image;
      }
    }
  }

  .content_subtitle_wrapper {
    padding: 90px calc((100% - 1140px) / 2) 20px;
    text-align: center;

    .content_subtitle_title {
      @include title_styles;

      span.subtitle {
        color: $corporate_1;
      }
    }

    .content_subtitle_description {
      @include text_styles;
      padding: 30px 130px 0;
      padding-top: 50px;
    }
  }

  .icon_styles {
    @include icon_styles($corporate_1, $corporate_2);
  }

  .icon_styles_2 {
    @include icon_styles_2($corporate_1);
  }

  .icon_styles_3 {
    @include icon_styles_3($corporate_2);
  }

  .btn_personalized_1 {
    @include btn_styles;

    &:lang(ru) {
      padding: 25px 25px;
    }
  }

  .icon_link {
    @include icon_link;
  }

  .with_bg_img {
    @include with_bg_img_styles($white_rgba);
  }

  /* Mis reservas */

  #my-bookings-form {
    #reservation {
      .modify_reservation_widget {
        margin: auto;
        margin-top: 40px;
        margin-bottom: 0;
        padding: 20px;

        #info_ninos {
          display: none !important;
        }

        #contenedor_opciones {
          margin: 0 auto 10px;

          .ninos-con-babies {
            margin-right: 15px;
          }
        }

        #contenedor_fechas {
          text-align: center;

          #fecha_entrada, #fecha_salida {
            display: inline-block;
            float: none;
          }
        }

        #contenedor_habitaciones {
          text-align: center;

          label {
            display: inline-block;
            float: none;
          }

          select {
            display: inline-block;
            float: none;
          }
        }

        #envio {
          text-align: center;
        }
      }

      .my-bookings-booking-info {
        margin: 40px auto 0;

        .fResumenReserva {
          margin: auto;
        }
      }
    }

    #modify-button-container {
      display: none;
    }

    #my-bookings-form-fields {
      label {
        display: block;
        text-align: center;
        text-transform: uppercase;
        color: #4B4B4B;
        font-weight: 100;
      }

      input, select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        display: block;
        width: 300px;
        margin: 10px auto;
        height: 40px;
        border-radius: 0;
        text-align: center;
        font-size: 14px;
        border: 1px solid #DDD;
      }

      select {
        padding: 0 0 0 15px;
      }

      ul {
        text-align: center;
        margin-top: 30px;

        li {
          display: inline-block;
          width: 200px;
          vertical-align: middle;

          button {
            @include btn_styles;
            text-transform: uppercase;
            border: 0;
            cursor: pointer;
            background: $corporate_1;
            padding: 11px 16px;
            font-size: 16px;
            @include transition(background, .4s);

            &.modify-reservation {
              background: $corporate_1;

              &:hover {
                background: darken($corporate_1, 10%);
              }
            }

            &.searchForReservation {
              background: $corporate_2;

              &:hover {
                background: darken($corporate_2, 10%);
              }
            }
          }
        }
      }
    }

    #cancelButton {
      display: none;
      background: $corporate_2;
      height: 40px;
      text-transform: uppercase;
      font-size: 16px;
      color: white;
      border: 0;
      cursor: pointer;
      width: 200px;
      font-weight: 100;
      margin: 40px auto 0;
      @include transition(background, .4s);

      &:hover {
        background: darken($corporate_2, 10%);
      }
    }
  }

  /* Iframe Map */

  .iframe_map_wrapper {
    margin-bottom: -4px;

    iframe {
      width: 100%;
    }
  }

  .tp-banner-container {
    height: calc(100vh - 168px) !important;
  }

  .caption.fade.tp-caption.start {
    top: 150px !important;
  }

  #contentInPage {
    width: 100%;
    text-align: center;
    padding: 100px 50px 70px;
    font-weight: 400;
    font-family: "Quicksand", sans-serif;
    font-size: 19px;
    line-height: 25px;
    color: #5F5F5F;
    box-sizing: border-box;
    padding: 30px 130px 0;
    padding-top: 50px;

    h3 {
      position: relative;
      font-family: "Quicksand", sans-serif;
      font-weight: 400;
      font-size: 36px;
      line-height: 37px;
      box-sizing: border-box;
      margin: 10px 0 25px;
      color: #333;
    }
  }

  .slide_icons_home_wrapper {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    @include display_flex;
    justify-content: space-between;
    width: 900px;
    margin: auto;
    z-index: 100;
    padding-bottom: 20px;

    .slide_icons_text_wrapper {
      width: 100%;
      margin-bottom: 10px;
      text-transform: uppercase;
      color: white;
      font-size: 18px;
      text-align: center;
      font-weight: 600;
      letter-spacing: 1px;
    }

    .icon_element {
      @include display_flex;
      justify-content: space-between;
      flex-wrap: nowrap;
      width: 25%;
      color: white;
      text-align: center;
      padding: 0 30px 0 0;
      align-items: center;

      .icon {
        font-size: 28px;
        margin-right: 15px;
      }

      .text {
        width: 100%;
        font-family: $title_family;
        text-transform: uppercase;
        font-size: 14px;
      }
    }
  }
}

.automatic_floating_picture {
  z-index: 1100 !important;
}

@media (max-width: 1400px) {
  .automatic_floating_picture {
    bottom: 220px !important;
  }
}


//IPAD
@media only screen and (max-width: 1440px) and (orientation: portrait) and (-webkit-min-device-pixel-ratio: 2) {
    #full_wrapper_booking {
        bottom: 220px !important;
        height: 130px;
        .wrapper_booking_button {
            width: 425px !important;
            .promocode_wrapper {
                width: 145px !important;
            }
            .resident_promocode_wrapper {
                width: 90px !important;
            }
        }
    }
}
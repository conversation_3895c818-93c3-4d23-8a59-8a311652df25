{% if logo_header %}
<header>
    <a href="{{host|safe}}/" id="logoDiv"><img itemprop="logo" src="{{ logo_header }}"/></a>
    <div class="right_block">
        {% if hotel_phone %}
        <div class="hotel_phone"><i class="fal fa-phone"></i>{{ hotel_phone }}</div>
        {% endif %}
        <div id="lang">
            <i class="fal fa-globe"></i>
            <span id="selected-language" class="selected-language2">{{ language|upper }}</span>
            <ul id="language-selector-options" class="language-selector-options2" style="display: none;">
           {% for key, value in language_codes.items() %}
                <li class="language-option-flag">
                    <a hreflang="{{ key }}"
                       href="{{ hostWithoutLanguage|safe }}/{{ key }}/">{{ value }}</a>
                </li>
            {% endfor %}
            </ul>
       </div>
    </div>
</header>
{% endif %}
{% if slider %}
<div class="slider">
    <img src="{{ slider.servingUrl }}=s1900" alt="{{ slider.altText|safe }}" class="slider_pic">
    <div id="full_wrapper_booking">
        <div id="wrapper_booking" class="container12">
            <div id="booking" class="boking_widget_inline">
                {{ booking_engine|safe }}
            </div>
        </div>
    </div>
</div>
{% endif %}
{% if content_subtitle and content_subtitle.content %}
    <div class="content_ventajas">
    {% if prelabel %}
        <div class="prelabel">{{ prelabel|safe }} <i class="fal fa-arrow-right"></i></div>
    {% endif %}
        {{ content_subtitle.content|safe }}
    </div>
{% endif %}
<footer>
    {% if logo_footer %}
        <a href="{{host|safe}}/" id="logoDiv"><img itemprop="logo" src="{{ logo_footer }}"/></a>
    {% endif %}
    <div class="footer_col">
        {% if hotel_phone %}{{ hotel_phone }}{% endif %}<br><br>
        {% if hotel_mail %}<a href="mailto:{{ hotel_mail }}">{{ hotel_mail }}</a>{% endif %}
    </div>
    {% if texto_legal %}
        <div class="legal_text">{{ texto_legal|safe }}</div>
    {% endif %}
    <div class="social">
        {%if facebook_id %}
            <a href="http://www.facebook.com/{{facebook_id}}" target="_blank">
                <i class="fab fa-facebook-f"></i>
            </a>
        {% endif %}
        {% if instagram_id %}
            <a href="http://www.instagram.com/{{ instagram_id }}" target="_blank">
                <i class="fab fa-instagram"></i>
            </a>
        {% endif %}
        {% if twitter_id %}
            <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank">
                <i class="fab fa-twitter"></i>
            </a>
        {% endif %}
        {% if linkedin_id %}
            <a href="http://www.linkedin.com/company/{{ linkedin_id }}/" target="_blank">
                <i class="fab fa-linkedin"></i>
            </a>
        {% endif %}
        {% if google_plus_id %}
            <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="publisher">
                <i class="fab fa-google-plus"></i>
            </a>
        {% endif %}
        {% if youtube_id %}
            <a href="https://www.youtube.com/{{youtube_id}}" target="_blank">
                <i class="fab fa-youtube"></i>
            </a>
        {% endif %}
        {% if pinterest_id %}
            <a href="http://es.pinterest.com/{{ pinterest_id }}" target="_blank">
                <i class="fab fa-pinterest-p"></i>
            </a>
        {% endif %}
    </div>
    <div class="footer-copyright">
        {% for x in policies_section %}
            <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}" class="myFancyPopup fancybox.iframe">{{ x.title|safe }}</a> |
        {% endfor %}
        <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html" title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
        <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
        <a target="_blank" href="/rss.xml">RSS</a>
    </div>
</footer>
<div class="recursos" style="display: none;">
    <div class="logo_agencias"><img src="https://cdn2.paraty.es/casual-corporativa/images/82f1f390316821f" class="logo_agencias"></div>
    {% if slider %}<div class="title_login">{{slider.description|safe}}</div>{% endif %}
    <div class="register_text">{% if register_text %}{{ register_text|safe }}{% endif %}</div>
    <div class="wellcome">{% if wellcome_text %}{{ wellcome_text|safe }}{% endif %}</div>
    <div class="register_button">
        <a href="#register">{{ T_register }}</a>
    </div>
    <div class="accede_button input_wrapper">
        <label>{{ T_miembro_acceder }}</label>
        <a href="#login" class="close_modal_signup btn btn_link">{{ T_acceder }}</a>
    </div>
</div>
<div class="modal_signup">
    <a href="#" class="close_modal_signup close_style"><span></span><span></span></a>
    <div class="modal_content">
        <div class="modal_pic" {% if popup and popup.servingUrl %}style="background-image:url('{{ popup.servingUrl }}=s1900');"{% endif %}>
            <div class="center_xy">
            <div class="title_singup"><img src="https://cdn2.paraty.es/casual-corporativa/images/07f8f40e9eb0d45"></div>
            {% if  popup and popup.description %}<div class="desc_signup">{{popup.description|safe}}</div>{% endif %}
            </div>
            {% if prelabel %}
                <div class="prelabel close_modal_signup">{{ prelabel|safe }} <i class="fal fa-arrow-right"></i></div>
            {% endif %}
        </div>
        <div class="modal_form"></div>
    </div>
</div>
<script>
// listener on show/hide element
(function($) {
	$.each(['show', 'hide'], function(i, ev) {
		var el = $.fn[ev];
		$.fn[ev] = function() {
			this.trigger(ev);
			return el.apply(this, arguments);
		};
	});
})(jQuery);
function load_userdata_cookie() {
    let cookie_agency = searchCookie("logged_agency");
    if(cookie_agency){
        let data_decode = atob(cookie_agency.replace(/igualigual/g, '=').replace(/masmas/g, '+').replace(/barrabarra/g, '/')),
            agency_config = jQuery.parseJSON(data_decode),
            name_agency = agency_config[0].name,
            commision_agency = agency_config[0].commision;
        $(".popup-agency-login").addClass("logged");
        let title_wellcome = "{{ T_hi }} " + name_agency + "";
        title_wellcome = title_wellcome.replace("!", "") + "!";
        $(".login-agency-message.m_ok").html("<h3>"+title_wellcome+"</h3>");
        console.log(commision_agency);
        $(".content_ventajas").hide();
        $("#full_wrapper_booking").show();
        $(".login-agency-message.m_ok").prepend($(".recursos .logo_agencias").html());
        $(".login-agency-message.m_ok").append($(".recursos").find(".wellcome").clone());
        $(".login-agency-message.m_ok").append("<a href=\"#\" onclick=\"deleteCookie('logged_agency');location.reload();\">{{ T_logout_amazonPay }}</a>");
    } else {
        setTimeout(function(){ load_userdata_cookie(); }, 500);
    }
}
$(function () {
    // login block
    $(".slider").addClass("agencies_login");
    $(".slider").append($(".popup-agency-login").detach());
    $(".modal_signup .modal_form").append($(".popup-agency-signup").detach());
    $(".popup-agency-login #submit_login_agency").attr("value","{{ T_acceder }}");
    $(".modal_signup .modal_form .popup-agency-signup").prepend($(".register_text").clone());
    $(".modal_signup .modal_form .popup-agency-signup").append($(".accede_button").clone());
    $("#full_wrapper_booking").hide();
    $(".popup-agency-login .title").html($(".recursos .logo_agencias").html());
    if((".title_login").length) {
        $(".popup-agency-login .title").append($(".recursos .title_login").html());
    }
    $("<span class='label'>{{ T_register_ask }}</span>").insertBefore($(".popup-agency-login .btn_wrapper .btn_link"));

    // login completed
    $('#agency-login-form').on('hide', function() {
        load_userdata_cookie();
    });

    // Signup modal
    $("#agency-login-form input[type=button]").prop("onclick", null).off("click");
    $("#agency-login-form input[type=button]").click(function(){
        $(".modal_signup").addClass("active");
        $("body").css("overflow-y","hidden");
    });
    $(".close_modal_signup").click(function(e){
        e.preventDefault();
        $(".modal_signup").removeClass("active");
        $("body").css("overflow-y","auto");
    });
    $(".popup-agency-login .input_wrapper label").click(function(){
        $(this).parent().find("input").focus();
    });
    $(".popup-agency-login .input_wrapper input").on("focus", function(){
        $(this).parent().prev().css({"opacity":"0","z-index":"-1"});
    });
    $(".popup-agency-login .input_wrapper input").on("blur", function(){
        if($(this).val() == ""){
            $(this).parent().prev().css({"opacity":"1","z-index":"2"});
        }
    });

    // content_ventajas
    if($(".content_ventajas").length) {
        $(".content_ventajas").append($(".register_button").clone());
    }
    $(".content_ventajas .prelabel").click(function () {
        let scroll_top = $(this).parent().offset().top;
        $("html, body").stop().animate({scrollTop:scroll_top}, 1000, 'swing');
    });
    $(document).on("click", ".register_button a", function(e){
        e.preventDefault();
        $(".modal_signup").addClass("active");
        $("body").css("overflow-y","hidden");
    });

    $("#lang").click(function () {
        $(this).find("#language-selector-options").slideToggle();
    });

});
</script>
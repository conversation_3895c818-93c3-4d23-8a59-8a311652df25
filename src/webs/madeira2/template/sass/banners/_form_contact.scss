.contact_form_wrapper {
  padding: 100px 0;
  overflow: hidden;

  h3 {
    @include title_styles;
    text-align: center;
    padding-bottom: 50px;
  }

  #contact {
    display: table;
    margin: auto;
    .info {
      display: table;
      position: relative;
    }
    .contInput {
      display: inline-block;
      float: left;
      padding: 10px 0 10px 20px;
      position: relative;

      &:nth-of-type(-n+3) {
        width: calc((100% - 5px) / 3);
        padding-top: 20px;
      }

      &:nth-of-type(4) {
        width: 100%;
      }

      &:nth-of-type(3), &:nth-of-type(5) {
        margin-right: 0;
      }

      input:not([type="checkbox"]), textarea {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: white;
        padding: 15px 25px;
        font-size: 14px;
        border-width: 0;
        width: 100%;
        margin-bottom: 20px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
      }
      textarea {
        height: 150px;
      }
      input, textarea {
        &.error {
          outline: 1px solid red;
        }
      }

      #accept-term, &#privacity {
        width: auto;
        height: auto;
        display: inline-block;
        vertical-align: middle;
      }
    }
    .policy-terms {
      display: inline-block;
      width: auto;
      float: left;
      color: $black;
      font-size: 12px;
      margin: 20px 50px;
    }
    a.myFancyPopup {
      display: inline-block;
      vertical-align: middle;
      color: $black;
    }

    #contact-button {
      @include btn_styles;
      position: absolute;
      right: 0;
      bottom: 20px;
    }
  }
}
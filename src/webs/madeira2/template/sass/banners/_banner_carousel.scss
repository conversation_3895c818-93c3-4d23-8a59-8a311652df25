.banner_carousel {
  height: 800px;
  width: 100%;

  .owl-item {
    height: 800px;

    .banner {
      display: block;
      width: 100%;
      height: 100%;
      background-size: cover;
      position: relative;
      background-color: rgba(black, 0.15);
      background-blend-mode: hue;

      &:after {
        content: '';
        top: 0;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        z-index: 1;
        background: linear-gradient(90deg, rgba(28,195,247,0.3) 0%, rgba(0,0,0,0) 100%);
      }

      .banner_content {
        position: relative;
        text-align: center;
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;

        .title {
          font-family: $title_family;
          color: white;
          font-size: 60px;
          font-weight: 700;
          text-transform: uppercase;
          display: block;
          padding-bottom: 20px;
          margin-bottom: 30px;
          position: relative;

          &:after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            height: 2px;
            width: 200px;
            background: white;
          }
        }

        .description {
          font-family: $title_family;
          color: white;
          font-size: 40px;
          font-weight: 400;
          text-transform: uppercase;
          display: block;
        }

        .button_seemore {
          display: block;
          position: relative;
          width: 70px;
          height: 70px;
          background: transparent;
          border-radius: 50%;
          margin: 30px auto 0;
          border: 5px solid white;

          &:after {
            content: '+';
            @include center_xy;
            color: white;
            font-size: 65px
          }
        }
      }
    }
  }

  .owl-nav {
    .owl-prev {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      left: 30px;

      i {
        font-size: 35px;
        color: white;
      }
    }

    .owl-next {
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      right: 30px;

      i {
        font-size: 35px;
        color: white;
      }
    }
  }
}
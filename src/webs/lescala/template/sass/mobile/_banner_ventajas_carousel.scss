.content__slider-advantages__wrapper {
  @include base_mobile_styles;

  &.container12 {
    width: 100%;
  }

  .slider-advantages {
    position: relative;

    .advantages_element {
      text-align: center;
      position: relative;

      .advantages_icon i {
        font-size: 50px;
        color: #2e4d5a;
        border: 3px solid transparent;
      }

      .advantages_title {
        margin-top: -3px;
        font-size: 15px;
        text-transform: uppercase;
        color: $corporate_1;
        font-weight: 600;
      }

      .advantages_title strong {
        color: $corporate_2;
        font-size: 17px;
        margin-top: 15px;
        display: block;
      }
    }
  }

  .owl-carousel {

    .owl-stage-outer {
      z-index: 1;
    }

    .owl-nav {
      position: absolute;
      z-index: 5;
      width: 100%;
      top: 0;
      bottom: 0;

      > div {
        width: 30px;
        font-size: 40px;
        @include center_y;
        left: 5px;

        &.owl-next {
          left: auto;
          right: 5px;
        }

        i {
          width: 30px;
          height: 30px;
          position: relative;

          &:before {
            top: 85%;
            left: 50%;
            font-size: 35px;
            color: $corporate_2;
            font-weight: 600;
            transform: translate(-50%, -50%);
          }
        }
      }
    }
  }
}
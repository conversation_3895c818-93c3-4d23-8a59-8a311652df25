{# Content subtitle #}
{% if content_subtitle %}
    <div class="content_subtitle_wrapper">
        <h1 class="content_subtitle_title">{{ content_subtitle.subtitle|safe }}</h1>

        <div class="content_subtitle_content">{{ content_subtitle.content|safe }}</div>
    </div>
{% endif %}

{# Content access #}
{% if content_access %}
    <div class="content_access_wrapper {% if my_booking %}my_booking_section{% endif %}">
        {{ content }}
    </div>

    {% if my_booking %}
        <script async>
            $("#emailInput").attr('placeholder', '{{ T_email|safe }}');
            $("#localizadorInput").attr('placeholder', '{{ T_localizador|safe }}');
        </script>
    {% endif %}
{% endif %}

{# Rooms section #}
{% if rooms_blocks %}
    {% include "room_blocks.html" %}
{% endif %}

{# Rooms individuals #}
{% if individual_room %}
    {% include "room_individuals.html" %}
{% endif %}

{# Location #}
{% if content_location %}
    {% include "location_section.html" %}
{% endif %}

{# Offers section #}
{% if offers %}
    {% include "offers_section.html" %}
{% endif %}

{% if bannerx2 or banner_slider %}
    {% if bannerx2 %}
        <div class="bannerx2_wrapper">
            {% for x in bannerx2 %}
                <div class="bannerx2_element">
                    <a href="{{ x.linkUrl|safe }}">
                        <div class="banner_image">
                            <div class="spinner_loading">
                                <i class="fa fa-spinner fa-pulse fa-3x fa-fw"></i>
                            </div>
                            <img data-src="{{ x.servingUrl|safe }}=s570"/>
                        </div>
                        <div class="banner_content">
                            <div class="banner_title">{{ x.title|safe }}</div>
                        </div>
                    </a>
                </div>
            {% endfor %}
        </div>
    {% endif %}
    {% if banner_slider %}
        <div class="banner_slider_wrapper">
            <div class="banner_slider_content">
                <ul class="slides">
                    {% for x in banner_slider %}
                        <li class="slider_element">
                            <div class="slider_image">
                                <div class="spinner_loading">
                                    <i class="fa fa-spinner fa-pulse fa-3x fa-fw"></i>
                                </div>
                                <img data-src="{{ x.servingUrl|safe }}=s1140"/>
                            </div>
                            <div class="slider_content">
                                <div class="slider_title">{{ x.title|safe }}</div>
                                <div class="horizontal_separator"></div>
                                <div class="slider_description">{{ x.description|safe }}</div>
                            </div>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        <script async>
            $(window).load(function () {
                slider_params = {
                    controlNav: true,
                    directionNav: false,
                    animation: "slide"
                };

                loadFlexslider($(".banner_slider_content"), slider_params);
            });
        </script>
    {% endif %}
{% endif %}

{% if cycle_banner %}
<div class="cycle_banner_wrapper">
    {% for x in cycle_banner %}
        <div class="cycle_element">
            <div class="cycle_image">
                <img src="{{ x.servingUrl|safe }}"/>
            </div>
            <div class="cycle_content">
                <div class="cycle_title">{{ x.title|safe }}</div>
                <div class="cycle_description">{{ x.description|safe }}</div>
                <div class="cycle_see_more" style="display: none">{{ T_leer_mas }}</div>
            </div>
        </div>
    {% endfor %}
</div>
    <script async>
        $(window).load(function(){

        })
    </script>
{% endif %}

{% if minigalleryx6 %}
    <div class="minigalleryx6_wrapper effects_sass" sass_effect="slide_up_effect">
        {% if minigalleryx6.subtitle %}
            <div class="minigallery_title">{{ minigalleryx6.subtitle|safe }}</div>
        {% endif %}
        <div class="minigallery_title">{{ minigalleryx6.subtitle|safe }}</div>
        <div class="minigalleryx6_content">
            <ul>
                {% for x in minigalleryx6.pictures %}
                    <li class="minigalleryx6_element">
                        <a href="{{ x|safe }}=s1900" rel="lightbox[minigalleryx6]">
                            <div class="spinner_loading">
                                <i class="fa fa-spinner fa-pulse fa-3x fa-fw"></i>
                            </div>
                            <img data-src="{{ x|safe }}=s350"/>
                            <div class="plus_image">
                                <i class="fa fa-plus fa-3x fa-fw"></i>
                            </div>
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
{% endif %}

{% if minigallery %}
    </div>
    <div class="minigallery_wrapper effects_sass" sass_effect="slide_up_effect">
        {% if minigallery.subtitle %}
            <div class="minigallery_title">{{ minigallery.subtitle|safe }}</div>
        {% else %}
            <div class="minigallery_title">{{ T_images_gallery }}</div>
        {% endif %}
        <div class="minigallery_content">
            <ul class="slides">
                {% for x in minigallery.pictures %}
                    <li class="minigallery_element">
                        <a href="{{ x|safe }}=s1900" rel="lightbox[minigallery]">
                            <div class="spinner_loading">
                                <i class="fa fa-spinner fa-pulse fa-3x fa-fw"></i>
                            </div>
                            <img data-src="{{ x|safe }}"/>
                        </a>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
    <script async>
        $(window).load(function () {
            slider_params = {
                controlNav: false,
                prevText: '<i class="fa fa-chevron-left" aria-hidden="true"></i>',
                nextText: '<i class="fa fa-chevron-right" aria-hidden="true"></i>',
                minItems: 4,
                maxItems: 4,
                itemWidth: ($(window).width() / 4),
                directionNav: true,
                animation: "slide",
                move: 1
            };

            loadFlexslider($(".minigallery_content"), slider_params);
        });
    </script>
    <div id="wrapper_content" class="container12">
{% endif %}
body {
  font-family: '<PERSON><PERSON><PERSON>', sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 25px;
  color: $gray-1;
}

.myFancyPopup_personalized_class {
  .fancybox-inner {
    overflow: scroll !important;
  }
}

.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking{
  color: #02307c;
  font-weight: 500;
}

#hidden_legal_alert, #use_conditions_alert {
  font-size: 11px;
  padding-top: 5px;
  line-height: 15px;
  font-family: "Open Sans", sans-serif;
  text-align: justify;

  p:first-of-type {
    color: #B99E87;
    text-align: center;
    font-size: 15px;
    margin-bottom: 25px;
    font-weight: lighter;
  }

  strong {
    color: #B99E87;
    font-size: 15px;
    margin-bottom: 25px;
    font-weight: lighter;
  }

  p {
    font-size: 11px;
    padding-top: 5px;
    line-height: 15px;
    font-family: "Open Sans", sans-serif;
    text-align: justify;
    -webkit-margin-before: 1em;
    -webkit-margin-after: 1em;
  }
}

#popup-banner, .popup_inicio {
  img {
    display: block;
  }
}

i {
  font-style: italic !important
}

.wrapper-new-web-support {
  padding-top: 5px !important;
  padding-bottom: 5px !important;
  margin-top: 0px;
  opacity: 0.9;
  font-size: 16px;
}

a {
  text-decoration: none;
}

.super-header {
  position: fixed;
  top: 0;
  z-index: 401;
  width: 100%;
  color: white;
  padding: 6px 0px 2px;

  a {
    color: white;

    &:hover {
      color: rgb(240, 240, 240);
    }
  }
}

#ticks-container {
  float: left;
  text-align: right;
  overflow: hidden;
  color: white;
  font-size: 11px;

  .ticks {
    padding-left: 25px;
    display: inline-block;
    width: 100px;
    line-height: 11px;
    text-align: left;
  }
}

.ticks-en {
  margin-left: 34px;
  .ticks {
    margin-right: 10px;
    margin-left: 5px;
    padding-left: 20px !important;
  }
}

.ticks-fr .ticks {
  width: 99px !important;
  font-size: 10px;
}

#tick1 {
  background: url("/img/ibers/ticks/pago-directo.png?v=1") no-repeat;
  background-size: 14px;
}

#tick2 {
  background: url("/img/ibers/ticks/sin-gastos.png?v=1") no-repeat;
  background-size: 17px;
}

#tick3 {
  background: url("/img/ibers/ticks/pago-seguro.png?v=1") no-repeat;
  background-size: 15px;
}

.top-sections {
  width: auto !important;
  float: right;
  font-size: 12px;
  margin-left: 0px !important;
  padding-left: 5px;
  text-align: right;
}

#lang {
  font-size: 12px;
  line-height: 12px;
  float: right;
  text-align: right;
  margin-top: 6px;
  width: 130px;
  margin-left: 0;

  .selected {
    color: rgb(240, 240, 240);
  }
  a {
    display: inline-block;
    padding: 0px 4px;
    text-transform: uppercase;
  }
}

.phone {
  width: 150px !important;
  font-size: 12px;
  line-height: 12px;
  float: right;
  text-align: right;
  margin-right: 0px;
  margin-top: 6px;
  border-right: 1px solid white;
  padding-right: 5px;

}

.ca .phone {
  width: 145px !important;
}

header {
  background: rgba(0, 0, 0, .75);
  position: fixed;
  width: 100%;
  z-index: 401;
  top: 37px;
}

.wrapper-header {
  position: relative;
}

#logoDiv {
  position: absolute;
  z-index: 999;
  top: -37px;
  width: 235px !important;
}

#main-sections {
  width: 870px;
  float: right;
}

#mainMenuDiv {
  font-size: 12px;
  z-index: 100;
  clear: both;
  height: 36px;
}

li.main-section-subsection {

  &:first-child {
    margin-top: 2px;
  }
  margin-top: 1px;
  a {
    background: rgba(0, 0, 0, 0.75);
    min-width: 96px;
  }
}

.main-link {
  padding: 4px 8px 5px;
  text-decoration: none;
  text-transform: uppercase;
  color: $white;
  display: inline-block;
}

.section-active a {
  padding: 4px 3px 5px;
  text-decoration: none;
  text-transform: uppercase;
  display: inline-block;
  color: $orange !important;
}

#main-sections-inner ul {
  display: none;
}

#main-sections-inner div ul {
  position: absolute;
}

#main-sections-inner li ul {
  position: absolute;
}

#main-sections-inner div li {
  float: none;
  display: block;
}

#main-sections-inner {
  text-align: justify;
  height: 40px;
}

#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

#main-sections-inner > div {
  display: inline-block;
  cursor: pointer;
}

.main-section-div-wrapper a {
  line-height: 30px;
  text-transform: uppercase;

  &:hover {
    color: $orange !important;
  }
}

.link-reservar a {
  color: $white !important;
}

/************************ SLIDER AND BOOKING ***********************/

#slider_container {
  margin-top: 37px;

  .button-promotion.promotion-view-more {
    color: white;
    background: #eca201;
    font-family: "Roboto", sans-serif;
    font-size: 18px;
    -webkit-transition: none;
    -moz-transition: none;
    -o-transition: none;
    -ms-transition: none;
  }
  .button-promotion.promotion-view-more:hover {
    background: darken(#eca201, 20%);
    transition: none;
  }
}

.booking_widget {
  top: 180px;

  h4.best_price {
    position: relative;
  }

  .booking_form_title {
    padding-bottom: 0px;
    padding-top: 20px;
  }

  .booking_form_title h4:before {
    content: "";
    width: 150px;
    border-top: 1px solid white;
    position: absolute;
    left: 0px;
    right: 0px;
    margin: auto;
    top: -5px;

  }
  .booking_form_title h4:after {
    content: "";
    width: 150px;
    border-bottom: 1px solid white;
    position: absolute;
    left: 0px;
    right: 0px;
    bottom: -5px;
    margin: auto;
  }

}

.booking_form {
  background: $orange;
}

.stay_selection .entry_date_wrapper label,
.stay_selection .departure_date_wrapper label,
.stay_selection .rooms_number_wrapper label,
.room .room_title,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  color: white;
}

.date_box .date_year,
.date_box .date_day {
  font-weight: 300;
  color: $gray-1;
}

.date_box .date_day {
  line-height: 16px;
  padding-top: 6px;
}

.selectric .button {
  background: $orange-2 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
}

.ibersol-soncaliumar .wrapper_booking_button {
  margin-top: 70px;
}

.wrapper_booking_button .promocode_input {
  width: 80px;
  font-size: 13px;
}

.wrapper_booking_button .submit_button {
  width: 170px;
  background: #0081c3;
  cursor: pointer;
  border: 1px solid white;

  &:hover {
    background: $blue;
  }
}

.fr .web_support_label_1 {
  font-size: 13px;
}

#booking_widget_popup {

  .booking_form_title {
    padding-top: 5px;
  }
}

.ui-widget-header {
  background: $orange;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $orange;
  color: white;
}

.tp-banner {
  .button-promotion {
    img:hover {
      opacity: 0.6;
    }
  }

}

.bullet {
  background: url(/img/ibers/sprite_bullet.png?v=1) no-Repeat top left !important;
}

.bullet.selected {
  background-position: bottom left !important;
}

.bullet:hover {
  background-position: bottom left !important;
}

/* ACCORDION */
.accordion-wrapper {
  margin-top: 40px;
  .main-title {
    font-size: 27px;
    color: $orange;
    margin-bottom: 20px;
    text-align: center;
  }

}

/********************* CONTENT ********************/

.main-content {
  text-align: center;
  font-size: 16px;
  line-height: 25px;

  .main-title {
    font-size: 27px;
    color: $orange;
    margin-bottom: 20px;
  }
  .section-description, p {
    font-family: 'Roboto', sans-serif;
    font-weight: 300;
  }

}

.blocks-content-section {
  margin-top: 40px;

  .sub-description-rooms {
    margin: 10px 0px;
  }
  .rooms-description {
    min-height: 210px;
  }

  span.btn-corporate,
  span.btn-flecha {
    text-align: center;
  }
}

#full-wrapper-banners1 {

  .title-banner {
    font-size: 20px;
    color: $orange;
    margin: 10px 0px;
  }
  .moreinfo-banner {
    color: white;
    background: $orange;
    border-radius: 4px;
    padding: 5px 10px;

  }

  .moreinfo-banner:hover {

    background: $corporate_2;
  }

  .description-banner {
    font-family: 'Roboto', sans-serif;
    font-weight: 300;
    margin-bottom: 15px;
  }
}

.banner1-pos-inicio {
  margin-top: 60px;
}

#wrapper-banners2 {

  .wrapper-banner2 {
    height: 305px;
    width: 550px;
    position: relative;
    overflow: hidden;

  }

  .banner2-top-img {

    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;
  }

  .wrapper-banner2:hover {

    .banner2-top-img {
      opacity: 0.5;
    }
  }

  .wrapper-banner2 img {
    position: absolute;
  }
  .wrapper-banner2 .content-banner2 {
    position: absolute;
    bottom: 0px;
    width: 100%;
    text-align: center;
    background: rgba(0, 0, 0, .7);
    -ms-filter: "progid:DXImageTransform.Microsoft.gradient(startColorstr=#B2000000,endColorstr=#B2000000)"; /* IE8 */
  }
  .wrapper-banner2 .title-banner2 {
    font-size: 27px;
    line-height: 100px;
    color: white;
    display: inline-block;
    padding-left: 24px;
    padding-right: 5px;
  }
  .wrapper-banner2 span {
    position: absolute;
    width: 20px;
    border-bottom: 3px solid white;
    top: 50px;
  }
}

.newsletter-section {
  margin: 60px 0;
  height: 230px !important;
}

#newsletter {
  text-align: center;
  color: white;
  padding: 25px;
  box-sizing: border-box;

  h2 {
    font-size: 27px;
    line-height: 40px;
    text-transform: uppercase;
    margin-bottom: 20px;
    margin-top: 10px;
  }
  #suscEmail {
    width: 220px;
    height: 50px;
    font-size: 16px;
    color: white;
    text-align: center;
    border: 1px solid white;
    background: transparent;
    @include border-radius(5px);
    outline: none;
  }
  ::-webkit-input-placeholder {
    color: white;
    font-size: 16px;
    line-height: 14px;
    text-align: center;
  }
  :-moz-placeholder {
    color: white;
    font-size: 16px;
    line-height: 14px;
    text-align: center;
  }
  ::-moz-placeholder {
    color: white;
    font-size: 16px;
    line-height: 14px;
    text-align: center;
  }
  :-ms-input-placeholder {
    color: white;
    font-size: 16px;
    line-height: 14px;
    text-align: center;
  }
}

#newsletterButtonExternalDiv {
  display: inline-block;

}

.button-newsletter {
  @include border-radius(5px);
  width: 220px;
  height: 54px;
  font-size: 16px;
  border: none;
  outline: none;
  margin-left: 20px;
  text-transform: uppercase;
  cursor: pointer;
  background-color: white;

  &:hover {
    color: white;
  }
}

.newsletter_checkbox {
  font-size: 10px;
  width: 689px;
  margin-left: 307px;
  text-align: left;

  a {
    text-decoration: underline !important;
    color: white;
  }
}

/*=========== Noticias =======*/
.new {
  margin-bottom: 30px;
  margin-top: 30px;
  padding-bottom: 30px;
  border-bottom: 1px solid rgba(80, 183, 1, 0.18);

  &:last-child {
    border-bottom: 0;
    padding-bottom: 0;
  }

  .photo-container {
    width: 300px;
    height: 200px;
    overflow: hidden;
    position: relative;
    float: left;

    img {
      position: absolute;
      left: -100%;
      right: -100%;
      top: -100%;
      bottom: -100%;
      margin: auto;
      min-height: 100%;
      min-width: 509px;
    }
  }
}

.block-new-description {
  display: inline-block;
  width: 800px;
  height: 200px;
  padding: 0px 20px;
  position: relative;

  .date {
    color: $corporate_1;
    font-size: 20px;
    margin-bottom: 5px;
    text-align: left;
  }

  .news-title {
    text-transform: uppercase;
    font-size: 17px;
    font-weight: 700;
    margin-bottom: 5px;
    color: $gray-1;
    text-align: left;
  }

  ul .read-more-news {
    text-decoration: none;
    display: block;
    text-align: left;
    position: absolute;
    bottom: 0px;

    color: white;
    background: $corporate_4;
    font-weight: 100;
    padding: 5px 25px;
    margin-right: 6px;
    margin-top: -3px;
  }
}

/********************** weeding form ***********************/
.form_wedding-container {
  margin-top: 60px;

  h3 {
    font-size: 27px;
    color: $orange;
    text-align: center;
    margin: 0px 0px 40px;
  }
}

#wedding-form {
  padding: 30px;
  margin: 0 auto;
  background: #F6F6F7;

  li {
    margin: 10px 15px;
    display: inline-block;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
  li label {
    width: 319px;
    display: block;
    font-size: 14px;
  }
  li input[type="text"] {
    width: 319px;
    display: inline-block;
    height: 20px;
    padding: 4px;
    font-size: 12px;
    background: white;
    border: none;
  }
  li.last-input {
    display: block;
    width: 100%;
    text-align: center;
    margin: 10px 0px;

    p {
      font-size: 12px;
    }

    a {
      font-size: 12px;
      color: cornflowerblue;
    }
  }
  a#contact-button {
    display: block;
    width: 200px;
    padding: 8px 0px;
    background: $orange;
    color: white;
    text-transform: uppercase;
    margin: auto;
    text-align: center;
    cursor: pointer;
  }
  .error {
    font-size: 12px;
    color: red;
  }
}

/********************** MY BOOKINGS ***********************/
.booking-content {
  text-align: center;
  margin-bottom: 20px;
}

#my-bookings-form-fields {
  text-align: center;

  label {
    display: block;
    color: $corporate_1;
    text-transform: uppercase;
  }
  input {
    display: block;
    margin: 0 auto;
    margin-bottom: 10px;
    width: 250px;
    height: 15px;
    padding: 5px;
    text-align: center;
    color: black;
    border: none;
    background: $gray-4;
  }
  #my-bookings-form-search-button {
    width: 260px;
    color: white;
    background-color: $corporate_1;
    padding: 10px 0;
    border: none;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: $corporate_2;
    }
  }
}

#cancel-button-container {
  text-align: center;

  #cancelButton {
    width: 260px;
    color: white;
    background-color: $corporate_1;
    padding: 10px 0;
    border: none;
    font-size: 14px;
    cursor: pointer;
    display: none;
    margin: 0 auto;

    &:hover {
      background-color: $corporate_2;
    }
  }
}

.my-bookings-booking-info {
  margin: 0 auto !important;
}

/****************** GALLERY ********************/

.gallery_1 li .crop {
  border: 2px solid white;
  box-sizing: border-box;
  position: relative;
}

.images-note {
  margin-top: 20px;
}

.filter-offers {
  margin-bottom: 30px;
}

.nav-tabs li {
  display: block;
  float: left;
  box-sizing: border-box;
  width: 190px;
  margin: 0;
  padding: 0;
  border: 1px solid white;
}

.nav-tabs li a {
  display: block;
  padding: 20px 0px;
  background: $orange;
  color: white;
  text-transform: uppercase;
  font-weight: lighter;

  &:hover {
    background: $orange-2;
  }
}

.nav-tabs li.active a {
  background: $orange-2;
}

.play_video_gallery {
  position: absolute;
  width: 60px !important;
  min-width: 60px !important;
  height: 60px;
  min-height: 60px !important;
  z-index: 100;
  top: 35%;
  left: 40%;
}

.gallery_1 li .crop img {
  max-width: 200px;

  &:hover {
    max-width: 200px;
  }
}

.gallery-mosaic {
  display: table;
  width: auto;
}

//Size tabs Ibersol La Casona

.ibersol-lacasona .nav-tabs li {
  width: 190px;
}

/****************** FOOTER ********************/

footer {
  margin-top: 60px;
  background: $blue;
  padding: 40px 0;
  color: white;
  font-weight: 300;
  font-size: 14px;

  a {
    color: white;
    line-height: 20px;

    &:hover {
      color: $gray-4;
    }
  }
}

//specific brown footer Ibersol La Casona

.ibersol-lacasona footer {
  background: #a88154;
}

.footer_column .footer_column_title,
.footer_column.last h2 {
  padding-bottom: 10px;
  border-bottom: 1px solid white;
  margin-bottom: 10px;
  text-transform: uppercase;
}

.full-copyright {
  margin-top: 20px;
  font-size: 14px;
  text-align: center;

  p {
    margin-bottom: 0px;
  }
}

#social {
  margin-left: 0px;
  img:hover {
    opacity: 0.7;
  }
}

.image_link_footer {
  img {
    padding-top: 14px;
    display: table;

  }
}

.footer-links {
  text-align: justify;
  width: 1120px;

  h3.footer_column_title {
    padding-bottom: 10px;
    border-bottom: 1px solid white;
    margin-bottom: 10px;
    text-transform: uppercase;
    width: 1120px;
  }

}

//SPECIFICS INDIVIDUALS WEB COLORS
//ALWAYS AT THE END
//If an advantage property exits, they are going to be overwritten

.fondo-individual {
  background-color: $corporate_3;
}

.color-individual {
  color: $corporate_3;

}

.hover-fondo-individual:hover {

  background-color: $corporate_3;

}

.hover-color-individual:hover {
  color: $corporate_3;

}

//booking engine in fancybox (for selects!)
.fancybox-inner {
  overflow: visible !important;

}

.popup-start .fancybox-outer .fancybox-inner {
  overflow: auto !important;
}

#main-sections .rooms {
  margin-bottom: 0px;
}

/*===== News ====*/
div#new-detail {
  #new-image p {
    color: $corporate_1;
  }

  .description {
    margin-top: 20px;
  }

  .addthis_toolbox.addthis_default_style {
    margin-top: 0px;
  }

  .share-this {
    margin-top: 25px;
    color: $corporate_4;
    text-align: left;
  }
}

.news_description {
  strong {
    font-weight: bolder;
  }
}

/*===== Hidden booking widget ====*/
.hidden_booking_widget {
  display: none;
  position: fixed;
  top: 77px;
  width: 100%;
  z-index: 400;
  border-top: 5px solid $corporate_4;
  box-shadow: 0 1px 3px #969393;
  background: $corporate_1;
  background: $corporate_1;

  .my_bookings_in_widget {
    display: none;
  }

  .booking_widget {
    position: relative;
    top: 0 !important;
  }

  .booking_form {
    width: 1000px;
    margin: auto;
    position: relative;
    background: transparent;
  }

  .destination_wrapper, .room_list_wrapper, .stay_selection {
    display: inline-block;
    vertical-align: top;
  }

  .room_list_wrapper {
    margin-top: 1px;
  }

  .promocode_text {
    font-size: 12px;
    padding-top: 0;
    text-transform: capitalize;
    width: 50%;
    color: white;
  }

  .wrapper_booking_button .promocode_input, .wrapper_booking_button, .wrapper_booking_button button {
    display: inline-block;
    margin-top: 0;
    border: 0;
    border-radius: 0px;
  }

  .destination_wrapper input {
    width: 190px;
    border: 0;
  }

  div#horizontal_booking_widget {
    width: 100%;
  }

  .destination_wrapper {
    float: left;
    margin-top: 1px;
    margin-right: 10px;
  }

  .promocode_input {
    width: 120px;
    margin-left: 6px;
    height: 44px;
  }

  .wrapper_booking_button button {
    width: 120px;
    margin-left: 10px;
    height: 44.5px;
  }

  .selectricWrapper {
    width: 120px;
  }

  .room .room_title, .room .adults_selector, .room .children_selector, .room .babies_selector, .stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper, .stay_selection .rooms_number_wrapper {
    width: auto;
  }

  .wrapper_booking_button {
    vertical-align: top;
    top: 2px;
    margin-top: 1px;
  }

  .rooms_number_wrapper {
    margin-top: 4px;
  }

  .date_box {
    width: 90px;
    height: 39px;
    background: white;
    border: 0;
    border-radius: 0px;
    padding: 3px;
  }

  .stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper {
    margin-top: 4px;
  }

  .selectric {
    height: 45px;
    border: 0;
    border-radius: 0px;
  }

  .room .room_title {
    margin-left: 10px;
    margin-top: 33.5px;
  }

  .wrapper-new-web-support.booking_form_title {
    background: $corporate_3;
    color: white;
    display: block;
  }

  .wrapper-new-web-support {
    padding: 5px 0px 10px;
  }

  .hotel_selector {
    z-index: 23;
    top: 90px !important;
    left: 18px;
  }

  .booking_form .stay_selection .entry_date_wrapper label, .booking_form .stay_selection .departure_date_wrapper label, .booking_form .stay_selection .rooms_number_wrapper label, .booking_form .room .adults_selector label, .booking_form .room .children_selector label {
    width: 100%;
    text-align: center !important;
    margin-left: 0 !important;
    display: block;
  }

  .room {
    margin-bottom: 2px;
  }

  .wrapper_booking_button {
    top: 2px;
  }

  .right_arrow {
    -webkit-transform: none;
    -moz-transform: none;
    -ms-transform: none;
    -o-transform: none;
    transform: none;
  }

  .date_box.entry_date.calendar, .date_box.departure_date.calendar {
    background: white;

    .date_day, .date_year {
      display: block;
    }
  }

  .date_box .date_day {
    .day {
      text-align: right;
    }

    .month {
      margin-right: 10px;
    }
  }

  .selectric .label {
    font-size: 11px;
    margin-left: 10px;
    color: #ababab;
    line-height: 42px;
  }

  .promocode_input {
    text-align: left;
    font-size: 11px;
    padding-left: 10px;
    color: #ababab;
    width: 120px !important;
    box-sizing: border-box;
    display: inline-block !important;
    height: 44.5px !important;
    opacity: 1 !important;
    margin: 0 0px 0 6px !important;
    padding-left: 10px !important;

    &::-webkit-input-placeholder {
      /* WebKit browsers */
      color: #ababab !important;
    }
    &:-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: #ababab !important;
      opacity: 1;
    }
    &::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: #ababab !important;
      opacity: 1;
    }
    &:-ms-input-placeholder {
      /* Internet Explorer 10+ */
      color: #ababab !important;
    }
  }

  .wrapper_booking_button .spinner_wrapper {
    position: absolute;
    left: 170px;
    bottom: 23px;
  }

  .destination_wrapper label {
    color: white;
  }

  .booking_form .stay_selection .entry_date_wrapper label, .booking_form .stay_selection .departure_date_wrapper label, .booking_form .stay_selection .rooms_number_wrapper label, .booking_form .room .room_title, .booking_form .room .adults_selector label, .booking_form .room .children_selector label {
    color: white !important;
  }

  .selectric .button {
    right: 5px;
  }
}

.ui-datepicker {
  z-index: 1004 !important;
}

/*=== Specific languages ====*/
body.it {
  #tick3 {
    width: 120px !important;
  }

  .btn-flecha {
    width: 78px;
    top: 16px;
  }

  .rooms span.btn-corporate {
    /*right: -5px;*/
  }

  span.btn-corporate {
    /*top: 8px;*/
  }
}

/*============== Bottom Pop-up ============*/
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 120px;
  background: #ffa902;
  box-shadow: 0 -6px 10px -6px #333;
  left: 0;
  bottom: 0;
  z-index: 1000;

  input[name="phone_input"] {
    margin-top: 40px;
    height: 25px;
    padding: 4px 10px;
    width: 290px;
    font-size: 14px;
    vertical-align: top;
  }
}

.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

.bottom_popup .bottom_popup_text {
  font-family: Source Sans Pro;
  font-size: 21px;
  margin-top: 36px;
  color: black;
  padding: 10px;
  font-weight: bold;
  margin-left: 85px;
  float: left;
  margin-right: 35px;
}

.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

.close_button {
  float: right;
  cursor: pointer;
}

.button-popup {
  width: auto;
  display: inline-block;
  margin-top: 40px;
  vertical-align: top;
}

.button-popup a {
  display: inline-block;
  background: #0081c3;
  border: 0;
  color: white;
  background-position: center;
  cursor: pointer;
  font-size: 12px;
  text-transform: uppercase;
  text-decoration: none;
  border-radius: 4px;
  box-shadow: 2px 3px 6px #004061;
  padding: 5px 30px;

  &:hover {
    opacity: 0.8;
  }
}

#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

.popup_inicial {
  width: 800px;
  height: 100%;
  background-size: cover !important;
  display: table;

  .email, .discount, .compra {
    text-align: center;
  }

  .compra {
    padding-top: 5px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }

  .discount {
    padding-top: 7px;
    color: white;
    font-size: 47px;
    text-shadow: 3px 3px black;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
  }

  .email {
    padding-top: 39px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }

  form.form_popup {
    text-align: center;
    padding-top: 50px;

    li {
      text-align: center;
    }

    input#id_email {
      height: 26px;
      text-align: center;
      width: 270px;
      font-size: 17px;
      box-shadow: 2px 2px black;
      border: 0px;
      color: $corporate-1;
    }

    button.popup_button {
      margin: 7px 0px 3px 20px;
      width: 277px;
      height: 40px;
      background: $corporate-1;
      font-size: 17px;
      border: 0px;
      text-transform: uppercase;
      color: white;
      cursor: pointer;
    }
  }

  .spinner_wrapper_faldon {
    padding-top: 20px;
  }

  .popup_message {
    color: white;
    padding-top: 25px;
    font-size: 20px;
    font-weight: lighter;
  }
}

.picture-bigskirt {
  float: left;
  position: relative;
  bottom: 21px;
  display: block;
}

/*=== Widget c2c ===*/
div#dialoga-animate-widget {
  display: none !important;
}

p.wait_message {
  margin-top: 4px;
  color: #0081c3;
  font-weight: 500;
  display: none;
  font-size: 13px;
}
///////////////////////////////////////////
/////* LOCATION AND CONTACT SECTION*/////
///////////////////////////////////////////
#wrapper_content_contact {
  background: white;
  padding: 20px;
  box-sizing: border-box;
  margin-bottom: 20px;

  .location-info, .form-contact {
    width: 520px !important;
  }

}

.location-info-and-form-wrapper h1 {
  font-family: 'Montserrat', sans-serif;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px $gray-3 !important;
  margin-bottom: 30px;
  color: $corporate_1;
  height: 29px;
}

.location-info-and-form-wrapper {
  p, strong {
    color: dimgrey;
  }
}

.location-info {
  text-align:left;

  p {
    margin-bottom: 10px;
    font-weight: 300;
  }
  strong {
    font-weight: bold;
  }
}

#location-description-intro {
  margin-bottom: 30px;
}



//customer support form
.form-contact{
  margin-bottom:60px;
}
.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;

  a {
    text-decoration: underline !important;
    color: $corporate_1;
  }
}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 5px;
  margin-top:15px;
  color: dimgrey;
  font-weight: 300;
  font-size: 16px;
}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px !important;
  height: 30px;
  background-color: #d1d1d1;
  color: dimgrey;
  padding-left: 3px;
  font-size: 16px;
}

.form-contact #contactContent .bordeInput{
 width: auto;
 margin-right: 5px;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 100%;
  border: 0px;
  background-color: #a0a0a0;
  color: dimgrey;
  text-align: center;
  padding-left: 3px;

}

.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: -5px;
}

.form-contact #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: $corporate-1 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;

}

div#wrapper_content {
  margin: 50px auto 0px;
}

.location-info {
  color: black;
  font-size: 14px;
  font-family: 'Roboto', sans-serif;
  font-weight: 300;
  line-height: 28px;
}

.form-contact #contact-button:hover {
  background: rgb(118, 90, 57) !important;
}

.form-contact #contact .contInput textarea {
  background-color: #D1D1D1;
  color: dimgrey;
  text-align: left;
  font-size: 16px;
}
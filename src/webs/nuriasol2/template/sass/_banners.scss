/* === === === == banner_icons == === === === */
.banner_icons_wrapper {
  position: relative;
  background-color: $corporate_1;
  padding: 40px calc((100% - 900px) / 2) 15px;
  text-align: center;
  .back {
    @include full_size;
    background-size: cover;
    background-attachment: fixed;
    mix-blend-mode: overlay;
    opacity: .2;
  }
  h3 {
    position: relative;
    z-index: 2;
    @include title_banner;
    color: white;
    text-align: left;
  }
  .icon {
    position: relative;
    z-index: 2;
    display: inline-block;
    width: 165px;
    margin: 0 5px;
    vertical-align: top;







    i.fa {
      display: block;
      margin: auto;
      font-size: 70px;
      color: white;
      @include transition(color, .6s);
    }
    span {
      display: inline-block;
      width: 100%;
      padding: 15px 0;
      color: $corporate_2;
      text-transform: uppercase;
      letter-spacing: 2px;
      font-size: 12px;
      font-weight: 700;
      line-height: 14px;
      opacity: 0;
      @include transition(opacity, .6s);
    }
    &:hover {
      i.fa {
        color: $corporate_2;
      }
      span {
        opacity: 1;
      }
    }
  }
}


/* === === === == banner_destacados == === === === */
.banner_destacados_wrapper {
  position: relative;
  padding: 100px calc((100% - 900px) / 2);
  .imago_back {
    background-position: center 100px !important;
  }
  h2 {
    position: relative;
    z-index: 2;
    @include title_banner;
    text-align: right;
    margin-bottom: 30px;
    span {
      text-align: left;
    }
    big {
      font-size: 300%;
    }
  }
  .banner_destacados {
    position: relative;
    z-index: 2;
    display: table;
    width: calc(260px * 3);
    margin: auto;
    text-align: center;
    .banner {
      float: left;
      .image, .banner_content {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        width: 260px;
        height: 260px;
      }
      .image {
        box-shadow: 0 0 20px rgba(0,0,0,0.5);
        z-index: 3;
        overflow: hidden;
        img {
          vertical-align: middle;
          @include center_image;
          width: auto;
        }
      }
      .banner_content {
        background-color: $corporate_2;
        color: white;
        padding: 20px;
        text-align: center;
        h3 {
          @include title_banner;
          color: white;
        }
        .desc {
          font-size: 14px;
        }
        a.banner_link {
          position: absolute;
          bottom: 20px;
          left: 40px;
          right: 40px;
          display: block;
          background: white;
          color: $corporate_2;
          text-transform: uppercase;
          text-align: center;
          padding: 5px;
          letter-spacing: 2px;
          font-weight: 700;
          @include transition(all, .4s);
          &:hover {
            background-color: $corporate_1;
            color: white;
            margin-bottom: 3px;
            box-shadow: 3px 3px 30px rgba(0,0,0,0.5);
          }
        }
      }
      &:nth-child(3n) {
        .image {
          float: right;
        }
        .banner_content {
          float: left;
          background-color: $corporate_1 !important;
          a.banner_link {
            color: $corporate_1 !important;
            &:hover {
              background-color: $corporate_2 !important;
            }
          }
        }
      }

      &:nth-child(2n) {
        float: right;
        .image {
          display: block;
        }
        .banner_content {
          background-color: $corporate_1;
          a.banner_link {
            color: $corporate_1;
            &:hover {
              background-color: $corporate_2;
            }
          }
        }
      }
      &:last-child {
        float: left;
        .image {
          display: inline-block;
        }
        .banner_content {
          background-color: $corporate_2;
          a.banner_link {
            color: $corporate_2;
            &:hover {
              background-color: $corporate_1;
              color: white;
            }
          }
        }
      }
    }
  }
}


/* === === === == banner_map == === === === */
.banner_map_wrapper {
  position: relative;
  display: table;
  width: 100%;
  height: 470px;
  iframe {
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
  }
  .map_widget {
    position: relative;
    z-index: 1;
    margin-left: calc((100% - 1140px) / 2);
      .iframe-google-maps-wrapper {
        background-color: rgba($corporate_1,.95);
        width: 420px;
        padding: 30px;
        h3 {
          position: relative;
          z-index: 2;
          @include title_banner;
          color: white;
          text-align: center;
        }
        input {
          display: inline-block;
          vertical-align: middle;
          padding: 15px;
          width: 210px;
          background-color: rgba(255,255,255,1);
          border-width: 0;
        }
        button {
          display: inline-block;
          vertical-align: middle;
          padding: 12px 20px;
          text-transform: uppercase;
          background-color: $corporate_2;
          color: white;
          font-weight: 700;
          font-size: 14px;
          border-color:transparent;
          cursor: pointer;
        }
        .go_map {
          display: block;
          text-align: center;
          i.fa {
            background-color: $corporate_2;
            color: $corporate_1;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-bottom: -20px;
            position: relative;
            &:before {
              @include center_xy;
            }
          }
        }
      }
  }
  .banner_map {
    position: relative;
    z-index: 1;
    margin-left: calc((100% - 1140px) / 2);
    width: 420px;
    background-color: rgba($corporate_1,.95);
    color: white;
    min-height: 470px;
    padding: 50px;
    letter-spacing: 2px;
    h3 {
      position: relative;
      z-index: 2;
      @include title_banner;
      color: white;
    }
    a {
      color: white;
      &:hover {
        text-decoration: underline;
      }
    }
    .link_map {
      position: absolute;
      bottom: 20px;
      left: 40px;
      display: inline-block;
      background: $corporate_2;
      color: white;
      text-transform: uppercase;
      text-align: center;
      letter-spacing: 2px;
      padding: 5px 40px;
      font-weight: 700;
      cursor: pointer;
      @include transition(all, .4s);
      &:hover {
        background-color: white;
        color: $corporate_1;
        margin-bottom: 3px;
        box-shadow: 3px 3px 30px rgba(0,0,0,0.5);
      }
    }
  }
}

/* === === === == opinions_wrapper == === === === */
.opinions_wrapper {
  padding: 35px calc((100% - 900px) / 2) 100px;
  text-align: center;
  position: relative;
  .imago_back {
    background-position: center 100px !important;
  }
  .score_wrapper{
    text-align: right;
    h3 {
      position: relative;
      z-index: 2;
      display: inline-block;
      @include title_banner;
      padding-right: 40px;
      .media_opinion {
        position: relative;
        font-size: 150%;
        font-weight: 700;
        padding: 3px 10px 3px 25px;
        background-color: $corporate_1;
        color: white;
        @extend .fa-star;
        &:before {
          @extend .fa;
          @include center_y;
          left: 5px;
          font-size: 16px;
        }
      }
    }
  }
  .opinions {
    .opinion {
      position: relative;
      background-color: white;
      border-radius: 5px;
      padding: 50px 20px 20px;
      margin: 10px 0 20px;
      box-shadow: 0 0 30px rgba(0, 0, 0, .15);
      .grade {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 2;
        font-size: 150%;
        font-weight: 700;
        border-radius: 5px 0 0 0;
        padding: 3px 10px 3px 25px;
        background-color: $corporate_1;
        color: white;
        @extend .fa-star;
        &:before {
          @extend .fa;
          @include center_y;
          left: 5px;
          font-size: 16px;
        }
      }
      .channel {
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        text-align: right;
        overflow: hidden;
        border-radius: 0 5px 0 0;
        background-color: #F9F9FB;
        padding: 7px 10px 0;
      }
      .comment {
        font-size: 12px;
        color: $corporate_1;
        letter-spacing: 1px;
        text-align: left;
        padding: 20px;
        .desc_title_opin {
          font-size: 16px;
          strong {
            font-weight: 700;
          }
        }
      }
    }
  }
  .opinions-button {
    display: inline-block;
    padding: 10px 30px;
    background-color: $corporate_2;
    color: white;
    position: relative;
    z-index: 3;
    @include transition(all,.6s);
    &:hover {
      -webkit-transform: translate(-3px,-3px);
      -moz-transform: translate(-3px,-3px);
      -ms-transform: translate(-3px,-3px);
      -o-transform: translate(-3px,-3px);
      transform: translate(-3px,-3px);
      box-shadow: 3px 3px 30px rgba(0,0,0,0.5);
      background-color: $corporate_1;
    }
  }
}
#opinions-form {
  font-size: 14px;
  h3 {
    font-size: 16px;
    font-weight: 700;
    text-align: center;
    text-transform: uppercase;
  }
  label {
    display: block;
    margin-top: 10px;
  }
  input, textarea {
    width: 400px;
    display: block;
    padding: 20px;
    border: 1px solid lightgrey;
  }
  #opinions-button {
    display: block;
    margin-top: 10px;
    font-weight: 700;
    text-align: center;
    text-transform: uppercase;
    padding: 10px;
    background-color: $corporate_2;
    color: white;
    @include transition(all,.6s);
    &:hover {
      -webkit-transform: translate(-3px,-3px);
      -moz-transform: translate(-3px,-3px);
      -ms-transform: translate(-3px,-3px);
      -o-transform: translate(-3px,-3px);
      transform: translate(-3px,-3px);
      box-shadow: 3px 3px 30px rgba(0,0,0,0.5);
      background-color: $corporate_1;
    }
  }
}

/* === === === == banner_fuengirola == === === === */

.banner_contenido_wrapper {
  padding: 0 calc((100% - 900px) / 2) 100px;
  background: linear-gradient(white, white 50%, #EBECEE);
  position: relative;
  .imago_back {
    background-position: center 100px !important;
  }
  .banner_contenido {
    .banner {
      margin-bottom: 20px;
      display: table;
      width: 100%;
      &:nth-child(2n) .image {
        float: right;
      }
      h2 {
          font-size: 16px;
          font-weight: 700;
          color: #2D3D54;
          text-transform: uppercase;
          letter-spacing: 2px;
          padding: 30px 0;
        }
       .banner_content {
         background: $corporate_1;
         width: calc(100% - 320px);
         display: inline-block;
           h3 {
             font-weight: 700;
             color: #F3B620;
             font-size: 36px;
             padding: 20px 30px;
             font-family: "Playfair Display", serif;
             letter-spacing: 2px;
          }
         .banner_contenido_desc {
           display: inline-block;
           width: 100%;
           color: white;
           padding: 0 30px 20px;
           ul {
             display: inline-block;
             width: 60%;
             padding-top: 10px;
             &:last-child {
               width: 30%;
             }
           }
         }
       }
    .image {
           display: inline-block;
           position: relative;
           z-index: 2;
           vertical-align: top;
           overflow: hidden;
           box-shadow: 0 0 30px rgba(0, 0, 0, 0.5);
           width: 320px;
           height: 320px;
           float: left;
           img {
             @include center-image;
           }
         }
      }
    }
}
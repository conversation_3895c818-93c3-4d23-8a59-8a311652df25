footer {
    background-color: $corporate_1;
    position: relative;

    .footer_content  {
        position: relative;
        padding: 80px 0 0;

        .container12 {

            .footer_columns_wrapper {
                display: flex;
                flex-flow: row nowrap;
                justify-content: flex-start;
                position: relative;
                padding-bottom: 60px;

                .hotels_column {
                    text-align: left;
                    padding: 0 20px;
                    width: 33%;

                    .logo_wrapper {
                        padding: 0 80px 50px 0;

                        img {
                            width: 100%;
                        }
                    }
                    
                    .content_title {
                        .title {
                            color: white;
                        }
                    }

                    .desc {
                        margin: 0 0 20px;
                        font-size: $font_body_size;
                        color: white;

                        ul {

                            &.menu_links_column {
                                display: flex;
                                flex-flow: row wrap;

                                li {
                                    width: 50%;
                                    line-height: 20px;

                                    a {
                                        font-family: 'gotham medium';
                                        text-transform: uppercase;
                                        font-size: $font_sm;
                                        color: white;
                                        font-weight: 700;
                                        padding-left: 0;
                                        transition: all .4s;
                                        
                                        &:hover {
                                            padding-left: 5px;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    
                    .btn {
                        font-size: $font_sm;
                    }
                    
                    .footer_rrss {
                        position: relative;
                        margin-top: 30px;
                        padding-top: 30px;
                        
                        a {
                            
                            i {
                                color: white;
                                margin: 0 5px;
                                width: 50px;
                                height: 50px;
                                border: solid 2px white;
                                position: relative;
                                border-radius: 50%;
                                font-size: 25px;
                                transform: none;
                                transition: all .3s;
                                
                                &::before {
                                    @include center_xy;
                                }
                            }
                            
                            &:hover {
                                
                                i {
                                    transform: rotate(15deg) translate(-2px, 0px);
                                    background-color: white;
                                    color: $corporate_1;
                                }
                            }
                        }
                        
                        &::before {
                            position: absolute;
                            content: '';
                            height: 1px;
                            background-color: white;
                            top: 0;
                            left: -110px;
                            right: 0;
                        }
                    }
                    
                    &:last-of-type {
                        position: absolute;
                        padding: 0;
                        width: 40%;
                        right: 0;
                        
                        .content_title {
                            margin-bottom: 0;
                        
                            .title {
                                font-size: 21px;
                            }
                        }
                        
                        &::before {
                            position: absolute;
                            font-family: 'Font Awesome 5 Pro';
                            content: '\f0e0';
                            top: 25px;
                            left: 0;
                            transform: translateX(calc(100% - 170px));
                            font-weight: 700;
                            font-size: 60px;
                            color: white;
                        }
                    }
                }
            }
        }

        .footer_legal_text_wrapper {
            background-color: $black;
            
            * {
                color: white;
                font-size: $font_sm;
            }

            .container12 {
                padding: 40px 0 40px;

                .footer_links_wrapper {
                    text-align: center;
                }

                .legal_text {
                    display: none;
                }
            }
        }
    }
}
* {
  margin: 0;
  padding: 0;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/

body {
  background: white;
  font-family: 'Lato', sans-serif;
  font-weight: normal;
  font-size: 13px;
  line-height: 24px;

  a {
    text-decoration: none;
  }
}

body .datepicker_wrapper_element .datepicker_ext_inf_sd .ui-state-active, body .datepicker_wrapper_element .datepicker_ext_inf_ed .ui-state-active{
  background: #002d72 !important;
  color: white!important;
}

.datepicker_wrapper_element{
  border: 1px solid #eaeaea;
}

.datepicker_wrapper_element {
  .header_datepicker {
    background: #ac8e55 !important;
  }

  .months_selector_container .cheapest_month_selector{
    background: #ac8e55 !important;
  }
}

/*============= Header ==============*/

header {
  z-index: 999;
  display: block;
  background: white;
  width: 100%;
  min-width: 1140px;
  top: 0px;
  height: 102px;
  box-shadow: 1px 1px 6px;

  #logoDiv {
    width: 245px;
    height: 115px;
    margin-top: -6px;
    margin-left: 0px;
    position: relative;

    float: left;

    a {
      width: 100%;
      height: 100%;
      img {
        width: 100%;
        height: 100%;
        display: block;
        box-shadow: 0px 0px 9px black;
        position: absolute;
        z-index: 999;
      }
    }
  }

  #main_menu {
    width: 845px;
    float: left;
  }

  #main-sections {
    margin-top: 6px;

    .main-section-div-wrapper {
      float: left;
      padding: 10px 8px;
      position: relative;
    }

    #main-sections-inner {
      display: flex;
      justify-content: space-between;
      list-style: none;
      margin: 0;
      width: auto;
      padding-left: 35px;
    }

    .main-section-div-wrapper a {
      color: #5E5E5E;
      padding: 5px 0px 7px;
      text-decoration: none;
      text-transform: uppercase;
      display: inline-block;
      border-top: 2px solid white !important;
      font-size: 12px;
      &:hover {
        border-top: 2px solid black !important;
        color: black;
      }
    }

    #section-active a {
      color: black;
      border-top: 2px solid black !important;
    }
  }

  #lang {
    margin-right: 20px;
    height: 35px;

    .arrow {
      display: inline-block;
      background: #5E5E5E url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
      float: right;
      width: 35px;
      height: 35px;
      margin-top: 0px;
    }

    #selected-language {
      background: #5E5E5E;
    }

    ul li {
      background: #5E5E5E;
      text-align: center;
      width: 100px;
      font-size: 16px;
      padding: 5px;
      cursor: pointer;
      display: block;
      border-bottom: 1px solid #EEE;
      color: white;
      border-top: 1px solid #FFF;

      &:hover {
        border-bottom: 1px solid rgba(128, 128, 128, 0.33);
        background: $gray-2;
        width: 100px;
      }

      a {
        color: white !important;
        text-decoration: none !important;
      }
    }
  }

  #social {
    float: right;
    display: inline-block;
    margin-top: 3px;
    margin-right: 20px;

    a {
      text-decoration: none;
      width: 30px;
      height: 30px;
      display: inline-block;
      background: #002d72;
      position: relative;
      border-radius: 50%;
      vertical-align: middle;

      .fa {
        font-size: 16px;
        color: white;
        @include center_xy;
      }
    }
  }

  .booking_top_button {
    cursor: pointer;
    background: $corporate_1 !important;
    border-radius: 0px !important;
    -moz-border-radius: 5px;
    -webkit-border-radius: 5px;
    color: white;
    float: right;
    font-size: 13px;
    text-transform: uppercase;
    height: 35px;
    width: 110px;
    margin-top: 12px;
    margin-left: 20px;
    text-align: center;
    text-decoration: none;
    box-sizing: border-box;
    padding-top: 9px;

    &:hover {
      background: $corporate_2 !important;
    }
  }

}

.top-header {
  margin-top: 10px;
  font-size: 14px;
}

.top-header .phone,
.top-header #top-sections {
  float: right;
  margin-top: 7px;
  color: $gray-1;

  a {
    text-decoration: none;
    display: inline-block;
    margin: 0px 4px;
    color: $gray-1;
  }
  a:hover {
    text-decoration: underline;
  }
}

.top-header #top-sections {
  margin-right: 20px;
}

#main-sections-inner ul {
  display: none;
}

#main-sections-inner div:hover > ul {
  display: block;
}



header #main-sections #main-sections-inner div ul {
  position: absolute;
  background: white;
  text-align: center;
  left: -18px;
  width: 150px;
  top: 50px;
  z-index: 9999;

  a {
    border-top: none !important;
    padding: 10px;
    display: block;
  }
  a:hover {
    background: $gray-4;
  }

}

#main-sections-inner li ul {
  position: absolute;
}

#main-sections-inner div li {
  float: none;
  display: block;
}

/*========== LANGUAGE SELECT =========*/
#language-selector-options {
  display: block;
  padding-top: 34px !important;
}

#lang {
  float: right;
  cursor: pointer;
  width: 110px;
  position: relative;

  #selected-language {
    background-color: rgba(255, 255, 255, 0.2);
    padding: 7px 9px 3px 7px;
    box-sizing: border-box;
    width: 75px;
    height: 35px;
    color: white;
    font-size: 12px;
    float: left;
    letter-spacing: 1px;
    display: table;
  }

  .arrow {
    display: inline-block;
    background: url(/img/prece/flecha_dorada_lang.png) no-repeat center center !important;
    float: right;
    width: 45px;
    height: 45px;
    margin-top: 0px;

  }

  #language-selector-options {
    position: absolute;
    z-index: 9999;
  }

  ul li {
    background: #ffffff;
    text-align: center;
    width: 100px;
    font-size: 16px;
    padding: 5px;
    cursor: pointer;
    display: block;
    border-bottom: 1px solid #EEE;
    color: #666;
    border-top: 1px solid #FFF;

    &:hover {
      border-bottom: 1px solid rgba(128, 128, 128, 0.33);
      background: #f0f0f0;
      width: 80px;
    }

    a {
      color: #666 !important;
      text-decoration: none !important;
    }
  }
}

#language-selector-options {
  display: none;

  a {
    color: $gray-2;

    &:hover {
      color: $corporate-1;
    }
  }
}

/*=============== Slider ====================*/
#slider_container {
  position: relative;

  .slide_inner {
    position: relative;
    height: 380px;
    overflow: hidden;
    width: 100%;
    display: inline-block;
    margin-top: -7px;

    img {
      @include center_xy;
      min-width: 100%;
      min-height: 100%;
      max-width: none;
    }
  }
}

.tp-bullets {
  bottom: 0px !important;
  opacity: 1 !important;
  z-index: 23 !important;
  width: 550px;
  padding: 10px;
  text-align: center;
}

.tp-bullets .bullet {
  background: url("/img/urbam/bullet_flexslider.png") no-repeat !important;
  background-position: center !important;
  display: inline-block !important;
  float: none !important;
}

.tp-bullets .bullet.selected {
  background: url("/img/urbam/bullet_flexslider_active.png") no-repeat center !important;
  background-size: cover;
}

.tp-leftarrow,
.tp-rightarrow {
  opacity: 1 !important;
}

.tp-leftarrow.default {
  background: url("/img/urbam/arrow_left.png") no-Repeat 0 0;
}

.tp-rightarrow.default {
  background: url("/img/urbam/arrow_right.png") no-Repeat 0 0;
}

#button-google {
  position: absolute;
  z-index: 100;
  bottom: -15px;
  width: 235px;
  left: 7px;
  height: 50px;
  background: url("/img/urbam/img_maps.png") no-repeat 0 center;
  text-align: center;

  a {
    color: white;
    text-transform: uppercase;
    font-size: 18px;
    display: block;
    padding: 17px 0 0px 30px;

    &:hover {
      color: $corporate-1;
    }
  }
}

#map-layer {
  width: 100%;
  height: 620px;
  overflow: hidden;
  position: absolute;
  top: 0px;
  z-index: 20;
  display: none;
  &.active {
    display: block;
  }
}

.slider_inner_container {
  height: 620px;

  .slider_image {
    width: 100%;
    height: auto;
    position: fixed;
    top: 60px;
    z-index: -2;
    min-width: 1920px;
    left: 0px;
  }

  .slider_text {
    position: absolute;
    left: 401px;
    right: 0px;
    top: 32px;
    bottom: 0px;
    width: 900px;
    height: 100px;
    z-index: 2;
    margin: auto;
    color: white;
    text-transform: uppercase;
    font-size: 77px;
    font-weight: 200;
    text-align: center;
    line-height: 65px;
  }
}

/*================ Footer ================*/
footer {
  color: white;
  background: $corporate_3;

  span.siguenos {
    float: left;
    margin-top: 12px;
    padding-right: 9px;
    text-transform: uppercase;
    font-weight: lighter;
    font-size: 12px;
  }

  #social a img {
    width: 40px;
    height: 40px;
  }

  .top_foot_wrapper {
    padding-top: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid;
  }

  label#suscEmailLabel {
    display: none !important;
  }

  #newsletter_wrapper {
    display: inline-block;
    width: 340px;

    h2#title_newsletter {
      float: left;
      padding-top: 10px;
      padding-right: 17px;
      text-transform: uppercase;
      font-weight: lighter;
      font-size: 12px;
    }
    input#suscEmail {
      margin-top: 8px;
      float: left;
      height: 27px;
      width: 197px;
      border: 0px;
      background: rgba(255, 255, 255, 0.13) url("/img/urbam/letter.png") no-repeat;
      -ms-background-position-x: 6px;
      background-position-x: 6px;
      -ms-background-position-y: 1px;
      background-position-y: 1px;
      color: white;
      padding-left: 40px;
      box-sizing: border-box;
    }

    button#newsletter-button {
      width: 27px;
      height: 27px;
      margin-top: 8px;
      margin-left: 4px;
      border: 0px;
      background: white;
      cursor: pointer;

      &:before {
        content: '\f105';
        font-family: 'fontawesome', sans-serif;
        color: $corporate_3;
        padding-left: 3px;
        line-height: 20px;
        font-size: 30px;
      }
      &:hover {
        opacity: 0.8;
      }
    }

    ::-webkit-input-placeholder {
      /* WebKit browsers */
      color: white;
    }
    :-moz-placeholder {
      /* Mozilla Firefox 4 to 18 */
      color: white;
      opacity: 1;
    }
    ::-moz-placeholder {
      /* Mozilla Firefox 19+ */
      color: white;
      opacity: 1;
    }
    :-ms-input-placeholder {
      /* Internet Explorer 10+ */
      color: white;
    }

    .newsletter_checkbox {
      display: block;
      clear: both;

      label {
        //white-space: nowrap;
        //width: 300px;
        //display: inline-block;
      }

      input {
         display: inline-block;
        margin-right: 5px;
      }

      a{
        color: white;
        text-decoration: underline;
      }
    }
  }

  div#social {
    float: left;
    margin-right: 28px;
    margin-top: 3px;

    a {
      display: inline-block;
      width: 40px;
      height: 40px;
      background: white;
      border-radius: 50%;
      position: relative;

      .fa {
        @include center_xy;
        color: $corporate_3;
        font-size: 18px;
      }
    }
  }
}

div#newsletterButtonExternalDiv {
  display: block;
  float: left;
}

.tick_center {
  float: right;
  padding-top: 9px;
  font-weight: lighter;
  width: 510px;

  .tick_wrapper {
    float: left;
    font-size: 15px;
    margin-left: 14px;

    span {
      font-family: 'Open Sans', sans-serif;
      font-size: 10px;
    }

    img {
      vertical-align: middle;
      padding-bottom: 4px;
      height: 21px;
      margin-top: 2px;
      margin-right: 3px;
      width: auto;
      margin-left: 10px;
    }

    span.tick_pago {
      padding-top: 2px;
    }
  }
}

.full-copyright {
  background: $corporate_3;
}

.footer-copyright {
  text-align: center;
  padding: 13px 0px;

  p {
    color: white;
  }
  a, span {
    text-decoration: none;
    color: white;
    font-weight: lighter;
    padding: 0px 7px;
    &:hover {
      opacity: 0.8;
    }
  }
}

/*==== Footer ==*/
div#facebook_like {
  width: 49%;
  float: left;
  text-align: right;
}

div#google_plus_one {
  width: 49%;
  margin-top: 2px;
  float: right;
}

.social_likes_wrapper {
  margin-top: 10px;
}

#wrapper_booking.inline {
  background: #002d72;
}

header #logoDiv {
  height: 108px;

  a img {
    box-shadow: none;
  }
}

/* ===== ==== === == = Banner Ticks = == === ==== ===== */
.banner_ticks {
  background-color: $corporate_1;
  .banner_ticks_content {
    h3 {
      margin-top: 30px;
      font-size: 30px;
      margin-bottom: 25px;
      font-weight: 300;
      font-family: 'Oswald', sans-serif;
    }
    .banner_ticks_content_text {
      width: 600px;
      margin: auto;
      padding: 0px 100px 30px;
      border-bottom: 1px solid rgba(white, .8);
      margin-bottom: 30px;
    }
  }
  .banner_ticks_wrapper {
    text-align: center;
    font-size: 12px;
    color: white;
    .ticks {
      display: inline-block;
      text-align: center;
      text-transform: uppercase;
      font-size: 10px;
      line-height: 15px;
      margin: 0px 40px;
      span {
        font-weight: 200;
      }
      img {
        display: block;
        margin: 10px auto;
      }
    }
    a {
      display: block;
      width: 120px;
      margin: 30px auto;
      text-transform: uppercase;
      background-color: $corporate_2;
      padding: 10px 45px;
      line-height: 15px;
      color: white;
      text-decoration: none;
    }
  }
}

.fancybox-ventajas {
  .fancybox-outer {
    background-color: #2A2A2A !important;
    padding: 0px !important;
    overflow: auto;
    overflow-x: hidden;
    color: white;
    .popup_ventajas {
      width: 100%;
      font-family: "Source Sans Pro", sans-serif;
    }
    h3 {
      border-bottom: 1px solid lightgrey;
      font-size: 18px;
      width: 100%;
      display: block;
      padding: 15px;
      box-sizing: border-box;
    }
    img {
      float: left;
      margin-right: 10px;
      margin-bottom: 10px;
    }
    .banner_title {
      font-size: 18px;
    }
    .banner_desc {
      font-weight: 200;
      display: block;
      padding-left: 30px;
    }
    .banner_list, p {
      padding: 15px;
      font-size: 12px;
      font-weight: 200;
    }
    p {
      padding-left: 45px;
    }
  }
}

/*=== Hotel List Home ===*/
.hotel_list_wrapper {
  display: inline-block;
  width: 100%;
  background: $corporate_4;
  padding: 50px 0;

  .hotel_list_title {
    text-transform: uppercase;
    font-size: 18px;
    text-align: center;
    font-family: "Oswald";

    &:after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: $corporate_1;
      margin: 15px auto 40px;
    }
  }

  .hotel_list_content {
    position: relative;
    overflow: hidden;

    .hotel_element {
      display: inline-block;
      width: calc(100%/2);
      height: 380px;
      position: relative;
      float: left;
      overflow: hidden;

      &:nth-child(even) {
        .hotel_image {
          &:before {
            background: rgba($corporate_3, .4);
          }
        }
      }

      &:hover {
        .hotel_image {
          &:before {
            opacity: 0;
          }
        }
      }

      .hotel_image {
        display: inline-block;
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;

        &:before {
          content: "";
          display: block;
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: rgba($corporate_3, .3);
          z-index: 1;
          @include transition(opacity, .6s);
        }

        img {
          @include center_xy;
          min-width: 100%;
          min-height: 100%;
          max-width: none;
        }
      }

      .hotel_links {
        position: absolute;
        bottom: 20px;
        right: 20px;
        z-index: 2;

        .see_more_information {
          display: inline-block;

          .fa {
            border: 1px solid white;
            color: white;
            width: 25px;
            height: 25px;
            position: relative;
            cursor: pointer;
            @include transition(all, .4s);

            &:hover {
              color: $corporate_1;
              background: white;
            }

            &:before {
              @include center_xy;
            }
          }
        }

        .see_hotel {
          width: 27px;
          height: 27px;
          display: inline-block;
          background: $corporate_1;
          position: relative;
          cursor: pointer;
          @include transition(background, .4s);

          &:hover {
            background: white;

            .fa {
              color: $corporate_1;
            }
          }

          .fa {
            @include center_xy;
            color: white;
            @include transition(color, .4s);
          }
        }
      }

      .hotel_content {
        .center_block {
          @include center_y;
          width: 100%;
          text-align: center;
          color: white;
          z-index: 2;

          .hotel_title {
            text-transform: uppercase;
            font-family: "Oswald";
            font-size: 22px;

            &:after {
              content: "";
              display: block;
              margin: 20px auto 90px;
              width: 30px;
              height: 2px;
              background: white;
            }
          }

          .price {
            border: 1px solid white;
            padding: 10px;
            display: inline-block;
            text-transform: uppercase;
            font-size: 16px;
          }

          .button-promotion {
            color: $corporate_1;
            background: white;
            font-size: 16px;
            width: 164px;
            text-align: center;
            display: inline-block;
            margin-top: 5px;
            padding: 10px;
            box-sizing: border-box;
            @include transition(all, .4s);

            &:hover {
              color: white;
              background: $corporate_1;
            }
          }
        }
      }
    }

    .hide_hotel_information {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 100%;
      width: 100%;
      background: white;
      z-index: 10;
      padding: 50px;
      box-sizing: border-box;
      text-align: center;
      @include transition(left, .8s);

      &.show {
        left: 0;
        @include transition(left, .8s);
      }

      .back_button {
        position: absolute;
        top: 40px;
        left: 50px;
        color: $corporate_1;
        cursor: pointer;

        &:hover {
          opacity: .8;
        }

        .fa {
          font-size: 46px;
          vertical-align: middle;

          &:before {
            vertical-align: bottom;
          }
        }

        span {
          text-transform: uppercase;
          font-family: "Oswald";
          display: inline-block;
          vertical-align: middle;
          margin-left: 10px;
        }
      }

      .hotel_title {
        text-transform: uppercase;
        font-size: 24px;
        font-family: "Oswald";

        &:after {
          content: "";
          display: block;
          margin: 15px auto;
          background: $corporate_1;
          height: 2px;
          width: 30px;
        }
      }

      .hotel_description {
        width: 80%;
        margin: auto;
      }

      .minigallery_hotel {
        display: inline-block;
        width: 425px;
        height: 75px;
        margin: 10px auto;

        .minigallery_element {
          width: 25%;
          height: 100%;
          position: relative;
          overflow: hidden;
          display: inline-block;
          float: left;

          img {
            @include center_xy;
            min-width: 100;
            min-height: 100%;
          }
        }
      }

      .hotel_link {
        display: inline-block;
        width: 100%;

        .see_web {
          text-transform: uppercase;
          border: 1px solid $corporate_1;
          color: $corporate_1;
          font-size: 18px;
          font-family: "Oswald";
          padding: 10px 40px;
          display: inline-block;
          vertical-align: middle;
          font-weight: lighter;
          cursor: pointer;
          @include transition(all, .4s);

          &:hover {
            background: $corporate_1;
            color: white;
          }
        }

        .button-promotion {
          background: $corporate_1;
          color: white;
          font-size: 18px;
          padding: 11px 30px;
          display: inline-block;
          vertical-align: middle;
          cursor: pointer;

          &:hover {
            opacity: .8;
          }
        }
      }
    }
  }
}

/*=== Banners x2 ===*/
.bannersx2_wrapper {
  display: inline-block;
  width: 100%;
  padding: 40px 0;

  .bannersx2_title {
    text-transform: uppercase;
    font-size: 18px;
    text-align: center;
    font-family: "Oswald";

    &:after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: $corporate_1;
      margin: 15px auto 40px;
    }
  }

  .bannersx2_content {
    .banner_element {
      display: inline-block;
      width: 49.5%;
      float: left;
      position: relative;

      &:nth-child(even) {
        float: right;
      }

      .banner_image {
        position: relative;
        display: inline-block;
        width: 100%;
        height: 270px;
        overflow: hidden;

        img {
          @include center_xy;
          min-width: 100%;
          min-height: 100%;
          max-width: none;
        }
      }

      .banner_link {
        @include center_x;
        top: 0;
        background: white;
        font-size: 16px;
        display: inline-block;
        color: $corporate_1;
        padding: 15px 30px;
        @include transition(all, .4s);

        &:hover {
          color: white;
          background: $corporate_1;
        }
      }

      .banner_content {
        background: $corporate_4;
        padding: 30px 50px;

        .banner_title {
          color: $corporate_5;
          font-size: 26px;
          text-align: center;
          margin-bottom: 15px;
        }

        .banner_description {
          width: 70%;
          margin: 0 auto;
          color: $corporate_2;
        }
      }
    }
  }
}

/*=== Slider Offers ===*/
.offers_home_wrapper {
  display: inline-block;
  width: 100%;
  border-bottom: 1px solid $corporate_5;
  position: relative;
  padding: 40px 0;

  .offers_home_title {
    text-transform: uppercase;
    font-size: 18px;
    text-align: center;
    font-family: "Oswald";

    &:after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: $corporate_1;
      margin: 15px auto 40px;
    }
  }

  .offers_home_content {
    width: 960px;
    margin: auto;

    .offer_element {
      display: none;
      position: relative;
      height: 340px;
      overflow: hidden;
      text-align: center;

      &:hover {
        .offer_image {
          &:after {
            opacity: 0;
          }
        }
      }

      &:nth-child(even) {
        .offer_image {
          &:after {
            background: rgba($corporate_3, .4);
          }
        }
      }

      .offer_image {
        display: inline-block;
        width: 100%;
        height: 100%;
        overflow: hidden;
        position: relative;

        &:after {
          content: "";
          display: inline-block;
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          background: rgba($corporate_3, .3);
          @include transition(opacity, .6s);
        }

        img {
          @include center_xy;
          min-width: 100%;
          min-height: 100%;
          max-width: none;
        }
      }

      .see_more {
        background: white;
        color: $corporate_1;
        position: absolute;
        top: 5px;
        right: 5px;
        z-index: 2;
        padding: 5px 15px;
        cursor: pointer;
        @include transition(all, .4s);

        &:hover {
          color: white;
          background: $corporate_1;
        }
      }

      .offer_content {
        position: absolute;
        top: 70px;
        left: 0;
        width: 100%;
        z-index: 2;
        text-align: center;

        .offer_title {
          font-family: "Oswald";
          color: white;
          font-size: 18px;

          &:after {
            content: "";
            display: block;
            margin: 20px auto 40px;
            height: 3px;
            width: 30px;
            background: white;
          }
        }

        .offer_description {
          font-size: 16px;
          font-weight: lighter;
          color: white;
          text-transform: uppercase;

          strong {
            display: block;
            font-size: 26px;
          }
        }
      }

      .button-promotion {
        @include center_x;
        bottom: 30px;
        background: $corporate_1;
        color: white;
        padding: 10px 30px;
        font-size: 18px;
        @include transition(all, .4s);

        &:hover {
          color: $corporate_1;
          background: white;
        }
      }
    }

    .flex-direction-nav {
      li {
        @include center_y;

        &.flex-nav-prev {
          left: 0;
        }

        &.flex-nav-next {
          right: 0;
        }

        .fa {
          color: $corporate_1;
          font-size: 36px;
        }
      }
    }
  }
}

/*=== Offer Section ===*/
.offers_list {
  width: 1140px;
  overflow: hidden;
}
.offers_wrapper {
  display: inline-block;
  width: 1145px;
  margin-bottom: 40px;

  .offer_element {
    display: inline-block;
    position: relative;
    height: 340px;
    width: 281px;
    float: left;
    overflow: hidden;
    text-align: center;
    margin-right: 5px;
    margin-top: 5px;

    &:hover {
      .offer_image {
        &:after {
          opacity: 0;
        }
      }
    }

    &:nth-child(even) {
      .offer_image {
        &:after {
          background: rgba($corporate_3, .4);
        }
      }
    }

    .offer_block {
      -webkit-transform: translateY(0%);
      -moz-transform: translateY(0%);
      -ms-transform: translateY(0%);
      -o-transform: translateY(0%);
      transform: translateY(0%);
      display: inline-block;
      width: 100%;
      height: 100%;
      @include transition(all, .8s);
    }

    .offer_block.hide {
      -webkit-transform: translateY(-100%);
      -moz-transform: translateY(-100%);
      -ms-transform: translateY(-100%);
      -o-transform: translateY(-100%);
      transform: translateY(-100%);
      @include transition(all, .8s);
    }

    .offer_image {
      display: inline-block;
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: relative;

      &:after {
        content: "";
        display: inline-block;
        position: absolute;
        top: 0;
        bottom: 0;
        left: 0;
        right: 0;
        background: rgba($corporate_3, .3);
        @include transition(opacity, .6s);
      }

      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }

    .offer_content {
      position: absolute;
      top: 70px;
      left: 0;
      width: 100%;
      z-index: 2;
      text-align: center;

      .offer_title {
        font-family: "Oswald";
        color: white;
        font-size: 18px;

        &:after {
          content: "";
          display: block;
          margin: 20px auto 40px;
          height: 3px;
          width: 30px;
          background: white;
        }
      }

      .offer_description {
        font-size: 16px;
        font-weight: lighter;
        color: white;
        text-transform: uppercase;

        strong {
          display: block;
          font-size: 26px;
        }
      }
    }

    .offer_link {
      @include center_x;
      bottom: 30px;
      display: table;
      width: 100%;

      .see_more {
        margin-left: 5px;
        display: inline-block;
        vertical-align: middle;
        background: transparent;
        color: white;
        cursor: pointer;
        height: 42px;
        width: 42px;
        @include transition(all, .4s);
        position: relative;
        border: 1px solid white;

        .fa {
          @include center_xy;
        }

        &:hover {
          background: white;
          color: $corporate_1;
        }
      }

      .button-promotion {
        display: inline-block;
        vertical-align: middle;
        background: $corporate_1;
        color: white;
        padding: 10px 30px;
        font-size: 18px;
        @include transition(all, .4s);

        &:hover {
          color: $corporate_1;
          background: white;
        }
      }
    }
  }
}

/*=== Offer Filter ===*/
.offers_filter_wrapper {
  display: inline-block;
  width: 100%;
  margin-bottom: 20px;

  .filter_element {
    display: inline-block;
    width: calc(100%/4 - 6px);
    margin-right: 5px;
    cursor: pointer;

    &:last-child, &:nth-child(4) {
      margin-right: 0;
    }

    &:hover {
      .filter_title {
        opacity: .8;
      }
    }

    .filter_image {
      display: inline-block;
      vertical-align: middle;
      height: 68px;
      width: 68px;
      position: relative;
      overflow: hidden;
      float: left;

      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }

    .filter_title {
      width: 211px;
      display: inline-block;
      vertical-align: middle;
      background: $corporate_1;
      color: white;
      float: left;
      padding: 22px 0;
      box-sizing: border-box;
      text-align: center;
      font: {
        family: "Oswald";
        size: 16px;
        weight: lighter;
      }
    }
  }
}

/*=== Destiny Banners ===*/
.destiny_banners_wrapper {
  display: inline-block;
  width: 100%;
  text-align: center;
  padding-bottom: 40px;

  .destiny_element {
    display: inline-block;
    width: calc(100% / 3 - 10px);
    height: 280px;
    border-right: 3px solid white;
    box-sizing: border-box;
    position: relative;
    overflow: hidden;

    &:last-child {
      margin-right: 0;
    }

    &:before {
      content: "";
      display: block;
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      background: rgba(black, .4);
      z-index: 1;
      @include transition(opacity, .6s);
    }

    &:hover {
      &:before {
        opacity: 0;
      }
    }

    .destiny_image {
      display: inline-block;
      width: 100%;
      height: 100%;
      overflow: hidden;
      position: relative;

      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }

    .center_block {
      @include center_xy;
      width: 100%;
      text-align: center;
      color: white;
      z-index: 2;

      .destiny_title {
        text-transform: uppercase;
        font-family: "Oswald";
        font-size: 22px;

        &:after {
          content: "";
          display: block;
          margin: 20px auto 30px;
          width: 30px;
          height: 2px;
          background: white;
        }
      }

      .destiny_description {
        width: 70%;
        margin: auto;

        .description_hide {
          display: none;
        }
      }

      .see_destiny_information {
        &:hover {
          .fa {
            background: white;
            color: $corporate_1;
          }
        }

        .fa {
          width: 160px;
          height: 44px;
          border: 1px solid white;
          color: white;
          position: relative;
          font-size: 24px;
          @include transition(all, .6s);

          &:before {
            @include center_xy;
          }
        }
      }
    }
  }
}

/*=== Destiny PopUp ===*/
.fancybox-wrap.destiny-popup {
  text-align: center;

  .fancybox-outer {
    border-radius: 0;

    .fancybox-inner {
      .popup_destiny_title {
        color: $corporate_1;
        text-transform: uppercase;
        font-family: "Oswald", sans-serif;
        font-size: 18px;
        font-weight: 300;
        margin-bottom: 20px;
        margin-top: 16px;
      }

      .popup_destiny_description {
        .description_show {
          display: none;
        }
      }

      .popup_minigallery {
        margin-top: 20px;
        display: inline-block;
        position: relative;
        width: 100%;

        .flex-direction-nav {
          li {
            @include center_y;

            &.flex-nav-prev {
              left: 20px;
            }

            &.flex-nav-next {
              right: 20px;
            }

            .fa {
              color: white;
              font-size: 22px;
            }
          }
        }
      }
    }
  }

  .fancybox-close {
    top: 0;
    right: 0;
    background: $corporate_1;

    &:before {
      content: "\f00d";
      @include center_xy;
      font-family: FontAwesome;
      color: white;
    }
  }
}

/*==== Content Subtitle ===*/
.content_subtitle_wrapper {
  display: inline-block;
  width: 100%;
  padding: 40px 0;
  text-align: center;

  .content_subtitle_title {
    text-transform: uppercase;
    font-size: 18px;
    text-align: center;
    font-family: "Oswald";

    &:after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: $corporate_1;
      margin: 15px auto 40px;
    }
  }

  .content_subtitle_content {
    width: 70%;
    margin: auto;
  }
}

.main-content-access {
  display: inline-block;
  width: 100%;
  padding: 40px 0;
  text-align: center;

  h3 {
    text-transform: uppercase;
    font-size: 18px;
    text-align: center;
    font-family: "Oswald";

    &:after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: $corporate_1;
      margin: 15px auto 40px;
    }
  }

  & > div {
    width: 70%;
    margin: auto;
    color: $corporate_2;
  }

  #my-bookings-form {
    margin-top: 20px;
    text-align: center;

    #my-bookings-form-fields {
      label, input {
        display: block;
        margin: auto;
      }

      label {
        width: 150px;
        padding: 12px 10px;
        padding-bottom: 11px;
        border-right-width: 0px;
        color: $corporate_1;
        vertical-align: top;
      }
      input[type=text] {
        border: 2px solid $corporate_1;
        font-size: 0.9em;
        padding: 0px 10px;
        width: 200px;
        height: 42px;
      }

      select {
        margin: auto;
        border: 2px solid $corporate_1;
        border-radius: 0;
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: white;
        padding: 15px 0 15px 5px;
        width: 220px;
      }

      button {
        border: 2px solid $corporate_1;
        background-color: $corporate_1;
        font-size: 0.9em;
        padding: 15px 10px;
        color: $white;
        width: 224px;
        margin: 30px 10px 10px;
      }
    }

    #cancel-button-container button {
      border: 2px solid $corporate_1;
      background-color: $corporate_1;
      font-size: 0.9em;
      padding: 15px 10px;
      color: $white;
      width: 224px;
      margin: 30px auto 10px;
    }

    #modify-button-container {
      display: none;
    }

    li {
      display: inline-block;
    }

    #cancelButton {
      display: none;
      margin: auto;
    }
    .my-bookings-booking-info {
      margin: auto;
      margin-bottom: 20px;
    }
    .fResumenReserva {
      background: #efefef;
      border-width: 0px;
      color: $gray-1;
      h2, h3 {
        color: $corporate_1;
      }
      .cajaCosteTotal, .txtCosteTotal {
        color: $corporate_1;
      }
    }
  }
}

/*=== Hotels Section ===*/
.hotels_wrapper {
  display: inline-block;
  width: 100%;
  padding-bottom: 40px;

  .hotel_element {
    display: inline-block;
    width: 100%;
    margin-bottom: 20px;

    &:nth-child(even) {
      .hotel_image {
        float: right;
      }

      .hotel_content {
        float: left;
      }
    }

    .hotel_image {
      display: inline-block;
      height: 420px;
      width: 30%;
      position: relative;
      overflow: hidden;
      float: left;

      & > img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }

      .hotel_minigallery {
        display: inline-block;
        width: 100%;
        height: 100%;
        position: relative;
        overflow: hidden;

        .minigallery_element {
          display: none;
          height: 380px;
          position: relative;
          overflow: hidden;

          &:first-child {
            display: block;
          }

          img {
            @include center_xy;
            min-width: 100%;
            min-height: 100%;
            max-width: none;
          }
        }

        .flex-direction-nav {
          li {
            @include center_y;

            &.flex-nav-prev {
              left: 20px;
            }

            &.flex-nav-next {
              right: 20px;
            }

            .fa {
              color: white;
              font-size: 22px;
            }
          }
        }
      }
    }

    .hotel_content {
      background: $corporate_4;
      width: 70%;
      display: inline-block;
      float: right;
      box-sizing: border-box;
      padding: 30px;

      .hotel_title {
        text-transform: uppercase;
        font-size: 18px;
        font-family: "Oswald";
        margin-bottom: 30px;
      }

      .hotel_description {
        display: inline-block;
        width: 100%;
        overflow: hidden;
        @include transition(height, .6s);

        &.closed {
          height: 220px !important;
          @include transition(height, .6s);
        }
      }

      .hotel_links {
        width: 100%;
        display: inline-block;
        margin-top: 5px;

        .hotel_icons {
          display: inline-block;
          vertical-align: middle;
          -webkit-transform: translate(0%,-50%);
          -moz-transform: translate(0%,-50%);
          -ms-transform: translate(0%,-50%);
          -o-transform: translate(0%,-50%);
          transform: translate(0%,-50%);
          float: left;

          .icon_hotel {
            padding: 0 5px;
            text-align: center;
            display: inline-block;
            width: 100px;
            img {
              display: block;
              margin: 0 auto 5px;
            }
            p {
              font-weight: normal;
              line-height: 13px;
            }
          }
        }

        .button-promotion {
          background: $corporate_1;
          float: right;
          color: white;
          padding: 5px 30px;
          display: inline-block;
          text-transform: uppercase;
          vertical-align: middle;
        }

        .see_more_hotel {
          display: inline-block;
          display: none;
          margin-right: 5px;

          .fa {
            border: 1px solid $corporate_1;
            color: $corporate_1;
            background: transparent;
            width: 32px;
            height: 32px;
            position: relative;
            cursor: pointer;
            @include transition(all, .4s);
            vertical-align: middle;

            &:hover {
              color: white;
              border-color: $corporate_3;
              background: $corporate_3;
            }

            &:before {
              @include center_xy;
            }
          }
        }
      }
    }
  }
}

/*====== Location =====*/
.location_section_wrapper {
  display: table;
  width: 100%;
  margin-top: 40px;

  img.location_image {
    width: 100%;
  }

  .image_location_wrapper {
    height: 530px;
    overflow: hidden;
  }

  .location_wrapper_text {
    display: table;
    margin-left: 30px;
    background: white;
    margin-top: -120px;
    z-index: 2;
    padding: 40px;
    position: relative;
    box-sizing: border-box;

    h3.location_title {
      font-family: "Oswald";
      font-size: 39px;

      &:after {
        content: '';
        display: block;
        width: 60px;
        height: 3px;
        background: $corporate_1;
        margin: 21px 0 27px;
      }
    }
  }

  .location_content {
    strong {
      display: block;
      color: $corporate_1;
    }

    .cols_wrapper {
      display: inline-block;

      .col_element {
        width: calc(100%/3);
        float: left;
        display: inline-block;
        box-sizing: border-box;
        padding: 0 20px;
        text-align: left;
      }
    }
  }
}

#contactContent .info {
  padding-left: 0 !important;
}

.contact_iframe_background {
  background: $corporate_4;
  padding: 40px 0;

  h1#title {
    display: none;
  }

  div#google-plus, .fb_iframe_widget {
    display: none;
  }

  .contact_form {
    background: white;
    width: 50%;
    float: left;
    padding: 0 41px;
    box-sizing: border-box;

    label.title {
      font-family: 'Open Sans', sans-serif;
      display: block;
      clear: both;
      width: 100% !important;
      font-size: 11px;
      font-weight: bolder;
      margin-bottom: 15px;
      color: $corporate_1;
      text-transform: uppercase;
    }

    .bordeInput {
      margin-left: 0 !important;
      width: 100% !important;
      box-sizing: border-box;
      border: 0 !important;
      background: #eeeeee;
      height: 40px;
    }

    div#contact-button {
      width: 155px !important;
      height: 42px !important;
      background: $corporate_1 !important;
      font-family: 'Source Sans Pro', sans-serif;
      text-transform: uppercase;
      text-align: center;
      box-sizing: border-box;
      padding: 11px 0 !important;
      border-radius: 0 !important;
    }

    div#contact-button-wrapper {
      padding-right: 0 !important;
    }

    input#privacy, input#has_reservation, input#promotions {
      display: inline-block;
      float: left;
      width: auto !important;
      vertical-align: middle;
      height: auto;
      margin-right: 10px;
      margin-top: 4px;
    }

    input#privacy + span a {
      font-family: 'Open Sans', sans-serif;
      font-size: 11px;
      margin-bottom: 15px;
      color: #585858;
      text-decoration: none;
    }

    .has_reservation_wrapper {
      display: block;
      margin-top: 7px;

      span {
        font-family: 'Open Sans', sans-serif;
        font-size: 11px;
        margin-bottom: 15px;
        color: #585858;
        text-decoration: none;
      }
    }
  }
}

.iframe_wrapper {
  width: 50%;
  float: right;
  overflow: hidden;
}

/*=== Galleries Filters ===*/
.filter_hotel_wrapper {
  display: inline-block;
  width: 100%;
  margin-bottom: 20px;

  .hotel_filter_element {
    display: inline-block;
    width: calc(100%/3 - 6px);
    margin-right: 9px;
    cursor: pointer;
    float: left;

    &:last-child, &:nth-child(4) {
      margin-right: 0;
    }

    &:hover {
      .filter_title {
        opacity: .8;
      }
    }

    .filter_image {
      display: inline-block;
      vertical-align: middle;
      height: 68px;
      width: 68px;
      position: relative;
      overflow: hidden;
      float: left;

      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }

    .filter_title {
      width: 305px;
      display: inline-block;
      vertical-align: middle;
      background: $corporate_1;
      color: white;
      float: left;
      padding: 22px 0;
      box-sizing: border-box;
      text-align: center;
      font: {
        family: "Oswald";
        size: 16px;
        weight: lighter;
      }
    }
  }
}
/*======= Full screen menu =======*/
  .full_screen_menu {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(49, 122, 190, 0.9);
    top: 93px;
    z-index: 24;

    div#mainMenuDiv ul#main-sections-inner {
      display: block;
      justify-content: none;
    }

    .separator_element {
      display: none;
    }

    div#logoDiv {
      display: table;
      margin: auto;
      margin-bottom: 0;

      img {
        max-width: 480px;
      }
    }

    #mainMenuDiv {
      position: absolute;
      right: auto;
      left: auto;
      top: 0;
      bottom: 0;
      transform: none;
      display: table;
      width: 100%;
      min-width: 1140px;
      margin: auto;

      .main-section-div-wrapper {
        text-align: center;
        text-transform: uppercase;
        font-size: 36px;
        font-weight: lighter;
        margin-bottom: 20px;

        .menu_icon {
          display: none;
        }

        a {
          text-decoration: none;
          color: white;
          cursor: pointer;

          &:hover {
            color: $corporate_2;
          }
        }
      }

        #main-sections-inner div li {
          margin-top: 20px;
          font-size: 26px;
        }
    }
  }
/*===== Offer individual =====*/
.detailed_offer_wrapper {
  margin-top: 30px;
}

.offer_detail_image_wrapper {
  height: 675px;
  position: relative;
  overflow: hidden;

  .offer_detail_image {
    @include center_xy();
    min-width: 100%;
    min-height: 100%;
    max-width: none;
  }
}

.offer_details_text {
  display: block;
  width: 825px;
  margin-top: -125px;
  z-index: 2;
  position: relative;
  background: white;
  margin-left: 30px;
  padding: 40px;

  h1.offer_title {
    text-transform: uppercase;
    font-size: 24px;
    display: inline-block;
    padding: 25px 20px;
    font-family: "Oswald";

    span {
      display: block;
      font-weight: 300;
      color: gray;
    }
  }

  .button-promotion {
    background: $corporate_1;
    padding: 20px 80px;
    color: white;
    text-transform: uppercase;
    display: inline-block;
    float: right;
  }

  .offer_description {
    font-size: 14px;
    color: $corporate_2;
    width: 725px;
    margin: 20px 20px;
  }

  div#shareSocialArea {
    float: right;
    font-size: 14px;
    color: grey;
  }
}

.datepicker_wrapper_element .specific_month_selector, .datepicker_wrapper_element .go_back_button {
  background: $corporate_3!important;
  color: white!important;

  strong {
    color: white !important;
  }
}

/*============== Bottom Pop-up ============*/

.bottom_popup {
  position: fixed;
  width: 100%;
  height: 120px;
  background: $corporate_1;
  //background: rgba(209,183,130,0.95);
  left: 0;
  bottom: 0;
  z-index: 1000;
}

.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

.bottom_popup .bottom_popup_text {
  text-align: center;
  font-family: Source Sans Pro;
  font-weight: lighter;
  font-size: 16px;
  width: 100%;
  margin-top: 27px;
  color: white;
  padding: 10px;
}

.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

.close_button {
  float: right;
  cursor: pointer;
}

button.bottom_popup_button {
  width: 120px;
  background: $corporate-1;
  border: 0;
  height: 36px;

  color: white;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  bottom: 12px;
  font-size: 16px;

}

#wrapper2 {
  width: 1140px;
  margin: 0 auto;
  text-align: center;
}

.popup_inicial {
  width: 800px;
  height: 100%;
  background-size: cover !important;
  display: table;

  .email, .discount, .compra {
    text-align: center;
  }

  .compra {
    padding-top: 5px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }

  .discount {
    padding-top: 7px;
    color: white;
    font-size: 47px;
    text-shadow: 3px 3px black;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
  }

  .email {
    padding-top: 39px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }

  form.form_popup {
    text-align: center;
    padding-top: 50px;

    li {
      text-align: center;
    }

    input#id_email {
      height: 26px;
      text-align: center;
      width: 270px;
      font-size: 17px;
      box-shadow: 2px 2px black;
      border: 0px;
      color: $corporate-1;
    }

    button.popup_button {
      margin: 7px 0px 3px 20px;
      width: 277px;
      height: 40px;
      background: $corporate-1;
      font-size: 17px;
      border: 0px;
      text-transform: uppercase;
      color: white;
      cursor: pointer;
    }
  }

  .spinner_wrapper_faldon {
    padding-top: 20px;
  }

  .popup_message {
    color: white;
    padding-top: 25px;
    font-size: 20px;
    font-weight: lighter;
  }
}

.picture-bigskirt {
  float: left;
  position: relative;
  bottom: 100px;
}

/*============== Bottom Pop-up ============*/
.bottom_popup {
  position: fixed;
  width: 100%;
  height: 120 px;
  background: $corporate-1;
  left: 0;
  bottom: 0;
  z-index: 1000;
}

.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

.bottom_popup .bottom_popup_text {
  width: 850px;
  float: left;
  color: white;
  padding: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
  text-align: left;
}

.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

.close_button {
  float: right;
  cursor: pointer;
}

button.bottom_popup_button {
  width: 120px;
  background: $gray-3;
  border: 0;
  height: 36px;
  position: absolute;
  color: white;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  bottom: 40px;
  right: 20px;
  font-size: 16px;

}

#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

.popup_inicial {
  width: 100%;
  height: 100%;
  background-size: cover !important;
  .email, .discount, .compra {
    text-align: center;
  }
  .compra {
    padding-top: 5px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }
  .discount {
    padding-top: 7px;
    color: white;
    font-size: 47px;
    text-shadow: 3px 3px black;
    text-transform: uppercase;
    font-family: 'Oswald', sans-serif;
  }
  .email {
    padding-top: 39px;
    color: white;
    font-size: 22px;
    font-weight: lighter;
  }
  form.form_popup {
    text-align: center;
    padding-top: 50px;
    li {
      text-align: center;
    }
    input#id_email {
      height: 26px;
      text-align: center;
      width: 270px;
      font-size: 17px;
      box-shadow: 2px 2px black;
      border: 0px;
      color: $corporate-1;
    }
    button.popup_button {
      margin: 7px auto 0px;
      width: 277px;
      height: 40px;
      background: $corporate-1;
      font-size: 17px;
      border: 0px;
      text-transform: uppercase;
      color: white;
      cursor: pointer;
    }
  }
  .spinner_wrapper_faldon {
    padding-top: 20px;
  }
  .popup_message {
    color: white;
    padding-top: 25px;
    font-size: 20px;
    font-weight: lighter;
  }
}
<div class="hotels_wrapper">
    {% for hotel in hotels %}
        <div class="hotel_element">
            <div class="hotel_image">
                {% if hotel.minigallery %}
                    <div class="hotel_minigallery">
                        <ul class="slides">
                            {% for img in hotel.minigallery %}
                                <li class="minigallery_element">
                                    <img src="{{ img.servingUrl|safe }}"/>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                {% else %}
                    <img src="{{ hotel.servingUrl|safe }}"/>
                {% endif %}
            </div>
            <div class="hotel_content">
                <div class="hotel_title">{{ hotel.title }}</div>
                <div class="hotel_description">{{ hotel.description|safe }}</div>
                <div class="hotel_links">
                    {% if hotel.hotel_icons%}
                    <div class="hotel_icons">
                        {% for icon in hotel.hotel_icons %}
                            <div class="icon_hotel">
                                <img src="{{icon.servingUrl|safe}}">
                                <p>{{icon.title|safe}}</p>
                            </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    <div class="see_more_hotel"><i class="fa fa-plus" aria-hidden="true"></i></div>
                    <a class="button-promotion" href="#data" namespace="{{ hotel.namespace }}"
                       hotel_url="https://{{ hotel.namespace|safe }}-dot-urban-dream.appspot.com/booking1"
                       hotel_name="{{ hotel.name_hotel|safe }}">{{ T_reservar }}</a>
                </div>
            </div>
        </div>
    {% endfor %}
</div>

<script async>
    $(window).load(function () {
        $(".hotel_description").each(function(){
            if($(this).height() > 220) {
                $(this).parent().find(".see_more_hotel").css({display: "inline-block"});
            }

            $(this).height($(this).height()).addClass("closed");
        });

        $(".see_more_hotel").click(function(){
            $(this).closest(".hotel_content").find(".hotel_description").toggleClass("closed");
        });

        slider_params = {
            controlNav: false,
            prevText: '<i class="fa fa-chevron-left" aria-hidden="true"></i>',
            nextText: '<i class="fa fa-chevron-right" aria-hidden="true"></i>',
            directionNav: true,
            animation: "slide"

        };
        $(".hotel_minigallery").flexslider(slider_params);



        $(".hotel_content").each(function(){
            $(this).parent().find(".minigallery_element").css({
                'min-height': $(this).outerHeight()
            });
        });
    })
</script>

<div class="cols_wrapper">
    <div class="col_element"></div>
    <div class="col_element"></div>
    <div class="col_element"></div>
</div>
body {
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 14px;
  font-weight: 100;
  color: $gray-1;
  background: #eee;
  line-height: 18px;

  .common-section {
    .main-block {
      h3.main-title {
        margin-bottom: 10px!important;
      }

      .section-title {
        display: none;
      }
    }
  }
}

p {
  margin-bottom: 10px;
}

a {
  text-decoration: none;
}

header {
  background: white;
  .top_header {
    display: block;
    background: #f0f0f0;
    text-align: center;
    a {
      display: inline-block;
      padding: 10px 20px;
      color: $black;
      i, p {
        display: inline-block;
        vertical-align: middle;
        margin: 0;
      }
      p {
        padding: 0 10px;
        text-transform: uppercase;
        font-weight: bold;
      }
    }
  }
}

.top-row {

}

#lang {
  margin-top: 8px;
  margin-left: 83px;
}

#lang a {
  background: $corporate-2;
  color: white;
  padding: 3px 3px 4px;
  margin: 0px 1px;

  &.selected {
    background: $corporate-3;
  }
}

.phone {
  color: $corporate-2;
  margin-top: 8px;
  font-weight: normal;
  font-size: 20px;
}

.oficial {
  background: $corporate-1;
  color: white;
  padding: 5px;
  text-align: center;
  margin-right: 0 !important;

  span {
    font-weight: 400;
  }
}

.bottom-row {
  margin-top: 10px;
}

#top-sections {
  margin-top: 10px;
  margin-left: 45px;
  text-align: right;

  a {
    color: $corporate-1;

    &:hover {
      color: $corporate-2;
    }
  }
}

#social {
  text-align: right;
  margin-right: 10px !important;
  width: 110px;
  float: right;
}

#main_menu {
  background: $corporate-2;
}

#mainMenuDiv {
  font-size: 15px;
  z-index: 99;
  clear: both;
  height: 40px;
}

#mainMenuDiv a {
  padding: 5px 8px 5px;
  text-decoration: none;
  text-transform: uppercase;
  color: $white;
  display: inline-block;
}

#mainMenuDiv a:hover {
  background: $corporate-1;
  color: white;
}

#section-active a {
  padding: 5px 8px 5px;
  text-decoration: none;
  text-transform: uppercase;
  background: $corporate-1;
  color: white;
  display: inline-block;
}

#main-sections-inner ul {
  display: none;
}

#main-sections-inner div:hover > ul {
  display: block;
}

#main-sections-inner div ul {
  position: absolute;
}

#main-sections-inner li ul {
  position: absolute;
}

#main-sections-inner div li {
  float: none;
  display: block;
}

#main-sections-inner {
  text-align: justify;
  height: 40px;
}

#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

#main-sections-inner > div {
  display: inline-block
}

.main-section-div-wrapper a {
  line-height: 30px;
  text-transform: uppercase;
  font-size: 15px;
}

.main-section-div-wrapper a:hover {
  color: $corporate-2;
}

/**************** SLIDER AND BOOKING *****************/

.ui-widget-header {
  background: $corporate-1;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate-1;
  color: white;
}

.date_box .date_year {
  color: $corporate-1;
}

.selectric, .date_box, .wrapper_booking_button .promocode_input {
  background: #eee;
}

.selectric .button {
  background: $corporate-2 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
}

.wrapper_booking_button button {
  background-color: $corporate-2;
  cursor: pointer;
}

.ticks-wrapper {
  position: relative;
}

#ticks-container {
  width: 450px;
  height: 65px;
  background-color: rgba(32, 111, 178, .8);
  @include border-radius(5px);
  color: white;
  text-transform: uppercase;
  font-size: 14px;
  z-index: 1000;
  position: absolute;
  bottom: 0;
  left: 10px;
}

.ticks {
  width: 100px;
  height: 35px;
  float: left;
  padding-left: 40px;
  margin-top: 16px;
  margin-left: 7px;
  line-height: 16px;
}

#tick1 {
  background: url("/img/vistl/pago_icon.png") no-repeat;
  background-size: 35px;
}

#tick2 {
  background: url("/img/vistl/segura_icon.png") no-repeat;
  background-size: 37px;
}

#tick3 {
  background: url("/img/vistl/gastos_icon.png") no-repeat;
  background-size: 37px;
}

.tp-caption a {
  color: white;
  text-align: center;
  font-size: 18px;
  font-family: 'Source Sans Pro', sans-serif;
  margin-left: 140px;

  &:hover {
    color: white;
  }
}

.booking_widget {
  top: 50px;
}

.booking_form_title {
  font-size: 18px !important;
}

#slider_container {
  position: relative;

  .booking_engine_inside {
    position: absolute;
    bottom: 40px;
    width: 100%;
  }
}

#slider_container.interior {
  position: relative;
  height: 300px;

  #ticks-container {
    display: none;
  }
}

.slider_inner_description {
  position: absolute;
  left: 0;
  right: 0;
  z-index: 22;
  color: white;
  top: 70px;
  bottom: 86px;
  text-align: center;
  font-size: 35px;
  font-weight: 300;
  font-family: 'Lato', sans-serif;
  margin: auto;
  display: table;
  width: 100%;
}

.slider_inner_container .slider_image {
  width: 100%;
  height: auto;
  position: fixed;
  top: 0px;
  z-index: -1;
  min-width: 1920px;
}

/******************* CONTENT ******************/
#content {
  background: url("/img/vistl/texture.png");

  h3 {

    font-size: 18px;
    margin-bottom: 5px;
  }
}

.main-content, .main-content-inner {
  margin: 20px 0;
  overflow: hidden;
}

h3.main-title {
  color: $corporate-2;
  font-weight: 700;
  font-size: 18px;
  margin-bottom: 5px;
}

.banner-content {
  overflow: hidden;
}

.banner {
  float: left;
  position: relative;
  width: 360px;
  height: 220px;

  .description {
    position: absolute;
    bottom: 0;
    background: rgba(0, 0, 0, .5);
    color: white;
    padding: 10px;

    h3 {
      text-transform: uppercase;
      font-weight: 400;
      margin-bottom: 10px;
    }
    p {
      font-size: 14px;
      font-weight: bold;
    }
  }
}

.row-content {
  margin-top: 20px;
  clear: both;
  overflow: hidden;

  #contentInPage h3 {
    font-size: 22px;
    text-transform: uppercase;
    margin-bottom: 10px;
  }
}

.main-block, .newsletter-block {
  box-sizing: border-box;
  background: white;
  padding: 30px;

  position: relative;
}

.newsletter-block {
  min-height: 180px;
}

.newsletter-block .dots {
  position: absolute;
  bottom: 5px;
}

/******************** Flex Slider ********************/

.mini_gallery_wrapper {
  padding: 40px 0px;
  background: white;

  h3 {
    padding-bottom: 33px;
    text-align: center;
    font-size: 30px;
    color: $corporate_1;
    font-weight: lighter;
    box-shadow: 0 4px 5px #A2A2A2;
    z-index: 2;
    position: relative;
  }
}

.flexslider {
  height: auto;
  width: 100%;
  position: relative;

  li {
    //width: 395px!important;
    .text-bannerx2 img {
      //width: 25%;
      height: 384px;
      max-width: none;
    }
  }
}

/***************** ROOMS *****************/
.rooms_home_container {
  margin-top: 20px;
  overflow: hidden;
}

.room_home_wrapper {
  width: 364px;
  float: left;
  margin-bottom: 20px;
  margin-right: 22px;

  &.block-3, &.block-6 {
    float: right;
    margin-right: 0;
  }
}

.room_home_wrapper.marginright {
  margin-right: 20px;
}

.room_home_back {
  width: 365px;
  min-height: 471px;
  background-color: rgb(245, 245, 245);
  position: relative;
}

//simulating the crop
.room_home_image {
  width: 365px;
  height: 130px;
  overflow: hidden;
  position: relative;

}

.room_home_image img {
  margin-top: -100px;
}

.room-img-lupa {
  position: absolute;
  top: 100px;
  right: 0px;
}

.room_home_title {
  padding: 30px 0;
  color: white;
  font-size: 19px;
  font-weight: normal;
  font-style: italic;
  text-align: center;
  background: $corporate-2
}

.room_home_description {
  padding: 30px 30px 30px 30px;
  text-align: left;
  font-weight: 200;
  line-height: 19px;

  a {
    text-align: center;
    margin-bottom: 5px;
    font-weight: normal;
    display: block;
  }
}

//room buttons
.buttons-rooms {
  text-align: center;
  padding-bottom: 15px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  display: block;

  a {
    display: block;
  }
}

.button-room-more button {
  cursor: pointer;
  @include border-radius(2px);
  color: white;
  border: 0;
  background-color: #606060;
  font-size: 12px;
  text-transform: uppercase;
  width: 110px;
  padding: 5px 20px 5px 20px;
  text-decoration: none;
  margin-bottom: 10px;
}

.button-room-more button:hover {
  color: $corporate-1;
  background-color: #edeef0;
}

.button-promotion button {
  cursor: pointer;
  @include border-radius(2px);
  color: white;
  border: 0;
  background: $corporate-2;
  font-size: 12px;
  text-transform: uppercase;
  width: 110px;
  padding: 5px 20px 5px 20px;
  text-decoration: none;
}

.button-promotion button:hover {
  color: $corporate-1;
  background: #edeef0;
}

.room-info-bottom {
  text-align: center;
  font-weight: bold;
  font-size: 15px;
}

/******************** PROMOTIONS SECTIONS ***************/
.main-content-promotions {
  margin-top: 20px;
  margin-bottom: 10px;
  overflow: hidden;
}

.promotion_wrapper_1 {
  width: 560px;
  margin-bottom: 10px;

  &.odd {
    float: left;
  }
  &.even {
    float: right;
  }
}

.contFotoRooms {
  float: left;
  overflow: hidden;
  height: 185px;
  width: 285px;
}

.contFotoRooms img {
  height: 185px;
  width: 285px;
}

.block_description {
  float: left;
  width: 255px;
  height: 154px;
  background-color: white;
  padding: 20px 10px 10px 10px;
  text-align: center;
  position: relative;
  line-height: 18px;
}

.promotion-title {
  color: $corporate-2;
  font-weight: bold;
  font-size: 18px;
  line-height: 18px;
  margin-bottom: 10px;
}

.promotions_description {
  height: 65px;
  overflow: hidden;
  line-height: 16px;
}

.promotions-buttons-wrapper {
  position: absolute;
  display: block;
  top: 142px;
  left: 32px;
}

.promotion-view-more, #button2_promotions {
  cursor: pointer;
  @include border-radius(2px);
  color: white;
  border: 0;
  background-color: $corporate-2;
  font-size: 12px;
  text-transform: uppercase;
  width: 100px;
  padding: 5px 20px 5px 20px;
  text-decoration: none;
  margin-right: 10px;
}

.promotion-view-more:hover, #button2_promotions:hover {
  background-color: $corporate-1;
}

.promo_full_description {
  padding: 20px;
}

.title-full-promo {
  color: $corporate-2;
  text-align: center;
  font-weight: 300;
  font-size: 20px;
  margin-bottom: 20px;

}

/**************PROMOCIONES******************/
.promotions {
  background: white;
  margin-bottom: 20px;
  position: relative;
}

#block_promotion {
  padding: 30px 30px 10px 30px;
  position: relative
}

#block_promotion .title {
  position: absolute;
  top: -60px;
  left: 270px;
  font-size: 24px;
  color: white;
}

#block_promotion .subtitle {
  font-weight: 100;
}

.promotion_header {
  background: $corporate-2;
  height: 100px;
}

.intro {
  margin-bottom: 30px;
  font-family: 'Roboto', sans-serif;
  font-size: 14px;
  font-weight: bold;
}

#block_promotion .intro h4.hight {
  padding: 0;
  color: $corporate-2;
  font-family: 'Roboto', sans-serif;
  font-weight: 400;
  font-size: 14px;
  text-transform: uppercase;
}

.intro p {
  color: rgb(135, 134, 138);
  font-weight: 100;
}

.data {
  font-family: 'Roboto', sans-serif;
  font-size: 24px;
  color: rgb(135, 134, 138);
  font-weight: 100;
  margin: 30px 0 10px;
}

.data .bold {
  font-weight: 400;
}

.booking_promotion {
  position: absolute;
  right: 20px;
  bottom: 30px;

}

.booking_promotion:hover {
  background: rgb(209, 165, 92);
}

/**************** SERVICES *******************/
.blocks-services {
  overflow: hidden;
  margin-top: 30px;
}

.service {
  width: 530px;
  float: left;

  &.block-1 {
    margin-right: 20px;
  }
}

.service h3 {
  text-align: center;
  padding: 15px 0;
  background-color: $gray-4;
  color: $corporate-1;
  font-size: 18px;
  font-weight: bold;
}

.service-description {
  padding: 10px;
  text-align: center;
  font-weight: bold;
}

.img-entorno {
  float: left;
  margin-right: 15px;
}

/************* LOCATION AND CONTACXT SECTION *****************/
.location-info {
  text-align: left;
  padding-left: 10px;
  width: 540px !important
}

.location-info-and-form-wrapper {
  padding: 10px 0;
}

.location-info-and-form-wrapper h1 {
  color: $corporate-2;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  margin-bottom: 30px;
}

.location-info strong {
  font-weight: bold;
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  padding-bottom: 30px;
}

#slider_map_container {
    background: white;
    margin-bottom: 30px;
}

#contactContent {
   .privacy_checkbox {
      display: inline-block !important;
       .input {
          align-items: center !important;
          width: 470px;
          .bordeInput {
            width: 10%;
          }
          .title, label {
            width: 90% !important;
          }
       }
   }
}



//customer support form
.form-contact {
  padding-right: 10px;
  width: 540px !important;
}

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;

  a {
    text-decoration: underline;
    color: $corporate_1;
  }
}

#contactContent span.title {
  float: left;
}

.form-contact #contactContent .bordeInput {
  width: 47px;
  margin-top: 22px;

}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0;
  width: 88%;
  border: 0px !important;
  height: 30px;
  background-color: $corporate-2;
  color: white;
  float: left;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0;
  width: 88%;
  border: 0px;
  background-color: $corporate-2;
  color: white;
  float: left;
}

.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 60px;
  margin-top: 20px;
}

.form-contact #contact-button {
  border-radius: 5px !important;
  height: 30px !important;
  width: 130px !important;
  background: $corporate-2 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;
}

.form-contact #contact-button:hover {
  background-color: $corporate-2 !important;
}

ul.location_destiny {
  margin-top: 10px;
  margin-bottom: 20px;

  li {
    padding: 10px 0 10px 35px;
    cursor: pointer;

    a {
      text-decoration: none;

      &:hover {
        color: $corporate-1;
      }

    }
    a.active {
      color: white;
    }
  }
  .car {
    background: url('/img/solyl/car.png') left center no-repeat;
  }
}

.beach-popup, .how-to-go.active {
  color: $corporate-1;
}

.beach-popup, .how-to-go:hover {
  color: $corporate-1;
}

/********************** MY BOOKINGS ***********************/
.booking-content {
  text-align: center;
  margin-bottom: 20px;
}

#my-bookings-form-fields {
  text-align: center;

  label {
    display: block;
    color: $corporate-2;
    text-transform: uppercase;
  }
  input {
    display: block;
    margin: 0 auto;
    margin-bottom: 10px;
    width: 250px;
    height: 15px;
    padding: 5px;
    text-align: center;
    background-color: $gray-4;
    color: black;
    border: none;
  }
  #my-bookings-form-search-button {
    width: 260px;
    color: white;
    background-color: $corporate-2;
    padding: 10px 0;
    border: none;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: $corporate-1;
    }
  }
}

#cancel-button-container {
  text-align: center;

  #cancelButton {
    width: 260px;
    color: white;
    background-color: $corporate-1;
    padding: 10px 0;
    border: none;
    font-size: 14px;
    cursor: pointer;
    display: none;

    &:hover {
      background-color: $corporate_2;
    }
  }
}

/***************** FOOTER *****************/
footer {
  background: #eee;
}

.wrapper_footer_columns {
  padding-top: 20px;
  margin-bottom: 20px;
}

.footer_column {
  box-sizing: border-box;
  background: white;
  padding: 20px;
  min-height: 150px;
}

.footer_column_title, .slider-footer h3 {
  @extend h3.main-title;
  font-size: 16px;
  margin-bottom: 20px;

}

.footer_column {
  position: relative;
}

.footer_column_title {
  text-transform: uppercase;
}

.newsletter_container {
  width: auto;
}
.popup_inicial {
    .check_newsletter {
        a {
          text-decoration: underline;
          color: white;
        }
        .check_privacy {
          width: 30px;
          margin-left: 0;
          height: auto;
        }
        label[for="accept-promo"] {
              display: inline;
              float: none;
        }
    }
}
.check_newsletter {
  float: left;

  a {
    text-decoration: underline;
    color: $corporate_1;
  }
}

#title_newsletter {
  font-size: 22px;
  text-transform: uppercase;
  margin-bottom: 10px;
  display: none;
}

#suscEmail {
  width: 190px;
  padding: 7px;
  margin: 10px 10px 10px 0px;
  border: none;
  background-color: #e6e6e6;
  outline: none;
  float: left;
}

.button_newsletter {
  background-color: $corporate_1;
  padding: 8px 32px;
  border: none;
  color: white;
  font-weight: bolder;
  width: 106px;
  cursor: pointer;
  height: 34px;
  font-size: 14px;
  margin-top: 6px;
  display: inline-block;

  &:hover {
    background-color: $corporate-2;
  }
}

.full-copyright {
  background-color: $corporate-1;
  color: white;
  text-align: center;
  padding: 10px 0;

  a {
    color: white;
  }
}

#google_plus_one {
  text-align: center;
}

#___plusone_0 {
  margin-top: 10px !important;
  width: 64px !important;
}

#facebook_like {
  text-align: center;
  margin-top: 10px;
}

#facebook_like iframe {
  height: 21px;
  width: 103px;
}

.dot {
  display: inline-block;
  width: 10px;
  height: 10px;
  margin: 0 4px;
  text-indent: -10000px;
  background-color: $corporate-1;
  border: 1px solid $corporate-1;
  color: $corporate-1;
  border-radius: 5px;
  cursor: pointer;
}

.dot.active {
  background-color: $corporate-2;
  border: 1px solid $corporate-2;
  color: $corporate-2;
}

.gallery_1 li .crop {
  width: 270px;
}

.fancybox-inner img {
  width: 100%;
}

.fancybox-inner .spinner_wrapper_faldon img {
  width: 35px;
}

#fancybox-buttons a {
  background: none !important;
}

/************************************************/
/**************** Bottom Pop-up *****************/
/************************************************/

.bottom_popup {
  position: fixed;
  width: 100%;
  padding: 22px 0;
  background: $corporate-2;
  left: 0;
  bottom: 0;
  z-index: 1000;
}

.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

.bottom_popup .bottom_popup_text {
  width: 890px;
  float: left;
  color: white;
  padding: 10px;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

.close_button {
  float: right;
  cursor: pointer;
  position: absolute;
  top: 10px;
  right: 10px;
}

button.bottom_popup_button {
  background: #D9DADE;
  width: 150px;
  color: #787878;
  text-transform: uppercase;
  height: 40px;
  font-size: 18px;
  margin-top: 10px;
  float: right;
  cursor: pointer;
  border: none;

  &:hover {
    background: $corporate-1;
    color: white;
  }
}

#wrapper2 {
  width: 1140px;
  margin: 0 auto;
}

/************************************************/
/**************** pop-up inicial ****************/
/************************************************/

.popup_thanks {
  padding-top: 10px;
  box-sizing: border-box;
  text-align: center;
  width: 100%;
  height: 100%;
  background-size: cover !important;

  #new_gracias_newsletter {
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    margin: 20px;
    padding: 20px;
    h3 {
      font-size: 20px;
      margin-bottom: 10px;
    }
  }
  button {
    background-color: $corporate_1;
    color: white;
    font-size: 20px;
    border-width: 0;
    padding: 10px 20px;
    &:hover {
      background-color: darken($corporate_1, 10%);
    }
  }
}

.wrapper_booking_button input[name=promocode].applyed {
  background-color: $gray-1 !important;
  color: white !important;
  text-align: center;
  font-size: 18px;
}

.popup_inicial {
  @include border-radius(4px);
  background: url("/img/vistl/popup_newsletter.png") -5px no-repeat;
  height: 320px !important;
  width: 650px;
  padding: 20px;

}

.popup_title {
  font-size: 26px;
  padding: 10px;
  color: white;
  text-transform: uppercase;
}

.popup_description {
  padding: 10px;
  font-size: 14px;
  width: 234px;
  line-height: 22px;
  color: white;
  padding-bottom: 0;
}

.popup_inicial form {
  padding: 10px;
  font-size: 14px;

  label {
    float: left;
    display: block;
    color: white;
    margin-top: 2px;
    font-size: 14px;
  }
}

.popup_inicial form li {
  margin: 10px 0;
}

.popup_inicial form input {
  width: 150px;
  height: 25px;
  margin-left: 20px;
}

.popup_inicial button {
  background: $corporate-1;
  width: 155px;
  height: 40px;
  font-size: 12px;
  margin-top: 0px;
  text-transform: uppercase;
  color: white;
  margin-left: 50px;
  border: none;
  cursor: pointer;

  &:hover {
    background: darken($corporate-1, 10%);
  }
}

#wrapper-header.hidden_top_menu {
  background: white;
  position: fixed !important;
  top: 0;
  display: none;
  z-index: 401;
  width: 100%;
  box-shadow: 0 2px 6px -2px gray;

  #main-sections-inner {
    text-align: center;
  }

  div#logoDiv {
    position: relative;
    z-index: 3;
    margin-top: 15px;
    width: 147px;
  }

  button.deploy_booking {
    float: right;
    border: 0;
    background: $corporate-2;
    color: white;
    height: 100%;
    text-transform: uppercase;
    line-height: 40px;
    padding: 0 45px;
    position: relative;
    z-index: 3;
    cursor: pointer;
    font-size: 18px;
    outline: none;
    border-bottom: 10px solid white;
    border-top: 10px solid white;

    &:hover {
      background: $corporate-1;
    }
  }

  #main_menu {
    left: 160px;
    right: 330px;
    background: white;
    margin-top: 10px;
  }
  #mainMenuDiv a {
    color: $corporate-1;
  }
  #section-active a,
  #mainMenuDiv a:hover {
    color: white;
  }
}

.hidden_booking_widget_header {
  position: fixed;
  top: 60px;
  display: none;
  width: 100%;
  z-index: 24;
  background: #eee;
  padding: 10px 0px;

  #full_wrapper_booking {
    padding-top: 0;
    position: relative;
    bottom: 0;

    #motor_reserva select, #envio input {
      margin-bottom: 0 !important;
      border-bottom: 1px solid #f0f0f0 !important;
      height: 40px !important;
      box-sizing: border-box;
    }
    #envio {
      padding-bottom: 0 !important;
    }

    #booking fieldset {
      margin-top: 0 !important;
    }

    #motor_reserva {
      margin: 0 !important;
    }

    #fecha_entrada input, #fecha_salida input {
      border-bottom: 1px solid #f0f0f0 !important;
    }
    #search-button {
      height: 40px !important;
    }
  }

  #main_menu {
    top: 0;
  }
  #booking label {
    display: none;
  }

  #full_wrapper_booking #booking fieldset {
    margin: 5px 0 0;
    float: left;
  }
  #full_wrapper_booking #envio input {
    width: 134px !important;
  }
  #full_wrapper_booking #search-button {
    background: $corporate-2;

    &:hover {
      background: $corporate-1;
    }
  }
}

.booking_engine_inside {

  #full_wrapper_booking {
    padding-top: 0;
    position: relative;
    width: 1140px;
    margin: auto;

    #motor_reserva select, #envio input {
      margin-bottom: 0 !important;
      border-bottom: 1px solid #f0f0f0 !important;
      height: 40px !important;
      box-sizing: border-box;
    }
    #envio {
      padding-bottom: 0 !important;
    }

    #booking fieldset {
      margin-top: 0 !important;
    }

    #motor_reserva {
      margin: 0 !important;
    }

    #fecha_entrada input, #fecha_salida input {
      border-bottom: 1px solid #f0f0f0 !important;
    }
    #search-button {
      height: 40px !important;
    }
  }

  #main_menu {
    top: 0;
  }
  #booking label {
    display: none;
  }

  #full_wrapper_booking #booking fieldset {
    margin: 5px 0 0;
    float: left;
  }
  #full_wrapper_booking #envio input {
    width: 134px !important;
  }
  #full_wrapper_booking #search-button {
    background: $corporate-1;
  }
}

/*==== Right popup ===*/
.popup_right {
  position: fixed;
  left: 50%;
  opacity: 0;
  margin-left: -225px;
  top: 50%;
  margin-top: -225px;
  bottom: 0;
  z-index: 222222;
  width: 450px;
  box-sizing: border-box;
  height: 450px;
  min-height: 450px;

  .circle_box {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: white;
    border-radius: 50%;
    overflow: hidden;

  }

  a.button-promotion {
    background: #f5d626;
    color: #4b4b4b;
    text-transform: uppercase;
    padding: 15px 30px;
    font-weight: 700;
    display: block;
    width: 100%;
    box-sizing: border-box;
    text-decoration: none;
    margin: 20px auto 0;
    text-align: center;
    z-index: 22;
    left: 0;
    right: 0;
  }

  .logo {
    display: block;
    margin: 25px auto 0;
    width: 150px;
  }

}

.close_button_popup {
  position: fixed;
  left: 50%;
  margin-left: 200px;
  opacity: 0;
  width: 50px;
  background: white;
  border-radius: 50%;
  z-index: -1;

  img {
    display: block;
    margin: auto;
    padding: 10px 0;
    cursor: pointer;
    border-radius: 50%;
  }
}

.black_opacity_overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  opacity: 0;
  display: none;
}

.popup_right_text {
  display: table;
  margin: auto;
  text-align: center;
  padding: 0 15px;
  color: #aaa;
  z-index: 22;
  font-family: Arial, sans-serif;
  font-size: 16px;

  .highlight {
    color: #85a726;
    margin-top: 20px;
    font-size: 18px;
    text-align: center;
  }
}

h4.popup_right_title {
  font-family: Arial, sans-serif;
  font-size: 21px;
  font-weight: bold;
  margin-top: 25px;
  margin-bottom: 25px;
  color: rgba(0, 0, 0, 0.67);
  text-align: center;
  padding: 10px 0;
}
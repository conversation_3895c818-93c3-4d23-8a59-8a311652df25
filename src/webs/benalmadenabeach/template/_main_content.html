{# Content subtitle #}
{% if content_subtitle and not room_individual_section %}
    <div class="content_subtitle_wrapper">
        <div class="container12">
            {% if offers_section %}
                <div class="offers_sections_wrapper">
                    {% for x in offers_section %}
                        <a class="offer_section {% if x.sectionName == sectionToUse.sectionName %}active{% endif %}"
                           href="{{ x.friendlyUrlInternational|safe }}">{{ x.title|safe }}</a>
                    {% endfor %}
                </div>
            {% endif %}

            <h1 class="content_subtitle_title"><span>{{ content_subtitle.subtitle|safe }}</span></h1>

            {% if content_subtitle.content %}
            <div>
                <div class="content_subtitle_description">
                    {{ content_subtitle.content|safe }}
                </div>
            </div>
                {% if home %}
                <script>
                $(window).load(function() {
                    reduceText(".content_subtitle_description","50");
                });
                </script>
                {% endif %}
            {% endif %}

            {% if booking_button %}
                <a class="button-promotion" href="#data">{{ T_reservar }}</a>
            {% endif %}

        </div>
    </div>
{% endif %}

{% if content_access %}
    <div class="content_access">
        <div class="container12">
            {{ content|safe }}
        </div>
    </div>
    {% if my_bookings %}
        <div id="my_bookings_form_wrapper">
            <div class="container12"></div>
        </div>
        <script>
            $(function () {
                $("#my_bookings_form_wrapper .container12").append($("#my-bookings-form"));
            })
        </script>
    {% endif %}
{% endif %}

{% if rooms %}
    {% include "_rooms.html" %}
{% endif %}

{% if rooms_individual %}
    {% include "_rooms_individual.html" %}
{% endif %}

{% if offers %}
    {% include "_offers.html" %}
{% endif %}

{% if gallery_section %}
    {% include "_gallery.html" %}
{% endif %}

{% if form_contact %}
    {% include "_form_contact.html" %}
{% endif %}

{% if form_contact_cv %}
    {% include "_form_contact_cv.html" %}
{% endif %}

{% if minigallery %}
    <div class="minigallery_content_wrapper effects_sass" sass_effect="slide_up_effect">
            {% if minigallery_content.subtitle or minigallery_content.content %}
                <div class="minigalery_content">
                    <div class="minigallery_title">{{ minigallery_content.subtitle|safe }}</div>
                    {% if minigallery_content.content %}
                        <div class="minigallery_description">{{ minigallery_content.content|safe }}</div>
                    {% endif %}
                </div>
            {% endif %}
            {{ minigallery|safe }}
    </div>
{% endif %}

{% if not minigallery %}
    {% if bannersx4 %}
        <div class="bannersx4_wrapper {% if cycle_banners %}cycle_banners{% endif %}">
            {% for x in bannersx4 %}
                <a class="banner_element effects_sass" sass_effect="slide_up_effect" {% if x.linkUrl %}href="{{ x.linkUrl }}" {% if "http" in x.linkUrl %}target="_blank" {% endif %}{% endif %}>
                    <div class="banner_image">
                        <img src="{{ x.servingUrl|safe }}=s800" class="center_image"/>
                        <div class="banner_title">{{ x.title|safe }}</div>
                    </div>
                    <div class="banner_content">
                        <div class="banner_block">
                            <div class="banner_description">{{ x.description|safe }}</div>
                            {% if x.linkUrl %}
                                <div class="banner_link">
                                    <div>
                                        <span><i class="fa fa-long-arrow-right"></i></span>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </a>
            {% endfor %}
        </div>
    {% endif %}
{% endif %}

{% if banner_video %}
    <div class="banner_video_wrapper">
        <div class="container12">
            {% if banner_video.subtitle %}
                <h3>{{ banner_video.subtitle|safe }}</h3>
            {% endif %}
            <div class="video_wrapper">
                {{ banner_video.content|safe }}
            </div>
        </div>
    </div>
{% endif %}

{% if bannerx2_text %}
    <div class="bannersx2_text_wrapper effects_sass" sass_effect="slide_up_effect">
        {% for x in bannerx2_text %}
            <div class="banner_element">
                <div class="banner_content center_xy">
                    <div class="banner_title">{{ x.title|safe }}</div>
                    <div class="banner_description">{{ x.description|safe }}</div>
                    {% if x.linkUrl %}
                        <a class="banner_link" {% if x.linkUrl %}href="{{ x.linkUrl }}" {% if "http" in x.linkUrl %}target="_blank" {% endif %}{% endif %}>
                            <div>
                                <span>{% if x.button_text %}{{ x.button_text|safe }}{% else %}{{ T_ver_mas }}{% endif %}</span>
                            </div>
                        </a>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>
{% endif %}

{% if minigallery %}
    {% if bannersx4 %}
        <div class="bannersx4_wrapper {% if cycle_banners %}cycle_banners{% endif %}">
            {% for x in bannersx4 %}
                <a class="banner_element effects_sass" sass_effect="slide_up_effect" {% if x.linkUrl %}href="{{ x.linkUrl }}" {% if "http" in x.linkUrl %}target="_blank" {% endif %}{% endif %}>
                    <div class="banner_image">
                        <img src="{{ x.servingUrl|safe }}=s800" class="center_image"/>
                        <div class="banner_title">{{ x.title|safe }}</div>
                    </div>
                    <div class="banner_content">
                        <div class="banner_block">
                            <div class="banner_description">{{ x.description|safe }}</div>
                            {% if x.linkUrl %}
                                <div class="banner_link">
                                    <div>
                                        <span><i class="fa fa-long-arrow-right"></i></span>
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </a>
            {% endfor %}
        </div>
    {% endif %}
{% endif %}

{% if banner_floating %}
    {% include "_banner_floating.html" %}
{% endif %}

{% if banner_logos %}
    {% include "_banner_logos.html" %}
{% endif %}

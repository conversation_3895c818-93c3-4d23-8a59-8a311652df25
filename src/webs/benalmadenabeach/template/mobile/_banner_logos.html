<div class="banner_logos_wrapper">
    {% if banner_logos_sec and banner_logos_sec.subtitle %}<h2>{{ banner_logos_sec.subtitle|safe }}</h2>{% endif %}
    {% for banner_logos, pics in banner_logos.items() %}
        {% if banner_logos %}<h3>{{ banner_logos|safe }}</h3>{% endif %}
        <div class="logos_wrapper">
            {% for pic in pics %}
                <a {% if pic.linkUrl %}href="{{ pic.linkUrl|safe }}" {% if 'http' in pic.linkUrl %}target="_blank"{% endif %}
                {% endif %} class="logo {% if pic.description %}with_desc{% endif %}">
                {% if pic.servingUrl %}<img src="{{ pic.servingUrl }}=s600" alt="{{ banner_logos|safe }}" title="{{ banner_logos|safe }}">{% endif %}
                {% if pic.description %}<span class="overlay">{{ pic.description|safe }}</span>{% endif %}
                </a>
            {% endfor %}
        </div>
    {% endfor %}
</div>
{% if banner_logos_color %}
<style>
.banner_logos_wrapper {
    background-color: {{ banner_logos_color|safe }};
}
.banner_logos_wrapper:before {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0) 50%, {{ banner_logos_color_pico|safe }} 50%, {{ banner_logos_color_pico|safe }});
}
</style>
{% endif %}
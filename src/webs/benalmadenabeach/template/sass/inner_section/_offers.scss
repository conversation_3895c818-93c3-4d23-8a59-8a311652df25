.offers_wrapper.offer_section {
  padding: 60px 0;
  -webkit-font-smoothing: antialiased;
  .offer_wrapper {
    display: inline-block;
    width: calc((100% - 20px) / 2);
    margin: 0 3px 10px;
    .offer {
      position: relative;
      height: 350px;
      overflow: hidden;
      @include transition(all, .6s);
      &:nth-child(even) {
        margin-right: 0;
      }
      img {
        @include center_image;
        opacity: 1;
        @include transition(all, .6s);
      }
      .banner_title {
        @include center_xy;
        text-align: left;
        a {
          display: none;
          padding: 15px 20px;
          text-align: center;
          text-transform: uppercase;
          font-size: 12px;
          font-weight: bold;
          margin-top: 20px;
          @include transition(all, .6s);
          i.fa {
            font-size: 30px;
            line-height: 20px;
            vertical-align: middle;
            margin-right: 10px;
          }
          &:hover {
            background-color: darken(#ffa543, 10%);
          }
          &.read_more {
            i.fa {
              font-family: 'FontAwesome';
              font-size: 20px;
            }
          }
        }
      }
      &:hover {
        background-color: #36424e;

        img {
          opacity: .4;
        }


      .banner_title {
          width: 100%;
          text-align: center;
        a {
          background-color: #006887;
          width: 220px;
          margin: auto;
          color: white;
          display: block;
          &:hover {
            background-color: darken(#006887, 10%);
          }
          &.read_more {
            background-color: #006887;
            color: white;
            &:hover {
              background-color: darken(#006887, 10%);
            }
          }
        }
      }
    }
  }

  .banner_text {
      position: relative;
      background-color: #006887;
      padding: 35px;
      float: left;
      width: 100%;
      height: 342px;
      @include top_corner(#006887);
      &:before {
        top: -40px;
        -webkit-transform: translate(-50%,0) rotate(-135deg);
        -moz-transform: translate(-50%,0) rotate(-135deg);
        -ms-transform: translate(-50%,0) rotate(-135deg);
        -o-transform: translate(-50%,0) rotate(-135deg);
        transform: translate(-50%,0) rotate(-135deg);
      }
      .title {
        display: block;
        font-size: 32px;
        color: white;
        font-weight: lighter;
        font-family: "Ropa Sans", sans-serif;
        letter-spacing: 3px;
      }
      .desc {
        font-size: 16px;
        color: white;
        font-family: "Muli", sans-serif;
        a {
          color: white;
        }
      }
    }
  }


}
#full_wrapper_booking {
  position: absolute;
  padding: 20px 0;
  width: 100%;
  min-width: 1140px;
  z-index: 500;
  bottom: 100px;
  font-family: $text_family;
  @include transition(background-color, .6s);

  .selectricItems {
    overflow: auto !important;
  }

  #full-booking-engine-html-7 {
    width: auto;
    display: table;
    margin: auto !important;
    position: relative;

    .promocode_header {
      display: none;
    }
  }

  #full-booking-engine-html-7 form.booking_form {
    background: transparent;
    position: relative;
  }

  .destination_wrapper {
    display: inline-block;
    //width: 175px;
    width: 210px;
    float: left;
    height: 40px;
    color: $corporate_2;
    background: white !important;
    padding: 10px;
    cursor: pointer;
    border-radius: 5px;
    margin-right: 5px;
    border: none;
    .label_hotel_selector {
      @include label_styles;
      margin: 0;
      display: none;
    }
    .destination_field {
      position: relative;
      z-index: 1;
      display: inline-block;
      width: 100%;
      padding-top: 0;
      @include icon_down;
      &:after {
        right: 2px;
        bottom: 0;
      }
      > i {
        position: absolute;
        top: 0;
        left: 0;
        color: $corporate_2;
        font-size: 20px;
        z-index: -1;
      }
      .destination {
        border: none;
        box-shadow: none;
        padding-left: 30px;
        width: 100%;
        background: transparent;
        margin-bottom: 0;
        cursor: pointer;
        color: $corporate_2;
        @include destination_input_styles;
        &:focus {
          outline: 0;
        }
        &::-webkit-input-placeholder {
          @include destination_input_styles;
        }
        &::-moz-placeholder {
          @include destination_input_styles;
        }
        &:-ms-input-placeholder {
          @include destination_input_styles;
        }
        &:-moz-placeholder {
          @include destination_input_styles;
        }
      }
      &.selected {
        &:after, > i {
          display: none;
        }
        .destination {
          padding-left: 0;
          padding-right: 0;
          font-size: 14px;
        }
      }
    }
    &:not(.unselected) {
      padding-right: 0;
      .destination_field {
        > i, &:after {
          display: none;
        }
        .destination {
          padding-left: 0;
          font-size: 13px;
        }
      }
    }
  }
  .hotel_selector {
    display: none;
    position: fixed;
    background: white;
    box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
    z-index: 4;
    box-sizing: border-box;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    &:before {
      content: '';
      @include full_size;
      background-color: rgba(white, .9);
    }
    .close_hotel_selector {
      position: absolute;
      top: 30px;
      right: 30px;
      color: $corporate_1;
      font-size: 50px;
      cursor: pointer;
    }
    .hotel_selector_inner {
      position: absolute;
      top: 20%;
      max-height: 80%;
      right: 0;
      left: 0;
      padding: 30px calc((100% - 1140px) / 2);
      overflow: auto;
      display: inline-block;
      vertical-align: top;
      .search_hotels {
        position: relative;
        z-index: 2;
        display: block;
        clear: both;
        margin-bottom: 20px;
        @extend .fa-search;
        &:before {
          @extend .fal;
          @include center_y;
          left: 10px;
          font-size: 30px;
          line-height: 52px;
          color: #efefef;
          padding-right: 10px;
          border-right: 2px solid #efefef;
        }
        .search_hotels_selector {
          width: 100%;
          padding: 15px 15px 15px 65px;
          background: transparent;
          font-size: 20px;
          font-weight: 700;
          border-width: 0;
          border-bottom: 2px solid #efefef;
          outline: none;
          &::placeholder {
            color: #AAA;
          }
        }
        &:focus {
          &:before {
            color: $corporate_1;
            border-right-color: $corporate_1;
          }
          .search_hotels_selector {
            border-bottom-color: $corporate_1;
          }
        }
        .all_hotels_list_search {
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          background: #ECECEC;
          padding: 20px;
          a {
            display: inline-block;
            vertical-align: top;
            float: left;
            width: calc(100% / 4);
            &:hover {
              color: $corporate_3;
              small {
                color: #aaa;
              }
            }
            strong {
              font-weight: 700;
            }
            small {
              color: #AAA;
            }
          }
        }
      }

      .destiny {
        padding: 5px 0;
        cursor: pointer;
        text-align: center;
        @include transition(all, .6s);
        ul {
          .hotel_selector_option {
            padding: 5px;
            .title_selector {
              position: relative;
              font-weight: 500;
              text-transform: uppercase;
              color: $corporate_1;
              font-size: 28px;
              letter-spacing: 1px;
              font-family: $title_family;
            }
          }
        }
        &:hover {
          background-color: $corporate_1;
          ul .hotel_selector_option .title_selector {
            color: white;
          }
        }
      }
    }
  }
  .start_end_date_wrapper {
    background: white;
    border-radius: 5px;
    font-weight: 700;
    color: $corporate_2;
    width: 320px;
    height: 40px;
    padding: 10px 33px 10px;

    &:before {
      content: '\f133';
      @include center_y;
      left: 10px;
      font-family: "Font Awesome 5 Pro", sans-serif;
      font-weight: 300;
    }

    &:after {
      content: '\f107';
      font-family: "Font Awesome 5 Pro", sans-serif;
      font-size: 14px;
      padding: 5px 10px;
      color: $corporate_2;
      @include center_y;
      right: 70px;
    }

    .nights_number_wrapper_personalized {
      width: 75px;
      font-weight: 300;
      background-color: $corporate_2;
      border-radius: 0 5px 5px 0;

      .days_number_datepicker {
        margin-top: 3px;
      }

      .night_label {
        line-height: 10px;
        text-transform: uppercase;
      }
    }
  }

  .booking_form_title .best_price {
    display: none;
    color: black;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }

  .promocode_header p.first_offer_name {
    color: black;
  }

  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background: transparent;
  }

  .room_list_wrapper .adults_selector, .room_list_wrapper .children_selector, .room_list_wrapper .babies_selector {
    width: 50% !important;
    height: auto;
    float: left;
    box-sizing: border-box;
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1, .web_support_label_2 {
    line-height: 15px !important;
    font-size: 14px !important;
    color: white;
    text-shadow: 1px 1px 1px #000000;

  }

  .wrapper-new-web-support.booking_form_title {
    text-align: center;
    background: none;
    opacity: 1;
    margin-top: 7px;
    font-size: 13px !important;

    .web_support_label_2 {
      display: inline-block;
      margin: 0 10px;
    }

    .phone_support_image {
      display: none;
    }
  }

  .date_box.entry_date, .date_box.departure_date {
    margin-top: 6px;
    background: url(/img/#{$base_web}/entry_ico.png) no-repeat center;
    background-position-x: left;

    .date_year {
      display: none;
    }

    .date_day {
      border-bottom: 0 !important;
      font-weight: 300;
      font-size: 16px !important;
      color: black;
    }
  }

  .date_box.departure_date {
    background: url(/img/#{$base_web}/departure_ico.png) no-repeat center;
    background-position-x: left;
  }

  .selectricWrapper {
    width: 100% !important;

    .selectric {
      margin-top: 0;
    }
  }

  #slider_inner_container #full-booking-engine-html-7 {
    margin-top: -17px !important;
  }

  .promocode_text {
    display: none;
  }

  .stay_selection {
    display: inline-block;
    vertical-align: top;
    float: left;

    .entry_date_wrapper, .departure_date_wrapper {
      display: inline-block;
      vertical-align: top;
      float: left;
      margin-right: 5px;
      border: 0 !important;
      background: white;
      width: 212px;
      height: 47px;
    }

    .departure_date_wrapper {
      border-left: 0;
      border-right: 0;
    }

    .nights_number_wrapper {
      display: inline-block;
      width: 95px;
      float: left;
      vertical-align: top;
      border-top: 1px solid grey;
    }
  }

  .rooms_number_wrapper {
    float: left;
    display: inline-block;
    vertical-align: top;
    width: 115px;
    height: 40px;
    border-radius: 5px;
    margin-right: 5px;
    padding: 5px 0 5px 10px;
    background: white;
    position: relative;

    .rooms_number {
      padding-left: 45px;
      box-sizing: border-box;
      background: url(/static_1/images/booking_5/rooms_number.png) no-repeat center left;
      background-position-y: 40%;
    }

    .selectricWrapper.rooms_number {
      background: none;

      &:before {
        content: '\f236';
        font-family: "Font Awesome 5 Pro", sans-serif;
        font-weight: 300;
        font-size: 20px;
        color: $corporate_2;
        @include center_y;
        left: 7px;
      }

      .selectric {
        height: 28px;

        .label {
          text-align: right;
          padding-right: 45px;
          line-height: 27px;
          height: 26px;
          font-family: $text_family;
        }

        .button {
          display: none;
        }

        &:after {
          content: '\f107';
          font-family: "Font Awesome 5 Pro", sans-serif;
          font-size: 14px;
          padding: 5px 10px;
          color: $corporate_2;
          @include center_y;
          right: 0;
        }
      }
    }
  }

  .room_list_wrapper {
    display: none;
    position: absolute;
    top: 100%;
    width: auto;
    right: 233px;
    background-color: #fff;
    box-shadow: $box_shadow;

    &:before {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 1px;
      height: 11px;
      z-index: 1;
      background-color: white;
    }

    .room_list {
      margin: 0;
      list-style: none;

      .room {
        margin: 0;
        display: table;
        width: 280px;
        border: 0;
        list-style: none;

        &.room2, &.room3 {
          margin-top: 10px;
        }

        .room_title {
          display: none;
        }

        .adults_selector, .children_selector, .babies_selector {
          display: inline-block;
          width: 50%;
          float: left;
          position: relative;
          text-align: center;
          border: none;
          border-bottom: 1px solid $separator_color;
          padding: 10px;
          height: auto;

          > label {
            @include label_styles;
            text-align: center;
            font-size: 14px;
            padding-bottom: 10px;
          }

          .selectricWrapper {
            position: relative;
            width: 100% !important;
            .selectric {
              cursor: pointer;
              display: block;
              vertical-align: middle;
              margin: 0;
              .label {
                @include option_strong_styles;
                font-size: 32px;
                margin: 0;
              }
              .button {
                display: none;
              }
            }
          }
        }
      }
    }
  }

  .wrapper_booking_button {
    display: inline-block;
    width: auto;
    float: left;
    height: 40px;
    position: relative;

    .my_bookings_link {
      position: absolute;
      bottom: 100%;
      background: $corporate_2;
      color: white;
      font-size: 14px;
      display: none;
      text-align: center;
      border-radius: 5px;
      margin-bottom: 5px;
      padding: 5px;
    }

    label.promocode_label {
      display: none;
    }

    .promocode_wrapper {
      display: inline-block;
      vertical-align: top;
      float: left;
      width: 125px;
      margin-right: 5px;
      border-radius: 5px;
      height: 40px;
      background: white;
      position: relative;
      padding-top: 0;

      input.promocode_input {
        color: $text_color;
        text-transform: uppercase;
        height: 30px;
        margin-top: 5px;
        background: white;
        text-align: center;
        font-family: $text_family;

        &:focus {
          outline: none;
        }

        &::-webkit-input-placeholder {
          font-size: 14px;
          font-weight: 300;
          text-transform: uppercase;
          color: black;
          font-family: $text_family;
        }

        &::-moz-placeholder {
          font-size: 14px;
          font-weight: 300;
          text-transform: uppercase;
          color: black;
          font-family: $text_family;
        }

        &:-ms-input-placeholder {
          font-size: 14px;
          font-weight: 300;
          text-transform: uppercase;
          color: black;
          font-family: $text_family;
        }

        &:-moz-placeholder {
          font-size: 14px;
          font-weight: 300;
          text-transform: uppercase;
          color: black;
          font-family: $text_family;
        }
      }
    }

    .submit_button {
      width: 125px;
      height: 40px;
      display: inline-block;
      vertical-align: top;
      float: left;
      color: white;
      border-radius: 5px;
      font-size: 16px;
      font-family: $text_family;
      font-weight: 800;
      letter-spacing: 1px;
      background: $corporate_1;
      @include transition(background, .6s);

      &:hover {
        background: $corporate_2;
      }
    }
  }

  .selectricWrapper {
    .selectricItems {
      overflow: inherit !important;
      @include box_shadow;
      margin-top: 15px;
      border-width: 0;
      width: 161px !important;
      margin-left: -26px;
      top: 100% !important;

      li {
        border-width: 0;
        background: white;

        &:hover {
          background-color: $corporate_2;
          color: white;
        }

        &.selected {
          background-color: $corporate_2;
          color: white;
        }
      }
    }
  }
}

body.home_section #full_wrapper_booking.fixed_booking {
  width: 100%;
}

.babies_selector {
  width: 33.3%;
  display: inline-block;
  padding: 7px 10px 5px;
  box-sizing: border-box;

  label {
    text-transform: uppercase;
    font-size: 10px;
  }
}

/*=== Ocupancy selector ====*/
.guest_selector {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 110px;
  height: 40px;
  border-radius: 5px;
  padding: 7px 10px 5px;
  box-sizing: border-box;
  cursor: pointer;
  margin-right: 5px;
  background: white;
  position: relative;


  &:before {
    content: '\f007';
    font-family: "Font Awesome 5 Pro", sans-serif;
    font-weight: 300;
    font-size: 18px;
    color: $corporate_2;
    @include center_y;
    left: 10px;
  }

  &:after {
    content: '\f107';
    font-family: "Font Awesome 5 Pro", sans-serif;
    font-size: 14px;
    padding: 5px 10px;
    color: $corporate_2;
    @include center_y;
    right: 0;
  }

  span.placeholder_text {
    @include center_y;
    left: 10px;
    font-size: 14px;
    font-weight: 300;
    display: block;
    padding-left: 33px;
    box-sizing: border-box;
    background: none;
    background-position-y: 0;

    &.selected_value {
      color: #585d63;
      font-size: 21px;
      padding-top: 3px;
      background-position-y: 8px;
      font-weight: 600;
    }
  }

  & > label {
    text-transform: uppercase;
    font-size: 10px;
    cursor: pointer;
  }

  b.button {
    background: none;
    display: none;
  }
}

#booking label {
  cursor: pointer;
}

.selectricWrapper .selectric .label {
  font-weight: 300;
  font-size: 16px;
  line-height: 37px;
  color: black;
}

#booking .room_list label {
  display: block !important;
}

#booking .room_list .room2 label, #booking .room_list .room3 label {
  display: none !important;
}

#booking label {
  display: none;
}

.hotel_selector {
  display: none;
}

div#full_wrapper_booking.floating_booking.showed {
  position: fixed;
  top: 0;
  bottom: auto;
  width: 100%;
  background-color: rgba($corporate_2, 0.9);
}

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
  border-radius: 0;
  border-top: 2px solid $grey;
  margin-top: 0;
  font-family: $text_family;
  @include box_shadow;

  &.datepicker_wrapper_up {
    margin-bottom: -35px;
    border-top: 2px solid $corporate_1;

    &:after, &:before {
      position: absolute;
      left: 0;
      margin: 0 auto;
      right: 0;
      top: auto;
      bottom: -24px;
      content: "";
      z-index: 9;
      width: 0;
      height: 0;
      border: 12px solid transparent;
      border-top-color: white;
      @-moz-document url-prefix() {
        display: none;
      }
    }

    &:before {
      bottom: -30px;
      border: 14px solid transparent;
      border-top-color: $corporate_1;
    }
  }

  .header_datepicker {
    background-color: $corporate_1;
    color: white;

    .specific_date_selector {
      font-weight: 400;
      font-family: $text_family;

      &:before {
        display: none;
      }
    }

    .close_button_datepicker {
      border-width: 0;

      &:before {
        @include center_xy;
        content: '\f00d';
        color: white;
        font-size: 16px;
        font-family: "Font Awesome 5 Pro", sans-serif;
      }
    }
  }

  .datepicker_ext_inf_sd, .datepicker_ext_inf_ed {
    .ui-widget-header {
      background: $corporate_2 !important;

      .ui-datepicker-title {
        color: white !important;
      }
    }

    .ui-datepicker-header {
      .ui-corner-all {
        background-color: transparent !important;

        &:before {
          content: '\f0d7';
          font-family: "Font Awesome 5 Pro";
          font-weight: 700;
          @include center_xy;
          color: white;
        }

        span {
          background: transparent !important;
        }
      }
    }

    .ui-datepicker td {
      border-color: white;

      &.ui-state-disabled {
        opacity: .15;
      }

      & .ui-state-active {
        background-color: $corporate_1 !important;
      }

      &.ui-datepicker-start_date {
        span.ui-state-default {
          &:before {
            border-color: transparent transparent transparent $corporate_1;
          }
        }
      }
    }
  }

  .specific_month_selector, .go_back_button {
    background-color: transparent;
    color: $corporate_1;
    border-radius: 0;

    strong {
      color: $corporate_2;
    }
  }

  .months_selector_container .cheapest_month_selector {
    background-color: $corporate_1;
    font-family: $text_family;
    font-weight: 400;
  }
}

#booking_widget_popup #contenedor_fechas .colocar_fechas .float_fecha,
#booking_widget_popup #contenedor_habitaciones label,
#booking_widget_popup #contenedor_opciones label,
#booking_widget_popup #envio input#promocode,
#booking_widget_popup #envio input#promocode::placeholder,
#booking_widget_popup #envio button#search-button,
#booking_widget_popup .selectric .label,
#booking_widget_popup .selectricItems ul li,
#ui-datepicker-div.ui-datepicker .ui-datepicker-title,
#ui-datepicker-div.ui-datepicker .ui-datepicker-calendar thead tr th,
#ui-datepicker-div.ui-datepicker .ui-datepicker-calendar tbody tr td span,
#ui-datepicker-div.ui-datepicker .ui-datepicker-calendar tbody tr td a {
  font-family: $text_family !important;
}
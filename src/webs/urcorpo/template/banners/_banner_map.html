<div class="banner_map_wrapper">
    {% if banner_map.subtitle %}<div class="title">{{ banner_map.subtitle|safe }}</div>{% endif %}
    {% if banner_map.content %}<div class="desc">{{ banner_map.content|safe }}</div>{% endif %}
    <div class="banner_map">
        {% if banner_map_back and banner_map_back.servingUrl %}
            <div class="background_wrapper">
                <img src="{{ banner_map_back.servingUrl }}=s1900" class="background">{% endif %}
            </div>
        {% for hotel in banner_map_hoteles %}
            <div class="hotel with_svg_logo {% if hotel.right %}hotel_right{% endif %} {% if hotel.bottom %}hotel_bottom{% endif %}" id="map_{{ hotel.id|safe }}"
                 style="{% if hotel.top %}top:{{ hotel.top|safe }};{% endif %}
                         {% if hotel.left %}left:{{ hotel.left|safe }};{% endif %}
                         {% if hotel.right %}right:{{ hotel.right|safe }};{% endif %}
                         {% if hotel.bottom %}bottom:{{ hotel.bottom|safe }};{% endif %}
                         {% if hotel.servingUrl %}background-image: url('{{ hotel.servingUrl|safe }}');{% endif %}">
                <div class="hotel_info">
                    <div class="hotel_info_pic">
                        {% if hotel.link %}
                            <a href="{{ hotel.link }}" {% if "http" in hotel.link  %}target="_blank" {% endif %}>
                                <i class="fal fa-info-circle"></i>
                            </a>
                        {% endif %}
                        {% if hotel.main and hotel.main.servingUrl %}
                            <img src="{{ hotel.main.servingUrl }}" {% if hotel.main.altText %}alt="{{ hotel.main.altText|safe }}"{% endif %}>
                        {% endif %}
                    </div>
                    <div class="hotel_info_name">{{ hotel.subtitle|safe }}</div>
                    <div class="hotel_info_desc">{{ hotel.description|safe }}</div>
                    <a href="#data" class="button_promotion btn_personalized_1" data-namespace="{{ hotel.id }}"><span>{{ T_reservar }}</span></a>
                </div>
            </div>
        {% endfor %}
    </div>
</div>
<div class="modal">
    <div class="modal_close"></div>
    <div class="modal_content"></div>
</div>
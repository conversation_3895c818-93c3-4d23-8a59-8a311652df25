.banner_map_wrapper {
    @include sec_pad;
    position: relative;
    top: 50px;
    
    &::before {
        position: absolute;
        content: '';
        height: 320px;
        background-color: #FFFBF2;
        top: 45px;
        left: 0;
        right: 0;
    }
    
    .container12 {
        .content_title {
            margin-bottom: 0;
        }
        
        .content_wrapper {
            position: relative;
            display: flex;
            flex-flow: row wrap;
            justify-content: space-between;
            
            .iframe_map_wrapper {
                position: relative;
                width: 100%;
                padding: 0;
                margin-bottom: 20px;
                &.active {
                    margin-top: 55px;
                    &::before {
                        display: none;
                    }
                }
                
                &::before {
                    position: absolute;
                    content: '';
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 55px;
                    background-color: #FFFBF2;
                }
                
                iframe {
                    height: 400px;
                }
                
                .map_widget_full_wrapper {
                    position: absolute;
                    left: 20px;
                    bottom: 20px;
                    
                    .map_info {
                        white-space: nowrap;
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        cursor: pointer;
                        opacity: 1;
                        transform: translateY(0);
                        transition: all .4s;
                        
                        .link_map {
                            overflow: visible;
                        }
                        
                        &.hide {
                            opacity: 0;
                            transform: translateX(-100%);
                        }
                    }
                    
                    .map_widget {
                        position: absolute;
                        bottom: 0;
                        left: 0;
                        opacity: 1;
                        transform: translateY(0);
                        transition: all .4s;
                        
                        .input_wrapper {
                            display: flex;
                            flex-direction: row;
                            
                            input {
                                height: 55px;
                                border: solid 2px $corporate_1;
                                min-width: 300px;
                                margin-right: 20px;
                                padding-left: 20px;
                                font-size: $font_md;
                                
                                &::placeholder {
                                    color: $corporate_1;
                                }
                            }
                            
                            .go, .close_map {
                                cursor: pointer;
                                margin-right: 20px;
                                height: 55px;
                                width: 55px;
                                background-color: $corporate_1;
                                position: relative;
                                
                                &::before {
                                    position: absolute;
                                    content: '';
                                    top: 0;
                                    bottom: 0;
                                    left: 0;
                                    right: 0;
                                    border: solid 1px $black;
                                    transform: translate(3px, 3px);
                                    transition: all .5s;
                                }
                                
                                i {
                                    color: white;
                                    font-size: 25px;
                                    @include center_xy;
                                }
                                
                                &:hover {
                                    background-color: lighten($corporate_1, 5%);
                                    
                                    &::before {
                                        border-color: lighten($corporate_1, 5%) !important;
                                    }
                                }
                            }
                            
                            .close_map {
                                background-color: #FF6000;
                            }
                        }
                    }
                    
                    .hide {
                        opacity: 0;
                        transform: translateY(-100%);
                    }
                }
            }
            
            .banner_item_info {
                width: calc(33% - 10px);
                padding: 20px;
                
                .desc {
                    position: relative;
                    padding-left: 70px;
                    
                    i {
                        position: absolute;
                        top: 0;
                        left: 0;
                        color: $corporate_1;
                        font-size: 35px;
                    }
                }
            }
        }
    }
}
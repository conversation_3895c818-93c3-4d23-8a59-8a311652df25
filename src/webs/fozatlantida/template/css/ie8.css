/* line 1, ../sass/ie8.scss */
.tp-simpleresponsive .caption, .tp-simpleresponsive .tp-caption {
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled = false) !important;
}

/* line 7, ../sass/ie8.scss */
.top_header #top-sections a {
  width: 195px;
  display: inline-block;
  margin-right: 0 !important;
}
/* line 14, ../sass/ie8.scss */
.top_header .contact_phone {
  width: 180px;
}

/* line 18, ../sass/ie8.scss */
#mainMenuDiv {
  margin-top: 10px;
}

/* line 21, ../sass/ie8.scss */
#mainMenuDiv ul li {
  padding-left: 15px;
  padding-right: 15px;
}

/* line 26, ../sass/ie8.scss */
.my_bookings_in_widget {
  background: black;
}

/* line 29, ../sass/ie8.scss */
.tp-bgimg,
.tp-bgimg img {
  width: 150% !important;
}

/* line 34, ../sass/ie8.scss */
.banner_element .square {
  font-size: 0px;
}
/* line 37, ../sass/ie8.scss */
.banner_element .square .discount {
  display: block;
  width: auto;
  white-space: nowrap;
}

/* line 45, ../sass/ie8.scss */
.bannersx3_wrapper .background_image {
  position: absolute;
  z-index: 1;
}

/* line 52, ../sass/ie8.scss */
.offers_wrapper .offer_description {
  display: none;
}

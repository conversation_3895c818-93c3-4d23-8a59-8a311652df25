<ul itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner" class="container">
    {% for section in main_sections %}
        <div class="main-section-div-wrapper {% if section.subsections %}has_subsection{% endif %}"
             {% if sectionToUse and sectionToUse.title == section.title %}id="section-active" {% endif %}>
            {% if section.subsections %}
                <a>{{ section.title|safe }}</a>
            {% else %}
                {% if section.title %}
                    <a itemprop="url" href="{% if section.replace_link %}{{ section.replace_link|safe }}{% else %}{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}{% endif %}">
                        <span itemprop="name">{{ section.title|safe }}</span>
                    </a>
                {% endif %}
            {% endif %}
            {% if section.subsections %}
                <ul>
                    {% for subsection in section.subsections %}
                        <li class="main-section-subsection {{ subsection.title|lower }}">
                            {% if subsection.title %}
                                <a href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrlInternational }}"
                                   {% if sectionToUse and sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                    {{ subsection.title|safe }}
                                </a>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
    {% endfor %}
</ul>
$(window).load(function () {
    $("img[lazy=true]").unveil();
});

$(function () {

    $(".myFancyPopup").fancybox({
        maxWidth: 800,
        maxHeight: 600,
        fitToView: false,
        width: '70%',
        height: '70%',
        autoSize: false,
        aspectRatio: false,
        closeClick: false,
        openEffect: 'none',
        closeEffect: 'none'
    });


    $(".myFancyPopupAuto").fancybox({
        width: 650,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });
    $(".myFancyPopupVideo").fancybox({
        width: 640,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });

    $(".button-promotion, .button_promotion").fancybox({
        width: 800,
        beforeLoad: function () {
            $(".room-selector").val("1");
            $(".datepicker1").val("");
            $(".datepicker2").val("");
            $(".hab2").hide();
            $(".hab3").hide();
        },
        wrapCSS: "booking-fancy"
    });


    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');


    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }


    if (typeof(TAPixel) !== "undefined") {
        TAPixel.impressionWithReferer("001F000000vA4u0");
    }

    $.simpleWeather({
        location: 'Benidorm',
        woeid: '',
        unit: 'c',
        success: function (weather) {
            var html = '<div class="number"><span class="img_weather"><img class"weather-icon" src="/static_1/images/weather/1/' + weather.code +
                '.png"></span><span class="grados">' +
                weather.temp + '&deg;' + '</span></div>';
            $(".weather").html(html);
        },
        error: function (error) {
            $("#weather").html('<p>' + error + '</p>');
        }
    });

    $('#lang').click(function () {
        $('#language-selector-options').slideToggle('fast');
    });

    /*    $(".hidden_booking .boking_widget_inline #fecha_entrada").click(function(){
            $("#ui-datepicker-div").css({
                'top':'40px'
            });
        });
        $(".hidden_booking .boking_widget_inline #fecha_slida").click(function(){
            $("#ui-datepicker-div").css({
                'top':'40px'
            });
        });*/

    $(document).ready(function () {
        $('.bxslider').bxSlider({
            slideWidth: 285,
            minSlides: 4,
            maxSlides: 4,
            slideMargin: 0
        });
    });


    $(window).scroll(function () {
        repositionBooking();
    });

    repositionBooking();

    max_height_block = 0;
    $(".full-banners-wrappers .banner-block").each(function () {
        var actual_height = $(this).height();
        if (actual_height > max_height_block) {

            max_height_block = actual_height;
        }
    });
    $(".full-banners-wrappers .banner-block").height(max_height_block);
});

function repositionBooking() {

    slider_height = $("#slider_container").height();
    slider_inner_height = $("#slider_inner_container").height();
    actual_height = $(window).scrollTop();
    var bottom_position = "";

    if ($("body").hasClass("madeira-centro")) {
        bottom_position = "0";
    } else {
        if (slider_height) {
            bottom_position = "60px";
        } else {
            bottom_position = "100px";
        }
    }

    if (slider_height) {
        if (actual_height > slider_height) {
            $("#full_wrapper_booking").css({
                'position': 'fixed',
                'top': '0px',
                'bottom': 'auto'
            });

        }
        else {
            $("#full_wrapper_booking").css({
                'position': 'absolute',
                'bottom': bottom_position,
                'top': 'auto'
            });
        }
    }
    else {
        if (actual_height > slider_inner_height) {
            $("#full_wrapper_booking").css({
                'position': 'fixed',
                'top': '0px',
                'bottom': 'initial'
            });
        }
        else {
            $("#full_wrapper_booking").css({
                'position': 'absolute',
                'bottom': bottom_position,
                'top': 'initial'
            });
        }
    }
}

(function () {
    var po = document.createElement('script');
    po.type = 'text/javascript';
    po.async = true;
    po.src = 'https://apis.google.com/js/plusone.js';
    var s = document.getElementsByTagName('script')[0];
    s.parentNode.insertBefore(po, s);
})();


function showGallery2(elements) {
    $.fancybox(elements, {
        'prevEffect': 'none',
        'nextEffect': 'none',
        'type': 'image',
        'arrows': true,
        'nextClick': true,
        'mouseWheel': true,
        'helpers': {
            title: {
                type: 'outside'
            },
            overlay: {
                opacity: 0.8,
                css: {
                    'background-color': '#000'
                }
            },
            thumbs: {
                width: 50,
                height: 50
            }
        }
    });
}


/* We call you form */
$("form.we_call_you").validate({
    rules: {
        telephone: {
            required: function (element) {
                return !$("#telephone").val() > 0;
            }
        },
        comments: "required"
    }, highlight: function (element) {
        $(element).addClass('input-error');
    }, unhighlight: function (element) {
        $(element).removeClass('input-error');
    }

});

var param = decodeURIComponent(window.location.search).substring(1);
var newParam = param.split('=');
if (newParam && newParam.length && newParam[0] == "promocode") {
    var promoCode = newParam[1];
    if (promoCode) {
        $("#promocode").val(promoCode);
    }
}

function prepare_countdowns(days_label, hours_label, minutes_label, seconds_label) {
    $(".slider_countdown").each(function () {
        var countdownDate = $(this).attr("date"),
            countdownWrapper = $(this);

        var html_countdown_template =
            "<div class='days'><div class='date'></div><div class='title_format'>" + days_label + "</div></div>" +
            "<div class='hours'><div class='date'></div><div class='title_format'>" + hours_label + "</div></div>" +
            "<div class='minutes'><div class='date'></div><div class='title_format'>" + minutes_label + "</div></div>" +
            "<div class='seconds'><div class='date'></div><div class='title_format'>" + seconds_label + "</div></div>";

        if (countdownDate) {
            var countdownDate = new Date(countdownDate).getTime();
            countdownWrapper.html(html_countdown_template);
            var interval = setInterval(function () {
                updateCounter(countdownDate, countdownWrapper);
            }, 1000);
        }
    });
}

function updateCounter(countdownDate, countdownWrapper) {
    var now = new Date().getTime(),
        differenceDates = countdownDate - now;

    if (differenceDates > 0) {
        var days = Math.floor(differenceDates / (1000 * 60 * 60 * 24)),
            hours = Math.floor((differenceDates % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
            minutes = Math.floor((differenceDates % (1000 * 60 * 60)) / (1000 * 60)),
            seconds = Math.floor((differenceDates % (1000 * 60)) / 1000);

        countdownWrapper.find(".days .date").html(days);
        countdownWrapper.find(".hours .date").html(hours);
        countdownWrapper.find(".minutes .date").html(minutes);
        countdownWrapper.find(".seconds .date").html(seconds);
    } else {
        countdownWrapper.html();
    }
}

$(window).scroll(showMenu);

function showMenu() {
    actual_position = $(window).scrollTop();
    slider_height = $("#slider_container").height() - 600;

    if (actual_position > slider_height) {
        $(".floating_buttons").removeClass('hide');
    }
    if (actual_position < slider_height) {
        $(".floating_buttons").addClass('hide');
    }
}
.faldon_footer_wrapper {
  position: fixed;
  left: 0;
  bottom: 0;
  width: 100%;
  z-index: 1000;
  height: 275px;
  display: block;
  box-shadow: 1px 1px 5px 2px black;
  .close_button_faldon {
    position: absolute;
    top: 20px;
    right: 20px;
    display: inline-block;
    z-index: 1000;
    color: white;
    cursor: pointer;
    @include transition(transform, .4s);

    &:hover {
      -webkit-transform: rotate(180deg);
      -moz-transform: rotate(180deg);
      -ms-transform: rotate(180deg);
      -o-transform: rotate(180deg);
      transform: rotate(180deg);
    }
  }

  &:before {
    content: "";
    @include full_size;
    background: rgba(black, .5);
    z-index: 1;
  }

  &:after {
    @include full_size;
    content: "";
    margin: 10px;
    z-index: 2;
    border: 2px solid white;
  }

  .faldon_content, .faldon_content_thanks {
    position: relative;
    height: 100%;
    z-index: 5;

    .center_block {
      @include center_y;
      width: 100%;
      text-align: center;
      z-index: 3;

      .faldon_title {
        font-weight: bolder;
        color: $corporate_1;
        font-size: 28px;
      }

      .faldon_description {
        font-size: 13px;
        color: white;
        line-height: 23px;
        width: 750px;
        margin: 10px auto 0;
      }

      .faldon_link {
        display: inline-block;
        text-transform: uppercase;
        padding: 7px 15px;
        background: #f19317;
        color: white;
        text-decoration: none;
        margin-top: 20px;
        cursor: pointer;
        @include transition(background, .4s);

        &:hover {
          background: darken($corporate_1, 10%);
        }
      }

      .faldon_newsletter {
        margin-top: 10px;
        display: inline-block;
        width: 100%;

        .faldon_send{
          background: $corporate_1;
        }

        input#faldon_email {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          border: 0;
          height: 32px;
          width: 200px;
          box-sizing: border-box;
          padding-left: 10px;
          background: white;
          color: #A9A8A8;

          &::-webkit-input-placeholder {
            /* Chrome/Opera/Safari */
            color: #A9A8A8;
          }
          &::-moz-placeholder {
            /* Firefox 19+ */
            color: #A9A8A8;
          }
          &:-ms-input-placeholder {
            /* IE 10+ */
            color: #A9A8A8;
          }
          &:-moz-placeholder {
            /* Firefox 18- */
            color: #A9A8A8;
          }
        }

        button {
          height: 32px;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          border: 0;
          background: #f19317;
          color: white;
          cursor: pointer;
          text-transform: uppercase;
          padding: 0 10px;
          @include transition(background, .4s);

          &:hover {
            background: darken(#f19317, 10%);
          }
        }

        .check_faldon {
          margin-top: 10px;

          .check_privacy {
            display: inline-block;
            vertical-align: middle;

            &.error + .newsletter_popup, &.error + .label_promotions {
              color: #ff6b6a !important;
            }
          }

          .newsletter_popup, label {
            display: inline-block;
            vertical-align: middle;
            color: white;
            font-size: 12px;
          }
        }

        .faldon_checkbox {
          color: white;
          font-size: 12px;
        }
      }
    }
  }
}

.promocode_highlight {
  .promocode_input {
    color: $corporate_1;
  }
}
/* line 1, ../sass/ie8.scss */
.tp-simpleresponsive .caption, .tp-simpleresponsive .tp-caption {
  filter: progid:DXImageTransform.Microsoft.Alpha(enabled = false) !important;
}

/* line 5, ../sass/ie8.scss */
#top-sections {
  width: 500px;
}
/* line 7, ../sass/ie8.scss */
#top-sections a {
  width: 130px;
  display: inline-block;
}
/* line 12, ../sass/ie8.scss */
#top-sections .second_top_link {
  width: 200px;
}

/* line 17, ../sass/ie8.scss */
#lang {
  width: 102px;
}

/* line 21, ../sass/ie8.scss */
#social {
  width: 130px;
}

/* line 26, ../sass/ie8.scss */
.ticks_wrapper .ticks {
  width: 220px;
}

/* line 32, ../sass/ie8.scss */
.bannersx3_wrapper .bannerx3_title {
  font-size: 14px !important;
  width: 350px;
  background: black;
}

/* line 40, ../sass/ie8.scss */
.bannersx2_wrapper .bannerx2_title {
  font-size: 14px !important;
  width: 400px;
  background: black;
}

/* line 48, ../sass/ie8.scss */
.banner_center_container .bannerx2_title {
  margin-top: 50px;
}

/* line 54, ../sass/ie8.scss */
.offers_wrapper .offer_description {
  display: none;
}

/* ----- FOR DEVELOPMENT ONLY ----- */

body{
  line-height: 24px;
  font-family: 'Rokkitt', arial;
  color: $corporate-1;
}
strong{
  font-weight: bold;
}
#ui-datepicker-div{
  font-size: 12px;
  z-index: 1200;
}
a{
  text-decoration: none;
}


/************* header *************/

header{
  border-bottom: 4px solid $corporate-2;
  background-color: $corporate-1;
  height: 140px;
}
#wrapper_header{
  position: relative;
  height: 140px;
}
#logoDiv{
  margin: 10px 0 0;
  width: 220px;
  text-align: center;
}
#ticks_container{
  float: left;
  font-size: 12px;
  text-transform: uppercase;
  margin: 10px 0 5px;
  color: $white;

  .ticks_top{
    float: left;
    width: 105px;
    padding-left: 45px;
    text-align: left;
    line-height: 16px;
  }
  #tick1{
    background: url('/img/velad/pago_icon.png') no-repeat 0;
  }
  #tick2{
    background: url('/img/velad/gastos_icon.png') no-repeat 0;
  }
  #tick3{
    background: url('/img/velad/segura_icon.png') no-repeat 0;
  }
}
#languageDiv{
  float: right;
  font-size: 15px;
  margin: 5px 10px;
  text-transform: uppercase;
}
#languageDiv a{
  margin: 0 5px;

  .selected{
    color: $corporate-2;
  }
}
#topMenuDiv{
  float: right;
  font-size: 15px;
  margin: 5px 0 5px 10px;
}
#topMenuDiv a, #languageDiv a{
  text-decoration: none;
  color: $white;
}
#social{
  clear: right;
  float: right;
  padding-top: 5px;
}
#social a{
  text-decoration: none;
  color: $white;
}
#main_menu{
  height: 140px;
  width: 880px;
}
#mainMenuDiv{
  font-size: 17px;
  z-index: 99;
  position: relative;
  top: 25px;
  clear: both;
}
#mainMenuDiv a{
  padding: 5px 18px 15px;
  text-decoration: none;
  text-transform: uppercase;
  color: $white;
  display: inline-block;
}
#mainMenuDiv a:hover{
  background: $corporate-2;
  color: $white;
}
#section-active a{
  padding: 5px 18px 15px;
  text-decoration: none;
  text-transform: uppercase;
  background: $corporate-2;
  color: $white;
  display: inline-block;
}
#main-sections-inner ul{
  display: none;
}
#main-sections-inner div:hover > ul{
  display: block;
}
#main-sections-inner div ul{
  position: absolute;
}
#main-sections-inner li ul{
  position: absolute;
}
#main-sections-inner div li{
  float: none;
  display: block;
}
#main-sections-inner{
  text-align:justify;
}
#main-sections-inner:after{
  content:' ';
  display:inline-block;
  width: 100%;
  height: 0;
}
#main-sections-inner > div{
  display: inline-block
}


/************* slider y banner *************/

#slider_container{
  position: relative;
}
#booking{
  width: 220px;
  position: absolute;
  top: 35px;
  z-index: 500;
}


/************* contenido *************/

#content{

  h3{
    font-size: 26px;
    padding-bottom: 30px;
    font-weight: bold;
  }
  h4{
    font-size: 18px;
    padding: 25px 0 20px;
    font-weight: bold;
  }
}
.background_content{
  background: transparent url("/img/velad/fondo_contenido.png");
}
#main_text{
  clear: left;

  li{
    list-style: disc;
    margin-left: 40px;
  }
}
.content-page-wrapper{
  padding: 30px 0;
  font-size: 19px;
}
.main_title{
  font-size: 26px;
  padding-bottom: 30px;
  font-weight: bold;
}


/************* Banners *************/

#banners_container{
  padding-top: 30px;
  padding-bottom: 30px;
}
.main_banners{
  position: relative;
  overflow: hidden;
  border-radius: 20px 20px 0 0;
  margin-bottom: 20px;

  img{
    width: 360px;
  }
  .main_banner_title{
    color: $corporate-1;
    font-size: 18px;
    text-transform: uppercase;
  }
  .main_banner_description{
    color: $white;
    background-color: $corporate-1;
    font-size: 18px;
    line-height: 18px;
    text-align: center;
    text-transform: uppercase;
    padding: 5px 10px;
    position: absolute;
    width: 340px;
    height: 40px;
    bottom: 0;
    opacity: 0;
    -webkit-transform: translateY(40px);
    transform: translateY(40px);
    -webkit-transition: opacity 1s, -webkit-transform 1s;
    transition: opacity 1s, transform 1s;
  }
}
.main_banners:hover .main_banner_description{
  -webkit-transform: translateY(0);
  transform: translateY(0);
  opacity: 0.9;
}

/************* footer *************/

footer{
  padding: 10px 0;
  background-color: $corporate-1;
  color: $white;
}
.footer_column{

  .footer_column_title{
    margin: 0 0 5px;
    color: $white;
    font-size: 18px;
    text-transform: uppercase;
  }
  .footer_column_description{
    font-size: 18px;

    a{
      color: $white;
    }
  }
}
#suscEmail{
  width: 222px !important;
  height: 20px;
}
#newsletter h2{
  margin: 0 0 5px;
  color: $white;
  font-size: 18px;
  text-transform: uppercase;
}
#newsletter label{
  font-size: 18px;
  color: white;
}
#newsletter input{
  margin: 15px 0;
}
#newsletter #form-newsletter{
  margin: 0;
}
#newsletter .bordeInput{
  border: 1px solid darkgray !important;
  width: 150px;
}
#newsletter button{
  width: 150px;
  margin: 0 auto;
  background: $corporate-2;
  color: $white;
  border: none;
  text-align: center;
  font-size: 18px;
  border-radius: 5px;
  padding: 7px 3px;
  cursor: pointer;
  font-family: 'Rokkitt', arial;
}
#newsletter button:hover{
  background: $gray-2;
}
#footer{
  color: $white;
  margin-top: 20px;
  text-align: center;
  line-height: 12px;

  a{
    text-decoration: none;
    color: $white;
  }
  #footer_bottom_text{
    font-size: 12px;
  }
}

/***************** seccion paquetes *****************/

.fichas_container{
  margin-top: 20px;
  margin-bottom: 20px;
}
.wrapper_ficha{
  background: rgb(250,250,250);
  border: 1px solid rgb(220,220,220);
  max-height: 122px;
  margin-top: 15px;
  margin-bottom: 15px;

  .ficha{
     text-align: left;

    img{
      float: left;
      margin-right: 25px;
      width: 250px;
      height: 120px;
    }
    .ficha_title{
      font-size: 20px;
      padding: 10px 0;
    }
    .ficha_description{
      font-size: 14px;
      padding-bottom: 15px;
    }
  }
}
#main_description{
  line-height: 18px;
  float: left;
  width: 640px;
}
#price{
  position: relative;
  top: -20px;
  left: 14px;
  height: 0;
}
#font_style_price{
  font-size: 16px;
}
#packages #wrapper_content,
#paquetes #wrapper_content{
  border-top: none !important;
  padding-top: 25px;
}
#info_button{
  font-size: 12px;
  width: 80px;
  margin: -10px 0px 0px 50px;
}
.paquetes_popup {
  padding: 30px;
  background: $corporate-2;
  color: $white;
}


/***************** otros retoques *****************/

button{
  width: 120px;
  border: none;
  border-radius: 5px;
  font-weight: normal;
  padding: 6px 10px;
  background-color: $corporate-1;
  color: $white;
  font-size: 26px;
  cursor: pointer;
  overflow: visible;
}
button:hover{
  background-color: $corporate-2;
}
.contVerDetalles a{
  color: $corporate-2;
}
#workWithUs{
  display: none;
}
#my-bookings-form .bordeInput, #contactContent .bordeInput{
  width: 250px;
  margin: 5px 0 15px;
  padding: 3px 0;
  display: block;
}
#cancelButton{
  display:none;
}
#my-bookings-form-fields p{
  margin-bottom: 15px;
  font-size: 19px;
}
#my-bookings-form{
  margin-top: 25px;
}
.promotions-wrap{
  padding: 0 30px;
}
ul.gallery li{
  list-style: none !important;
  margin: 10px 6px !important;
  width: 125px !important;
  height: 95px !important;
  border: solid 1px #cccccc;
  background-color: #ffffff;
  padding: 3px;
}
ul.gallery li img{
  margin: 0 !important;
  padding: 0 !important;
  width: 100%;
  height: 100%;
  background: none !important;
  border: none !important;
}
#habitaciones #content h3,
#rooms #content h3{
  text-align: left;
  padding: 30px 0 10px;
}
#habitaciones #servicios_habitacion,
#rooms #servicios_habitacion,
#apartamentos #servicios_habitacion,
#apartments #servicios_habitacion{
  display: none;
}
#link_ver_mas{
  display: none;
}
#rooms_description{
  min-height: 130px;
  margin-left: 280px;
}
.contFotoRooms button{
  font-size: 16px;
}
.dropdown_menu_options{
  margin-top: 10px;
}
.dropdown_menu_options td{
  vertical-align: middle;
  padding: 0 10px 5px 0;
}
#contact input, #contact textarea{
  border: 1px solid #787878 !important;
}
#contact #contact-button-wrapper{
  float: left !important;
  width: auto;
}
#contactContent h1{
  color: $corporate-1 !important;
}
#itemDescription{
  width: 420px;
}
#google_plus_one{
  text-align: center;
}
#___plusone_0{
  margin-top: 10px !important;
  width: 64px !important;
}
#facebook_like{
  text-align: center;
  margin-top: 10px;
}
#facebook_like iframe{
  height: 21px;
  width: 103px;
}
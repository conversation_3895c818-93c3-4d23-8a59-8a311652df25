<section class="cycle_banners_wrapper">
    <div class="cycle_banners_content container12">
        {% if offers_home %}
            <article class="cycle_element">
                <ul class="slides">
                    {% for x in offers_home %}
                        <li class="offer_slide">
                            <div class="cycle_image"><img data-src="{{ x.picture }}=s800" lazy="true"/></div>
                            <div class="cycle_content">
                                <div class="center_block">
                                    <div class="cycle_title">{{ x.name|safe }}</div>
                                    <div class="cycle_description">{{ x.description|safe }}</div>
                                    <div class="links_block">
                                        <div class="booking_button">
                                            <a class="button-promotion" href="#data">{{ T_reservar }}</a>
                                        </div>
                                        {% if x.see_more %}
                                            <div class="see_more">
                                                <a href="{{ x.see_more|safe }}">
                                                    <i class="fa fa-plus" aria-hidden="true"></i>
                                                </a>
                                            </div>
                                        {% else %}
                                            {% if "<hide>" in x.description %}
                                                <div class="see_more">
                                                    <a href="#hide_offer_{{ forloop.parentloop.counter }}_{{ forloop.counter }}"
                                                       class="hide_popup">
                                                        <i class="fa fa-plus" aria-hidden="true"></i>
                                                    </a>
                                                </div>

                                                <div id="hide_offer_{{ forloop.parentloop.counter }}_{{ forloop.counter }}"
                                                     style="display: none">
                                                    <div class="cycle_title">{{ x.title|safe }}</div>
                                                    <div class="cycle_description">{{ x.description|safe }}</div>
                                                </div>
                                            {% endif %}
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </li>
                    {% endfor %}
                </ul>

                <script async>
                    $(function () {
                        slider_params = {
                            controlNav: true,
                            directionNav: false,
                            animation: "slide",
                            slideshowSpeed: 14000,
                            animationSpeed: 1500
                        };
                        $(".cycle_element").flexslider(slider_params);
                    })
                </script>
            </article>
        {% endif %}
        {% if cycle_banners %}
            {% for x in cycle_banners %}
                <article class="cycle_element">
                    {% if x.flexslider %}
                        <ul class="slides">
                            {% for y in x.flexslider %}
                                <li class="cycle_slide">
                                    <div class="cycle_image"><img data-src="{{ y.servingUrl }}=s800" lazy="true"/></div>
                                    <div class="cycle_content">
                                        <div class="center_block">
                                            <div class="cycle_title">{{ y.title|safe }}</div>
                                            <div class="cycle_description">{{ y.description|safe }}</div>
                                            <div class="links_block">
                                                {% if y.booking %}
                                                    <div class="booking_button">
                                                        <a class="button-promotion" href="#data">{{ T_reservar }}</a>
                                                    </div>
                                                {% endif %}
                                                {% if y.linkUrl %}
                                                    <div class="see_more">
                                                        <a href="{{ y.linkUrl|safe }}">
                                                            <i class="fa fa-plus" aria-hidden="true"></i>
                                                        </a>
                                                    </div>
                                                {% else %}
                                                    {% if "<hide>" in y.description %}
                                                        <div class="see_more">
                                                            <a href="#hide_cycle_{{ forloop.parentloop.counter }}_{{ forloop.counter }}"
                                                               class="hide_popup">
                                                                <i class="fa fa-plus" aria-hidden="true"></i>
                                                            </a>
                                                        </div>

                                                        <div id="hide_cycle_{{ forloop.parentloop.counter }}_{{ forloop.counter }}"
                                                             style="display: none">
                                                            <div class="cycle_title">{{ y.title|safe }}</div>
                                                            <div class="cycle_description">{{ y.description|safe }}</div>
                                                        </div>
                                                    {% endif %}
                                                {% endif %}
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            {% endfor %}
                        </ul>

                        <script async>
                            $(function () {
                                slider_params = {
                                    controlNav: true,
                                    directionNav: false,
                                    animation: "slide",
                                    slideshowSpeed: 14000,
                                    animationSpeed: 1500
                                };
                                $(".cycle_element").flexslider(slider_params);
                            })
                        </script>
                    {% else %}
                        <div class="cycle_image"><img data-src="{{ x.servingUrl }}=s800" lazy="true"/></div>
                        <div class="cycle_content">
                            <div class="center_block">
                                <div class="cycle_title">{{ x.title|safe }}</div>
                                <div class="cycle_description">{{ x.description|safe }}</div>
                                <div class="links_block">
                                    {% if x.booking %}
                                        <div class="booking_button">
                                            <a class="button-promotion" href="#data">{{ T_reservar }}</a>
                                        </div>
                                    {% endif %}
                                    {% if x.linkUrl %}
                                        <div class="see_more">
                                            <a href="{{ x.linkUrl|safe }}">
                                                <i class="fa fa-plus" aria-hidden="true"></i>
                                            </a>
                                        </div>
                                    {% else %}
                                        {% if "<hide>" in x.description %}
                                            <div class="see_more">
                                                <a href="#hide_cycle_{{ forloop.counter }}"
                                                   class="hide_popup">
                                                    <i class="fa fa-plus" aria-hidden="true"></i>
                                                </a>
                                            </div>

                                            <div id="hide_cycle_{{ forloop.counter }}"
                                                 style="display: none">
                                                <div class="cycle_title">{{ x.title|safe }}</div>
                                                <div class="cycle_description">{{ x.description|safe }}</div>
                                            </div>
                                        {% endif %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    {% endif %}
                </article>
            {% endfor %}
        {% endif %}
    </div>
</section>
<div id="main-sections">
	<ul id="main-sections-inner" class="container" itemscope itemtype="//schema.org/SiteNavigationElement">
		{% for section in main_sections %}
		<div class="main-section-div-wrapper" {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
			{% if section.subsections %}

					<a>{{ section.title|safe }}</a>
            {% else %}
                <a itemprop="url" {% if section.replace_link %}href="{{ section.replace_link|safe }}" target="_blank"
                   {% else %}href="{{host|safe}}/{{seoLinkString}}{{section.friendlyUrl}}"{% endif %}>
                    <span itemprop="name">{{ section.title|safe }}</span>
                </a>
            {% endif %}

            {% if section.subsections %}
            <ul>


                {% for subsection in section.subsections %}
                    <li class="main-section-subsection {{ subsection.title|lower }}">
                        <a itemprop="url" href="{{host|safe}}/{{seoLinkString}}{{subsection.friendlyUrl}}" {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                        <span itemprop="name">{{ subsection.title|safe}}</span>
                        </a>
                    </li>
                {% endfor %}
            </ul>
            {% endif %}


		</div>
		{% endfor %}
	</ul>
</div>
//Remove forbidden names subsections
$(".main-section-subsection.hotel").removeClass('hotel');

$(window).load(function () {


    news_height();

    $(".myFancyPopup").fancybox({
                                    maxWidth: 800,
                                    maxHeight: 600,
                                    fitToView: false,
                                    width: '70%',
                                    height: '70%',
                                    autoSize: false,
                                    aspectRatio: false,
                                    closeClick: false,
                                    openEffect: 'none',
                                    closeEffect: 'none'
                                });


    $(".myFancyPopupAuto").fancybox({
                                        width: 650,
                                        height: 'auto',
                                        fitToView: false,
                                        autoSize: false
                                    });
    $(".myFancyPopupVideo").fancybox({
                                         width: 640,
                                         height: 'auto',
                                         fitToView: false,
                                         autoSize: false
                                     });
    $(".popup_full_auto").fancybox({
        width: 'auto',
        height: 'auto',
        fitToView: false,
        autoSize: false,
        padding: 0
    });

    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');

    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }

    if (typeof(TAPixel) !== "undefined") {
        TAPixel.impressionWithReferer("001F000000vA4u0");
    }

    $("img[lazy=true]").unveil();

    effects_sass();

    $(".button-promotion, .button_promotion").unbind('click');
    only_execute_once = false;
    $(".button-promotion, .button_promotion").click(function () {
        prepare_booking_popup2();
        open_booking_full2($(this));

        var smartkids = 0;

        if ($(this).attr('data-smartkids')) {
            smartkids = $(this).attr('data-smartkids');
        }

        $("#data select.selector_ninos#hab1_ninos").val(smartkids);

        if (!only_execute_once) {
            only_execute_once = true;
            $("#data select.selector_ninos").selectric("refresh");
        }
    });

    $(".selector_hoteles").click(function(){
        _open_selection_destiny();
    });

    $(".location_select_wrapper .black_overlay, .location_select_wrapper .close_button_image").click(function(){
       _close_selection_destiny();
    });

    $(".destiny_selector_element").each(function(){
        var number_hotels = $(this).attr('hotels_number');
        $(this).unbind("click");
        if(number_hotels == "1"){
            $(this).bind('click', function(){
                 _auto_select_hotel_destiny($(this));
            })
        }else{
            $(this).bind('click', function(){
                 _open_target_destiny_selector($(this));
            })
        }
    });

    $(".hotels_selection .return_button").click(function(){
        _return_destiny_selection($(this));
    });

    $(".search_hotels_selector").keydown(function () {
        searchHotelElement($(this));
    }).focus(function () {
        $(".all_hotels_list_search").slideDown();
    }).blur(function () {
        setTimeout(function(){
             $(".all_hotels_list_search").slideUp();
        }, 300);
    });


    $(".hotel_element_available, .hotel_element_search").click(function(){
        _hotel_selection($(this));
    });

    $(".see_all_hotels, .booking_0_hotel_selection").click(function(){
        _set_all_hotels_search($(this));
    });

    $(".menu_toggle").click(function () {
        $(this).toggleClass("opened");
        $("nav#main_menu").toggleClass("opened");
        $("header").toggleClass("opened");
    });

    $(".top_toggle").click(function () {
        $(this).find("ul").toggle();
    });

    $(".lang_selected").click(function () {
        $(".lang_options_wrapper").slideToggle();
    });

    $(".open_modal").click(function (e) {
        e.preventDefault();
        $($(this).attr("href")).slideToggle();
    });

    $(".modal_popup i.fa-times").click(function (e) {
        e.preventDefault();
        $(this).parent().slideToggle();
    });

    $(".open_popup_phone, .close_popup_phone").click(function (e) {
        $(".popup_phone").fadeToggle();
    });
});

$(window).scroll(showMenu);

var deployed_menu = false;

function showMenu() {
    actual_position = $(window).scrollTop();
    slider_height = $("#slider_container").height() - 200;
    menu_showed = $("#full_wrapper_booking").hasClass('showed');

    if ((actual_position > slider_height) && (!menu_showed)) {
        $("#full_wrapper_booking").addClass('floating_booking').addClass('showed');
    }
    if ((actual_position < slider_height) && (menu_showed)) {
        $("#full_wrapper_booking").removeClass("floating_booking").removeClass('showed');
    }

    if (actual_position > 100 && !deployed_menu) {
        $("header").addClass("scrolled");
        deployed_menu = true;
    } else if (actual_position < 100 && deployed_menu) {
        $("header").removeClass("scrolled");
        deployed_menu = false;
    }
}


function searchHotelElement(){
    var searched_hotel = $(".search_hotels_selector").val();

    $(".hotel_element_search").each(function(){
        var actual_html = $(this).html();
        actual_html = actual_html.toLowerCase();
        searched_hotel = searched_hotel.toLowerCase();


        if(actual_html.indexOf(searched_hotel) < 0){
            $(this).css('display', 'none');
        }else{
            $(this).css('display', 'block');
        }

        if(searched_hotel == ""){
            $(this).css('display', 'block')
        }
    })
}

function getUrlParameter(sParam) {
    var sPageURL = decodeURIComponent(window.location.search.substring(1)),
        sURLVariables = sPageURL.split('&'),
        sParameterName,
        i;

    for (i = 0; i < sURLVariables.length; i++) {
        sParameterName = sURLVariables[i].split('=');

        if (sParameterName[0] === sParam) {
            return sParameterName[1] === undefined ? true : sParameterName[1];
        }
    }
}

function booking_click(namespace) {

    prepare_booking_popup2();
    open_booking_full2();

    if (!only_execute_once) {
        only_execute_once = true;
    }
}


function _open_selection_destiny(){
    $(".location_select_wrapper").css('opacity', '0').show();
    $(".location_select_wrapper").animate({'opacity': '1'}, 500);
}

function _return_destiny_selection(clicked_element){
    var parent_hotels = clicked_element.closest(".hotels_selection");
    parent_hotels.animate({'opacity': '0'}, 500, function(){
        parent_hotels.hide();
        $(".location_selector").css('opacity', '0').show();
        $(".location_selector").animate({'opacity': '1'}, 500);
    })
}

function _close_selection_destiny(){
     $(".location_select_wrapper").animate({'opacity': '0'}, 500, function () {
         $(".location_select_wrapper").hide();
     });
}

function _open_target_destiny_selector(clicked_element){
    var open_target = clicked_element.attr('target');
    $(".location_selector").animate({'opacity': '0'}, 500, function(){
        $("." + open_target).css({'opacity': '0'});
        $("." + open_target).show().animate({'opacity': '1'});
    })
}


function searchHotelElement(input_element){
    var searched_hotel = input_element.val();

    $(".hotel_element_search").each(function(){
        var actual_html = $(this).html();
        actual_html = actual_html.toLowerCase();
        searched_hotel = searched_hotel.toLowerCase();


        if(actual_html.indexOf(searched_hotel) < 0){
            $(this).css('display', 'none');
        }else{
            $(this).css('display', 'block');
        }

        if(searched_hotel == ""){
            $(this).css('display', 'block')
        }
    })
}


function _hotel_selection(hotel_selection){
    var hotel_namespace = hotel_selection.attr('namespace'),
        hotel_url = hotel_selection.attr('hotel_url');

    $(".paraty-booking-form").each(function(){
        $(this).attr('action', hotel_url);
        $(this).find("#namespace").val(hotel_namespace);
        $(this).find(".destination").attr('placeholder', hotel_selection.text());
        $(this).find(".destination").val(hotel_selection.attr('hotel_name'));
    });

    _close_selection_destiny();
}


function _set_all_hotels_search(clicked_element){
    var all_namespaces = clicked_element.attr('namespaces'),
        hotel_name = clicked_element.attr('hotel_name');
    $(".paraty-booking-form").each(function(){
        $(this).attr('action', 'https://checkin-corporativa-dot-checkin-hoteles.appspot.com/booking0');
        $(this).find(".destination").val(hotel_name);
        if (!$(this).find("input[name='applicationIds']").length){
            $("<input type='hidden' id='applicationIds' name='applicationIds' value=''>").appendTo($(this));
        }else{
            $(this).find("input[name='applicationIds']").val(all_namespaces);
        }
        $(this).find("#applicationIds").val(all_namespaces)
    });
    _close_selection_destiny();
}


function _auto_select_hotel_destiny(clicked_element){
    var target_destiny_popup = clicked_element.attr('target');
    $("." + target_destiny_popup).find(".hotel_element_available").trigger('click');
}

function open_booking_full2(selector) {
    $.fancybox.open($('#data'),{
        autosize: false,
        padding: 0,
        margin: 0,
        wrapCSS: "booking-data-popup",
        afterShow: function () {
            updateDates2($.datepicker.formatDate("dd/mm/yy", new Date()));
            $("#data select.selector_adultos").selectric('refresh');
            $("#data select.selector_ninos").selectric('refresh');
            $("#data select.selector_bebes").selectric('refresh');
            $("body").addClass('booking-popup-opened');
            smartDatas(selector);
        },
        openOpacity: true,
        closeOpacity: true,
        openSpeed: 'slow',
        closeSpeed: 'slow',
        afterClose: function(){
            $("#ui-datepicker-div").addClass("fixed_datepicker_element");
            $("body").removeClass('booking-popup-opened');
            $("#data input[name=startDate]").datepicker("hide");
            $("#data input[name=endDate]").datepicker("hide");
        }
    });
}
function prepare_booking_popup2() {
    $("#data").css({
        width: $(window).width(),
        height: $(window).height()
    });

    $("#data #selector_habitaciones option").each(function () {
        $(this).html($(this).val())
    });

    $("#data #selector_habitaciones").selectric({
        onChange: function (element) {
            popup_rooms_change($(element).val());
        }
    });

    $("#data select.selector_adultos").selectric();
    $("#data select.selector_ninos").selectric();
    $("#data select.selector_bebes").selectric();

    $("#data #hab2, #data #hab3").addClass("disabled").append("<div class='disabled_overlay'></div>");

    updateDates2($.datepicker.formatDate("dd/mm/yy", new Date()));
}

function _update_capacity_rooms_adults() {
    $(".paraty-booking-form").each(function () {
        var rooms_number = parseInt($(this).find("select.rooms_number").val()),
            adults_room_1 = $(this).find('select.adults_room_1').val(),
            children_room_1 = $(this).find('select.children_room_1').val(),
            adults_room_2 = $(this).find('select.adults_room_2').val(),
            children_room_2 = $(this).find('select.children_room_2').val(),
            adults_room_3 = $(this).find('select.adults_room_3').val(),
            children_room_3 = $(this).find('select.children_room_3').val();
        var final_string = '';
        if (rooms_number > 0) {
            final_string = adults_room_1;
        }
        if (rooms_number > 1) {
            final_string += ' + ' + adults_room_2;
        }
        if (rooms_number > 2) {
            final_string += ' + ' + adults_room_3;
        }
        $(this).find(".guest_selector .placeholder_text").html(final_string);
    });

}

function smartDatas(selector) {
    var promocode = selector.attr("data-promocode"),
        data_ini = selector.attr("data-smartdateini"),
        data_fin = selector.attr("data-smartdatefin"),
        package_preselection = selector.attr('data-smartpackage');

    if (package_preselection) {
        $(".paraty-booking-form").each(function () {
            var package_preselection = $("<input type='hidden' name='package_preselection' value='true'></input>");
            package_preselection.addClass('hidden_package_preselection');
            $(this).append(package_preselection);
        });

        setTimeout(function(){
            $(".fancybox-close").click(function() {
                $(".hidden_package_preselection").remove();
            });
        }, 1500);
    }

    if (promocode) {
        $("#data .paraty-booking-form input[name=promocode]").val(promocode);
    }

    if(data_ini) {
        $("#data .fecha_entrada input[name=startDate]").val(data_ini);

        var end_date = $.datepicker.parseDate("dd/mm/yy", data_ini);
        end_date.setDate(end_date.getDate() + 1);
        end_date = $.datepicker.formatDate("dd/mm/yy", end_date);
        $("#data .fecha_salida input[name=endDate]").val(end_date);
        $("#data .fecha_salida input[name=endDate]").datepicker("option", "minDate", $.datepicker.parseDate("dd/mm/yy", end_date));
    }

    if (data_fin) {
        $("#data .fecha_salida input[name=endDate]").val(data_fin);
    }

}
function news_height() {
    max_height = 0;
    $(".entry_widget .news_content").each(function () {
        actual_height = $(this).height();
        if (actual_height > max_height) {
            max_height = actual_height
        }
    });

    $(".entry_widget .news_content").height(max_height);
}
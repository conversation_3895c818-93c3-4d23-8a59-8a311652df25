{% if faqs_config %}
    <div class="faqs_section">
        <div class="faqs_wrapper">
            <div class="questions_wrapper">

                <div class="filters_button">
                    <i class="fas fa-sliders-h"></i>
                    {{ T_filtrar }}
                </div>


                {% for faq in faqs_config %}
                    {% if not faq.hide_in_section %}
                        <div class="question_element" tag="{{ faq.category|safe }}">
                            <h3 class="question">
                                {{ faq.title|safe }}
                            </h3>
                            <div class="answer">
                                {{ faq.description|safe }}
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        </div>

        <div id="filter_wrapper_banner">
            <div id="close_filters">
                <i class="fal fa-times"></i>
            </div>

            <div class="banner_title">
                <i class="fas fa-sliders-h"></i>
                {{ T_filtrar }}
            </div>

            <div class="available_filters_wrapper">
                <div class="faqs_filters_wrapper filter_block">
                    <div class="options_list active"></div>
                </div>
            </div>

            <div class="filters_buttons_wrapper">
                <div id="clear_filters_button">{{ T_borrar_filtros }}</div>
                <div id="apply_filters_button">{{ T_aplicar_filtros }}</div>
            </div>
        </div>
    </div>

    <script src="/js/{{ base_web }}/mobile_functions.js?v=1.00"></script>
    <script>
        $(function () {
            $(".question_element .question").click(function () {
                $(this).parent().toggleClass('active')
            })

            FaqsController.init()
        });
    </script>
{% endif %}
<script>
    var FaqsController = function () {
        return {
            init: function () {
                this.prepare_available_filters();
                this.prepare_listeners();
            },

            prepare_listeners: function () {
                $(".faqs_wrapper .filters_button").click(function () {
                    FaqsController.open_filters();
                });

                $(".faqs_filters_wrapper label").click(function () {
                    FaqsController.remove_all_selections();
                    $(this).closest('.option_element').find("input[type='checkbox']").prop('checked', true);
                    if (!$("#apply_filters_button").length) {
                        FaqsController.apply_filters();
                    }
                });

                if ($("#apply_filters_button").length) {
                    $("#apply_filters_button").click(function () {
                        FaqsController.apply_filters()
                    });
                }

                $("#clear_filters_button").click(function () {
                    FaqsController.remove_filters();
                });

                $("#close_filters").click(function () {
                    FaqsController.close_filters();
                });
            },

            remove_all_selections: function () {
                $(".faqs_filters_wrapper input").each(function () {
                    $(this).prop('checked', false);
                })
            },

            prepare_available_filters: function () {
                var available_filters = [];
                $(".question_element").each(function () {
                    var tag = $(this).attr('tag');
                    if (tag && available_filters.indexOf(tag) == -1) available_filters.push(tag);
                });

                available_filters.forEach(function (element, n) {
                    var option_element = $("<div class='option_element'></div>"),
                        radio_button = $("<input type='checkbox' name='faq_filter'>"),
                        label_element = $("<label></label>");

                    label_element.html(element);
                    radio_button.attr('value', element);
                    option_element.append(radio_button).append(label_element);
                    $(".faqs_filters_wrapper .options_list").append(option_element);
                });
            },

            open_filters: function () {
                $("#filter_wrapper_banner").addClass('active');
            },

            close_filters: function () {
                $("#filter_wrapper_banner").removeClass('active');
            },

            apply_filters: function () {
                var selected_radio_value = $(".faqs_filters_wrapper input:checked").attr('value');
                if (selected_radio_value) {
                    $(".question_element").each(function () {
                        if ($(this).attr('tag') != selected_radio_value) {
                            $(this).addClass('hide');
                        } else {
                            $(this).removeClass('hide');
                        }
                    })
                }

                this.close_filters();
            },

            remove_filters: function () {
                $(".faqs_filters_wrapper input:checked").prop('checked', false);
                $(".question_element").removeClass('hide');
                this.close_filters();
            }
        }
    }();
</script>
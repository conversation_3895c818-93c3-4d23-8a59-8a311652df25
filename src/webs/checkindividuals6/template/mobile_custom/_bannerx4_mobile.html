<div class="bannerx4_wrapper">
<div class="container12">
    {% if banner_ventajas_section.subtitle %}<h3>{{ banner_ventajas_section.subtitle|safe }}</h3>{% endif %}
    <div class="banner_ventajas">
        {% for banner in banner_ventajas %}
        <div class="banner">
            {% if 'href' in banner.description %}
                <div class="banner_content">
                     <div class="banner_img">
                         {% if not banner.cycle_banner_gallery and banner.servingUrl %}
                             {% if banner.servingUrl %}<img src="{{ banner.servingUrl|safe }}=s400">{% endif %}
                         {% elif banner.cycle_banner_gallery %}
                            <div class="owl-carousel">
                                {% for pic in banner.cycle_banner_gallery %}
                                    {% if pic.servingUrl %}<img src="{{ pic.servingUrl|safe }}=s400">{% endif %}
                                {% endfor %}
                            </div>
                         {% endif %}
                         {% if banner.title %}<h4>{{ banner.title|safe }}</h4>{% endif %}
                     </div>
                     <span>{{ banner.description|safe }}</span>
                </div>
            {% else %}
                {% if banner.cycle_banner_gallery %}
                    <div class="banner_img">
                         <div class="owl-carousel">
                            {% for pic in banner.cycle_banner_gallery %}
                             <a class="banner_link" {% if banner.linkUrl %}href="{{ banner.linkUrl|safe }}"{% endif %}>
                                 <div class="banner_img">
                                     {% if pic.servingUrl %}<img src="{{ pic.servingUrl|safe }}=s400">{% endif %}
                                     {% if banner.title %}<h4>{{ banner.title|safe }}</h4>{% endif %}
                                 </div>
                                 <span>{{ banner.description|safe }}</span>
                            </a>
                            {% endfor %}
                         </div>
                         {% if banner.title %}<h4>{{ banner.title|safe }}</h4>{% endif %}
                     </div>
                     <a class="banner_link" {% if banner.linkUrl %}href="{{ banner.linkUrl|safe }}"{% endif %}>
                        <span>{{ banner.description|safe }}</span>
                     </a>
                {% else %}
                <a class="banner_link" {% if banner.linkUrl %}href="{{ banner.linkUrl|safe }}"{% endif %}>
                     <div class="banner_img">
                         {% if banner.servingUrl %}<img src="{{ banner.servingUrl|safe }}=s400">{% endif %}
                         {% if banner.title %}<h4>{{ banner.title|safe }}</h4>{% endif %}
                     </div>
                     <span>{{ banner.description|safe }}</span>
                </a>
                {% endif %}
            {% endif %}
                {% if banner.widget_restaurant %}
                    <div class="widget_restaurant_button">
                        <a data-fancybox="" href="{{ banner.widget_restaurant|safe }}" class="widget_button_url fancybox.iframe">
                            {% if banner.widget_picture %}
                                <img src="{{ banner.widget_picture|safe }}">
                            {% endif %}
                            <span>
                                {% if banner.widget_restaurant_button %}{{ banner.widget_restaurant_button|safe }}{% else %}{{ T_ver_mas }}{% endif %}
                            </span>
                        </a>
                    </div>

                    <script>
                        $(window).load(function(){
                            $(".widget_button_url").fancybox({
                                type: "iframe"
                            })
                        })
                    </script>
                {% endif %}

            {% if banner.linkUrl %}
                <div class="bottom_links">
                    <a href="{{ banner.linkUrl|safe }}" {% if 'http' in banner.linkUrl|safe %}target="_blank"{% endif %}><span>{% if banner.btn_text %}{{ banner.btn_text|safe }}{% else %}{{ T_ver_mas }}{% endif %}</span></a>
                </div>
            {% endif %}

        </div>
        {% endfor %}
    </div>
    <div class="banner_ventajas_icos">
        {% for banner in banner_ventajas_icos %}<div class="banner">
            {% if banner.title %}<i class="fa {{ banner.title|safe }}"></i><span>{{ banner.description|safe }}</span>{% endif %}
        </div>{% endfor %}
    </div>
</div>
</div>

<script>
$(window).load(function () {
    $(".bannerx4_wrapper .banner_img .owl-carousel").owlCarousel({
        loop: true,
        nav: true,
        dots: false,
        items: 1,
        margin: 5,
        navText: ['<i class="fas fa-arrow-left"></i>', '<i class="fas fa-arrow-right"></i>'],
        autoplay: true
    });
})
</script>
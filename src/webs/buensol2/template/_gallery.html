<div class="gallery_filter_wrapper">
    {% for filter, gallery in gallery_section.items() %}
        <div class="gallery_filter" data-filter="{{ filter|safe }}">
            <div class="gallery_photos">
                {% for x in gallery %}
                    {% if x.linkUrl and "youtube" in x.linkUrl %}
                        <a class="iframe_video">
                            <iframe src="{{ x.linkUrl|safe }}" frameborder="0"></iframe>
                        </a>
                    {% else %}
                        <a href="{{ x.servingUrl|safe }}=s1024" rel="lightbox[gallery_carousel_{{ filter }}]"><img
                                src="{{ x.servingUrl|safe }}=s350-c">
                        {% if loop.index == 3 %}<span>{{ x.title|safe }}</span>{% endif %}
                        </a>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    {% endfor %}
</div>

<script>
    $(window).load(resize_video);
    $(window).resize(resize_video);
    function resize_video() {
        $("a.iframe_video").height($("a.iframe_video").width() - 1);
    }
</script>

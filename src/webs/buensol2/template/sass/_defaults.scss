//Base web (change too in template<PERSON><PERSON><PERSON> and in config.rb)
$base_web: "buen2";
// colors definitions
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);

// corporative colors definitions
$corporate_1: #9ACED5;
$corporate_2: #23143D;
$corporate_3: #F7BC2C;

$title_family: "Abril Fatface", serif;
$title_size: 1.7rem;
$description_size: .9rem;
$line_height: 2rem;

// colors for booking widget
$booking_widget_color_1: $white;//body back ground & year input text color
$booking_widget_color_2: $corporate_1;//header background & input texts
$booking_widget_color_3: gray;//label texts
$booking_widget_color_4: gray;//not used, but must be defined

@keyframes shine {
  from { -webkit-mask-position: 150%; }
  to { -webkit-mask-position: -50%; }
}

@-webkit-keyframes shine {
  from { -webkit-mask-position: 150%; }
  to { -webkit-mask-position: -50%; }
}
@-moz-keyframes shine {
  from { -webkit-mask-position: 150%; }
  to { -webkit-mask-position: -50%; }
}
@-o-keyframes shine {
  from { -webkit-mask-position: 150%; }
  to { -webkit-mask-position: -50%; }
}
@keyframes shine {
  from { -webkit-mask-position: 150%; }
  to { -webkit-mask-position: -50%; }
}



@keyframes shine-color {
  0% { color: white; }
  50% { color: #F7BC2C; }
  100% { color: white; }
}

@-webkit-keyframes shine-color {
  0% { color: white; }
  50% { color: #F7BC2C; }
  100% { color: white; }
}
@-moz-keyframes shine-color {
  0% { color: white; }
  50% { color: #F7BC2C; }
  100% { color: white; }
}
@-o-keyframes shine-color {
  0% { color: white; }
  50% { color: #F7BC2C; }
  100% { color: white; }
}

@keyframes shake {
  10%, 90% {
    -webkit-transform: translate3d(-1px, 0, 0);
    -moz-transform: translate3d(-1px, 0, 0);
    -ms-transform: translate3d(-1px, 0, 0);
    -o-transform: translate3d(-1px, 0, 0);
    transform: translate3d(-1px, 0, 0);
  }

  20%, 80% {
    -webkit-transform: translate3d(2px, 0, 0);
    -moz-transform: translate3d(2px, 0, 0);
    -ms-transform: translate3d(2px, 0, 0);
    -o-transform: translate3d(2px, 0, 0);
    transform: translate3d(2px, 0, 0);
  }

  30%, 50%, 70% {
    -webkit-transform: translate3d(-4px, 0, 0);
    -moz-transform: translate3d(-4px, 0, 0);
    -ms-transform: translate3d(-4px, 0, 0);
    -o-transform: translate3d(-4px, 0, 0);
    transform: translate3d(-4px, 0, 0);
  }

  40%, 60% {
    -webkit-transform: translate3d(4px, 0, 0);
    -moz-transform: translate3d(4px, 0, 0);
    -ms-transform: translate3d(4px, 0, 0);
    -o-transform: translate3d(4px, 0, 0);
    transform: translate3d(4px, 0, 0);
  }
}

@-webkit-keyframes shake {
  10%, 90% {
    -webkit-transform: translate3d(-1px, 0, 0);
    -moz-transform: translate3d(-1px, 0, 0);
    -ms-transform: translate3d(-1px, 0, 0);
    -o-transform: translate3d(-1px, 0, 0);
    transform: translate3d(-1px, 0, 0);
  }

  20%, 80% {
    -webkit-transform: translate3d(2px, 0, 0);
    -moz-transform: translate3d(2px, 0, 0);
    -ms-transform: translate3d(2px, 0, 0);
    -o-transform: translate3d(2px, 0, 0);
    transform: translate3d(2px, 0, 0);
  }

  30%, 50%, 70% {
    -webkit-transform: translate3d(-4px, 0, 0);
    -moz-transform: translate3d(-4px, 0, 0);
    -ms-transform: translate3d(-4px, 0, 0);
    -o-transform: translate3d(-4px, 0, 0);
    transform: translate3d(-4px, 0, 0);
  }

  40%, 60% {
    -webkit-transform: translate3d(4px, 0, 0);
    -moz-transform: translate3d(4px, 0, 0);
    -ms-transform: translate3d(4px, 0, 0);
    -o-transform: translate3d(4px, 0, 0);
    transform: translate3d(4px, 0, 0);
  }
}

@-moz-keyframes shake {
  10%, 90% {
    -webkit-transform: translate3d(-1px, 0, 0);
    -moz-transform: translate3d(-1px, 0, 0);
    -ms-transform: translate3d(-1px, 0, 0);
    -o-transform: translate3d(-1px, 0, 0);
    transform: translate3d(-1px, 0, 0);
  }

  20%, 80% {
    -webkit-transform: translate3d(2px, 0, 0);
    -moz-transform: translate3d(2px, 0, 0);
    -ms-transform: translate3d(2px, 0, 0);
    -o-transform: translate3d(2px, 0, 0);
    transform: translate3d(2px, 0, 0);
  }

  30%, 50%, 70% {
    -webkit-transform: translate3d(-4px, 0, 0);
    -moz-transform: translate3d(-4px, 0, 0);
    -ms-transform: translate3d(-4px, 0, 0);
    -o-transform: translate3d(-4px, 0, 0);
    transform: translate3d(-4px, 0, 0);
  }

  40%, 60% {
    -webkit-transform: translate3d(4px, 0, 0);
    -moz-transform: translate3d(4px, 0, 0);
    -ms-transform: translate3d(4px, 0, 0);
    -o-transform: translate3d(4px, 0, 0);
    transform: translate3d(4px, 0, 0);
  }
}

@-o-keyframes shake {
  10%, 90% {
    -webkit-transform: translate3d(-1px, 0, 0);
    -moz-transform: translate3d(-1px, 0, 0);
    -ms-transform: translate3d(-1px, 0, 0);
    -o-transform: translate3d(-1px, 0, 0);
    transform: translate3d(-1px, 0, 0);
  }

  20%, 80% {
    -webkit-transform: translate3d(2px, 0, 0);
    -moz-transform: translate3d(2px, 0, 0);
    -ms-transform: translate3d(2px, 0, 0);
    -o-transform: translate3d(2px, 0, 0);
    transform: translate3d(2px, 0, 0);
  }

  30%, 50%, 70% {
    -webkit-transform: translate3d(-4px, 0, 0);
    -moz-transform: translate3d(-4px, 0, 0);
    -ms-transform: translate3d(-4px, 0, 0);
    -o-transform: translate3d(-4px, 0, 0);
    transform: translate3d(-4px, 0, 0);
  }

  40%, 60% {
    -webkit-transform: translate3d(4px, 0, 0);
    -moz-transform: translate3d(4px, 0, 0);
    -ms-transform: translate3d(4px, 0, 0);
    -o-transform: translate3d(4px, 0, 0);
    transform: translate3d(4px, 0, 0);
  }
}

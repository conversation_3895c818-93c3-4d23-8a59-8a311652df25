# -*- coding: utf-8 -*-
from collections import OrderedDict

from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.language.language_utils import get_web_dictionary
from utils.mobile.mobile_utils import user_agent_is_mobile
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "silco"

class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)

		return self.buildTemplate('booking/booking_engine_3/_booking_widget.html', params)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(Temp<PERSON><PERSON><PERSON><PERSON>, self).getBookingWidgetOptions(language)
		options['promo_double'] = True
		options['caption_submit_book'] = True
		return options

	def buildRoomsFromSections(self, language):
		all_rooms = self.getPicturesProperties(language,'habitaciones_blocks',['icons'])
		for room in all_rooms:
			sect_gallery = room['linkUrl']
			room['pictures']=get_pictures_from_section_name(sect_gallery, language)
			room['name']=room['title']
		return all_rooms

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''

		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

		result_params_dict = {
			'base_web': base_web,
			'official_text': get_section_from_section_spanish_name('web oficial', language),
			'phone_contact': get_section_from_section_spanish_name('telefono', language),
			"banner_x2": get_pictures_from_section_name("bannersx2", language),
			'footer_columns': get_pictures_from_section_name("footer columns", language)
		}

		#Content Sections
		content_sections = {
			'Mis Reservas': True,
			u'Galeria de Imagenes': True
		}

		#Content by Subtitle
		actual_section = get_section_from_section_spanish_name(sectionToUse.get('sectionName', ''), language)

		if not user_agent_is_mobile():
			result_params_dict['pictures'] = get_pictures_from_section_name(section_name, language)

		if actual_section.get('subtitle', False):
			result_params_dict['content_subtitle'] = actual_section

		if content_sections.get(sectionToUse.get('sectionType', ''), False) or not sectionToUse:
			result_params_dict['content_access'] = True

		if section_type == "Inicio":
			result_params_dict["ini_section"] = True

		# Habitaciones
		if section_name == 'apartamentos':
			all_rooms = self.buildRoomsFromSections(language)
			result_params_dict['rooms'] = all_rooms

			for i in all_rooms:
				if i.get('icons'):
					i['icos'] = get_pictures_from_section_name(i['icons'],language)

		# Ofertas
		if section_type == 'Ofertas':
			all_promotions = super(TemplateHandler, self).buildPromotionsInfo(language)
			result_params_dict['blocks'] = all_promotions

		if section_name == u'localización y contacto':
			additionalParams4Contact={}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True
			additionalParams4Contact['privacy_checkbox'] = True

			sectionTemplate = 'secciones/contact.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['content'] = ''
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			location_html=get_section_from_section_spanish_name(u"Localización", language)
			iframe_google_map=get_section_from_section_spanish_name("Iframe google maps", language)

			subtitle_form=sectionToUse['subtitle']

			result_params_dict['contact_html'] = contact_html
			result_params_dict['location_html'] = location_html
			result_params_dict['iframe_google_map'] = iframe_google_map
			result_params_dict['subtitle_form'] = subtitle_form
			result_params_dict['location_access'] = True

		#Galeria de Imagenes
		if section_type == u"Galeria de Imagenes":
			result_params_dict["pictures"] = get_pictures_from_section_name(" imagenes_slider", language)


		if advance_properties.get("mini_gallery", False):
			result_params_dict["mini_gallery"] = get_pictures_from_section_name(advance_properties["mini_gallery"],language)
			result_params_dict["title_mini_gallery"] = get_section_from_section_spanish_name(advance_properties["mini_gallery"],language)

		if advance_properties.get("blocks", False):
			result_params_dict["content_blocks"] = get_pictures_from_section_name(advance_properties["blocks"],language)

		if user_agent_is_mobile() and section_type == "Inicio":
			args = {
				"inicio": get_section_from_section_spanish_name(sectionToUse['sectionName'], language).get('content', '')
			}
			html_after_booking_engine = self.buildTemplate_2("/mobile/_html_after_booking_engine.html", args, False,"silchoro")
			result_params_dict['html_after_booking_engine'] = html_after_booking_engine
			if advance_properties.get("mini_gallery"):
				gallery_params = {
					"minigallery_mobile": get_pictures_from_section_name(advance_properties.get("mini_gallery"), language)
				}
				result_params_dict['html_after_booking_engine'] += self.buildTemplate_2("mobile_templates/2/_minigallery_v1.html", gallery_params, False)




		return result_params_dict


	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		template_sections_dict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter.html',
		}
		template = template_sections_dict.get(sectionType, super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate))
		return template

	def getParamsForSection(self, section, language):
		result = {}
		if section['sectionType'] == 'Galeria de Imagenes':
			result['automatic_content'] = False
			result['gallery_section'] = True
			result['filters_gallery'] = True
			picture_gallery = get_pictures_from_section_name(section['sectionName'], language)
			filters = OrderedDict()
			for x in picture_gallery:
				if not filters.get(x.get('title', ''), False):
					filters[x.get('title', '')] = [x['servingUrl']]
				else:
					filters[x.get('title', '')].append(x['servingUrl'])
			result['filters_gallery'] = filters
		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "550"

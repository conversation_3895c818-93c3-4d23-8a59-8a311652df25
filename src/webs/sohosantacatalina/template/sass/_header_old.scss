header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 150;
    background: linear-gradient(rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0));
    
    .top_header {
        .container12 {
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            padding: 10px 0;
            
            *, * a {
                color: white;
                font-size: 12px;
                font-weight: 500;
                text-transform: uppercase;
                letter-spacing: 1px;
            }
            
            > div {
                flex: auto;
                display: flex;
                flex-flow: row nowrap;
                align-content: center;
            }
            
            .header_icons_wrapper {
                width: 55%;
                justify-content: flex-start;
                
                .header_icon {
                    margin-right: 30px;
                    display: flex;
                    
                    i {
                        font-size: 20px;
                        margin-right: 7px;
                        @include flex_xy;
                    }
                }
            }
            
            .links_wrapper {
                width: 45%;
                justify-content: flex-end;
                
                #top-sections {
                    a {
                        margin-left: 30px;
                    }
                }
                
                #lang {
                    margin-left: 80px;
                    
                    a {
                        position: relative;
                        display: inline-block;
                        transition: all .4s;
                        
                        &:not(:first-child) {
                            padding-left: 10px;
                            
                            &::before {
                                position: absolute;
                                content: '';
                                width: 2px;
                                height: 2px;
                                border-radius: 50%;
                                background-color: white;
                                top: 50%;
                                left: 0;
                                transform: translateY(-50%);
                                transition: all .4s;
                            }
                        }
                        
                        &:hover, &.selected {
                            position: relative;
                            color: $corporate_2;
                            
                            &::before {
                                position: absolute;
                                content: '';
                                background-color: white;
                                bottom: 0;
                                left: 0;
                                right: 0;
                                height: 2px;
                                background-color: white;
                            }
                        }
                    }
                }
            }
        }
    }
    
    .bottom_header {
        .container12 {
            position: relative;
            display: flex;
            flex-direction: row;
            flex-wrap: nowrap;
            
            #logoDiv {
                width: 270px;
                padding-top: 30px;
            }
            
            #main_menu {
                padding-top: 70px;
                flex: auto;
                
                #main-sections-inner {
                    display: flex;
                    flex-wrap: nowrap;
                    justify-content: flex-end;
                    
                    
                    .main-section-div-wrapper {
                        margin-left: 35px;
                        
                        a {
                            display: inline-block;
                            text-transform: uppercase;
                            color: white;
                            text-align: center;
                            font-size: 13px;
                            font-weight: 500;
                            position: relative;
                            @include transition(color, .6s);
                            
                            &::before {
                                position: absolute;
                                content: '';
                                bottom: -2px;
                                left: 50%;
                                right: auto;
                                transform: translateX(-50%);
                                height: 3px;
                                border-radius: 1px;
                                max-width: 0;
                                background-color: white;
                                opacity: 0;
                                z-index: -1;
                                transition: all .2s;
                            }
                        }
                        
                        &:hover, &#section-active {
                            > a {
                                position: relative;
                                color: $corporate_2;
                                
                                &::before {
                                    opacity: 1;
                                    left: 0;
                                    right: 0;
                                    transform: translateX(0);
                                    max-width: 100%;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
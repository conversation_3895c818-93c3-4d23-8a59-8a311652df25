<div class="container12">
<article class="entry">
    <h1 class="entry_title">{{ entry.name|safe }}</h1>
    <div class="entry_picture">
        <img src="{{ entry.picture|safe }}=s1140" alt="">
    </div>
    {% if entry.author %}
    <div class="entry_author">
        <div class="author_name">by <span>{{ entry.author|safe }}</span></div>
    </div>
    {% endif %}
    {% if entry.creationDate or entry.tags or entry.comments %}
    <div class="entry_info">
        {% if entry.creationDate %}<span class="date">{{ entry.creationDate|safe }}</span>{% endif %}
        {% if entry.tags %}<span class="tags">tags: {{ entry.tags|safe }}</span>{% endif %}
        {% if entry.comments %}<span class="comments">{{ entry.comments|length }} {{ T_comentarios }}</span>{% endif %}
    </div>
    {% endif %}
    <div class="entry_content">{{ entry.description|safe }}</div>
    <div class="entry_share">
        <div class="addthis_inline_share_toolbox_4w52"></div>
    </div>
    <div class="entry_comments">
        <h3>{{ T_comentarios }}</h3>
        {% if entry.comments|length > 0 %}
            <div class="comment_list">
                {% for comment in entry.comments %}
                    <div class="comment">
                        <div class="comment_pic"><img src="{{ comment.pic|safe }}=s100"></div>
                        <div class="name"><span>{{ comment.name|safe }}</span>:</div>
                        <div class="text">{{ comment.description|safe }}</div>
                    </div>
                {% endfor %}
            </div>
        {% endif %}
        <div class="comment_form">
            <form name="fancy_contact_form" id="fancy_contact_form" method="post" action="/utils/?action=contact">
                <input type="hidden" name="section" id="section-name" value="Comment in {{ sectionName|safe }}"/>

                <div class="text_element">
                    <label for="comments" class="title">{{ T_comentarios }}</label>
                    <textarea style="height:150px;" type="text" id="comments" name="comments" class="bordeInput"
                              value=""></textarea>
                </div>

                {% if destination_address %}
                    <input type="hidden" name="destination_email" id="destination_email"
                           value="{{ destination_address }}">
                {% endif %}

                <div class="input_element">
                    <label for="name" class="title">{{ T_nombre_y_apellidos }}</label>
                    <input type="text" id="name" name="name" class="bordeInput" value=""/>
                </div>

                <div class="input_element">
                    <label for="email" class="title">{{ T_email }}</label>
                    <input type="text" id="email" name="email" class="bordeInput" value=""/>
                </div>

                <div class="input_element">
                    <label for="telephone" class="title">{{ T_telefono }}</label>
                    <input type="text" id="telephone" name="telephone" class="bordeInput" value=""/>
                </div>

                <div class="check_element">
                    <input class="check_privacy" id="privacy" name="privacy" type="checkbox"
                           value="privacy"/>
                                <span class="title"><a href="/{{ language }}/?sectionContent=politica-de-privacidad.html"
                                                       class="myFancyPopup fancybox.iframe">{{ T_lopd }}</a></span>
                </div>

                <div id="contact-button-wrapper">
                    <div id="popup_form_button">
                        {{ T_enviar }}
                    </div>
                </div>

                <div class="thanks_popup_wrapper" style="display: none">{{ T_gracias_contacto }}</div>
            </form>
            <script type="text/javascript" src="/static_1/lib/jquery.validate.js" async></script>
            <script type="text/javascript">
                $(window).load(function () {
                    jQuery.validator.addMethod("phone", function (phone_number, element) {
                        phone_number = phone_number.replace(/\s+/g, "");
                        return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                                phone_number.match(/^[0-9 \+]\d+$/);
                    }, "Please specify a valid phone number");

                    $("#fancy_contact_form").validate({
                                                          rules: {
                                                              name: "required",
                                                              privacy: "required",
                                                              email: {
                                                                  required: true,
                                                                  email: true
                                                              },
                                                              telephone: {
                                                                  required: true,
                                                                  phone: true
                                                              },
                                                              comments: "required"
                                                          },
                                                          messages: {
                                                              name: "{{ T_campo_obligatorio}}",
                                                              privacy: "{{ T_campo_obligatorio }}",
                                                              email: {
                                                                  required: "{{ T_campo_obligatorio|safe }}",
                                                                  email: "{{ T_campo_valor_invalido|safe }}"
                                                              },
                                                              telephone: {
                                                                  required: "{{ T_campo_obligatorio|safe }}",
                                                                  phone: "{{ T_campo_valor_invalido|safe }}"
                                                              },
                                                              comments: "{{ T_campo_obligatorio|safe }}"
                                                          }

                                                      });

                    $("#popup_form_button").click(function () {


                        if ($("#fancy_contact_form").valid()) {
                            $.post(
                                    "/utils/?action=contact",
                                    {
                                        'name': $(".input_element #name").val(),
                                        'telephone': $(".input_element #telephone").val(),
                                        'email': $(".input_element #email").val(),
                                        'comments': $(".input_element #comments").val(),
                                        'section': $("#section-name").val()
                                    },

                                    function (data) {
                                        $(".input_element #name").val("");
                                        $(".input_element #telephone").val("");
                                        $(".input_element #email").val("");
                                        $(".input_element #comments").val("");
                                        $(".thanks_popup_wrapper").fadeIn("slow");
                                    }
                            );
                        }
                    });
                })
            </script>
        </div>
    </div>
</article>


<aside class="right_column">

<h3 class="widget_title"><span>{{ T_noticias }}</span></h3>
<div class="news_right_widget">
    {% for item in news_widget %}
        <div class="news_widget_entry">
            <div class="image">
                <a href="/{{ path }}/{{ item.friendlyUrl }}"><img src="{{ item.picture|safe }}=s100" alt=""></a>
            </div>
            <div class="title"><a href="/{{ path }}/{{ item.friendlyUrl }}">{{ item.name|safe }}</a></div>
            <div class="extra_info">{% if entry.date_formated %}<span><i class="fal fa-calendar-alt"></i>{{ item.date_formated|safe }}</span>{% endif %}{% if entry.comments %}<span><i class="fa fa-comment-o"></i>{{ item.comments|length }}</span>{% endif %}</div>
            <div style="clear:both;"></div>
        </div>
    {% endfor %}
</div>

{% if social %}
<h3 class="widget_title"><span>{{ T_siguenos_en }}</span></h3>
<div class="social_widget">
            {% if social.facebook_id %}<a href="http://www.facebook.com/{{ social.facebook_id }}" target="_blank" class="social_facebook"><i class="fa fa-facebook" aria-hidden="true"></i></a>{% endif %}
            {% if social.twitter_id %}<a href="https://twitter.com/#!/{{ social.twitter_id }}" target="_blank" class="social_twitter"><i class="fa fa-twitter" aria-hidden="true"></i></a>{% endif %}
            {% if social.google_plus_id %}<a href="https://plus.google.com/u/0/{{ social.google_plus_id }}" target="_blank" rel="publisher"><i class="fa fa-google-plus" aria-hidden="true"></i></a>{% endif %}
            {% if social.youtube_id %}<a href="https://www.youtube.com/{{ social.youtube_id }}" target="_blank" class="social_youtube"><i class="fa fa-youtube" aria-hidden="true"></i></a>{% endif %}
            {% if social.pinterest_id %}<a href="http://es.pinterest.com/{{ social.pinterest_id }}" target="_blank"><i class="fa fa-pinterest-p" aria-hidden="true"></i></a>{% endif %}
            {% if social.instagram_id %}<a href="http://www.instagram.com/{{ social.instagram_id }}" target="_blank"><i class="fa fa-instagram" aria-hidden="true"></i></a>{% endif %}
</div>
{% endif %}

{% if instagram_pictures %}
<h3 class="widget_title"><span>Instagram</span></h3>
<div class="instagram_widget">
    {% for picture in instagram_pictures.data[:9] %}
            <a href="{{ picture.link }}" target="_blank">
                <div class="instagram_picture">
                    <img src="{{ picture.images.standard_resolution.url }}" alt="" class="instagram_img">
                    <div class="black_overlay"></div>
                    <div class="picture_info">
                        <div class="comment_info"><i class="fa fa-comment" aria-hidden="true"></i>{{ picture.comments.count }}</div>
                        <div class="likes_info"><i class="fa fa-heart" aria-hidden="true"></i>{{ picture.likes.count }}</div>
                    </div>
                </div>
            </a>
    {% endfor %}
    <div style="clear: both;"></div>
</div>
{% endif %}

{% if foto_collage %}
<h3 class="widget_title"><span>{{ T_images_gallery }}</span></h3>
<div class="instagram_widget">
    {% for picture in foto_collage[:9] %}
            <a class="room_gallery_element" href="{{ picture.servingUrl }}" rel="lightbox[gallery_room_1]">
                <div class="instagram_picture">
                    <img src="{{ picture.servingUrl }}" alt="" class="instagram_img">
                    <div class="black_overlay"></div>
                </div>
            </a>
    {% endfor %}
    <div style="clear: both;"></div>
</div>
{% endif %}





{% if banner_mosaic_audio_player %}
{% if banner_mosaic_audio_player_section.subtitle %}<h3 class="widget_title"><span>{{ banner_mosaic_audio_player_section.subtitle|safe }}</span></h3>{% endif %}
<div class="comments_widget">
    {% for track in banner_mosaic_audio_player %}
        {% if track.title %}
        <div class="comment">
            <div class="text">{{track.description|safe}}</div>
            <div class="name">{{track.title|safe}}</div>
        </div>
        {% endif %}
    {% endfor %}
</div>
{% endif %}

</aside>

<div style="clear:both;"></div>

<script type="text/javascript" src="//s7.addthis.com/js/300/addthis_widget.js#pubid=ra-5a1825aff91cd51e"></script>
</div>
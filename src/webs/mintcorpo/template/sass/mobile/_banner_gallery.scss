.banner_gallery_wrapper {
  @include before_bg;
  padding: $mobile_padding;
  box-sizing: border-box;
  @include display_flex;
  * {
    box-sizing: border-box;
  }
  &:before {
    top: 80px;
  }
  .banner {
    position: relative;
    display: inline-block;
    width: calc(50% - 10px);
    height: 150px;
    overflow: hidden;
    cursor: pointer;

    &:not(:nth-of-type(2n)) {
      margin-right: 10px;
    }

    img {
      @include center_image;
      width: auto;
    }

    .img_content {
      display: none;
    }
  }

  .owl-nav {
    text-align: center;
    margin-top: 30px;
    .owl-prev, .owl-next {
      @include owl_nav_styles;
      margin:  0 35px 0 0;
      i {
        font-size: 48px;
      }
    }
    .owl-next {
      margin: 0 0 0 35px;
    }
  }
}
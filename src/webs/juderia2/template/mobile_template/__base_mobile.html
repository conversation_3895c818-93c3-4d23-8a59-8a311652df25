<!DOCTYPE html>
<html lang="es">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1"/>
    <title>{% if sectionName %} {{sectionName|safe}} - {% endif %} {{title|safe}}</title>

    <link rel="stylesheet" href="//ajax.googleapis.com/ajax/libs/jqueryui/1.10.4/themes/smoothness/jquery-ui.css" />

    {% if namespace %}
	    <link href="/static_1/css/styles_mobile_{{namespace}}.css" rel="stylesheet"/>
    {% else %}
        <link href="/static_1/css/styles_mobile.css" rel="stylesheet"/>
    {% endif %}
      <link rel="stylesheet" href="/static_1/css/swipebox.css">
      <script src="//ajax.googleapis.com/ajax/libs/jquery/1.11.0/jquery.min.js"></script>





  </head>


  {% if using_fancybox %}

      <link rel="stylesheet" href="/static_1/lib/fancybox4Mobile/css/jquery.fancybox.css" type="text/css" media="screen" />
	  <link rel="stylesheet" href="/static_1/lib/fancybox4Mobile/css/jquery.fancybox-buttons.css" type="text/css" media="screen" />
      <link rel="stylesheet" href="/static_1/lib/fancybox4Mobile/css/jquery.fancybox-thumbs.css" type="text/css" media="screen" />
      <script type="text/javascript" src="/static_1/lib/fancybox4Mobile/lib/jquery.mousewheel-3.0.6.pack.js"></script>
	  <script type="text/javascript" src="/static_1/lib/fancybox4Mobile/source/jquery.fancybox.pack.js"></script>
      <script type="text/javascript" src="/static_1/lib/fancybox4Mobile/source/helpers/jquery.fancybox-buttons.js"></script>
	  <script type="text/javascript" src="/static_1/lib/fancybox4Mobile/source/helpers/jquery.fancybox-media.js"></script>
	  <script type="text/javascript" src="/static_1/lib/fancybox4Mobile/source/helpers/jquery.fancybox-thumbs.js"></script>

      <script>

       $(".fancybox").fancybox({{config_fancybox}});


       // Detect whether device supports orientationchange event, otherwise fall back to
       // the resize event.
        var supportsOrientationChange = "onorientationchange" in window,
        orientationEvent = supportsOrientationChange ? "orientationchange" : "resize";

        window.addEventListener(orientationEvent, function() {
            console.log('Rotating:' + $(window).orientation + " " + screen.width);
            $(".fancybox").fancybox({{config_fancybox}});
            if ($("{{fancybox_id_wrapper}}").css('display') != 'none'){
                $.fancybox($("{{fancybox_id_wrapper}}"), {{config_fancybox}} );
            }

        }, false);

      </script>


   {% endif %}



  <body>
  <div class="wrap">
    <div class="container_100">
      <header>

          <a href="#menu" id="simple-menu">
             <img src="/static_1/images/mobile_img/menu_button.png"/>
          </a>


        <p class="logo">
          <a href="{{host|safe}}">

          </a>
        </p>

        <div class="contact">
          {% if email %}
            <a href="mailto:{{ email }}" class="mail"><span class="icon-envelope"></span></a>
          {% endif %}


          {% if phones and phonesInMobile and phones.0%}
            <a href="tel:{{ phones.0 }}" class="telefono"><span class="icon-phone"></span></a>
          {% endif %}

        </div>
      </header>



      <div class="flexslider">
        <ul class="slides">
          {% for picture in pictures %}
            {% if picture.allowInMobile %}
                <li><img src="{{ picture.servingUrl }}=s600" ></li>
            {% endif %}
          {% endfor %}
        </ul>
      </div>
    </div>

    <div class="container_90">
        <div class="content">{% block content %}{% endblock %}</div>
    </div>

    {% if main_menu_in_bottom %}
    <div class="container_100">
        <ul class="main-menu-bottom">
        {% for section in left_mobile_sections %}
            <li class="bg-step-{{ forloop.counter }}"><a href="{{ host|safe }}/{{ section.friendlyUrl }}">{{ section.title|safe }}</a></li>
        {% endfor %}
        </ul>
    </div>
    {% endif%}

    {% include "_iconos.html" %}

    <div class="container_100">
        {% if not main_menu_in_bottom %}
            <nav><ul>
            {% for section in bottom_mobile_sections %}
              <li><a href="{{ host|safe }}/{{ section.friendlyUrl }}">{{ section.title|safe }}</a></li>
            {% endfor %}
            </ul></nav>
        {% endif %}
        {% include "_footer.html" %}
    </div>


    {% include "_lateral_menu.html" %}


    <script src="/static_1/lib/mmenu/mmenu.js"></script>
    <script src="//ajax.googleapis.com/ajax/libs/jqueryui/1.10.1/jquery-ui.min.js"></script>
    <script src="/static_1/js/datepicker/jquery.ui.datepicker-{{language_code}}.js" type="text/javascript"></script>
    <script src="/static_1/lib/swipebox/jquery.swipebox.js"></script>
    <script src="/static_1/scripts/functions.js"></script>
    {% if extra_code_mobile %}
        {{ extra_code_mobile }}
    {% endif %}


  </div>
  </body>

</html>
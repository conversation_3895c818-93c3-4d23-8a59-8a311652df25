.newsletter_wrapper {
    @extend .sec_pad;
    position: relative;
    background-color: $lightgrey;
    background-size: 100%;
    background-attachment: fixed;
    background-blend-mode: overlay;

    &:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: #f3f3f350;
    }

    .container12 {
        position: relative;
    }

    .newsletter_title {
        @include title_styles;
        text-align: center;
        margin-bottom: 130px;
    }

    .newsletter_description {
        color: white;
        text-align: center;
        letter-spacing: 2px;
        font-size: 16px;
        font-weight: 700;
        margin-bottom: 20px;
        strong {
            font-weight: bold;
        }
    }


    .newsletter_form {
        text-align: center;
        position: relative;

        &:before {
            content: "\f658";
            font-family: "Font Awesome 5 Pro";
            font-weight: 300;
            color: $corporate_1;
            position: absolute;
            top: 25px;
            z-index: 5;
            left: 215px;
            font-size: 34px;
        }

        input#suscEmail {
            position: relative;
            display: inline-block;
            vertical-align: middle;
            background-color: white;
            border-width: 0;
            font-size: 16px;
            font-weight: 300;
            padding: 25px 25px 25px 80px;
            width: 500px;
            margin-right: 10px;
            margin-bottom: 20px;
            color: $black;

            &::placeholder {
                color: $corporate_2;
                font-weight: 500;
                font-size: $font_body_size;
            }
        }

        .button_newsletter {
            display: inline-block;
            vertical-align: middle;
            width: 250px;
            font-size: 18px;
            font-weight: 700;
            padding: 22px 0;
            background-color: $corporate_2;
            color: white;
            letter-spacing: 2px;
            text-transform: uppercase;
            margin-bottom: 20px;
            cursor: pointer;
            transition: background-color 0.6s;

            &:hover {
                background-color: $corporate_1;
            }
        }

        .check_newsletter {
            display: block;
            width: 100%;

            a, label {
                font-size: $font_sm;
                font-weight: 100;
            }

            strong {

            }

            .newsletter_checkbox {

                input.check_privacy {
                    -webkit-appearance: none;
                    border: solid 2px $corporate_1;
                    border-radius: 0;
                    height: 15px;
                    width: 15px;
                    vertical-align: middle;

                    &:checked {
                        background-color: $corporate_1;
                    }
                }
            }
        }
    }

    .social_newsletter {
        position: absolute;
        left: 50%;
        top: 80px;
        transform: translateX(-50%);

        * {
            color: white;
        }

        a {
            margin: 0 25px;

            i {
                position: relative;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                font-size: 30px;

                &::before {
                    @include center_xy;
                    z-index: 1;
                }

                &::after {
                    position: absolute;
                    content: '';
                    background-color: $corporate_3;
                    border: 7px solid lighten($corporate_3, 10%);
                    top: -7px;
                    bottom: -7px;
                    left: -7px;
                    right: -7px;
                    border-radius: 50%;
                }
            }
        }
    }
}
<div class="news_full_wrapper">
    {% if blog_filters %}
        <div class="filter_list_wrapper">
            <div class="filter_list filter_tag active">
                <b class="filter_list_title">{% if blog_filter_title %}{{ blog_filter_title|safe }}{% else %}{{ T_filtrar_su_busqueda }}{% endif %}</b>
                <i class="fal fa-plus"></i>
                {% for filter in blog_filters %}
                    <div class="filter_element" style="display: none;">
                        <span class="input_checkbox"><input type="checkbox" name="category" value="{{ filter|safe }}"><span class="checkmark"></span></span>&nbsp;
                        <span>{{ filter|safe }}</span>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}
    <div class="news_wrapper">
        {% for item in news %}
            <div class="entry_widget" data-filters="{% for tag in item.tags_list %}{{ tag|safe }};{% endfor %}">
                <div class="image">
                    <a href="/{{ language_code }}/{{ T_noticias }}/{{ item.friendlyUrl }}"><img src="{{ item.picture|safe }}" alt=""></a>
                </div>
                <a href="/{{ language_code }}/{{ T_noticias }}/{{ item.friendlyUrl }}" class="read_more_new"><i class="fa fa-plus"></i>{{ T_leer_mas }}</a>
                {% if item.name %}<h2 class="title"><a href="/{{ language_code }}/{{ T_noticias }}/{{ item.friendlyUrl }}">{{ item.name|safe }}</a></h2>{% endif %}
                {% if item.creationDate %}<div class="date"><i class="fa fa-calendar-o"></i><span>{{ item.creationDate|safe }}</span></div>{% endif %}
                {% if item.description %}<div class="content">{{ item.description|safe }}</div>{% endif %}
            </div>
        {% endfor %}
    </div>
</div>
<script src="/static_1/lib/isotope/isotope.min.js"></script>
<script>
$(window).load(function () {
    $(".filter_list_wrapper .filter_list .filter_list_title, .filter_list_wrapper .filter_list i").click(function () {
       $(this).parent().toggleClass("active");
       $(this).parent().find("i").toggleClass("fa-plus fa-minus");
       $(this).parent().find(".filter_element").slideToggle();
    });

    $(".filter_list .filter_element input").click(function () {
        setTimeout(function () {
            apply_filters();
        }, 500);
    });
});

function apply_filters() {
    let filters = [];
    let $grid = $('.news_wrapper').isotope({
        itemSelector: '.entry_widget'
    });
    $(".filter_list .filter_element input:checked").each(function () {
        if($(this).closest(".filter_list").hasClass("filter_tag")) {
            filters.push($(this).val());
        }
    }).promise().done(function () {
        $grid.isotope({filter: function () {
            let entry_filters = $(this).data('filters').split(';');
            return filters.every((filter) => entry_filters.includes(filter));
        }});
    });
}
</script>

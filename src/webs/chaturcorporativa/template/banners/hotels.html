<div class="container12">
    <div class="cycle_banners_wrapper">
        {% for x in widget_hotels %}
            <div class="cycle_element">
                {% if x.servingUrl %}
                    <div class="hotels_image cycle_image">
                        <img src="{{ x.servingUrl|safe }}=s800" alt="{{ x.altText|safe }}">
                    </div>
                    <div class="cycle_content">
                        {% if x.title %}
                            <h2 class="cycle_title">{{ x.title|safe }}</h2>
                        {% endif %}
                        {% if x.description %}
                            <div class="cycle_description">{{ x.description|safe }}</div>
                        {% endif %}
                        <div class="hotels_icons">
                            {% if x.gallery and not is_mobile %}<div class="hotels_icon">
                                    <i class="fa fa-picture-o"></i>
                                    {% for pic in x.images %}
                                        <a href="{{ pic.servingUrl|safe }}=s1900" {% if not loop.first %}style="display: none" {% endif %} rel="lightbox[{{ x.namespace }}]">{{ T_ver_fotos }}</a>
                                    {% endfor %}
                                </div>{% endif %}{% if x.iframe_map and not is_mobile %}<div class="hotels_icon">
                                    <i class="fa fa-map-marker"></i>
                                    <a href="{{ x.iframe_map }}" class="myFancyPopup fancybox.iframe">{{ T_ver_mapa }}</a>
                                </div>{% endif %}{% if x.phone %}<div class="hotels_icon">
                                    <i class="fa fa-phone"></i>
                                    <a href="tel:{{ x.phone }}">{{ x.phone|safe }}</a>
                                </div>{% endif %}{% if x.email %}<div class="hotels_icon">
                                    <i class="fa fa-envelope"></i>
                                    <a href="mailto:{{ x.email }}">{{ x.email|safe }}</a>
                                </div>{% endif %}{% if x.linkUrl %}<div class="hotels_icon">
                                    <i class="fa fa-external-link"></i>
                                    <a href="{{ x.linkUrl }}">{{ T_visitar_web }}</a>
                                </div>{% endif %}
                            {% if x.booking_url %}
                                <a href="javaScript:booking_click('{{ x.namespace|safe }}')" class="booking_button"><span>{{ T_reservar }}</span></a>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </div>
        {% endfor %}
    </div>
</div>

<script>
    {% if is_mobile %}
        function booking_click(namespace) {
            $(".mobile_engine_action").click();
            if (namespace){
                $(".hotel_selector_mobile option[data-namespace="+namespace+"]").attr('selected', 'selected');
                $(".hotel_selector_mobile").trigger('change');
            }
        }
    {% endif %}
</script>
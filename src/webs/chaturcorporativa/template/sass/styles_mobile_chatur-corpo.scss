@import "defaults";
@import "styles_mobile/2/2";
@import "styles_mobile/2/carousel_icon";

.mobile_engine {
  &.open {
    height: 390px;

    .mobile_engine_action {
      bottom: 385px;
    }
  }
}


/* Mis reservas */

#my-bookings-form {

  #my-bookings-form-fields {

    select {
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      display: block;
      width: 100%;
      margin: 10px auto;
      padding: 22px;
      text-align: center;
      font-size: 14px;
      background-color: white;
      border-radius: 5px;
      border: 1px solid $corporate_1;
    }

    #my-bookings-form-search-button {
      background: $corporate_1;
      width: 100%;
      padding: 22px;
      font-size: 24px;
      border-radius: 5px;
      color: white;
    }

  }
}

/* Tabla Precios */
.banner_table_wrapper {
  padding-bottom: 30px;
  position: relative;

  &::before {
    position: absolute;
    content: '';
    top: 70px;
    bottom: 20px;
    right: 0;
    width: 50px;
    background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 1) 100%);
  }

  #salones-table {
    table {
      border-right: 30px solid white;

      tr {
        th, td {
          font-size: 14px;
          padding: 5px;

          div {
            img {
              width: 40px;
            }
          }
        }
      }
    }
  }
}

/* FINAL Tabla Precios */


/* Cycle banners */

.cycle_banners_wrapper {
  width: 100%;
  background-color: #F4F4F4;
  margin-top: 50px;

  .cycle_element {
    display: block;
    width: 100%;
    position: relative;

    .cycle_image {
      position: relative;
      overflow: hidden;
      width: 100%;
      height: 200px;

      img {
        @include center_image;
      }
    }

    .cycle_content {
      background-color: #F4F4F4;
      box-sizing: border-box;
      padding: 10px;
      position: relative;

      .cycle_title {
        text-transform: uppercase;
        font-size: 23px;;
        color: $corporate_1;
        font-weight: 400;
        background-color: #F4F4F4;

      }

      .cycle_description {
        font-size: 14px;
        line-height: 26px;
        text-align: justify;
        font-weight: 300;
        display: block;
        background-color: #F4F4F4;
        float: none;
        padding-top: 10px;
      }

      .hotels_icons {
        .hotels_icon {
          margin-top: 20px;

          i {
            color: $corporate_1;
            margin-right: 10px;
          }

          a {
            color: black;
            font-size: 13px;
          }
        }

        .booking_button {
          position: relative;
          display: block;
          vertical-align: middle;
          margin: 30px auto;
          text-align: center;
          background-color: $corporate_1;
          font-size: 20px;
          text-transform: uppercase;
          color: white;
          padding: 10px 0;

          span {
            position: relative;
          }
        }
      }
    }
  }
}

#full_wrapper_booking {
  .destination_wrapper {
    width: 95%;
    margin: 10px 0;

    .destination_field {
      .hotel_selector_mobile {
        background-color: white;
        width: 100%;
        padding: 10px;
      }
    }
  }

  .wrapper_booking_button {
    .submit_button {
      background: $corporate_1;
    }
  }
}

.offers_filter_wrapper {
  position: fixed;
  top: 50%;
  transform: rotate(-90deg);
  z-index: 1000;
  width: 200px;
  left: -70px;


  .fa {
    @include center_y;
    right: 10px;
    color: white;
    font-size: 13px;
  }

  select {
    background: $corporate_1;
    color: white;
    font-size: 12px;
    text-transform: uppercase;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    border-radius: 0;
    width: 100%;
    border: 1px solid white;
    padding: 5px 30px 5px 15px;
    box-sizing: border-box;
  }
}


/* Work Form Contact */

.work_form {

  .content_subtitle_wrapper {
    margin: 0 0 10px;
    color: $corporate_1;

    .content_subtitle_title {
      font-size: 30px;

      span {
        color: $corporate_2;
      }
    }
  }

  form#contact {
    margin-bottom: 30px;

    .contInput {
      i {
        display: none;
      }

      select {
        font-size: 15px;
        height: 48px;
        background: #eaeaea;
        width: 100%;
        box-sizing: border-box;
        border: 0;
        border-radius: 5px;
        margin-top: 15px;
        padding-left: 25px;
        font-family: "Arial", sans-serif;
        appearance: none
      }

      &.selector_hotel, &.selector_job {
        position: relative;

        &:after {
          content: '\f0dc';
          font-family: 'FontAwesome';
          font-size: 10px;
          right: 15px;
          position: absolute;
          top: 35px;
        }
      }

      &.area {
        #uploadFile {
          border-radius: 5px 5px 0 0;
        }

        #file_cv {
          border-radius: 0 0 5px 5px;
          padding: 0 0 0 25px;
          margin-top: 0;
        }
      }

      &.extra_policies {
        text-align: left;
        font-size: 14px;
        margin-top: 20px;

        .extra_data_info {
          margin-bottom: 10px;

          input {
            vertical-align: top;
          }

          div {
            max-width: 90%;
            display: inline-block;
          }
        }

        a {
          color: $corporate_1;
          font-weight: 700;
        }

        label.error {
          font-size: 14px;
        }
      }
    }

    #contact-button {
      background: $corporate_2;
      margin: 20px auto;
      width: 100%;
      border: none;
      border-radius: 5px;
      color: white;
      font-size: 22px;
      text-transform: uppercase;
      padding: 10px 0;
      text-align: center;
      line-height: 1;
    }
  }
}

// Resident promocode
.resident_promocode_wrapper {
  width: 100%;
  display: block;
  clear: both;
  color: white;
  text-align: center;
  padding-top: 9px;
  margin-left: -14px;

  input {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 20px;
    border: 1px solid white;
    display: inline-block;
    vertical-align: middle;

    &:checked {
      background-color: white;
    }
  }

  label {
    display: inline-block!important;
    vertical-align: middle;
    font-size: 17px;
  }
}
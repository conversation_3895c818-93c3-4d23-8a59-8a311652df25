@media only screen and (max-width: 970px) {
  #slider_container .inner_slider .inner_picture {
    width: calc(100% - 30px);
  }
}

@media only screen and (max-width: 960px) {
  #rooms_list_wrapper {
    width: 100%;
  }
}

//
@media only screen and (max-width: 900px) {
  #custom_landing_element {
    table.calendar {
      width: 100%;
    }
  }
}

@media only screen and (min-width: 800px) {
  #rooms_list_wrapper {
    width: 100%;

    .selected_room_title {
      width: 100%;
    }

    .room_element {
      width: 100%;
      position: relative;

      .room_description_wrapper {
        padding-bottom: 0;

        .room_description {
          //height: 224px;
          height: auto;
        }

        .rooms_icons_wrapper {
          margin-top: 10px;
        }
      }

      .room_picture_wrapper {
        overflow: hidden;
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        height: auto;

        img {
          @include center_xy();
          max-width: none;
        }

        .see_more_room {
          width: 70px;
          height: 70px;
          font-size: 40px;
          line-height: 74px;
        }
      }
    }
  }

  #free-calendar-wrapper, #stay-selection-wrapper {
    display: table;
    padding: 0;

    #free-first-month, .first_month {
      width: 49.5%;
      float: left;
    }

    #free-second-month, .second_month {
      width: 49.5%;
      float: right;
    }
  }

  .first_step_wrapper, .second_step_wrapper {
    width: calc(100% - 30px);
    margin: auto;
  }

  #free-calendar-wrapper, #prices-calendar {
    padding: 0;

    table.calendar {
      width: 100%;
    }
  }

  #prices-calendar {
    .calendars_wrapper {
      #calendar-1 {
        width: 49%;
        float: left;
      }

      #calendar-2 {
        width: 49%;
        float: right;
      }
    }
  }

  .main_content_wrapper {
    .content_wrapper ul li {
      padding-left: 55px;
      position: relative;
      color: gray;
      font-size: 13px;
      display: table;
      padding-top: 5px;
      margin: 0 auto 20px;
      min-width: 300px;
    }

    .background_image {
      top: 25px !important;
    }
  }

  #slider_container {
    .inner_slider {
      .content_wrapper {
        .block_options {
          width: 600px;

          .block_element {
            padding: 20px 80px;
          }
        }
      }
    }
  }

  #progress_bar_wrapper {
    width: 820px;
    display: block;
    margin: auto;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
    box-sizing: border-box;
  }
}

@media only screen and (max-width: 800px) {
  body.patrick {
    #slider_container .inner_slider .inner_picture .slider_image {
      left: 66%;
    }
  }
}

@media only screen and (max-width: 800px) {
  .popup_month_selector {
    width: calc(100% - 30px);
    margin: 0 auto 10px;
  }

  #custom_landing_element {
    margin-bottom: 0;

    .rate_selector_wrapper {
      width: calc(49% - 15px);
      margin-left: 15px !important;
    }

    .boards_selector_wrapper {
      width: calc(49% - 15px);
      margin-right: 15px !important;
    }
  }

  #stay-selection-wrapper {
    padding: 0;
  }

  #slider_container {
    padding: 0;
  }

  #free-second-month {
    display: none !important;
  }

  .selected_room_title {
    width: calc(100% - 30px) !important;
  }

  #rooms_list_wrapper {
    .room_element {
      width: calc(100% - 30px) !important;

      .room_description_wrapper .room_description {
        height: 120px;
      }

      .rooms_icons_wrapper {
        display: none;
      }

      .room_picture_wrapper {
        height: 158px;
        overflow: hidden;

        img {
          @include center-xy();
          max-width: 420px;
        }
      }
    }
  }

  #slider_container {
    .inner_slider {
      height: 40vh;
    }
  }

  .hidden_popup_room {
    width: 94%;

    .owl-item {
      img {
        max-width: 470px;
        min-height: auto;
        width: auto;
      }
    }
  }

  .logotype_container .logo_image {
    max-width: 190px;
  }
}

@media only screen and (max-width: 650px) {
  #slider_container .inner_slider .slider_image {
    max-width: 900px;
    top: 50%
  }
}

@media only screen and (max-width: 570px) {
  body.patrick {
    #slider_container .inner_slider .inner_picture .slider_image {
      left: 85%;
    }
  }

  #slider_container .inner_slider {
    .content_wrapper {
      position: relative;
      top: auto;
      transform: none;

      .block_options {
          width: calc(100% - 30px);
      }
    }

    .inner_picture {
      height: auto;

      .slider_image {
        position: relative;
        top: auto;
        left: auto;
        -webkit-transform: none;
        -moz-transform: none;
        -ms-transform: none;
        -o-transform: none;
        transform: none;
        max-width: 100%;
        height: auto;
        min-height: auto;
        display: block;
      }
    }
  }
}

@media only screen and (max-width: 430px) {
  .logotype_container {
    padding: 5px 0;
    .logo_image {
      max-width: 160px;
    }
  }
  #custom_landing_element .day-content .price {
    font-size: 12px;

    .monedaConv {
      margin-left: 2px;
    }
  }
}

@media only screen and (max-width: 375px) {
  .title_block {
    font-size: 14px !important;
  }
}

@media only screen and (min-width: 1000px) {
  .content_wrapper {
    max-width: 1000px;
    margin: auto;
    left: 0;
    right: 0;
  }

  #custom_landing_element {
    .landing_center_element {
      width: 1000px;
      margin: auto;
    }
  }

  .rooms_icons_wrapper {
    i {
      margin-left: 20px !important;
    }
  }

  .icon_website_element {
    width: 49% !important;
  }

  .image_ico_wrapper {
    margin-left: 0 !important;
  }
}

{{ currency_selector|safe }}

<script>
    regimen_descriptions = {
        {% for regimen_element in regimens_info %}
            {% if regimen_element.regimenDescription.0.value %}
                '{{ regimen_element.key }}': '{{ regimen_element.regimenDescription.0.value }}',
            {% endif %}
        {% endfor %}
    }
</script>


<div id="custom_landing_element">
    <div class="landing_center_element">
        {% if start_label %}
            <div class="calendar_title">{{ start_label|safe }}</div>
        {% endif %}

        {% include "calendar_steps/free_night_select_2.html" %}

        {% include "calendar_steps/stay_nights_select_2.html" %}

        {% include "calendar_steps/personal_details_2.html" %}

        {% if step_1_label %}
            <input type="hidden" id="landing_calendar_step1" value="{{ step_1_label|safe }}">
        {% endif %}

        {% if step_2_label %}
            <input type="hidden" id="landing_calendar_step2" value="{{ step_2_label|safe }}">
        {% endif %}
    </div>
</div>


<div id="alert_error_message" style="display: none;">
    <h4 class="alert_title">{{ error_selection_message.subtitle|safe }}</h4>
    {{ error_selection_message.content|safe }}
</div>


{% if transfer_utm_source %}<input type="hidden" id="transfer_utm_source" value="{{ transfer_utm_source|safe }}">{% endif %}
{% if transfer_utm_medium %}<input type="hidden" id="transfer_utm_medium" value="{{ transfer_utm_medium|safe }}">{% endif %}
{% if confirmation_utm_source %}<input type="hidden" id="confirmation_utm_source" value="{{ confirmation_utm_source|safe }}">{% endif %}
{% if confirmation_utm_medium %}<input type="hidden" id="confirmation_utm_medium" value="{{ confirmation_utm_medium|safe }}">{% endif %}
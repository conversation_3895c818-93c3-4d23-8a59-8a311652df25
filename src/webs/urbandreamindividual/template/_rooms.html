<div class="rooms_wrapper">
    <div class="container12">
    {% for banner in rooms %}
        <div class="banner">
            <div class="banner_image">
                {% if banner.images %}
                    <div class="banner_image_carousel owl-carousel">
                        {% for pic in banner.images %}
                            <a href="{{ pic.servingUrl|safe }}=s1900" rel="lightbox[rooms_gallery]" class="gallery_icon">
                                <span class="search_icon_wrapper"><i class="fa fa-search"></i></span>
                                <img src="{{pic.servingUrl|safe}}=s1900" alt="{{pic.altText|safe}}">
                                </a>{% endfor %}
                    </div>
                {% else %}
                    <div class="banner_img_static">
                        <img src="{{banner.servingUrl|safe}}=s1900" alt="{{banner.altText|safe}}">
                    </div>
                {% endif %}

            </div>{% if banner.gallery %}{% endif %}<div class="banner_content">
                <div class="title_wrapper">
                    <span class="title">{{banner.title|safe}}</span>
                </div>
                <p class="text">{{banner.description|safe}}</p>

                {% if banner.room_icons|length > 1 %}
                    <div class="room_icons">
                    {% for service in banner.room_icons[:5] %}
                        <span class="tooltip">
                            <i class="fa {{ service.ico|safe }}"></i>
                            <span class="tooltiptext">{{ service.description|safe }}</span>
                        </span>
                    {% endfor %}
                    </div>
                {% endif %}
                <div class="banner_links">
                    {% if banner.linkUrl %}<a href="{{ banner.linkUrl|safe }}">{{ T_ver_mas_info }}</a>{% endif %}
                    <a href="#data" class="button_promotion">{{T_reservar}}</a>
                </div>
            </div>
        </div>
    {% endfor %}
    </div>
</div>


<script>
$(window).load(function () {
   $(".banner_image_carousel").owlCarousel({
        loop: true,
        nav: false,
        dots: false,
        items: 1,
        margin: 0,
        navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
        smartSpeed: 600,
        fluidSpeed: 600,
        navSpeed: 600,
        dotsSpeed: 600,
        dragEndSpeed: 600,
        autoplay: false
    });
});
</script>
.newsletter_and_icons_footer_wrapper {
  padding: 45px 0;
  display: inline-block;
  width: 100%;
  position: relative;
  box-sizing: border-box;

  * {
    box-sizing: border-box;
  }

  & > div {
    width: 100%;
    display: inline-block;
    padding: 30px 50px;
  }

  .newsletter_wrapper {
    .newsletter_container {
      width: auto;
      text-align: center;

      .newsletter_title {
        font-family: $title_family;
        font-weight: 100;
        font-size: 50px;
        color: $corporate_1;
      }

      .newsletter_description {
        font-size: 12px;
        line-height: 12px;
        width: 600px;
        margin: 10px auto 0;
      }

      .newsletter_form {
        margin-top: 20px;

        input {
          background: #DCDBDC;
          border-width: 0;
          text-align: center;
          width: 260px;
          height: 60px;
          margin-right: 10px;
          font-size: 16px;
          font-weight: 300;
          display: inline-block;
          vertical-align: top;

          &::-webkit-input-placeholder {
            //color: $corporate_3;
          }
          &::-moz-placeholder {
            //color: $corporate_3;
          }
          &:-ms-input-placeholder {
            //color: $corporate_3;
          }
          &:-moz-placeholder {
            //color: $corporate_3;
          }
        }

        .button_newsletter {
          display: inline-block;
          background: $corporate_2;
          color: white;
          font-size: 16px;
          text-transform: uppercase;
          vertical-align: top;
          width: 260px;
          padding: 20px 0;
          margin-right: 30px;
          text-align: center;
          position: relative;
          cursor: pointer;

          &:hover:before {
            width: 100%;
            background: #444444;
          }

          &:before {
            content: "";
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 0;
            //background: darken($corporate_3, 10%);
            z-index: 1;
            @include transition(width, .4s)
          }

          span {
            position: relative;
            z-index: 2;
          }
        }
      }

      .newsletter_checkbox {
         margin-top: 5px;
         font-size: 14px;

         a {
            color: $corporate_1;
            text-decoration: underline;
         }
      }

      input#privacy, input#promotions {
        width: auto;
        height: auto;
      }

      .social_newsletter {
        display: inline-block;
        vertical-align: top;
        margin-top: 10px;

        a {
          display: inline-block;
          vertical-align: middle;
          color: $corporate_1;
          font-size: 32px;
          margin: 0 5px;
          @include transition(background, .4s);
          &:hover {
            color: $corporate_2;
          }
        }
      }
    }
  }
}

footer {
  background-color: $corporate_1;
  padding: 65px 0;
  .logo_footer {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    max-width: 250px;
    margin-right: 50px;
  }
  .legal_text {
    display: inline-block;
    vertical-align: middle;
    width: 400px;
    font-size: 16px;
    line-height: 25px;
    color: white;
  }
  .footer_columns {
    display: inline-block;
    vertical-align: middle;
    text-align: center;
    width: 430px;
    a {
      display: inline-block;
      vertical-align: middle;
      position: relative;
      img {
        max-width: 120px;
        max-height: 60px;
      }
      .tooltiptext {
        visibility: hidden;
        opacity: 0;
        @include center_x;
        bottom: calc(100% + 15px);
        width: 170px;
        background-color: rgba($corporate_3, 0.6);
        color: #fff;
        text-align: center;
        padding: 5px 0;
        z-index: 1;
        transition: opacity 0.3s;
        font-size: 15px;
        &:after {
          content: '';
          @include center_x;
          bottom: 0;
          margin-bottom: -10px;
          border-width: 5px;
          border-style: solid;
          border-color: rgba(0, 0, 0, 0.6) transparent transparent transparent;
        }
      }
      &:hover{
        .tooltiptext {
          visibility: visible;
          opacity: 1;
        }
      }
    }
  }
  .footer_legal_text_wrapper {
    border-top: 2px solid rgba(white, .3);
    margin-top: 30px;
    padding-top: 30px;
    #main-sections-inner {
      text-align: center;
      margin-bottom: 20px;
      .main-section-div-wrapper {
        display: inline-block;
        a {
          padding: 10px 20px;
          display: block;
          color: white;
          font-weight: lighter;
          text-transform: uppercase;
        }
      }
    }
    .footer_links_wrapper {
      text-align: center;
      a {
        color: white;
        letter-spacing: 1px;
        font-size: 14px;
        padding: 0 5px 0 2px;
        display: inline-block;
        border-right: 1px solid white;
        &:last-of-type {
          border-right-width: 0;
        }
      }
    }
  }
}
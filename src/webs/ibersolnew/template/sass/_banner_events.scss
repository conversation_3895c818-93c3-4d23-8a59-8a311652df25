.banner_events_wrapper {
  padding: 50px calc((100% - 1140px) / 2);
  background-color: $corporate_4;
  .divider {
    height: 1px;
    background-color: #DDD;
    margin: 20px auto;
  }
  .banner_event_element {
    .banner_event_title {
      position: relative;
      font-weight: 600;
      font-size: 20px;
      color: #222;
      margin-bottom: 10px;
      cursor: pointer;
      background-color: white;
      span {
        display: inline-block;
        vertical-align: middle;
        width: 350px;
        text-align: center;
        text-transform: uppercase;
        color: white;
        background-color: $corporate_1;
        padding: 20px;
        margin-right: 10px;
      }
      i.fa {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 50px;
        background-color: $corporate_1;
        color: white;
        &:before {
          @include center_xy;
        }
      }
      &.active {
        i.fa {
          &:before {
            content: '\f106';
            display: inline-block;
            font: normal normal normal 14px/1 FontAwesome;
            text-rendering: auto;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }
        }
      }
    }
    .banner_hotel {
      display: none;
        margin-bottom: 20px;
      .hotel {
        background-color: white;
        cursor: pointer;
        &:hover .hotel_info {
          &:before {
            opacity: 0;
          }
          .content {
            opacity: 0;
          }
          .see_more {
            opacity: 1;
            i.fa {
              width: 70px;
              height: 70px;
              opacity: 1;
            }
             &:after {
              width: 70px;
              height: 70px;
              opacity: 1;
            }
            &:before {
              width: 150px;
              height: 150px;
              opacity: 0;
            }
          }
          .more_info {
            background-color: $corporate_2;
          }
        }
        .hotel_info {
          position: relative;
          z-index: 2;
          display: inline-block;
          vertical-align: middle;
          width: 350px;
          height: 240px;
          overflow: hidden;
          img {
            @include center_image;
            z-index: -1;
          }
          &:before {
            content: '';
            @include full_size;
            background-color: rgba($corporate_2,.8);
            @include transition(opacity, 1.6s);
          }
          .see_more {
            @include center_xy;
            margin-top: -20px;
            opacity: 0;
            i.fa {
              @include center_xy;
              color: $corporate_1;
              font-size: 30px;
              width: 0;
              height: 0;
              border-radius: 50%;
              opacity: 0;
              background-color: rgba($corporate_2,.8);
              @include transition(all, 2s);
              &:before {
                @include center_xy;
              }
            }
            &:before, &:after {
              content: '';
              @include center_xy;
              width: 0;
              height: 0;
              overflow: hidden;
              border-radius: 50%;
              opacity: 0;
              border: 2px solid white;
              @include transition(all, 1s);
            }
            &:before {
              opacity: 1;
              @include transition(all, 1.5s);
            }
          }
          .more_info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 10px;
            text-transform: uppercase;
            text-align: center;
            color: white;
            font-weight: 600;
            letter-spacing: 2px;
            background-color: $corporate_1;
            font-size: 13px;
            @include transition(background, 1s);
          }
          .content {
            @include center_xy;
            color: white;
            width: 100%;
            text-align: center;
            font-weight: 600;
            margin-top: -20px;
            @include transition(opacity, .3s);
            .title {
              font-size: 30px;
            }
            .region {
              font-size: 18px;
              font-weight: 400;
              color: $corporate_1;
              &:before {
                content: '';
                display: block;
                width: 30px;
                height: 2px;
                background-color: white;
                margin: 10px auto;
              }
            }
          }
        }
        .hotel_extra {
          display: inline-block;
          vertical-align: middle;
          padding: 20px;
          width: calc(100% - 350px);
          font-size: 13px;
          line-height: 20px;
        }
      }
    }
    .banner_event_content_events {
      position: relative;
    }
    .events {
      position: relative;
      width: 100%;
      z-index: 2;
      display: none;
      .event {
        position: relative;
        margin: 10px auto;
        background-color: white;
      }
      .event_image {
        width: 350px;
        overflow: hidden;
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        img {
          @include center_image;
        }
      }
      .event_content {
        margin-right: 350px;
        padding: 40px 20px 20px 20px;
        font-size: 13px;
        line-height: 20px;
        strong {
          font-weight: 600;
        }
        .title {
          font-size: 30px;
          font-weight: 600;
          &:after {
            content: '';
            display: block;
            width: 100px;
            height: 3px;
            background-color: $corporate_1;
            margin: 10px 0 20px;
          }
        }
        .event_links {
          padding: 20px 0 10px;
          a {
            position: relative;
            display: inline-block;
            padding: 10px 20px;
            margin-bottom: 10px;
            text-transform: uppercase;
            font-weight: 600;
            border-radius: 30px;
            background-color: $corporate_1;
            color: white;
            &:hover {
              i.fa {
                &:after {
                  z-index: 2;
                  width: 100px;
                  height: 100px;
                  opacity: 0;
                }
              }
              &:after {
                opacity: .5;
              }
            }
            &:after {
              content: '';
              @include full_size;
              background-color: rgba(white,.3);
              opacity: 0;
              @include transition(opacity,1.6s);
            }
            i.fa {
              position: relative;
              margin-right: 10px;
              font-size: 20px;
              vertical-align: middle;
              &:after {
                content: '';
                @include center_xy;
                z-index: -1;
                border: 1px solid white;
                width: 0;
                height: 0;
                border-radius: 50%;
                @include transition(all, 1.6s);
              }
            }
            &.dark {
              background-color: $corporate_2;
            }
          }
        }
      }

    }
  }
}
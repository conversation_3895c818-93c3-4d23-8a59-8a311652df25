# -*- coding: utf-8 -*-
from paraty_commons_3.data_structures.advanced_dictionaries import ImmutableDict

morgado = {
	'prefix': 'nau',
	'id': 'nau-morgado',
	'db_connect': '',
	'name': u'NAU Morgado Golf & Country Club',
	'value': u'NAU Morgado Golf & Country Club',
	'short_name': 'Morgado Golf',
	'class': 'morgado',
	'namespace': 'nau-morgado',
	'mail': 'Correo Morgado',
	'bookingUrl': '-dot-nau-hotels.appspot.com/booking1',
	'url_booking': '-dot-nau-hotels.appspot.com/booking1',
	'domain': 'https://nau-morgado-dot-nau-hotels.appspot.com',
	'stars': '****',
	'city': u"Portimão"
}

gobernador = {
	'prefix': 'nau',
	'id': 'nau-governador',
	'db_connect': '',
	'name': u'NAU Palácio do Governador',
	'value': u'NAU Palácio do Governador',
	'short_name': 'Palacio Governador',
	'class': 'governador',
	'namespace': 'nau-governador',
	'mail': 'Correo Governador',
	'bookingUrl': '-dot-nau-hotels.appspot.com/booking1',
	'url_booking': '-dot-nau-hotels.appspot.com/booking1',
	'domain': 'https://nau-governador-dot-nau-hotels.appspot.com',
	'stars': '*****',
	'city': u"Belém"
}

atlantico = {
	'prefix': 'nau',
	'id': 'nau-rafael-atlantico',
	'db_connect': '',
	'name': u'NAU São Rafael Atlântico',
	'value': u'NAU São Rafael Atlântico',
	'short_name': 'Rafael Atlantico',
	'class': 'atlantico',
	'namespace': 'nau-rafael-atlantico',
	'mail': 'Correo Rafael Atlantico',
	'bookingUrl': '-dot-nau-hotels.appspot.com/booking1',
	'url_booking': '-dot-nau-hotels.appspot.com/booking1',
	'domain': 'https://nau-rafael-atlantico-dot-nau-hotels.appspot.com',
	'stars': '*****',
	'city': 'Albufeira'
}

suites = {
	'prefix': 'nau',
	'id': 'nau-rafael-suites',
	'db_connect': '',
	'name': u'NAU São Rafael Suites',
	'value': u'NAU São Rafael Suites',
	'short_name': 'Rafael Suites',
	'class': 'suites',
	'namespace': 'nau-rafael-suites',
	'mail': 'Correo Rafael Suites',
	'bookingUrl': '-dot-nau-hotels.appspot.com/booking1',
	'url_booking': '-dot-nau-hotels.appspot.com/booking1',
	'domain': 'https://nau-rafael-suites-dot-nau-hotels.appspot.com',
	'stars': '*****',
	'city': "Albufeira"
}

salema_beach = {
	'prefix': 'nau',
	'id': 'nau-salema',
	'db_connect': '',
	'name': u'NAU Salema Beach Village',
	'value': u'NAU Salema Beach',
	'short_name': 'Salema Beach',
	'class': 'salema-beach',
	'namespace': 'nau-salema',
	'mail': 'Correo Salema Beach',
	'bookingUrl': '-dot-nau-hotels.appspot.com/booking1',
	'url_booking': '-dot-nau-hotels.appspot.com/booking1',
	'domain': 'https://nau-salema-dot-nau-hotels.appspot.com',
	'city': "Vila do Bispo"
}

salgados_dunas = {
	'prefix': 'nau',
	'id': 'nau-dunas',
	'db_connect': '',
	'name': u'NAU Salgados Dunas Suites',
	'value': u'NAU Salgados Dunas Suites',
	'short_name': 'Salgados Dunas',
	'class': 'salgados-dunas',
	'namespace': 'nau-dunas',
	'mail': 'Correo Salgados Dunas',
	'bookingUrl': '-dot-nau-hotels.appspot.com/booking1',
	'url_booking': '-dot-nau-hotels.appspot.com/booking1',
	'domain': 'https://nau-dunas-dot-nau-hotels.appspot.com',
	'stars': '*****',
	'city': "Albufeira"
}

salgados_palace = {
	'prefix': 'nau',
	'id': 'nau-salgados-palace',
	'db_connect': '',
	'name': u'NAU Salgados Palace',
	'value': u'NAU Salgados Palace',
	'short_name': 'Salgados Palace',
	'class': 'salgados-palace',
	'namespace': 'nau-salgados-palace',
	'mail': 'Correo Salgados Palace',
	'bookingUrl': '-dot-nau-hotels.appspot.com/booking1',
	'url_booking': '-dot-nau-hotels.appspot.com/booking1',
	'domain': 'https://nau-salgados-palace-dot-nau-hotels.appspot.com',
	'stars': '*****',
	'city': "Albufeira"
}

salgados_palm = {
	'prefix': 'nau',
	'id': 'nau-salgados-palm',
	'db_connect': '',
	'name': u'NAU Salgados Palm Village',
	'value': u'NAU Salgados Palm Village',
	'short_name': 'Salgados Palm Village',
	'class': 'salgados-palm',
	'namespace': 'nau-salgados-palm',
	'mail': 'Correo Salgados Palm Village',
	'bookingUrl': '-dot-nau-hotels.appspot.com/booking1',
	'url_booking': '-dot-nau-hotels.appspot.com/booking1',
	'domain': 'https://nau-salgados-palm-dot-nau-hotels.appspot.com',
	'city': "Albufeira"
}

salgados_vila = {
	'prefix': 'nau',
	'id': 'nau-salgados-lagoas',
	'db_connect': '',
	'name': u'NAU Salgados Vila das Lagoas',
	'value': u'NAU Salgados Vila das Lagoas',
	'short_name': 'Salgados Vila Lagoas',
	'class': 'salgados-vila',
	'namespace': 'nau-salgados-lagoas',
	'mail': 'Correo Salgados Vila Lagoas',
	'bookingUrl': '-dot-nau-hotels.appspot.com/booking1',
	'url_booking': '-dot-nau-hotels.appspot.com/booking1',
	'domain': 'https://nau-salgados-lagoas-dot-nau-hotels.appspot.com',
	'city': "Albufeira"
}

lisboa = {
	'prefix': 'nau',
	'id': 'nau-lisboa',
	'db_connect': '',
	'name': u'NAU Vintage Lisboa',
	'value': u'NAU Vintage Lisboa',
	'short_name': 'Vintage Lisboa',
	'class': 'vintage',
	'namespace': 'nau-lisboa',
	'mail': 'Correo Vintage Lisboa',
	'bookingUrl': '-dot-nau-hotels.appspot.com/booking1',
	'url_booking': '-dot-nau-hotels.appspot.com/booking1',
	'domain': 'https://nau-lisboa-dot-nau-hotels.appspot.com',
	'stars': '*****',
	'city': u"Belém"
}





all_namespaces = [morgado, gobernador, atlantico, suites, salema_beach, salgados_dunas, salgados_palace, salgados_palm, salgados_vila]#, lisboa]
all_hotels_namespace = [morgado, gobernador, atlantico, suites, salema_beach, salgados_dunas, salgados_palace, salgados_palm] #, salgados_vila #, lisboa]

morgadoMob = ImmutableDict({
'id': 'https://nau-morgado-dot-nau-hotels.appspot.com/booking1',
'url_booking': '-dot-nau-hotels.appspot.com/booking1',
'applicationId': 'nau-morgado',
'namespace': 'nau-morgado',
'domain': 'https://nau-morgado-dot-nau-hotels.appspot.com',
'value':u'NAU Morgado Golf & Country Club'
})

gobernadorMob = ImmutableDict({
'id': 'https://nau-governador-dot-nau-hotels.appspot.com/booking1',
'url_booking': '-dot-nau-hotels.appspot.com/booking1',
'applicationId': 'nau-governador',
'namespace': 'nau-governador',
'domain': 'https://nau-governador-dot-nau-hotels.appspot.com',
'value':u'NAU Palácio do Governador'
})

atlanticoMob = ImmutableDict({
'id': 'https://nau-rafael-atlantico-dot-nau-hotels.appspot.com/booking1',
'url_booking': '-dot-nau-hotels.appspot.com/booking1',
'applicationId': 'nau-rafael-atlantico',
'namespace': 'nau-rafael-atlantico',
'domain': 'https://nau-rafael-atlantico-dot-nau-hotels.appspot.com',
'value':u'NAU São Rafael Atlântico'
})

suitesMob = ImmutableDict({
'id': 'https://nau-rafael-suites-dot-nau-hotels.appspot.com/booking1',
'url_booking': '-dot-nau-hotels.appspot.com/booking1',
'applicationId': 'nau-rafael-suites',
'namespace': 'nau-rafael-suites',
'domain': 'https://nau-rafael-suites-dot-nau-hotels.appspot.com',
'value':u'NAU São Rafael Suites'
})

salemaBeachMob = ImmutableDict({
'id': 'https://nau-salema-dot-nau-hotels.appspot.com/booking1',
'url_booking': '-dot-nau-hotels.appspot.com/booking1',
'applicationId': 'nau-salema',
'namespace': 'nau-salema',
'domain': 'https://nau-salema-dot-nau-hotels.appspot.com',
'value':u'NAU Salema Beach Village'
})

salgadosDunasMob = ImmutableDict({
'id': 'https://nau-dunas-dot-nau-hotels.appspot.com/booking1',
'url_booking': '-dot-nau-hotels.appspot.com/booking1',
'applicationId': 'nau-dunas',
'namespace': 'nau-dunas',
'domain': 'https://nau-dunas-dot-nau-hotels.appspot.com',
'value':u'NAU Salgados Dunas Suites'
})

salgadosPalaceMob = ImmutableDict({
'id': 'https://nau-salgados-palace-dot-nau-hotels.appspot.com/booking1',
'url_booking': '-dot-nau-hotels.appspot.com/booking1',
'applicationId': 'nau-salgados-palace',
'namespace': 'nau-salgados-palace',
'domain': 'https://nau-salgados-palace-dot-nau-hotels.appspot.com',
'value':u'NAU Salgados Palace'
})

salgadosPalmMob = ImmutableDict({
'id': 'https://nau-salgados-palm-dot-nau-hotels.appspot.com/booking1',
'url_booking': '-dot-nau-hotels.appspot.com/booking1',
'applicationId': 'nau-salgados-palm',
'namespace': 'nau-salgados-palm',
'domain': 'https://nau-salgados-palm-dot-nau-hotels.appspot.com',
'value':u'NAU Salgados Palm Village'
})

salgadosVilaMob = ImmutableDict({
'id': 'https://nau-salgados-lagoas-dot-nau-hotels.appspot.com/booking1',
'url_booking': '-dot-nau-hotels.appspot.com/booking1',
'applicationId': 'nau-salgados-lagoas',
'namespace': 'nau-salgados-lagoas',
'domain': 'https://nau-salgados-lagoas-dot-nau-hotels.appspot.com',
'value':u'NAU Salgados Vila Lagoas'
})

lisboaMob_hotel = ImmutableDict({
'id': 'https://nau-lisboa-dot-nau-hotels.appspot.com/booking1',
'url_booking': '-dot-nau-hotels.appspot.com/booking1',
'applicationId': 'nau-lisboa',
'namespace': 'nau-lisboa',
'domain': 'https://nau-lisboa-dot-nau-hotels.appspot.com',
'value':u'NAU Vintage Lisboa'
})

booking0_algarve = {
'id': 'https://nau-corporate-dot-nau-hotels.appspot.com/booking0',
'applicationId': 'nau-rafael-suites;nau-rafael-atlantico;nau-dunas;nau-salgados-palace;nau-salgados-palm;nau-salgados-lagoas;nau-morgado;nau-salema',
'domain': 'https://nau-corporate-dot-nau-hotels.appspot.com',
'value':u''
}

booking0_lisboa = {
'id': 'https://nau-corporate-dot-nau-hotels.appspot.com/booking0',
'applicationId': 'nau-lisboa;nau-governador',
'domain': 'https://nau-corporate-dot-nau-hotels.appspot.com',
'value':u''
}

#Groups Selector
portugal_location = ImmutableDict({"group_class": "portugal",
	"group_label": u'PORTUGAL',
	"group_list": [morgado, gobernador, atlantico, suites, salema_beach, salgados_dunas, salgados_palace, salgados_palm, salgados_vila],# lisboa],
	"group_id": "Portugal",
})

algarve = ImmutableDict({"group_class": "algarve",
						"group_label": u'Algarve',
						# "group_list": [suites, atlantico, salgados_dunas, salgados_palace, salgados_palm, salgados_vila, morgado, salema_beach],
						"group_list": [suites, atlantico, salgados_dunas, salgados_palace, salgados_palm, morgado, salema_beach],
						"group_id": u"Algarve"
})

lisboa_site = ImmutableDict({"group_class": "lisboa",
						"group_label": u'Lisboa',
						"group_list": [gobernador], #lisboa
						"group_id": u"Lisboa"
})

algarveMob = ImmutableDict({"group_class": "algarve",
						"group_label": u'Algarve',
						# "group_list": [suitesMob, atlanticoMob, salgadosDunasMob, salgadosPalaceMob, salgadosPalmMob, salgadosVilaMob, morgadoMob, salemaBeachMob],
						"group_list": [suitesMob, atlanticoMob, salgadosDunasMob, salgadosPalaceMob, salgadosPalmMob, morgadoMob, salemaBeachMob],
						"group_id": u"Algarve"
})

lisboaMob = ImmutableDict({"group_class": "lisboa",
						"group_label": u'Lisboa',
						"group_list": [gobernadorMob], #lisboaMob
						"group_id": u"Lisboa"
})

all_hotels_mob = {
	'alone_option': True,
	'id': 'https://nau-corporate-dot-nau-hotels.appspot.com/booking0',
	'applicationId': 'nau-morgado;nau-governador;nau-rafael-atlantico;nau-rafael-suites;nau-salema;nau-dunas;nau-salgados-palace;nau-salgados-palm;nau-salgados-lagoas',#;nau-lisboa',
	'domain': 'https://nau-corporate-dot-nau-hotels.appspot.com',
	'value': u''
}


group_hotels_list = [portugal_location, algarve, lisboa_site]
group_hotels_list_mybookings = [algarve, lisboa_site]
{% if hoteles_title.subtitle %}
    <h2 class="title-section">
        {{ hoteles_title.subtitle|safe }}
    </h2>
{% endif %}
<section class="filter-hotel in-page">
    <div class="filter-list container12">
        {% for filter, values in rooms_filter.items %}
            <div class="filter_wrapper">
                <select class="filter_select" value-group="{{ filter }}">
                    <option value=".all" selected disabled="">{{ filter }}</option>
                    <option value=".all">{{ T_todos }}</option>
                    {% for val in values %}
                        {% if val %}<option value=".{{ val|slugify }}">{{ val|safe }}</option>{% endif %}
                    {% endfor %}
                </select>
                <i class="fa fa-angle-down"></i>
            </div>
        {% endfor %}
    </div>
</section>

<section class="hotels-blocks in-page">
    <div class="wrapper-hotels-blocks container12">
        <div class="hotel-grid grid cleardiv" id="hotel-grid">
            {% for hotel in rooms_information %}
                <div class="block-hotel all {{ hotel.location|slugify }}
                {% for service in hotel.services %}{{ service|slugify }} {% endfor %}
                {% for interest in hotel.interest %}{{ interest|slugify }} {% endfor %}
                {% for event_type in hotel.event_type %}{{ event_type|slugify }} {% endfor %}
                {% for capacity in hotel.capacity %}{{ capacity|slugify }} {% endfor %}
                {% for areas in hotel.areas %}{{ areas|slugify }} {% endfor %}
                {% for regimen in hotel.regimen %}{{ regimen|slugify }} {% endfor %}">
                    <div class="image-box">
                        <img src="{{ hotel.servingUrl|safe }}" alt=""/>
                    </div>
                    <div class="description-box">
                        {{ hotel.description|safe }}
                        <a href="{{ hotel.linkUrl|safe }}{{ language }}/" target="_blank" class="individual_hotel_link"><div class="button_link_hotel">{% if hotel.see_hotel %}{{ hotel.see_hotel|safe }}{% else %}{{ T_ver_hotel }}{% endif %}</div></a>
                    </div>
                    <a href="#data" class="button-promotion" data-hotelname="{{ hotel.hotel_name|safe }}" data-namespace="{{ hotel.namespace }}">{{ T_reservar }}</a>

                </div>

            {% endfor %}
        </div>
    </div>
</section>

<script src="/static_1/lib/isotope/isotope.min.js"></script>
<script>
    $(window).load(function() {
        var $grid = $('.hotel-grid.grid').isotope({
            itemSelector: '.block-hotel'
        });
        var filters = {};

        $('select.filter_select').on('change', function (event) {
            var $select = $(event.target);
            var filterGroup = $select.attr('value-group');
            filters[filterGroup] = event.target.value;
            var filterValue = concatValues(filters);
            $grid.isotope({filter: filterValue});
        });

        function concatValues(obj) {
            var value = '';
            for (var prop in obj) {
                value += obj[prop];
            }
            return value;
        }
    });

</script>


{% if blocks_destiny %}

<section class="destiny-wrapper cleardiv">
    {% for banner in blocks_destiny %}
        {% if banner.linkUrl %}<a href="{{ banner.linkUrl|safe }}{% if banner.link_extra %}{{ banner.link_extra|safe }}{% endif %}">{% endif %}
            <div class="block-destiny">
                <h2>{{ banner.title|safe }}</h2>
                <img src="{{ banner.servingUrl|safe }}=s800" alt=""/>
            </div>
        {% if banner.linkUrl %}</a>{% endif %}
    {% endfor %}
</section>
    
{% endif %}
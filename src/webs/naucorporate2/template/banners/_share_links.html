<div class="share_links_main_wrapper">
    <div class="share_links_title"><i class="fas fa-share-alt"></i>{{ T_compartir|safe }}:</div>
    <div class="share_links">
        <a href="" target="_blank" class="share_link mail">
            <i class="fa fa-envelope"></i>
        </a>
        <a href="" target="_blank" class="share_link facebook">
            <i class="fa fa-facebook"></i>
        </a>
        <a href="" target="_blank" class="share_link whatsapp">
            <i class="fa fa-whatsapp"></i>
        </a>
        <a href="" target="_blank" class="share_link telegram">
            <i class="fa fa-telegram"></i>
        </a>
        <a class="share_link link">
            <i class="fa fa-link"></i>
            <span class="copy_tool" style="display: none">{{ T_copia_url }}</span>
        </a>
    </div>
</div>

<script>
    $(window).on("load", function () {
        const $temp = $("<input>"),
              $url = $(location).attr('href'),
              wha_url = "https://wa.me/?text=Vaga de Emprego: ",
              fc_url = "https://www.facebook.com/dialog/share?app_id=140586622674265&display=popup&href=",
              tg_url = "https://t.me/share/url?url=",
              mail_url = "mailto:?";

        $(".share_link:not(.link)").each(function () {

            // whatsapp //
            if ($(this).hasClass("whatsapp")) {
                $(this).attr("href", wha_url + $url);
            }
            // facebook //
            if ($(this).hasClass("facebook")) {
                $(this).attr("href", fc_url + $url);
            }
            // telegram //
            if ($(this).hasClass("telegram")) {
                $(this).attr("href", tg_url + $url);
            }
            // mail //
            if ($(this).hasClass("mail")) {
                let final_mail_url = mail_url + "subject=Confira esta vaga na Nau Hotels" + "&body=" + $url;
                $(this).attr("href", final_mail_url);
            }
        });

        $('.share_link.link').on('click', function() {
          $("body").append($temp);
          $temp.val($url).select();
          document.execCommand("copy");
          $temp.remove();
          $(".copy_tool").fadeIn();
          window.setTimeout(function () {
                $(".copy_tool").fadeOut();
            }, 1500);
        })
    });
</script>
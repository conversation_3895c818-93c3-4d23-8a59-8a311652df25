.rooms_render_content {
  box-sizing: border-box;
  padding: 0 30px;
  * {
    box-sizing: border-box;
  }
  .iframe_form {
    iframe {
      width: 100%;
      border-width: 0;
    }
  }
  .rooms_wrapper {
    margin-top: 0;
    #add_room_filters {
      position: relative;
      width: 100%;
      background-color: $black;
      text-align: center;
      height: 50px;
      color: white;
      font-size: 18px;
      line-height: 25px;
      text-transform: uppercase;
      padding: 11px;
      i:not(.arrow_down) {
        @include center_y;
        left: 10px;
        font-size: 32px;
      }
      i.arrow_down {
        position: absolute;
        top: 0;
        right: 0;
        display: block;
        width: 50px;
        height: 50px;
        font-size: 32px;
        color: white;
        background-color: $corporate_2;
        &:before {
          @include center_xy;
        }
      }
    }
    .select_filter {
      position: relative;
      width: calc(100% - 40px);
      margin: 0 auto;
      display: none;

      .filter_wrapper {
        position: relative;
        width: calc(100% - 20px);
        display: block;
        overflow: hidden;
        margin: 10px auto;
        font-family: $text_family;
        .selectricHideSelect, .selectricInput {
          display: none;
        }
        .selectric {
          position: relative;
          z-index: 5;
          width: 100%;
          background-color: transparent;
          text-align: center;
          border: 1px solid $black;
          padding: 11px 11px 11px 40px;
          height: 48px;
          margin: 0;
          &:before {
            content: '\f3c5';
            font-family: "Font Awesome 5 Pro";
            font-weight: 400;
            @include center_y;
            left: 10px;
            font-size: 24px;
            color: $corporate_2;
          }
          .label {
            margin: 0;
            font-size: 14px;
            color: $corporate_2;
            line-height: 22px;
            font-weight: 400;
            text-transform: uppercase;
          }
          .button {
            position: absolute;
            top: -1px;
            right: -1px;
            display: block;
            width: 48px;
            height: 48px;
            font-size: 0;
            background: $corporate_1 !important;
            border-radius: 0 5px 5px 0;
            &:before {
              content: '\f175';
              font-family: "Font Awesome 5 Pro";
              font-weight: 300;
              @include center_xy;
              font-size: 32px;
              color: white;
            }
          }
        }
        .selectricWrapper {
          width: 100% !important;
          &.filter_select.selectricOpen{
            z-index: 1;
          }
          .selectricItems {
            width: 100% !important;
            margin: 0;
            display: none;
            position: relative;
            top: auto !important;
            background: transparent;
            border: none;
            ul {
              width: 100%;
              margin-top: 10px;
              li {
                width: 100%;
                background-color: white;
                background-image: none;
                text-align: center;
                border: 1px solid $black;
                padding: 11px;
                margin: 0;
                font-size: 13px !important;
                color: $corporate_2;
                line-height: 18px !important;
                font-weight: 400;
                list-style: none;
                text-transform: uppercase;
                &:not(:last-of-type) {
                  margin-bottom: 7px;
                }
              }
            }
          }
          &.selectricOpen {
            .selectricItems {
              display: block;
              height: auto !important;
            }
          }
        }
        &:nth-of-type(3) {
          .selectric {
            &:before {
              content: '\e900';
              font-family: 'icomoon';
              font-size: 28px;
            }
          }
        }
        &:nth-of-type(2) {
          .selectric {
            &:before {
              content: '\f5c5';
              left: 5px;
            }
          }
        }
        &:nth-of-type(4) {
          .selectric {
            &:before {
              content: '\f7a2';
            }
          }
        }
      }
    }

    .room_element {
      width: 100%;
      .rooms_slider {
        height: auto;
        padding-bottom: 0;
        .pic {
          position: relative;
          overflow: hidden;
          width: 100%;
          display: block;
          height: 200px;
          img {
            @include center-image;
            max-height: 101%;
            width: auto;
          }
        }
      }
      .room_content {
        text-align: center;
        border: 1px solid $corporate_1;
        border-width: 0 1px 0 1px;
        .room_title {
          display: none;
          & ~ h2, & ~ h3 {
            display: none;
          }
        }
        .room_description, .oportunity_description {
          padding: 20px 10px;
          text-align: left;
          .element-hide {
            display: none;
          }
          .location {
            font-size: 16px;
            line-height: 20px;
            font-weight: 300;
            margin: 0 0 10px;
          }
          .title {
            font-size: 16px;
            line-height: 20px;
            font-weight: 400;
            font-family: $title_family;
            margin: 0 0 10px;
            color: $corporate_1;
          }
          .content {
            margin: 0;
            font-size: 12px;
            font-weight: 300;
            padding: 0;
            line-height: 20px;
          }
          .see_more_room {
            color: $corporate_3;
          }
        }

        .room_btn {
          padding-bottom: 20px;
          .btn_personalized_1 {
            width: 200px;
            margin-bottom: 5px;
            padding: 7px 40px 7px 75px;
            white-space: nowrap;
          }
        }
      }

      .booking_general_button {
        display: block;
        background: $corporate_2;
        color: white;
        font-size: 25px;
        padding: 10px 0;
        font-weight: 300;
        letter-spacing: 1px;
        text-transform: uppercase;
        text-align: center;
        cursor: pointer;
      }
    }
  }
}
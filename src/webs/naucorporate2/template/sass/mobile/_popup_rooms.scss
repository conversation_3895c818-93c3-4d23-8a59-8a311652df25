@import "booking_mobile/v1/_mixins.scss";

#main_modal {
  position: fixed !important;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: 50;
  transform: translateY(100%);
  transition: all .2s;

  .overlay_modal {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    transform: translateY(100%);
    opacity: 0;
    z-index: 55;

    &.dark {
      background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, .8) 40%); /* w3c */
      filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#ffffff', endColorstr='#000000', GradientType=0);
    }
  }

  .body_modal {
    background-color: white;
    position: fixed;
    border-radius: 4px 4px 0 0;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 60;
    transform: translateY(100%);
    transition: all .3s;
    transition-delay: .3s;

    .close_modal {
      position: absolute;
      top: 10px;
      right: 15px;
      width: 30px;
      height: 30px;
      z-index: 70;
      transform: rotate(0deg);
      opacity: 0;
      transition: all .5s;
      transition-delay: 1s;
      @include has_icon_before ('\f00d', 50%, 50%, -50%, -50%, 30px, #333)
    }

    .resize_modal {
      position: absolute;
      background-color: darken(lighten(#e2e2e2, 5%), 20%);
      height: 5px;
      width: 75px;
      border-radius: 8px;
      left: 50%;
      transform: translateX(-50%);
    }
  }

  &.active {
    display: block;
    transform: translateY(0);

    .overlay_modal {
      transform: translateY(-30%);
      opacity: .8;
      transition: all 0.6s;
    }

    .body_modal {
      .body_modal_content {
        .popup_title {
          font-size: 23px;
          margin-bottom: 12px;
          color: $corporate_1;
          font-family: 'Droid Serif', serif;
        }
        .popup_description {
          font-size: 15px;
        }
        .popup_services li, .rooms_number .room_types li {
          text-align: left;
          font-size: 15px;
        }
        .popup_rooms {
          padding-bottom: 60px;
        }
        .rooms_number .rooms_total_number {
          font-size: 15px;
          text-align: left;
          margin-bottom: 15px;
          display: block;
        }
      }
      &.rooms_features_modal_wrapper {
        transform: translateY(0%);

        .resize_modal {
          @media (min-width: 769px) {
            top: 435px;
          }

          @media (min-width: 576px) and (max-width: 768px) {
            top: 395px;
          }

          @media (max-width: 575px) {
            top: 255px;
          }
        }

        .close_modal {
          &::before {
            color: white;
          }
        }
      }

      &.rooms_content, &.iframe_modal_wrapper {
        transform: translateY(20%);
        padding-top: 50px;

        .resize_modal {
          top: 15px;
        }

        .body_modal_content {
          padding: 0 30px 15px;
          overflow-y: scroll;
          height: calc(100vh - 30% - 50px);

          iframe {
            width: 100%;
            height: 100%;
          }
        }
      }

      &.iframe_modal_wrapper {
        .body_modal_content {
          overflow: hidden;
        }
      }

      .close_modal {
        transform: rotate(180deg);
        opacity: 1;
      }
    }
  }

  .modal_info {
    display: block;
    z-index: 65;
  }
}

.modal_info {
  display: none;

  .owl-carousel {
    //margin-bottom: 100px;

    .owl-stage-outer {
    }
    .classic_dots {
      display: flex;
      justify-content: center;
      position: absolute;
      left: 0;
      right: 0;
      top: 220px;

      .classic_dot {
        width: 8px;
        height: 8px;
        background-color: white;
        margin: 0 3px;
        border-radius: 50%;
        opacity: .5;
        cursor: pointer;

        &.active {
          opacity: 1;
        }
      }
    }
    .owl-dots {
      min-height: 80px;
      margin: 40px 15px 0;
      position: relative;
      bottom: auto;

      .owl-dot {
        height: 80px !important;
        border-radius: 4px;
        background-size: cover;
      }
    }
  }

  .content_title {
    .title {
      background-color: lighten(#e2e2e2, 5%);;
      padding: 5px;
    }
  }
}

.loading_animation_popup {
  background: white;
  @include full_size;
  position: fixed;
  z-index: 1000;
  text-align: center;
  .spincircle {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    position: relative;
    border: 6px solid #f5f5f5;
    margin: auto;
    img {
      @include center_xy;
      max-width: 120px;
    }
    &:after {
      position: absolute;
      top: -6px;
      left: -6px;
      content: '';
      display: block;
      width: 140px;
      height: 140px;
      border-radius: 50%;
      border: 6px solid #00ac6b;
      border-color: transparent transparent #00ac6b transparent;
      -webkit-animation: rotatecircle 2s linear infinite;
      animation: rotatecircle 2s linear infinite
    }
  }
  span {
    display: block;
    font-size: 12px;
    font-weight: bold;
    color: #666;
    padding: 20px 0;
  }
}

@keyframes rotatecircle {
  from {
    -webkit-transform: rotate(0deg);
    transform: rotate(0deg)
  }
  to {
    -webkit-transform: rotate(1turn);
    transform: rotate(1turn)
  }
}

.confirmation_animation_popup {
  background: white;
  @include full_size;
  position: fixed;
  z-index: 1001;
  text-align: center;
  i {
    position: relative;
    display: block;
    width: 150px;
    height: 150px;
    border-radius: 50%;
    background: #00ac6b;
    color: white;
    font-size: 60px;
    margin: auto;
    background-clip: content-box;
    border: 15px solid rgba(#00ac6b, .6);
    animation-name: zoom_confirmation;
    animation-duration: 3s;
    animation-delay: 1s;
    animation-iteration-count: 1;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    &:before {
      @include center_xy;
    }
  }
  span {
    display: block;
    font-size: 12px;
    font-weight: bold;
    color: #666;
    padding: 20px 0;
    animation-name: zoom_confirmation;
    animation-duration: 5s;
    animation-delay: 1.5s;
    animation-iteration-count: 1;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
  }
}

@keyframes zoom_confirmation {
  0% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
  }
  20% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  80% {
    -webkit-transform: scale(1);
    -moz-transform: scale(1);
    -ms-transform: scale(1);
    -o-transform: scale(1);
    transform: scale(1);
  }
  100% {
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
  }
}

.modal_map {
  position: fixed;
  top: 100vh;
  bottom: -100vh;
  left: 0;
  right: 0;
  height: 100vh;
  background: white;
  z-index: 1001;
  transition: all .2s;
  &.active {
    top: 0;
    bottom: 0;
  }
  .header_modal {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 100;
    text-align: right;
    padding: 15px;
    background: linear-gradient(rgba(0, 0, 0, .5), rgba(0, 0, 0, 0));
    .close_modal_map {
      color: white;
      font-size: 30px;
    }
  }
  .hotel_carousel {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    .owl-item.active {
      .hotel {
        .price_label {
          -webkit-transform: scale(1);
          -moz-transform: scale(1);
          -ms-transform: scale(1);
          -o-transform: scale(1);
          transform: scale(1);
        }
      }
    }
    .hotel {
      margin: 30px 5px 15px 5px;
      padding: 10px;
      border-radius: 10px;
      background: white;
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
      position: relative;
      .price_label {
        position: absolute;
        top: -35px;
        left: -30px;
        z-index: 2;
        padding: 10px;
        background: $corporate_3;
        color: white;
        -webkit-transform: scale(0);
        -moz-transform: scale(0);
        -ms-transform: scale(0);
        -o-transform: scale(0);
        transform: scale(0);
        @include transition(all, .6s);
        .since {
          display: block;
          font-size: 8px;
          text-transform: uppercase;
          line-height: 10px;
          width: calc(100% - 15px);
          font-weight: normal;
        }
        .currencyValue {
          font-size: 20px;
          line-height: 20px;
          font-weight: bold;
        }
        .monedaConv {
          font-size: 10px;
          font-weight: normal;
        }
      }
      .hotel_pic {
        display: inline-block;
        vertical-align: top;
        width: 60px;
        height: 60px;
        border-radius: 5px;
        position: relative;
        overflow: hidden;
        img {
          @include center_image;
        }
      }
      .hotel_info {
        display: inline-block;
        vertical-align: top;
        padding-left: 5px;
        width: calc(100% - 65px);
        h3 {
          display: inline-block;
        }
        .hotel_submit {
          float: right;
          padding: 12px 0;
          font-size: 25px;
          color: $corporate_3;
        }
      }
    }
  }
}

.popup_gift {
  .gift_header {
    background: $corporate_3;
    color: white;
    text-align: center;
    padding: 50px 30px 30px;
    font-weight: normal;
    i {
      font-size: 70px;
      margin-bottom: 15px;
    }
  }
  .newsletter {
    padding: 30px;
    input[type=text], button {
      display: block;
      width: 100%;
      padding: 15px 10px;
      font-size: 20px;
      text-align: center;
      border-width: 0;
      margin: 0;
    }
    input[type=text] {
      background: #EFEFEF;
      font-weight: normal;
      &::-webkit-input-placeholder {
        color: #CCC;
        font-weight: normal;
      }
      &::-moz-placeholder {
        color: #CCC;
        font-weight: normal;
      }
      &:-moz-placeholder {
        color: #CCC;
        font-weight: normal;
      }
      &:-ms-input-placeholder {
        color: #CCC;
        font-weight: normal;
      }
    }
    button {
      background: $corporate_1;
      color: white;
      margin-bottom: 20px;
    }

    input[type=checkbox] {
      display: inline-block;
      vertical-align: top;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background: white;
      border: 1px solid #aaa;
      width: 20px;
      height: 20px;
      margin: 5px 0;
      position: relative;
      &:before {
        content: '';
        @include center_xy;
        background: transparent;
        width: 13px;
        height: 13px;
      }
      &:checked {
        &:before {
          background: $corporate_1;
        }
      }
    }
    a, span {
      display: inline-block;
      vertical-align: top;
      color: #444;
      font-size: 14px;
      width: calc(100% - 30px);
      padding: 5px 0 5px 5px;
      font-weight: normal;
    }
  }
}

#bookingMatcheContainer {
  display: none;
}

.wrapper_popup {
  height: auto !important;
  width: 100% !important;
  margin: 0 !important;
  z-index: 999999999999 !important;
  .matcher-main-wrapper.rate-check-v4 {
    bottom: 0;
    width: 100%;
    border-radius: 0;
    position: absolute !important;
    .matcher-header-wrapper {
      position: relative;
      border-radius: 0 !important;
      text-align: center;
      background: $corporate_3;
      padding: 30px 10px 20px;
      &:before {
        content: '';
        position: absolute;
        top: 10px;
        left: 0;
        right: 0;
        margin: auto;
        width: 50px;
        height: 4px;
        border-radius: 6px;
        background: rgba(white, .6);
      }
      .matcher-title {
        margin: auto;
        float: none;
      }
      .matcher-corner.matcher-open {
        position: absolute;
        top: 0;
        right: 10px;
        &:before {
          content: "";
          font-family: 'Font Awesome 5 Pro', 'icomoon';
          font-size: 30px;
          color: white;
        }
        img {
          display: none;
        }
      }
    }
    .matcher-full-body-wrapper {
      font-family: "Quicksand", Sans-Serif;
      .matcher-ourprice-wrapper {
        background: $corporate_1;
      }
      .matcher-body-wrapper {
        .matcher-board-selector {
          .background-board-image {
            width: 35px;
            background: $corporate_1;
            position: relative;
            &:before {
              @include center_xy;
              content: "\f153";
              font-family: 'Font Awesome 5 Pro', 'icomoon';
              font-size: 25px;
              font-weight: 900;
              color: white;
            }
            i {
              display: none;
            }
          }
          select {
            width: calc(100% - 35px) !important;
          }
        }
        .matcher-our-price-container {
          background: $corporate_3;
          color: white;
        }
      }
      .matcher-footer-wrapper {
        padding: 20px;
        border-radius: 0;
      }
    }
  }
}
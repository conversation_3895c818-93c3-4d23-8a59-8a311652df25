.main_menu {
  @include transition(none, 0s);
  .social_menu, ul.main_ul {
    @include transition(none, 0s);
    a, li {
      animation: none !important;
    }
  }
  .social_menu {
    background-color: $newsletter_color;
    text-align: left;
    display: block;
    box-shadow: 3px 3px 10px 2px rgba(0, 0, 0, .3);
    a.mailto {
      display: inline-block;
      vertical-align: middle;
      float: none;
      i {
        position: relative;
        width: 60px;
        height: 60px;
        font-size: 26px;
        color: $corporate_1;
        background-color: transparent;
        &:before {
          @include center_y;
          top: 55%;
          left: 25px;
        }
      }
    }
    .custom_social_menu {
      display: inline-block;
      vertical-align: middle;
      width: calc(100% - 70px);
      .custom_social_menu_logo {
        display: inline-block;
        vertical-align: middle;
        width: 48%;
        margin: 0;
        text-align: left;
        img {
          max-width: 90px;
          display: inline-block;
        }
        .svg {
          filter: brightness(0) invert(1);
        }
      }
      .language_selector {
        display: inline-block;
        vertical-align: middle;
        width: 50%;
        text-align: right;
        a {
          display: inline-block;
          vertical-align: middle;
          color: white;
          font-size: 18px;
          line-height: 18px;
          font-weight: 300;
          margin: 0;
          padding: 0 5px;
          text-transform: uppercase;
          &:not(:first-of-type) {
            border-left: 1px solid white;
          }
          &.selected {
            color: $corporate_1;
          }
        }
      }
    }
  }

  .policies_links {
    display: none;
  }
}

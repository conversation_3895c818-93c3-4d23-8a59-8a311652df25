.media_kits_wrapper {
  background-color: white;
  padding: 0 30px 40px;
  
  .container12 {
    .banner {
      .card {
        width: 100%;
        position: relative;
        display: flex;
        flex-direction: column;
        flex-wrap: nowrap;
        margin-bottom: 40px;
        
        .picture_wrapper {
          height: 175px;
          
          img {
            height: 100%;
            width: 100%;
            object-fit: cover;
          }
        }
        
        .content_wrapper {
          text-align: left;
          border: 1px solid #897966;
          padding: 20px 30px 80px;
          position: relative;
          flex: 1;
          
          .card_title {
            font-family: $title_family;
            font-size: 16px;
            line-height: 20px;
            font-weight: 400;
            margin: 0 0 10px;
            color: $corporate_1;
          }
          
          .text {
            @include text_styles;
            font-size: 10px;
            line-height: 13px;
          }
          
          .links_wrapper {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            background: $corporate_1;
            padding: 10px 0;
            border: 1px solid white;
            
            a {
              color: white;
              font-size: 12px;
              text-transform: uppercase;
              text-align: center;
            }
          }
        }
      }
      
      .owl-nav {
        @include owl_nav_styles($black2);
        
        .owl-prev {
          right: auto;
          left: -30px;
        }
        
        .owl-next {
          left: auto;
          right: -30px;
        }
      }
    }
  }
}
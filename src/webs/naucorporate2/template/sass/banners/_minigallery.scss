.minigallery_wrapper {
  width: 100%;
  overflow: hidden;
  margin-bottom: 80px;
  .owl-item {
    background-color: $corporate_1;
    height: 300px;
    overflow: hidden;

    img {
      width: auto;
      opacity:1;
      @include transition(all,.6s);
    }
    span {
      display: block;
      width: 90%;
      color: white;
      font-size: 20px;
      text-align: center;
      text-shadow: 0 0 5px rgba(0,0,0,.6);
      @include center_xy;
      i.fa {
        display: block;
        text-align: center;
        font-size: 25px;
      }
    }

    &:hover {
      img {
        opacity: .4;
      }
      .minigallery_desc {
        img {
          opacity: .8;
        }
      }
    }
    .minigallery_desc {
      img {
        opacity: .4;
      }
    }
  }

  .owl-nav {
    display: block !important;
    & > div {
      @include center_y;
      margin-top: -40px;
      color: white;
      cursor: pointer;
      font-size: 25px;
    }
    .owl-prev, .owl-next {
      width: 100px;
      height: 100px;
      background: $corporate_1;
      background-clip: content-box;
      border-radius: 50%;
      border: 15px solid rgba($corporate_1, .6);
      transform: rotate(-45deg);
      @include transition(all, .6s);
      i.fa {
        @include center_xy;
      }
      &:hover {
        background-color: rgba(white, .8);
        color: $corporate_1;
        border: 10px solid rgba(white, .3);
      }
    }
    .owl-prev {
      left: -50px;
      i.fa {
        margin-left: 0;
        transform: rotate(45deg);
      }
    }
    .owl-next {
      right: -50px;
      i.fa {
        margin-left: -20px;
        margin-top: -20px;
        transform: rotate(45deg);
      }
    }
  }
}
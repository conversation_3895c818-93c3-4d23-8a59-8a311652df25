<div class="detailed_room_wrapper">
    <div class="container12">
        <div class="room_detail_image_wrapper">
            {% if room_details.pictures %}
                <img class="room_detail_image" src="{{ room_details.pictures.0.servingUrl|safe }}=s1140">
            {% endif %}
            {% if not offer_individual %}
                {% if previous_room and next_room %}
                    <div class="next_prev_wrapper">
                        <a class="prev_room" href="{{ previous_room }}">
                            <i class="fa fa-angle-left" aria-hidden="true"></i> <span>{{ T_previous_room }}</span>
                        </a>
                        <a class="next_room" href="{{ next_room }}">
                            <i class="fa fa-angle-right" aria-hidden="true"></i> <span>{{ T_next_room }}</span>
                        </a>
                    </div>
                {% endif %}
                {% if room_details.pictures %}
                    <a href="{{ room_details.pictures.0.servingUrl|safe }}=s1900" class="see_more_pictures_detailed"
                       rel="lightbox['gallery_room']">
                        <span>{{ T_ver_fotos }}</span><i class="fa fa-plus" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if room_details.pictures|length >= 1 %}
                    {% for img in room_details.pictures[1:] %}
                        <a href="{{ img.servingUrl|safe }}=s1900" style="display: none"
                           rel="lightbox['gallery_room']"></a>
                    {% endfor %}
                {% endif %}

            {% endif %}
        </div>

        <div class="room_details_text">
            <h1 class="room_title">
                {{ room_details.subtitle|safe }}
            </h1>
            <a href="#data" class="button-promotion">{{ T_reserva_ahora }}</a>

            <div class="room_description">{{ room_details.content|safe }}</div>
        </div>
        {% if not offer_individual %}
            <div class="minigallery_room_wrapper">
                {% for img in room_details.pictures %}
                    <a class="minigallery_element" href="{{ img.servingUrl|safe }}=s1900"
                       rel="lightbox['gallery_room']">
                        <img data-src="{{ img.servingUrl|safe }}=s172-c" lazy="true"/>
                    </a>
                {% endfor %}
            </div>
        {% endif %}
        {% if services_slider %}
            <div class="services_slider_wrapper effects_sass" sass_effect="slide_up_effect">
                <div class="services_carousel owl-carousel">
                    {% for x in services_slider %}
                        <div class="services_element center_y">
                            {% if x.description %}
                                <i class="fa {{ x.description|safe }}"></i>
                            {% elif x.servingUrl %}
                                <img src="{{ x.servingUrl|safe }}"/>
                            {% endif %}
                            <span>{{ x.title|safe }}</span>
                        </div>
                    {% endfor %}
                </div>
            </div>
            <script>
                $(window).load(function () {
                    var owl_params = {
                        loop: true,
                        nav: true,
                        dots: false,
                        items: 4,
                        navText: ['<i class="fa fa-chevron-left" aria-hidden="true"></i>', '<i class="fa fa-chevron-right" aria-hidden="true"></i>'],
                        margin: 0,
                        autoplay: true
                    };

                    $(".services_carousel").owlCarousel(owl_params);
                })
            </script>
        {% endif %}
    </div>
</div>
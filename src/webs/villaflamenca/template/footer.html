<footer>
    {% if footer_columns %}
        <div class="footer_columns_wrapper">
            {% for column in footer_columns %}
                <div class="column {% if column.servingUrl %}logo{% endif %}">
                    {% if column.servingUrl %}
                        <div class="footer_logo">
                            <img src="{{ column.servingUrl|safe }}" {% if column.altText %}alt="{{ column.altText|safe }}" {% endif %}>
                        </div>
                    {% endif %}
                    <div class="column_content">
                        {% if column.title %}
                            <span class="title">{{ column.title|safe }}</span>
                        {% endif %}
                        {% if column.description %}
                            <div class="text">{{ column.description|safe }}</div>
                        {% endif %}

                    </div>
                </div>
            {% endfor %}

        </div>
    {% endif %}
    {% if suggested_hotels %}
    <div class="suggested_hotels_wrapper">
        <div class="top_content">
            <span class="top_content_title">
                {{ T_visitala }}:
            </span>
        </div>
        <div class="hotel_logos">
            {% for x in suggested_hotels %}
                {% if x.servingUrl and x.linkUrl %}
                    <div class="logo_wrapper">
                        <a href="{{ x.linkUrl }}">
                            <img src="{{ x.servingUrl }}" alt="{{ x.altText }}">
                        </a>
                    </div>
                {% endif %}
            {% endfor %}
        </div>
    </div>
    {% endif %}
    <div class="footer_legal_text_wrapper">
        <div class="footer_links_wrapper">
            {% for x in policies_section %}
                {% if x.custom_link %}
                <a href="{{ x.custom_link }}">{{ x.title|safe }}</a> |
                {% else %}
                <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}"
                   class="myFancyPopup fancybox.iframe">{{ x.title|safe }}</a> |
                {% endif %}
            {% endfor %}
            <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html"
               title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
            <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
            <a target="_blank" href="/rss.xml">RSS</a>
        </div>
        {% if texto_legal %}
            <div class="legal_text">{{ texto_legal|safe }}</div>
        {% endif %}
    </div>
    {% if footer_legal_text %}
    <div class="footer_legal_text">
        {{footer_legal_text|safe}}
    </div>
    {% endif %}
</footer>
<div class="banner_gallery_full_wrapper owl-carousel">
    {% for banner in banner_gallery_pics %}
        <div class="banner">
            {% if banner.servingUrl %}
                <img src="{{ banner.servingUrl|safe }}=s1900" {% if banner.altText %}alt="{{ banner.altText|safe }}"{% endif %}>
            {% endif %}
            <div class="banner_content">
                {% if banner.title %}<h4 class="title">{{ banner.title|safe }}</h4>{% endif %}
                {% if banner.description %}<div class="text">{{ banner.description|safe }}</div>{% endif %}
                {% if banner.linkUrl %}
                    <a href="{{ banner.linkUrl|safe }}" {% if "http" in banner.linkUrl %}target="_blank" {% endif %} class="icon_link">
                        <i class="fal fa-plus"></i>
                    </a>
                {% endif %}
            </div>
        </div>
    {% endfor %}
</div>

<script>
    $(window).load(function () {
        $(".banner_gallery_full_wrapper.owl-carousel").owlCarousel({
            loop: true,
            nav: true,
            dots: false,
            items: 1,
            navText: ['<i class="far fa-chevron-left"></i>', '<i class="far fa-chevron-right"></i>'],
            navSpeed: 500,
            autoplay: false,
            autoHeight: true
        });

        {% if not is_mobile and not user_isIpad %}
            function banner_gallery_fx() {
                $(".banner_gallery_full_wrapper").addnimation({parent:$(".banner_gallery_full_wrapper"),class:"fadeInRight", reiteration: false});
            }
            banner_gallery_fx();
            $(window).scroll(banner_gallery_fx);
        {% endif %}
    });
</script>
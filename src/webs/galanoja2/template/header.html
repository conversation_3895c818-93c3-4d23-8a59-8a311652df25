<header>
<div class="container12" style="width:1280px;">

    <div class="logo column2">
        <div id="logoDiv">
            <a href="{{ host|safe }}/">
                <img src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
            </a>
        </div>
    </div>

    <div class="top-row column11">

        <div class="lang-booking">
            <div id="lang" class="lang2">
                <span id="selected-language" class="selected-language2">{{ language_selected }}</span>
                <span class="arrow"></span>

                <ul id="language-selector-options" class="language-selector-options2">
                    {% for key, language in language_codes.items %}
                        {% if not language == language_selected %}
                        <li class="language-option-flag">
                            <a hreflang="{{ key }}"
                               href="{{ hostWithoutLanguage|safe }}/{{ key }}/">{{ language }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}
                </ul>
            </div>
        </div>

        <div id="top-sections">
            {% for section in top_sections %}
                <a href="{{ host|safe }}/{{ section.friendlyUrl }}">
                    <span>{{ section.title|safe }}</span>
                </a>
            {% endfor %}
         </div>

         {% if phones %}
            <div class="contact_phone">{{ T_telefono_hotel }} {{ phones.0 }}</div>
         {% endif %}

    </div>

    <div id="main-sections"  class="column10">
        <ul id="main-sections-inner">
            {% for section in main_sections %}
                <li class="main-section-div-wrapper"
                     {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
                    {% if section.subsections %}

                        <a>{{ section.title|safe }}</a>
                    {% else %}
                        <a href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrl }}">
                            {{ section.title|safe }}
                        </a>
                    {% endif %}

                    {% if section.subsections %}
                        <ul>
                            {% for subsection in section.subsections %}
                                <li class="main-section-subsection {{ subsection.title|lower }}">
                                    <a href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrl }}"
                                       {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                        {{ subsection.title|safe }}
                                    </a>
                                </li>
                            {% endfor %}
                        </ul>
                    {% endif %}
                </li>
            {% endfor %}
        </ul>
    </div>

</div>
</header>
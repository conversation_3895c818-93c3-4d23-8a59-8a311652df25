<div class="banner_destinies_full_wrapper {% if home %}home{% endif %}">
    <h3 class="content_title">
        {% if home %}
            {{ T_destinos }}
        {% else %}
            {{ C_otros_destinos|safe }}
        {% endif %}
    </h3>
    <div class="banner_destinies_wrapper owl-carousel">
        {% for destiny, destiny_element in banner_all_destinies.items() %}
            {% if is_mobile %}
                <div class="destinies_group_wrapper">
                    <div class="banner">
                        {% if destiny_element.main_image %}
                            <img src="{{ destiny_element.main_image|safe }}=s400" alt="{{ destiny }}">
                        {% endif %}
                        <span class="title"><i class="fas fa-map-marker"></i> {{ destiny|safe }}</span>
                        {% if destiny_element.link %}
                            <a href="{{ destiny_element.link|safe }}" class="btn_personalized_2">
                                {{ T_saber_mas }}
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% else %}
                <div class="banner">
                    {% if destiny_element.main_image %}
                        <img src="{{ destiny_element.main_image|safe }}=s400" alt="{{ destiny }}">
                    {% endif %}
                    <span class="title"><i class="fas fa-map-marker"></i> {{ destiny_element.name|safe }}</span>
                    {% if destiny_element.link %}
                        <a href="{{ destiny_element.link|safe }}" class="btn_personalized_2">
                            {{ T_saber_mas }}
                        </a>
                    {% endif %}
                </div>
            {% endif %}
        {% endfor %}
    </div>
</div>

<script>
  $(window).on('load', function () {
    $(".banner_destinies_wrapper.owl-carousel").owlCarousel({
      loop: false,
      nav: true,
      dots: false,
      {% if  is_mobile %}
        items: 1,
      {% endif %}
      navText: ['<i class="far fa-chevron-left"></i>', '<i class="far fa-chevron-right"></i>'],
      margin: 10,
      navSpeed: 1000,
      autoHeight: false,
      autoplay: false,
      {% if not is_mobile %}
        stagePadding: 100,
        responsive: {
          // breakpoint from 0 up
          0: {
            items: 4,
          },
          // breakpoint from 1400 up
          1500: {
            items: 6,
          }
        }
      {% endif %}
    });
  });
</script>
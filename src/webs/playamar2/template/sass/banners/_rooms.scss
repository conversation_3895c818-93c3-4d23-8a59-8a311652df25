.rooms_wrapper {
  .room_wrapper {
    padding-bottom: 100px;
    text-align: center;
    .img_title {
      display: inline-block;
    }
    h3 {
      position: relative;
      display: inline-block;
      color: $corporate_2;
      font-size: 32px;
      line-height: 38px;
      font-family: $title_family;
      font-weight: 300;
      text-align: center;
      padding: 0 30px 50px 30px;
      big {
        display: block;
        font-size: 36px;
        font-weight: 400;
      }
    }
    .room {
      .image {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        width: 50%;
        height: 350px;
        overflow: hidden;
        img {
          @include center_image;
        }
      }
      .content {
        position: relative;
        width: 50%;
        display: inline-block;
        vertical-align: top;
        padding: 70px 100px;
        text-align: center;
        color: white;
        background-color: $corporate_1;
        &:before, &:after {
          position: absolute;
          content: '';
          display: block;
          width: 100px;
          height: 80%;
          top: 10%;
          bottom: 10%;
          visibility: visible;
          border: 1px solid rgba(white, .4);
        }
        &:before {
          left: 10%;
          border-width: 1px 0 1px 1px;
        }
        &:after {
          right: 10%;
          border-width: 1px 1px 1px 0;
        }
        p {
          padding-bottom: 30px;
        }
        .gallery_icon {
          &:first-of-type {
            display: inline-block;
            vertical-align: middle;
            background-color: $corporate_2;
            padding: 5px;
            height: 35px;
            @include transition(all, .6s);
            &:hover {
              background-color: white;
              i {
                color: $corporate_2;
              }
            }
            i {
              font-size: 24px;
              color: white;
            }
          }
        }
        .button_promotion {
          display: inline-block;
          vertical-align: middle;
          background-color: white;
          color: $corporate_1;
          height: 35px;
          padding: 7px 40px;
          @include transition(all, .6s);
          &:hover {
            background-color: $corporate_2;
            color: white;
          }
        }
        .see_more {
          margin-top: 5px;
          a {
            display: inline-block;
            vertical-align: middle;
            background-color: white;
            color: $corporate_2;
            padding: 7px 60px;
            height: 35px;
            @include transition(all, .6s);
            &:hover {
              background-color: $corporate_2;
              color: white;
            }
          }
        }
      }
    }
  }
}
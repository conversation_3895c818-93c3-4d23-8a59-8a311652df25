.banner_icons_services_full_wrapper {
    position: relative;
    overflow: hidden;
    
    .banner_icons_wrapper {
        @include display_flex;
        align-items: start;
        justify-content: center;
        
        .owl-item {
            width: 50%;
        }
        
        .icon_wrapper {
            display: inline-block;
            text-align: center;
            width: calc((100% / 5) - 25px);
            color: $corporate_1;
            margin: 50px 10px;
            
            &:nth-child(1) {
                margin-left: 0;
            }
            
            &:nth-child(6n) {
                margin-right: 0;
            }
            
            &:nth-child(even) {
                color: $corporate_2;
            }
            
            .icon {
                font-size: 30px;
                margin-bottom: 5px;
            }
            
            .icon_text {
                display: block;
                vertical-align: middle;
                text-align: center;
                margin-top: 25px;
                font-size: 14px;
                //color: $color_text;
                letter-spacing: 1px;
                line-height: 18px;
            }
        }
    }
}

.test_wrapper {
    @include d-flex(33%, row, nowrap);
    
    .item {
        outline: 1px solid red;
        
        
        color: blue;
    }

}
@import "defaults";
@import "styles_mobile/2/2";
@import "styles_mobile/2/carousel_icon";
@import "styles_mobile/2/cycle_banners_x3";



.mobile_engine {
  &.open {
    height: 375px !important;
    .mobile_engine_action {
      bottom: 380px !important;
      i.fa {
        &:before {
          font-family: "fontawesome", sans-serif;
        }
      }
    }
  }
  .mobile_engine_action {
    i.fa {
      @extend .icon-specialcalendar;
      &:before {
        font-family: "icomoon", sans-serif;
      }
    }
  }
}

#full_wrapper_booking {
  padding-top: 30px;

  .booking_form_title {
    display: none;
  }

  .destination_wrapper {
    background: white !important;
    margin-bottom: 10px;
    width: calc(100% - 20px);
    position: relative;

    &:before {
      content: '\f278';
      display: block;
      font-family: "FontAwesome", sans-serif;
      font-size: 14px;
      color: #666;
      @include center_y;
      left: 7px;
      z-index: 2;
    }

    &:after {
      content: '\f078';
      display: block;
      font-family: "FontAwesome", sans-serif;
      font-size: 18px;
      color: #666;
      @include center_y;
      right: 7px;
      z-index: 2;
    }

    select {
      width: 100%;
      height: 45px;
      padding-left: 35px;
      box-sizing: border-box;
    }
  }
  .wrapper-new-web-support.booking_form_title {
    top: 133%;
    right: 25px;
    display: block;
    position: absolute;
  }
  .booking_form_title {
    display: block;
  }
  .web_support_label_1 {
    color: white;
  }
}
  .wrapper_booking_button .submit_button {
    background-color: $corporate_1;
  }

.promotions_wrapper .offer_content h3 {
  padding: 10px 10px 0 10px;
}

#my-bookings-form-fields {
  .selectHotel {
    font-size: 15px;
    padding: 1em;
    border-width: 0;
    background-color: white;
    box-sizing: border-box;
    width: 100%;
    border-radius: 5px;
    text-align: left;
    margin: 1em auto 0;
    border: 0.5em solid #F9F9F9;

  }

  #my-bookings-form-search-button {
    display: block;
    padding: 10px 0;
    box-sizing: border-box;
    font-size: 22px;
    text-transform: uppercase;
    width: 100%;
    border-radius: 5px;
    margin: auto;
    background-color: $corporate_1;
    color: white;
  }
}

.carousel_icon {
  clear: both;
  margin-bottom: 60px;
  bottom: 0;
  top: 1em;
  .owl-item {
    box-sizing: border-box;
    .icon {
      @include center_xy;
      text-align: center;
      margin-left: -50px;
    }
    span, i, img {
      margin: auto;
    }
  }
}
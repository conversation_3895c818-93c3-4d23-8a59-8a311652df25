$(window).load(function () {

    $(".myFancyPopup").fancybox({
        maxWidth: 800,
        maxHeight: 600,
        fitToView: false,
        width: '70%',
        height: '70%',
        autoSize: false,
        aspectRatio: false,
        closeClick: false,
        openEffect: 'none',
        closeEffect: 'none'
    });


    $(".myFancyPopupAuto").fancybox({
        width: 650,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });
    $(".myFancyPopupVideo").fancybox({
        width: 640,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });

    $(".hotel_selector_option").click(function(){
        var submit_button = $(this).closest('.paraty-booking-form').find(".submit_button");
        if (!(submit_button.is(':visible'))) {
            submit_button.trigger('click');
        }
    });

    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');

    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }

    if (typeof(TAPixel) !== "undefined") {
        TAPixel.impressionWithReferer("001F000000vA4u0");
    }

    $("img[lazy=true]").unveil();

    effects_sass();

    $(".button-promotion, .button_promotion").unbind('click');
    only_execute_once = false;
    $(".button-promotion, .button_promotion").click(function () {
        prepare_booking_popup();
        open_booking_full();

        var namespace_preselection = $(this).attr('namespace');
        if (namespace_preselection){
            $("#" + namespace_preselection).trigger('click');
        }

        if (!only_execute_once) {
            only_execute_once = true;
            $("#data select.selector_ninos").selectric("refresh");

        }
    });

    $(".lang_selected_image").click(function () {
        $(".lang_options").slideToggle();
    });

    showMenu();
});

$(window).scroll(showMenu);

function showMenu() {
    actual_position = $(window).scrollTop();
    slider_height = $("#slider_container").height() + $("header").height();
    menu_showed = $("#full_wrapper_booking").hasClass('showed');

    if ((actual_position > slider_height) && (!menu_showed)) {
        if ($("#full_wrapper_booking").css('display') == "block") {
            $("#full_wrapper_booking").hide();
        }
        $("#full_wrapper_booking").addClass('floating_booking').addClass('showed').slideDown().css("overflow", "inherit");
    }

    if ((actual_position < slider_height) && (menu_showed)) {
        $("#full_wrapper_booking.floating_booking").slideUp().promise().done(function () {
            $("#full_wrapper_booking").removeClass("floating_booking").removeClass('showed').fadeIn().css("overflow", "inherit");
        });
    }
}
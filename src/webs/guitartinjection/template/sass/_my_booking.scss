#my-bookings-form {
  background: #fafafa;
  padding: 40px 70px;

  .title_hotel_my_booking {
    display: block !important;
    text-align: center;
    background: $corporate_1;
    color: white;
    width: 480px;
    margin: 0 auto 40px;
    text-transform: uppercase;
    font-size: 24px;
    padding: 10px 0;
    font-weight: 300;
  }

  #emailInput, #localizadorInput {
    width: 100%;
    margin-bottom: 10px;
    border: 0;
    padding: 12px;
    font-weight: 300;
    font-size: 14px;
    border-bottom: 2px solid #d8d8d8;
    box-sizing: border-box;
    text-align: center;
  }

  #my-bookings-email-label, #my-bookings-localizador-label, #hotelSelect, #my-bookings-hotel-label {
    display: none;
  }

  button#my-bookings-form-search-button, button#cancelButton, .modify-reservation, .cancelButton {
    display: block;
    width: 100%;
    color: white;
    font-weight: 300;
    background: #01c8cf;
    -webkit-appearance: none;
    -moz-appearance: none;
    font-size: 15px;
    appearance: none;
    border: 0;
    line-height: 35px;
    text-transform: uppercase;
    margin-top: 20px;
  }

  button#cancelButton, .cancelButton {
    background: #fb4545;
    display: none;
    cursor: pointer;
  }

  .fResumenReserva {
    display: block;
    margin: auto;
    border: 0;
    border-bottom: 2px solid #d8d8d8;
    background: white;
    color: #646464;
    font-weight: 300;
    font-size: 13px;
    line-height: 20px;

    h2.titulo.mt15.mb15 {
      font-size: 29px;
      font-weight: 300;
      color: $corporate_1;
      text-decoration: underline;
    }

    .txtCosteTotal {
      color: $corporate_1;
    }

    h3 {
      color: $corporate_1;
    }
  }
}

.modify_reservation_widget {
  margin: 0 auto 20px;
}

.myFancyPopupModify {
  display: none;
}

.my-bookings-booking-info {
  display: block;
  margin: auto !important;
  background: white;
}

h2.booking_form_title {
  font-size: 43px;
  font-weight: 300;
  color: $corporate_1;
  text-align: center;
  width: 590px;
  margin: 0 auto 27px;
}

#my-bookings-form-fields > ul {
  li {

  }

  .cancelButton {
    float: right;
    display: inline-block;
  }
}

.search_booking_wrapper {
  .booking_search_wrapper {

  }

  .titles_wrapper {
    width: 90%;
    margin: auto;
    text-align: center;
    .main_title {
      margin: 30px auto 20px;
      font-family: "Karla", sans-serif;
      font-size: 24px;
      color: #c11731;
    }
  }

  input {
    display: block;
    width: 100%;
    padding: 13px 10px;

    @include input-placeholder {
      opacity: 0.6;
      font-size: 13px;
      color: #424242;
      font-weight: 500;
      letter-spacing: 0.4px;
      font-family: "Open sans", sans-serif;
      font-style: italic;
    }
  }

  .contInput {
    display: block;
    margin: 10px auto 20px;
    padding: 7px;
    width: 300px;

    .hidden {
      display: none;
    }

    label {
      font-size: 15px;
      color: #424242;
      font-weight: 700;
      letter-spacing: 0.6px;
      line-height: 32px;
    }

    &.increase_decrease {
      display: block;
      width: 100%;
      clear: both;

      .controlls {
        display: inline-block;
        width: auto;
        margin-left: 13px;

        & > * {
          display: inline-block;
          width: auto;
        }

        input {
          width: 20px;
          text-align: center;
        }

        i {
          color: $corporate_1;
          font-style: normal;
          font-weight: 700;
          font-size: 15px;
        }
      }

      .option_block {
        width: auto;
        display: inline-block;

        label {
          max-width: 150px;
          display: inline-block;
          vertical-align: middle;
        }

        & + .option_block {
          margin-left: 18px;
          border-left: 1px solid #cacaca;
          padding-left: 24px;
        }
      }
    }
  }

  .separator_title {
    display: block;
    width: 100%;
    clear: both;
    margin-bottom: 10px;
    color: $corporate_2;
    font-size: 14px;
    text-transform: uppercase;
    font-weight: 700;
    letter-spacing: 0.4px;
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #cacaca;
  }

  #modify-button-container {
    display: inline-block;
  }

  #my-bookings-form-search-button, #modify-button-container button{
    background: $corporate_1;
    color: white;
    border: 0;
    text-transform: uppercase;
    cursor: pointer;
    @include transition(all, .5s);
    font-size: 20px;
    font-weight: 700;
    margin: auto;
    padding: 18px 54px 10px;
    letter-spacing: 1px;
    display: inline-block;
    width: auto;
    font-size: 14px;
  }
}

#reservation {
  width: 90%;
  display: table;
  border: 1px solid #cacaca;
  margin: 20px auto;
  padding: 20px;
  box-sizing: border-box;

  &.hide {
    display: none;
  }

  .table_reservation_info {
    background: #f4f4f4;
    display: table;
    width: 100%;

    .info_block {
      border-bottom: 1px solid #cacaca;
      border-right: 1px solid #cacaca;
      color: #424242;
      width: 50%;
      display: inline-block;
      float: left;
      box-sizing: border-box;
      padding: 25px;

      &.hotel_info {
        width: 100%;
      }

      .bold_input {
        font-weight: bold;
      }

      &:nth-last-child(2), &:nth-last-child(1) {
        border-bottom: 0;
      }

      &:nth-child(odd) {
        border-right: 0;
      }

      .hide {
        display: none;
      }
    }
  }

  .cancell_reservation {
    background: #ca0000;
    float: right;
    color: white;
    border: 0;
    text-transform: uppercase;
    cursor: pointer;
    @include transition(all, .5s);
    margin-top: 20px;
    margin-bottom: 0;
    font-size: 20px;
    font-weight: 700;
    padding: 10px 32px;
    letter-spacing: 1px;
  }
}

.booking_questions_wrapper {
  text-align: left;

  .cancellation_reason_title {
    margin-bottom: 15px;
  }

  .radio_wrapper_cancellation {
    margin-bottom: 5px;

    input {
      cursor: pointer;
      margin-right: 15px;
    }
  }

  .cancel_booking_reasons_wrapper {
    margin-top: 15px;

    button {
      background: $corporate_1;
      float: right;
      color: white;
      border: 0;
      font-size: 11px;
      font-weight: 500;
      letter-spacing: 0.6px;
      text-transform: uppercase;
      cursor: pointer;
      @include transition(all, .5s);
      padding: 10px 33px;
      margin-top: 0;
      margin-bottom: 0;

      &:hover {
        opacity: 0.8;
      }

      &[disabled='disabled'] {
        opacity: 0.6;
      }
    }
  }

  #cancellation-reasons {
    width: 100%;
    margin-top: 10px;
  }
}

.popup_bono_gift_wrapper {
  .fa.fa-times-circle-o:before {
    content: '\f057'!important;
  }
}

#modify-popup {
  width: 800px;

  .image-box {
    position: relative;
    margin-bottom: 30px;
  }
  .description h3 {
    text-transform: uppercase;
    text-align: center;
    font-weight: 500;
    font-size: 28px;
    color: $corporate-1;
    margin-bottom: 30px;
  }
  .description div {
    font-family: "Roboto", "sans-serif";
    font-size: 14px;
    font-weight: 300;
    margin-bottom: 20px;
    text-align: center;
  }
}

.my_bookings_loading {
    width: 100vw;
    height: 100vh;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    font-family: "Roboto", "sans-serif";
    font-weight: 300;
    font-size: 13px;
    line-height: 27px;
    color:  $corporate-2;
    z-index: 99999;

    .bg_overlay {
        background-color: #d7d7d7;
        background-image: linear-gradient(360deg, #020202 0%, #353535 100%);
        width: 100%;
        height: 100%;
        background-size: cover;
        position: absolute;
        top: 0;
        right: 0;
        left: 0;
        bottom: 0;
        opacity: 0.7;
    }

    .loader {
        width: 48px;
        height: 48px;
        position: relative;
    }

    .loader_msg {
        position: absolute;
        bottom: 20%;
        text-align: center;
        font-size: 14px;
        text-transform: uppercase;
        font-weight: 100;
        color: white;
    }

    .loader::before,
    .loader::after {
        content: '';
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
        width: 48em;
        height: 48em;
        background-image:
            radial-gradient(circle 10px, #FFF 100%, transparent 0),
            radial-gradient(circle 10px, #FFF 100%, transparent 0),
            radial-gradient(circle 10px, #FFF 100%, transparent 0),
            radial-gradient(circle 10px, #FFF 100%, transparent 0),
            radial-gradient(circle 10px, #FFF 100%, transparent 0),
            radial-gradient(circle 10px, #FFF 100%, transparent 0),
            radial-gradient(circle 10px, #FFF 100%, transparent 0),
            radial-gradient(circle 10px, #FFF 100%, transparent 0);
        background-position: 0em -18em, 0em 18em, 18em 0em, -18em 0em,
            13em -13em, -13em -13em, 13em 13em, -13em 13em;
        background-repeat: no-repeat;
        font-size: 0.5px;
        border-radius: 50%;
        animation: blast 1s ease-in infinite;
    }

    .loader::after {
        font-size: 1px;
        background: #fff;
        animation: bounce 1s ease-in infinite;
    }

    @keyframes bounce {

        0%,
        100% {
            font-size: 0.75px
        }

        50% {
            font-size: 1.5px
        }
    }

    @keyframes blast {

        0%,
        40% {
            font-size: 0.5px;
        }

        70% {
            opacity: 1;
            font-size: 4px;
        }

        100% {
            font-size: 6px;
            opacity: 0;
        }
    }
}
<div class="banner_carousel_activities_wrapper">
    <div class="container_fluid">
        {% if carousel_activities_section %}
        <div class="content_title text_center">
            <h3 class="title">
                {{ carousel_activities_section.subtitle|safe }}
            </h3>
        </div>
        {% endif %}
        <div class="gallery owl-carousel">
            {% for x in carousel_activities_pics %}
            <div class="picture_wrapper">
                <div class="picture_info">
                    {% if x.title %}
                    <h4 class="title sub_border center">{{ x.title|safe }}</h4>
                    {% endif %}
                    {% if x.description %}
                    <div class="desc">{{ x.description|safe }}</div>
                    {% endif %}
                    {% if x.linkUrl %}
                    <a href="{{ x.linkUrl|safe }}" class="see_more"></a>
                    {% endif %}
                </div>
                <img src="{{ x.servingUrl }}" alt="{{ x.title|safe }}">
            </div>
            {% endfor %}
        </div>
    </div>
</div>
<script type="text/javascript">

    $(window).load(function () {
        {% if is_mobile %}
             $(".banner_carousel_activities_wrapper .owl-carousel").owlCarousel({
                loop: true,
                nav: true,
                navText: ['<i aria-hidden="true"></i>', '<i aria-hidden="true"></i>'],
                dots: false,
                items: 1,
                margin: 15,
                smartSpeed: 600,
                fluidSpeed: 600,
                navSpeed: 600,
                autoplay: false
            });
        {% else %}
             $(".banner_carousel_activities_wrapper .owl-carousel").owlCarousel({
                loop: true,
                nav: true,
                navText: ['<i aria-hidden="true"></i>', '<i aria-hidden="true"></i>'],
                dots: false,
                items: 4,
                margin: 15,
                smartSpeed: 600,
                fluidSpeed: 600,
                navSpeed: 600,
                autoplay: true
            });
        {% endif %}


    });

</script>
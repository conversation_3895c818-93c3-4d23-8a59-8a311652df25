.banner_rooms_wrapper {
    @include sec_pad;
    padding-top: 0;
    
    .container12 {
        .room_wrapper {
            margin-bottom: 50px;
            position: relative;
            
            &:hover {
                .pictures_gallery {
                    .picture_wrapper {
                        height: 640px;
                        
                        &::before {
                            opacity: .5;
                        }
                    }
                }
            }
            
            .picture_link {
                width: 100px;
                height: 100px;
                position: absolute;
                top: 0;
                left: 0;
                z-index: 2;
                
                &::after {
                    position: absolute;
                    font-family: "Font Awesome 5 Pro";
                    content: '\f002';
                    width: 30px;
                    height: 30px;
                    color: white;
                    top: 40px;
                    right: 30px;
                    transform: translate(-50%, -50%);
                    color: white;
                    text-align: center;
                    font-size: 40px;
                    opacity: 1;
                    transition: all .8s;
                    transition-delay: .3s;
                }
            }
            
            .pictures_gallery {
                .picture_wrapper {
                    height: 640px;
                    
                    //&::before {
                    //    content: '';
                    //    position: absolute;
                    //    top: 0;
                    //    left: 0;
                    //    right: 0;
                    //    bottom: 0;
                    //    background-color: $corporate_1;
                    //    opacity: 0;
                    //    transition: all .3s;
                    //}
                }
            }
            
            .content_wrapper {
                position: absolute;
                top: 120px;
                bottom: 120px;
                right: 0;
                left: 55%;
                background-color: white;
                z-index: 1;
                padding: 40px;
                
                .content_title {
                    margin-bottom: 20px;
                }
                
                .room_icons {
                    display: flex;
                    flex-flow: row wrap;
                    align-items: center;
                    padding: 20px 0;
                    
                    .icon {
                        display: inline-block;
                        white-space: nowrap;
                        //overflow: hidden;
                        position: relative;
                        margin: 0 10px 10px 0;
                        
                        i {
                            font-size: 25px;
                            color: $corporate_2;
                            display: inline-block;
                            vertical-align: middle;
                            padding: 5px 0;
                            width: 30px;
                            height: 30px;
                            position: relative;
                            
                            &::before {
                                @include center_xy;
                            }
                        }
                        
                        .icon_text {
                            bottom: 0px;
                            left: 0;
                            box-shadow: $shadow;
                            position: absolute;
                            display: block;
                            font-size: 11px;
                            color: white;
                            white-space: nowrap;
                            opacity: 0;
                            font-weight: 500;
                            background-color: $black;
                            border-radius: 2px;
                            padding: 0 5px;
                            transform: translateY(0);
                            transition: all .6s;
                        }
                        
                        &:hover {
                            
                            .icon_text {
                                bottom: -25px;
                                opacity: 1;
                            }
                        }
                    }
                }
                
                .btn_primary {
                    position: absolute !important;
                    bottom: 0;
                    right: 0;
                    @include btn;
                }
            }
        }
    }
}
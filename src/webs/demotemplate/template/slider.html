<div class="tp-banner-container">
    <div class="tp-banner">
        <ul>
            {% for picture in pictures %}
                <li data-transition="slideleft" data-slotamount="7" data-masterspeed="800">
                    <img src="/static_1/images/dummy.png" data-lazyload="{{ picture.servingUrl }}=s1600">
                    {{ picture.description|safe }}
                </li>
            {% endfor %}
        </ul>
    </div>
</div>

<script type="text/javascript">

    var revapi;

        jQuery(document).ready(function() {

        revapi = jQuery('.tp-banner').revolution(
                {
//Add a comment to this line
                    delay: 5000,
                    startwidth: 1140,
                    startheight: 400,
                    hideThumbs: 10,
                    fullWidth: "on",
                    forceFullWidth: "on",
                    fullScreen: "off",
                    lazyLoad: "on",
                    navigationArrows: "solo",
                    onHoverStop: "off",
                    spinner: "spinner3"
                });
    });

</script>

<style type="text/css">

    .tp-banner-container {
        width: 100%;
        position: relative;
        padding: 0;
        min-width: 1140px;
    }

    .tp-banner {
        width: 100%;
        position: relative;
    }

</style>
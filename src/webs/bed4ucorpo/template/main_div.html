<ul itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner" class="container">
    {% for section in main_sections %}
        <div class="main-section-div-wrapper"
             {% if sectionToUse and sectionToUse.title == section.title %}id="section-active" {% endif %}>
            {% if section.subsections %}
                <a>{{ section.title|safe }}</a>
            {% else %}
                {% if section.title %}
                    <a itemprop="url" {% if section.replace_link %}href="{{ section.replace_link|safe }}" target="_blank" {% else %}{% if  section.sectionType != 'Habitaciones' %}href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}"{% endif %}{% endif %}>
                        <span itemprop="name">{{ section.title|safe }}</span>
                    </a>
                {% endif %}
            {% endif %}
            {% if section.sectionType == 'Habitaciones' %}
                <ul>
                    {% for group_key, group_info in hotels_subsection.items() %}
                        {% for hotel in group_info.hoteles %}
                            {% if hotel.coming_soon %}
                                <li class="main-section-subsection coming_soon">
                                    <a>
                                        {{ hotel.title|safe }}
                                        <span class="coming_soon_text">{{ hotel.coming_soon|safe }}</span>
                                    </a>
                                </li>
                            {% else %}
                                <li class="main-section-subsection">
                                    <a href="{{ host|safe }}/{{ seoLinkString }}{{ hotel.friendlyUrlInternational }}">
                                        {{ hotel.title|safe }}
                                    </a>
                                </li>
                            {% endif %}
                        {% endfor %}
                    {% endfor %}
                </ul>
            {% endif %}
            {% if section.subsections %}
                <ul>
                    {% for subsection in section.subsections %}
                        <li class="main-section-subsection {{ subsection.title|lower }}">1111
                            {% if subsection.title %}
                                <a href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrlInternational }}"
                                   {% if sectionToUse and sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                    {{ subsection.title|safe }}
                                </a>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
    {% endfor %}
</ul>
<div id="lang" class="languages_wrapper">
    {% for key, value in language_codes.items() %}
        <a href="{{ hostWithoutLanguage }}/{{ key }}/"
           class="lang {% if  key == language %}selected{% endif %}">
            {{ value|upper }}
        </a>
    {% endfor %}
</div>
<div class="social">
    {% if facebook_id %}
        <a href="http://www.facebook.com/{{ facebook_id }}" target="_blank"> <i class="fa fa-facebook"></i> </a>
    {% endif %}
    {% if instagram_id %}
        <a href="https://www.instagram.com/{{ instagram_id }}/" target="_blank"> <i class="fa fa-instagram"></i> </a>
    {% endif %}
    {% if twitter_id %}
        <a href="https://twitter.com/#!/{{ twitter_id }}" target="_blank"> <i class="fa fa-twitter"></i> </a>
    {% endif %}
    {% if google_plus_id %}
        <a href="https://plus.google.com/u/0/{{ google_plus_id }}" target="_blank" rel="publisher"> <i class="fa fa-google-plus"></i> </a>
    {% endif %}
    {% if flickr_id %}
        <a href="http://www.flickr.com/photos/{{ flickr_id }}" target="_blank"> <i class="fa fa-flickr"></i> </a>
    {% endif %}
    {% if youtube_id %}
        <a href="https://www.youtube.com/channel/{{ youtube_id }}" target="_blank"> <i class="fa fa-youtube"></i> </a>
    {% endif %}
    {% if pinterest_id %}
        <a href="https://es.pinterest.com/{{ pinterest_id }}" target="_blank"> <i class="fa fa-pinterest"></i> </a>
    {% endif %}
    {% if linkedin_id %}
        <a href="https://www.linkedin.com/{{ linkedin_id }}" target="_blank"> <i class="fa fa-linkedin"></i> </a>
    {% endif %}
    {% if whatsapp_id %}
        <a href="http://wa.me/{{ whatsapp_id }}" target="_blank"> <i class="fa fa-whatsapp"></i> </a>
    {% endif %}
    {% if line_id %}
        <a href="https://line.me/R/ti/p/{{ line_id }}" target="_blank"> <i class="fab fa-line"></i> </a>
    {% endif %}
    {% if tripadvisor_id %}
        <a href="https://www.tripadvisor.es/{{ tripadvisor_id }}" target="_blank"> <i class="fa fa-tripadvisor"></i> </a>
    {% endif %}
</div>
.contact_form_wrapper_cv, .contact_form_wrapper_events {
    padding: 100px calc((100% - 1140px) / 2) 120px;
    overflow: hidden;
    
    .content_title {
        @include title_styles($font_lg);
        margin-bottom: 30px;
    }
    
    #contact {
        position: relative;
        margin: auto;
        
        .info {
            @include display_flex;
            width: 100%;
            justify-content: center;
            align-items: center;
            position: relative;
        }
        
        .form_group_wrapper {
            padding-bottom: 15px;
            @include display_flex;
            width: 100%;
            justify-content: center;
            align-items: center;
            
            .form_title_group {
                display: block;
                width: 100%;
                text-align: center;
                font-size: 20px;
                padding-bottom: 10px;
                font-family: $primary_font;
                color: $corporate_2;
            }
            
            .form_group {
                width: 100%;
                
                .title {
                    display: block;
                    width: 100%;
                    text-align: left;
                    font-size: 16px;
                    padding-bottom: 10px;
                    color: $corporate_2;
                    border-bottom: 1px solid $corporate_2;
                    margin-bottom: 10px;
                }
            }
        }
        
        .contInput {
            position: relative;
            display: inline-block;
            padding-top: 10px;
            width: calc((100% - 9px) / 3);
            border: none;
            
            &:not(:nth-of-type(3n)) {
                padding-right: 20px;
            }
            
            &.half_size_input {
                width: 50%;
                
                #file_cv {
                    position: relative;
                    z-index: 5;
                    opacity: 0;
                    cursor: pointer;
                }
                
                #uploadFile {
                    position: absolute;
                    top: 0;
                    left: 20px;
                    width: calc(100% - 20px);
                    cursor: pointer;
                    z-index: 1;
                }
            }
            &.captcha {
                padding-top: 0;
                float: left;
            }
            &.area {
                width: 100%;
                margin-right: 0;
            }
            
            &:nth-of-type(3n) {
                margin-right: 0;
            }
            
            label, .radio_text, input[type="radio"] {
                display: inline-block;
                vertical-align: middle;
                color: $corporate_2;
            }
            
            input:not([type="checkbox"]):not([type="radio"]), textarea, select {
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                background: white;
                display: inline-block;
                vertical-align: middle;
                padding: 15px 25px;
                font-size: 14px;
                width: 100%;
                margin-bottom: 20px;
                border: 1px solid $grey;
                
                &:focus {
                    outline: 3px solid $grey;
                }
                
                &::-webkit-input-placeholder {
                    font-family: $primary_font;
                }
                
                &:-moz-placeholder {
                    font-family: $primary_font;
                }
                
                &::-moz-placeholder {
                    font-family: $primary_font;
                }
                
                &:-ms-input-placeholder {
                    font-family: $primary_font;
                }
                
                &::placeholder {
                    font-family: $primary_font;
                }
            }
            
            textarea {
                height: 150px;
            }
            
            input, textarea {
                &.error {
                    outline: 1px solid red;
                }
            }
            
            #accept-term, &#privacity {
                -moz-appearance: none;
                  -webkit-appearance: none;
                  display: inline-block;
                  vertical-align: middle;
                  width: 12px;
                  height: 12px;
                  box-sizing: border-box;
                  background-color: white;
                  border: 1px solid $black;
                  cursor: pointer;
                  box-sizing: border-box;
                  padding: 0;
                  border-radius: 0;
                  &:checked {
                    background-color: $corporate_1;
                  }
                  &:focus {
                    outline: none;
                  }
            }
            &.extra_terms_contact.text {
                display: inline-block;
                float: left;
                padding: 10px 0 10px 20px;
                position: relative;
                font-size: 10px;
                color: #949494;
                line-height: 1.5;

                a {
                    color: $corporate_1;
                }
            }
        }
        .extra_terms_content {
            display: inline-block;
            width: 300px;

            .extra_terms_contact.checkbox {
                display: block;
                width: 100%;
                color: #19171c;
                font-size: 12px;
                margin-top: 0;
                padding-bottom: 0;
                padding-top: 10px;

                &:first-of-type {
                    padding-top: 0;
                    padding-bottom: 0;
                    margin-top: 34px;
                }
            }
        }
        .extra_form_text {
            display: block;
            height: 100px;
            position: absolute;
            bottom: -110px;
            left: 25px;
            color: #9c9c9c;
            line-height: 1.4;
            font-size: 11px;
            width: 80%;
            a {
                color: #9c9c9c;
                font-weight: bold;
                font-size: 11px;
            }
        }
        
        .policy-terms {
            display: inline-block;
            width: auto;
            float: left;
            color: $black;
            font-size: 12px;
            margin: 0 50px;
        }
        
        a.myFancyPopup {
            display: inline-block;
            vertical-align: middle;
            color: $black;
        }
        
        #contact-button {
            @extend .btn;
            @extend .btn_primary;
            position: absolute;
            right: 0;
            bottom: 20px;
        }
    }
}

.contact_form_wrapper_events {
    #contact-button {
        bottom: -50px !important;
    }
}
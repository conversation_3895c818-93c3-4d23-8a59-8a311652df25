# -*- coding: utf-8 -*-
from copy import deepcopy

from booking_process.constants.advance_configs_names import CONTACT_PHONES, TOP_SECTIONS, PUBLIC_CAPTCHA_KEY, ROOMS_ICONS
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.pictures_utils import getPicturesFor<PERSON>ey, get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, \
	get_section_from_section_spanish_name_with_properties
from booking_process.utils.development.dev_booking_utils import DEV_NAMESPACE, DEV
from booking_process.utils.language.language_utils import get_language_code, get_web_dictionary
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.namespaces.namespace_utils import get_namespace
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
import os

from collections import OrderedDict
from booking_process.utils.booking.normalizationUtils import normalizeForClassName

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html" % thisWeb

TEMPLATE_NAME = "clarin"


# Change this value in default.scss and in config.rb!!
base_web = TEMPLATE_NAME[:4] + TEMPLATE_NAME[-1:]


class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		params = {
			'base_web': base_web
		}

		if not user_agent_is_mobile():
			params.update(self.getDesktopData(sectionToUse, language))
		else:
			script_args = {
				"section_type": sectionToUse.get("sectionType"),
				'base_web': base_web
			}

			params_mobile = {
				"custom_element_home": self.getHtmlExtraBannersMobile(sectionToUse, language),
				"fontawesome5": True,
				"extra_bottom_script": self.buildTemplate_2("mobile/_script_mobile.html", script_args, False, TEMPLATE_NAME)
			}

			params.update(params_mobile)

		return params

	def getDesktopData(self, section, language):
		section_type = ''
		section_name = ''
		if section:
			section_type = section['sectionType']
			section_name = section['sectionName']
		else:
			section = {}

		result_params_dict = {
			'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False}),
			'booking_engine_2': self.buildSearchEngine2(language),
			'footer_column': self.getPicturesProperties(language, '_footer_columns'),
			'newsletter': self.buildNewsletter2(language, check_newsletter=True, social=True),
			'phone_contact': get_config_property_value(CONTACT_PHONES),
			'language_selected_code': get_language_code(language)
		}

		all_sections = self.getSections(language)
		top_sections = self.getSectionsFor(language, all_sections, TOP_SECTIONS)
		for top_element in top_sections:
			section_properties = self.getSectionAdvanceProperties(top_element, language)
			top_element['icon'] = section_properties.get('icon', '')
		self.internationalizeUrls(top_sections)
		result_params_dict['top_sections'] = top_sections

		section_pictures = self.getPicturesProperties(language, section_name)
		result_params_dict['slider_pics'] = list(filter(lambda x: x.get('title') and x.get('title') == "slider", section_pictures))

		footer_column = self.getPicturesProperties(language, '_footer_columns')
		result_params_dict['footer_column'] = []
		for x in footer_column:
			if x.get('title') == 'footer_logo':
				result_params_dict['footer_logo'] = x.get('servingUrl', '')
			else:
				result_params_dict['footer_column'].append(x)

		if section.get('subtitle'):
			result_params_dict['content_subtitle'] = section

		automatic_content = {
			'Galeria de Imagenes': False
		}

		automatic_content['Mis Reservas'] = True

		if automatic_content.get(section_type):
			result_params_dict['content_access'] = True

		result_params_dict.update(self.getDataSection(section, language))
		result_params_dict.update(self.getExtraBanners(section, language))

		return result_params_dict

	def getDataSection(self, section, language):
		result = {}
		section_type = section.get("sectionType")
		if section_type == "Inicio":
			result['home'] = True

		elif section_type == "Habitaciones":
			result['rooms'] = self.getRooms(language)

		elif section_type == "Ofertas":
			result['offers'] = self.getOffers(language)
			result['offers_filter'] = {}
			for offer in result['offers']:
				pictures_offer = getPicturesForKey(language, str(offer.get("offerKey")), [])
				if pictures_offer:
					offer_properties = self.getSectionAdvanceProperties(pictures_offer[0], language)
					if offer_properties.get("filter"):
						filter_list = offer_properties.get("filter", "").split("@@")
						offer['filter_class'] = []
						for filter in filter_list:
							offer['filter_class'].append(normalizeForClassName(filter))
							result['offers_filter'][normalizeForClassName(filter)] = filter

		elif section_type == u"Localización":
			result['contact_form'] = True
			result['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)
			result['iframe_map'] = get_section_from_section_spanish_name("iframe google maps", language).get('content',
			                                                                                                 '')

		if section['sectionType'] == 'Galeria de Imagenes':
			result['gallery_section'] = True
			pics = OrderedDict()
			filters = OrderedDict()

			picture_gallery = self.getPicturesForGallerySection(language).get('images', {}).get(
				'images_blocks')
			pics['random'] = picture_gallery
			alternate_title = get_web_dictionary(language).get('T_mas_fotos')
			for pic in pics['random']:
				filters.setdefault(normalizeForClassName(pic.get('title')), pic.get('title', alternate_title))
				pic['class_filter'] = normalizeForClassName(pic.get('title', alternate_title))


			result['pics_gallery'] = pics
			result['filters_gallery_room'] = []
			result['filters_gallery_single'] = []
			for x, y in filters.items():
				result['filters_gallery_single'].append({x: y})

		return result

	def getExtraBanners(self, section, language):
		result = {}
		advance_properties = self.getSectionAdvanceProperties(section, language)

		if advance_properties.get('banner_cards'):
			banner_cards = get_section_from_section_spanish_name(advance_properties.get('banner_cards'), language)
			if banner_cards:
				result['banner_cards'] = banner_cards
				banner_cards_pics = self.getPicturesProperties(language, advance_properties.get('banner_cards'))
				banner_cards_button = list(filter(lambda x: x.get('title') == 'banner_btn', banner_cards_pics))
				result['banner_cards_button'] = banner_cards_button[0]
				banner_cards_pics_filtered = list(filter(lambda y: y.get('title') != 'banner_btn', banner_cards_pics))
				result['banner_cards_pics'] = banner_cards_pics_filtered

		if advance_properties.get("banner_offers"):
			banners_offers = advance_properties['banner_offers'].split(";")
			args = ['btn_booking']
			result['banners_offers'] = []
			for banner_offer in banners_offers:
				banner_offers = self.getPicturesProperties(language, banner_offer, args)
				for banner in banner_offers:
					banner['name'] = banner.get("title")
					banner['picture'] = banner.get("servingUrl")

				if banner_offers:
					result['banners_offers'].append(banner_offers)

			if not result['banners_offers']:
				banner_offers = self.getOffers(language)
				result['banners_offers'].append(banner_offers)

		if advance_properties.get("minigallery"):
			minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery"), language)
			result['minigallery_for_mobile'] = minigallery_images
			mini_dict = {'minigallery': minigallery_images, 'num_items': 5, 'margin': 5}
			minigallery_html = self.buildTemplate_2("banners/_minigalleryx.html", mini_dict, False)
			result["minigallery"] = minigallery_html

		if advance_properties.get("banner_services"):
			args = ['link_text', 'hover_text']
			result["banner_services_pic_properties"] = self.getPicturesProperties(language, advance_properties.get(
				"banner_services"), args)

		if advance_properties.get("banner_ventajas"):
			result["banner_ventajas_section"] = get_section_from_section_spanish_name(
				advance_properties['banner_ventajas'], language)
			banner_ventajas_pic = self.getPicturesProperties(language, advance_properties['banner_ventajas'],
			                                                 ['icon', 'gallery'])

			result["banner_ventajas_carousel"] = []
			for pic in banner_ventajas_pic:
				if pic.get("title") == "main":
					if pic.get('gallery'):
						gallery_serving_url = list(map(lambda x: x.get('servingUrl'), get_pictures_from_section_name(pic['gallery'], language)))
						result['banner_ventajas_pic'] = gallery_serving_url
					else:
						result['banner_ventajas_pic'] = [pic.get("servingUrl", '')]

				else:
					result["banner_ventajas_carousel"].append(pic)

		if advance_properties.get("bg_fullsize"):
			target_pictures = get_pictures_from_section_name(advance_properties['bg_fullsize'], language)
			result['banner_bg_fullsize'] = {
				'section': get_section_from_section_spanish_name(advance_properties['bg_fullsize'], language),
				'pictures': list(filter(lambda x: not x.get('title') == 'background', target_pictures)),
				'background': list(filter(lambda x: x.get('title') == 'background', target_pictures))
			}

		if advance_properties.get("banner_testimonials"):
			list_properties = ['country', 'date', 'icon', 'rating']
			result['banner_testimonials_pic_properties'] = self.getPicturesProperties(language, advance_properties[
				'banner_testimonials'], list_properties)
			result['banner_testimonials_section'] = get_section_from_section_spanish_name(
				advance_properties['banner_testimonials'], language)

		if advance_properties.get("cycle_banner"):
			result["cycle_banner"] = self.getPicturesProperties(language, advance_properties.get("cycle_banner"))

		if advance_properties.get('blocks_x3'):
			result['blocks_x3'] = self.getPicturesProperties(language, advance_properties['blocks_x3'])
			filters = OrderedDict()

			for block in result['blocks_x3']:
				filter_blocks = block.get('filter', '')
				if filter_blocks:
					filter_clean = normalizeForClassName(filter_blocks)
					filters[filter_clean] = filter_blocks
					block['filter_class_name'] = filter_clean

			result['blocks_x3_filters'] = filters

		if advance_properties.get('full_carousel'):
			result['full_carousel'] = {
				'section': get_section_from_section_spanish_name(advance_properties['full_carousel'], language),
				'pictures': get_pictures_from_section_name(advance_properties['full_carousel'], language)
			}

		if advance_properties.get('banner_map'):
			section = get_section_from_section_spanish_name(advance_properties.get('banner_map'), language)
			pics = self.getPicturesProperties(language, advance_properties['banner_map'])
			mini_dict = {
				'subtitle': section.get('subtitle'),
				'content': section.get('content'),
				'bg': list(filter(lambda x: x.get('title') == 'background', pics)),
				'map_points': list(filter(lambda x: x.get('title') != 'background', pics)),
				'iframe': get_section_from_section_spanish_name("iframe google maps", language).get('content','')
			}

			result['banner_map'] = mini_dict

		if advance_properties.get('rooms_blocks'):
			section_info = get_section_from_section_spanish_name(advance_properties['rooms_blocks'], language)
			rooms_list = self.getRooms(language)
			result['rooms_blocks'] = {
				'section_info': section_info,
				'rooms_list': rooms_list
			}

		if advance_properties.get('cycle_content'):
			result['cycle_content_block'] = self.getPicturesProperties(language, advance_properties['cycle_content'])

		if advance_properties.get('logo_blocks'):
			result['logo_blocks'] = get_section_from_section_spanish_name_with_properties(advance_properties['logo_blocks'], language)
			result['logo_blocks_pictures'] = self.getPicturesProperties(language, advance_properties['logo_blocks'])


		if advance_properties.get('full_content_background'):
			result['full_content_background'] = get_section_from_section_spanish_name(advance_properties['full_content_background'], language)
			result['full_content_background']['pictures'] = self.getPicturesProperties(language, advance_properties['full_content_background'])

		popup_inicio = get_pictures_from_section_name("_popup_inicio_automatico_2", language)
		if popup_inicio:
			result['popup_inicio'] = popup_inicio

		return result

	def getRooms(self, language):
		extra_properties = ['icons', 'gallery']
		rooms = deepcopy(self.getPicturesProperties(language, "_habitaciones_blocks", extra_properties))
		rooms_config = get_config_property_value(ROOMS_ICONS)
		room_icons = self.getPicturesProperties(language, rooms_config, ['icon'])
		for room in rooms:
			room['images'] = get_pictures_from_section_name(room.get('gallery'), language)

			room['room_icons_list'] = []
			room_icons_list = room.get('icons', '').split(';')
			for x in room_icons_list:
				for y in room_icons:
					if x == y.get('title'):
						service_config = y.get('icon').split(";") if y.get('icon') else ''
						if service_config:
							serviceToAppend = {'ico': service_config[0], 'description': y.get('description')}
							if len(service_config) > 1:
								serviceToAppend = {'ico': service_config[0], 'color': service_config[1],
								                   'description': y.get('description')}
							room['room_icons_list'].append(serviceToAppend)
						break

		return rooms

	def getOffers(self, language):
		offers = self.buildPromotionsInfo(language)
		for offer in offers:
			offer['btn_booking'] = True

		return offers

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "650"

	def get_revolution_full_screen(self):
		return "on"

	def get_revolution_transition(self):
		return "fade"

	def get_revolution_initializer(self):
		return True

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_7/_booking_widget.html', params, False)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		options['T_put_promocode'] = "PROMOCODE"

		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True

		return options

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		parent_data = super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate)
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter_flexslider.html',
		}
		template = templateSectionsDict.get(sectionType, parent_data)
		return template

	def getParamsForSection(self, section, language):
		result = {}

		if section['sectionType'] == 'Galeria de Imagenes':
			result = self.getPicturesForGallerySection(language)

		elif section['sectionType'] == "Mis Reservas":
			result = super(TemplateHandler, self).getParamsForSection(section, language)
			result['disable_content'] = True

		elif section['sectionType'] == 'Mis Reservas Corp':
			result['modify_reservation_content'] = get_section_from_section_spanish_name("_popup modificacion reserva", language)
			# result['selectOptions'] = hotels_list
			result['disable_content'] = True
			result['real_modify_reservation'] = True
			result['modify_reservation'] = True

		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def buildContentForSection(self, sectionFriendlyUrl, language,
	                           sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
		currentSection = self.getSectionParams(sectionFriendlyUrl, language)

		if currentSection:
			if user_agent_is_mobile():
				html_to_render = self.getHtmlTypeSectionMobile(currentSection, language)

				if html_to_render:
					return html_to_render
				else:
					additionalParams['custom_elements'] = self.getHtmlExtraBannersMobile(currentSection, language)

				additionalParams['get_subtitle'] = True

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate,
		                                                           additionalParams)

	def getHtmlTypeSectionMobile(self, section, language):
		section_type = section['sectionType']
		language_dict = get_web_dictionary(language)

		if section_type == "Habitaciones":
			return False

		elif section_type == "Ofertas":
			return False

		elif section_type == u"Localización":
			mini_dict = self.getExtraBanners(section, language)
			mini_dict["is_mobile"] = True
			mini_dict["contact_form"] = True
			mini_dict["namespace"] = DEV_NAMESPACE if DEV else get_namespace()
			mini_dict["newsletter"] = self.buildNewsletter2(language, check_newsletter=True, social=True)

			if section.get('subtitle'):
				mini_dict['content_subtitle'] = section

			mini_dict.update(language_dict)
			return self.buildTemplate_2('_main_content.html', mini_dict, False, TEMPLATE_NAME)


	def getHtmlExtraBannersMobile(self, section, language):
		extra_banners = self.getExtraBanners(section, language)
		language_dict = get_web_dictionary(language)
		extra_banners.update(language_dict)
		extra_banners['is_mobile'] = True
		if section.get('sectionType') == 'Inicio':
			extra_banners['home'] = True

		result = "<div class='section_content'>"

		result += self.buildTemplate_2('_main_content.html', extra_banners, False, TEMPLATE_NAME)

		if extra_banners.get("minigallery_for_mobile"):
			args = {
				'minigallery_mobile': extra_banners["minigallery_for_mobile"]
			}
			result += self.buildTemplate_2("mobile_templates/2/_minigallery_v1.html", args, False)

		result += "</div>"

		return result

	def get_hotels(self, language):
		return self.getPicturesProperties(language, "_hotel_selector")
.opiniones_wrapper{
    width: 800px;
    margin: 100px auto;
    color: $black;

    .opiniones{
        border: 0.5px solid #b2a89c45;
        padding: 0 60px;
        border-radius: 10px;
        margin: 20px 0;

        .opinion_ind{
            position: relative;
            margin: 60px auto;

            .opiniones_title{
                font-size: 20px;
                font-weight: 500;
            }

            .opniones_date{
                margin-top: 5px;
                font-weight: 300;

                i{
                    font-size: 16px;
                    margin-left: 10px;
                }

                .icon{
                    display: inline-block;
                    width: 30px;
                    margin-left: 15px;
                    vertical-align: middle;

                    img {
                        width: 100%;
                        height: auto;
                    }
                }
            }

            .opiniones_rate{
                position: absolute;
                right: 0;
                top: 0;
                font-size: 25px;
                font-weight: bold;
            }

            .opiniones_description{
                margin-top: 20px;
                max-height: 66px; // 3 lines
                overflow: hidden;
                font-weight: 300;
                font-size: 16px;
                line-height: 22px;
                @include transition(all, 1s);

                &.active{
                    max-height: 150px;
                }
            }

            a{
                display:none;
                color: $corporate_2;
                font-size: 16px;
                margin-top: 20px;
                float: right;
                text-transform: uppercase;
                letter-spacing: 1px;
                text-align: center;
                font-weight: 500;
                @include transition(all,.5s);

                &:hover{
                    color: #b2a89c;
                }

                &.active{
                    display: block;
                }
            }
        }
    }
}
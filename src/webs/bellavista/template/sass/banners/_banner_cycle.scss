.banner_cycle_wrapper {
    .banner {
        display: flex;
        flex-wrap: nowrap;
        margin-bottom: 100px;
        
        .picture_wrapper {
            width: 50%;
            
            &.img_vector {
                img {
                    height: auto!important;
    
                    @media (min-width: 1421px) {
                        width: 680px;
                    }
    
                    @media (min-width: 1161px) and (max-width: 1420px) {
                        width: 570px;
                    }
    
                    @media (max-width: 1160px) {
                        width: 560px;
                    }
                }
            }
        }
        
        .content_wrapper {
            width: 50%;
            
            .desc {
                margin-bottom: 40px;
            }
        }
        
        &:nth-child(odd) {
            flex-direction: row-reverse;
            
            .content_wrapper {
                padding-right: 40px;
            }
        }
     
        &:nth-child(even) {
            flex-direction: row;
    
            .content_wrapper {
                padding-left: 40px;
            }
        }
    }
}
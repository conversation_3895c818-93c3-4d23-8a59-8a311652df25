{% extends "index.html" %}



{% block mis_reservas %}

<section id="content">
    <div id="wrapper_content" class="container12">
        <div id="wrapper_services" class="column12">
            {% for service in promotionBanners %}
                <div class="services_column column4" id="service_{{ forloop.counter }}">
                    <a class="services_column_title" href="{{ service.linkUrl|safe }}"> <h3>{{ service.title|safe }}</h3> </a>
                    <a class="services_column_description" href="{{ service.linkUrl|safe }}"> <p>{{ service.description|safe }}</p> </a>
                </div>
            {% endfor %}
        </div>


         <div id="main_text" class="column12">
            <div class="content-page-wrapper">
                {{our_booking.content|safe}}
    </div>

    <form style="display:block" name="contact" method="post" id="my-bookings-form">

        <input type="hidden" name="action" id="action" value="reservation"/>

        <div id="my-bookings-form-fields">

            <p><select id="hotelSelect" name="hotel" class="bordeSelect">
                <option value="" selected="selected">{{ T_seleccionar_hotel }} *</option>
                {% if selectOptions %} {% include "my_bookings_selector.html" %} {% endif %}
                {% if groupedSelectOptions %} {% include "my_bookings_selector_grouped.html" %} {% endif %}
            </select></p>

            <p><input email type="text" id="emailInput" name="email" class="bordeInput" value="" placeholder="{{ T_email }} *"/></p>

            <p><input type="text" id="localizadorInput" name="localizador" class="bordeInput" value="" placeholder="{{T_localizador|safe}} *"/></p>
            <button type="submit" id="my-bookings-form-search-button" onClick="return false;">{{T_buscar|safe}}</button>
        </div>

        <div id="reservation" style="margin-top: 40px; clear: both">
        </div>

        <button type="submit" id="cancelButton" onClick="javascript:cancelReservation();return false;">{{T_cancelar_reserva|safe}}</button>

    </form>
        </div>
    </div>
</section>




    <script>
    if(!window.jQuery)
    {
       var script = document.createElement('script');
       script.type = "text/javascript";
       script.src = "/static_1/lib/jquery-1.7.min.js";
       document.getElementsByTagName('head')[0].appendChild(script);

       var script = document.createElement('script');
       script.type = "text/javascript";
       script.src = "/static_1/lib/spin.min.js";
       document.getElementsByTagName('head')[0].appendChild(script);


    }
    </script>
    <script>

    function searchForReservation(){

        hotel = $('#hotelSelect').val().trim();
        email = $('#emailInput').val();
        localizador = $('#localizadorInput').val();


        $.ajax({
              url: hotel + '/utils?action=searchReservation',
              data: {'email': email, 'localizador': localizador},
              success: function(data) {

                 if (data != ''){
                    $('#reservation').html(data);
                    $('#cancelButton').css('display', 'block');
                }

                 else {
                    alert("{{T_reserva_no_encontrada|safe}}");
                }

              }
            });
    }

    function cancelReservation(){

        var r=confirm("{{T_confirmacion_cancelacion_reserva|safe}}");
        if (r==false) {
            return;
        }

        var opts = {
                  lines: 13, // The number of lines to draw
                  length: 4, // The length of each line
                  width: 3, // The line thickness
                  radius: 10, // The radius of the inner circle
                  rotate: 0, // The rotation offset
                  color: '#e1e1e1', // #rgb or #rrggbb
                  speed: 1.5, // Rounds per second
                  trail: 60, // Afterglow percentage
                  shadow: false, // Whether to render a shadow
                  hwaccel: false, // Whether to use hardware acceleration
                  className: 'spinner', // The CSS class to assign to the spinner
                  zIndex: 2e9, // The z-index (defaults to 2000000000)
                  top: 'auto', // Top position relative to parent in px
                  left: 'auto' // Left position relative to parent in px
                };
        var target = document.getElementById('cancelButton');
        $('#cancelButton').css('display', 'none');
        var spinner = new Spinner(opts).spin(target);

        reservationId = $('#reservationId').val();
        hotel = $('#hotelSelect').val().trim();
        $.ajax({
              url: hotel + '/utils?action=cancelReservation',
              data: {'reservationKey': reservationId},
              success: function(data) {
                $('#cancelButton').css('display', 'none');
                alert(data);
              }
            });
    }

    $(document).ready(function() {

        $("#my-bookings-form").validate({
            rules: {
                email: {
                    required: true,
                    email: true
                },

                localizador: "required",
                hotel: "required"
            },

            messages: {
                email: "{{T_campo_valor_invalido}}",
                localizador: "{{ T_campo_obligatorio }}",
                hotel: "{{ T_campo_obligatorio }}"

            }
        });

        $("#my-bookings-form-search-button").click(function() {
            if ($("#my-bookings-form").valid()) {
                searchForReservation();
                console.log("valid");
            }
        });

    });

    </script>

{% endblock %}
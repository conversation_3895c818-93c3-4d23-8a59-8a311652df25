div.banner_cycle_wrapper_destacados {
  .banner.destacado {
    background: $corporate_1;
  }
}
.banner_cycle_wrapper, .banner_cycle_wrapper_destacados {
  .banner {
    padding: 0;
    background: #ECECEC;

    &.destacado {
      background: $corporate_1;
      .banner_content {
        h2 {
          color: white;
          &:after {
            background: white;
          }
        }
        .desc {
          color: white;
          .link {
            a {
              &:before {
                background-color: $corporate_3;
              }
            }
          }
        }
      }
    }
    .image, .banner_content {
      display: inline-block;
      vertical-align: middle;
    }
    .image {
      width: 100%;
      height: 300px;
      position: relative;
      overflow: hidden;
      img {
        @include center_image;
      }
      .destacado_label {
        position: absolute;
        top: 0;
        left: 0;
        background: rgba(0,0,0,0.8);
        display: inline-block;
        color: white;
        padding: 10px 20px;
        letter-spacing: 2px;
        font-size: 20px;
        font-weight: bold;
        text-transform: uppercase;
        i.fa {
          font-size: 50px;
          vertical-align: middle;
        }
      }
    }
    .banner_content {
      width: 100%;
      padding: 20px 0;
      h2 {
        @include title_style;
        text-align: left;
        padding: 0 20px;
        &:after {
          background:#333;
          margin: 10px 0 20px;
        }
      }
      .desc {
        text-align: left;
        padding: 0 20px;
        strong {
          font-weight: bold;
        }
        .link {
          padding: 20px 0;
          a {
            position: relative;
            display: inline-block;
            vertical-align: middle;
            padding: 15px 30px;
            color: white;
            background: $corporate_2;
            font-size: 20px;
            font-weight: bold;
            width: 220px;
            text-align: center;
            text-transform: uppercase;
            &.button-promotion {
              margin-top: 20px;
              background: $corporate_1;
            }
            i.fa {
              font-size: 30px;
              vertical-align: middle;
              margin: 0 0 0 15px;
            }
            span {
              position: relative;
              z-index: 2;
            }
            &.read_more {
              background: white;
              color: $corporate_2;
              padding: 15px 20px;
              width: 61px;
              height: 61px;
              box-sizing: border-box;
              span {
                @include center_xy;
                font-size: 50px;
                font-weight: lighter;
              }
            }
            &:before {
              content: '';
              @include full_size;
              left: auto;
              background: $corporate_1;
              width: 0;
              @include transition(width, .6s);
            }
          }
        }
      }
    }
  }
}
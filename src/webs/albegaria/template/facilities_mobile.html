<link rel="stylesheet" href="/static_1/css/templates/mobile/rooms/rooms_3.css" type="text/css" media="screen"/>

<div class="facilities_render_content">
    {% if title %}
        <h2 class="facilities_title">{{title|safe}}</h2>
    {% endif %}
    <div class="rooms_wrapper">
        {% for room in facilities_information %}
            <div class="room_element">
                <div class="rooms_slider flex_room_{{ forloop.counter }}">
                    <ul class="slides">
                        <li>
                            <img class="room_picture" src="{{ room.servingUrl|safe }}=s1900">
                        </li>
                    </ul>
                </div>

                <h4 class="room_title">{{ room.title|safe }}</h4>

                <div class="room_description">{{ room.description|safe }}</div>


            </div>
            <script>
                $(function () {
                    $(".flex_room_{{ forloop.counter }}").flexslider({
                        controlNav: true
                    });
                });
            </script>
        {% endfor %}
    </div>
</div>


<script>
    $(function () {
        $(".room_element .room_description").each(function (n) {
            var number_words = $(this).html().split(" ");
            if (number_words.length > 50) {
                var normal_text = number_words.slice(0, 50);
                var hidden_text = number_words.slice(50);
                var final_text = normal_text.join(" ") + '<span class="hide_room_description">' + hidden_text.join(" ") + '</span><div class="see_more_room">{{ T_ver_mas }}</div>';
                $(this).html(final_text);
            }
        });

        $(".see_more_room").click(function () {
            $(this).parent().find(".hide_room_description").slideToggle();
        });

        $(".booking_general_button").show();
    });
</script>

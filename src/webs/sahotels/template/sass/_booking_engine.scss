#full_wrapper_booking {
    @include center_x;
    bottom: 100px;
    width: 1030px;
    z-index: 1000;
    background-color: $widget_bg;
    font-family: $text_family;
    text-align: center;
    opacity: 0;
    height: $widget_height;
    @include transition(opacity, .6s);
    
    
    &.default_view {
        opacity: 1;
    }
    
    #wrapper_booking {
        width: 1030px;
        display: inline-block;
    }
    
    &.floating_booking {
        position: fixed;
        top: 0;
        left: 0;
        bottom: auto;
        right: 0;
        width: 100%;
        @include translate(0, 0);
        box-shadow: 0 4px 10px 0 rgba(0, 0, 0, .2);
        
        .hotel_selector_wrapper {
            display: none!important;
        }
    }
    
    #full-booking-engine-html-7 {
        width: 100%;
        z-index: 2;
        
        .booking_form_title {
            display: none;
        }
        
        .booking_form {
            display: table;
            width: 100%;
            max-width: 1140px;
            margin: auto;
            position: relative;
            
            .dates_selector_personalized {
                position: relative;
                float: left;
                width: 420px;
                display: inline-block;
                vertical-align: middle;
                margin-right: 0;
                height: $widget_height;
                padding: $top_padding $horizontal_padding $bottom_padding 60px;
                
                .dates_selector_label {
                    @include label_styles;
                    text-align: left;
                    margin-left: 45px;
                    
                    > span {
                        color: $option_color;

                        &:last-of-type {
                            margin-left: 105px;
                        }
                    }
                }
                
                .start_end_date_wrapper {
                    display: table;
                    width: 100%;
                    padding: 0;
                    font-size: 0;
                    background: none;
                    height: auto;
                    
                    .nights_number_wrapper_personalized {
                        display: none;
                    }
                    
                    .start_date_personalized, .end_date_personalized {
                        display: inline-block;
                        width: calc(50% - 10px);
                        float: left;
                        margin-right: 10px;
                        @include icon_down;
                        @include option_styles;
                        
                        .day {
                            font-size: 38px;
                            line-height: 51px;
                            display: inline-block;
                            vertical-align: top;
                        }
                        
                        .month {
                            display: inline-block;
                            margin-top: 4px;
                        }
                        
                        &:after {
                            position: absolute;
                            bottom: 5px;
                            right: 60px;
                        }
                    }
                }
            }
            
            .rooms_number_wrapper {
                display: none;
            }
            
            .guest_selector {
                display: inline-block;
                float: left;
                padding: $top_padding $horizontal_padding $bottom_padding $horizontal_padding;
                width: 210px;
                height: $widget_height;
                text-align: left;
                margin-left: 10px;
                
                label {
                    @include label_styles;
                    color: $option_color;
                }
                
                .placeholder_text {
                    @include option_styles;
                    @include icon_down;
                    width: 100%;
                    
                    .rooms_number {
                        font-size: 38px;
                        line-height: 51px;
                    }
                }
                
                b.button {
                    display: none;
                }
            }
            
            .room_list_wrapper {
                display: none;
                position: absolute;
                top: 100%;
                left: 53%;
                -webkit-transform: translateX(-50%);
                -moz-transform: translateX(-50%);
                -ms-transform: translateX(-50%);
                -o-transform: translateX(-50%);
                transform: translateX(-50%);
                text-align: left;
                width: 400px;
                background: white;
                padding-top: 20px;
                box-shadow: 0 5px 10px rgba(0, 0, 0, 0.3);
                
                .close_room_selector {
                    position: absolute;
                    display: block;
                    border-radius: 50%;
                    top: 5px;
                    right: 5px;
                    background-color: $black;
                    color: white;
                    height: 17px;
                    width: 17px;
                    text-align: center;
                    line-height: 14px;
                    font-size: 15px;
                    cursor: pointer;
                    
                    &:before {
                        content: 'x';
                    }
                }
                
                .room_list {
                    .room {
                        .adults_selector, .children_selector, .babies_selector {
                            border-width: 0;
                            height: 50px;
                            text-align: center;
                            width: 50% !important;
                            float: left;
                            padding: 7px 0 5px;
                            
                            label.adults_label, label.children_label, label[for=babiesRoom1] {
                                font-size: 10px;
                                text-transform: uppercase;
                            }
                            
                            .room_selector {
                                width: 100% !important;
                                
                                .selectricItems {
                                    display: none !important;
                                }
                                
                                .selectric {
                                    height: 30px;
                                    
                                    .label {
                                        text-align: center;
                                        content: 'search me here';
                                        @extend .fa-plus;
                                        
                                        &:before {
                                            position: absolute;
                                            top: 50%;
                                            left: 15px;
                                            -webkit-transform: translateY(-50%);
                                            -moz-transform: translateY(-50%);
                                            -ms-transform: translateY(-50%);
                                            -o-transform: translateY(-50%);
                                            transform: translateY(-50%);
                                            @extend .fal;
                                            font-size: 18px;
                                            margin-top: 5px;
                                            font-family: "Font Awesome 5 Pro";
                                        }
                                    }
                                    
                                    .button {
                                        text-indent: 0;
                                        height: auto;
                                        margin: 0;
                                        font-size: 0;
                                        line-height: 24px;
                                        background: transparent !important;
                                        text-shadow: 0 0 0 rgba(0, 0, 0, 0) !important;
                                        @extend .fa-minus;
                                        
                                        &:before {
                                            position: absolute;
                                            top: 50%;
                                            right: 15px;
                                            -webkit-transform: translateY(-50%);
                                            -moz-transform: translateY(-50%);
                                            -ms-transform: translateY(-50%);
                                            -o-transform: translateY(-50%);
                                            transform: translateY(-50%);
                                            display: inline-block;
                                            vertical-align: middle;
                                            @extend .fal;
                                            font-size: 18px;
                                            line-height: 24px;
                                            margin-top: 5px;
                                            color: #585d63;
                                            font-family: "Font Awesome 5 Pro";
                                        }
                                    }
                                }
                            }
                        }
                        
                        &.room2, &.room3 {
                            height: 50px;
                            
                            .adults_selector, .children_selector, .babies_selector {
                                height: 40px;
                                padding-top: 0;
                                
                                label {
                                    display: none;
                                }
                            }
                        }
                        
                        &.room_with_babies {
                            .adults_selector, .children_selector, .babies_selector {
                                width: calc(100% / 3) !important;
                            }
                        }
                    }
                }
                
                .add_remove_room_wrapper {
                    display: table;
                    width: 100%;
                    text-transform: uppercase;
                    
                    div {
                        position: relative;
                        float: left;
                        text-align: center;
                        padding: 10px;
                        width: 50%;
                        color: white;
                        cursor: pointer;
                        background: $corporate_1;
                        
                        &:nth-child(2) {
                            background: $corporate_2;
                        }
                        
                        &:before {
                            content: '';
                            @include full_size;
                            background: rgba(0, 0, 0, .3);
                            opacity: 0;
                            @include transition(opacity, .6s);
                        }
                        
                        &.disabled {
                            opacity: .4;
                            cursor: default;
                        }
                        
                        &:not(.disabled):hover {
                            &:before {
                                opacity: 1;
                            }
                        }
                    }
                }
            }
            
            .wrapper_booking_button {
                position: relative;
                display: inline-block;
                float: right;
                width: 380px;
                height: $widget_height;
                
                .promocode_wrapper {
                    display: inline-block;
                    float: left;
                    height: 100%;
                    width: 50%;
                    position: relative;
                    padding: 0;
                    border-top: none;
                    
                    .promocode_label {
                        display: block;
                        margin-top: 30px;
                        text-align: center;
                        color: transparent;
                        line-height: 0;
                        
                        &:before {
                            content: "PROMOCODE";
                            @include label_styles;
                            color: $option_color;
                        }
                    }
                    
                    .promocode_input {
                        -webkit-appearance: none;
                        -moz-appearance: none;
                        appearance: none;
                        background-color: transparent;
                        border: 0;
                        text-align: center;
                        width: 80%;
                        margin: 10px auto 0;
                        border-bottom: 1px solid $label_color;
                        @include promocode_placeholder;
                        
                        &:focus {
                            outline: 0;
                        }
                        
                        &::-webkit-input-placeholder {
                            color: transparent;
                        }
                        
                        &:-moz-placeholder {
                            color: transparent;
                        }
                        
                        &::-moz-placeholder {
                            color: transparent;
                        }
                        
                        &:-ms-input-placeholder {
                            color: transparent;
                        }
                    }
                }
                
                .submit_button {
                    @include btn_styles;
                    padding: 15px 30px;
                    display: inline-block;
                    float: left;
                    width: 50%;
                    height: 100%;
                    font-size: 20px;
                    line-height: 24px;
                    letter-spacing: 1px;
                }
            }
        }
    }
}

.selectricWrapper {
    .selectricHideSelect, .selectricInput {
        display: none;
    }
    
    &.selectricOpen {
        .selectricItems {
            display: block;
            border: 0;
            box-shadow: $box_shadow;
        }
    }
    
    .selectricItems {
        position: absolute;
        top: calc(100% + 10px);
        background-color: white;
        display: none;
        z-index: 9999;
        
        ul {
            margin: 0;
            list-style: none;
            
            li {
                margin: 0;
                background-color: white;
                text-align: center;
                color: $color_text;
                padding: 10px 5px;
                font-weight: 600;
                cursor: pointer;
                background-image: none;
                @include transition(all, .4s);
                
                &:hover {
                    background-color: rgba($corporate_1, .6);
                    color: white;
                }
                
                &.selected {
                    background-color: $corporate_1;
                    color: white;
                }
            }
        }
    }
}

.datepicker_wrapper_element,
.datepicker_wrapper_element_2,
.datepicker_wrapper_element_3 {
    box-shadow: 0 5px 10px rgba($black, .3);
    border-radius: 0;
    z-index: 1100;
    font-family: $text_family;
    
    .header_datepicker {
        background-color: $corporate_1;
        margin: 0;
        
        .close_button_datepicker {
            top: 9px;
            background: white;
            color: $black;
        }
        
        .specific_date_selector {
            text-transform: uppercase;
            font-family: $title_family;
            
            &:before {
                display: none;
            }
        }
    }
    
    .datepicker_ext_inf_sd,
    .datepicker_ext_inf_ed,
    .datepicker_ext_inf_sd_2,
    .datepicker_ext_inf_ed_2 {
        padding: 0;
        
        table.ui-datepicker-calendar {
            margin: 0 15px;
            width: calc(100% - 30px);
            
            th span {
                font-weight: bold;
                color: $corporate_2;
            }
            
            td {
                border-width: 0;
                background: transparent !important;
                height: 40px;
                padding: 5px 0;
                
                .ui-state-default {
                    line-height: 30px;
                    height: 30px;
                    width: 30px;
                    border-radius: 50%;
                }
                
                .ui-state-active {
                    background: $corporate_1 !important;
                }
                
                .ui-state-hover {
                    background: $corporate_2;
                }
                
                &.highlight, &.ui-datepicker-highlighted {
                    .ui-state-default {
                        background: rgba($corporate_1, .4) !important;
                        color: white !important;
                    }
                }
                
                &.ui-datepicker-start_date, &.last-highlight-selection {
                    .ui-state-default {
                        background: $corporate_1 !important;
                        
                        &:before {
                            display: none;
                        }
                    }
                }
            }
        }
    }
    
    .ui-datepicker-header.ui-widget-header {
        border-bottom: 1px solid $corporate_1;
        font-family: $title_family;
        
        .ui-datepicker-prev, .ui-datepicker-next {
            background: $corporate_1;
            
            .ui-icon {
                color: transparent;
                position: relative;
                @extend .fa-arrow-down;
                
                &:before {
                    @extend .fal;
                    color: $black;
                    font-size: 20px;
                    @include center_xy;
                }
            }
            
            &:hover, .ui-state-hover {
                background: $corporate_1 !important;
                opacity: 0.8;
                
                .ui-icon:before {
                    color: $corporate_2;
                }
            }
        }
    }
    
    .specific_month_selector {
        margin: 0;
        background: transparent;
        color: $corporate_2;
        border-top: 1px solid $corporate_1;
        
        strong {
            color: $black;
        }
    }
    
    .months_selector_container {
        .cheapest_month_selector {
            border-radius: 0;
            background-color: $corporate_2;
        }
    }
}
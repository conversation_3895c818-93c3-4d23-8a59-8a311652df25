.banner_block_full_wrapper {
  @include base_banner_styles;
  padding: 90px calc((100% - 1300px) / 2);
  min-width: 1140px;
  @include display_flex;
  flex-direction: row-reverse;
  width: 100%;
  .banner_block_wrapper {
    display: inline-flex;
    width: 75%;
    .banner {
      position: relative;
      display: inline-block;
      width: calc((100% / 3) - 10px);
      background-color: white;
      border: 1px solid $grey2;
      &:before {
        content: '';
        position: absolute;
        top: -1px;
        left: -1px;
        display: block;
        height: 219px;
        z-index: 1;
        width: calc(100% - 1px);
        border: 2px solid white;
        border-bottom: none;
      }
      &:not(:last-of-type) {
        margin-right: 15px;
      }
      .image_wrapper {
        position: relative;
        width: 100%;
        height: 220px;
        overflow: hidden;
        img {
          @include center_image;
          max-height: 130%;
        }
      }
      .banner_content {
        padding: 35px 40px 20px;
        text-align: center;
        &.with_btn {
          padding-bottom: 90px;
        }
        .icon_wrapper {
          @include center_xy;
          top: 220px;
          width: 44px;
          height: 44px;
          border-radius: 50%;
          z-index: 1;
          background-color: $corporate_1;
          overflow: hidden;
          &:before {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            width: 100%;
            z-index: -1;
            @include linear_gradient(rgba(0, 0, 0, 0), rgba(255, 255, 255, .1));
            @include transition(all, .6s);
          }
          &:after {
            content: '';
            position: absolute;
            top: 0;
            bottom: 0;
            left: 100%;
            width: 100%;
            z-index: -1;
            background-color: rgba(255, 255, 255, .1);
            @include transition(all, .6s);
          }
          i {
            display: inline-block;
            @include center_xy;
            color: white;
            font-size: 24px;
          }
          @if not $is_mobile {
            &:hover {
              &:before {
                left: -100%;
              }
              &:after {
                left: 0;
              }
            }
          }
        }
        .title {
          @include banner_title_styles_2;
          font-size: 26px;
        }
        .text {
          @include banner_text_styles_2;
          font-size: 16px;
          line-height: 1.2;
        }
        .btn_wrapper {
          position: absolute;
          left: 20px;
          right: 20px;
          bottom: 20px;
        }
      }
    }
  }

  .banner_block_content {
    position: relative;
    display: inline-block;
    width: 25%;
    text-align: left;
    padding: 25px 0 0 50px;
    .logo_svg {
      position: absolute;
      width: 100%;
      top: 0;
      right: -20px;
      * {
        fill: $bg_grey;
      }

      &.serra {
        width: auto;
        height: 40%;
        top: auto;
        bottom: 20px;
        right: -120px;
        g {
          -webkit-transform: scale(2);
          -moz-transform: scale(2);
          -ms-transform: scale(2);
          -o-transform: scale(2);
          transform: scale(2);
        }
        * {
          fill: rgba($corporate_1, .2);
        }
      }
    }
    .content_title {
      @include title_styles;
      text-align: left;
      font-size: 30px;
      line-height: 36px;
      .subtitle {
        &:before {
          @include transform(none);
          left: 0;
        }
      }
    }
    .content_text {
      @include text_styles;
      font-size: 14px;
      line-height: 20px;
      padding-top: 20px;
    }
    .btn_personalized_2 {
      margin-top: 20px;
    }
  }
}
<div class="banner_x3_wrapper">
    {% for banner in banner_x3_pictures %}
        <div class="banner">
            {% if banner.servingUrl %}
                <div class="image_wrapper">
                    <img data-src="{{ banner.servingUrl|safe }}=s400" {% if banner.altText %}alt="{{ banner.altText|safe }}" {% endif %}>
                </div>
            {% endif %}
            <div class="banner_content {% if banner.linkUrl or banner.btn_booking %}with_btn{% endif %}">
                {% if banner.icon %}
                    <div class="icon_wrapper">
                        <i class="{{ banner.icon|safe }}"></i>
                    </div>
                {% endif %}
                {% if banner.title %}
                    <h4 class="title">{{ banner.title|safe }}</h4>
                {% endif %}
                {% if banner.description %}
                    <div class="text">{{ banner.description|safe }}</div>
                {% endif %}
                {% if banner.linkUrl or banner.btn_booking %}
                    <div class="btn_wrapper">
                        {% if banner.linkUrl %}
                            <a href="{{ banner.linkUrl|safe }}" {% if "http" in banner.linkUrl %}target="_blank" {% endif %} class="btn_personalized_2">{{ T_ver_mas }}</a>
                        {% endif %}
                        {% if banner.btn_booking %}
                            <a href="#data" class="btn_personalized_1 button-promotion"
                               {% if banner.smartDatasAttributes %}{{ banner.smartDatasAttributes|safe }}{% endif %}
                            >{{ T_reservar }}</a>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    {% endfor %}
</div>
<script>
    $(window).load(function () {
        $(".banner_x3_wrapper .image_wrapper img").each(function () {
            $(this).attr('src',$(this).attr('data-src'))
        })
    });
</script>
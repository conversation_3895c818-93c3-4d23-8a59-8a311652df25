/*=== General ===*/
body {
  font-family: 'gotham';
}

body.interior {
  section#slider_container {
    height: 630px;
    overflow: hidden;
    width: 100%;
    display: inline-block;
  }
}

section#content {
  padding-top: 80px;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/

.ui-state-default {
  border: 1px solid white !important;
}

.ui-datepicker-title {
  color: white !important;
}

.ui-widget-header {
  background: $corporate_1 !important;
}

.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: $corporate_1 !important;
  color: white;
}

/*=== Header ===*/
header {
  width: 100%;
  z-index: 30;
  background: $corporate_1;
  min-width: 1140px;
  top: 0;
  height: 150px;
  overflow: hidden;
  position: absolute;

  div#logoDiv {
    margin-top: 7px;
    margin-left: 0;
  }
}

.top_header {
  text-align: right;
  margin-top: 14px;

  div#social {
    display: inline-block;
    float: right;
    margin-left: 15px;
    margin-top: 22px;

    a {
      text-decoration: none;
      display: inline-table;
      float: left;
      margin-right: 5px;
    }
  }

  #lang {
    margin-top: 20px;
  }

  div#lang {
    display: inline-block;
  }

  .contact_phone {
    display: inline-block;
    font-size: 14px;
    color: white;
    font-weight: lighter;
    margin-right: 15px;
    margin-left:15px;
    margin-top:23px;

    img {
      vertical-align: bottom;
      margin-bottom: 2px;
      margin-right: 5px;
    }

    i {
      margin-right: 5px;
    }

  }

  div#top-sections {
    display: inline-table;
    margin-top:23px;

    a {
      font-size: 12px;
      color: white;
      font-weight: lighter;
      text-decoration: none;
      margin-right: 22px;

      &:hover {
        opacity: 0.8;
      }

      &:last-of-type {
        margin-right: 0;
      }

      img {
        vertical-align: middle;
        margin-bottom: 2px;
        margin-right: 5px;
      }
    }
  }
}

.web_oficial {
  color: white;
  float: left;
  font-size: 12px;
  margin-top: 27px;
}


#lang {
    position: relative;
    top: 7px;
    float: right;
    font-size: 12px;
    color: white;
    font-weight: lighter;
    text-decoration: none;
    margin-left: 15px;
    cursor: pointer;

  span#selected-language {
    padding-left: 4px;
  }

  #language-selector-options {
    position: absolute;
    margin-top: 4px;
  }

  .arrow {
    display: inline-block;
    background: url(/img/#{$base_web}/flecha_white_down.png?v=1) no-repeat center center !important;
    float: right;
    width: 30px;
    height: 26px;
    margin-top: -6px;
    background-size: 17px !important;

  }

  #selected-language {

  }

  ul li {
    background: #ffffff;
    text-align: left;
    width: 80px;
    font-size: 14px;
    padding: 5px;
    cursor: pointer;
    display: block;
    border-bottom: 1px solid #EEE;
    color: #666;
    border-top: 1px solid #FFF;

    &:hover {
      border-bottom: 1px solid rgba(128, 128, 128, 0.33);
      background: #f0f0f0;
      width: 80px;
    }

    a {
      color: #666 !important;
      text-decoration: none !important;
    }
  }
}



/*===== Menu =====*/
#mainMenuDiv {
  margin-top: 35px;

  ul {
    text-align: justify;
    justify-content: space-between;

    &:after {
      content: "";
      width: 100%;
      display: inline-block;
      height: 0;
    }

    li {
      display: inline-block;
      text-align: center;

      &:first-of-type {
        padding-left: 0;
      }

      &:nth-last-of-type(2), &:last-of-type {
        border-right: 0;
      }

      &:last-of-type {
        padding-right: 0;
      }

      a {
        text-decoration: none;
        font-size: 16px;
        font-weight: lighter;
        color: white;
        text-transform: uppercase;
        padding: 6px 0 5px;

        &.button-promotion {
          color: white !important;
          background: $corporate_1;
          text-transform: uppercase;
          font-weight: bold;
          font-size: 12px;
          padding: 9px 7px;
        }
      }

      &:hover a {
        border-top: 2px solid white;
        border-bottom: 2px solid white;
      }

      &#section-active a, {
        font-weight: 700;
        border-top: 2px solid white;
        border-bottom: 2px solid white;
        padding: 6px 0 5px;
      }
    }
  }
}

/*=== Slider ===*/
#slider_container {
  position: relative;

  .ticks {
    position: absolute;
    left:0;
    right:0;
    bottom:0;
    z-index: 30;
    background-color: rgba(black, 0.6);
    padding: 20px;
    text-align: center;
    color: white;
    .tick {
      display: inline-block;
      vertical-align: middle;
      font-size: 18px;
      font-weight: 100;
      .fa, span {
        display: inline-block;
        vertical-align: middle;
        margin: 0 20px 0 0;
       }
      .fa {
        margin: 0 5px;
        font-size: 200%;
      }
    }
  }
}

.left_slider_arrow {
  position: absolute;
  top: 315px;
  z-index: 22;
  left: 20%;
  cursor: pointer;
}

.right_slider_arrow {
  position: absolute;
  top: 315px;
  z-index: 22;
  right: 20%;
  -moz-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
  filter: FlipH;
  -ms-filter: "FlipH";
  cursor: pointer;
}

.tparrows {
  display: none !important;
}

.tp-bullets {
  bottom: 150px !important;
  opacity: 1 !important;
}

.slide_inner {
  height: 630px;
  width: 100%;
  overflow: hidden;
  display: inline-block;

  img {
    width: 100%;
    display: block;
  }
}

.down_slider_arrow {
  position: absolute;
  bottom: 175px;
  z-index: 22;
  right: 20%;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

/*==== Booking Widget ====*/
label#titulo_fecha_entrada, label#titulo_fecha_salida {
  display: none;
}

#contenedor_habitaciones > label {
  display: none;
}

.adultos.numero_personas {
  & > label {
    display: none !important;
  }
}

#titulo_ninos {
  display: none !important;
}

#booking fieldset {
  margin: 5px 0 0;
}

#search-button {
  font-size: 14px;
}

/*======= Booking Widget Data =====*/
#data {
  .date_box {
    background: #ECECEC;

    .date_year {
      color: #5CACDB;
    }
  }

  .selectric, .promocode_input {
    background: #ECECEC;
  }

  .promocode_input {
    font-size: 13px;
  }
}

/*====== Content Subtitle =====*/
h3.subtitle_title {
  text-align: center;
  font-size: 32px;
  font-weight: 700;
  color: $corporate_1;
  margin-bottom: 28px;
}

.content_subtitle_wrapper {
  display: table;
  width: 100%;

  .divided1, .divided2, .divided3 {
    width: 350px;
    float: left;
    text-align: left;
  }

  .divided3 {
    float: right;
  }

  .divided2 {
    margin-left: 45px;
  }

  .subtitle_description {
    font-size: 17px;
    font-weight: lighter;
    line-height: 29px;
    color: #636363;
    text-align: center;
    margin-bottom: 40px;

  }
}

/*============== Gallery Section features ==============*/

.gallery_1 li .crop img{
    height: 235px!important;
    width: 100%;


}

/*============== Gallery Mosaic ==============*/
.gallery_title {
  padding-top: 60px;
}

.gallery_title, .services_title {
  color: #626262;
  text-align: center;
  font-size: 27px;
  margin-bottom: 30px;
  font-weight: lighter;
  text-transform: uppercase;

  &:after {
    content: "";
    width: 55px;
    border-bottom: 2px solid $corporate_1;
    display: block;
    margin: 17px auto 0px;
  }
}

.gallery-mosaic-item {
  float: left;
  width: 375px;
  margin: 2px;
  height: 250px;
  overflow: hidden;
  position: relative;

  img {
    min-width: 100%;
    min-height: 100%;
    max-height: auto;
    max-width: auto;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

    &:hover {
      -webkit-transform: scale(1.2);
      -moz-transform: scale(1.2);
      -ms-transform: scale(1.2);
      -o-transform: scale(1.2);
      transform: scale(1.2);
    }
  }
}

.gallery-mosaic-item.minigallery-last-item {
  float: right;
  width: 100%;
  position: relative;
  height: 421px;

  &:hover img {
    -webkit-transform: scale(1.2);
    -moz-transform: scale(1.2);
    -ms-transform: scale(1.2);
    -o-transform: scale(1.2);
    transform: scale(1.2);
  }

  img {
    width: auto;
    height: 100%;
    max-width: none;
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    -webkit-transition: all .5s ease-in-out;
    -moz-transition: all .5s ease-in-out;
    -ms-transition: all .5s ease-in-out;
    -o-transition: all .5s ease-in-out;
    transition: all .5s ease-in-out;

  }

  img.video_arrow {
    position: absolute;
    top: 0px;
    right: 0px;
    left: 0px;
    bottom: 0px;
    width: 100px;
    height: 100px;
    z-index: 2;
    min-height: inherit;
    min-width: initial;
  }
}

.gallery-smalls, .gallery-big {
  margin-left: 0px;
  margin-right: 2px;
}

.gallery-big {
  width: 396px;
  height: 360px;

}

.gallery-mosaic {
  margin: 78px auto 0px;
}

.flexslider_gallery {
  position: relative;

  li {
    width: 1285px !important;
  }
}

/*======= Banners x3 =======*/
.bannersx3_wrapper {
  display: table;
  width: 100%;
  margin-top: 73px;

  .banner_element {
    float: left;
    width: 33.3%;
    position: relative;
    overflow: hidden;
    -webkit-transition: all 1s;
    -moz-transition: all 1s;
    -ms-transition: all 1s;
    -o-transition: all 1s;
    transition: all 1s;

    &:hover {
      opacity: 0.8;
    }

    &:before {
      content: "";
      display: block;
      padding-top: 95%;
    }

    img {
         width: auto;
        position: absolute;
        left: -50%;
        top: 0;
        min-height: 100%;
        max-width: none;
        margin: 0 auto;
        right: -50%;
        bottom: 0px;
    }

    &.big {
      width: 33.4%;

      &:before {
        content: "";
        display: block;
        padding-top: 94.6%;
      }
    }

    .circle {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
      height: 22px;
      width: 235px;
      margin: auto;
      text-align: center;
      color: white;
      background: rgba(101, 180, 230, 0.5);
      font-size: 21px;
      border-radius: 181px;
      padding: 105px 0px;
    }

    .underline_bottom {
      width: 75px;
      border-bottom: 2px solid white;
      margin: 5px auto;
    }

    .underline_top {
      width: 75px;
      border-bottom: 2px solid white;
      margin: 5px auto;
    }
  }
}

/*===== Bottom Phrase ====*/
.bottom_phrase {
  text-align: center;
  margin-top: 78px;
  margin-bottom: 70px;

  h3.title {
    font-size: 34px;
    color: #5CACDB;
    margin-bottom: 30px;
    font-weight: bolder;
  }

  .description {
    font-size: 24px;
    color: #636363;
    font-weight: lighter;
  }
}

/*===== Footer ====*/
footer {
  background: #ECECEC;
  padding-top: 43px;

  .footer_column {
    text-align: center;

    &.left {
      box-sizing: border-box;
      padding-left: 295px;


    }

    &.right {
      padding-right: 295px;
      box-sizing: border-box;

      #newsletter {
        #title_newsletter, label#suscEmailLabel {
          display: none !important;
        }
      }
    }

    .footer_column_description {
        border-bottom: 1px solid $corporate_1;
        padding-bottom: 10px;
    }


    h3.footer_column_image {
      height: 80px;
    }

    .footer_column_description {
      color: #A5A5A5;
      font-weight: lighter;
      font-size: 12px;
      line-height: 24px;
    }
  }

  input#suscEmail {
    margin-top: 13px;
    width: 237px;
    height: 35px;
    border: 0;
  }

  button#newsletter-button {
    width: 239px;
    border: 0;
    background: $corporate_1;
    color: white;
    margin-top: 7px;
    text-transform: uppercase;
    font-size: 17px;
    padding: 8px;
  }

  .wrapper_footer_columns {
    margin-bottom: 51px;

    a{
      text-decoration: none;
    }
  }

  .full-copyright {
    background: $corporate_1;
    padding: 29px 0;

    .footer-copyright {
      text-align: center;
      font-size: 14px;
      color: white;

      a {
        text-decoration: none;
        color: white;
        font-weight: lighter;
      }
    }
  }

  div#facebook_like {
    width: 49%;
    float: left;
    margin-top: 2px;
    text-align: right;
  }

  #google_plus_one {
    width: 49%;
    float: right;
  }

  .social_likes {
    margin-top: 3px;
  }

  div#div-txt-copyright {
    margin-top: 3px;
  }
}

/*====== Rooms =======*/
.room_element {
  background: #EBECEE;
  display: table;
  width: 100%;
  margin-bottom: 7px;
  position: relative;

  .exceded {
    width: 270px;
    float: left;
    height: 180px;
    overflow: hidden;
    position: relative;

    .lupa {
      position: absolute;
      top: 0;
      right: 0;
    }
  }

  & > .description {
    float: right;
    width: 855px;
    box-sizing: border-box;
    padding: 23px 16px;
    padding-right: 150px;
    line-height: 20px;

    h3.room_title {
      text-align: left;
      font-size: 20px;
      font-weight: 300;
      color: #5CACDB;
      margin-bottom: 10px;
    }

    .room_description {
      font-size: 11px;
      color: gray;
      height: 76px;
      overflow: hidden;
    }
  }

  .see_more_room {
    position: absolute;
    right: 20px;
    top: 23px;
    background: white;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }

    span {
      padding: 0 10px 0 7px;
      font-size: 14px;
      font-style: italic;
      color: gray;
    }

    img:not(.lupa) {
      width: 25px;
      display: inline-block;
      vertical-align: middle;
    }
  }


  .book_room {
    position: absolute;
    right: 20px;
    top: 60px;
    background: $corporate_1;
    color: white;
    cursor: pointer;
    height: 24px;
    box-sizing: border-box;
    width: 99px;

    &:hover {
      opacity: 0.8;
    }

    a {

      font-size: 14px;
      font-style: italic;
      color: white;
      text-align: center;
        width: 100%;
        display: block;
        padding-top: 5px;
        text-decoration: none;
    }


  }



}

.hide_room_description {
  padding: 30px;

  .room_title {
    font-size: 20px;
    font-weight: 300;
    color: #5CACDB;
    margin-bottom: 10px;
  }

  .room_description {
    font-size: 13px;
    color: gray;
  }
}

/*======= Ofertas ======*/
a.plus {
  padding: 8px 8px 7px !important;
}

a.play {
  padding: 10px 9px 5px !important;
}


.enlace_offer {
    display: block;
    height: 250px;
    overflow: hidden;
    position: relative;

    img{
        position: absolute;
        left: -100%;
        right: -100%;
        top: -100%;
        bottom: -100%;
        margin: auto;
        min-height: 100%;
        min-width: 100%;
    }


}


.scapes-blocks {
  overflow: hidden;
  margin-top: 0px;
  margin-bottom: -12px;
}

.scapes-blocks .block {
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;

    a.button-promotion.oferta-reserva {
        width: 100px;
        margin-right: 10px;
    }


  .description {
    padding: 20px;
    position: relative;
    background-color: #ECECEC;
    padding-right: 210px;

    .title-module {
      font-size: 23px;
      color: $corporate_1;
      font-weight: 500;
      margin-top: 6px;
      height: 40px;

      .offer-subtitle {
        font-weight: 300;
        font-size: 18px;
      }
    }

    ul {
      position: absolute;
      width: 115px;
      right: 7px;
      top: 24px;
      text-align: right;
      padding-right: 10px;

      li {
        display: inline;

        a {
          background-color: #4C4C4C;
          top: -3px;
          color: white;
          padding: 8px 7px 6px 7px;
          right: 97px;
          position: absolute;
          text-align: center;
          text-decoration: none;

          &:hover {
            opacity: 0.8;
          }
        }
        a.plus {
          padding: 10px 7px 5px;
          margin-right: -86px;
          height: 18px;
          background: $corporate_1;
        }

        a.play {
          padding: 10px 9px 5px;

          img {
            margin-top: 2px;
          }
        }
      }
    }

    p {
      margin-bottom: 0;
    }
  }
}

.en a.button-promotion.oferta-reserva {
  width: 82px;
  right: 114px !important;
}

.scapes-blocks .row1 {
  margin-right: 10px;
}

.scapes-blocks .row2 {
  margin-left: 10px;
}

.scapes-bottom-content {
  background: $corporate_1;
  padding: 20px;
}

.scapes-popup {
  padding: 19px;
}

.escapadas-popup {

  h3 {
    color: $corporate_1;
    margin-bottom: 20px;
  }
  h5 {
    color: $corporate_1;
  }
}

.offer_popup {
  padding: 25px;
  .btn_mas {
    display: table;
    margin: auto;
    white-space: nowrap;
    padding: 15px 25px;
    background: $corporate_1;
    color: white;
    text-decoration: none;
    &:hover {
      background: $corporate_2;
    }
  }
}

/*======= Content Access =====*/
.content_access {
  h3.section-title {
    text-align: center;
    font-size: 32px;
    font-weight: 700;
    color: #5CACDB;
    margin-bottom: 28px;

    & + div {
      font-size: 17px;
      font-weight: lighter;
      line-height: 29px;
      color: #636363;
      text-align: center;
    }
  }

  #my-bookings-form-fields {
    text-align: center;
  }

  form#my-bookings-form {
    text-align: center;
    padding-bottom: 1px;
  }

  #my-bookings-form-fields {
    margin-top: 20px;

    label {
      display: block;
      line-height: 18px;
      font-size: 17px;
      font-weight: lighter;
      color: #636363;
      text-align: center;
    }

    input {
      width: 160px;
      text-align: center;
    }

    input#emailInput {
      margin-bottom: 6px;
    }

    button#my-bookings-form-search-button {
      display: block;
      margin: 20px auto 0;
      width: 165px;
      border: 0;
      background: #007DAD;
      color: white;
      text-transform: uppercase;
      padding: 7px;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  button#cancelButton {
    display: block;
    margin: 20px auto 0;
    width: 165px;
    border: 0;
    background: #007DAD;
    color: white;
    text-transform: uppercase;
    padding: 7px;
    cursor: pointer;
    display: none;
  }
}

/*========= Location and Contact ======*/
.page-localizacion {
  #wrapper_content {
    background: rgba(252, 241, 235, 0.86);
    padding: 20px;
    margin-top: 200px;
    width: 1100px;
  }

  .container12 .column6 {
    width: 530px;
  }
}



.location-info-and-form-wrapper {
  background: rgba(226, 226, 226, 0.46);
  display: table;
  padding-top: 30px;
  padding-bottom: 20px;
  margin-bottom: 75px;
}

.location-info-and-form-wrapper h1 {
  font-weight: 700;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  color: $corporate_1;
  width: 95%;
  line-height: 20px;
}

.location-info strong {
  font-weight: bold;
}

#location-description-intro {
  margin-bottom: 30px;
}

.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  margin-bottom: 30px;

}

//customer support form

.form-contact #title {
  display: none !important;
}

.form-contact #google-plus {
  display: none !important;
}

.form-contact .fb-like {
  display: none !important;
}

.form-contact #contact {

}

.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

.form-contact #contactContent .bordeInput{
   width: auto;
   margin-right: 5px;
}

.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: black;
  font-weight: 300;
  font-size: 14px;
  color: #717171;

}

.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: white;
  color: white;
}

.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px;
  background-color: white;
  color: white;
  margin-right: 35px;
}

.form-contact #contact .check_privacy + .title a {
  color: $corporate_1;
  font-size: 12px;
}
.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
}

.form-contact #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: $corporate_1 !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;

}

.form-contact #contact-button:hover {
  background-color: $corporate_1 !important;
}

.location-info {
  font-weight: 300;
  padding-left: 20px;
  box-sizing: border-box;
  font-size: 14px;
  color: #717171;
  line-height: 30px;
}

.how-to {
  @extend #wrapper_content;
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;

  h3 {
    font-size: 30px;
    margin-bottom: 20px;
    color: $corporate_1;
  }
}

.under_construction_wrapper {
  width: 100%;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  background-color: #000;

  img {
    position: absolute;
    top: 50%;
    left: 0;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);
    width: 100%;
  }
}
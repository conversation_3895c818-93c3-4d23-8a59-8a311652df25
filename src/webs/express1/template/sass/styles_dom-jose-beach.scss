@import "compass";

//Base web (change too in templateHand<PERSON> and in config.rb)
$base_web: "expr1";
// colors definitions
$white: rgb(255, 255, 255);
$black: rgb(0, 0, 0);
$gray-1: rgb(90, 90, 90);
$gray-2: rgb(120, 120, 120);
$gray-3: rgb(190, 190, 190);
$gray-4: rgb(230, 230, 230);

// corporative colors definitions
$corporate_1: #00bfd5;
$corporate_2: #e2900c;

// colors for booking widget
$booking_widget_color_1: $white;
//body back ground & year input text color
$booking_widget_color_2: $corporate_1;
//header background & input texts
$booking_widget_color_3: gray;
//label texts
$booking_widget_color_4: gray;
//not used, but must be defined

@import "booking/booking_engine";
@import "booking_engine";
@import "booking/selectric";
@import "fonts";

@import "template_specific";

body.interior section#slider_container {
  display: none;
}

header {
  position: inherit;
}

#logoDiv {
  img {
    margin-top: 30px;
  }
}

.top_header,
#mainMenuDiv,
#slider_container,
.content_subtitle_wrapper,
.bannersx3_wrapper,
.bottom_phrase,
.wrapper_footer_columns,
footer .social_likes{
  display: none;
}

section#content {
  padding-top: 0;
}

.content_access button#cancelButton {
  margin-bottom: 20px;
}

.grid_12.alpha.my-bookings-booking-info {
  margin: auto;
}

footer .full-copyright {
  //background: white;
}

header {
  height: auto;
  background: white;
  border-bottom: 2px solid $corporate_1;

  div#logoDiv {
    margin-top: 0;
    padding: 20px 0;

    img {
      margin-top: 0;
      vertical-align: middle;
    }
  }
}

footer {
  background: white;
}

.content_access {
  text-align: center;
  margin: 40px auto 0;
  position: relative;

  h3.section-title {
    color: $corporate_1;

    &:after {
      content: "";
      width: 30px;
      border-bottom: 6px dotted $corporate_1;
      display: block;
      margin: 5px auto 0;
    }
  }

  & > div {
    width: 700px;
    margin: 20px auto 0;
    text-align: justify;
  }

  #my-bookings-form {

    #my-bookings-form-fields {
      text-align: center;

      label {
        display: block;
        text-transform: uppercase;
        font-weight: 100;
      }

      input {
        display: block;
        margin: 10px auto;
        width: 250px;
        padding: 5px 0;
        box-sizing: border-box;
        text-align: center;
        border: 2px solid lightgray;
      }

      ul {
        li {
          display: inline-block;

          button {
            display: inline-block;
            border: 0;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            color: white;
            font-size: 16px;
            font-weight: 100;
            padding: 10px 30px;
            cursor: pointer;
            @include transition(opacity, .6s);

            &:hover {
              opacity: .8;
            }

            &.modify-reservation {
              background: $corporate_1;
            }

            &.cancelButton, &.searchForReservation {
              background: $corporate_2;
            }
          }
        }
      }
    }

    #reservation {

      .modify_reservation_widget {
        margin: 0 auto 40px;
        width: 531px;
        border: 3px solid #c8e3fd;
        background: #e3f1fe;
        padding: 10px;

        #motor_reserva {
          width: auto;
          display: inline-block;

          #booking_engine_title {
            display: none;
          }

          #contenedor_fechas {
            display: inline-block;
            width: 100%;

            #fecha_entrada, #fecha_salida {
              width: 220px;
              display: inline-block;
              float: left;
              text-align: center;
              height: auto;

              label {
                font-family: "Oswald";
                margin-bottom: 5px;
                display: inline-block;
              }

              input {
                width: 100%;
                box-sizing: border-box;
                text-align: center;
                height: 30px;
              }
            }

            #fecha_salida {
              float: right;
            }
          }

          #contenedor_habitaciones {
            margin-top: 5px;
            text-align: center;
            margin-left: 0;

            label {
              font-family: "Oswald";
              margin-bottom: 5px;
              display: inline-block;
            }

            select {
              display: block;
              margin: auto;
              width: 100px;
              text-align: center;
              margin-left: 0;
            }
          }

          #contenedor_opciones {
            margin-top: 5px;
            display: inline-block;
            width: 370px!important;

            & > div {
              text-align: center;
              margin: 0;
            }

            .numero_habitacion {
              display: inline-block;
              width: 100%;
              font-family: "Oswald";
              margin: 10px auto;
            }

            #hab1 {
              .adultos.numero_personas, .ninos.numero_personas {
                margin-top: 0;
              }
            }

            .adultos.numero_personas, .ninos.numero_personas {
              width: 50%;
              display: inline-block;
              float: left;
              margin: 0;
              margin-top: 5px;

              label {
                font-family: "Oswald";
                display: inline-block!important;
                margin-bottom: 5px;
              }

              select {
                display: block;
                width: 100px;
                margin: auto;
              }

              #info_ninos {
                display: none!important;
              }
            }
          }

          #envio {
            margin-top: 10px;
            height: auto;
            width: 100%;
            margin-left: 0;

            #promocode {
              -webkit-appearance: none;
              -moz-appearance: none;
              appearance: none;
              border: 1px solid gray;
              width: 50%;
              float: left;
              box-sizing: border-box;
              height: 38px;
              padding-left: 10px;
              display: none;
            }

            button {
              width: 100%;
              float: left;
              box-sizing: border-box;
              appearance: none;
              border: 0;
              background: $corporate_1;
              color: white;
              padding: 10px 0;
              font-size: 16px;
            }
          }
        }
      }

      .my-bookings-booking-info {
        margin: 0 auto;
      }
    }

    #cancel-button-container button {
      background: $corporate_1;
      display: inline-block;
      border: 0;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      color: white;
      font-size: 16px;
      font-weight: 100;
      padding: 10px 30px;
      margin: 40px auto 0;
      display: none;
      width: auto;
    }
  }
}
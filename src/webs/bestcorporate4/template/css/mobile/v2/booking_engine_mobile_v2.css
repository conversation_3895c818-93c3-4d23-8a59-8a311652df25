/* line 11, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector {
  overflow: hidden;
}
/* line 16, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  background: white;
  width: 100%;
  height: 100%;
  max-height: 100%;
  z-index: 99999;
  transition: all 0.5s ease-in-out;
}
/* line 29, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .top_wrapper {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 25px;
  margin-left: 15px;
  margin-top: 10px;
}
/* line 37, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .top_wrapper .close_icon {
  position: absolute;
  right: 20px;
  top: 30px;
  width: 25px;
  height: 25px;
}
/* line 44, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .top_wrapper .close_icon::after, .open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .top_wrapper .close_icon::before {
  content: "";
  width: 100%;
  height: 2px;
  position: absolute;
  right: 0;
  background: #2E2E2E;
}
/* line 52, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .top_wrapper .close_icon::after {
  transform: rotate(-45deg);
}
/* line 55, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .top_wrapper .close_icon::before {
  transform: rotate(45deg);
}
/* line 59, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .top_wrapper span {
  font-size: 16px;
  line-height: 19px;
  color: #424242;
  font-weight: normal;
}
/* line 65, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .top_wrapper i {
  font-size: 19px;
  font-weight: 300;
  color: #012379;
}
/* line 71, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .bottom_wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 30px;
  margin-top: 30px;
  margin-bottom: 30px;
}
/* line 79, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  width: 100%;
}
/* line 84, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options .all_hotels_filter, .open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options .featured_hotels_filter {
  color: #012379;
  font-weight: bold;
  line-height: 15px;
}
/* line 88, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options .all_hotels_filter i, .open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options .featured_hotels_filter i {
  margin-right: 10px;
  font-weight: 300;
}
/* line 92, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options .all_hotels_filter:not(.active), .open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options .featured_hotels_filter:not(.active) {
  opacity: .5;
}
/* line 97, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .bottom_wrapper .hotels_destiny_search {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* line 102, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .bottom_wrapper .hotels_destiny_search .input_filter_option {
  width: 85%;
  height: 45px;
  border-radius: 30px;
  padding: 0px 0px 0px 55px;
  border: 1px solid #707070;
  color: #707070;
  font-size: 17px;
  letter-spacing: 1px;
}
/* line 111, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .bottom_wrapper .hotels_destiny_search .input_filter_option::placeholder {
  color: #707070;
  font-size: 17px;
  letter-spacing: 1px;
}
/* line 117, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .header_destination_wrapper .bottom_wrapper .hotels_destiny_search i {
  position: absolute;
  left: 50px;
  color: #424242;
  font-weight: 300;
  font-size: 20px;
}
/* line 127, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .grouped_hotels .group_element {
  padding: 0px;
}
/* line 129, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .grouped_hotels .group_element .group_label {
  color: #222222;
  font-size: 16px;
  letter-spacing: 0.26px;
  text-transform: uppercase;
  font-weight: normal;
  padding: 0 50px 0 50px;
  width: 90%;
  line-height: 50px;
}
/* line 139, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .grouped_hotels .group_element .group_label i {
  font-weight: 300;
  color: #2074CA;
}
/* line 144, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .grouped_hotels .group_element .group_options {
  background: white;
  padding-right: 15px;
  padding-left: 15px;
  width: 80%;
  margin: 0 auto;
  border: 1px solid #707070;
  border-bottom: none;
}
/* line 152, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .grouped_hotels .group_element .group_options .group_header {
  margin: -10px 0px 25px 0px;
  border-bottom: 1px solid #707070;
  line-height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  font-size: 16px;
}
/* line 161, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .grouped_hotels .group_element .group_options .group_header span {
  font-size: 16px;
  color: #2074CA;
  font-weight: bold;
}
/* line 165, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .grouped_hotels .group_element .group_options .group_header span.total_hotels {
  font-weight: normal;
}
/* line 169, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .grouped_hotels .group_element .group_options .group_header .destiny_b0 {
  display: flex !important;
  align-items: center;
  gap: 10px;
  font-weight: bold;
  color: #2074CA !important;
}
/* line 175, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .grouped_hotels .group_element .group_options .group_header .destiny_b0 i {
  font-size: 15px;
}
/* line 178, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .grouped_hotels .group_element .group_options .group_header .destiny_b0::before {
  content: "\f3c5";
  font-family: "Font Awesome 5 Pro";
  font-weight: 200;
}
/* line 185, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .grouped_hotels .group_element .group_options .hotel_element {
  font-size: 16px;
  font-weight: bold;
}
/* line 191, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .grouped_hotels .group_element.active .group_options {
  border-bottom: 1px solid #707070;
}
/* line 197, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .featured_wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
/* line 202, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .featured_wrapper .featured_element {
  height: 270px;
  width: 90%;
  margin: 5px;
}
/* line 206, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .featured_wrapper .featured_element::before {
  content: "";
  position: absolute;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.6), transparent);
  width: 90%;
  height: 110px;
  left: 50%;
  transform: translate(-50%, 0%);
  z-index: 1;
}
/* line 216, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .featured_wrapper .featured_element .group_label {
  font-size: 14px;
  color: white;
  position: relative;
  z-index: 1;
  line-height: 24px;
  font-weight: normal;
  padding: 0px;
  margin: 20px 0px 0px 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 2px;
}
/* line 229, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .featured_wrapper .featured_element .group_label::before {
  content: "\f3c5";
  font-family: "Font Awesome 5 Pro";
  font-weight: 200;
}
/* line 234, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .featured_wrapper .featured_element .group_label i {
  position: relative;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  transform: none;
}
/* line 243, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .featured_wrapper .featured_element .featured_options {
  margin: 0px 0px 0px 20px;
}
/* line 246, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .featured_wrapper .featured_element .hotel_element {
  font-size: 24px;
  color: white !important;
  font-weight: normal;
  font-style: italic;
  position: relative;
  z-index: 1;
}
/* line 254, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .featured_wrapper .featured_element .hotel_element span {
  color: white;
  font-weight: 700;
}
/* line 259, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #full_wrapper_booking.hotel_selector_popup .hotel_selector .destination_wrapper .featured_wrapper .featured_element .hotel_element i {
  color: white;
}
/* line 272, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #occupancy_popup .header_wrapper .banner_title {
  color: #424242;
  text-transform: capitalize;
  font-size: 16px;
  font-style: normal;
  font-weight: normal;
}
/* line 278, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #occupancy_popup .header_wrapper .banner_title i {
  color: #012379;
  font-weight: 100;
}
/* line 283, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #occupancy_popup .header_wrapper .close_popup {
  color: #222222;
}
/* line 285, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #occupancy_popup .header_wrapper .close_popup i {
  font-weight: 100;
}
/* line 292, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #occupancy_popup .rooms_wrapper .rooms_number_wrapper .rooms_label {
  font-size: 16px;
}
/* line 298, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #occupancy_popup .rooms_wrapper .room_list .room .room_title {
  font-size: 15px;
  color: #424242;
  font-weight: 600;
}
/* line 304, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #occupancy_popup .rooms_wrapper .room_list .room_list_wrapper_close {
  background: #F7BB1E;
  font-size: 22px;
  letter-spacing: 0.87px;
  font-weight: 700;
}
/* line 312, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #occupancy_popup .rooms_wrapper .modification_buttons i {
  border-color: #424242;
  color: #424242;
}
/* line 315, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #occupancy_popup .rooms_wrapper .modification_buttons i::after, .open_popup_selector .mobile_engine_v2 #occupancy_popup .rooms_wrapper .modification_buttons i::before {
  background: #424242;
}
/* line 319, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .mobile_engine_v2 #occupancy_popup .rooms_wrapper .modification_buttons input {
  color: #424242;
}
/* line 328, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .calendar_root_wrapper.active {
  top: 0px;
  position: fixed;
  z-index: 999;
  height: 100%;
  width: 100%;
  left: 0;
  background: white;
  right: 0;
  bottom: 0;
  transition: all 0.5s ease-in-out;
}
/* line 339, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .calendar_root_wrapper.active .close_calendar_app {
  width: 20px;
  height: 20px;
  top: 20px;
  right: 25px;
  z-index: 998;
  position: absolute;
}
/* line 346, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .calendar_root_wrapper.active .close_calendar_app::after, .open_popup_selector .calendar_root_wrapper.active .close_calendar_app::before {
  content: "";
  width: 100%;
  height: 2px;
  background: #424242;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 357, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .calendar_root_wrapper.active .close_calendar_app::after {
  transform: translate(-50%, -50%) rotate(-45deg);
}
/* line 362, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .calendar_root_wrapper.active .header_calendar {
  position: absolute;
  top: 0;
  z-index: 999;
  display: flex !important;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  padding: 30px;
  background: white;
  height: 30px;
  gap: 20px;
  border-bottom: 1px solid #8D8D8D;
  font-size: 16px;
  color: #424242;
  font-weight: normal;
}
/* line 378, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .calendar_root_wrapper.active .header_calendar i {
  font-size: 24px;
  color: #012379;
}
/* line 387, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.open_popup_selector .calendar_root_wrapper .header_calendar {
  display: none;
}

/* line 395, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .wrapper_booking_button .promocode_wrapper .promocode_input:focus::placeholder {
  opacity: 0;
}
/* line 402, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .hotel_selector .booking_title {
  padding: 0px 0px 7px 10px;
  font-size: 16px;
}
/* line 405, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .hotel_selector .booking_title .title_label {
  color: #222222;
}
/* line 409, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .hotel_selector .selected_hotel {
  font-size: 16px;
  padding: 20px 0px 20px 0px;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  border-bottom: 1px solid #d0d0d0;
}
/* line 418, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .hotel_selector .selected_hotel .destiny {
  display: block !important;
}
/* line 422, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .hotel_selector .selected_hotel::after {
  display: none;
  content: "\f078";
  position: absolute;
  color: #012379;
  font-family: "Font Awesome 5 Pro";
  margin-left: 20px;
  margin-top: 4px;
}
/* line 434, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .room_list_wrapper .booking_title {
  padding: 0px 0px 7px 10px;
  font-size: 16px;
}
/* line 437, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .room_list_wrapper .booking_title .title_label {
  color: #222222;
}
/* line 443, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .room_list_wrapper .total_occupancy_wrapper .rooms_number_value::after {
  display: none;
  content: "\f078";
  position: absolute;
  color: #012379;
  font-family: "Font Awesome 5 Pro";
  margin-left: 20px;
  margin-top: 4px;
}
/* line 456, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .room_list_wrapper .occupancy_number .occupancy_number_value::after {
  display: none;
  content: "\f078";
  position: absolute;
  color: #012379;
  font-family: "Font Awesome 5 Pro";
  margin-left: 20px;
  margin-top: 4px;
}
/* line 468, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper:not(.active) {
  top: calc(100% + 400px);
  bottom: -150%;
  transition: all 0.5s ease-in-out;
  position: fixed;
  z-index: 999;
  height: 100%;
  width: 100%;
  left: 0;
  right: 0;
  background: white;
  max-height: 100% !important;
}
/* line 482, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .calendar_app {
  overflow-y: auto;
  height: 100%;
  width: 100%;
  position: relative;
  top: 30px;
}
/* line 489, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper {
  top: 60px;
  overflow-y: hidden;
  overflow-x: hidden;
  padding-bottom: 50px;
  width: 100%;
  position: relative;
  margin: 0 auto;
  z-index: 999;
  box-shadow: none;
}
/* line 499, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container {
  padding: 0px;
  padding-bottom: 220px;
}
/* line 502, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .calendar_wrapper {
  margin: 0px auto;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
/* line 514, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper:not(.selected) {
  color: #121317 !important;
}
/* line 519, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.has_min_stay.selected::before {
  border: none;
}
/* line 526, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected .day {
  color: white;
}
/* line 530, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected::before {
  background-color: #2074CA;
}
/* line 534, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.selected::after {
  background-color: #2074CA;
  opacity: .13;
}
/* line 540, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.hover::after {
  background-color: #2074CA;
}
/* line 544, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper .day {
  font-weight: normal;
  color: #222222;
}
/* line 550, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.disabled .day {
  opacity: .45;
}
/* line 555, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper table.month_wrapper tbody tr td.day_wrapper.loading_prices {
  pointer-events: none;
}
/* line 568, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .calendar_wrapper .month_full_wrapper .month_selector_wrapper .month_selector .selector_full_wrapper .selector_wrapper .selector_label {
  font-size: 24px;
  color: #012379;
  line-height: 32px;
  font-weight: bold;
}
/* line 581, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper {
  display: flex;
  flex-direction: column;
  width: 100%;
  margin-left: 0px;
}
/* line 586, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top {
  display: flex;
  justify-content: flex-start;
  flex-direction: column;
  position: relative;
}
/* line 591, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .info_currency_wrapper {
  width: 100%;
}
/* line 594, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .legends_wrapper {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding-left: 15px;
  gap: 10px;
}
/* line 601, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .legends_wrapper .legend {
  font-size: 16px;
  color: #8D8D8D;
  font-weight: normal;
  letter-spacing: 0.35px;
  line-height: 22px;
}
/* line 609, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .legends_wrapper .legend .icon.no_dispo {
  background: url(https://storage.googleapis.com/cdn.paraty.es/best-remember/files/no_dispo_icon.png) no-repeat;
  background-size: contain;
}
/* line 614, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .legends_wrapper .legend .icon.min_stay {
  background: url(https://storage.googleapis.com/cdn.paraty.es/best-remember/files/min_est_icon.png) no-repeat;
  background-size: contain;
}
/* line 621, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .toggle_chart {
  display: none;
}
/* line 624, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .notice_info {
  display: none;
}
/* line 627, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper {
  position: absolute;
  bottom: -150px;
  width: 100%;
  display: unset;
}
/* line 634, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .filter_selector .selector_full_wrapper {
  width: 100%;
}
/* line 636, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .filter_selector .selector_full_wrapper .selector_wrapper {
  width: 100%;
  padding: 15px;
}
/* line 639, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .filter_selector .selector_full_wrapper .selector_wrapper .selector_label {
  border: 1px solid #222222 !important;
  border-radius: 0;
  padding: 15px;
  width: 100%;
  font-size: 16px;
  letter-spacing: 0.35px;
  line-height: 22px;
  color: #222222;
}
/* line 648, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .filter_selector .selector_full_wrapper .selector_wrapper .selector_label svg {
  display: none;
}
/* line 651, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .filter_selector .selector_full_wrapper .selector_wrapper .selector_label::before {
  content: "\f0b0";
  font-family: "Font Awesome 5 Pro";
  font-weight: 100;
  margin-right: 10px;
}
/* line 660, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .top .selectors_wrapper .filters_selector_wrapper .filter_selector .selector_full_wrapper .selector_wrapper .selector .options .option {
  font-size: 16px;
  letter-spacing: 0.35px;
  line-height: 26px;
  color: #222222;
}
/* line 674, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .bottom {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  flex-direction: column;
  gap: 20px;
}
/* line 680, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .bottom .left_wrapper {
  align-items: center;
  display: flex;
  padding-left: 15px;
}
/* line 685, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .bottom .left_wrapper .amount_item {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #8D8D8D;
  font-size: 16px;
  line-height: 19px;
  letter-spacing: 0.8px;
}
/* line 693, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .bottom .left_wrapper .amount_item:nth-child(3) {
  display: none;
}
/* line 696, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .bottom .left_wrapper .amount_item .amount {
  font-size: 16px;
  color: #1D2634;
  letter-spacing: 0.8px;
  line-height: 19px;
  font-weight: 500;
}
/* line 706, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper {
  display: flex;
  align-items: center;
}
/* line 709, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .custom-button {
  display: none;
}
/* line 712, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .calendar_root_wrapper .price_calendar_wrapper .full_container .bottom_wrapper .bottom .right_wrapper .clear_dates {
  margin-left: 15px;
  color: #8D8D8D;
  border-bottom: 1px solid #8D8D8D;
  font-size: 16px;
  letter-spacing: 0.35px;
  line-height: 19px;
  text-decoration: none;
}
/* line 728, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper {
  top: calc(100% + 400px);
  bottom: -150%;
  position: fixed;
  transition: all 0.5s ease-in-out;
  background: white;
  max-height: 100%;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
  z-index: 99999;
  transition: all 0.5s ease-in-out;
}
/* line 742, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .top_wrapper {
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 25px;
  margin-left: 15px;
  margin-top: 10px;
}
/* line 750, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .top_wrapper .close_icon {
  position: absolute;
  right: 20px;
  top: 30px;
  width: 25px;
  height: 25px;
}
/* line 757, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .top_wrapper .close_icon::after, #full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .top_wrapper .close_icon::before {
  content: "";
  width: 100%;
  height: 2px;
  position: absolute;
  right: 0;
  background: #2E2E2E;
}
/* line 765, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .top_wrapper .close_icon::after {
  transform: rotate(-45deg);
}
/* line 768, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .top_wrapper .close_icon::before {
  transform: rotate(45deg);
}
/* line 772, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .top_wrapper span {
  font-size: 16px;
  line-height: 19px;
  color: #424242;
  font-weight: normal;
}
/* line 778, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .top_wrapper i {
  font-size: 19px;
  font-weight: 300;
  color: #012379;
}
/* line 784, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .bottom_wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  gap: 30px;
  margin-top: 30px;
  margin-bottom: 30px;
}
/* line 792, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  width: 100%;
}
/* line 797, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options .all_hotels_filter, #full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options .featured_hotels_filter {
  color: #012379;
  font-weight: bold;
  line-height: 15px;
}
/* line 801, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options .all_hotels_filter i, #full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options .featured_hotels_filter i {
  margin-right: 10px;
  font-weight: 300;
}
/* line 805, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options .all_hotels_filter:not(.active), #full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .bottom_wrapper .filter_options .featured_hotels_filter:not(.active) {
  opacity: .5;
}
/* line 810, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .bottom_wrapper .hotels_destiny_search {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
/* line 815, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .bottom_wrapper .hotels_destiny_search .input_filter_option {
  width: 85%;
  height: 45px;
  border-radius: 30px;
  padding: 0px 0px 0px 55px;
  border: 1px solid #707070;
  color: #707070;
  font-size: 17px;
  letter-spacing: 1px;
}
/* line 824, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .bottom_wrapper .hotels_destiny_search .input_filter_option::placeholder {
  color: #707070;
  font-size: 17px;
  letter-spacing: 1px;
}
/* line 830, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .header_destination_wrapper .bottom_wrapper .hotels_destiny_search i {
  position: absolute;
  left: 50px;
  color: #424242;
  font-weight: 300;
  font-size: 20px;
}
/* line 840, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .grouped_hotels .group_element {
  padding: 0px;
}
/* line 842, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .grouped_hotels .group_element .group_label {
  color: #222222;
  font-size: 16px;
  letter-spacing: 0.26px;
  text-transform: uppercase;
  font-weight: normal;
  padding: 0 50px 0 50px;
  width: 90%;
  line-height: 50px;
}
/* line 852, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .grouped_hotels .group_element .group_label i {
  font-weight: 300;
  color: #2074CA;
}
/* line 857, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .grouped_hotels .group_element .group_options {
  background: white;
  padding-right: 15px;
  padding-left: 15px;
  width: 80%;
  margin: 0 auto;
  border: 1px solid #707070;
  border-bottom: none;
}
/* line 865, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .grouped_hotels .group_element .group_options .group_header {
  margin: -10px 0px 25px 0px;
  border-bottom: 1px solid #707070;
  line-height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 10px;
  font-size: 16px;
}
/* line 874, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .grouped_hotels .group_element .group_options .group_header span {
  font-size: 16px;
  color: #2074CA;
  font-weight: bold;
}
/* line 878, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .grouped_hotels .group_element .group_options .group_header span.total_hotels {
  font-weight: normal;
}
/* line 882, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .grouped_hotels .group_element .group_options .group_header .destiny_b0 {
  display: flex !important;
  align-items: center;
  gap: 10px;
  font-weight: bold;
  color: #2074CA !important;
}
/* line 888, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .grouped_hotels .group_element .group_options .group_header .destiny_b0 i {
  font-size: 15px;
}
/* line 891, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .grouped_hotels .group_element .group_options .group_header .destiny_b0::before {
  content: "\f3c5";
  font-family: "Font Awesome 5 Pro";
  font-weight: 200;
}
/* line 898, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .grouped_hotels .group_element .group_options .hotel_element {
  font-size: 16px;
  font-weight: bold;
}
/* line 904, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .grouped_hotels .group_element.active .group_options {
  border-bottom: 1px solid #707070;
}
/* line 910, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .featured_wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
}
/* line 915, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .featured_wrapper .featured_element {
  height: 270px;
  width: 90%;
  margin: 5px;
}
/* line 919, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .featured_wrapper .featured_element::before {
  content: "";
  position: absolute;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.6), transparent);
  width: 90%;
  height: 110px;
  left: 50%;
  transform: translate(-50%, 0%);
  z-index: 1;
}
/* line 929, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .featured_wrapper .featured_element .group_label {
  font-size: 14px;
  color: white;
  position: relative;
  z-index: 1;
  line-height: 24px;
  font-weight: normal;
  padding: 0px;
  margin: 20px 0px 0px 20px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 2px;
}
/* line 942, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .featured_wrapper .featured_element .group_label::before {
  content: "\f3c5";
  font-family: "Font Awesome 5 Pro";
  font-weight: 200;
}
/* line 947, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .featured_wrapper .featured_element .group_label i {
  position: relative;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  transform: none;
}
/* line 956, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .featured_wrapper .featured_element .featured_options {
  margin: 0px 0px 0px 20px;
}
/* line 959, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .featured_wrapper .featured_element .hotel_element {
  font-size: 24px;
  color: white !important;
  font-weight: normal;
  font-style: italic;
  position: relative;
  z-index: 1;
}
/* line 967, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .featured_wrapper .featured_element .hotel_element span {
  color: white;
  font-weight: 700;
}
/* line 972, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
#full_wrapper_booking.new_booking_engine .destination_wrapper .featured_wrapper .featured_element .hotel_element i {
  color: white;
}

/* line 981, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.dates_selector_personalized {
  border-bottom: 1px solid #d0d0d0;
}
/* line 983, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.dates_selector_personalized .dates_selector_label {
  width: 95%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 13px 10px 20px;
  border-bottom: 1px solid #d0d0d0;
  margin: 0 auto;
  text-transform: uppercase;
  color: #222222;
}
/* line 993, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.dates_selector_personalized .dates_selector_label i {
  font-size: 19px;
  color: #2074CA;
}
/* line 998, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.dates_selector_personalized .start_end_date_wrapper {
  display: flex;
  align-items: center;
  justify-content: space-evenly;
  flex-direction: row;
  height: 70px;
}
/* line 1004, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.dates_selector_personalized .start_end_date_wrapper .start_date_wrapper, .dates_selector_personalized .start_end_date_wrapper .end_date_wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  height: 80%;
}
/* line 1011, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.dates_selector_personalized .start_end_date_wrapper .start_date_wrapper .start_date_personalized, .dates_selector_personalized .start_end_date_wrapper .start_date_wrapper .end_date_personalized, .dates_selector_personalized .start_end_date_wrapper .end_date_wrapper .start_date_personalized, .dates_selector_personalized .start_end_date_wrapper .end_date_wrapper .end_date_personalized {
  font-weight: bold;
  color: #012379;
}
/* line 1014, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.dates_selector_personalized .start_end_date_wrapper .start_date_wrapper .start_date_personalized::after, .dates_selector_personalized .start_end_date_wrapper .start_date_wrapper .end_date_personalized::after, .dates_selector_personalized .start_end_date_wrapper .end_date_wrapper .start_date_personalized::after, .dates_selector_personalized .start_end_date_wrapper .end_date_wrapper .end_date_personalized::after {
  display: none;
  content: "\f078";
  position: absolute;
  color: #012379;
  font-family: "Font Awesome 5 Pro";
  margin-left: 10px;
  margin-top: 4px;
}
/* line 1024, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.dates_selector_personalized .start_end_date_wrapper .start_date_wrapper span, .dates_selector_personalized .start_end_date_wrapper .end_date_wrapper span {
  color: #222222;
}
/* line 1028, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.dates_selector_personalized .start_end_date_wrapper .start_date_wrapper {
  border-right: 1px solid #d0d0d0;
}
/* line 1032, ../../../sass/mobile/v2/booking_engine_mobile_v2.scss */
.dates_selector_personalized .nights_number_wrapper_personalized {
  display: none;
}

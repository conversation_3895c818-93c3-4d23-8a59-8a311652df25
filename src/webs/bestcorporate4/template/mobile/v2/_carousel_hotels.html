<div class="banners_carousel_wrapper">
    <div class="general_title_block banners_bottom_title">
        <p class="mini_title">{{ C_our_proposals|safe }}</p>
        <div class="main_title">{{ C_recommend_hotels|safe }}</div>
    </div>
    {% if hotels_carousel_subtitle %}
        <div class="subtitle_wrapper">{{ hotels_carousel_subtitle|safe }}</div>
    {% endif %}
    <div class="carousel_wrapper">
        <div class="owl-carousel owl_banners_carousel">
            {% for banner in carousel_hotels %}
                <div class="banner_element">
                    <div class="image_wrapper">
                        {% if banner.since %}
                            <div class="since_wrapper">
                                <span class="since_label">{{ T_desde }}</span>
                                {{ banner.since|safe }}€
                            </div>
                        {% endif %}
                        <img src="{{ banner.pictures.0.servingUrl|safe }}=s140" alt="" class="image_element">
                    </div>
                    <div class="content_wrapper general_title_block">
                        <p class="mini_title">{{ banner.destiny_name|safe }}</p>
                        <div class="main_title">
                            {% if banner.short_name %}{{ banner.short_name|safe }}{% else %}{{ banner.title|safe }}{% endif %}
                            <div class="hotel_category">
                                {% for star in banner.category|create_range %}
                                    <span class="category"><i class="fas fa-star"></i></span>
                                {% endfor %}
                            </div>
                            {% for star in category %}<span class="key"></span>{% endfor %}
                        </div>
                        <div class="tags_wrapper">
                            {% for tag in banner.keywords|slice:":2" %}
                                <div class="tag_element">{{ tag.description|safe }}</div>
                            {% endfor %}
                        </div>
                        {% if banner.friendlyUrlInternational %}
                            <a href="{{ banner.friendlyUrlInternational|safe }}" class="see_hotel_link">{{ T_ver_hotel }}</a>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>

{#{% for key in carousel_hotels.0 %}#}
{#    {{ key }}#}
{#{% endfor %}#}
{#{{ carousel_hotels.0 }}#}
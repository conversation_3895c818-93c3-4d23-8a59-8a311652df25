{% extends 'default_mobile.html' %}

{% block 'main_block' %}

<div class="filters_button">
    <i class="fas fa-sliders-h"></i>
    {{ T_filtrar }}
</div>

<div id="filter_wrapper_banner">
    <div id="close_filters">
        <i class="fal fa-times"></i>
    </div>

    <div class="banner_title">
        <i class="fas fa-sliders-h"></i>
        {{ T_filtrar }}
    </div>

    <div class="availabler_filters_wrapper">
        <div class="destiny_filters_wrapper filter_block">
            <div class="filter_title">
                {{ T_filtar_destino }}
                <i class="fas more fa-plus-circle"></i>
                <i class="fal less fa-minus-circle"></i>
            </div>
            <div class="options_list"></div>
        </div>

        <div class="themes_filters_wrapper filter_block">
            <div class="filter_title">
                {{ C_por_temas }}
                <i class="fas more fa-plus-circle"></i>
                <i class="fal less fa-minus-circle"></i>
            </div>
            <div class="options_list"></div>
        </div>

        <div class="offers_filters_wrapper filter_block">
            <div class="filter_title">
                {{ C_por_ofertas }}
                <i class="fas more fa-plus-circle"></i>
                <i class="fal less fa-minus-circle"></i>
            </div>
            <div class="options_list">
            </div>
        </div>
    </div>

    <div class="filters_buttons_wrapper">
        <div id="clear_filters_button">{{ T_borrar_filtros }}</div>
        <div id="apply_filters_button">{{ T_aplicar_filtros }}</div>
    </div>
</div>

<div class="hotels_found">
   <span class="hotels_number"></span>
    <span class="more_found" style="display: none">{{ T_hoteles_encontrados }}</span>
    <span class="less_found" style="display: none">{{ T_hotel_encontrado }}</span>
</div>

<div class="hotels_buttons_wrapper">
    <div class="view_list active">
        <i class="fas fa-list-ul"></i>
        {{ T_ver_listado }}
    </div>
    <div class="view_map">
        <!-- https://openlayers.org/en/latest/doc/quickstart.html -->
        <i class="fal fa-map-marker-alt"></i>
        {{ T_ver_mapa }}
    </div>
</div>

<div class="hotels_map_wrapper">
    {% include "_hotels_list.html" %}
    {% include "_hotels_map.html" %}
</div>


<link rel="stylesheet" type="text/css" href="/css/best4/mobile/hotels_section_mobile.css?v=1.13" />

    <script>
        //Offers
        var promotions_list = [];
        $(".promotions_list_popup").each(function () {
            var available_promotions = $(this).find("li");
            available_promotions.each(function () {
                var copied = $(this).find(".title_promotion").clone();
                    copied.find("i").remove();
                    var target_html = copied.html();
                if (promotions_list.indexOf(target_html) === -1) {
                    promotions_list.push(target_html);
                }
            });
        });

        promotions_list.forEach(function (element, n) {
            var option_element = $("<div class='option_element'></div>"),
                radio_button = $("<input type='checkbox' name='offers_filter'>"),
                label_element = $("<label></label>");

            radio_button.attr('id', 'offer' + n);
            label_element.attr('for', 'offer' + n);

            label_element.html(element);
            radio_button.attr('value', element);
            option_element.append(radio_button).append(label_element);
            $(".offers_filters_wrapper .options_list").append(option_element);
        });
    </script>

{% endblock %}
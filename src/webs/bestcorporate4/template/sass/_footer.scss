footer {
  background-color: white;
  box-sizing: border-box;
  * {
    box-sizing: border-box;
  }

  .full_footer_columns_wrapper {
    position: relative;
    overflow: hidden;
    padding: 80px 0;

    img.background_footer_columns {
      @include center_image;
      max-height: 100%;
    }

    .center_footer_columns {
      position: relative;
    }

    .footer_columns {
      &.newsletter_wrapper_footer {
        border-right: 1px solid white;

        .newsletter_content {
          width: 47%;
          float: left;
        }

        .titles_wrapper {
          text-align: left;
          display: inline-block;
          padding-bottom: 0;
          padding-top: 0;
          margin-bottom: 10px;
        }

        .newsletter_footer_description {
          margin-top: 10px;
          font-size: 17px;
          color: #2d2d2d;
          line-height: 26px;
          font-weight: 300;
        }
      }
    }

    h2.newsletter_footer_title, .club_register_title {
      font-size: 35px;
      font-weight: 300;
      color: white;
      margin-bottom: 13px;
    }
  }

  .secciones_footer_wrapper {
    color: #424242;
    padding: 40px calc((100% - 1140px) / 2);
    text-align: center;
    @include display_flex;
    justify-content: center;
    width: 100%;
    .section_col {
      display: inline-block;
      text-align: left;
      width: calc(100% / 4);
      font-size: 16px;
      line-height: 26px;

      &:not(:first-of-type) {
        padding-left: 40px;
      }

      &:nth-of-type(3) {
        padding-left: 80px;
      }

      .col_img {
        display: block;
        margin-bottom: 15px;
      }
      h2 {
        text-transform: uppercase;
        font-weight: 700;
        font-size: 18px;
        letter-spacing: 0.6px;
        padding-bottom: 5px;
      }
      .desc {
        font-weight: 300;
        letter-spacing: 0.6px;
        line-height: 32px;
      }
      .links_list {
        a {
          display: table;
          text-align: left;
          color: #2d2d2d;
          padding: 3px 0;
          text-decoration: none;
          @include transition(opacity, .6s);
          letter-spacing: 0.6px;
          padding-bottom: 5px;
          position: relative;

          &:before {
            content: '';
            bottom: 5px;
            height: 1px;
            left: 0;
            right: 100%;
            background: #2d2d2d;
            @include transition(all,.5s);
            display: block;
            position: absolute;
          }

          &:hover {
            opacity: .7;

            &:before {
              right: 0;
            }
          }
        }
      }
    }
    &.double {
      .section_col {
        width: calc(100% / 2);
        text-align: center;
        position: relative;
        &:first-of-type {
          &:before {
            content: "";
            width: 1px;
            height: 100%;
            background: $corporate_6;
            position: absolute;
            left: 100%;
            opacity: .3;
          }
        }
      }
    }
  }

  .footer_menu_wrapper {
    background: $lightgrey;
    width: 100%;
    .footer_menu {
      @include display_flex;
      width: 1140px;
      align-items: center;
      justify-content: center;
      margin: auto;
      .list_select {
        width: calc(100% / 4);
        padding: 7px 0;
        color: $color_text_light;
        letter-spacing: 1px;
        font-weight: 700;
        font-size: 15px;

        .footer_selector {
          width: max-content;
          cursor: pointer;
          i {
            pointer-events: none;
            font-size: 30px;
            vertical-align: middle;
            margin-left: 10px;
          }
        }
        &.active {


          .footer_selector {
            color: $corporate_1;

            i {
              @include transform(rotate(180deg));
              margin-top: -5px;
            }
          }
        }
      }
    }
  }
  .hidden_list {
    .footer_list {
      display: grid;
      grid-template-columns: calc(100% / 4) calc(100% / 4) calc(100% / 4) calc(100% / 4);
      width: 1140px;
      margin: auto;
      text-align: left;
      padding-top: 20px;
      a {
        text-decoration: none;
        outline: none;
        color: $color_text_light;
        padding: 9px 0;
        font-size: 14px;
        font-weight: 400;
        letter-spacing: 0.5px;

        &:hover {
          color: $corporate_1;
          @include transition(color, .5s)
        }
      }
    }
  }

  .bottom_footer_wrapper {
    padding: 26px 0;

    .footer-copyright {
      text-align: center;
      color: #2d2d2d;
      font-size: 14px;
      font-weight: 300;
      margin-bottom: 7px;

      a {
        color: #2d2d2d;
        font-size: 14px;
        font-weight: 300;
        margin-bottom: 12px;
        text-decoration: none;
        -webkit-transition: all 0.5s;
        -moz-transition: all 0.5s;
        -ms-transition: all 0.5s;
        -o-transition: all 0.5s;
        transition: all 0.5s;

        &:hover {
          opacity: 0.7;
        }
      }
    }

    div#div-txt-copyright {
      p {
        color: #2d2d2d;
        font-size: 14px;
        font-weight: 300;
        text-decoration: none;

        &:first-of-type {
          margin-bottom: 7px;
        }
      }
    }
  }
}

html[lang="de"] {
  .titles_wrapper {
    .main_title {
      line-height: 48px;
    }
  }
}
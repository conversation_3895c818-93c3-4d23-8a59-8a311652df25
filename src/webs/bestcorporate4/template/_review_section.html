<div class="review_section_wrapper">

    <div class="hotel_review_selector">
        <select class="hotel_select_review">
            {% for hotel_element in hotels_review %}
                <option class="hotel_element" value="{{ hotel_element.hotel_namespace|safe }}">{{ hotel_element.title|safe }}</option>
            {% endfor %}
        </select>
    </div>

    {% for hotel_element in hotels_review %}
        <div class="trust_wrapper_element trust_namespace_{{ hotel_element.hotel_namespace|safe }}" {% if not forloop.first %}style="display: none;"{% endif %}>
            {{ hotel_element.trust_you|safe }}
        </div>
    {% endfor %}

</div>

<script>
    $(function(){
        $(".hotel_select_review").selectric({
            onChange: function(element){
                $(".trust_wrapper_element").slideUp(function(){
                   $(".trust_namespace_" + $(element).val()).slideDown();
                });
            }
        });



        url_param_value = getUrlParameter('hotel_namespace');
        if (url_param_value){
            $(".hotel_select_review").val(url_param_value);
            $(".hotel_select_review").selectric('refresh');

            $(".trust_wrapper_element").slideUp(function () {
                $(".trust_namespace_" + url_param_value).slideDown();
            });
{#            $(".hotel_select_review option[value='" + url_param_value + "']").trigger('click');#}
        }
    });
</script>
<div id="hotel_services" class="individual_hotel_services_wrapper">
    {% if services_blocks_sec %}
    <div class="general_title_block titles_wrapper">
        <p class="mini_title">{{ services_blocks_sec.subtitle|safe }}</p>
        <h2 class="main_title">{{ services_blocks_sec.content|safe }}</h2>
    </div>
    {% endif %}
    <div class="services_individual_hotel_wrapper active">
        <div class="services_wrapper">
            {% for service_element in individual_hotel.services_pictures %}
                <div class="individual_service_item">
                    <div class="service_icon_wrapper">
                        {% if service_element.icon %}
                            <i class="{{ service_element.icon }}" {% if service_element.color %}style="color:{{ service_element.color|safe }}"{% endif %}></i>
                        {% else %}
                            <img class="service_icon" src="{{ service_element.servingUrl }}" alt="">
                        {% endif %}
                    </div>
                    <div class="service_description">
                        {{ service_element.description|safe }}
                        <div class="see_more_icon">
                            <i class="fal fa-plus"></i>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>

<script>
    /*
    $(".services_title").click(function(){
        $(this).closest(".services_individual_hotel_wrapper").toggleClass('active');
    });*/

    $(".individual_service_item").each(function(){
        var hotel_namespace = "{{ individual_hotel.hotel_namespace }}";
        var has_hidden_namespace = $(this).find("hide[namespace='" + hotel_namespace + "']").length;

        if (has_hidden_namespace) {
            $(this).addClass('has_hidden_description');
            $(this).find("hide").each(function(){
                var namespace = $(this).attr('namespace');
                if (namespace != "{{ individual_hotel.hotel_namespace }}"){
                    $(this).remove();
                } else {
                    var has_link = $(this).attr('href');
                    if (has_link) {
                        let target_link = has_link.indexOf('.pdf') > -1 ? 'is_link_pdf is_link' : 'is_link';
                        $(this).closest('.individual_service_item').addClass(target_link);
                    }
                    if (has_link && !$(this).find("hide").html()) {
                         $(this).closest('.individual_service_item').addClass("no_text");
                    }
                }


            })
        }
    });

    $(".individual_service_item.has_hidden_description").click(function(e){
        var has_link = $(this).hasClass('is_link');
        if ($(e.target).hasClass('see_more_icon') || $(e.target).closest('.see_more_icon').length) {
            {#var target_html = $(this).find(".service_description hide").html();#}
            {#WebsiteController.open_popup_v2(target_html);#}
            $(this).toggleClass('active');
            return;
        }

        if (has_link) {
            var target_link = $(this).find("hide").attr('href');
            window.open(target_link, '_blank');
        } else {
            if ($(e.target).hasClass('pdf')) return;
            $(this).toggleClass('active');
            {#var target_html = $(this).find(".service_description hide").html();#}
            {#WebsiteController.open_popup_v2(target_html);#}
        }
    });

$(".individual_service_item a").each(function(){
    var link = $(this).attr('href');
    if (link.indexOf('pdf') > -1){
        $(this).addClass('pdf');
    }
});

$('.individual_service_item').each(function () {
    var a_href = $(this).find('a');
    if (a_href.length) {
        if (a_href.attr('href').indexOf('http') > -1 && a_href.attr('href').indexOf('.pdf') == -1) {
            $(this).addClass('external_link');
        }
    }
});

</script>
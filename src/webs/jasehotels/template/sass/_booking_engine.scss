#full_wrapper_booking {
  /*======== Booking Widget =======*/
  div#wrapper_booking {
    position: absolute;
    height: 70px;
    bottom: 100px;
    left: 0;
    right: 0;
    z-index: 1000;
    width: 1022px;
    padding: 10px;
    background: $corporate_2;
    &:lang(es) {
        width: 1052px;
    }
  }
  .booking_widget {
    position: absolute;
    left: 0;
  }

  .destination_wrapper {
    display: inline-block;
    width: 200px;
    border-right: 1px solid lightgrey;
    float: left;
    &:lang(es) {
        width: 207px;
    }
    .destination_field {
      margin: 7px 0 4px;
      input {
        width: calc(100% - 3px);
        padding-left: 10px;
        box-sizing: border-box;
        color: lighten($corporate_3, 10%);
        cursor: pointer;
      }
    }
  }

  .hotel_selector {
    display: none;
    position: absolute;
    background: white;
    box-shadow: 0 0 30px rgba(0,0,0,0.3);
    padding: 10px;
    width: 222px;
    box-sizing: border-box;
    top: 80px;
    .hotel_search_input_wrapper {
      position:relative;
      margin-bottom: 10px;
      .hotel_search_input {
        border: 1px solid #DDD;
        width: 100%;
        padding: 10px;
        box-sizing: border-box;
        position: relative;
        z-index: 2;
        background-color: transparent;
      }
      &:after {
        content: '\f002';
        font-family: "fontawesome", sans-serif;
        font-size: 20px;
        color: #DDD;
        @include center_y;
        right: 10px;
      }
    }

    ul {
      li {
        display: block;
        padding: 5px 10px;
        color: #444;
        cursor: pointer;
        font-size: 11px;
        text-transform: uppercase;
        @include transition(opacity, .4s);
        &.title_group {
          color: $corporate_1;
          font-weight: bold;
        }
        &.hotel_selector_option,
        &.hotel_selector_option_no_dispo {
          padding-left: 20px;
        }
        &:hover {
          opacity: .8;
        }
      }

    }
  }

  #full-booking-engine-html-5 {
    width: 1022px;
    &:lang(es) {
        width: 1052px;
    }
  }

  .departure_date_wrapper {
    border-right: 0;
  }

  #full-booking-engine-html-5 form.booking_form {
    background: white;
  }

  #full-booking-engine-html-5 .booking_form_title {
    background: $corporate-2;
    color: white;
    padding: 16px 30px;
    font-size: 18px;
    display: none;
  }

  .booking_form_title .best_price {
    display: none;
    color: white;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: white;
  }
  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background: transparent;
  }

  .room_list_wrapper .adults_selector, .room_list_wrapper .children_selector, .room_list_wrapper .babies_selector {
    float: left!important;
    height: 69px;
    border-width: 0;
    width: calc(100%/3)!important;

    .selectricWrapper {
      width: 100%!important;
    }
  }
  .room_list_wrapper .children_selector {
    float: none;
    padding-right: 0;
    padding-bottom: 4px;
    position: relative;

    .range-age {
      font-size: 8px;
      position: absolute;
      left: 10px;
      top: 19px;
    }
  }

  .room_list_wrapper .babies_selector {
    position: relative;
    padding: 7px 10px 5px;

    .range-age {
      font-size: 8px;
      position: absolute;
      left: 10px;
      top: 24px;
    }
  }

  .room_list_wrapper .babies_selector {
    display: inline-block;
    box-sizing: border-box;
    padding-left: 10px;
    label {
      font-family: 'Montserrat', sans-serif;
      text-transform: uppercase;
      font-size: 10px;
    }
  }

  button.submit_button {
    background: #6AB2D8 !important;
    color: white !important;
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
  }

  .wrapper-new-web-support.booking_form_title {
    background: gray !important;
  }

  #full-booking-engine-html-5 {
    //margin-top: 20px!important;
  }

  #data #full-booking-engine-html-5 {
    margin-top: 0 !important;
  }

  .date_box.entry_date {
    margin-top: 6px;
    background: none;
  }

  .date_box.departure_date {
    background: none;
    margin-top: 6px;
  }

  .selectricWrapper .selectric {
    margin-top: 0px;
  }

  #slider_inner_container #full-booking-engine-html-5 {
    margin-top: -17px !important;
  }

  .promocode_text {
    display: none;
  }

  .stay_selection {
    display: inline-block;
    vertical-align: top;
    float: left;

    .entry_date_wrapper, .departure_date_wrapper {
      display: inline-block;
      vertical-align: top;
      width: 130px;
      float: left;
      border-top: 1px solid lightgrey;
      padding: 8px 10px;
      .date_day {
        div {
          font-size: 27px;
          line-height: 20px;
          display: inline-block;
        }
      }
    }

    .nights_number_wrapper {
      display: inline-block;
      width: 95px;
      float: left;
      vertical-align: top;
      border-top: 1px solid lightgrey;
      padding: 8px 10px;
    }
  }

  .rooms_number_wrapper {
    float: left;
    display: inline-block;
    vertical-align: top;
    width: 75px;
    border-left: 1px solid lightgrey;
    &:lang(es) {
        width: 97px;
    }

    .selectricWrapper {
      width: 100%!important;
    }
  }

  .room_list_wrapper {
    width: 214px;
    display: inline-block;
    vertical-align: top;
    float: left;
    border-bottom: 1px solid lightgrey;
    height: 68px;

    .room_list {
      background: white;

      .room {
        height: 69px;

      }
    }
  }

  .wrapper_booking_button {
    display: inline-block;
    width: auto;
    float: right;
    border-bottom: 1px solid lightgrey;
    border-left: 1px solid lightgrey;
    height: 69px;

    .promocode_wrapper {
      display: inline-block;
      position: relative;
      width: 120px;
      vertical-align: top;
      float: left;
      height: 70px;
      .promocode_label {
        text-align: center;
        @include center_xy;
      }
      input.promocode_input {
        @include center_xy;
        font-size: 12px;
        text-align: center;
        background: transparent;
        &:focus {
          background: white;
        }
        &::-webkit-input-placeholder {
          color: transparent;
        }
        &::-moz-placeholder {
          color: transparent;
        }
        &:-ms-input-placeholder {
          color: transparent;
        }
        &:-moz-placeholder {
          color: transparent;
        }
      }
    }

    .submit_button {
      width: 151px;
      display: inline-block;
      vertical-align: top;
      float: left;
      height: 70px;
      border: 1px solid lightgrey;
      border-right: 0;

      &:hover {
        opacity: .8;
      }
    }
  }

  &.floating_booking {
    width: 100%;
    position: fixed;
    z-index: 1000;
    bottom: auto;
    top:190px;
    div#wrapper_booking {
      width: 100%;
      #full-booking-engine-html-5 {
        margin: auto !important;
      }
    }
  }
}

.fancybox-wrap {

  .fancybox-outer {
    padding: 0 !important;
  }

  .fancybox-inner {
    width: auto !important;
  }

  /*======== Booking Widget =======*/
  div#wrapper_booking {
    position: absolute;
    height: 420px;
    top: 145px;
    left: 0px;
    right: 0px;
    z-index: 35;
  }
  .booking_widget {
    position: absolute;
    left: 0px;
  }

  #full-booking-engine-html-5 {
    width: 299px;
  }

  #full-booking-engine-html-5 form.booking_form {
    background: white;
  }

  .booking_form_title .best_price {
    display: none;
    color: white;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: white;
  }
  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background: transparent;
  }
  .selectricWrapper {
    width: 62px !important;
  }

  .room_list_wrapper .adults_selector, .room_list_wrapper .children_selector, .room_list_wrapper .babies_selector {
    float: left;
    height: 69px;
  }
  .room_list_wrapper .children_selector {
    float: none;
    border-right: 1px solid lightgrey;
    padding-right: 0;
    padding-bottom: 4px;
  }
  .room_list_wrapper .babies_selector {
    display: inline-block;
    box-sizing: border-box;
    padding-left: 10px;
    label {
      font-family: 'Montserrat', sans-serif;
      text-transform: uppercase;
      font-size: 10px;
    }
  }
  .rooms_number_wrapper {
    width: 29.3%;
  }
  .room_list_wrapper {
    width: 70.6%;
  }

  button.submit_button {
    background: #FCD430 !important;
    //color: white !important;
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
  }

  .wrapper-new-web-support.booking_form_title {
    background: gray !important;
  }

  #full-booking-engine-html-5 {
    margin-top: 20px !important;
  }

  #data #full-booking-engine-html-5 {
    margin-top: 0 !important;
  }

  .date_box.entry_date {
    margin-top: 6px;
  }
  .selectricWrapper .selectric {
    margin-top: 0px;
  }

  #slider_inner_container #full-booking-engine-html-5 {
    margin-top: -17px !important;
  }

  .promocode_text {
    display: none;
  }
}

#data, #dataBono {
  .destination_wrapper {
    width: 100%;
    box-sizing: border-box;
    cursor: pointer;
    box-sizing: border-box;

    input {
      padding-left: 8px;
      box-sizing: border-box;
      color: #4b4b4b;
    }
  }
}

.fancybox-inner {
  overflow: visible !important;
}

.room_list {
  .room.room2, .room.room3 {
    border-bottom: 1px solid lightgrey;
  }
  .room.room3 {
    border-top: 0;
  }
}

.selectricItems {
  overflow: auto !important;
}

.no_dispo_wrapper {
  @include full_size;
  position: fixed;
  background-color: rgba(0,0,0,0.6);
  z-index: 1001;
  i.fa {
    position: absolute;
    top:20px;
    right: 20px;
    color: white;
    font-size: 50px;
  }
  .no_dispo {
    @include center_xy;
    width: 500px;
    background-color: white;
    text-align: center;
    padding: 40px 20px;
    color: #4B4B4B;
    box-shadow: 0 0 10px rgba(0,0,0,0.3);
    h3 {
      display: block !important;
      font-size: 40px;
      font-weight: 700;
      color: $corporate_1;
      margin-bottom: 20px;
    }
  }
}
.rooms_wrapper {
  position: relative;
  overflow: hidden;
  padding: 0 calc((100% - 1140px) / 2) 100px;
  .room_wrapper {
    position: relative;
    display: flex;
    align-items: start;
    width: 100%;
    padding-bottom: 100px;
    padding-left: 100px;
    .room_icons {
      position: absolute;
      left: 50px;
      top: 0;
      .icon {
        position: relative;
        margin-bottom: 10px;
        i {
          display: inline-block;
          vertical-align: middle;
          font-size: 24px;
          color: $corporate_2;
        }
        .tooltiptext {
          @include center_y;
          right: 50px;
          opacity: 0;
          display: inline-block;
          vertical-align: middle;
          text-align: center;
          font-size: 14px;
          font-weight: 300;
          color: $black;
          @include transition(opacity, .6s);
        }
        &:hover {
          .tooltiptext {
            opacity: 1;
          }
        }
      }
    }
    .room_summary {
      display: inline-block;
      vertical-align: top;
      width: 40%;
      padding: 8px;
      box-shadow: $box_shadow;
      .room_images {
        position: relative;
        display: block;
        width: 100%;
        height: 270px;
        overflow: hidden;
        a {
          display: block;
          width: 100%;
          height: 270px;
          position: relative;
          overflow: hidden;
        }
        img {
          @include center_image;
          min-height: unset;
        }
        .lupa {
          position: absolute;
          z-index: 10;
          top: 15px;
          left: 15px;
          i {
            font-size: 20px;
            color: white;
          }
        }
      }
      .room_summary_text {
        position: relative;
        z-index: 1;
        @include text_styles;
        padding-top: 10px;
        .summary {
          padding: 10px;
        }
        .description {
          display: none;
        }
        svg {
          @include center_xy;
          z-index: -1;
          width: 160px;
          * {
            fill: rgba($corporate_2, .3);
          }
        }
      }
    }
    .room_content {
      display: inline-block;
      vertical-align: top;
      width: 60%;
      padding: 0 180px 0 30px;
      .title {
        @include title_styles;
        font-size: 26px;
        line-height: 32px;
        padding: 10px 5px;
      }
      .room_text {
        @include text_styles;
        padding: 10px 5px;
        border-top: 1px solid $black;
        .summary {
          display: none;
        }
      }
      .button_promotion, .link {
        @include btn_styles;
        padding: 15px 70px;
        margin-top: 30px;
      }
      .button_promotion {
        text-transform: uppercase;
      }
    }
    &:nth-of-type(even) {
      flex-direction: row-reverse;
      padding-left: 0;
      padding-right: 100px;
      .room_icons {
        left: auto;
        right: 50px;
        .icon {
          .tooltiptext {
            right: auto;
            left: 50px;
          }
        }
      }
      .room_content {
        padding: 0 30px 0 180px;
      }
    }
    &:last-of-type {
      padding-bottom: 0;
    }
  }
}
<style>

.column-right {

float: right;

}

#contactContent .bordeInput {
border: 5px solid #C9E3FD;
width: 290px;
border-image: initial;
margin-left: 10px;
}

#contactContent .title {
float: left;
width: 130px;
text-align: left;
padding-right: 10px;
margin-top: 4px;
}

#contactContent .contInput {
margin-bottom: 10px;
display: block;
width:100%;
clear: both;
}

#contact .info .g-recaptcha {
    padding-bottom: 10px;
}


#contactContent .info {
margin-top: 30px;
padding-left: 10px;
}

#contact .error {
	/*display: block !important;*/
	color: red !important;
}

#google-plus {
	float: left;
	margin-left: 10px;
}

</style>

<div id="contactContent">


{% if not only_form %}


    {% if title %}
    <h1 id="title" style="font-size: 22px;
    font-weight: bold;margin-bottom:18px;color:black;">{{title|safe}}</h1>
    {% endif %}

    {% if pictures.0 and not picturesInSlider %}
        {% if minglePicture %}
        <div style="float:left; padding: 0px 15px 15px 0px;">
            <img src="{{pictures.0}}=s510" style="width:200px;"/>
        </div>
        {% else %}
            {% if pictures|length == 2 %}
                <div style="float:left;margin-bottom:10px;">
                    <img src="{{pictures.0}}=s510" style="border:5px solid #CCCCCC;width:240px;height:120px;"/>
                </div>
                <div style="float:right;margin-bottom:10px;">
                    <img src="{{pictures.1}}=s510" style="border:5px solid #CCCCCC;width:240px;height:120px;"/>
                </div>
            {% endif %}
            {% if pictures|length == 3 %}
                <div style="float:left;margin:0 auto;margin-bottom:20px;">
                    <img src="{{pictures.0}}=s510" style="border:5px solid #CCCCCC;width:155px;height:120px;"/>
                    <img src="{{pictures.1}}=s510" style="border:5px solid #CCCCCC;width:155px;height:120px;"/>
                    <img src="{{pictures.2}}=s510" style="border:5px solid #CCCCCC;width:155px;height:120px;"/>
                </div>
            {% endif %}
            {% if pictures|length == 1 %}
                <div style="width:100%;text-align:center; margin-bottom: 15px;">
                    <img src="{{pictures.0}}=s510" alt="{{title|safe}}" style="width:100%;max-height:270px;"/>
                </div>
            {% endif %}

        <div style="clear: both"></div>
        {% endif %}
    {% endif %}
    {{content|safe}}


    <div id="fb-root"></div>
    <script>(function(d, s, id) {
      var js, fjs = d.getElementsByTagName(s)[0];
      if (d.getElementById(id)) return;
      js = d.createElement(s); js.id = id;
      js.src = "//connect.facebook.net/{{ language_code }}/all.js#xfbml=1";
      fjs.parentNode.insertBefore(js, fjs);
    }(document, 'script', 'facebook-jssdk'));</script>
    <div class="fb-like" data-send="false" data-layout="standard" data-width="450" data-show-faces="false" data-colorscheme="light" data-action="like"></div>

{% endif %}




<form name="contact" id="contact" method="post" action="/utils/?action=contact">

<input type="hidden" name="action" id="action" value="contact"/>
<div class="info">

		<input type="hidden" name="section" id="section-name" value="{{ sectionName|safe}}"/>
        {% if destination_address %}
            <input type="hidden" name="destination_email" id="destination_email" value="{{ destination_address }}">
        {% endif %}

		<div class="contInput">
			<label for="name" class="title">{{T_nombre_y_apellidos}}</label>
			<input type="text" id="name" name="name" class="bordeInput" value=""/>
		</div>

		<div class="contInput">
			<label for="email" class="title">{{T_email}}</label>
			<input type="text" id="email" name="email" class="bordeInput" value=""/>
		</div>

		<div class="contInput">
			<label for="email" class="title">{{T_confirm_email}}</label>
			<input type="text" id="emailConfirmation" name="emailConfirmation" class="bordeInput" value=""/>
		</div>

		<div class="contInput">
			<label for="telephone" class="title">{{T_telefono}}</label>
			<input type="text" id="telephone" name="telephone" class="bordeInput" value=""/>
		</div>

		<div class="contInput">
			<label for="comments" class="title">{{T_comentarios}}</label>
			<textarea style="height:150px;" type="text" id="comments" name="comments" class="bordeInput" value=""></textarea>
		</div>

        {% if captcha_box %}
        <div class="contInput recaptcha_box_wrapper">
            <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
            <script src='https://www.google.com/recaptcha/api.js'></script>
        </div>
        {% endif %}

        {% if custom_checks %}
            {{ custom_checks.content|safe }}
        {% else %}
            {% if privacy_checkbox %}
                <input class="bordeInput check_privacy" id="privacy" name="privacy" type="checkbox" value="privacy"/><span class="title"><a href="/{{language}}/?sectionContent={% if custom_lopd_link %}{{ custom_lopd_link }}{% else %}politica-de-privacidad.html{% endif %}" class="myFancyPopup fancybox.iframe">{{T_lopd}}</a></span><br>
                <input class="bordeInput check_privacy" id="promotions" name="promotions" type="checkbox" value="privacy"/><label for="promotions">{{T_acepto_promociones}}</label>
            {% endif %}
            {% if data_protection %}
                <input class="check_privacy" id="data_protection" name="data_protection" type="checkbox" value="data_protection"/><span class="title"><a class="myFancyPopupNewsletter"  href="#data_protection_checkbox_contact_popup" rel="nofollow">{{ data_protection.subtitle|safe }}</a></span><br>
                <div style="display:none; padding: 20px; max-height: 350px" id="data_protection_checkbox_contact_popup">{{ data_protection.content|safe }}</div>
            {% endif %}
        {% endif %}

        {% if reservation_question %}
            <div class="has_reservation_wrapper">
                <input class="bordeInput has_reservation" id="has_reservation" name="has_reservation" type="checkbox" value=""/><span>{{ T_tienes_reserva }}</span>
            </div>
        {% endif %}

        {% if html_before_send %}
            <div class="html_before_send">{{ html_before_send|safe }}</div>
        {% endif %}

		<div style="float:right;padding-right:45px;" id="contact-button-wrapper">
			<a>
				<div id="contact-button" style="width:80px;color:#EFEFEF;border-top-left-radius: 5px;width:auto; border-top-right-radius: 5px; border-bottom-left-radius: 5px; border-bottom-right-radius: 5px;padding-top:8px;padding-bottom:8px;padding-left:10px; padding-right:10px;margin-bottom:20px; background: transparent url(/static_1/images/booking/fndMotor.png) repeat-x; cursor: pointer">
					{{T_enviar}}
				</div>
			</a>
		</div>

	<br clear="all">
</div>
</form>
</div>
<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript">

	$(document).ready(function(){

		jQuery.validator.addMethod("phone", function(phone_number, element) {
    		phone_number = phone_number.replace(/\s+/g, "");
			return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
				phone_number.match(/^[0-9 \+]\d+$/);
		}, "Please specify a valid phone number");


		$("#contact").validate({
			rules: {
				name: "required",
                {% if privacy_checkbox %}privacy: "required",{% endif %}
                {% if data_protection %}data_protection: "required",{% endif %}
				email: {
					required: true,
					email: true
				},
				emailConfirmation: {
					required: true,
					equalTo: "#email",
					email: true
				},
				telephone: {
					required: true,
					phone: true
				},
				comments: "required"
			},
			messages: {
				name: "{{ T_campo_obligatorio}}",
                {% if privacy_checkbox %}privacy: "{{ T_campo_obligatorio }}",{% endif %}
				email: {
					required: "{{ T_campo_obligatorio|safe }}",
					email: "{{ T_campo_valor_invalido|safe }}"
				},
				emailConfirmation: {
					required: "{{ T_campo_obligatorio|safe }}",
					email: "{{ T_campo_valor_invalido|safe }}",
					equalTo: "{{T_not_confirmed_email_warning|safe}}"
				},
				telephone: {
					required: "{{ T_campo_obligatorio|safe }}",
					phone: "{{ T_campo_valor_invalido|safe }}"
				},
				comments: "{{ T_campo_obligatorio|safe }}"
			}

		});

		$("#contact-button").click(function(){


			if ( $("#contact").valid() ) {
                if(!$("#g-recaptcha-response").length || $("#g-recaptcha-response").val()) {
                    $.post(
                            "/utils/?action=contact",
                            {
                                'name': $("#name").val(),
                                'telephone': $("#telephone").val(),
                                'email': $("#email").val(),
                                'comments': $("#comments").val(),
                                'section': $("#section-name").val(),
                                {% if captcha_box %}'g-recaptcha-response': $("#g-recaptcha-response").val(),{% endif %}
                                {% if reservation_question %}'has_resevation': $("#has_reservation").val(){% endif %}
                            },

                            function (data) {
                                alert("{{ T_gracias_contacto }}");
                                $("#name").val("");
                                $("#telephone").val("");
                                $("#email").val("");
                                $("#emailConfirmation").val("");
                                $("#comments").val("");
                            }
                    );
                }
			}
		})



		//check backgrounds inputs color
		$("form#contact input[type=text], form#contact textarea").each(function(){

            var bck = $(this).css('background');
            var bck_color = $(this).css('background-color');

            if (bck.indexOf("white") != -1 || bck_color.indexOf("white") != -1 || bck_color.indexOf("FFF") != -1 || bck.indexOf("FFF") != -1 || bck_color.indexOf("255, 255, 255") != -1 || bck.indexOf("255, 255, 255") != -1){
                $(this).css('color', 'black');

            }

        });


	});



</script>
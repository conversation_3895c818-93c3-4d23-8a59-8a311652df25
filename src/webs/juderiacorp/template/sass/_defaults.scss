//Base web (change too in template<PERSON>and<PERSON> and in config.rb)
$base_web: "judep";
// colors definitions
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);

// corporative colors definitions
$corporate1: #87121a;
$corporate2: #ab6623;
$corporate3: rgba(252, 241, 235, 0.42);
$corporate4: rgb(252, 241, 235);

// colors for booking widget
$booking_widget_color_1: $white;//body back ground & year input text color
$booking_widget_color_2: $corporate1;//header background & input texts
$booking_widget_color_3: gray;//label texts
$booking_widget_color_4: gray;//not used, but must be defined


.footer_column{
  min-height: 180px!important;
}
span.btn-corporate{
  margin-right: 0px!important;
}

.button-promotion.oferta-reserva{
  width: 80px;
margin-right: 10px!important;
}



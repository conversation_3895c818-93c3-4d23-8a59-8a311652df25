{% if title_4_mobile %}
    <div id="title_line" class="container12">
        <span><h2>{{ title_4_mobile|safe }}</h2></span>
    </div>
{% endif %}

{% for x, y in filtered_gallery_mobile.iteritems() %}
    <ul class="gallery_1">

        {% for z in y %}
        {% if loop.first %}<h2 class="section-title gallery_divided_title"><span>{{ z.hotel|safe }}</span></h2>{% endif %}
        <li class="{{ loop.index }}/{{ y|length }} {% if z.not_show %}notshow{% endif %}">
            <div class="crop{% if 'youtube' in z %} video_iframe{% endif %}">
                {% if 'youtube' in z or 'maps' in z %}
                <a href="{{z|safe}}" data-fancybox="{{ x|safe }}" data-caption="{{ z.hotel|safe }} - {{loop.index}}/{{ y.images|length }}">
                    <div class="overlay"></div>
                    <iframe id="iframevideo" width="100%" src="{{z|safe}}?rel=0&amp;controls=0&amp;showinfo=0" frameborder="0" allowfullscreen></iframe>
                </a>
                {% else %}
                    <a href="{{ z.servingUrl }}=s800" rel="gallery1" title="{{ x|safe}}" data-fancybox="{{ x|safe }}" data-caption="{{ x|safe }} - {{loop.index}}/{{ y.images|length }}">
                        <div class="overlay"></div>
                        <img src="{{ z.servingUrl }}=s350" title="{{ x|safe }}-{{loop.index}}" alt="{{ x }}-{{loop.index}}"/>
                    </a>
                {% endif %}
            </div>
        </li>
    {% endfor %}
    </ul>
{% endfor %}

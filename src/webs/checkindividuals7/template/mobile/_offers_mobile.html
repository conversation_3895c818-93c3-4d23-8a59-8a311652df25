<div class="offers_wrapper">
    {% for offer in ofertas %}
        {% if offer.hotel_name == "corpo" or offer.offer_visible %}
            <div class="offer">
                <div class="offer_pic">
                    <img src="{{ offer.pictures.0 }}" alt="" loading="lazy">
                    {% if offer.extra_text %}
                        <span>{{ offer.extra_text|safe }}</span>
                        <div class="black_overlay"></div>
                    {% endif %}
                </div>
                <div class="offer_info{% if 'check' in offer.description %} check{% endif %}">
                    <h3>{{ offer.name|safe }}</h3>
                    <div class="offer_description">{{ offer.description|safe }}</div>
                    <div class="offer_link">
                        {% if offer.linkUrl %}<a href="{{ offer.linkUrl|safe }}" class="btn_more"><span>{{ T_ver_mas }}</span></a>{% endif %}
                        <a href="{% if offer.hotel_namespace and offer.hotel_namespace == "checkin-masia" %}mailto:<EMAIL>{% else %}#data{% endif %}" class="button-promotion" namespace="{{ offer.hotel_namespace }}"><span>{{ T_reservar }}</span></a>
                    </div>
                </div>
            </div>
        {% endif %}
    {% endfor %}
</div>
{% if default_advantage_banner %}
    <div class="banner_ventajas_wrapper default_advantage_banners">
        <div class="container12">
            {% if default_advantage_banner.section.subtitle %}<h3>{{ default_advantage_banner.section.subtitle|safe }}</h3>{% endif %}
            {% if default_advantage_banner.pictures.images %}
            <div class="banner_ventajas">
                {% for banner in default_advantage_banner.pictures.images %}
                    <div class="banner">
                        {% if banner.title %}<i class="fa {{ banner.title|safe }}"></i><span>{{ banner.description|safe }}</span>{% endif %}
                        {% if banner.linkUrl %}
                            <a href="{{ banner.linkUrl|safe }}" {% if 'http' in banner.linkUrl %}target="_blank"{% endif %}>
                                {% if banner.altText %}{{ banner.altText|safe }}{% else %}{{ T_ver_mas }}{% endif %}</a>
                        {% endif %}
                        {% if banner.description and 'hide' in banner.description and banner.altText and not banner.linkUrl %}<div class="alt_text" onclick="">{{ banner.altText|safe }}</div>{% endif %}
                    </div>
                {% endfor %}
            </div>
            {% endif %}
            {% if default_advantage_banner.pictures.icos %}
            <div class="banner_ventajas_icos">
                {% for banner in default_advantage_banner.pictures.icos %}
                    <div class="banner">
                        {% if banner.title %}
                            <i class="fa {{ banner.title|safe }}"></i>
                            <span>{{ banner.description|safe }}</span>
                            {% if 'hide' in banner.description and banner.altText %}<div  class="alt_text">{{ banner.altText|safe }}</div>{% endif %}
                        {% endif %}
                    </div>
                {% endfor %}
            </div>
            {% endif %}
        </div>
    </div>
{% endif %}


<div class="banner_ventajas_wrapper" {% if banner_ventajas_section.background %}style="background: {{ banner_ventajas_section.background|safe }}"{% endif %}>
    <div class="container12">
        {% if banner_ventajas_section.subtitle %}<h3>{{ banner_ventajas_section.subtitle|safe }}</h3>{% endif %}
        {% if banner_ventajas %}
        <div class="banner_ventajas {{ 'owl-carousel' if banner_ventajas|length > 5 }}">
            {% for banner in banner_ventajas %}
                <div class="banner">
                    {% if banner.title %}<i class="fa {{ banner.title|safe }}"></i><span>{{ banner.description|safe }}</span>{% endif %}
                    {% if banner.linkUrl %}
                        <a href="{{ banner.linkUrl|safe }}" {% if 'http' in banner.linkUrl %}target="_blank"{% endif %}>
                            {% if banner.altText %}{{ banner.altText|safe }}{% else %}{{ T_ver_mas }}{% endif %}</a>
                    {% endif %}
                    {% if banner.description and 'hide' in banner.description and banner.altText and not banner.linkUrl %}<div class="alt_text" onclick="">{{ banner.altText|safe }}</div>{% endif %}
                </div>
            {% endfor %}
        </div>
        {% endif %}
        {% if banner_ventajas_icos %}
        <div class="banner_ventajas_icos">
            {% for banner in banner_ventajas_icos %}
                <div class="banner">
                    {% if banner.title %}
                        <i class="fa {{ banner.title|safe }}"></i>
                        <span>{{ banner.description|safe }}</span>
                        {% if 'hide' in banner.description and banner.altText %}<div  class="alt_text">{{ banner.altText|safe }}</div>{% endif %}
                    {% endif %}
                </div>
            {% endfor %}
        </div>
        {% endif %}
    </div>
</div>
<script>
    $(function () {
        $('.banner_ventajas_wrapper .banner_ventajas.owl-carousel').owlCarousel({
            loop: false,
            nav: false,
            dots: false,
            items: 5,
            margin: 10
        })
    });
</script>
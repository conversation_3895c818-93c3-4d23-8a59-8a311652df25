    <h2 class="section-title">{{ title.sectionName }}</h2>


    {% for hotel in hotel %}
        <div class="hotel">
           <img class="hotel-image" src="{{ hotel.pictures.0.servingUrl|safe }}">

            <h3 class="hotel-title">{{ hotel.title|safe }}</h3>

            <div class="hotel-description">{{ hotel.description|safe }}</div>

            <div class="buttons-wrapper" id="{{ forloop.counter }}">
                <a {% if more_pictures %}style="margin-left: -14px;"{% endif %} class="cboxelement button-promotion"
                   id="button2_rooms" href="/">
                    {{ T_reservar }}
                </a>

                {% if more_pictures %}
                    <span class="cboxelement button-promotion showpictures" id="button2_rooms">
                    {{ T_ver_mas }}
                </span>
                {% endif %}

            </div>

            <div class="hidden-pictures" id="hide-{{ forloop.counter }}">
                {% for fotos in hotel.pictures %}
                    <img class="hotel-image" src="{{ fotos.servingUrl }}">
                {% endfor %}
            </div>


        </div>
    {% endfor %}

    <script>
        $('.showpictures').click(function (event) {
            var num = $(this).parent().attr('id');
            var status = $('#hide-' + num).css('display');
            if (status == 'none') {
                console.log("Muestra #hide-" + num);
                $('#hide-' + num).slideDown('slow');
            } else {
                console.log("Oculta #hide-" + num);
                $('#hide-' + num).slideUp('slow');
            }
        });
    </script>

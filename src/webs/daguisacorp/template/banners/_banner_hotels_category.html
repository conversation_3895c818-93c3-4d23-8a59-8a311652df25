<div class="banner_hotels_category_wrapper">
    {% for banner in banner_hotels_category %}
        <div class="banner {% if banner.description and banner.description|length > 500 %}experience_banner{% endif %}">
            <div class="banner_content">
                <div class="banner_text {% if banner_hotels_category_filter %}banner_hotels_category_filter{% endif %}">
                    {% if banner.altText %}
                        <i class="{{ banner.icon|safe }}"></i>
                    {% endif %}
                    {% if banner.title %}<h2 class="title">{{ banner.title|safe }}</h2>{% endif %}
                    {% if banner.description %}<div class="desc {% if banner.description|length > 500 %}experience_desc{% endif %}">{{ banner.description|safe }}</div>{% endif %}
                </div>
            </div>
            {% if hotels and banner.filter %}
                <div class="banner_slider owl-carousel">
                    {% for hotel in hotels %}
                        {% if hotel.cat|lower == banner.filter|lower %}
                            <div class="hotel">
                                <div class="hotel_pic">
                                    <img src="{{ hotel.main.0.servingUrl }}=s1900" alt="{{ hotel.subtitle|safe }}">
                                </div>
                                <div class="center_y">
                                    <img src="{{ hotel.logo.0.servingUrl }}" alt="{{ hotel.subtitle|safe }}">
                                    {% if hotel.site %}
                                        <a href="{{ hotel.site }}" class="link" {% if "http" in hotel.site %}target="_blank" {% endif %}><i class="fal fa-external-link"></i><span>{{ T_ir_a_web }}</span></a>
                                    {% endif %}
                                    <a href="#map_{{ hotel.id }}" class="see_map link"><i class="fal fa-map-marker-alt"></i><span>{{ T_ver_en_mapa }}</span></a>
                                    {% if not is_mobile %}
                                    <div class="button">
                                        <a href="{{ hotel.friendlyUrlInternational }}"><span>{{ T_ver_hotel }}</span></a>
                                        <a href="#data" class="{% if is_mobile %}button-promotion{% else %}button_promotion{% endif %}" data-namespace="{{ hotel.id }}"><span>{{ T_reservar }}</span></a>
                                    </div>
                                    {% endif %}
                                </div>
                                {% if is_mobile %}
                                    <div class="button">
                                        <a href="{{ hotel.friendlyUrlInternational }}"><span>{{ T_ver_hotel }}</span></a>
                                        <a href="#data" class="{% if is_mobile %}button-promotion{% else %}button_promotion{% endif %}" data-namespace="{{ hotel.id }}"><span>{{ T_reservar }}</span></a>
                                    </div>
                                {% endif %}
                                <div class="hotel_map" id="map_{{ hotel.id|safe }}"></div>
                            </div>
                        {% endif %}
                    {% endfor %}
                </div>
                <div class="hotels_row">
                    {% set count = 1 %}
                    {% for hotel in hotels %}
                        {% if hotel.cat|lower == banner.filter|lower %}
                            <a href="#" class="hotel" data-counter="{{ count }}">
                                <span>{{ hotel.subtitle|safe }}</span>
                            </a>
                            {% set count = count + 1 %}
                        {% endif %}
                    {% endfor %}
                </div>
            {% else %}
                <div class="banner_slider owl-carousel">
                    {% if banner.servingUrl %}
                        <div class="hotel">
                            <div class="hotel_pic">
                                <img src="{{ banner.servingUrl|safe }}=s1900" {% if banner.altText %}alt="{{ banner.altText|safe }}"{% endif %}>
                            </div>
                            {% if banner.linkUrl %}
                                <div class="center_y banner_button">
                                    <a href="{{ banner.linkUrl }}" class="link banner_button" {% if "http" in banner.linkUrl %}target="_blank" {% endif %}>
                                        <i class="fal fa-plus"></i>
                                        <span>
                                            {% if banner.btn_text %}{{ banner.btn_text }}{% else %}{{ T_ver_mas }}{% endif %}
                                        </span>
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    {% endif %}
                    {% for img in banner.images %}
                        <div class="hotel">
                            <div class="hotel_pic">
                                <img src="{{ img.servingUrl|safe }}=s1900" {% if img.altText %}alt="{{ img.altText|safe }}"{% endif %}>
                            </div>
                            {% if banner.linkUrl %}
                                <div class="center_y banner_button">
                                    <a href="{{ banner.linkUrl }}" class="link banner_button" {% if "http" in banner.linkUrl %}target="_blank" {% endif %}>
                                        <i class="fal fa-plus"></i>
                                        <span>
                                            {% if banner.btn_text %}{{ banner.btn_text }}{% else %}{{ T_ver_mas }}{% endif %}
                                        </span>
                                    </a>
                                </div>
                            {% endif %}
                        </div>
                    {% endfor %}


                </div>
            {% endif %}
        </div>
    {% endfor %}
</div>
<script>
    $(window).load(function () {
       $(".banner_slider").owlCarousel({
            loop: false,
            nav: true,
            dots: false,
            items: 1,
            navText: ['<i class="fal fa-arrow-left"></i>', '<i class="fal fa-arrow-right"></i>'],
            autoplay: true
        });
       $(".banner_hotels_category_wrapper .banner .hotels_row a").click(function (e) {
           e.preventDefault();
           var count = $(this).attr("data-counter");
           $(this).parent().parent().find(".banner_slider").trigger('to.owl.carousel', parseInt(count - 1));
       });
       $(".banner_slider").on('changed.owl.carousel', function(event) {
           var count = event.item.index + 1;
            event.relatedTarget.$element.parent().find(".hotels_row a").removeClass("active");
            event.relatedTarget.$element.parent().find(".hotels_row a[data-counter="+count+"]").addClass("active");
       });
    })
</script>
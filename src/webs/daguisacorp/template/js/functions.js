$(window).load(function () {

    $(".myFancyPopup").fancybox({
                                    maxWidth: 800,
                                    maxHeight: 600,
                                    fitToView: false,
                                    width: '70%',
                                    height: '70%',
                                    autoSize: false,
                                    aspectRatio: false,
                                    closeClick: false,
                                    openEffect: 'none',
                                    closeEffect: 'none'
                                });


    $(".myFancyPopupAuto").fancybox({
                                        width: 650,
                                        height: 'auto',
                                        fitToView: false,
                                        autoSize: false
                                    });
    $(".myFancyPopupVideo").fancybox({
                                         width: 640,
                                         height: 'auto',
                                         fitToView: false,
                                         autoSize: false
                                     });

    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');

    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }

    if (typeof(TAPixel) !== "undefined") {
        TAPixel.impressionWithReferer("001F000000vA4u0");
    }

    $("img[lazy=true]").unveil();

    effects_sass();

    $(".button-promotion, .button_promotion").unbind('click');
    only_execute_once = false;
    $("body").on("click",".button-promotion, .button_promotion",function (e) {
        e.preventDefault();
        prepare_booking_popup();
        open_booking_full();
        var namespace_attr = $(this).attr('data-namespace') ? $(this).attr('data-namespace') : $(this).attr('data-smarthotel'),
            data_package_orders =  $(this).attr('data-smartpackageorder');
        if (namespace_attr){
            $("#data .hotel_selector_option#" + namespace_attr).trigger('click');
        }
        var hotels_in = $(this).attr("data-hotel-in");
        if (hotels_in){
            var hotels_in_array = hotels_in.split(";");
            $(".hotel_selector_option").hide();
            hotels_in_array.forEach(function (namespace, idx) {
                $(".hotel_selector_option#" + namespace).show();
            });
        }
        if (!only_execute_once) {
            only_execute_once = true;
            $("#data select.selector_ninos").selectric("refresh");

        }
        $(".modal").fadeOut();
        $(".modal .modal_content").html("");

        min_stay_number = 1;
        var packages_orders_input = $("input[name='packages_order']");
        if (!packages_orders_input.length) {
            $(".paraty-booking-form").each(function(){
                $(this).append($("<input type='hidden' name='packages_order' value=''></input>"));
            });
            packages_orders_input = $("input[name='packages_order']");
        }
        if (data_package_orders) {
            packages_orders_input.val(data_package_orders);
        } else {
            packages_orders_input.val("");
        }
        check_smart_datas($(this));
    });

    $(".destination_wrapper .destination_field").unbind("click");
    $(".destination_wrapper .destination_field, .hotel_selector .close").click(function(){
        show_hotel_selector();
    });

    $(".scroll_action").click(function () {
        var scroll_to = $("#slider_container").height() + 25;
        $('html, body').animate({
            scrollTop: scroll_to
        }, 500);
    });

    $(".see_map").click(function (e) {
        e.preventDefault();
        var map_id = $(this).attr("href");
        $(".modal .modal_content").html($(".banner_map").clone());
        $(".modal").fadeIn();
        $(".modal .modal_content .banner_map .hotel").hide();
        $(".modal .modal_content .banner_map .hotel"+map_id).show();
    });

    $(".scroll_map").click(function () {
        var scroll_to = $(".banner_map_wrapper").position().top;
        $('html, body').animate({
            scrollTop: scroll_to
        }, 600);
    });

    $(".modal_close").click(function () {
        $(".modal").fadeOut();
        $(".modal .modal_content").html("");
    });

    $(".destiny_selector_inner .destiny a").click(function(e){
        e.preventDefault();
        $(this).parent().toggleClass("active");
    });

    $(".hotel_selector_filter .destiny_filter").click(function(e){
        e.preventDefault();
        var to_show = $(this).attr("data-destiny");
        $(".hotel_selector_filter .destiny_filter").removeClass("selected");
        $(".destiny_selector_inner, .hotel_selector_inner, .map_selector_inner").removeClass("active");
        $(this).addClass("selected");
        $("."+to_show).addClass("active");
    });

    $("#lang .language_selected").click(function () {
        $(".language_selector").slideToggle();
    });

    $(".hotel_selector_search input").keydown(function () {
         setTimeout(function(){
             searchHotelElement();
         }, 100);
    }).focus(function () {
        $(".hotel_selector_filter .destiny_filter").removeClass("selected");
        $(".destiny_selector_inner, .map_selector_inner").removeClass("active");
        $(".hotel_selector_filter .destiny_filter[data-destiny=hotel_selector_inner]").addClass("selected");
        $(".hotel_selector_inner").addClass("active");
    }).blur(function () {
        $(this).val("");
        setTimeout(function(){
             searchHotelElement();
         }, 100);
    });

    $(".toggle_menu").click(function (e) {
        e.preventDefault();
        $(this).toggleClass("open");
        $("nav#main_menu").toggleClass("open");
        setTimeout(function () {
            $("#full_wrapper_booking").toggleClass("lower");
        }, 200);
    });

    $('.select2').select2();

    $(".hotel_selector_option").click(function () {
        $("#full_wrapper_booking .destination_wrapper").removeClass("unselected");
        var kids_range = $(this).data("kidsrange");
        if(kids_range) {
            var rangek = kids_range.split(":"),
                min_age = parseInt(rangek[0]),
                max_age = parseInt(rangek[1]);
            $("#dialog-form").each(function () {
                $(this).find("select option").remove();
                for (let i = min_age; i <= max_age; i++) {
                    $(this).find("select").append("<option value='"+i+"'>"+i+"</option>");
                }
            });
        } else {
            var min_age = 2,
                max_age = 12;
            $("#dialog-form").each(function () {
                $(this).find("select option").remove();
                for (let i = min_age; i <= max_age; i++) {
                    $(this).find("select").append("<option value='"+i+"'>"+i+"</option>");
                }
            });
        }
        setTimeout(function(){
            $(".paraty-booking-form").each(function(){
                var submit_button = $(this).find(".submit_button");
                if (!submit_button.is(':visible') || submit_button.attr('disabled')){
                    submit_button.trigger('click');
                }
            })
        }, 1000)
    });

    showMenu();
    slide_popup();
    if ($(".wrapper_gift_bono").length) {
        if ($(".select2.select2-container:not(.select2-hidden-accessible)").length) {
            $(".select2.select2-container:not(.select2-hidden-accessible)").remove();
            $('.my-select-2').select2();
        }
    }

});

$(window).scroll(showMenu);

function showMenu() {
    actual_position = $(window).scrollTop();
    slider_height = $("#slider_container").height();
    menu_showed = $("#full_wrapper_booking").hasClass('showed');

    if ((actual_position > slider_height) && (!menu_showed)) {
        $("#full_wrapper_booking").addClass('floating_booking').addClass('showed');
    }

    if ((actual_position < slider_height) && (menu_showed)) {
        $("#full_wrapper_booking").removeClass("floating_booking").removeClass('showed');
    }
}

function searchHotelElement(){
    var searched_hotel = $(".hotel_selector_search input").val();

    $(".hotel_selector_inner li").each(function(){
        var actual_html = $(this).find(".title_selector").html();
        actual_html = actual_html.toLowerCase();
        searched_hotel = searched_hotel.toLowerCase();

        if(actual_html.indexOf(searched_hotel) < 0){
            $(this).fadeOut();
        }else{
            $(this).fadeIn();
        }
        if(searched_hotel == ""){
            $(this).fadeIn();
        }
    })
}

function booking_fancybox_closed() {
    $(".hotel_selector_option").show();
}

function show_hotel_selector() {
    $(".hotel_selector").slideToggle("slow");

    var floating_widget = $("#full_wrapper_booking").hasClass("showed"),
        actual_position = $(window).scrollTop();

    if (actual_position > 10 && !floating_widget) {
        $('html, body').animate({
            scrollTop: 0
        }, 400);
    }

    if ($(".hotel-selector-inner-step1")) {
        $(".hotel-selector-inner-step1").css("display", "block");
        $(".hotel-selector-inner-step2").css("display", "none");
    }
}

function slide_popup(){
    $('.btn_info_popup').click(function(e){
        e.preventDefault();
        var lang1 = $('#lang1popup').val()
        var lang2 = $('#lang2popup').val()
        $('.btn_info_popup, .fancybox-wrap.fancybox-desktop.fancybox-type-inline.popup-start.fancybox-opened').toggleClass('active');
        $('.popup_content_v2 .popup_description_v2 .content').toggleClass('active');
        $('.popup_inicio_v2, .popup_img_wrapper').toggleClass('active');
        if($('.btn_info_popup').hasClass('active')){
            $('.btn_info_popup').text(lang2);
        }else{
            $('.btn_info_popup').text(lang1);
        }
    });
}
$.simpleWeather({
        location: 'Andorra',
        woeid: '',
        unit: 'c',
        days: '5',
        success: function (weather) {
            var d = new Date();
            var language = $("html").attr("lang");
            for (i = 0; i <= weather.length - 1; i++) {
            var weather_day = $.datepicker.regional[language].dayNamesShort[d.getDay()],
                weather_icon = weather[i].code,
                weather_img = '/static_1/images/weather/1/' + weather_icon + '.png',
                weather_high = Math.round(weather[i].high),
                weather_low = Math.round(weather[i].low),
                weather_element = $("#weather_wrapper .weather_" + i);

                if (weather_day == "Juv") {
                    weather_day = "Jue";
                }

                d.setDate(d.getDate() + 1);

                weather_element.find(".weather_day").html(weather_day);
                weather_element.find(".weather_icon img").attr('src', weather_img);
                weather_element.find(".weather_high").html(weather_high + "º");
                weather_element.find(".weather_low").html(weather_low  + "º");
            }
        },
        error: function (error) {
            $("#weather").html('<p>' + error + '</p>');
        }
    });

$("#social-right .sun").click(function (e) {
    e.preventDefault();
    $("#weather_wrapper").slideToggle();
});


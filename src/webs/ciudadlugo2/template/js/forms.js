$(document).ready(function () {
    if (typeof $.i18n == 'object') {
        if (typeof messages == 'object') {
            $.i18n.load(messages);
        }
    }
    $("#opinions-form-inner").validate({
        rules: {
            name: "required",
            email: "required",
            opinion: "required"
        }
        , messages: {
            name: $.i18n._("campo_obligatorio"),
            email: $.i18n._("campo_obligatorio"),
            opinion: $.i18n._("campo_obligatorio")
        }
        , highlight: function (element) {
            $(element).addClass('input-error');
        }, unhighlight: function (element) {
            $(element).removeClass('input-error');
        }
    });
    $("#opinions-button").click(function () {
        if ($("#opinions-form-inner").valid()) {
            $.post(
                "/utils/?action=contact",
                {
                    'name': $("#name").val(),
                    'email': $("#email").val(),
                    'comments': $("#opinion").val(),
                    'destination_email': '<EMAIL>',
                    'emailSubject': 'Opiniones'
                },
                function (data) {
                    alert($.i18n._("gracias_contacto"));
                    $("#name").val("");
                    $("#opinion").val("");
                    $("#email").val("");
                }
            );
        }
    });
});
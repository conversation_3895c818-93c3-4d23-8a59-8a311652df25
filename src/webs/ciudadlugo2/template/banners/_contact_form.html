<div class="form_contact_wrapper">
    <form name="contact" id="contact" method="post" action="/utils/?action=contact">

        <input type="hidden" name="action" id="action" value="contact"/>

        <div class="info">

            <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>

            <div class="contInput">
                <label for="name" class="title">{{ T_nombre_y_apellidos }}</label>
                <input type="text" id="name" name="name" class="bordeInput" value=""/>
            </div>

            <div class="contInput">
                <label for="email" class="title">{{ T_email }}</label>
                <input type="text" id="email" name="email" class="bordeInput" value=""/>
            </div>

            <div class="contInput">
                <label for="email" class="title">{{ T_confirm_email }}</label>
                <input type="text" id="emailConfirmation" name="emailConfirmation" class="bordeInput" value=""/>
            </div>

            <div class="contInput">
                <label for="telephone" class="title">{{ T_telefono }}</label>
                <input type="text" id="telephone" name="telephone" class="bordeInput" value=""/>
            </div>

            <div class="contInput">
                <label for="comments" class="title">{{ T_comentarios }}</label>
                <textarea style="height:150px;" type="text" id="comments" name="comments" class="bordeInput"
                          value=""></textarea>
            </div>

            <div class="contInput policy-terms">
                <input type="checkbox" id="accept-term" name="accept_term"/>

                {% if custom_checkbox %}
                    <a class="myFancyPopup" href="#custom_checkbox_contact_popup"
                       rel="nofollow">{{ T_acepto }} {{ custom_checkbox.title|safe }}</a>
                    <div style="display:none"
                         id="custom_checkbox_contact_popup">{{ custom_checkbox.content|safe }}</div>

                {% else %}

                    <a class="myFancyPopup fancybox.iframe"
                       href="{{ language }}/?sectionContent=politica-de-privacidad.html"
                       rel="nofollow">{{ T_acepto_politica }}</a>
                {% endif %}

            </div>

            {% if recaptcha_publickey %}
                <div class="contInput captcha">
                    <div class="g-recaptcha" data-sitekey="{{ recaptcha_publickey }}"></div>
                </div>
            {% endif %}

            <button id="contact-button" onClick="return false;">{{ T_enviar }}</button>

            <br clear="all">
        </div>
    </form>
</div>

<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script src='https://www.google.com/recaptcha/api.js?hl={{ language_code }}'></script>
<script type="text/javascript">

    $(document).ready(function () {

        jQuery.validator.addMethod("phone", function (phone_number, element) {
            phone_number = phone_number.replace(/\s+/g, "");
            return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                    phone_number.match(/^[0-9 \+]\d+$/);
        }, "Please specify a valid phone number");


        $("#contact").validate({
            rules: {
                name: "required",
                email: {
                    required: true,
                    email: true
                },
                emailConfirmation: {
                    required: true,
                    equalTo: "#email",
                    email: true
                },
                telephone: {
                    required: true,
                    phone: true
                },
                comments: "required",
                accept_term: "required"
            },
            messages: {
                name: "{{ T_campo_obligatorio}}",
                email: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    email: "{{ T_campo_valor_invalido|safe }}"
                },
                emailConfirmation: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    email: "{{ T_campo_valor_invalido|safe }}",
                    equalTo: "{{T_not_confirmed_email_warning|safe}}"
                },
                telephone: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    phone: "{{ T_campo_valor_invalido|safe }}"
                },
                comments: "{{ T_campo_obligatorio|safe }}",
                accept_term: "{{ T_campo_obligatorio|safe }}"
            }
        });


        $("#contact-button").click(function () {
            let is_valid_form = $("#contact").valid();
            {% if recaptcha_publickey %}
                is_valid_form = is_valid_form && $("#g-recaptcha-response").val();
            {% endif %}

            if (is_valid_form) {
                $.post(
                    "/utils/?action=contact",
                    {
                        'name': $("#name").val(),
                        'telephone': $("#telephone").val(),
                        'email': $("#email").val(),
                        'comments': $("#comments").val(),
                        'section': $("#section-name").val()
                        {% if recaptcha_publickey %}
                            ,'g-recaptcha-response': $("#g-recaptcha-response").val()
                        {% endif %}
                    },

                    function (data) {
                        alert("{{ T_gracias_contacto }}");
                        $("#name").val("");
                        $("#telephone").val("");
                        $("#email").val("");
                        $("#emailConfirmation").val("");
                        $("#comments").val("");
                    }
                );
            }
        });
    });
</script>
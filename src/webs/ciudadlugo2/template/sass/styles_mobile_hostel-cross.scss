@import "styles_mobile";

.main_menu {
    ul {
        li {
            a, span {
                font-weight: 300;
            }
        }
    }
}
.container_popup_booking {
    img {
        background-color: $corporate_1;
    }
}

.cycle_banners_wrapper .cycle_element .cycle_image_wrapper .cycle_see_wrapper a{
  width: 60%;
}

span.ui-icon.ui-icon-circle-triangle-e, body span.ui-icon.ui-icon-circle-triangle-w{
  display: none;
}
a.ui-datepicker-prev.ui-corner-all:before {
  content: '\f104';
  left: 50%;
}

a.ui-datepicker-next.ui-corner-all:before {
  content: '\f105';
  right: 50%;
}
a.ui-datepicker-prev.ui-corner-all:before, a.ui-datepicker-next.ui-corner-all:before {
  font-family: 'FontAwesome';
  font-size: 20px;
  color: black;
  position: absolute;
}
.rooms_wrapper .room_block .buttons a{
  border-radius: 5px !important;
  &.room_link{
    display: none;
  }
}

.default_reservation_text, .contact_content_element {
  a{
    color: $corporate_1;
  }
}

.location_content .section-subtitle .only_desktop{
  display: none;
}


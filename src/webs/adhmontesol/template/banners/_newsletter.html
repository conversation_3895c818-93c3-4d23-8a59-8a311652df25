<div class="container12_fluid">
    <div class="component_wrapper newsletter_wrapper">
        <div class="title_wrapper">
            <div class="subtitle">{{ newsletter_banner.subtitle|safe }}</div>
            <div class="title">{{ newsletter_banner.content|safe }}</div>
        </div>
        <form action="#" class="newsletter_form">
            <div class="input_wrapper">
                <input type="text" name="suscEmail" id="suscEmail" class="input_email" placeholder="


                        {% if newsletter_banner.placeholder_text %}{{ newsletter_banner.placeholder_text|safe }}{% else %}{{ T_introduce_email_placeholder|safe }}{% endif %}"
                       required>
                <button type="submit" class="btn_submit button_newsletter">
                    {% if newsletter_banner.button_text %}{{ newsletter_banner.button_text|safe }}{% else %}
                        {{ T_enviar|safe }}{% endif %}</button>
            </div>
            <div class="policy_wrapper">
                <div class="checkbox_wrapper">
                    <input type="checkbox" name="privacy" id="privacy" class="input_checkbox" required>
                    <a data-fancybox class="desc"
                       data-options='{"caption" : "{{ T_lopd }}", "src" : "/{{ language }}/?sectionContent=politica-de-privacidad.html", "type" : "iframe", "width" : "100%", "max-width" : "100%"}'
                       data-width="1200" class="myFancyPopup fancybox.iframe newsletter_popup"
                       href="{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                </div>
                <div class="checkbox_wrapper">
                    <input type="checkbox" name="ads" id="ads" class="input_checkbox" required>
                    <a class="desc">
                        {{ T_acepto_promociones }}
                    </a>
                </div>
            </div>
        </form>
        <div class="gallery_wrapper">
            <div class="social">
                {% if social.facebook_id %}
                    <a class="icon" href="https://www.facebook.com/{{ social.facebook_id }}" target="_blank"
                       rel="nofollow">
                        <i class="{% if fontawesome5 %}fab{% else %}fa{% endif %} fa-facebook" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.flickr_id %}
                    <a class="icon" href="https://www.flickr.com/photos/{{ social.flickr_id }}" target="_blank"
                       rel="nofollow">
                        <i class="{% if fontawesome5 %}fab{% else %}fa{% endif %} fa-flickr" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.twitter_id %}
                    <a class="icon" href="https://twitter.com/#!/{{ social.twitter_id }}" target="_blank"
                       rel="nofollow">
                        <i class="{% if fontawesome5 %}fab{% else %}fa{% endif %} fa-twitter" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.google_plus_id %}
                    <a class="icon" href="https://plus.google.com/u/0/{{ social.google_plus_id }}" target="_blank"
                       rel="publisher nofollow">
                        <i class="{% if fontawesome5 %}fab{% else %}fa{% endif %} fa-google-plus"
                           aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.youtube_id %}
                    <a class="icon" href="https://www.youtube.com/{{ social.youtube_id }}" target="_blank"
                       rel="nofollow">
                        <i class="{% if fontawesome5 %}fab{% else %}fa{% endif %} fa-youtube" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.pinterest_id %}
                    <a class="icon" href="http://es.pinterest.com/{{ social.pinterest_id }}" target="_blank"
                       rel="nofollow">
                        <i class="{% if fontawesome5 %}fab{% else %}fa{% endif %} fa-pinterest-p"
                           aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.instagram_id %}
                    <a class="icon" href="http://www.instagram.com/{{ social.instagram_id }}" target="_blank"
                       rel="nofollow">
                        <i class="{% if fontawesome5 %}fab{% else %}fa{% endif %} fa-instagram" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.line_id %}
                    <a class="icon" href="https://line.me/R/ti/p/{{ social.line_id }}" target="_blank" rel="nofollow">
                        <i class="{% if fontawesome5 %}fab{% else %}fa{% endif %} fa-line" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.blog_id %}
                    <a class="icon" href="{{ social.blog_id|safe }}" target="_blank" class="blog_link" rel="nofollow">BLOG</a>
                {% endif %}
                {% if social.linkedin_id %}
                    <a class="icon" href="https://es.linkedin.com/{{ social.linkedin_id }}" target="_blank"
                       rel="nofollow">
                        <i class="{% if fontawesome5 %}fab{% else %}fa{% endif %} fa-linkedin" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if social.tripadvisor_id %}
                    <a class="icon" href="https://www.tripadvisor.es/{{ social.tripadvisor_id }}" target="_blank"
                       rel="nofollow">
                        <i class="{% if fontawesome5 %}fab{% else %}fa{% endif %} fa-tripadvisor"
                           aria-hidden="true"></i>
                    </a>
                {% endif %}
            </div>
            <div class="newsletter_gallery instagram_wrapper gallery owl-carousel owl-theme" data-instagram-id="montesolibiza">
                {% for picture in instagram_pictures %}
                    <div class="instapic">
                        <a {% if not picture.fallback %}
                                href="{{ picture.permalink }}" target="_blank"
                            {% else %}
                                {% if picture.permalink %}
                                    href="{{ picture.permalink|safe }}" target="_blank"
                                {% else %}
                                    href="{{ picture.media_url|safe }}" rel="lightbox[instagram]"
                                {% endif %}
                            {% endif %}
                        >
                            <img src="{{ picture.media_url }}" alt="">
                            <span class="picture_info">
                                <i class="fal fa-external-link"></i>
                            </span>
                        </a>
                    </div>
                {% endfor %}
                {% if not instagram_pictures %}
                    {% for picture in newsletter_pictures %}
                        <div class="instapic">
                            <a href="{{ picture.servingUrl }}" rel="lightbox[newsletter]">
                                <img src="{{ picture.servingUrl }}" alt="">
                                <span class="picture_info">
                                    <i class="fal fa-search-plus"></i>
                                </span>
                            </a>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if instagram_pictures or newsletter_pictures %}
    <script>
        $(window).on("load", function() {
            var owl_config_newsletter = {
                items: 3,
                margin: 15, // 0
                loop: true, // false
                mouseDrag: true, // true
                autoWidth: true, // false
                nav: true, // false
                navText: [
                    '<i class="fal fa-long-arrow-right"></i>',
                    '<i class="fal fa-long-arrow-left"></i>'
                ], // [&#x27;next&#x27;,&#x27;prev&#x27;]
                dots: false, // true
                rtl: true // false
            }
            var $owl = $(".owl-carousel.newsletter_gallery").owlCarousel(owl_config_newsletter);
        });
    </script>
{% endif %}
<script async type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script>
    $(window).on("load", function () {
        $(".newsletter_form").validate({
            rules: {
                {% if check_newsletter %}privacy: "required",{% endif %}
                promotions: "required",
                suscEmail: {
                    required: true,
                    email: true
                }
            },
            messages: {
                suscEmail: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    email: "{{ T_campo_valor_invalido|safe }}"
                },
                privacy: "{{ T_campo_obligatorio|safe }}",
                promotions: "{{ T_campo_obligatorio|safe }}",
            },
            highlight: function (input) {
                $(input).parent().find("a").addClass('error_class');
                $(input).parent().find("label").addClass('error_class');
                $(input).parent().find("#suscEmail").addClass('error_class');
            },
            errorPlacement: function (error, element) {
                //this keeps enable the validation but hides the error message
            }
        });

        $(".button_newsletter").unbind('click').click(function () {
            var clicked_button = $(this),
                form_newsletter = clicked_button.parent(".newsletter_form");

            if (form_newsletter.valid()) {
                $.post("/utils?action=newsletter&language={{ language_code }}",
                    {
                        'email': form_newsletter.find("#suscEmail").val()
                    },

                    function (data) {
                        {% if newsletter_thanks %}
                            $.fancybox($(".newsletter_thanks_popup"), {
                                padding: 0,
                                wrapCSS: "newsletter_thanks_popup_wrapper",
                                width: 640,
                                height: 'auto',
                                fitToView: false,
                                autoSize: false,
                                modal: true
                            });
                            setTimeout(function () {
                                window.location.href = window.location.href;
                            }, 5000);
                        {% else %}
                            alert("{{ T_gracias_newsletter }}");
                        {% endif %}
                        $("#suscEmail").val("");
                    }
                );

            } else {
                alert("{{ T_campos_obligatorios|safe }} ");
                console.log("invalid");
            }

        });
    })
</script>
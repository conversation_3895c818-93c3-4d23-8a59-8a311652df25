/*======== Forms =======*/
$("form.group_contact_form").validate({
    rules: {
        name: "required",
        file: "required",
        cp: "required",
        telephone: {
            required: function (element) {
                return !$("#telephone").val() > 0 || !$("#mobile").val() > 0;
            }
        },
        //comments: "required",
        email: {
            required: true,
            email: true
        },
        privacy: "required"
    }, highlight: function (element) {
        $(element).addClass('input-error');
    }, unhighlight: function (element) {
        $(element).removeClass('input-error');
    }
});


function send_work_form(form) {

    if ($(this).hasClass("disabled")){
        return false;
    }

    var url_for_upload = "",
        url_cv_download = "";

    $(this).addClass('disabled');

   if(!$("#g-recaptcha-response").length || $("#g-recaptcha-response").val()) {
       if (form.valid()) {
           var final_form_data = {};
           var formData_dict = form.serializeArray();

           for (var i = 0; i < formData_dict.length; i++) {
               final_form_data[formData_dict[i]['name']] = formData_dict[i]['value']
           }

           if ($("input[name=destiny]:checked").length > 0) {
               final_form_data['destiny'] = $("input[name='destiny']:checked").map(function () {
                   return $(this).val()
               }).toArray().join(",");
           }

           console.log(final_form_data);

           $.ajax({
               url: "/get_upload_url",
               type: 'GET',
               async: false,
               cache: false,
               contentType: false,
               processData: false,
               success: function (returndata) {
                   url_for_upload = returndata;
               }
           });

           if (form.find("#file").length && url_for_upload && form.find("#file").val()) {
               formData_dict = new FormData(form[0]);

               console.log("Envio");
               console.log(formData_dict);

               $.ajax({
                   url: url_for_upload,
                   type: 'POST',
                   data: formData_dict,
                   async: false,
                   cache: false,
                   contentType: false,
                   processData: false,
                   success: function (returndata) {
                       url_cv_download = returndata;
                   }
               });
           }

           var formData_to_send = final_form_data;

           formData_to_send['url_file_download'] = url_cv_download;

           console.log($.param(formData_to_send));

           $.ajax({
               url: "/utils/?action=work_with_us",
               type: 'POST',
               data: formData_to_send,
               async: false,
               cache: false,
               success: function () {
                   _thanks_work_form();
               }
           });
       }
   }

    $(this).removeClass("disabled");

    return false;
}

function _thanks_work_form(){
    $('form.group_contact_form')[0].reset();
    alert($("#thanks_job").val());
}

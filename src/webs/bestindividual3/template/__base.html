<!DOCTYPE html>
<html lang="{{ language }}"  xml:lang="{{ language }}">

<head>
{% if gtm_head_opening %}{{ gtm_head_opening|safe }}{% endif %}
    {% if facebook_head_opening %}{{ facebook_head_opening|safe }}{% endif %}

<link href='//fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,200italic,300italic|Roboto:100,300,400,500,700|Playfair+Display:400,700' rel='stylesheet' type='text/css'>
<link href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300;0,400;0,600;0,700;1,300;1,400;1,600;1,700&display=swap" rel="stylesheet">

<title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %} {{sectionName|safe}}{% else %} {{hotel_name|safe}} {% endif %} {% endif %}</title>
<meta http-equiv="content-type" content="text/html; charset=UTF-8" />

{% if namespace %}
	  <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico" type="image/x-icon">
{% else  %}
      <link rel="icon" href="/static_1/images/favicon.ico" type="image/x-icon">
{% endif %}

<meta name="keywords" content="{{keywords|safe}}" />
<meta name="description" content="{{description|safe}}" />
<meta name="revisit-after" content="2 days" />
<meta http-equiv="Content-Language" content="{{language}}" />

<meta name="dc.title" content="{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %} {{sectionName|safe}}{% else %} {{hotel_name|safe}} {% endif %} {% endif %}" />
<meta name="dc.description" content="{{description|safe}}" />
<meta name="dc.keywords" content="{{keywords|safe}}" />
<meta name="dc.language" content="{{ language }}" />
<meta name="dc.creator" content="{{ hotel_name }}"/>
<meta name="dc.format" content="text/html" />
<meta name="dc.identifier" content="{{ hostWithoutLanguage}}{{ path }}" />

{% if color_1 %}
    <style>
        #content, .top_header {
            background-color: {{ color_1 }}!important;
        }

        header {
            background-color: {{ color_1 }}cc!important;
        }

        div#full_wrapper_booking {
            background-color: {{ color_1 }}bf!important;
        }

        {% if color_2 %}
            .content_subtitle_wrapper h1.content_subtitle_title {
                color: {{ color_2 }} !important;
            }

            #full_wrapper_booking .wrapper_booking_button .submit_button {
                background-color: {{ color_2 }}!important;
            }
        {% endif %}
    </style>
{% endif %}
    <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/loading.css?v=1.1"/>

<!-- REVOLUTION BANNER CSS SETTINGS -->
	<link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings.css" media="screen" />
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings-ie8.css" media="screen" />

<!-- Font Awesome for icons -->
    <link href="//maxcdn.bootstrapcdn.com/font-awesome/4.7.0/css/font-awesome.min.css" rel="stylesheet" integrity="sha384-wvfXpqpZZVQGK6TAh5PVlGOfQNHSoD2xbE+QkPxCAFlNEevoEH3Sl0sibVcOQVnN" crossorigin="anonymous">

    {% if datepicker_theme %}
    <link type="text/css" rel="stylesheet" href="/static_1/css/plugins/jquery-ui/{{ datepicker_theme }}/jquery-ui-1.10.3.custom.css"/>
    {% else %}
    <link type="text/css" rel="stylesheet" href="/static_1/css/datepicker.redmond/jquery-ui-1.8.16.custom.css"  />
    {% endif %}

<!-- lightbox -->

    <link rel="stylesheet" href="/static_1/lib/lightbox/css/lightbox.css" type="text/css"/>

<!-- styles -->
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/css/1140/1140.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/css/booking/booking_engine_2_vertical.css" />
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/pricescalendar/styles.css?v=1">
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/dates-selector/css/datepicker_ext_inf.css?v=1">
    <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles.css?v=@@automatic_version@@" />

<!--[if IE 8]>
<link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ie8.css" />

<![endif]-->

<!--[if IE 9]>

<![endif]-->

    <script type="text/javascript">
if(navigator.userAgent.match(/Android/i)
|| navigator.userAgent.match(/webOS/i)
|| navigator.userAgent.match(/iPhone/i)
|| navigator.userAgent.match(/iPad/i)
|| navigator.userAgent.match(/iPod/i)
|| navigator.userAgent.match(/BlackBerry/i)
|| navigator.userAgent.match(/Windows Phone/i)) {
 document.write('<meta name="viewport" content="width=1160, initial-scale=1, user-scalable=yes">');
}
</script>

<!--[if lte IE 7]>
	<script type="text/javascript">
	alert('{{ T_explorer6_no_soportado }}');
	</script>
<![endif]-->

<!--[if lte IE 8]>
<script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
<![endif]-->


<!-- jquery -->
    {{ jquery|safe }}

    {{ extra_head|safe }}

    {% if gtm_datalayer_web %}
        {{ gtm_datalayer_web|safe }}
    {% endif %}

    {{ all_tracking_codes_header|safe }}
</head>

<body itemscope itemtype="//schema.org/Hotel" class="{% if inner_section %}inner_section{% else %}home_section{% endif %} {% if "best-serenade" in namespace %}{{ namespace }}{% endif %} {% if "test11" in namespace %}{{ namespace }}{% endif %}">
{% if gtm_body_opening %}{{ gtm_body_opening|safe }}{% endif %}

{{ all_tracking_codes_body|safe }}
{% include "_loading.html" %}
{{ rich_snippet|safe }}
<meta itemprop="description" content="{{ description_microdata }}">

{% if lang_management %}
<input type="hidden" id="lang_management" value="{{ lang_management }}">
{% endif %}
{% if lang_default %}
<input type="hidden" id="lang_default" value="{{ lang_default }}">
{% endif %}

{% block content %}

<!--EDIT HERE YOUR PAGE-->

{% endblock %}

{{ scripts_to_render_desktop|safe }}

{% comment %}<script type="text/javascript" src="/static_1/js/mainWebSkeletonAux.min.js"></script>
<script type="text/javascript" src="/static_1/lib/jquery-i18n-1.1.1/jquery.i18n.js"></script>{% endcomment %}

<script type="text/javascript" src="/static_1/i18n/messages_{{language_code}}.js"></script>


{% block additional_js %}

    <!-- jquery datepicker -->
    {% comment %}<script type="text/javascript" src="/static_1/lib/jquery-ui-1.10.1.custom.min.modified.js"></script>
    <script src="/static_1/js/datepicker/jquery.ui.datepicker-{{language_code}}.js" type="text/javascript"></script>{% endcomment %}

    <script type="text/javascript" src="/static_1/scripts/common.js?v=2.1"></script>

    <!-- lightbox -->
    {% comment %}<script type="text/javascript" src="/static_1/lib/lightbox/js/lightbox.js"></script>
    <script src="/static_1/lib/selectric/jquery.selectric.min.js" type="text/javascript"></script>{% endcomment %}

    <script src="/static_1/lib/jquery.unveil/jquery.unveil.min.js"></script>
    <script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
    <script src="/static_1/lib/owlcarousel/owl.carousel.min.js"></script>

     <!-- My specific js  -->
    <script type="text/javascript" src="/js/{{ base_web }}/booking_popup_personalized.js?v=3.2"></script>
    <script async type="text/javascript" src="/static_1/scripts/booking_full_popup.js?v=1.1"></script>

    <script type="text/javascript" src="/js/{{ base_web }}/analytics_utils.js?v=1.3"></script>
    <script type="text/javascript" src="/js/{{ base_web }}/users_register.js?v=8.1"></script>

    <!-- new booking engine -->
    <script type="text/javascript" src="/static_1/plugins/pricescalendar/calendar.plugin.js?v=1"></script>
    <script type="text/javascript" src="/static_1/plugins/dates-selector/datepicker_v1.js?v=1.2"></script>
    <script>
        $(function () {
            DP_extend_info.config.booking_version = '7';
            DP_extend_info.config.months_show = 2;
            DP_extend_info.config.custom_format_day_month = function (dateComponents) {
                dateComponents = dateComponents.split("/");
                var month_short = $.datepicker._defaults['monthNamesShort'][parseInt(dateComponents[1], 10) - 1];
                return "<div class='day'>" + dateComponents[0] + "</div><div class='month'>" + month_short + "</div><div class='year'>" + dateComponents[2] + "</div>";
            };
            DP_extend_info.config.show_callback = opened_datepicker;
            DP_extend_info.config.hide_callback = closed_datepicker;
            DP_extend_info.config.short_days_names = true;

            $.i18n.dict["entry_date_select"] = "{{ T_fechas }}";
            $.i18n.dict["departure_date_select"] = "{{ T_fechas }}";

            $("body > .black_overlay").click(function(){
                DP_extend_info.hide_datepicker_wrapper();
            })
        });
        $(window).load(function () {
            DP_extend_info.init();
            $("label.dates_selector_label").html("<span>{{ T_entrada }}</span><span>{{ T_salida }}</span>");
        });
    </script>
    <script type="text/javascript" src="/static_1/scripts/booking_7.js"></script>
    <script type="text/javascript" src="/js/{{ base_web }}/functions.js?v=8.53"></script>
    <script type="text/javascript" src="//www.tripadvisor.com/js3/conversion/pixel.js" async></script>
    <script src='https://www.google.com/recaptcha/api.js'></script>


    <!-- KenBurn Slider ALWAYS AT THE END!!!!!!!! -->
     <!-- jQuery KenBurn Slider  -->
    {% comment %}<script type="text/javascript" src="/static_1/lib/rs-plugin/js/revolution_4_6_4/jquery.themepunch.tools.min.js"></script>
    <script type="text/javascript" src="/static_1/lib/rs-plugin/js/revolution_4_6_4/jquery.themepunch.revolution.js"></script>{% endcomment %}

    <script type="text/javascript" src="/static_1/lib/smoothscroll/smoothscroll.js"></script>

    <script src="/static_1/lib/flexslider/jquery.flexslider.js"></script>

    <script src="/static_1/scripts/hotel_selector_2.js"></script>
    <script type="text/javascript" src="/js/{{ base_web }}/functions_v2.js?v=1.56"></script>
    <script>
        //HotelSelector.config.preselect_hotel = '{{ individual_hotel.hotel_namespace }}';
    </script>


{% endblock %}




{% include "sections_config/_popup_inicio_automatico.html" %}



<script>
    $("#newsletter #suscEmail").attr('placeholder', '{{ T_introduzca_email }}');
    unsubscribe_message = '{{ T_baja_completa }}';

    $(function () {

        make_booking_label = "{{ T_reservar }}";

        {% if promocode %}
            $(".promocode_input").val("{{ promocode|safe }}");
        {% endif %}

        {% if countdown %}
            var countDownDate = new Date("{{countdown|safe}}").getTime();

            function updatecounter() {
                var now = new Date().getTime();
                var distance = countDownDate - now;
                var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                if(hours < 10 ){hours = "0"+ hours}
                var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                if(minutes < 10 ){minutes = "0"+ minutes}
                var seconds = Math.floor((distance % (1000 * 60)) / 1000);
                if(seconds < 10 ){seconds = "0"+ seconds}
                $(".countdown").html("<table><tr><td class='countdown_element first'>" + days + "<span> {{ T_dias }}</span></td><td class='countdown_element'>" + hours + "<span> {{ T_horas }}</span></td><td class='countdown_element'>"
                + minutes + "<span> {{ T_minutos }}</span></td><td class='countdown_element'>" + seconds + "<span> {{ T_segundos }}</span></td></tr></table>");
                if (distance < 0) {
                    clearInterval(x);
                    $(".countdown").html("");
                }
            }

            var x = setInterval(function() {
                updatecounter();
            }, 1000);
        {% endif %}
    })
</script>

<!-- AFFILIRED MASTER TAG, PLEASE DON'T REMOVE -->
<script type="text/javascript">
    (function () {
        var sc = document.createElement('script'); sc.type='text/javascript'; sc.async=true;
        sc.src='//customs.affilired.com/track/?merchant=4064';
        var s = document.getElementsByTagName('script')[0]; s.parentNode.insertBefore(sc, s);
    })();
</script>
<!-- END AFFILIRED MASTER TAG -->

<script>
    user_not_exist = "{{ T_repeated_user }}";
    incorrect_user_message = "{{ T_bad_user }}";
    user_or_password_bad = "{{ T_login_ko }}";

    profile_viewer_translates = {
        surname: "{{ T_apellidos }}",
        gender: "{{ T_genero }}",
        password: "{{ T_contrasena }}",
        name: "{{ T_nombre }}",
        idmember: "ID",
        username: "{{ T_usuario }}",
        dni: "DNI",
        language: "Language",
        email: "{{ T_email }}",
        city: "{{ T_ciudad }}",
        province: "{{ T_provincia }}",
        childs_number: "{{ T_num_hijos }}",
        job: "{{ T_profesion }}",
        civil_status: "{{ T_estado_civil }}",
        pais: "{{ T_pais }}",
        address: "{{ T_direccion }}",
        telephone: "{{ T_telefono }}",
        postal_code: "{{ T_codigo_postal }}",
        birthday: "Cumpleaños"

    };
</script>

<script>
    adult_tag = "{{ T_adulto }}";
    adults_tag = "{{ T_adultos }}";
    kid_tag = "{{ T_nino }}";
    kids_tag = "{{ T_ninos }}";
    baby_tag = "{{ T_bebe }}";
    babies_tag = "{{ T_bebes }}";
    years_tag = "{{ T_anyos }}";
</script>

{{ extra_content_website|safe }}
{% if extra_bottom_script %}
    {{ extra_bottom_script|safe }}
{% endif %}

<div class="black_overlay"></div>


{{ all_tracking_codes_footer|safe }}
</body>
</html>

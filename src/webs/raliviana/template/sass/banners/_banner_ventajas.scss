.banner_ventajas_wrapper {
    position: relative;
    padding: 100px 0 50px;
    
    .content_title {
        text-align: center;
        
        .title {
            font-family: $secondary_font;
            font-size: 70px;
        }
    }

    .services {
        position: relative;
        text-align: center;
        padding: 20px 0 50px;
        
        .service {
            display: inline-block;
            vertical-align: top;
            margin: 0 20px;
            width: 150px;
            
            span {
                display: block;
                width: 100%;
                padding: 20px 0;
                text-align: center;
                font-size: 15px;
                font-weight: 300;
                color: #4b4b4b;
                
                &:lang(fr) {
                    font-size: 16px;
                }
                
                strong {
                    font-weight: 700;
                }
            }
            
            i.fa {
                position: relative;
                display: block;
                margin: auto;
                text-align: center;
                font-size: 40px;
                color: $corporate_2;
                width: 70px;
                height: 70px;
                border: 1px solid lightgrey;
                border-top: 3px solid $corporate_1;
                margin-bottom: 1px;
                border-radius: 50%;
                -webkit-animation-name: rotate-border;
                -webkit-animation-duration: 5s;
                -webkit-animation-delay: 2s;
                -webkit-animation-iteration-count: infinite;
                -webkit-animation-direction: normal;
                -webkit-animation-timing-function: ease-in-out;
                animation-name: rotate-border;
                animation-duration: 5s;
                animation-delay: 2s;
                animation-iteration-count: infinite;
                animation-direction: normal;
                animation-timing-function: ease-in-out;
                @include transition(all, .3s);
                
                &:before {
                    @include center_xy;
                }
            }
            
            &:hover {
                i.fa {
                    border: 3px solid $corporate_1 !important;
                    margin-bottom: 0;
                }
            }
            
            &:nth-child(1) i.fa {
                -webkit-animation-delay: 1.8s;
                animation-delay: 1.8s;
            }
            
            &:nth-child(2) i.fa {
                -webkit-animation-delay: 2s;
                animation-delay: 2s;
            }
            
            &:nth-child(3) i.fa {
                -webkit-animation-delay: 2.2s;
                animation-delay: 2.2s;
            }
            
            &:nth-child(4) i.fa {
                -webkit-animation-delay: 2.4s;
                animation-delay: 2.4s;
            }
        }
    }
}


@-webkit-keyframes rotate-border {
    0%, 10% {
        -webkit-transform: translate(0, -5px);
        -moz-transform: translate(0, -5px);
        -ms-transform: translate(0, -5px);
        -o-transform: translate(0, -5px);
        transform: translate(0, -5px);
    }
    10%, 50% {
        -webkit-transform: translate(0, 0);
        -moz-transform: translate(0, 0);
        -ms-transform: translate(0, 0);
        -o-transform: translate(0, 0);
        transform: translate(0, 0);
    }
    50%, 100% {
        -webkit-transform: translate(0, 0);
        -moz-transform: translate(0, 0);
        -ms-transform: translate(0, 0);
        -o-transform: translate(0, 0);
        transform: translate(0, 0);
    }
}

@keyframes rotate-border {
    0%, 10% {
        -webkit-transform: translate(0, -5px);
        -moz-transform: translate(0, -5px);
        -ms-transform: translate(0, -5px);
        -o-transform: translate(0, -5px);
        transform: translate(0, -5px);
    }
    10%, 50% {
        -webkit-transform: translate(0, 0);
        -moz-transform: translate(0, 0);
        -ms-transform: translate(0, 0);
        -o-transform: translate(0, 0);
        transform: translate(0, 0);
    }
    50%, 100% {
        -webkit-transform: translate(0, 0);
        -moz-transform: translate(0, 0);
        -ms-transform: translate(0, 0);
        -o-transform: translate(0, 0);
        transform: translate(0, 0);
    }
}

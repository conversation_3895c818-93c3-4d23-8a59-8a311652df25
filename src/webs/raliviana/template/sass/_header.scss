header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 50;
    padding: 20px calc((100% - 1140px) / 2) 120px;
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0));
    min-width: 1140px;
    
    .header_top {
        display: flex;
        padding-top: 40px;
        
        #logoDiv {
            @include center_x;
            top: 0;
            padding: 30px 0 0;
            
            img {
                max-width: 170px;
            }
        }
        
        .top_content {
            display: flex;
            align-items: center;
            width: 50%;
            
            * {
                color: white;
                font-weight: 500;
                font-size: 13px;
            }
            
            &.left {
                justify-content: flex-start;
                
                span, a {
                    margin-right: 40px;
                }
                
                .phone_contact {
                    position: relative;
                    padding-left: 35px;

                    small {
                        font-size: 10px;
                        display: block;
                        position: absolute;
                        white-space: nowrap;
                    }
                    
                    &::before {
                        position: absolute;
                        font-family: 'Font Awesome 5 Pro';
                        content: '\f095';
                        top: 0;
                        left: 0;
                        font-size: 18px;
                        font-weight: 100;
                    }
                }
            }
            
            &.right {
                justify-content: flex-end;

                .top_section_link {
                    margin-right: 20px;
                }

                #social {
                    margin: 0 20px;
                    
                    i {
                        font-size: 18px;
                    }
                }

                #lang {
                    display: flex;

                    .lang {
                        position: relative;
                        width: 36px;
                        height: 36px;
                        overflow: hidden;
                        text-align: center;
                        border-radius: 50%;
                        font-size: 14px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        margin-left: 20px;
                        background-color: transparent;
                        color: white;
                        transition: all .4s;

                        &.selected, &:hover {
                            border: 1px solid white;
                        }

                        &:hover {
                            background-color: white;
                            color: $corporate_1;
                        }
                    }
                }
            }
            
            .top_section_link {
                
                i {
                    margin-right: 10px;
                    font-size: 18px;
                    font-weight: 100;
                }
            }
        }
    }
}

#main_menu {
    position: relative;
    z-index: 5;
    padding-top: 40px;
    
    
    #main-sections-inner {
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
        
        .main-section-div-wrapper {
            width: auto;
            
            a {
                display: block;
                text-transform: uppercase;
                color: white;
                text-align: center;
                font-size: 14px;
                font-weight: 500;
                position: relative;
                @include transition(color, .6s);
                
                &::before {
                    opacity: 0;
                    position: absolute;
                    content: '';
                    transform: translateY(10px);
                    bottom: 0;
                    left: 0;
                    right: 0;
                    height: 2px;
                    background-color: $corporate_1;
                    transition: all .4s;
                }
            }
            
            &:hover, &#section-active {
                > a {
                    position: relative;
                    color: $corporate_1;
                    &::before {
                        opacity: 0;
                        transform: translateY(0);
                    }
                }
            }
        }
    }
}
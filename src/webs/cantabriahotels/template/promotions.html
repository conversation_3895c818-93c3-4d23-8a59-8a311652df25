

{% block "main_content" %}

<section id="content">
    <div id="wrapper_content" class="container12">
        <div class="promotion-content-wrapper">

            <div class="promotion-blocks">
                {% for promotion in promotions %}

                    <div class="promotion {% cycle 'row1' 'row2' %}">

                        <div class="promotion-header">
                            <div class="description">
                                <h3 class="title-module">{{promotion.name|safe}}</h3>
                                <div class="offer_description">
                                    {{promotion.description|safe}}
                                </div>
                            </div>
                            {% if not i_am_mobile %}
                                <ul class="buttons_promotion_wrapper">
                                    <li><a href="{% if i_am_mobile %}/{% else %}#data{%  endif %}" class="button-promotion" data-promocode="{% if promotion.promocode %}{{ promotion.promocode }}{% endif %}">{{ T_reservar }}</a> </li>
                                    <li><a href="#promo-popup-{{forloop.counter}}" class="myFancyPopupAuto read-more">{{T_ver_mas}}</a></li>
                                </ul>
                            </ul>
                            {% endif %}
                        </div>
                        <a href="#promo-popup-{{forloop.counter}}" class="{% if i_am_mobile %}scapesmobileversion{% else %}myFancyPopupAuto{%  endif %}">
                            <img src="{{promotion.picture}}=s1000" alt="{{promotion.name|safe}}" title="{{promotion.name|safe}}">
                        </a>

                        {% if i_am_mobile %}
                            {{promotion.description|safe}}
                            <a href="/" class="booking-mobile">{{ T_reservar }}</a>
                        {%  endif %}

                        <div id="promo-popup-{{forloop.counter}}" class="promo-popup-container" style="display:none">
                            <h3 class="title-module">{{promotion.name|safe}}</h3>
                            {{promotion.description|safe}}
                        </div>

                    </div>

                {% endfor %}

            </div>

        </div>

    </div>
</section>

{% endblock %}
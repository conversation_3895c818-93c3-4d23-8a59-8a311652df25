<footer>
    <div class="wrapper_footer_columns container12">
        <div class="footer_column column4">
            <h3 class="footer_column_title">{{ footer_columns.0.title|safe }}</h3>
            <div id="footer_column_description">{{ footer_columns.0.description|safe }}</div>
        </div>
        <div class="footer_column column4">
            <h3 class="footer_column_title">{{ footer_columns.2.title|safe }}</h3>
            <div id="footer_column_description">
                <ul>
                    {% for section in main_sections %}
                    <li><a itemprop="url" href="{{host|safe}}/{{seoLinkString}}{{section.friendlyUrl}}">
                    <span itemprop="name">{{ section.title|safe }}</span>
                    </a></li>
                    {% endfor %}
                </ul>
            </div>
        </div>
        <div class="footer_column column4">
            <h3 class="footer_column_title">{{ footer_columns.1.title|safe }}</h3>
            <div id="footer_column_description">{{ footer_columns.1.description|safe }}</div>
        </div>

        <div class="footer_column column4 last">
            <h2 id="title_newsletter">{{ newsletter_info.title|safe }}</h2>
            <label for="suscEmail" id="suscEmailLabel" style="display: block;">{{ newsletter_info.subtitle|safe }}</label>
            <div id="footer_column_description">{{ newsletter|safe }}</div>
        </div>

    </div>

    <div class="container12 bottom_footer">

        <div class="column12">
            <div id="facebook_like">
                <div id="fb-root"></div>
                <script src="//connect.facebook.net/es_ES/all.js#appId=128897243865016&amp;xfbml=1"></script>
                <div>
                    <fb:like font="" href="" layout="button_count" send="false" show_faces="false" width="110"></fb:like>
                </div>
            </div>
            <div id="google_plus_one">
                <div class="g-plusone"></div>
            </div>
        </div>

         <div class="full-copyright">
            <div class="footer-copyright container12">


                <div id="social-widgets">
                    <div id="google">{{ footer_columns.4.description|safe }}</div>
                    <div id="facebook">{{ footer_columns.5.description|safe }}</div>
                </div>

                {% for x in policies_section %}
                    <a href="/{{ language }}/?sectionContent={{ x.friendlyUrl }}" class="myFancyPopup fancybox.iframe">{{ x.title|safe }}</a> |
                {% endfor %}
                <a target="_blank" href="https://www.paratytech.com/motor-de-reservas.html" title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
                <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
                <a target="_blank" href="/rss.xml">RSS</a>

            </div>

              {% if texto_legal %}
                <div id="div-txt-copyright" class="footer-copyright container12">
                  {{texto_legal|safe}}
                </div>
              {% endif %}

        </div>

    </div>


</footer>
<div class="landing_page_main_content">
    <h3 class="landing_title">{{ landing_page_content.subtitle|safe }}</h3>

    <div class="landing_page_content">{{ landing_page_content.content|safe }}</div>

    <div class="lateral_elements_wrapper">
        <div class="tripadvisor_wrapper">
            <img class="tripadvidor_image" src="{{ landing_page_tripadvisor.servingUrl }}">
        </div>

        <div class="weather_widget">
            <div class="site_info">
                <strong></strong>
                <small></small>
            </div>
            <div class="image_info">
                <img src="">
            </div>
            <div class="weather_info"></div>
            <div class="left_info">
                <div class="pressure">
                    <strong></strong>
                    pressure
                </div>

                <div class="visibility">
                    <strong></strong>
                    visibility
                </div>

                <div class="humidity">
                    <strong></strong>
                    pressure
                </div>
            </div>

            <div class="right_info">
                <div class="sunrise">
                    <strong></strong>
                    sunrise
                </div>

                <div class="sunset">
                    <strong></strong>
                    sunset
                </div>

                <div class="wind">
                    <strong></strong>
                    wind
                </div>
            </div>

            <div class="bottom_weather_info">
                <strong></strong>
                <small></small>
                <div class="degress_mark">ºC</div>
            </div>
        </div>
        <div class="weather_tomorow_wrapper">

        </div>
    </div>
</div>


<div class="rooms_landing_wrapper">
    <h3 class="room_landing_title">{{ landing_page.room_section.0.title|safe }}</h3>

    <div class="room_services_wrapper">
        {% for service_element in landing_page.room_services %}
            <div class="room_service_element">
                <img class="room_service_image" src="{{ service_element.servingUrl }}">

                <div class="room_service_title">{{ service_element.title|safe }}</div>
            </div>
        {% endfor %}
    </div>


    <div class="rooms_blocks_wrapper">
        {% for room_element in landing_page.room_elements %}
            <div class="room_element_wrapper">
                <div class="exceded"><img class="room_image" src="{{ room_element.servingUrl }}"></div>
                <div class="room_element_text">
                    <h4 class="room_element_title">{{ room_element.title|safe }}</h4>

                    <div class="room_element_description">{{ room_element.description|safe }}</div>
                </div>
            </div>
        {% endfor %}
    </div>

</div>


<div class="room_services_blocks_wrapper">
    <h3 class="services_landing_title">{{ T_servicios }}</h3>

    {% for block_element in landing_page.services %}
        {% if block_element.linkUrl %}<a href="{{ block_element.linkUrl }}">{% endif %}
        <div class="room_service_block_element">
            <div class="exceded">
                <img src="{{ block_element.servingUrl }}">
            </div>

            <div class="service_block_title">{{ block_element.title|safe }}</div>
        </div>
        {% if block_element.linkUrl %}</a>{% endif %}
    {% endfor %}
</div>


<div class="landing_room_gallery">
    {{ gallery_landing }}
</div>


<div class="region_landing_wrapper">
    <h3 class="location_landing_title">{{ T_localizacion }}</h3>

    <div class="location_map_wrapper">
        <div class="overlay_map"><span>{{ T_ver }}</span></div>
        {{ iframe_google_maps.content|safe }}
    </div>

    <div class="region_landing_text">
        {{ landing_location_text.content|safe }}
    </div>
</div>


{% if carousel_banners_landing %}
    <div class="carousel_landing_block">
        {% include "desktop_old/carousel_banners.html" %}
    </div>
{% endif %}


<div class="landing_booking_widget"></div>

<script src="/static_1/lib/jquery.simpleweather/jquery.simpleWeather.min.js"></script>
<script src="/js/{{ base_web }}/landing_scripts.js"></script>

<input type="hidden" value="{{ location_widget_weather|safe }}" class="location_widget_hidden"/>
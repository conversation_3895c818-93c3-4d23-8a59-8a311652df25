.ui-datepicker {
  width: 283px;

  .ui-widget-header {
    background: none !important;
    border: 0;

    .ui-datepicker-title {
      color: #646464 !important;
      font-family: <PERSON><PERSON><PERSON>, Aria<PERSON>, sans-serif;
      font-weight: 300;
    }
  }
}

body {

  .ui-dialog.ui-widget .ui-dialog-buttonpane .ui-button {
    font-size: 17px !important;
    border: 1px solid #a6a6a6 !important;
    margin: 0 !important;
    display: inline-block !important;
    width: 100% !important;
    max-width: 350px !important;
    border-radius: 0 !important;
    background: #BF705B !important;
    color: white !important;
  }






  .ui-datepicker th {
    font-weight: 300;
    font-size: 14px;
  }

  .ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
    background: transparent !important;
    border: 0 !important;
    color: #646464 !important;
    font-weight: 400;
    font-family: Circular, "Helvetica Neue", Helvetica, Arial, sans-serif;
    text-align: center;
    font-size: 13px;
  }

  .ui-widget-content .ui-state-hover {
    border: 0;
    background: $corporate_1 !important;
    color: white !important;
  }

  .ui-widget-content {
    border: 0;
    border-radius: 0;
    padding-bottom: 40px;
  }

  .ui-state-default {
    padding: 8px !important;
  }

  .ui-datepicker-start_date {
    opacity: 1 !important;

    .ui-state-default {
      border: 0;
      background: $corporate_1 !important;
      color: white !important;
    }
  }

  .ui-datepicker-highlighted .ui-state-default {
    border: 0;
    background: rgba(40, 96, 144, 0.25) !important;
  }

  .ui-widget-content .ui-state-active {
    border: 0;
    background: $corporate_1 !important;
    color: white !important;
  }

  span.ui-icon.ui-icon-circle-triangle-e, span.ui-icon.ui-icon-circle-triangle-w {
    background: url(/static_1/images/mobile_img/renovation/flecha_der.png) no-repeat center;
  }

  span.ui-icon.ui-icon-circle-triangle-w {
    -webkit-transform: rotate(180deg);
    -moz-transform: rotate(180deg);
    -ms-transform: rotate(180deg);
    -o-transform: rotate(180deg);
    transform: rotate(180deg);
  }

  span.ui-icon.ui-icon-circle-triangle-e, span.ui-icon.ui-icon-circle-triangle-w {
    &:before {
      display: none;
    }
  }

  .ui-datepicker .ui-datepicker-next, .ui-datepicker .ui-datepicker-prev {
    background: none;
    cursor: pointer;
  }

  .ui-datepicker-next.ui-state-hover, .ui-datepicker-prev.ui-datepicker-prev-hover {
    border: 1px solid #636363;
  }
}

.start_end_date_wrapper {
  width: 290px;
  height: 47px;
  display: inline-block;
  background: white url(/img/#{$base_web}/calendar_ico.png) no-repeat 4px center;
  font-weight: 300;
  font-size: 14px;
  padding: 15px 33px 12px;
  box-sizing: border-box;
  cursor: pointer;
  position: relative;
}

.dates_selector_personalized {
  display: inline-block;
  float: left;
  margin-right: 5px;
  position: relative;
}

.nights_number_wrapper_personalized {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 55px;
  background: $corporate_3;
  line-height: 16px;

  span.days_number_datepicker {
    display: block;
    text-align: center;
    color: white;
    font-size: 14px;
    font-weight: 500;
    margin-top: 8px;
  }

  span.night_label {
    color: white;
    font-size: 12px;
    text-align: center;
    display: block;
  }

  .ui-datepicker td {
    border: 0;
    padding: 1px 0;
  }
}

.stay_selection {
  display: none !important;
}

label.dates_selector_label {
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  font-size: 10px;
}

.start_date_datepicker, .departure_datepicker {
  position: absolute;
  top: 100%;
}

.close_calendar {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 40px;
  height: 40px;
  cursor: pointer;
  background: rgb(83, 84, 84);

  &:before {
    content: "\f00d";
    font-family: FontAwesome;
    @include center_xy;
    color: white;
  }
}

.entry_label_calendar, .departure_label_calendar {
  position: absolute;
  bottom: 0;
  font-family: 'Roboto', sans-serif;
  left: 0;
  font-weight: 300;
  color: white;
  font-size: 13px;
  background: rgb(49, 83, 144);
  width: 250px;
  line-height: 40px;
  padding-left: 18px;
  box-sizing: border-box;
}
.cols_table_wrapper {
  position:relative;
  padding:20px 0;
  .cols_background {
    overflow: hidden;
    @include full_size;
    .owl-stage-outer, .owl-stage {
      height: 100%;
    }
    .owl-item {
      width: 100%;
      height: 100%;
      overflow: hidden;
      img {
        min-width: 100%;
        width: auto;
        min-height: 100%;
        max-width: none;
      }
    }
  }
  .cols_table {
    position: relative;
    border-spacing: 2px;
    border-collapse: separate;
    z-index: 2;
    margin: 0 1em;
    tr {
      td {
        background-color: rgba(0,0,0,0.5);
      }
      &:nth-child(even) {
        td {
          background-color: rgba(0,0,0,0.7);
        }
      }
      &:first-of-type {
        td:first-of-type {
          background-color: transparent;
        }
      }
      &:nth-child(2) {
        td:first-of-type {
          border-radius: 15px 0 0 0;
        }
      }
      &:last-of-type {
        td:first-of-type {
          border-radius: 0 0 0 15px;
        }
        td:last-of-type {
          border-radius: 0 0 15px 0;
        }
      }
    }
    th,
    td {
      width: calc(100% / 4);
      text-align: center;
      vertical-align: middle;
      margin: 1px;
      color:white;
      &:last-of-type {
        border-right-width: 0;
      }
    }
    th {
      color: white;
      padding: 10px;
      box-sizing: border-box;
      font-size: 17px;
      border-radius: 15px 0 0 0;
      background-color: $corporate_1;
      &:last-of-type {
        background-color: #F9A602;
      border-radius: 0 15px 0 0;
      }
    }
    td {
      font-size: 14px;
      padding: 10px;
      font-weight: lighter;
      strong {
        font-weight: normal;
      }
      &:first-of-type {
        text-align: left;
        width: calc(100% / 4 * 2);
      }
      i.fa {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        overflow: hidden;
        position: relative;
        color: white;
        background-color: #4DB26F;
        &.fa-times {
          background-color: #FF5D55;
        }
        &:before {
          @include center_xy;
        }
      }
    }
  }
}
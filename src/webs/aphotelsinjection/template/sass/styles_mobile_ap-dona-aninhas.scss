@import "defaults";
@import "styles_mobile/2/2";

$corporate_1: #776B6A;


.breadcrumbs,
.mobile_engine,
.mailto {
  display: none;

}

nav{
  display: table;
  border-top: 1px solid black;

  > a{
    background-color: white;
  }
  a .flag{
    z-index: 1;
  }
  .lang_selector{
    position: relative;
    z-index: 5;
    width: 100%;
    border-radius: 0;

  }
}


header {
  background-color: $corporate_1;
}

#hotelSelect {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 2px solid $corporate_1;
  display: inline-block;
  width: 100%;
  height: 50px;
  padding-left: 25px;
  box-sizing: border-box;
}

#my-bookings-form-search-button, #cancelButton, #my-bookings-form-modify-button {
  background-color: $corporate_1;
  color: white;
  display: inline-block;
  width: 100%;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border: 0;
  height: 40px;
  font-size: 16px;
  text-transform: uppercase;
}

#cancelButton {
  background-color: $corporate_1;
}
#my-bookings-form-modify-button{
    margin-bottom: 5px;
    background-color: $corporate_1;
    color: white;
}

.popup_booking_not_modificable_wrapper .center_block {
  grid-template-rows: auto!important;
}
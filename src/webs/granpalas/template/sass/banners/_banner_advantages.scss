.banner_advantages_full_wrapper {
  .content_wrapper {
    width: 1140px;
    margin: 0 auto;
    padding: 60px 0;
    display: flex;
    justify-content: center;
    align-items: center;

    .title_wrapper {
      .content_title {
        @include title_styles;
        padding-bottom: 30px;
        font-weight: 600;
        text-transform: none;
        margin-right: 30px;
      }
    }
    
    .icons_wrapper {
      padding: 15px 0 5px;
      display: flex;
      justify-content: space-between;

      .icon {
        margin: 0 30px;
        white-space: nowrap;
        position: relative;

        .icon_wrapper {
          position: relative;
          display: inline-block;
          vertical-align: middle;
          width: 100px;
          height: 100px;
          border-radius: 50%;
          background-color: white;
          font-size: 60px;
          color: #0096D2;
          -webkit-box-shadow: 2px 9px 23px -7px rgba(0,0,0,0.72);
          -moz-box-shadow: 2px 9px 23px -7px rgba(0,0,0,0.72);
          box-shadow: 0px 5px 10px -5px rgba(0,0,0,0.72);
          transition: transform .3s;

          &:after {
            top: calc(50% - 23.5px);
            left: calc(50% - 23.5px);
          }

          i {
            @include center_xy;
          }
        }

        .text {
          color: white;
          display: inline-block;
          vertical-align: middle;
          font-size: 14px;
          white-space: normal;
          position: absolute;
          background: #0096D2;
          top: 50%;
          padding: 20px;
          border-radius: 5px;
          text-align: center;
          width: 130px;
          @include center_x;
          opacity: 0;
          transition: opacity .3s;

          &:after {
            content: "";
            position: absolute;
            background: #0096D2;
            width: 25px;
            height: 25px;
            top: 5px;
            left: 50%;
            @include center_x;
            transform: rotate(45deg) translate(-50%, 0%);
          }
        }

        &:hover {
          .text {
            opacity: 1;
          }

          .icon_wrapper {
            transform: translateY(-50px);
          }
        }
      }
    }
  }
}
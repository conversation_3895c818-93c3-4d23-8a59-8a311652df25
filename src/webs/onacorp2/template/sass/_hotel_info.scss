.hotel_info_wrapper {
  padding: 30px calc((100% - 1040px) / 2);
  &.restaurant_wrapper {
    padding-bottom: 0;
  }

  .sports_friendly & {
    .wave-navy-small {
      display: none;
    }
  }
  .breadcrumbs {
    margin-bottom: 30px;
    @include body2;
    font-weight: 500;
    color: $aqua1;
    a {
      color: $aqua1;
    }
  }

  .tags {
    padding: 18px 0 0;
    span {
      @include overline;
      display: inline-block;
      padding: 3px 15px;
      background: $aqua1;
      border-radius: 30px;
      margin-right: 15px;
    }
  }
  .score {
    color: $corporate_1;
    margin-top:25px;
    margin-bottom: 30px;
    font-weight: normal;
    a {
      color: $corporate_1;
      &.hotel_fav {
        &.active {
          i:before {
            color: $red;
            font-weight: 900;
          }
        }
      }
    }
  }
  .description {
    margin-top: 25px;
    .content {
      @include h4;
      font-size:21px;
      line-height: 28px;
      @include transition(all, .5s);

      .policy_wrapper {
        line-height: 32px;
        font-size: 18px;

        p {
          margin: 20px 0 25px;
        }

        table {
          margin: 20px 0 45px;

          tr {
            td {
              box-sizing: border-box;
              padding: 20px;
              text-align: left;

              &:first-child {
                width: 33%;
              }
            }

            &:nth-child(even) {
              td {
                background-color: #f5f5f5;
              }
            }
          }
        }
      }

      hide {
        display: block;
        max-height: 0;
        overflow: hidden;
        @include transition(all, .5s);
      }
      &.active hide {
        max-height: 500px;
        @include transition(all, 1s);
      }
    }
    .read_more {
      margin-top: 10px;
      display: block;
      padding: 10px 0;
      @include overline;
      &:hover {
        span {
          opacity: .6;
        }
      }
      span, i {
        display: inline-block;
        vertical-align: middle;
      }
      i {
        margin: 0 5px;
        border: 1px solid $aqua1;
        color: $aqua1;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        position: relative;
        &:before {
          @include center_xy;
        }
      }
      &.active {
        i.fa-plus {
          &:before {
            content: '\f068';
          }
        }
      }
    }
    .button_promotion {
      display: inline-block;
      padding: 10px 60px;
      margin: 25px 0 0;
      border-radius: 30px;
      font-weight: 700;
      text-transform: uppercase;
      color: #002d42;
      background: #11ccc7;
      @include transition(all, .4s);
      &:hover {
        color: white;
      }
    }
  }
  .contact_info {
    margin-top: 30px;
    @include body1;
    font-weight: bold;
    text-transform: initial;
    a {
      color: $navy1;
      &.see_map {
        display: block;
        font-size: 12px;
        margin-bottom: 5px;
        text-transform: uppercase;
        i {
          color: $corporate_2;
          margin-right: 5px;
        }
      }
      &:hover {
        opacity: .6;
      }
    }
  }
  .hotel_info {
    .wave-navy-small {
      width: 200px;
      margin: 28px 0;
    }
  }
  .extra_info {
    position: relative;
    * {
      position: relative;
      z-index: 2;
    }
    svg {
      overflow: visible;
      position: absolute;
      top: 50%;
      left: 50%;
      z-index: 0;
      -webkit-transform: translate(-50%,-50%) scale(2.3);
      -moz-transform: translate(-50%,-50%) scale(2.3);
      -ms-transform: translate(-50%,-50%) scale(2.3);
      -o-transform: translate(-50%,-50%) scale(2.3);
      transform: translate(-50%,-50%) scale(2.3);
    }
    .title {
      margin-bottom: 15px;
    }
    .wave-navy-small {
      width: 200px;
      margin: 10px 0;
    }
    a {
      color: $corporate_1;
    }
    .hotel_social {
      padding: 10px 0;
      a {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        width: 30px;
        height: 30px;
        background: $navy1;
        border-radius: 50%;
        color: white;
        &:hover {
          opacity: .6;
        }
        i {
          @include center_xy;
        }
      }
    }
  }
}
.table_book {
  margin: 10px 0 30px;
  border-radius: 20px;
  span {
    display: inline-block;
    padding: 5px 15px;
  }
}
.map_wrapper, .book_table_wrapper {
  display: none;
  @include full_size;
  position: fixed;
  z-index: 1005;
  background: rgba($corporate_1,.9);
  .close_map, .close_book_table {
    position: absolute;
    top:20px;
    right: 20px;
    font-size: 40px;
    color: white;
    cursor: pointer;
  }
  .map, .book_table {
    @include center_xy;
    background: white;
    width: 820px;
    max-width: 80vw;
    max-height: 80vh;
    border-radius: 10px;
    iframe {
      width:100%;
      vertical-align:middle;
      border-radius: 10px;
    }
    .contact_form_wrapper {
      padding: 30px;
    }
  }
}
.icon_list {
  li {
    display: inline-block;
    margin-right: 94px;
    white-space: nowrap;
    @include body1;
    font-size: 14px;
    padding: 5px 0;
    &:nth-child(4n) {
      margin-right: 0;
    }
    a {
      display: inline-block;
      vertical-align: middle;
      &:hover {
        text-decoration:underline;
      }
    }
    span {
      display: inline-block;
      vertical-align: middle;
    }
    i {
      position: relative;
      vertical-align: middle;
      font-size: 20px;
      margin-right: 10px;
      width: 40px;
      height: 40px;
      text-align: center;
      &:before {
        @include center_xy;
        z-index: 2;
      }
      &:after {
        content: '';
        @include full_size;
        background: $corporate_2;
        border-radius: 50%;
      }
    }
  }
}
.icon_ticks {
  padding: 15px 0;
  .tick {
    display: inline-block;
    vertical-align: middle;
    padding-right:20px;
    i, span {
      display: inline-block;
      vertical-align: middle;
    }
    i {
      color: $corporate_2;
      font-size: 30px;
    }
    span {
      text-transform: uppercase;
      font-size: 14px;
    }
  }
}

.hotel_info_menu {
  position: sticky;
  top: 50px;
  z-index:100;
  background: $aqua4;
  text-align: center;
  margin-top: 20px;
  display: flex;
  a {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    text-transform: uppercase;
    font-weight: bold;
    font-size:12px;
    padding: 10px 0;
    color: $navy1;
    &.active {
      background: $aqua1;
    }
    &:last-of-type {
      &:after {
        display: none;
      }
    }
    &:after {
      content: '';
      width: 1px;
      background: $navy3;
      position: absolute;
      top: 7px;
      bottom: 7px;
      right: 0;
    }
  }
}
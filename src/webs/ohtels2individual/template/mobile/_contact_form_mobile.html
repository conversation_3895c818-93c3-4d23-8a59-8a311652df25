<div id="contactContent">

{% if not no_form %}

    <form name="contact" id="contact" method="post" action="/utils/?action=contact">

        <input type="hidden" name="destination_email" id="destination_email" value=""/>

        <div class="info">

            <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>

            <div class="contInput effects_sass" sass_effect="slide_up_effect">
                <label for="name" class="title">{{ T_nombre_y_apellidos }}</label>
                <input type="text" id="name" name="name" class="bordeInput" value=""/>
            </div>

            <div class="contInput contTextarea effects_sass" sass_effect="slide_up_effect">
                <label for="comments" class="title">{{ T_comentarios }}</label>
                <textarea type="text" id="comments" name="comments" class="bordeInput" value=""></textarea>
            </div>

            <div class="contInput effects_sass" sass_effect="slide_up_effect">
                <label for="email" class="title">{{ T_email }}</label>
                <input type="text" id="email" name="email" class="bordeInput" value=""/>
            </div>

            <div class="contInput effects_sass" sass_effect="slide_up_effect">
                <label for="email" class="title">{{ T_confirm_email }}</label>
                <input type="text" id="emailConfirmation" name="emailConfirmation" class="bordeInput" value=""/>
            </div>

            <div class="contInput effects_sass" sass_effect="slide_up_effect">
                <label for="telephone" class="title">{{ T_telefono }}</label>
                <input type="text" id="telephone" name="telephone" class="bordeInput" value=""/>
            </div>

            <script src='https://www.google.com/recaptcha/api.js'></script>
            <div class="g-recaptcha" data-sitekey="{{ public_recaptcha }}"></div>

            <div class="checkbox_wrapper effects_sass" sass_effect="slide_up_effect">

                <div class="contInput">
                    <input name="privacity" id="privacity" type="checkbox">
                    <label><a data-fancybox data-options='{"caption" : "{{T_declaro_condiciones}}", "src" : "/mobile/{{language}}/?sectionContent=politica-de-privacidad.html", "type" : "iframe", "width" : "100%", "max-width" : "100%"}' data-width="1200" href="/mobile/{{language}}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{T_declaro_condiciones}}</a></label>
                </div>

                <div class="contInput">
                    <input name="hasreservation" id="hasreservation" type="checkbox">
                    <label>{{ T_tienes_reserva }}</label>
                </div>

                <div id="contact-button-wrapper">
                    <a>
                        <div id="contact-button">
                            {{ T_enviar }}
                        </div>
                    </a>
                </div>

            </div>




            <br clear="all">
        </div>
    </form>

    <script type="text/javascript">

    $(document).ready(function () {
        $.getScript("/static_1/lib/jquery.validate.js");
    });
    $(window).load(function () {


        $("#hotelSelect").change(function(){

            var email_selected = $("#hotelSelect option:selected").attr("data-mail");
            $("#destination_email").val(email_selected);

           if (email_selected == "None"){
                email_selected = $("#hotelSelect option:selected").attr("data-mail");
                $("#destination_email").val(email_selected);
            }
            else{
                email_selected = $("#hotelSelect option:selected").attr("data-mail");
                $("#destination_email").val(email_selected);
            }
        })



        jQuery.validator.addMethod("phone", function (phone_number, element) {
            phone_number = phone_number.replace(/\s+/g, "");
            return this.optional(element) || phone_number.length > 7 && phone_number.length < 13 &&
                    phone_number.match(/^[0-9 \+]\d+$/);
        }, "Please specify a valid phone number");


        $("#contact").validate({
            rules: {
                name: "required",
                email: {
                    required: true,
                    email: true
                },
                emailConfirmation: {
                    required: true,
                    equalTo: "#email",
                    email: true
                },
                telephone: {
                    required: true,
                    phone: true
                },
                comments: "required",
                privacity: "required"

            },
            messages: {
                name: "{{ T_campo_obligatorio}}",
                email: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    email: "{{ T_campo_valor_invalido|safe }}"
                },
                emailConfirmation: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    email: "{{ T_campo_valor_invalido|safe }}",
                    equalTo: "{{T_not_confirmed_email_warning|safe}}"
                },
                telephone: {
                    required: "{{ T_campo_obligatorio|safe }}",
                    phone: "{{ T_campo_valor_invalido|safe }}"
                },
                comments: "{{ T_campo_obligatorio|safe }}"
            }

        });

        $("#contact-button").click(function () {


            if ($("#contact").valid()) {
                if ($("#g-recaptcha-response").val()) {
                    $.post(
                            "/utils/?action=contact",
                            {
                                'name': $("#name").val(),
                                'email': $("#email").val(),
                                'telephone': $("#telephone").val(),
                                'hotel': $("#hotelSelect").val(),
                                'comments': $("#comments").val(),
                                'hasreservation': $("#hasreservation").is(':checked'),
                                'privacity': $("#privacity").val(),
                                'g-recaptcha-response': $("#g-recaptcha-response").val(),
                                'destination_email':  $("#destination_email").val()
                            },

                            function (data) {
                                alert("{{ T_gracias_contacto }}");
                                $("#name").val("");
                                $("#telephone").val("");
                                $("#email").val("");
                                $("#emailConfirmation").val("");
                                $("#comments").val("");
                            }
                    );
                }else {
                    $(".g-recaptcha > div > div").css('border', '1px solid red');
                }
            }
        })
    });


</script>

{% endif %}


{% if poi_hoteles and google_maps_api %}
<!--# Custom JS map-->
<div class="all_hotels_map_wrapper">
    <div id="map" style="height: 600px;width: 100%;"></div>
</div>
<script>
    var styles = {
        default: null,
        Ohtels: [
                {
                    "featureType": "administrative",
                    "elementType": "labels.text.fill",
                    "stylers": [
                        {
                            "color": "#444444"
                        }
                    ]
                },
                {
                    "featureType": "landscape",
                    "elementType": "all",
                    "stylers": [
                        {
                            "color": "#f2f2f2"
                        }
                    ]
                },
                {
                    "featureType": "poi",
                    "elementType": "all",
                    "stylers": [
                        {
                            "visibility": "off"
                        }
                    ]
                },
                {
                    "featureType": "road",
                    "elementType": "all",
                    "stylers": [
                        {
                            "saturation": -100
                        },
                        {
                            "lightness": 45
                        }
                    ]
                },
                {
                    "featureType": "road.highway",
                    "elementType": "all",
                    "stylers": [
                        {
                            "visibility": "simplified"
                        }
                    ]
                },
                {
                    "featureType": "road.arterial",
                    "elementType": "labels.icon",
                    "stylers": [
                        {
                            "visibility": "off"
                        }
                    ]
                },
                {
                    "featureType": "transit",
                    "elementType": "all",
                    "stylers": [
                        {
                            "visibility": "off"
                        }
                    ]
                },
                {
                    "featureType": "water",
                    "elementType": "all",
                    "stylers": [
                        {
                            "color": "#46bcec"
                        },
                        {
                            "visibility": "on"
                        }
                    ]
                },
                {
                    "featureType": "water",
                    "elementType": "geometry.fill",
                    "stylers": [
                        {
                            "color": "#102f57"
                        }
                    ]
                }
            ]
      };

  var hotels = [];
  var locations = [];

  {% for poi in poi_hoteles %}
  {% if poi.lat and poi.lng %}
      hotels.push("{{poi.poi_description|safe}} <a href='javaScript:booking_click(\"{{poi.namespace|safe}}\")'>{{T_reservar}}</a>");
      locations.push({lat: {{poi.lat|safe}}, lng: {{poi.lng|safe}}});
  {% endif %}
  {% endfor %}

  function initMap() {

        var map = new google.maps.Map(document.getElementById('map'), {
          zoom: 4,
          center: {lat: 35.437, lng: -3.819},
          styles: styles['Ohtels']
        });

        var markers = locations.map(function(location, i) {
          var marker = new google.maps.Marker({
            position: location,
            animation: google.maps.Animation.DROP,
            icon: '/img/{{ base_web }}/maps/marker-maps.png'
          });
            var infowindow = new google.maps.InfoWindow({
              content: hotels[i]
            });

            marker.addListener('click', function() {
              infowindow.open(marker.get('map'), marker);
            });
          return marker;
        });
        var markerCluster = new MarkerClusterer(map, markers,
            {imagePath: 'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m'});

  }

</script>

<script src="/js/{{base_web}}/markerclusterer.js"></script>
<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyBLyjPPuIPYHk-WSQlNlmMMhCpdlkSO7DY&callback=initMap" async defer></script>

{% else %}
    <div class="all_hotels_map_wrapper">
        {{ iframe_google_maps.content|safe }}
    </div>
{% endif %}

</div>
<script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/functions_mobile.js?v=1"></script>
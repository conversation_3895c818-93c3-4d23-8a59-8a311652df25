# -*- coding: utf-8 -*-
import datetime, os

from flask import request

from booking_process.utils.booking.normalizationUtils import normalizeForClassName
from booking_process.utils.country import ALL_COUNTRIES_LIST
from booking_process.constants.advance_configs_names import PUBLIC_CAPTCHA_KEY, FACEBOOK_PIXEL_ID, MODIFY_RESERVATION_POPUP, \
	MODIFY_RESERVATION
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import build_friendly_url
from booking_process.utils.data_management.pictures_utils import getPicturesForKey, get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, \
	get_section_from_section_spanish_name_with_properties, get_sections_from_type
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.language.language_utils import get_language_title, get_web_dictionary, get_language_code
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.namespaces.namespace_utils import get_namespace
from booking_process.utils.session import session_manager
from booking_process.utils.templates.template_utils import buildTemplate
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
from collections import OrderedDict

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb
TEMPLATE_NAME = os.path.dirname(__file__).split("/")[-1]

#Change this value too in default.scss and in config.rb!!
base_web = "ohtel"

class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)


		result_params_dict = {'base_web': base_web,
							  'aviso_legal': get_section_from_section_spanish_name("aviso legal", language),
							  'language_selected': get_language_title(language),
							  'booking_engine_2': self.buildSearchEngine2(language),
							  'python_now': datetime.datetime.now(),
							  'country_list': ALL_COUNTRIES_LIST.items(),
							  'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False}),
		                      'footer_columns': get_pictures_from_section_name("_footer columns", language),
							  'public_recaptcha': get_config_property_value(PUBLIC_CAPTCHA_KEY),
							  'facebook_pixel_id': get_config_property_value(FACEBOOK_PIXEL_ID),
		                      'newsletter': self.buildNewsletter2(language, check_newsletter=True),
							  'extra_top_header': self.getPicturesProperties(language, 'extra_top_header')
							  }

		if user_agent_is_mobile():
			extra_top_header = self.getPicturesProperties(language, 'extra_top_header')
			if extra_top_header:
				result_params_dict['extra_code_mobile'] = self.getMobileHeaderBanner(extra_top_header)

		# if user_agent_is_mobile():
		# 	result_params_dict['extra_code_mobile'] = "<script async type='text/javascript' type='text/javascript' src='/js/ohte2/functions_mobile.js'></script>"

		if sectionToUse.get('subtitle'):
			result_params_dict['content_subtitle'] = sectionToUse


		#Automatic Content
		automatic_content = {
		# 'Galeria de Imagenes': True,
		'Mis Reservas': True,
		'Extra 1': True
		}
		if automatic_content.get(section_type):
			result_params_dict['content_access'] = True

		checkin_online = get_section_from_section_spanish_name_with_properties('_checkin_online', language)
		if checkin_online:
			result_params_dict['checkin_online'] = checkin_online
			result_params_dict['checkin_online_pics'] = get_pictures_from_section_name('_checkin_online',language)

		if user_agent_is_mobile():
			checkin_online = get_section_from_section_spanish_name_with_properties('_checkin_online', language)
			if checkin_online:
				checkin_online_pics = get_pictures_from_section_name('_checkin_online', language)
				result_dict = {
					'checkin_online': checkin_online,
					'checkin_online_pics': checkin_online_pics,
					'mobile_true': True
				}
				result_dict.update(get_web_dictionary(language))
				result_params_dict['extra_code_mobile'] = self.buildTemplate_2("_floating_precheckin.html", result_dict, False, 'ohtels2individual')

		if section_type == "Inicio":
			result_params_dict['home'] = True
			result_params_dict['offers_section'] = get_sections_from_type('Ofertas', language)
			if result_params_dict.get('offers_section'):
				offers_pictures = get_pictures_from_section_name(result_params_dict['offers_section'][0].get("sectionName"), language)
				result_params_dict['offers_paralax'] = map(lambda m: m.get("servingUrl"), filter(lambda l: l.get("title") == "parallax", offers_pictures))
			result_params_dict['offers_elements'] = self.buildPromotionsInfo(language)

		if advance_properties.get("section") == "club":
			result_params_dict['section_club'] = True
			result_params_dict['response_club'] = get_section_from_section_spanish_name('_response alta club', language)
			section_picture = get_pictures_from_section_name(section_name, language)
			result_params_dict['logo_header'] = list(filter(lambda p: p.get('title') == "logo_header", section_picture))


		if advance_properties.get('custom_contact_form'):
			result_params_dict['contact_form_custom'] = build_context_contact_form(advance_properties['custom_contact_form'], language)
			result_params_dict['contact_form_custom_section'] = get_section_from_section_spanish_name(advance_properties['custom_contact_form'], language)

		if section_type == 'Form Promo Empresa':
			result_params_dict['jobs_promotions'] = self.getPicturesProperties(language, "work_blocks", ['type'])
			result_params_dict['department'] = request.values.get('department')

		if section_type == 'Oferta Individual':
			self._build_individual_offer(result_params_dict, sectionToUse, language, allSections)
			offers_section = get_sections_from_type('Ofertas', language)
			if offers_section:
				result_params_dict['offers_parent'] = offers_section[0]
			result_params_dict['content_subtitle']['hide_subtitle'] = True
			if advance_properties.get("slider_message"):
				result_params_dict['slider_title'] = advance_properties.get("slider_message")

		if section_type == "Ofertas":
			result_params_dict['content_subtitle'] = None
			if advance_properties.get("slider_message"):
				result_params_dict['slider_title'] = advance_properties.get("slider_message")
			result_params_dict['slider_contet'] = sectionToUse.get('content')

			result_params_dict['offers'] = self.getOffers(language)


		if section_type == "Mis reservas":
			result_params_dict['modify_reservation'] = get_config_property_value(MODIFY_RESERVATION)

		if section_type == u'Localización':
			result_params_dict['contact_access'] = True
			result_params_dict['no_form'] = True
			result_params_dict['iframe_google_maps'] = get_section_from_section_spanish_name('Iframe google maps', language)

		if section_type == 'Habitaciones':
			result_params_dict['rooms_options'] = get_pictures_from_section_name('_habitaciones_blocks', language)
			self.build_room_extras(language, result_params_dict['rooms_options'], allSections)
			result_params_dict['offers_section'] = get_sections_from_type('Ofertas', language)
			if result_params_dict.get('offers_section'):
				offers_pictures = get_pictures_from_section_name(result_params_dict['offers_section'][0].get("sectionName"), language)
				result_params_dict['offers_paralax'] = map(lambda m: m.get("servingUrl"), filter(lambda l: l.get("title") == "parallax", offers_pictures))
			result_params_dict['offers_elements'] = self.buildPromotionsInfo(language)
			if advance_properties.get("slider_message"):
				result_params_dict['slider_title'] = advance_properties.get("slider_message")

		if section_name == 'apartamentos':
			result_params_dict['rooms_options'] = get_pictures_from_section_name('_apartamentos_blocks', language)
			self.build_room_extras(language, result_params_dict['rooms_options'], allSections)
			result_params_dict['offers_section'] = get_sections_from_type('Ofertas', language)
			if result_params_dict.get('offers_section'):
				offers_pictures = get_pictures_from_section_name(result_params_dict['offers_section'][0].get("sectionName"), language)
				result_params_dict['offers_paralax'] = map(lambda m: m.get("servingUrl"), filter(lambda l: l.get("title") == "parallax", offers_pictures))
			result_params_dict['offers_elements'] = self.buildPromotionsInfo(language)
			if advance_properties.get("slider_message"):
				result_params_dict['slider_title'] = advance_properties.get("slider_message")
			if advance_properties.get("footer_2"):
				result_params_dict['footer_2'] = advance_properties.get("footer_2")

		if section_type == u'Habitación Individual':
			room_pictures = get_pictures_from_section_name(sectionToUse.get("sectionName"), language)
			result_params_dict['pictures'] = list(filter(lambda l: l.get("title") == "slider", room_pictures))
			result_params_dict['individual_room'] = True
			result_params_dict['offers_section'] = get_sections_from_type('Ofertas', language)
			result_params_dict['offers_elements'] = self.buildPromotionsInfo(language)
			mini_dict = {'minigallery': list(filter(lambda l: not l.get("title"), room_pictures)),'num_items': 5,'margin': 2, 'navigation' : "false"}
			minigallery_html = self.buildTemplate_2("banners/_minigalleryx.html", mini_dict, False)
			result_params_dict["minigallery"] = minigallery_html

			room_services_list = self.getPicturesProperties(language, '_rooms_services_desktop', ['awesome_icon', 'color'])
			advance_room = self.getSectionAdvanceProperties(get_section_from_section_spanish_name(sectionToUse.get("sectionName"), language), language)
			if advance_room.get('website_services'):
				matched_list = []
				match_services =  advance_room['website_services'].split(";")
				for room_service_element in room_services_list:
					if room_service_element['title'] in match_services:
						matched_list.append(room_service_element)

				result_params_dict['room_services'] = {'section_info': get_section_from_section_spanish_name('_rooms_services_desktop', language), 'section_pictures': matched_list}

		elif section_type == "Galeria de Imagenes":
			result_params_dict['gallery_section'] = self.getGalleryPictures(language)

		result_params_dict = self.getExtraBanners(advance_properties, result_params_dict, language)

		return result_params_dict

	def build_room_extras(self, language, rooms_elements, all_sections=[]):
		language_dict = get_web_dictionary(language)
		default_room_parent = build_friendly_url(language_dict['T_habitacion'])
		if default_room_parent:
			default_room_parent = default_room_parent.replace(".html", "/")
		language_code_url = "/" + get_language_code(language) + "/"

		for room_element in rooms_elements:
			if not room_element.get('linkUrl'):
				continue
			room_url = build_friendly_url(room_element['title'])
			room_element['friendlyUrl'] = language_code_url + default_room_parent + room_url
			for s in all_sections:
				if s.get("friendlyUrlInternational") == room_element.get("linkUrl"):
					pictures_room = get_pictures_from_section_name(s.get("sectionName"), language)
					room_element['pictures'] = list(filter(lambda l: not l.get("title") or ("ico" not in l.get("title") and "slider" not in l.get("title")), pictures_room))
					break
			#room_advance_properties = self.getSectionAdvanceProperties(room_element, language)

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "650"

	def get_revolution_full_screen(self):
		return "on"

	def get_revolution_initializer(self):
		return True

	def getExtraBanners(self, advance_properties, result_params, language):

		checkin_online = get_section_from_section_spanish_name_with_properties('_checkin_online', language)
		if checkin_online:
			result_params['checkin_online'] = checkin_online
			result_params['checkin_online_pics'] = get_pictures_from_section_name('_checkin_online',language)

		if advance_properties.get("minigallery"):
			minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery"), language)
			mini_dict = {'minigallery': minigallery_images,'num_items': 5,'margin': 2}
			minigallery_html = self.buildTemplate_2("banners/_minigalleryx.html", mini_dict, False)
			result_params["minigallery"] = minigallery_html

		if advance_properties.get("icos_bottom"):
			result_params["icos_bottom_services"] = {'section_info': get_section_from_section_spanish_name(advance_properties['icos_bottom'], language),
			                                         'section_pictures': get_pictures_from_section_name(advance_properties['icos_bottom'], language)}

			result_params['icos_bottom_services']['section_pictures'] = map(lambda x: dict(list(x.items()) + list(self.getSectionAdvanceProperties(x, language).items())), result_params['icos_bottom_services']['section_pictures'])

		if advance_properties.get("work_form"):
			result_params['cv_form'] = True
		if advance_properties.get("bannerx3"):
			result_params["bannerx3"] = get_pictures_from_section_name(advance_properties.get("bannerx3"), language)
		if advance_properties.get("banner_focus"):
			result_params["banner_focus"] = self.getPicturesProperties(language, advance_properties.get("banner_focus"), [''])
			result_params["banner_focus_section"] = get_section_from_section_spanish_name(advance_properties.get("banner_focus"), language)
			for banner in result_params["banner_focus"]:
				banner['class_name'] = normalizeForClassName(banner['description'])
		if advance_properties.get("banner_card"):
			result_params["banner_card"] = get_pictures_from_section_name(advance_properties.get("banner_card"), language)
		if advance_properties.get("banner_5pic"):
			result_params["banner_5pic"] = get_pictures_from_section_name(advance_properties.get("banner_5pic"), language)
			result_params["banner_5pic_section"] = get_section_from_section_spanish_name(advance_properties.get("banner_5pic"), language)
		if advance_properties.get('banner_mosaic', False):
			result_params['banner_mosaic'] = get_section_from_section_spanish_name(advance_properties.get('banner_mosaic'), language)
			banner_mosaic_images = self.getPicturesProperties(language, advance_properties.get("banner_mosaic"), ['title_seo', 'description_seo'])
			result_params['banner_mosaic_images'] = list(filter(lambda x: not x.get('title') in ['pattern', 'background'], banner_mosaic_images))
			for image_in_block_mosaic in result_params['banner_mosaic_images']:
				if image_in_block_mosaic.get('linkUrl'):
					result_params['banner_mosaic_link'] = image_in_block_mosaic.get('linkUrl')
				if image_in_block_mosaic.get('title') == 'newsletter':
					result_params['banner_mosaic_newsletter'] = get_pictures_from_section_name(image_in_block_mosaic.get('description'), language)


		if advance_properties.get("destiny_blocks"):
			result_params['destinos'] = self.getPicturesProperties(language, advance_properties["destiny_blocks"], ['video'])
			for destiny in result_params['destinos']:
				destiny['classname'] = normalizeForClassName(destiny.get('title',''))

		result_params["banner_club"] = get_pictures_from_section_name("_banner_club", language)

		return result_params

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		if not user_agent_is_mobile():
			return self.buildTemplate('booking/booking_engine_7/_booking_widget.html', params, allowMobile=False)
		else:
			return super(TemplateHandler, self).buildSearchEngine(language, selectOptions)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['horizontal_nolabel'] = True
		options['departure_date_select'] = True
		options['custom_new_title'] = get_section_from_section_spanish_name('saber mas', language).get('content', '')

		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		return options

	def _get_promotions_info(self, all_promotions, language):
		for promotion in all_promotions:
			promotion_pictures = getPicturesForKey(language, str(promotion.get('offerKey')), [])
			if promotion_pictures:
				picture_info = self.getSectionAdvanceProperties(promotion_pictures[0], language)
				if picture_info.get('hotels'):
					promotion['hotels_availables'] = picture_info.get('hotels', '').split(";")

				if picture_info.get('filter'):
					promotion['promotion_filters'] = picture_info.get('filter', '').split("@")

		return all_promotions

	def getGalleryPictures(self, language):
		filters = OrderedDict()
		picture_gallery = self.get_hotel_gallery(language)
		for img in picture_gallery:
			if img.get("title") == "slider":
				continue
			nameFilter = normalizeForClassName(img.get("title"))
			filters.setdefault(nameFilter, []).append(img)

		return filters

	def getCurrenSection(self, sections=None):
		#TODO: Possible hole for performance copied from bestcorp
		current_section = super(TemplateHandler, self).getCurrenSection(sections)

		if not current_section:
			path = self.getPathComponents()
			language = session_manager.get('language')
			# promotions = self.buildPromotionsInfo(language)
			# for promotion_element in promotions:
			# 	promotion_url = self.build_friendly_url(promotion_element.get('name'))
			# 	if promotion_url in path:
			# 		promotion_element['sectionName'] = promotion_element.get('name')
			# 		promotion_element['subtitle'] = promotion_element.get('name')
			# 		promotion_element['content'] = promotion_element.get('description')
			# 		promotion_element['sectionType'] = 'Oferta Individual'
			# 		promotion_element['friendlyUrl'] = self.build_friendly_url(promotion_element.get('name'))
			# 		return promotion_element

			rooms_list = get_pictures_from_section_name('_habitaciones_blocks', language)
			self.build_room_extras(language, rooms_list)
			if path[-1] and path[-1].endswith('.html'):
				match_room = list(filter(lambda x: x.get('friendlyUrl') and path[-1] and path[-1] in x.get('friendlyUrl'), rooms_list))
			else:
				match_room = []

			if match_room:
				target_room = match_room[0]
				target_room['sectionName'] = target_room.get('title')
				target_room['subtitle'] = target_room.get('title')
				target_room['content'] = target_room.get('description')
				target_room['sectionType'] = u'Habitación Individual'
				target_room['pictures'] = target_room.get('pictures')
				target_room['key'] = str(target_room.get('key'))
				return target_room

		return current_section

	def getParamsForSection(self, section, language):
		result = {}

		if section['sectionType'].lower() == 'mis reservas':
			result['modify_reservation'] = get_config_property_value(MODIFY_RESERVATION_POPUP)
			result['modify_reservation_content'] = get_section_from_section_spanish_name("_popup modificacion reserva", language)


		section_type = section.get('sectionType')

		if user_agent_is_mobile():
			if section['sectionType'] == 'Galeria de Imagenes':
				result['images'] = OrderedDict()
				gallery_pictures = self.get_hotel_gallery(language)
				filtered_filters = list(filter(lambda x: x.get('title') and not x.get('title') in ['ico', 'slider'] and not 'youtube' in [x.get('description', '')], gallery_pictures))
				all_filter_together = map(lambda x: x.get('title'), filtered_filters)
				all_available_filters = []

				for filter_element in all_filter_together:
					if not filter_element in all_available_filters:
						all_available_filters.append(filter_element)

				result['filters'] = all_available_filters

				for picture_element in gallery_pictures:
					if picture_element.get('linkUrl'):
						picture_element['video'] = picture_element['linkUrl']

				gallery_pictures_filtered = list(filter(lambda x: x.get('title') != 'slider', gallery_pictures))
				result['images'] = {'images_blocks': gallery_pictures_filtered}

		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
		currentSection = self.getSectionParams(sectionFriendlyUrl, language)
		advance_properties = self.getSectionAdvanceProperties(currentSection, language)
		language_dict = get_web_dictionary(language)
		additionalParams['custom_elements'] = ''
		section_name = ''
		section_type = ''
		result_dict = {'base_web': base_web}

		if currentSection:
			section_name = currentSection['sectionName'].lower().strip()
			section_type = currentSection['sectionType']

		if user_agent_is_mobile():
			room_info_section = False
			if section_type == 'Habitaciones' and get_namespace() == 'ohtels-belvedere':
				room_info_section = "_habitaciones_blocks"

			if section_name == 'apartamentos':
				room_info_section = "_apartamentos_blocks"

			if room_info_section:
				result = {
					'rooms_information': get_pictures_from_section_name(room_info_section, language)
				}
				for room in result['rooms_information']:
					room['linkUrl'] = ""
				language_dict = get_web_dictionary(language)
				result.update(language_dict)
				return self.buildTemplate_2('/mobile_templates/2/_rooms.html', result, False)


			if advance_properties.get("banners_cycle"):
				cycle_dict = dict(get_web_dictionary(language))
				cycle_dict['cycle_banners_mobile'] = get_pictures_from_section_name(advance_properties.get("banners_cycle"), language)
				cycle_html = self.buildTemplate_2("mobile_templates/2/_cycle_banners_v1.html", cycle_dict, False)
				additionalParams['custom_elements'] += cycle_html

			if advance_properties.get("custom_contact_form"):
				args = {
					'contact_form_custom': build_context_contact_form(advance_properties['custom_contact_form'], language),
					'contact_form_custom_section': get_section_from_section_spanish_name(advance_properties.get("custom_contact_form"), language),
					'public_recaptcha': get_config_property_value(PUBLIC_CAPTCHA_KEY),
				}
				result = dict(list(args.items()) + list(get_web_dictionary(language).items()))
				contact_form_html = self.buildTemplate_2("banners/_contact_form.html", result, False, TEMPLATE_NAME)
				additionalParams['custom_elements'] += contact_form_html

			if advance_properties.get("minigallery"):
				mini_dict = {
					'minigallery_mobile': get_section_from_section_spanish_name(advance_properties.get("minigallery"), language)
				}
				mini_html = self.buildTemplate_2("mobile_templates/2/_minigallery_v1.html", mini_dict, False)
				additionalParams['custom_elements'] += mini_html

			if advance_properties.get("destiny_blocks"):
				cycle_dict = dict(get_web_dictionary(language))
				cycle_dict['cycle_banners_mobile'] = get_pictures_from_section_name(advance_properties.get("destiny_blocks"), language)
				cycle_html = self.buildTemplate_2("mobile_templates/2/_cycle_banners_v1.html", cycle_dict, False)
				additionalParams['custom_elements'] += cycle_html

			if section_type == u'Localización':
				result_dict['contact_access'] = True
				result_dict['no_form'] = True
				result_dict['content_subtitle'] = None
				if advance_properties.get("slider_message"):
					result_dict['slider_title'] = advance_properties.get("slider_message")
				result_dict['slider_contet'] = currentSection.get('content')
				result_dict['iframe_google_maps'] = get_section_from_section_spanish_name('Iframe google maps', language)
				result_dict['public_recaptcha'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)
				result = dict(list(result_dict.items()) + list(get_web_dictionary(language).items()))
				return self.buildTemplate_2("/mobile/_contact_form_mobile.html", result, False, 'ohtels2individual')

			if advance_properties.get("work_form"):
				result_dict['content_subtitle'] = currentSection
				result_dict['cv_form'] = True
				result = dict(list(result_dict.items()) + list(get_web_dictionary(language).items()))
				return self.buildTemplate_2("/mobile/_main_content_mobile.html", result, False, 'ohtels2individual')

			if advance_properties.get("section") == "club":
				result_dict["bannerx3"] = get_pictures_from_section_name(advance_properties.get("bannerx3"),language)
				result_dict['section_club'] = True
				result_dict['response_club'] = get_section_from_section_spanish_name('_response alta club', language)
				section_picture = get_pictures_from_section_name(section_name, language)
				result_dict['logo_header'] = filter(lambda p: p.get('title') == "logo_header", section_picture)
				result_dict['content_subtitle'] = currentSection
				result_dict['python_now'] = datetime.datetime.now()
				result = dict(list(result_dict.items()) + list(get_web_dictionary(language).items()))
				return self.buildTemplate_2("/mobile/_main_content_mobile.html", result, False, 'ohtels2individual')

			# checkin_online = get_section_from_section_spanish_name_with_properties('_checkin_online', language)
			# if checkin_online:
			# 	checkin_online_pics = get_pictures_from_section_name('_checkin_online', language)
			# 	result_dict = {
			# 		'checkin_online': checkin_online,
			# 		'checkin_online_pics': checkin_online_pics,
			# 		'mobile_true': True
			# 	}
			# 	result = dict(list(result_dict.items()) + list(get_web_dictionary(language).items()))
			# 	additionalParams['custom_elements'] += self.buildTemplate_2("_floating_precheckin.html", result, False, 'ohtels2individual')

			if section_type == 'Form Promo Empresa':
				result_dict['jobs_promotions'] = self.getPicturesProperties(language, "work_blocks", ['type'])
				result_dict['department'] = request.values.get('department')
				result = dict(list(result_dict.items()) + list(get_web_dictionary(language).items()))
				return self.buildTemplate_2("/mobile/_main_content_mobile.html", result, False, 'ohtels2individual')

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)

	def getOffers(self, language):
		offers = self.buildPromotionsInfo(language)
		for x in offers:
			pictures_offers = getPicturesForKey(language, str(x.get("offerKey")), [])
			if pictures_offers:
				x['linkUrl'] = pictures_offers[0].get("linkUrl")
		return offers

	def getMobileHeaderBanner(self, top_header):
		base_path = os.path.dirname(__file__)
		mobile_template_params = {
			'extra_top_header': top_header
		}
		fullPath_extra_header = os.path.join(base_path, 'template/mobile/_extra_top_header.html')
		mobile_template = buildTemplate(fullPath_extra_header, mobile_template_params)
		return mobile_template


def build_context_contact_form(target_section, language):
	section_info = get_section_from_section_spanish_name(target_section, language)
	section_pictures = getPicturesForKey(language, section_info.get('key'), [])
	for picture in section_pictures:
		extra_properties = get_properties_for_entity(picture.get('key', False), language)
		picture.update(extra_properties)

	return section_pictures
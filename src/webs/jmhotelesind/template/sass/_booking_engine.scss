.datepicker_wrapper_element,
.datepicker_wrapper_element_2,
.datepicker_wrapper_element_3 {
  box-shadow: 0 5px 10px rgba(black, .3);
  border-radius: 0;
  .header_datepicker {
    background-color: $corporate_1 !important;
    margin: 0;
    .close_button_datepicker {
      top: 9px;
      background: white;
      color: black;
    }
    .specific_date_selector {
      text-transform: uppercase;
      &:before {
        display: none;
      }
    }
  }
  .datepicker_ext_inf_sd,
  .datepicker_ext_inf_ed {
    padding: 0;

    table.ui-datepicker-calendar {
      margin: 0 15px;
      width: calc(100% - 30px);
      th span {
        font-weight: bold;
        color: $corporate_2;
      }
      td {
        border-width: 0;
        background: transparent !important;
        height: 40px;
        padding: 5px 0;
        .ui-state-default {
          line-height: 30px;
          height: 30px;
          width: 30px;
          border-radius: 50%;
        }
        .ui-state-active {
          background: $corporate_2 !important;
        }
        .ui-state-hover {
          background: $corporate_1;
        }
        &.ui-datepicker-start_date, &.highlight, &.ui-datepicker-highlighted {
          .ui-state-default {
            background: $corporate_2 !important;
            color:white !important;
          }
        }
        &.ui-datepicker-start_date {
          .ui-state-default:before {
            display: none;
          }
        }
      }
    }
  }
  .ui-datepicker-header.ui-widget-header {
    border-bottom: 1px solid $black;
    .ui-datepicker-prev, .ui-datepicker-next {
      background: transparent;
      .ui-icon {
        background: transparent;
        color: transparent;
        position: relative;
        @extend .fa-arrow-down;
        &:before {
          @extend .fal;
          color: black;
          font-size: 18px;
          @include center_xy;
          top: 45%;
        }
      }
      &:hover, .ui-state-hover {
        background: transparent !important;
        .ui-icon:before {
          color: $corporate_2;
        }
      }
    }
  }
  .specific_month_selector {
    margin: 0;
    background: transparent;
    color: $corporate_2;
    strong {
      color: black;
    }
  }
}
#full_wrapper_booking {
  position: absolute;
  padding: 20px 0;
  width: 100%;
  min-width: 1140px;
  z-index: 1000;
  bottom: 30px;
  display: none;
  @include transition(background, .6s);

  .hotel_selector {
    position: absolute;
    background: white;
    box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
    width: 500px;
    z-index: 4;
    box-sizing: border-box;
    top: 89px;
    .hotel_selector_filter {
      width: 150px;
      display: inline-block;
      vertical-align: top;
      padding: 10px 0;
      border-right: 1px solid lightgrey;
      a {
        display: block;
        color: black;
        padding: 5px 10px;
        font-size: 14px;
        &:hover {
          color: $corporate_1;
        }
        &.selected {
          color: black;
        }
      }
      .destinies {
        border-top: 1px solid lightgrey;
        padding-top: 7px;
        a {
          color: #4b4b4b;
          &:hover {
            color: $corporate_1;
          }
          &.selected {
            color: black;
          }
        }
      }
    }
    .hotel_selector_inner {
      position: absolute;
      top: 0;
      right: 0;
      left: 150px;
      bottom: 0;
      width: 350px;
      height: 100%;
      overflow: auto;
      display: inline-block;
      vertical-align: top;
    }
    .title_group {
      display: block;
      color: black;
      padding: 13px 10px 3px;
    }
    ul {
      li {
        display: block;
        padding: 5px 10px;
        color: #999;
        cursor: pointer;
        font-size: 14px;
        font-weight: 400;
        @include transition(all, .4s);
        .title_selector {
          color: #4b4b4b;
        }
        &.hotel_selector_option {
          padding-left: 20px;
        }
        &:hover .title_selector {
          color: $corporate_1;
        }
      }
    }
  }

  #booking .destination_wrapper {
    position: relative;
    background: transparent !important;
    border-bottom-width: 0;
    margin: 0;
    height: 89px;
    @extend .fa-angle-down;
    &:before {
      @extend .fa;
      position: absolute;
      right: 10px;
      z-index: 5;
      bottom: 20px;
      margin-left: 15px;
      color: white;
    }
    label {
      position: absolute;
      top: 0;
      left: 10px;
      z-index: 2;
      display: block;
      color: white;
      margin-bottom: -23px;
      font-weight: 600;
    }
    .destination_field {
      @extend .fa-search;
      &:before {
        @extend .fa;
        position: absolute;
        bottom: 20px;
        left: 15px;
        font-size: 30px;
        color: $corporate_1;
      }
      &:after {
        content: '';
        background: transparent !important;
      }
      .destination {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        display: inline-block;
        vertical-align: middle;
        border-width: 0;
        color: white;
        padding: 52px 25px 13px 40px;
        font-family: $text_font;
        font-weight: 700;
        font-size: 14px;
        text-align: center;
        resize: none;
        width: 250px;
        outline: none;
        cursor: pointer;
        height: 89px;
        position: relative;
        z-index: 10;
        background: transparent;
        &::placeholder {
          color: white;
        }
      }
    }
  }

  .dates_selector_personalized {
    padding-bottom: 6px;
    @-moz-document url-prefix() {
      padding-bottom: 4px;
    }
    margin: 0;
    label.dates_selector_label {
      display: block;
      span {
        display: inline-block;
        width: 50%;
        padding: 7px 10px 5px 30px;
        color: white;
      }
    }
    .start_end_date_wrapper {
      font-size: 0;
      padding: 0;
      height: auto;
      width: 400px;
      background: transparent;
      .start_date_personalized, .end_date_personalized {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        width: 48%;
        padding: 5px 40px 7px 30px;
        font-family: $text_font;
        font-size: 16px;
        font-weight: 400;
        color: white;
        @extend .fa-angle-down;
        &:before {
          @extend .fa;
          position: absolute;
          bottom: 10px;
          right: 0;
          color: white;
          font-size: 18px;
        }
        div {
          display: inline-block;
          margin: 0;
          text-transform: uppercase;
          &.day {
            font-size: 36px;
          }
        }
      }
      .nights_number_wrapper_personalized {
        display: none;
      }
    }
    &:after {
      content: '';
      background: rgba(white, .3) !important;
      width: 1px;
      height: 60px;
      @include center_y;
      right: 0;
    }
  }

  .selectricItems {
    overflow: auto !important;
  }

  #full-booking-engine-html-7 {
    width: auto;
    display: block;
    margin: auto !important;
    position: relative;
    .promocode_header {
      display: none;
    }
  }

  #full-booking-engine-html-7 form.booking_form {
    background: transparent;
    position: relative;
    display: table;
    margin: auto;
    background: rgba($corporate_2, .8);
  }

  .booking_form_title .best_price {
    display: none;
    color: black;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: black;
  }
  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background: transparent;
    .button {
      background: transparent !important;
    }
  }

  .rooms_number_wrapper .rooms_number .selectric {
    margin-top: 0;
  }

  .room_list_wrapper {
    display: none;
    width: 170px;
    position: absolute;
    left: 530px;
    top: 89px;
    &:lang(it) {
      width: 340px;
    }
    &:lang(pt), &:lang(de) {
      width: 350px;
    }
    .room_list {
      position: relative;
      z-index: 500;
      box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
    }
    li:first-child:before {
      content: '';
      text-align: center;
      background: white !important;
      width: 90%;
      left: 5%;
      height: 1px;
      position: absolute;
    }
    li:first-child {
      height: 55px;
    }
    li {
      height: 40px;
      background-color: white;
      margin: 0;
    }
    label {
      margin: 0;
      text-align: center;
      color: $corporate_2;
      font-size: 11px;
      font-weight: 600;
      text-transform: capitalize;
    }
    .selectricWrapper {
      .selectric {
        .label {
          color: $corporate_2;
        }
      }
    }
    .adults_selector:after {
      content: '';
      position: absolute;
      top: 50%;
      transform: translate(0%, -50%);
      margin-top: 14px;
      right: 0;
      width: 1px;
      height: 27px;
      background-color: white;
    }

    .room {
      width: 230px;
      .adult_label, .children_label {
        white-space: nowrap;
      }
      .selectricWrapper .selectric .label {
        text-align: center;
      }
    }
    .room1 {
      .adults_selector:after {
        height: 39px;
        margin-top: 21px;
      }
    }
    .adults_selector, .children_selector {
      height: 1px;
      border: 0;
      position: relative;
      width: calc((100% - 5px) / 2) !important;
      float: none;
    }
    .babies_selector {
      float: right;
    }
    .selectricItems {
      li {
        height: 35px;
        border-top-color: rgba($corporate_1, .3);
        &:hover, &.selected {
          border-top-color: $corporate_1;
        }
      }
    }
  }
  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
    font-size: 14px !important;
  }

  .wrapper-new-web-support.booking_form_title {
    text-align: center;
    background: none;
    opacity: 1;
    font-size: 13px !important;
    width: 1040px;
    margin: auto;
    background-color: rgba(white, .8);
    color: $corporate_2;
    position: relative;
    z-index: 100;

    .web_support_label_2 {
      display: inline-block;
      margin: 0 10px;
    }

    .phone_support_image {
      display: none;
    }
  }

  .date_box.entry_date, .date_box.departure_date {
    margin-top: 6px;

    .date_year {
      display: none;
    }

    .date_day {
      border-bottom: 0 !important;
      font-weight: 300;
      font-size: 16px !important;
      color: black;
    }
  }

  .date_box.departure_date {
  }

  .selectricWrapper {
    width: 100% !important;

    .selectric {
      margin-top: -5px;
    }
  }

  #slider_inner_container #full-booking-engine-html-7 {
    margin-top: -17px !important;
  }

  .promocode_text {
    display: none;
  }
  input.promocode_input {
    font-size: 16px;
  }

  .stay_selection {
    display: inline-block;
    vertical-align: top;
    float: left;

    .entry_date_wrapper, .departure_date_wrapper {
      display: inline-block;
      vertical-align: top;
      float: left;
      margin-right: 5px;
      border: 0 !important;
      background: white;
      width: 212px;
      height: 47px;
    }

    .departure_date_wrapper {
      border-left: 0;
      border-right: 0;
    }

    .nights_number_wrapper {
      display: inline-block;
      width: 95px;
      float: left;
      vertical-align: top;
      border-top: 1px solid lightgrey;
    }
  }

  .rooms_number_wrapper {
    margin: 0;
    float: left;
    height: 89px;
    width: 130px;
    padding: 7px 10px 5px;
    box-sizing: border-box;
    position: relative;
    @extend .fa-angle-down;
    &:before {
      @extend .fa;
      position: absolute;
      bottom: 16px;
      right: 10px;
      color: white;
      font-size: 18px;
    }
    &:after {
      content: '';
      background: rgba(white, .3) !important;
      width: 1px;
      height: 60px;
      @include center_y;
      right: 0;
    }
    .selectricWrapper {
      .selectric {
        margin-top: 10px;
        .label {
          font-family: $text_font;
          font-size: 36px;
        }
      }
    }
  }

  .wrapper_booking_button {
    display: inline-block;
    background-color: transparent;
    width: auto;
    float: right;
    padding: 0;
    label.promocode_label {
      display: none;
    }

    .promocode_wrapper {
      display: inline-block;
      vertical-align: top;
      float: none;
      width: 140px;
      height: 89px;
      background: transparent;
      position: relative;
      padding: 0;
      border-width: 0;
      input.promocode_input {
        display: inline-block;
        vertical-align: middle;
        width: calc(100% - 6px);
        margin-top: 0;
        padding: 9px;
        height: auto;
        color: white;
        background: transparent;
        text-align: center;
        text-transform: capitalize;
        &::-webkit-input-placeholder {
          color: white;
        }
        &:-moz-placeholder {
          color: white;
        }
        &::-moz-placeholder {
          color: white;
        }
        &:-ms-input-placeholder {
          color: white;
        }
      }
      &:before {
        content: '';
        display: inline-block;
        vertical-align: middle;
        width: 1px;
        height: 60px;
        margin: 20px 0;
        background-color: rgba(white, .3);
      }
    }

    .submit_button {
      position: relative;
      width: 200px;
      height: 89px;
      display: inline-block;
      vertical-align: top;
      float: right;
      color: white;
      font-size: 18px;
      background: $corporate_1;
      letter-spacing: 2px;
      font-weight: 700;
      span {
        position: relative;
        z-index: 2;
      }
      &:before {
        content: '';
        @include full_size;
        width: 0;
        background-color: darken($corporate_2, 10%);
        @include transition(width, .6s);
      }
      &:hover:before {
        width: 100%;
      }
    }
  }
}

#full-booking-engine-html-7 {
  &.adults_only {
    .guest_selector {
      display: none;
    }

    .room_list_wrapper {
      display: inline-block !important;
      position: relative;
      top: auto;
      left: auto;
      float: none;
      width: auto;
      vertical-align: middle;
      margin-top: 0;

      &:before {
        display: none !important;
      }

      li {
        background: none;
        height: auto;

        &:before {
          display: none;
        }

        .children_selector {
          display: none;
        }

        .adults_selector {
          margin: 0;
          float: left;
          height: 89px;
          width: 170px !important;
          padding: 7px 10px 5px;
          box-sizing: border-box;
          position: relative;

          &:after {
            display: none;
          }

          .adults_label {
            margin: 0;
            color: white;
            font-size: 12px;
            font-weight: 400;
            text-transform: uppercase;
            margin-bottom: 13px;
            text-align: left;
            margin-top: 3px;
          }

          p.label {
            line-height: 37px;
            color: white;
            text-align: left;
            font-weight: 500;
            font-family: $text_font;
            font-size: 36px;
          }
        }
      }
    }
  }
}

body.home_section #full_wrapper_booking.fixed_booking {
  width: 100%;
}

.babies_selector {
  width: 33.3%;
  display: inline-block;
  padding: 7px 10px 5px;
  box-sizing: border-box;

  label {
    text-transform: uppercase;
    font-size: 10px;
  }
}

/*=== Ocupancy selector ====*/
.guest_selector {
  position: relative;
  margin: 0;
  float: left;
  height: 89px;
  padding: 7px 10px 5px;
  box-sizing: border-box;
  width: 170px;
  cursor: pointer;
  @extend .fa-angle-down;
  &:before {
    @extend .fa;
    position: absolute;
    bottom: 16px;
    right: 10px;
    color: white;
    font-size: 18px;
  }
  span {
    margin: 0;
    color: white;
    font-size: 14px;
    font-weight: 400;
    text-transform: capitalize;
    font-family: 'Montserrat', sans-serif;
  }
  .guest_adults {
    font-size: 36px;
    font-weight: 400;
  }
  label {
    margin: 5px 0 5px;
    color: white;
    display: block;
  }

  .button {
    display: none;
  }
}

#booking label {
  cursor: pointer;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 12px;
}

.selectricWrapper .selectric .label {
  font-size: 16px;
  line-height: 37px;
  color: white;
  text-align: left;
  font-weight: 500;
}

#booking .room_list label {
  display: block !important;
}

#booking .room_list .room2 label, #booking .room_list .room3 label {
  display: none !important;
}

#full_wrapper_booking {
  .rooms_number {
    .selectricItems {
      width: 130px !important;
      margin-left: -10px !important;
      top: 55px;
    }
  }
  .room_list_wrapper {
    .selectricItems {
      width: 115px !important;
      top: 30px;
    }
  }
}

.rooms_number_wrapper label {
  margin: 0;
  text-align: center;
  color: white;
  font-size: 14px;
  font-weight: 700;
  text-transform: capitalize;
}

.hotel_selector {
  display: none;
}

.destination_wrapper {
  display: inline-block;
  float: left;
  margin-right: 5px;
  cursor: pointer;

  input {
    height: 46px;
    box-sizing: border-box;
    font-weight: 300;
    font-size: 13px;
    padding-left: 15px;
    cursor: pointer;
    color: black;
    width: 220px;
  }
  .destination_field {
    position: relative;
  }
  .destination_field:after {
    background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
    color: #585d63;
    font-size: 23px;
    margin-left: 0;
    text-indent: 999px;
    font-weight: 600;
    float: right;
    width: 30px;
    height: 30px;
    position: absolute;
    top: 10px;
    right: 10px;
    content: '';
    display: block;
  }
}

body {
  &.with_top_header_covid {
    div#full_wrapper_booking.floating_booking.showed {
      position: fixed;
      top: 165px;
    }
  }
}
div#full_wrapper_booking.floating_booking.showed {
  position: fixed;
  top: 125px;
  bottom: auto;
  width: 100%;
  padding: 0;
  background: $corporate_2;

  .wrapper-new-web-support.booking_form_title {
    width: 100%;
  }
}

.datepicker_wrapper_element,
.datepicker_wrapper_element_2,
.datepicker_wrapper_element_3 {
  border-radius: 0;
  margin-top: 6px;
  .header_datepicker {
    background-color: $corporate_2;
  }
}

body.ibersol-antemare {
  #full_wrapper_booking .wrapper-new-web-support.booking_form_title {
    width: 1100px;
  }
}
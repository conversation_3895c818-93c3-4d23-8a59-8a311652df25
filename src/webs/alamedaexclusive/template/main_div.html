<ul itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner" class="container">
    {% for section in main_sections %}
        <div class="main-section-div-wrapper"
             {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
            {% if section.subsections %}
                <a><span itemprop="name">{{ section.title|safe }}</span></a>
            {% else %}
                {% if section.title %}
                    <a itemprop="url" href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}">
                        <span itemprop="name">{{ section.title|safe }}</span>
                    </a>
                {% endif %}
            {% endif %}
            {% if section.subsections %}
                <ul>
                    {% for subsection in section.subsections %}
                        <li class="main-section-subsection {{ subsection.title|lower }}">
                            {% if subsection.title %}
                                <a href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrlInternational }}"
                                   {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                    <span>{{ subsection.title|safe }}</span>
                                </a>
                            {% endif %}
                        </li>
                    {% endfor %}
                </ul>
            {% endif %}
        </div>
    {% endfor %}
</ul>
/*
    THIS CODE DISPLAY A FORM WHEN CLICK ON BOOKING BUTTON

$(window).load(function(){
   booking_rates_form.init()
});


booking_rates_form = function(){
    return {
        constants: {
            booking_engine: $("#full-booking-engine-html-7")
        },

        init: function(){
            this._clear_engine_listeners();
            this._prepare_custom_listeners();
        },

        _clear_engine_listeners: function() {
            this.constants.booking_engine.find(".submit_button").unbind('click');
        },

        _prepare_custom_listeners: function() {
            this.constants.booking_engine.find(".submit_button").click(function() {
                booking_rates_form.open_form_popup();
            });

            $("#rates-form-contact").find(".submit_form_button").click(function(){
                booking_rates_form.submit_form();
            });


        },

        open_form_popup: function() {
            $.fancybox($("#rates-form-contact"), {
                padding: 0
            });
        },

        submit_form: function() {

            if($("#rates-form-contact").find("form").valid()){
                $.ajax({
                    type: "POST",
                    url: '/utils',
                    data: $("#rates-form-contact").find("form").serialize(),
                    success: function(response){
                        var message = $("#rates-form-contact").find(".sent_form").html();
                        alert(message);
                        $.fancybox.close()
                    }
                })
            }
        }
    };
}();
 */
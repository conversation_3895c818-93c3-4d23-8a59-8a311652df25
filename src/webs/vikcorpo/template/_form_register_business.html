<div class="contact_form_wrapper register_user_wrapper effects_sass" sass_effect="slide_up_effect" {% if table_tabs %}style="background-color: white;"{% endif %}>
    <div class="container12">
        {% if register_agencias.subtitle %}
            <h3>{{ register_agencias.subtitle|safe }}</h3>
        {% endif %}

        <form name="suscribe_form" id="suscribe_form" class="suscribe_form" onsubmit="register_user();return false;">
            <input type="hidden" name="action" id="action" value="contact"/>

            <div class="info">
                <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>
                <input type="hidden" name="language" id="language" value="{{ language }}"/>
                {% if dont_send_user_email %}
                    <!-- Do not send user creation email to user -->
                    <input type="hidden" name="dont_send_user_email" value="True">
                    <!-- Do not send user creation email to user -->
                {% endif %}
                {% if register_agencias_email %}
                    <input type="hidden" name="register_agencias_email" id="register_agencias_email" value="{{ register_agencias_email }}"/>
                {% endif %}

                <h4>{{ T_empresa }} / {{ T_agencia }}</h4>
                <div class="contInput">
                    <i class="fa fa-building" aria-hidden="true"></i>
                    <input type="text" id="business_name" name="business_name" class="bordeInput" value="" placeholder="{{ T_nombre }}"/>
                </div>
                {% if not register_empresas %}
                    <div class="contInput contHalf">
                        <i class="fa fa-id-card-o" aria-hidden="true"></i>
                        <input type="text" id="cif" name="cif" class="bordeInput" value="" placeholder="{{ T_cif }}"/>
                    </div>
                    <div class="contInput contHalf" style="padding-top: 20px">
                        <i class="fa fa-id-badge" aria-hidden="true" style="top: 25px"></i>
                        <input type="text" id="iata" name="iata" class="bordeInput" value="" placeholder="IATA"/>
                    </div>
                    <div class="contInput contHalf">
                        <i class="fa fa-building-o" aria-hidden="true"></i>
                        <input type="text" id="cod_sucursal" name="cod_sucursal" class="bordeInput" value="" placeholder="{{ T_codigo }}"/>
                    </div>
                    <div class="contInput">
                        <i class="fa fa-briefcase" aria-hidden="true"></i>
                        <input type="text" id="agency_type" name="agency_type" class="bordeInput" value="" placeholder="{{ T_tipo }}"/>
                    </div>
                    <div class="contInput contHalf">
                        <i class="fa icon-map" style="font-size: 140%" aria-hidden="true"></i>
                        <input type="text" id="city" name="city" class="bordeInput" value="" placeholder="{{ T_ciudad }}"/>
                    </div>
                    <div class="contInput ">
                        <i class="fa icon-mapmarker" style="font-size: 120%" aria-hidden="true"></i>
                        <input type="text" id="address" name="address" class="bordeInput" value="" placeholder="{{ T_direccion }}"/>
                    </div>
                    <div class="contInput contHalf">
                        <i class="fa fa-map-pin" aria-hidden="true"></i>
                        <input type="text" id="postal_code" name="postal_code" class="bordeInput" value="" placeholder="{{ T_codigo_postal }}"/>
                    </div>
                    <div class="contInput contHalf">
                        <i class="fa fa-map-signs" aria-hidden="true"></i>
                        <input type="text" id="province" name="province" class="bordeInput" value="" placeholder="{{ T_provincia }}"/>
                    </div>

                {% endif %}

                <h4>{{ T_persona_de_contacto }}</h4>
                <div class="contInput contHalf" style="padding-top: 10px">
                    <i class="fa fa-user" aria-hidden="true" style="top:15px;"></i>
                    <input type="text" id="name" name="name" class="bordeInput" value="" placeholder="{{ T_nombre }}"/>
                </div>
                <div class="contInput contHalf">
                    <i class="fa fa-user-plus" aria-hidden="true"></i>
                    <input type="text" id="surname" name="surname" class="bordeInput" value="" placeholder="{{ T_apellidos }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-phone" aria-hidden="true"></i>
                    <input type="text" id="telephone" name="telephone" class="bordeInput" value="" placeholder="{{ T_telefono }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-mobile" aria-hidden="true"></i>
                    <input type="text" id="mobile" name="mobile" class="bordeInput" value="" placeholder="{{ T_movil }}"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-fax" aria-hidden="true"></i>
                    <input type="text" id="fax" name="fax" class="bordeInput" value="" placeholder="Fax"/>
                </div>
                <div class="contInput">
                    <i class="fa fa-envelope-o" aria-hidden="true"></i>
                    <input type="text" id="email" name="email" class="bordeInput" value="" placeholder="{{ T_email }}"/>
                    <input type="hidden" name="username" id="username_element">
                </div>
                <div class="contInput">
                    <i class="fa fa-comment" aria-hidden="true"></i>
                    <input type="text" id="comments" name="comments" class="bordeInput" value="" placeholder="{{ T_comentarios }}"/>
                </div>

                <div class="contInput recaptcha">
                    <script src='https://www.google.com/recaptcha/api.js?hl={{ language_code }}'></script>
                    <div class="g-recaptcha" data-sitekey="{{ recaptcha_publickey }}"></div>
                </div>

                <button id="contact-button">{{ T_enviar }}</button>
                <div class="contInput policy-terms">
                    <input type="checkbox" id="accept-term" name="accept_term"/>
                    <a data-fancybox data-options='{"caption" : "{{ T_lopd }}", "src" : "/{{language}}/?sectionContent=politica-de-privacidad.html", "type" : "iframe", "width" : "100%", "max-width" : "100%"}' data-width="1200" href="/{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                </div>

                <input type="hidden" class="completed_message" value="{{ T_thanks }}">

            </div>
        </form>
    </div>
</div>


<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>

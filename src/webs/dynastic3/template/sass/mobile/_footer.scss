footer {
  position: relative;
  z-index: 10;
  padding: 20px 20px 70px;
  font-size: 16px;
  background-color: $corporate_1;
  text-align: center;
  color: white;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  
  .footer_column {
    border-top: 1px solid white;
    width: 100%;
    padding: 20px;
    
    .column_img, .column_text {
      display: block;
    }
    
    .column_img {
      margin: 20px 0;
      
      img {
        height: 75px;
        width: auto;
      }
    }
    
    .column_title {
      font-size: 20px;
    }
    
    .column_text {
      font-size: 11px;
      text-align: center;
    }
    
    
    
    &:nth-child(3),
    &:nth-child(4) {
      width: 50%;
    }
    
  }
  
  .legal_texts_wrapper {
    .legal_text {
      font-family: $texts_family;
      font-size: 11px;
      font-weight: 300;
      color: white;
    }
  
    .submenu_list {
      display: flex!important;
      flex-flow: row wrap;
      justify-content: center;
      list-style: none;
      width: 100%;
      padding: 10px 0 30px;
      margin: auto;
    
    
      .default_link {
        padding: 0;
        margin-right: 5px;
        padding-right: 5px;
        position: relative;
      
        &::before {
          position: absolute;
          content: '';
          width: 1px;
          background-color: white;
          top: 7px;
          bottom: 7px;
          right: 0;
        }
      
        &:last-child {
          &::before {
            display: none;
          }
        }
      
        a,
        span,
        a span {
          font-family: $texts_family;
          font-size: 11px;
          font-weight: 300;
          color: white;
        }
      }
    }
  }
}
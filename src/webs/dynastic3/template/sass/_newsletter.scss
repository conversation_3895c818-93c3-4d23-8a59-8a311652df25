/*======= Footer newsletter =======*/
.newsletter_wrapper {
  position: relative;
  overflow: hidden;
  padding: 97px 0;
  z-index: 2;
  text-align: center;
  background-size: cover;

  &:before {
    @include full_size;
    content: "";
    background: rgba(black, .6);
  }

  .newsletter_title {
    text-align: center;
    color: white;
    font-size: 33px;
    margin-bottom: 28px;
    font-family: $title_family;
    text-transform: uppercase;
  }

  .newsletter_description {
    width: 600px;
    margin: auto;
    text-align: center;
    color: white;
    line-height: 31px;
  }

  .newsletter_container {
    position: relative;
    z-index: 1;
  }

  .newsletter_form {
    margin-top: 50px;

    input#suscEmail {
      background: none;
      border: 1px solid white;
      width: 212px;
      line-height: 51px;
      height: 55px;
      box-sizing: border-box;
      color: white;
      font-size: 20px;
      font-weight: lighter;
      text-align: center;

      &::-webkit-input-placeholder {
        text-transform: uppercase;
        color: white;
        font-size: 16px;
        font-weight: lighter;
      }

      &::-moz-placeholder {
        text-transform: uppercase;
        color: white;
        font-size: 16px;
        font-weight: lighter;
      }

      &:-ms-input-placeholder {
        text-transform: uppercase;
        color: white;
        font-size: 16px;
        font-weight: lighter;
      }

      &:-moz-placeholder {
        text-transform: uppercase;
        color: white;
        font-size: 16px;
        font-weight: lighter;
      }
    }
  }

  .social_newsletter {
    display: inline-block;
    vertical-align: middle;

    a {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background: $corporate_1;
      display: inline-block;
      position: relative;

      &:hover {
        opacity: .8;
      }

      .fa {
        @include center_xy;
        color: white;
        font-size: 26px;
      }
    }
  }

  .newsletter_checkbox {
    margin-top: 5px;
  }

  .check_newsletter a {
    color: white;
  }

  .button_newsletter {
    width: 213px;
    height: 55px;
    -webkit-appearance: none;
    background: $corporate_3;
    background-position-x: 90%;
    color: white;
    border: 0;
    font-family: $title_family;
    transform: translateY(-3px);
    display: inline-flex;
    vertical-align: middle;
    justify-content: center;
    align-items: center;
    text-transform: uppercase;
    font-size: 22px;
    cursor: pointer;
    @include transition(background, .4s);

    &:hover {
      background: lighten($corporate_3, 10%);
    }
  }

  input#suscEmail {
    background: none;
    border: 1px solid white;
    width: 212px;
    line-height: 51px;
    height: 55px;
    box-sizing: border-box;
    color: white;
    font-size: 20px;
    font-weight: lighter;
    text-align: center;

    &::-webkit-input-placeholder {
      text-transform: uppercase;
      color: white;
      font-size: 16px;
      font-weight: lighter;
    }
  }
}
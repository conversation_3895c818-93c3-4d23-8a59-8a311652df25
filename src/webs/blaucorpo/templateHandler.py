# -*- coding: utf-8 -*-
import copy
import json

from booking_process.utils.calendar.utilities.closed_hotel_utils import get_range_hotel_closed
from booking_process.constants.advance_configs_names import WHATSAPP_ID, CONTACT_PHONES, MAIN_SECTIONS, REPLACE_LINK_MENU, \
	PUBLIC_CAPTCHA_KEY, KEY_DOMAIN
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name, getLogotypes
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name_with_properties, \
	get_section_from_section_spanish_name, get_full_data_sections
from booking_process.utils.language.language_utils import get_language_code, get_web_dictionary
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.namespaces.namespace_utils import get_namespace
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

TEMPLATE_NAME = "blaucorpo"
#Change this value in default.scss and in config.rb!!
base_web = TEMPLATE_NAME[:4]+TEMPLATE_NAME[-1:]

class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		params = {
			'base_web': base_web,
			'floating_buttons': get_pictures_from_section_name("_floating_buttons", language),
			'whatsapp_id': get_config_property_value(WHATSAPP_ID)
		}

		if not user_agent_is_mobile():
			params.update(self.getDesktopData(sectionToUse, language))
		else:
			script_args = {
				"section_type": sectionToUse.get("sectionType")
			}

			params_mobile = {
				"custom_element_home": self.getHtmlExtraBannersMobile(sectionToUse, language),
				"fontawesome5": True,
				"extra_bottom_script": self.buildTemplate_2("mobile/_script_mobile.html", script_args, False, TEMPLATE_NAME),
				'footer_extra_links': get_pictures_from_section_name('_additional_policies', language)
			}
			whatsapp_id = get_config_property_value(WHATSAPP_ID)
			if whatsapp_id:
				args = {
					'whatsapp_id': whatsapp_id
				}
				mini_html = self.buildTemplate_2('mobile/_whatsapp_button.html', args, False, TEMPLATE_NAME)
				params_mobile['custom_element_home'] += mini_html
				params_mobile['custom_element_inner'] = mini_html

			params.update(params_mobile)

		return params

	def getDesktopData(self, section, language):
		section_type = ''
		section_name = ''
		if section:
			section_type = section['sectionType']
			section_name = section['sectionName']
		else:
			section = {}

		result_params_dict = {
			'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False, "allow_fancybox": False}),
			# 'booking_engine_2': self.buildSearchEngine2(language),
			'newsletter': self.buildNewsletter2(language, check_newsletter=True, social=True),
			'phone_contact': get_config_property_value(CONTACT_PHONES),
			'language_selected': get_language_code(language),
			'closed_chain': json.dumps(get_range_hotel_closed(language)),
			'extra_footer_links': self.getPicturesProperties(language, '_extra_footer_links')
		}

		top_header_text = get_section_from_section_spanish_name_with_properties('_top_header_text', language)
		if top_header_text:
			if top_header_text.get("extra_phone"):
				top_header_text['extra_phone'] = unescape(top_header_text.get("extra_phone"))
			result_params_dict['top_header_text'] = top_header_text

		whatsapp_widget = get_section_from_section_spanish_name('_whatsapp_widget', language)
		if whatsapp_widget:
			whatsapp_id = get_config_property_value(WHATSAPP_ID)
			if whatsapp_id:
				whatsapp_widget['whatsapp_id'] = whatsapp_id
			if whatsapp_widget.get("subtitle"):
				whatsapp_widget['subtitle'] = unescape(whatsapp_widget.get("subtitle"))
			result_params_dict['whatsapp_widget'] = whatsapp_widget

		logo_floating = getLogotypes("floating")
		if logo_floating:
			result_params_dict['logo_floating'] = logo_floating[0]

		pictures_preloading = get_pictures_from_section_name("_preloading", language)
		result_params_dict['preloading_background'] = list(filter(lambda x: x.get('title') == "background", pictures_preloading))
		result_params_dict['preloading_motif'] = list(filter(lambda x: x.get('title') == "motif", pictures_preloading))
		result_params_dict['preloading_logo'] = list(filter(lambda x: x.get('title') == "logo", pictures_preloading))

		footer_columns = self.getPicturesProperties(language, "_footer_columns")
		result_params_dict['footer_column'] = []
		result_params_dict['footer_logo_column'] = []
		for column in footer_columns:
			if column.get('logo'):
				result_params_dict['footer_logo_column'].append(column)
			elif column.get("title") == "to_top_text":
				result_params_dict['footer_to_top_text'] = column.get("description", "")
			else:
				result_params_dict['footer_column'].append(column)

		if section.get('subtitle'):
			result_params_dict['content_subtitle'] = section

		automatic_content = {
			'Galeria de Imagenes': True,
			'Mis Reservas': True
		}
		if automatic_content.get(section_type):
			result_params_dict['content_access'] = True

		result_params_dict.update(self.getDataSection(section, language))
		result_params_dict.update(self.getExtraBanners(section, language))

		all_sections = get_full_data_sections(language)
		menu_sections = self.getSectionsFor(language, all_sections, MAIN_SECTIONS)
		replace_link_menu = get_config_property_value(REPLACE_LINK_MENU)
		if replace_link_menu:
			replace_link_menu = replace_link_menu.split(";")
		for menu_section in menu_sections:
			if replace_link_menu:
				for replace_link_section in replace_link_menu:
					if menu_section.get("sectionName").lower() == replace_link_section:
						advance_section = self.getSectionAdvanceProperties(menu_section, language)
						if advance_section.get("replace_link"):
							menu_section['replace_link'] = advance_section.get("replace_link")
							break
			if menu_section.get('subsections'):
				for subsection in menu_section['subsections']:
					if subsection.get('sectionName') == section_name:
						result_params_dict['is_subsection'] = True

		menu_length = len(menu_sections)
		menu_limit = int(menu_length / 2)
		if menu_limit % 2 == 1:
			menu_limit += 1
		result_params_dict["menu_left"] = menu_sections[:menu_limit]
		result_params_dict["menu_right"] = menu_sections[menu_limit:]

		return result_params_dict

	def getDataSection(self, section, language):
		result = {}
		section_type = section.get("sectionType")
		section_name = section.get("sectionName")

		advance_properties = self.getSectionAdvanceProperties(section, language)
		if section_type == "Inicio":
			result['home'] = True

		elif section_type == "Habitaciones":
			result['class_section'] = 'rooms_section'
			if advance_properties.get('rooms_sections'):
				sections = advance_properties['rooms_sections'].split(';')
				rooms = []
				for sec in sections:
					ind_room = get_section_from_section_spanish_name_with_properties(sec, language)
					if ind_room.get('big_room'):
						ind_room_dict = {
							'banner_big_room': ind_room,
							'banner_big_room_pictures': get_pictures_from_section_name(sec, language)
						}

					else:
						ind_room_dict = {}
						not_banner_big_room = self.getPicturesProperties(language, sec)
						for x in not_banner_big_room:
							if x.get('gallery'):
								x['images'] = get_pictures_from_section_name(x['gallery'], language)
								x['info'] = get_section_from_section_spanish_name(x['gallery'], language)
						ind_room_dict['not_banner_big_room'] = not_banner_big_room

					rooms.append(ind_room_dict)

				result['rooms'] = rooms
			else:
				return get_pictures_from_section_name("_habitaciones_blocks", language)

		elif section_type == "Ofertas":
			result['class_section'] = 'offers_section'
			result['booking_button'] = advance_properties.get("booking_button")
			result['offers'] = self.getOffers(language)

		elif section_type == u"Localización":
			result['class_section'] = 'contact_section'
			result['contact_form'] = True
			result['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)
			# result['iframe_map'] = get_section_from_section_spanish_name("iframe google maps", language).get('content', '')

		if not section_type == "Inicio":
			section_pictures = get_pictures_from_section_name(section_name, language)
			slider_pictures = []
			minigallery_carousel = []
			for pic in section_pictures:
				if pic.get('title') == 'slider':
					slider_pictures.append(pic)
				else:
					minigallery_carousel.append(pic)

			result['minigallery_carousel'] = minigallery_carousel
			result['revolution_slider'] = self.buildRevolutionSlider(language, slider_pictures)

		return result

	def getExtraBanners(self, section, language):
		result = {}
		advance_properties = self.getSectionAdvanceProperties(section, language)

		if advance_properties.get("minigallery"):
			minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery"), language)
			result['minigallery_for_mobile'] = minigallery_images
			mini_dict = {'minigallery': minigallery_images,'num_items': 5,'margin': 5}
			minigallery_html = self.buildTemplate_2("banners/_minigalleryx.html", mini_dict, False)
			result["minigallery"] = minigallery_html

		if advance_properties.get('banner_ofertas_web'):
			result['banner_ofertas_section'] = get_section_from_section_spanish_name_with_properties(advance_properties['banner_ofertas_web'], language)
			result['banner_ofertas_properties'] = self.getSectionAdvanceProperties(result['banner_ofertas_section'], language)
			banner_ofertas_pics = get_pictures_from_section_name(advance_properties['banner_ofertas_web'], language)
			result['banner_ofertas_pics'] = []
			for pic in banner_ofertas_pics:
				if pic.get('title') == 'see_more_button':
					result['banner_ofertas_seemorebutton'] = pic
				else:
					result['banner_ofertas_pics'].append(pic)

		if advance_properties.get("banner_icons"):
			result['banner_icons_content'] = get_section_from_section_spanish_name(advance_properties['banner_icons'], language)
			result['banner_icons'] = self.getPicturesProperties(language, advance_properties['banner_icons'])

		if advance_properties.get("banner_hotels"):
			result['banner_hotels_section'] = get_section_from_section_spanish_name(advance_properties['banner_hotels'], language)
			result['banner_hotels_pic_properties'] = self.getPicturesProperties(language, advance_properties['banner_hotels'], ['price'])
			for x in result['banner_hotels_pic_properties']:
				if x.get('price'):
					x['price'] = unescape(x['price'])

		if advance_properties.get("banner_inner_gallery"):
			result['banner_inner_gallery_pics'] = get_pictures_from_section_name(advance_properties['banner_inner_gallery'], language)

		if advance_properties.get("banner_apartments_block"):
			result['banner_apartments_block_pics'] = get_pictures_from_section_name(advance_properties['banner_apartments_block'], language)
			result['banner_apartments_block_pics_properties'] = self.getPicturesProperties(language, advance_properties['banner_apartments_block'], ['gallery'])

			for x in result['banner_apartments_block_pics_properties']:
				if x.get('gallery'):
					x['images'] = get_pictures_from_section_name(x['gallery'], language)
					x['info'] = get_section_from_section_spanish_name(x['gallery'],language)

		if advance_properties.get("banner_ventajas_web"):
			result['banner_ventajas_web_section'] = get_section_from_section_spanish_name_with_properties(advance_properties['banner_ventajas_web'], language)
			result['banner_ventajas_web_pictures'] = get_pictures_from_section_name(advance_properties['banner_ventajas_web'], language)

			if result['banner_ventajas_web_section'].get('icons_gallery'):
				result['banner_ventajas_icons'] = get_pictures_from_section_name(result['banner_ventajas_web_section']['icons_gallery'], language)

		if advance_properties.get("banner_text"):
			result['banner_text_section'] = get_section_from_section_spanish_name(advance_properties['banner_text'], language)
			result['banner_text_pictures'] = get_pictures_from_section_name(advance_properties['banner_text'], language)

		if advance_properties.get("cycle_banner"):
			result["cycle_banner"] = get_pictures_from_section_name(advance_properties.get("cycle_banner"), language)

		if advance_properties.get('banner_localizacion'):
			result['banner_localizacion'] = get_section_from_section_spanish_name(advance_properties['banner_localizacion'],language)
			banner_location_info = self.getPicturesProperties(language,advance_properties['banner_localizacion'])
			result['location_direction'] = []
			for info in banner_location_info:
				title = info.get('title')
				if title == 'map_image':
					result['location_image'] = info.get('servingUrl')
				elif title == 'telefono':
					result['location_phone'] = info.get('description')
				elif title == 'email':
					result['location_email'] = info.get('description')
				elif title == 'googlemaps_link_general':
					result['googlemaps_link_general'] = info.get('linkUrl')
				elif title == 'localizacion':
					result['location_direction'].append(info)

		if advance_properties.get('banner_big_room'):
			result['banner_big_room'] = get_section_from_section_spanish_name(advance_properties['banner_big_room'], language)
			result['banner_big_room_pictures'] = get_pictures_from_section_name(advance_properties['banner_big_room'], language)

		if advance_properties.get("work_form"):
			work_section = get_section_from_section_spanish_name(advance_properties['work_form'], language)
			work_options = get_pictures_from_section_name(advance_properties['work_form'], language)
			result['work_form'] = True
			result['work_section'] = work_section
			result['work_options'] = {
				"work": [],
				"department": []
			}
			result['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)

			for work in work_options:
				if work.get("title") == "work":
					result['work_options']['work'].append(work)

				else:
					result['work_options']['department'].append(work)

		return result

	def getRooms(self, language):
		rooms = get_pictures_from_section_name("_habitaciones_blocks", language)

		return rooms

	def getRoomsPersonalized(self, language, advance_properties):
		if advance_properties.get('rooms_sections'):
			sections = advance_properties['rooms_sections'].split(';')
			rooms = []
			for sec in sections:
				ind_room_info = self.getPicturesProperties(language,sec)
				rooms.append(ind_room_info)
			return rooms

		else:
			return get_pictures_from_section_name("_habitaciones_blocks", language)

	def getOffers(self, language):
		offers = self.buildPromotionsInfo(language)

		return offers

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "650"

	def get_revolution_full_screen(self):
		return "on"

	def get_revolution_transition(self):
		return "fade"

	def get_revolution_initializer(self):
		return True

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_7/_booking_widget.html', params, False)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		options['T_put_promocode'] = "PROMOCODE"
		general_dict = get_web_dictionary(language)

		if user_agent_is_mobile():
			options['hotels_list_mobile'] = copy.deepcopy(self.get_hotels(language))
			for hotel in options['hotels_list_mobile']:
				hotel['value'] = hotel.get('title')
				hotel['id'] = hotel.get('namespace')
				hotel['class'] = hotel.get('namespace')

			actual_namespace = get_namespace()
			all_hotels_dict = {
				'value': general_dict.get('T_todos_hoteles_2'),
				'id': actual_namespace,
				'class': actual_namespace,
				'namespace': actual_namespace,
				'app_ids': ';'.join(map(lambda x: x.get('namespace'), options['hotels_list_mobile'])),
				'url_booking': '%s/booking0' % get_config_property_value(KEY_DOMAIN)
			}

			options['hotels_list_mobile'] = [all_hotels_dict] + options['hotels_list_mobile']
		else:
			mini_dict = dict(get_web_dictionary(language))
			mini_dict['hotels'] = self.get_hotels(language)
			mini_dict['closed_chain'] = json.dumps(get_range_hotel_closed(language))
			options['applicationIds'] = ';'.join(map(lambda x: x.get('namespace'), mini_dict['hotels']))
			options['hotels_list_html'] = self.buildTemplate_2("_hotel_selector.html", mini_dict, False, 'blaucorpo')
			options["booking_no_hide"] = True

		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		return options

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		parent_data = super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate)
		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter_flexslider.html',
		}
		template = templateSectionsDict.get(sectionType, parent_data)
		return template

	def getParamsForSection(self, section, language):
		result = {}

		if section['sectionType'] == 'Galeria de Imagenes':
			result = self.getPicturesForGallerySection(language)

		elif section['sectionType'] == "Mis Reservas":
			result = super(TemplateHandler, self).getParamsForSection(section, language)
			available_hotels = self.get_hotels(language)
			result['all_namespaces'] = map(lambda x: x.get('namespace'), available_hotels)
			result['disable_content'] = True

		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def buildContentForSection(self, sectionFriendlyUrl, language,
							   sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
		currentSection = self.getSectionParams(sectionFriendlyUrl, language)

		if currentSection:
			if user_agent_is_mobile():
				html_to_render = self.getHtmlTypeSectionMobile(currentSection, language)

				if html_to_render:
					return html_to_render
				else:
					additionalParams['custom_elements'] = self.getHtmlExtraBannersMobile(currentSection, language)

				additionalParams['get_subtitle'] = True

		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate,
																   additionalParams)

	def getHtmlTypeSectionMobile(self, section, language):
		section_type = section['sectionType']
		advance_properties = self.getSectionAdvanceProperties(section, language)
		language_dict = get_web_dictionary(language)
		if section_type == "Habitaciones":
			args = {
				'is_mobile': True
			}
			args['class_section'] = 'rooms_section'
			rooms = []
			if advance_properties.get('rooms_sections'):
				sections = advance_properties['rooms_sections'].split(';')
				for sec in sections:
					ind_room = get_section_from_section_spanish_name_with_properties(sec, language)
					if ind_room.get('big_room'):
						ind_room_dict = {
							'banner_big_room': ind_room,
							'banner_big_room_pictures': get_pictures_from_section_name(sec, language)
						}

					else:
						ind_room_dict = {}
						not_banner_big_room = self.getPicturesProperties(language, sec)
						for x in not_banner_big_room:
							if x.get('gallery'):
								x['images'] = get_pictures_from_section_name(x['gallery'], language)
								x['info'] = get_section_from_section_spanish_name(x['gallery'], language)
						ind_room_dict['not_banner_big_room'] = not_banner_big_room

					rooms.append(ind_room_dict)

			args['rooms'] = rooms
			args.update(language_dict)

			return self.buildTemplate_2('banners/_banner_rooms.html', args, False, TEMPLATE_NAME)

		elif section_type == "Ofertas":
			return False

	def getHtmlExtraBannersMobile(self, section, language):
		extra_banners = self.getExtraBanners(section, language)
		language_dict = get_web_dictionary(language)
		extra_banners.update(language_dict)
		extra_banners['is_mobile'] = True
		extra_banners['base_web'] = base_web

		if section.get('sectionType') == 'Inicio':
			extra_banners['home'] = True

		result = "<div class='section_content'>"

		result += self.buildTemplate_2('_main_content.html', extra_banners, False, TEMPLATE_NAME)

		if extra_banners.get("minigallery_for_mobile"):
			args = {
				'minigallery_mobile': extra_banners["minigallery_for_mobile"]
			}
			result += self.buildTemplate_2("mobile_templates/2/_minigallery_v1.html", args, False)

		result += "</div>"

		return result

	def get_hotels(self, language):
		hotels = self.getPicturesProperties(language, "_hotel_selector")
		for hotel in hotels:
			if hotel.get("closed_hotel"):
				hotel['closed_hotel'] = json.dumps(get_range_hotel_closed(specific_value=hotel['closed_hotel']))

		return hotels

header {
  position: absolute;
  top: 0;
  //bottom: 0;
  left: 0;
  right: 0;
  z-index: 110;
  min-width: 1140px;

  &::before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    right: 0;
    height: 280px;
    background: linear-gradient(0deg, rgba(255, 255, 255, 0) 0%, rgba(0, 0, 0, 0.8) 100%);
  }

  #wrapper-header {
    position: relative;
    display: flex;
    flex-flow: row nowrap;
    text-transform: uppercase;
    padding: 20px 0;

    .widget_button {
      font-size: 17px;
    }

    > div {
      width: 45%;
      display: flex;
      flex-direction: row;
      font-size: .75rem;
      font-weight: 500;
      letter-spacing: 1px;
      @media (max-width: 1345px) {
        width: 50%;
      }
    }

    * {
      color: white;
    }

    #social {
      display: none;
    }

    .top_section_left {
      font-size: 14px;
      font-family: "PT Sans";
      font-weight: 300;
      width: 100% !important;

      #top-sections {

        > div {
          position: relative;
          display: inline-block;
          margin-right: 15px;
          padding-right: 15px;
          border-right: 1px solid white;
          i {
            margin-right: 10px;
          }

          &:first-of-type {
            margin-left: 15px;
          }

          &#lang {
            cursor: pointer;
            text-transform: uppercase;
            position: absolute;
            display: inline-block;
            vertical-align: middle;
            z-index: 99;
            top: 20px;

            .language_selected {
              color: white;
              font-weight: 700;

              i {
                font-weight: 400;
                font-size: 20px;
                vertical-align: middle;
                margin: 0 5px;
              }
            }

            .language_selector {
              display: none;
              position: absolute;
              top: 100%;
              right: 0;
              left: 0;
              z-index: 100;
              background-color: white;
              box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
              text-align: center;

              a {
                display: block;
                text-align: center;
                padding: 5px;
                color: $corporate_3;

                &:hover {
                  color: $corporate_1;
                }
              }
            }
          }

          &:last-of-type {
            font-weight: 700;
            border: none;
          }
        }
      }
    }

    .top_section_right {
      display: flex;
      justify-content: flex-end;
      padding-right: 80px;
      @media (max-width: 1345px) {
        padding-right: 10px;
        width: 20%;
      }

      a {
        font-weight: 700;
      }

      > div {
        position: relative;
        margin-left: 15px;
        padding-left: 15px;
        border-left: 1px solid white;

        &#lang {
          cursor: pointer;
          text-transform: uppercase;
          position: absolute;
          display: inline-block;
          vertical-align: middle;
          z-index: 99;
          top: 20px;
          right: 15px;

          .language_selected {
            color: white;
            font-weight: 700;

            i {
              font-weight: 400;
              font-size: 20px;
              vertical-align: middle;
              margin: 0 5px;
            }
          }

          .language_selector {
            display: none;
            position: absolute;
            top: 100%;
            right: 0;
            left: 0;
            z-index: 100;
            background-color: white;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
            text-align: center;

            a {
              display: block;
              text-align: center;
              padding: 5px;
              color: $corporate_3;

              &:hover {
                color: $corporate_1;
              }
            }
          }
        }

        &::before {
          position: absolute;
          content: '';
          width: 1px;
          top: 0;
          bottom: 0;
          left: 0;
          background-color: white;
        }

        &:first-of-type::before {
          display: none;
        }
      }
    }
  }

  nav {
    position: relative;
    font-family: "PT Sans";
    font-weight: 300;

    &#main_menu {

      .container12 {
        display: flex;
        flex-flow: row nowrap;
        justify-content: space-between;
        align-items: center;

        #main-sections-inner {
          width: 42%;
          display: flex;
          flex-flow: row nowrap;
          justify-content: space-between;


          .main-section-div-wrapper {
            position: relative;

            a {
              color: white;
              position: relative;
              text-transform: uppercase;
              font-size: 15px;
              font-weight: 700;

              span {
                white-space: nowrap;
              }

              &:hover {
                &::before {
                  position: absolute;
                  content: '';
                  left: 0;
                  right: 0;
                  bottom: -5px;
                  height: 1px;
                  background-color: white;
                }
              }
            }

            &#section-active {

              a {
                &::before {
                  position: absolute;
                  content: '';
                  left: 0;
                  right: 0;
                  bottom: -5px;
                  height: 1px;
                  background-color: white;
                }
              }
            }
          }
        }

        #logoDiv {
          width: 16%;
          text-align: center;
          padding: 0 10px;
          margin: 0;
        }
      }
    }
  }

  .widget_button {
    padding: 20px 15px;
    min-width: 290px;
    margin-top: -20px;
  }
}

#menu_scroll {
  z-index: 1001;
  display: block;
  position: fixed;
  width: 100%;
  height: auto;
  top: -100px;
  background-color: white;
  box-shadow: 0px 0px 40px -15px rgba(0, 0, 0, 0.6);
  @include transition(top, 1s);

  &:before {
    display: none;
    content: "";
    position: absolute;
    width: 1140px;
    left: 50%;
    transform: translateX(-50%);
    top: 100%;
    height: 20px;

  }

  &.scroll_active {
    top: 0px;
  }
  .menu_container {
    min-width: 1140px;
    width: 100%;
    display: grid;
    grid-template-columns: 13% 67% 20%;
    align-items: center;
    justify-items: center;

    .scroll_active {
      display: block;
    }

    #logoDiv {
      text-align: center;
      margin: 0;
      border: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      position: relative;

      img {
        height: auto;
        max-width: 100%;
        position: absolute;
        right: 0;
      }
    }

    #main-sections-inner {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-items: center;
      justify-content: space-evenly;
      text-align: center;
      padding: 19px 0;

      .main-section-div-wrapper {
        position: relative;
        padding-right: 5px;
        display: inline-block;

        &:last-child {
          padding-right: 0;
        }

        a {
          color: $corporate_2;
          position: relative;
          text-transform: uppercase;
          font-size: 14px;
          font-weight: 700;

          &:hover {
            &::before {
              position: absolute;
              content: '';
              left: 0;
              right: 0;
              bottom: -5px;
              height: 1px;
              background-color: $corporate_2;
            }
          }
        }

        &#section-active {
          a {
            &::before {
              position: absolute;
              content: '';
              left: 0;
              right: 0;
              bottom: -5px;
              height: 1px;
              background-color: $corporate_2;
            }
          }
        }
      }
    }
  }
}
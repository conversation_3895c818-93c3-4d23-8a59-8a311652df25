


{% if show_hotels %}
    <div class="hotels_wrapper list">
        {% for hotel in list_hotels %}
            <div class="hotel {{ hotel.region_class|safe }}">
                <div class="hotel_image">
                    <img src="{{ hotel.gallery.0.servingUrl|safe }}=s800" alt="{{ hotel.name|safe }}">
                    <div class="price">{{ T_desde }} <span>{{ hotel.price|safe }}€</span> {{ hotel.price_text|safe }}</div>
                </div>
                <div class="hotel_info">
                    <h2>{{ hotel.name|safe }} {% for star in range(hotel.stars) %}*{% endfor %}</h2>
                    <h3>{{ hotel.region|safe }}</h3>
                    <p>{{ hotel.description|safe }}</p>
                    <div class="half servis">
                        <a href="{{ hotel.link|safe }}"><i class="fa fa-angle-right"></i>{{ T_ir_a_web }}</a><br>
                        {{ hotel.servicios|safe }}
                    </div>
                    <div class="half">
                        <a href="javaScript:booking_click('{{ hotel.namespace|safe }}')" class="booking_button" data-namespace="{{ hotel.namespace|safe }}" data-hotelname="{{ hotel.value|safe }}">{{ T_reservar }}</a>
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>
{% endif %}

{% if poi_hoteles %}
<!--# Custom JS map-->
<div class="all_hotels_map_wrapper map" style="display: none;">
    <div id="map" style="height: 600px;width: 100%;"></div>
</div>
<script>
    var styles = {
        default: null,
        Ohtels: [
                {
                    "featureType": "administrative",
                    "elementType": "labels.text.fill",
                    "stylers": [
                        {
                            "color": "#444444"
                        }
                    ]
                },
                {
                    "featureType": "landscape",
                    "elementType": "all",
                    "stylers": [
                        {
                            "color": "#f2f2f2"
                        }
                    ]
                },
                {
                    "featureType": "poi",
                    "elementType": "all",
                    "stylers": [
                        {
                            "visibility": "off"
                        }
                    ]
                },
                {
                    "featureType": "road",
                    "elementType": "all",
                    "stylers": [
                        {
                            "saturation": -100
                        },
                        {
                            "lightness": 45
                        }
                    ]
                },
                {
                    "featureType": "road.highway",
                    "elementType": "all",
                    "stylers": [
                        {
                            "visibility": "simplified"
                        }
                    ]
                },
                {
                    "featureType": "road.arterial",
                    "elementType": "labels.icon",
                    "stylers": [
                        {
                            "visibility": "off"
                        }
                    ]
                },
                {
                    "featureType": "transit",
                    "elementType": "all",
                    "stylers": [
                        {
                            "visibility": "off"
                        }
                    ]
                },
                {
                    "featureType": "water",
                    "elementType": "all",
                    "stylers": [
                        {
                            "color": "#46bcec"
                        },
                        {
                            "visibility": "on"
                        }
                    ]
                },
                {
                    "featureType": "water",
                    "elementType": "geometry.fill",
                    "stylers": [
                        {
                            "color": "#102f57"
                        }
                    ]
                }
            ]
      };

  var hotels = [];
  var locations = [];

  {% for poi in poi_hoteles %}
  {% if poi.lat and poi.lng %}
      hotels.push("{{poi.poi_description|safe}}");
      locations.push({lat: {{poi.lat|safe}}, lng: {{poi.lng|safe}}});
  {% endif %}
  {% endfor %}

  function initMap() {

        var map = new google.maps.Map(document.getElementById('map'), {
          zoom: 4,
          center: {lat: 35.437, lng: -3.819},
          styles: styles['Ohtels']
        });

        var markers = locations.map(function(location, i) {
          var marker = new google.maps.Marker({
            position: location,
            animation: google.maps.Animation.DROP,
            icon: '/img/{{ base_web }}/maps/marker-maps.png'
          });
            var infowindow = new google.maps.InfoWindow({
              content: hotels[i]
            });

            marker.addListener('click', function() {
              infowindow.open(marker.get('map'), marker);
            });
          return marker;
        });
        var markerCluster = new MarkerClusterer(map, markers,
            {imagePath: 'https://developers.google.com/maps/documentation/javascript/examples/markerclusterer/m'});

  }

</script>

<script src="/js/{{base_web}}/markerclusterer.js"></script>
<script>
    var array_apis = [
        'AIzaSyDC6UytqYMu85MqQlhUGkkCeoDSjZyktO4', // Checkin
        'AIzaSyDYEbenToRFQ6n1Thx3BGATNl5Szd1dJ7Y'  // secure-booking14
    ];
    var num_api = Math.floor((Math.random() * 2));
    $.getScript("https://maps.googleapis.com/maps/api/js?key="+array_apis[num_api]+"&callback=initMap");
</script>

{% else %}
<div class="all_hotels_map_wrapper">
    <iframe src="https://www.google.com/maps/d/u/0/embed?mid=zJfv756oSuko.kajIv20sN274" width="1140" height="480"></iframe>
</div>
{% endif %}

<script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/functions_mobile.js?v=1"></script>
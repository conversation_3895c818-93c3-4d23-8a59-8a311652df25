<header>
    <div id="wrapper-header" class="container12">
        <div id="lang">
            <div class="phone">
                <i class="fa icon-headset"></i>
                <span>{{ T_atencion_al_cliente }}</span>
                <i class="fa fa-angle-down"></i>
            </div>
            <div class="phone_wrapper">
                {% for destiny, destiny_element in destiny_hotels.items() %}
                {% for destination, hotel_list in destiny_element.destinos.items() %}
                {% for hotel_element in hotel_list %}
                    <div class="hotel" href="{{ hotel_element.link }}">{{ hotel_element.name|safe }}: <span>{{ hotel_element.telefono }}</span></div>
                {% endfor %}
                {% endfor %}
                {% endfor %}
            </div>
        </div>
    <script>$(window).load(function () {
        $("#lang").click(function () {
            $(this).toggleClass("opened");
        });
    });</script>
        <div class="menu_toggle"><div id="lines" class="has_transition_600"><hr class="has_transition_600 _1"><hr class="has_transition_600 _2"><hr class="has_transition_600 _3"></div></div>
        <div id="logoDiv"><a href="{{host|safe}}/">{% include "imago.html" %}</a></div>
        <div id="top-sections">
            {% for section in custom_main_sections[:4] %}
                {% if not section.subsections %}<a href="{{ host|safe }}/{{ section.friendlyUrl }}">{% else %}<div class="top_toggle">{% endif %}
                    {% if section.icon %}
                        {% if section.icon.0.description %}
                            <i class="fa {{ section.icon.0.description|safe }}"></i>
                        {% else %}
                            <img src="{{ section.icon.0.servingUrl|safe }}"/>
                        {% endif %}
                    {% endif %}
                    <span>{{ section.title|safe }}</span>
                    {% if section.subsections %}<ul>{% for subsection in section.subsections %}
                        <li class="top-section-subsection">
                            {% if subsection.title %}
                                <a href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrl }}"
                                   {% if sectionToUse and sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                    {{ subsection.title|safe }}
                                </a>
                            {% endif %}
                        </li>
                    {% endfor %}</ul>{% endif %}
                {% if not section.subsections %}</a>{% else %}</div>{% endif %}
            {% endfor %}
        </div>
    </div>
<nav id="main_menu">
    <div id="mainMenuDiv" class="container12">
        {% include "main_div.html" %}
    </div>
</nav>
</header>
<div class="left_floating">
    <div id="top-sections">
        {% for section in top_sections %}
            <a href="{{ host|safe }}/{{ section.friendlyUrl }}">
                {% if section.icon %}
                        {% if section.icon.0.description %}
                            <i class="fa {{ section.icon.0.description|safe }}"></i>
                        {% else %}
                            <img src="{{ section.icon.0.servingUrl|safe }}"/>
                        {% endif %}
                    {% endif %}
                <span>{{ section.title|safe }}</span></a>
        {% endfor %}
    </div><div id="lang">{% for key, value in language_codes.items() %}<a href="{{ hostWithoutLanguage }}/{{ key }}/" {% if  key == language %} class="selected" {% endif %}>{{ key|upper }}</a>{% endfor %}</div>
</div>
</div>

<div class="news_widget_background" {% if news_section_widget_pics.0 %}style="background-image: url({{ news_section_widget_pics.0.servingUrl|safe }});background-color: {{ news_section_widget_pics.0.title|safe }};"{% endif %}>
    <div class="container12">
        <div class="news_widget_wrapper">
            {% for item in news %}
                <div class="entry_widget effects_sass" sass_effect="slide_up_effect">
                    <div class="image">
                        <a href="/{{ path }}/{{ item.friendlyUrl }}">
                            <img src="{{ item.picture|safe }}=s360" alt="" lazy="true">
                        </a>
                    </div>
                    {% if item.tags %}
                        <div class="tags">{{ item.tags|safe }}</div>
                    {% endif %}
                    <div class="title"><a href="/{{ path }}/{{ item.friendlyUrl }}">{{ item.name|truncatewords:4|safe }}</a></div>
                    <div class="content">{{ item.shortDesc|truncatewords_html:50|safe }}</div>
                    <div class="links">
                        <div class="comments">
                            <span>
                                <i class="fal fa-comment"></i>
                                <b>{{ item.comments|length }}</b>
                            </span>
                        </div>
                        <div class="author">
                            <span>{{ item.author|safe }}</span>
                        </div>
                        <div class="date">
                            <span>{{ item.creationDate|safe }}</span>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
</div>
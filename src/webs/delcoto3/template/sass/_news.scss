// COPIED FROM estacada/_news_widget.scss

.news_widget_background {
  margin-top: 40px;
  .news_widget_wrapper {
    h2.news_widget_title {
      padding-top: 60px;
      text-transform: uppercase;
      font-size: 22px;
      color: $corporate_1;
      font-family: "Merriweather";
      span {
        display: block;
        color: black;
        font-size: 16px;
        font-family: "Source sans pro";
      }
      &:after {
        content: "";
        display: block;
        height: 4px;
        width: 30px;
        background: $corporate_1;
        margin: 20px auto;
      }
    }
  }
}

.news_widget_wrapper {
  font-size: 14px;
  text-align: center;
  //padding: 60px 0;
  .entry_widget {
    display: inline-block;
    background-color: white;
    vertical-align: top;
    width: 360px;
    text-align: center;
    margin: 0 5px 20px;
    color: #666;
    border: 1px solid #DDD;
    .image {
      width: 100%;
      height: 225px;
      overflow: hidden;
      margin-bottom: 20px;
      a {
        display: block;
        width: 100%;
        height: 100%;
        img {
          @include cover_image;
        }
      }
    }
    .tags {
      font-size: 80%;
      color: $corporate_1;
      padding: 0 20px;
      margin-bottom: 20px;
    }
    .title {
      font-weight: bold;
      text-transform: uppercase;
      padding: 0 20px;
      font-size: 20px;

      a {
        color: #666;
        &:hover {
          color: $corporate_1;
        }
      }
    }
    .content {
      font-size: 80%;
      padding: 20px;
      line-height: 180%;
      height: 200px;
      width: 100%;
      box-sizing: border-box;
      overflow: hidden;
    }
    .links {
      display: table;
      width: 100%;
      box-sizing: border-box;
      border-top: 1px solid #DDD;
      padding: 10px;
      .comments, .author, .date {
        display: table-cell;
        position: relative;
        font-size: 80%;
        height: 50px;
        text-align: center;
        span {
          display: block;
          width: 100%;
          @include center_xy;
        }
      }
      .comments {
        border-left: 1px solid transparent;
        border-right: 1px solid #DDD;
        i {
          border: 1px solid #DDD;
          border-radius: 50%;
          padding: 10px;
          font-size: 25px;
          color: #AAA;
          vertical-align: middle;
        }
        b {
          background-color: $corporate_1;
          color: white;
          padding: 3px 10px;
          border-radius: 50%;
          font-size: 10px;
          margin-left: -10px;
          vertical-align: middle;
        }
      }
      .author {
        border-right: 1px solid transparent;
        border-left: 1px solid transparent;
      }
      .date {
        border-left: 1px solid #DDD;
        border-right: 1px solid transparent;
        color: $corporate_1;
      }
    }
  }
}
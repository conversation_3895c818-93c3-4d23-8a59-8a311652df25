<nav id="main_menu" itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner">
    <div class="main_menu">{% for section in main_sections %}
        {% if section.subsections %}
            <a {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>{{ section.title|safe }}</a>
            <ul>
                {% for subsection in section.subsections %}
                    <li class="main-section-subsection {{ subsection.title|lower }}">
                        {% if subsection.title %}
                            <a href="{{ host|safe }}/{{ seoLinkString }}{{ subsection.friendlyUrlInternational }}"
                               {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                {{ subsection.title|safe }}
                            </a>
                        {% endif %}
                    </li>
                {% endfor %}
            </ul>
        {% else %}
            {% if section.title %}
                <a itemprop="url" {% if section.replace_link %}href="{{ section.replace_link|safe }}" target="_blank" {% else %} href="{{ host|safe }}/{{ seoLinkString }}{{ section.friendlyUrlInternational }}" {% endif %}
                {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
                    <span itemprop="name">{{ section.title|safe }}</span>
                </a>
            {% endif %}
        {% endif %}
        {% endfor %}
    </div>
    <div id="top-sections">
        {% for section in menu_extra %}
            <a {% if section.top_section_properties.replace_link %}href="{{ section.top_section_properties.replace_link|safe }}" target="_blank" {% else %}
                href="{{ host|safe }}/{{ section.friendlyUrl }}" {% endif %}>
                <span>{{ section.title|safe }}</span>
            </a>
        {% endfor %}
    </div>
    <div class="social">
        {%if facebook_id %}
            <a href="http://www.facebook.com/{{facebook_id}}" target="_blank" rel="nofollow">
                <i class="fab fa-facebook-f"></i>
            </a>
        {% endif %}
        {% if instagram_id %}
            <a href="http://www.instagram.com/{{ instagram_id }}" target="_blank" rel="nofollow">
                <i class="fab fa-instagram"></i>
            </a>
        {% endif %}
        {% if twitter_id %}
            <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank" rel="nofollow">
                <i class="fab fa-twitter"></i>
            </a>
        {% endif %}
        {% if linkedin_id %}
            <a href="http://www.linkedin.com/company/{{ linkedin_id }}/" target="_blank" rel="nofollow">
                <i class="fab fa-linkedin"></i>
            </a>
        {% endif %}
        {% if google_plus_id %}
            <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="nofollow">
                <i class="fab fa-google-plus"></i>
            </a>
        {% endif %}
        {% if youtube_id %}
            <a href="https://www.youtube.com/{{youtube_id}}" target="_blank" rel="nofollow">
                <i class="fab fa-youtube"></i>
            </a>
        {% endif %}
        {% if pinterest_id %}
            <a href="http://es.pinterest.com/{{ pinterest_id }}" target="_blank" rel="nofollow">
                <i class="fab fa-pinterest-p"></i>
            </a>
        {% endif %}
    </div>
    <img src="/img/{{ base_web }}/{{ namespace }}2.png" class="menu_image">
</nav>
<div class="destination_wrapper">
  <label for="destination"><span>{{ T_seleccionar }}</span></label>
  <div class="destination_field">
  <input class="destination" readonly="readonly" type="text" name="destination" placeholder="{{T_hotel}}">
  <input  type="hidden" id="default_destination_placeholder" name="default_destination_placeholder" class="default_destination_placeholder" value="{{T_hotel}}">

  <div class="right_arrow"></div>
  </div>
</div>

<div class="hotel_selector">
    <div class="hotel_selector_inner">
        <div class="close_hotel_selector"><i class="fal fa-times"></i></div>
        <ul>{% for hotel in hotels %}
            <li id="{{ hotel.namespace }}" class="{{ hotel.namespace }} hotel_selector_option">
                <span class="title_selector">{{ hotel.title|safe }}</span>
            </li>
            <input type="hidden" id="url_booking_{{ hotel.namespace }}" value={{ hotel.url_booking }}>
            <input type="hidden" id="namespace_{{ hotel.namespace }}" value={{ hotel.namespace }}>
        {% endfor %}</ul>
    </div>
</div>



@import "styles";
@import "styles_ipad";

.newsletter_wrapper .newsletter_container .newsletter_form:before {
  display: none;
}

.fancybox-content {
  width: initial !important;
  height: initial !important;
}

@media only screen and (min-device-width: 768px) and (max-device-width: 1024px) {
  .price_calendar_wrapper {
    width: fit-content !important;
  }
}

.banner_pics3_wrapper .title,
.banner_carousel_wrapper .title,
.bannerx3_wrapper .title,
.banner_map_wrapper .section .title {
  h2 {
    padding-left: 0;

    &::before {
      display: none;
    }
  }

  span {
    margin: -15px 0 0 165px;
  }
}

.banner_pics3_wrapper {
  .motif {
    position: absolute;
    left: -30px;
    top: 240px;
    z-index: -1;
  }
}

.bannerx3_wrapper {
  .motif {
    z-index: -1;
    top: -60px;
    right: -80px;
  }
}

.content_subtitle_wrapper {
  background-image: none !important;

  .content_subtitle_title {
    text-align: center;

    span {
      margin: -15px 0 0 0 !important;

      h1 {
        padding-left: 0;

        &::before {
          display: none;
        }
      }
    }
  }
}

.banner_text_wrapper .banner_text,
.banner_cycle_wrapper .banner_cycle .extra_text,
 header nav#main_menu {
  background-image: none;
}

.fancybox-container .fancybox-content {
  width: 100vw!important;
  height: 100vh!important;
}
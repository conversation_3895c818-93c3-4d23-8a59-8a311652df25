.menu_container {
    text-align: center;
    
    .title {
        margin-bottom: 30px;
        
        h2 {
            font-family: $title_family !important;
            font-weight: bold;
            font-size: 36px;
        }
    }
    
    .colors {
        text-align: center;
        padding-top: 20px;
        
        > li {
            list-style: none;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border-bottom: 5px solid rgba(0, 0, 0, .1);
            display: inline-block;
            margin: 0 10px;
            cursor: pointer;
            transition-duration: .2s;
            box-shadow: 0 2px 1px rgba(0, 0, 0, .2);
            
            &:hover {
                -webkit-transform: scale(1.2);
                transform: scale(1.2);
                border-bottom: 10px solid rgba(0, 0, 0, .15);
                box-shadow: 0 10px 10px rgba(0, 0, 0, .2);
            }
            
            &.active-color {
                -webkit-transform: scale(1.2) translateY(-10px);
                transform: scale(1.2) translateY(-10px);
                box-shadow: 0 10px 10px rgba(0, 0, 0, .2);
                border-bottom: 20px solid rgba(0, 0, 0, .15);
            }
            
            &:nth-child(1) {
                background-color: #2ecc71;
            }
            
            &:nth-child(2) {
                background-color: #D64A4B;
            }
            
            &:nth-child(3) {
                background-color: #8e44ad;
            }
            
            &:nth-child(4) {
                background-color: #46a1de;
            }
            
            &:nth-child(5) {
                background-color: #bdc3c7;
            }
        }
    }
    
    .banners_filter {
        margin-bottom: 10px;
        
        a {
            position: relative;
            display: inline-block;
            padding: 17px;
            margin: 0 10px;
            color: black;
            text-transform: uppercase;
            @include transition(all, .6s);
            
            &:before, &:after {
                font-size: 50px;
                color: $corporate_1;
                opacity: 0;
                font-family: $title_family;
                @include center_y;
                transform: translate(0%, -47%);
                @include transition(all, .6s);
            }
            
            &:before {
                content: '{';
                left: 25%;
            }
            
            &:after {
                content: '}';
                right: 25%;
            }
            
            &.active {
                color: $corporate_1;
                
                &:before, &:after {
                    opacity: 0.5;
                }
                
                &:before {
                    left: 0;
                }
                
                &:after {
                    right: 0;
                }
            }
            
            &:hover {
                &:before, &:after {
                    opacity: 0.5;
                }
                
                &:before {
                    left: 0;
                }
                
                &:after {
                    right: 0;
                }
            }
            
        }
    }
    
    .banner_menu {
        display: inline-flex;
        justify-content: center;
        flex-wrap: wrap;
        margin-bottom: 80px;
        
        .banner {
            display: inline-block;
            margin: 10px 5px;
            -webkit-box-shadow: -1px 3px 18px 0px rgba(0, 0, 0, 0.2);
            -moz-box-shadow: -1px 3px 18px 0px rgba(0, 0, 0, 0.2);
            box-shadow: -1px 3px 9px 0px rgba(0, 0, 0, 0.2);
            
            .menu_img {
                display: block;
                width: 100%;
                height: 200px;
                object-fit: cover;
            }
            
            a {
                position: relative;
                display: block;
                background: $corporate_1;
                color: white;
                line-height: 50px;
                height: 50px;
                font-size: 14px;
                text-align: left;
                padding-left: 15px;
                @include transition(all, .6s);
                
                i {
                    position: absolute;
                    display: block;
                    right: 15px;
                    top: 20px;
                }
                
                &:hover {
                    background: $black;
                }
            }
        }
    }
}

.missing_filters {
    display: none;
}
@charset "UTF-8";
/* line 1, ../../../../sass/booking/_booking_engine.scss */
.booking_widget {
  position: absolute;
  z-index: 400;
  top: 185px;
}

/* line 7, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title {
  background: #EF8E25;
  text-align: center;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 20px;
  padding-top: 5px;
  padding-bottom: 5px;
}
/* line 15, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title h4 {
  margin-top: 0;
  color: white;
}
/* line 20, ../../../../sass/booking/_booking_engine.scss */
.booking_form_title .booking_title_1,
.booking_form_title .booking_title_2 {
  display: none;
}

/* line 26, ../../../../sass/booking/_booking_engine.scss */
.booking_form {
  font-family: 'Source Sans Pro', sans-serif;
  padding: 20px;
  width: 260px;
  background: white;
}

/* line 33, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper {
  position: relative;
}
/* line 35, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper label {
  color: gray;
  font-size: 12px;
}
/* line 40, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper input {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  height: 40px;
  border: 0;
  width: 250px;
  padding-left: 10px;
}
/* line 48, ../../../../sass/booking/_booking_engine.scss */
.destination_wrapper .right_arrow {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  position: absolute;
  background: #EF8E25 url(/static_1/images/booking/flecha_motor_der.png) no-repeat center center;
  right: 2px;
  top: 30px;
  height: 35px;
  width: 35px;
}

/* line 62, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper {
  margin-right: 10px;
}
/* line 67, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper,
.stay_selection .departure_date_wrapper,
.stay_selection .rooms_number_wrapper {
  float: left;
  width: 80px;
}
/* line 74, ../../../../sass/booking/_booking_engine.scss */
.stay_selection .entry_date_wrapper label,
.stay_selection .departure_date_wrapper label,
.stay_selection .rooms_number_wrapper label {
  color: gray;
  font-size: 12px;
}

/* line 83, ../../../../sass/booking/_booking_engine.scss */
.date_box {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  cursor: pointer;
  position: relative;
  background: white;
  height: 40px;
}
/* line 90, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day,
.date_box .date_year {
  display: block;
  text-align: center;
}
/* line 96, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_day {
  text-transform: uppercase;
  color: #EF8E25;
  margin-left: 5px;
  margin-right: 5px;
  font-size: 16px;
  font-weight: bolder;
}
/* line 105, ../../../../sass/booking/_booking_engine.scss */
.date_box .date_year {
  color: white;
  font-size: 12px;
  height: 14px;
  line-height: 14px;
}

/* line 113, ../../../../sass/booking/_booking_engine.scss */
.room {
  clear: both;
  margin-bottom: 5px;
}
/* line 116, ../../../../sass/booking/_booking_engine.scss */
.room .room_title {
  margin-top: 25px;
}
/* line 120, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector {
  margin-right: 10px;
}
/* line 125, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector,
.room .children_selector,
.room .babies_selector {
  float: left;
  width: 80px;
}
/* line 131, ../../../../sass/booking/_booking_engine.scss */
.room .room_title label,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  display: block;
}
/* line 136, ../../../../sass/booking/_booking_engine.scss */
.room .room_title,
.room .adults_selector label,
.room .children_selector label,
.room .babies_selector label {
  color: gray;
  font-size: 12px;
}

/* line 145, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button {
  font-family: 'Source Sans Pro', sans-serif;
  position: relative;
  margin-top: 50px;
  text-align: left;
}
/* line 152, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .promocode_input {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  border: 0;
  padding: 0;
  margin-top: 10px;
  text-align: center;
  color: #EF8E25;
  width: 170px;
  height: 40px;
  font-size: 16px;
}
/* line 164, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button button {
  -moz-border-radius: 5px;
  -webkit-border-radius: 5px;
  border-radius: 5px;
  color: white;
  border: 0;
  float: right;
  background: #EF8E25;
  font-size: 16px;
  text-transform: uppercase;
  height: 40px;
  width: 80px;
  margin-top: 10px;
}
/* line 177, ../../../../sass/booking/_booking_engine.scss */
.wrapper_booking_button .spinner_wrapper {
  position: absolute;
  right: 30px;
  bottom: 20px;
}

/* line 184, ../../../../sass/booking/_booking_engine.scss */
.horizontal_engine {
  height: 379px;
  background: white !important;
}

/* line 189, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal {
  width: 600px;
  margin: 0 auto;
  padding-top: 172px;
}
/* line 194, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 199, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  border-left: 1px solid white;
  border-right: 1px solid white;
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
}
/* line 207, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form_title {
  display: none;
}
/* line 211, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .booking_form {
  width: 560px;
}
/* line 215, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .destination_wrapper {
  float: left;
}
/* line 219, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .stay_selection {
  float: right;
}
/* line 223, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .room {
  float: right;
}
/* line 227, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button {
  clear: both;
}
/* line 231, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .promocode_input {
  width: 260px !important;
}
/* line 235, ../../../../sass/booking/_booking_engine.scss */
.booking_widget_horizontal .wrapper_booking_button button {
  width: 170px !important;
}

/* line 241, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline {
  width: 1140px;
  height: 75px;
  margin: 0 auto;
  padding-top: 20px;
  background-color: gray;
}
/* line 248, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .hotel_selector {
  left: 20px;
  top: 70px;
}
/* line 253, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  padding-top: 0;
  padding-bottom: 0;
  position: relative;
  background-color: gray;
}
/* line 260, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form_title {
  display: none;
}
/* line 264, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking_form {
  width: 1140px;
  padding: 0;
}
/* line 269, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .destination_wrapper {
  float: left;
}
/* line 273, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection {
  float: left;
  margin-left: 90px;
}
/* line 277, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .stay_selection .entry_date_wrapper label,
.boking_widget_inline .stay_selection .departure_date_wrapper label,
.boking_widget_inline .stay_selection .rooms_number_wrapper label {
  color: #787878;
  font-size: 12px;
}
/* line 285, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room_list_wrapper {
  float: left;
  margin-left: 20px;
  margin-right: 20px;
  margin-top: 5px;
}
/* line 291, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room {
  float: right;
}
/* line 294, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title,
.boking_widget_inline .room .adults_selector label,
.boking_widget_inline .room .children_selector label,
.boking_widget_inline .room .babies_selector label {
  color: gray;
  font-size: 12px;
}
/* line 301, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .room .room_title {
  text-align: right;
}
/* line 307, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button {
  display: inline;
  margin-top: 10px;
}
/* line 311, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  float: none;
}
/* line 316, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .promocode_input {
  width: 200px !important;
  margin-right: 20px;
  margin-top: 19px;
  background-color: #5a5a5a;
}
/* line 323, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .wrapper_booking_button button {
  width: 170px !important;
}
/* line 327, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .selectric, .boking_widget_inline .date_box {
  background: #5a5a5a;
}

/* line 333, ../../../../sass/booking/_booking_engine.scss */
.booking_footer_message {
  margin-top: 65px;
}

/* line 338, ../../../../sass/booking/_booking_engine.scss */
.booking-form-center-text {
  text-align: center;
}

/* line 342, ../../../../sass/booking/_booking_engine.scss */
.babies_selector {
  margin-left: 10px;
}

/* line 346, ../../../../sass/booking/_booking_engine.scss */
.room_title.room_title_with_babies {
  float: none;
  margin-bottom: 5px;
  padding-top: 10px;
  color: #EF8E25;
  text-align: center;
  width: 100%;
  text-transform: uppercase;
}

/* line 359, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text {
  margin-top: 7px;
}
/* line 362, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .stay_selection {
  margin-left: 45px !important;
}
/* line 366, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_title.room_title_with_babies {
  margin-top: -30px;
  text-align: center;
}
/* line 371, ../../../../sass/booking/_booking_engine.scss */
.boking_widget_inline .booking-form-center-text .room_list_wrapper {
  margin-left: 60px;
}

/* line 379, ../../../../sass/booking/_booking_engine.scss */
.range-age {
  width: 55px;
  display: inline-block;
  float: right;
  white-space: nowrap;
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 405, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 {
  border-radius: 0;
}
/* line 408, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 .fancybox-skin {
  border-radius: 0;
}
/* line 412, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 .gif_wrapper {
  display: block;
  margin: 21px auto;
  width: 100%;
  height: 105px;
}
/* line 418, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 .gif_wrapper .default_line_loading {
  background-color: #3484b2;
  height: 100%;
  width: 3px;
  display: inline-block;
  -webkit-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -moz-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  -o-animation: sk-stretchdelay 2.3s infinite ease-in-out;
  animation: sk-stretchdelay 2.3s infinite ease-in-out;
  margin-right: 6px;
}
/* line 431, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 .description_top_popup_booking {
  display: block;
  font-family: Roboto, sans-serif;
  font-size: 20px;
  font-weight: lighter;
  color: gray;
}
/* line 439, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 .description_bottom_popup_booking {
  font-weight: bold;
  text-transform: uppercase;
  color: gray;
}
/* line 445, ../../../../sass/booking/_booking_engine.scss */
.fancy-booking-search_v2 .container_popup_booking {
  width: 555px;
  display: block;
  padding: 30px 0;
  box-sizing: border-box;
  margin: 7px;
  border: 1px solid #3483b2;
}

/* line 455, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine {
  width: auto !important;
  height: auto !important;
  top: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  left: 0 !important;
}
/* line 463, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-outer {
  background: transparent;
}
/* line 468, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine.custom_color_overlay .fancybox-skin {
  background: transparent;
}
/* line 473, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-skin {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.9);
}
/* line 482, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .fancybox-inner {
  overflow: visible;
}
/* line 486, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  position: fixed;
  top: 50%;
  left: 0;
  right: 0;
  margin: auto !important;
  -webkit-transform: translateY(-50%);
  -moz-transform: translateY(-50%);
  -ms-transform: translateY(-50%);
  -o-transform: translateY(-50%);
  transform: translateY(-50%);
}
/* line 499, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .container_popup_booking {
  border: 0 !important;
}
/* line 503, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking {
  font-weight: lighter;
}
/* line 506, ../../../../sass/booking/_booking_engine.scss */
.fancybox-wrap.fancy-booking-search_v2.full_screen_engine .description_bottom_popup_booking strong {
  font-weight: bold;
  text-decoration: underline;
  font-size: 14px;
}

/*=====================================================================
  Selectric
======================================================================*/
/* line 5, ../../../../sass/booking/_selectric.scss */
.selectricWrapper {
  position: relative;
  margin: 0 0 0px;
  width: 80px;
  cursor: pointer;
}

/* line 12, ../../../../sass/booking/_selectric.scss */
.selectricDisabled {
  filter: alpha(opacity=50);
  opacity: 0.5;
  cursor: default;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* line 23, ../../../../sass/booking/_selectric.scss */
.selectricOpen {
  z-index: 9999;
}

/* line 27, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 31, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect {
  position: relative;
  overflow: hidden;
  width: 0;
  height: 0;
}

/* line 38, ../../../../sass/booking/_selectric.scss */
.selectricHideSelect select {
  position: absolute;
  left: -100%;
  display: none;
}

/* line 44, ../../../../sass/booking/_selectric.scss */
.selectricInput {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  margin: 0 !important;
  padding: 0 !important;
  width: 1px !important;
  height: 1px !important;
  outline: none !important;
  border: none !important;
  _font: 0/0 a;
  background: none !important;
}

/* line 60, ../../../../sass/booking/_selectric.scss */
.selectricTempShow {
  position: absolute !important;
  visibility: hidden !important;
  display: block !important;
}

/* line 67, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectric {
  z-index: 9999;
}

/* line 72, ../../../../sass/booking/_selectric.scss */
.selectricOpen .selectricItems {
  display: block;
}

/* line 76, ../../../../sass/booking/_selectric.scss */
.selectric {
  background: white;
  position: relative;
  border-radius: 6px;
  height: 40px;
}

/* line 83, ../../../../sass/booking/_selectric.scss */
.selectric .label {
  display: block;
  overflow: hidden;
  font-size: 16px;
  line-height: 40px;
  color: #EF8E25;
  text-align: left;
  margin-left: 20px;
}

/* line 93, ../../../../sass/booking/_selectric.scss */
.selectric .button {
  zoom: 1;
  position: absolute;
  font: 0/0 a;
  overflow: hidden;
  margin: auto;
  top: 0;
  right: 2px;
  bottom: 0;
  width: 35px;
  height: 35px;
  border: 0;
  background: #EF8E25 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  padding: 0;
}

/* line 109, ../../../../sass/booking/_selectric.scss */
.selectricHover .selectric .button {
  border-top-color: #DDD;
}

/* Items box */
/* line 115, ../../../../sass/booking/_selectric.scss */
.selectricItems {
  display: none;
  position: absolute;
  overflow: hidden;
  top: 100%;
  left: 0;
  background: #F9F9F9;
  border: 1px solid #CCC;
  z-index: 9998;
  -webkit-box-shadow: 0 0 10px -6px;
  box-shadow: 0 0 10px -6px;
}

/* line 128, ../../../../sass/booking/_selectric.scss */
.selectricItems ul,
.selectricItems li {
  list-style: none;
  padding: 0;
  margin: 0;
  min-height: 20px;
  line-height: 20px;
  font-size: 12px;
}

/* line 138, ../../../../sass/booking/_selectric.scss */
.selectricItems li {
  padding: 5px;
  cursor: pointer;
  display: block;
  border-bottom: 1px solid #EEE;
  color: #666;
  border-top: 1px solid #FFF;
}
/* line 146, ../../../../sass/booking/_selectric.scss */
.selectricItems li:hover {
  background: #F0F0F0;
  color: #444;
}
/* line 151, ../../../../sass/booking/_selectric.scss */
.selectricItems li.selected {
  background: #EFEFEF;
  color: #444;
  border-top-color: #E0E0E0;
}

/* line 158, ../../../../sass/booking/_selectric.scss */
.selectricItems li.disabled {
  background: #F5F5F5;
  color: #BBB;
  border-top-color: #FAFAFA;
  cursor: default;
}

/** DON'T FORGET TO DO THATTTTT CALENDAR DATEPICKER**/
/* line 2, ../sass/_template_specific.scss */
body {
  font-family: 'Roboto', sans-serif;
}

/* line 6, ../sass/_template_specific.scss */
.ui-state-default {
  border: 1px solid white !important;
}

/* line 10, ../sass/_template_specific.scss */
.ui-datepicker-title {
  color: white !important;
}

/* line 14, ../sass/_template_specific.scss */
.ui-widget-header {
  background: #EF8E25 !important;
}

/* line 18, ../sass/_template_specific.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: #EF8E25 !important;
  color: white;
}

/* line 23, ../sass/_template_specific.scss */
.tp-caption a {
  color: white !important;
}

/*===== Header =====*/
/* line 28, ../sass/_template_specific.scss */
header {
  background: white;
  position: relative;
  z-index: 5;
}

/* line 34, ../sass/_template_specific.scss */
.superheader {
  background: #EF8E25;
  color: white;
  padding: 5px 0;
  position: relative;
  z-index: 5;
}

/* line 42, ../sass/_template_specific.scss */
#wrapper-header {
  position: relative;
  overflow: hidden;
}

/* line 47, ../sass/_template_specific.scss */
#logoDiv {
  position: absolute;
  z-index: 100;
  top: 3px;
  left: 0px;
}

/* line 54, ../sass/_template_specific.scss */
#top-sections, .oficial {
  float: right;
}

/* line 58, ../sass/_template_specific.scss */
#top-sections {
  margin-top: 0px;
  border-left: 1px solid white;
}
/* line 61, ../sass/_template_specific.scss */
#top-sections a {
  color: white;
  text-transform: uppercase;
  padding-left: 70px;
  font-size: 12px;
  text-decoration: none;
}
/* line 68, ../sass/_template_specific.scss */
#top-sections a:hover {
  opacity: 0.8;
}

/* line 74, ../sass/_template_specific.scss */
.oficial {
  border-right: 1px solid white;
  text-align: center;
  font-size: 13px;
  font-weight: lighter;
  margin-right: 0;
  padding-right: 24px;
  padding-top: 4px;
}
/* line 83, ../sass/_template_specific.scss */
.oficial strong {
  font-weight: bold;
}

/* line 88, ../sass/_template_specific.scss */
.right-header {
  margin-left: 285px;
  margin-right: 0;
  margin-top: 15px;
  width: 855px !important;
  position: relative;
}

/* line 96, ../sass/_template_specific.scss */
#social {
  float: right;
}
/* line 99, ../sass/_template_specific.scss */
#social a {
  text-decoration: none;
}
/* line 102, ../sass/_template_specific.scss */
#social a:hover {
  opacity: 0.8;
}

/* line 108, ../sass/_template_specific.scss */
#lang {
  width: 85px;
  background: #e6e6e6;
  color: #868381;
  padding: 3px 17px 7px 25px;
  cursor: pointer;
  float: right;
  vertical-align: middle;
  font-size: 14px;
  display: block;
  position: relative;
  margin-right: 20px;
  margin-top: 1px;
  font-weight: lighter;
}
/* line 123, ../sass/_template_specific.scss */
#lang .arrow {
  display: inline-block;
  background: #C50B18 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
  float: right;
  width: 31px;
  height: 31px;
  margin-right: -4px;
  -moz-border-radius: 0px;
  -webkit-border-radius: 0px;
  border-radius: 0px;
  position: absolute;
  right: 0;
  top: 0;
}
/* line 135, ../sass/_template_specific.scss */
#lang #selected-language {
  display: inline-block;
  padding-top: 6px;
}

/* line 141, ../sass/_template_specific.scss */
#language-selector-options {
  display: none;
  position: absolute;
}
/* line 145, ../sass/_template_specific.scss */
#language-selector-options li {
  background: #e6e6e6;
  width: 117px;
  margin-left: -7px;
  margin-top: 6px;
  padding: 3px 0 5px 7px;
}
/* line 153, ../sass/_template_specific.scss */
#language-selector-options a {
  color: #787878;
}
/* line 156, ../sass/_template_specific.scss */
#language-selector-options a:hover {
  color: #EF8E25;
}

/* line 162, ../sass/_template_specific.scss */
.ticks_wrapper {
  display: table;
  float: right;
  margin-right: 70px;
}
/* line 167, ../sass/_template_specific.scss */
.ticks_wrapper .tick {
  display: inline-block;
  font-size: 11px;
  height: 0px;
  margin-top: -5px;
  margin-left: 30px;
}
/* line 174, ../sass/_template_specific.scss */
.ticks_wrapper .tick img {
  vertical-align: middle;
  padding-bottom: 3px;
}

/* line 181, ../sass/_template_specific.scss */
.oficial_web {
  display: table;
  color: #605E5D;
  float: right;
  font-size: 17px;
  margin-top: 6px;
  margin-right: 30px;
  font-weight: lighter;
}
/* line 190, ../sass/_template_specific.scss */
.oficial_web strong {
  font-weight: bold;
}

/*===== Menú =====*/
/* line 196, ../sass/_template_specific.scss */
#main_menu {
  clear: both;
  margin-top: 2px;
  width: 835px;
  float: right;
  padding-bottom: 2px;
}

/* line 204, ../sass/_template_specific.scss */
#mainMenuDiv {
  z-index: 99;
  clear: both;
  height: 40px;
}

/* line 210, ../sass/_template_specific.scss */
#mainMenuDiv a {
  text-decoration: none;
  text-transform: uppercase;
  color: #605E5D;
  display: inline-block;
  padding: 3px 0 9px;
}

/* line 222, ../sass/_template_specific.scss */
#section-active a {
  color: white;
}

/* line 227, ../sass/_template_specific.scss */
#main-sections-inner ul {
  display: none;
}

/* line 231, ../sass/_template_specific.scss */
#main-sections-inner div:hover > ul {
  display: block;
}

/* line 235, ../sass/_template_specific.scss */
#main-sections-inner div ul {
  position: absolute;
}

/* line 239, ../sass/_template_specific.scss */
#main-sections-inner li ul {
  position: absolute;
}

/* line 243, ../sass/_template_specific.scss */
#main-sections-inner div li {
  float: none;
  display: block;
}

/* line 248, ../sass/_template_specific.scss */
#main-sections-inner {
  text-align: justify;
  height: 40px;
}

/* line 253, ../sass/_template_specific.scss */
#main-sections-inner:after {
  content: ' ';
  display: inline-block;
  width: 100%;
  height: 0;
}

/* line 260, ../sass/_template_specific.scss */
#main-sections-inner > div {
  display: inline-block;
}

/* line 264, ../sass/_template_specific.scss */
.main-section-div-wrapper a {
  line-height: 30px;
  text-transform: uppercase;
  font-weight: 400;
  font-size: 13px;
}

/* line 271, ../sass/_template_specific.scss */
.main-section-div-wrapper a:hover {
  color: #9D9CA1;
}

/* line 275, ../sass/_template_specific.scss */
#mainMenuDiv .link-reservar a {
  color: white;
  background: #9D9CA1;
  padding: 3px 5px 5px;
}

/* line 281, ../sass/_template_specific.scss */
.main-section-div-wrapper {
  padding: 0 10px;
}
/* line 284, ../sass/_template_specific.scss */
.main-section-div-wrapper#section-active, .main-section-div-wrapper:hover {
  background: #EF8E25;
}
/* line 286, ../sass/_template_specific.scss */
.main-section-div-wrapper#section-active a, .main-section-div-wrapper:hover a {
  color: white !important;
}

/*======== Booking Widget =======*/
/* line 293, ../sass/_template_specific.scss */
.booking_form {
  background: rgba(0, 0, 0, 0.43);
}

/* line 297, ../sass/_template_specific.scss */
.booking_widget {
  top: 245px;
}

/* line 301, ../sass/_template_specific.scss */
.selectric .button {
  background: #9D9CA1 url(/static_1/images/booking/flecha_motor.png) no-repeat center center !important;
}

/* line 305, ../sass/_template_specific.scss */
.wrapper_booking_button button {
  background: #9D9CA1;
  width: 110px;
  font-size: 15px;
  cursor: pointer;
}
/* line 311, ../sass/_template_specific.scss */
.wrapper_booking_button button:hover {
  background: #EF8E25;
}

/* line 316, ../sass/_template_specific.scss */
.wrapper_booking_button .promocode_input {
  width: 143px;
  font-size: 12px;
  color: #605E5D;
}
/* line 321, ../sass/_template_specific.scss */
.wrapper_booking_button .promocode_input::-webkit-input-placeholder {
  color: #605E5D;
  font-weight: 500;
}
/* line 326, ../sass/_template_specific.scss */
.wrapper_booking_button .promocode_input:-moz-placeholder {
  color: #605E5D;
  font-weight: 500;
}
/* line 331, ../sass/_template_specific.scss */
.wrapper_booking_button .promocode_input::-moz-placeholder {
  color: #605E5D;
  font-weight: 500;
}
/* line 336, ../sass/_template_specific.scss */
.wrapper_booking_button .promocode_input:-ms-input-placeholder {
  color: #605E5D;
  font-weight: 500;
}

/* line 342, ../sass/_template_specific.scss */
.date_box .date_day {
  color: #605E5D;
  font-size: 15px;
  padding-top: 2px;
  border-bottom-color: #605E5D;
}

/* line 349, ../sass/_template_specific.scss */
.date_box .date_year {
  color: #605E5D;
}

/* line 353, ../sass/_template_specific.scss */
.stay_selection .entry_date_wrapper, .stay_selection .departure_date_wrapper, .stay_selection .rooms_number_wrapper {
  margin-bottom: 4px;
}

/* line 357, ../sass/_template_specific.scss */
.booking_form {
  padding: 10px 20px 20px 20px;
}

/* line 361, ../sass/_template_specific.scss */
.stay_selection .entry_date_wrapper label, .stay_selection .departure_date_wrapper label, .stay_selection .rooms_number_wrapper label, .room .room_title, .room .adults_selector label, .room .children_selector label, .room .babies_selector label {
  color: white;
  font-weight: 300;
}

/* line 366, ../sass/_template_specific.scss */
.selectric {
  background: none;
}

/* line 370, ../sass/_template_specific.scss */
.selectric .label {
  margin-left: 0px;
  text-align: center;
  background: white;
  width: 39px;
  border-radius: 5px;
  line-height: 41px;
  color: #605E5D;
}

/* line 380, ../sass/_template_specific.scss */
.selectric .button {
  border: 3px solid white;
  margin-top: 0px;
  border-radius: 6px;
}

/*======= Content Subtitle ======*/
/* line 387, ../sass/_template_specific.scss */
.content_subtitle_wrapper {
  margin: 50px auto 55px;
  text-align: center;
}
/* line 391, ../sass/_template_specific.scss */
.content_subtitle_wrapper h3.content_subtitle_title {
  font-size: 21px;
  color: #EF8E25;
  margin-bottom: 24px;
}
/* line 397, ../sass/_template_specific.scss */
.content_subtitle_wrapper .content_subtitle_content {
  font-size: 12px;
  color: #605E5D;
  width: 980px;
  margin: auto;
}

/*======= Banners x2 ======*/
/* line 406, ../sass/_template_specific.scss */
.bannersx2_wrapper {
  display: table;
  margin-bottom: 55px;
}
/* line 410, ../sass/_template_specific.scss */
.bannersx2_wrapper a {
  text-decoration: none;
}
/* line 416, ../sass/_template_specific.scss */
.bannersx2_wrapper .bannerx2_element .exceded {
  display: block;
  width: 100%;
  position: relative;
  height: 225px;
  overflow: hidden;
}
/* line 423, ../sass/_template_specific.scss */
.bannersx2_wrapper .bannerx2_element .exceded .black_overlay {
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  opacity: 0;
}
/* line 430, ../sass/_template_specific.scss */
.bannersx2_wrapper .bannerx2_element .exceded .black_overlay span.overlay {
  background: rgba(0, 0, 0, 0.3);
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
/* line 439, ../sass/_template_specific.scss */
.bannersx2_wrapper .bannerx2_element .exceded .black_overlay span.see_more {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  color: white;
  width: 100%;
  margin: auto;
  height: 30px;
  font-size: 30px;
  font-style: italic;
  text-align: center;
}
/* line 455, ../sass/_template_specific.scss */
.bannersx2_wrapper .bannerx2_element .exceded img {
  width: 100%;
}
/* line 461, ../sass/_template_specific.scss */
.bannersx2_wrapper .bannerx2_element:hover .black_overlay {
  opacity: 1;
}
/* line 466, ../sass/_template_specific.scss */
.bannersx2_wrapper .bannerx2_element .bannerx2_description {
  text-align: center;
  background: #F0F0F0;
  padding: 20px 70px;
}
/* line 471, ../sass/_template_specific.scss */
.bannersx2_wrapper .bannerx2_element .bannerx2_description .title {
  color: #EF8E25;
  margin-bottom: 20px;
}
/* line 476, ../sass/_template_specific.scss */
.bannersx2_wrapper .bannerx2_element .bannerx2_description .content {
  font-size: 12px;
  color: #605E5D;
}

/*===== Mini Gallery =====*/
/* line 485, ../sass/_template_specific.scss */
.gallery_title {
  padding-top: 60px;
}

/* line 489, ../sass/_template_specific.scss */
.gallery_title, .services_title {
  text-transform: uppercase;
  color: #EF8E25;
  padding: 0px 59px 17px;
  display: table;
  font-size: 22px;
  margin: 0 auto 7px;
  font-weight: 500;
  background-size: 11px;
}
/* line 499, ../sass/_template_specific.scss */
.gallery_title strong, .services_title strong {
  font-weight: 300;
}

/* line 505, ../sass/_template_specific.scss */
.gallery-mosaic-item {
  float: left;
  width: 242px;
  margin: 2px;
  height: 178px;
  overflow: hidden;
  position: relative;
}
/* line 513, ../sass/_template_specific.scss */
.gallery-mosaic-item img {
  width: auto;
  max-width: none;
  height: 137%;
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}
/* line 531, ../sass/_template_specific.scss */
.gallery-mosaic-item img:hover {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

/* line 541, ../sass/_template_specific.scss */
.gallery-mosaic-item.minigallery-last-item {
  float: right;
  width: 100%;
  height: 100%;
  position: relative;
  height: 360px;
}
/* line 548, ../sass/_template_specific.scss */
.gallery-mosaic-item.minigallery-last-item img {
  width: auto;
  max-width: none;
  height: 97%;
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
  -webkit-transition: all .5s ease-in-out;
  -moz-transition: all .5s ease-in-out;
  -ms-transition: all .5s ease-in-out;
  -o-transition: all .5s ease-in-out;
  transition: all .5s ease-in-out;
}
/* line 566, ../sass/_template_specific.scss */
.gallery-mosaic-item.minigallery-last-item img:hover {
  -webkit-transform: scale(1.2);
  -moz-transform: scale(1.2);
  -ms-transform: scale(1.2);
  -o-transform: scale(1.2);
  transform: scale(1.2);
}

/* line 576, ../sass/_template_specific.scss */
.gallery-smalls, .gallery-big {
  margin-left: 0px;
  margin-right: 2px;
}

/* line 581, ../sass/_template_specific.scss */
.gallery-smalls {
  width: 373px;
}

/* line 585, ../sass/_template_specific.scss */
.gallery-big {
  width: 396px;
  height: 360px;
}

/* line 591, ../sass/_template_specific.scss */
.gallery-mosaic {
  margin: 17px auto 0px;
}

/* line 595, ../sass/_template_specific.scss */
.gallery_container {
  margin-left: 22px;
  display: table;
  margin-bottom: 58px;
}

/*========= Footer ========*/
/* line 602, ../sass/_template_specific.scss */
footer {
  background: #E6E5E3;
  padding-top: 50px;
}
/* line 606, ../sass/_template_specific.scss */
footer .wrapper_footer_columns {
  display: table;
  margin-bottom: 10px;
  padding-bottom: 15px;
  border-bottom: 2px solid white;
}
/* line 613, ../sass/_template_specific.scss */
footer .footer_column {
  text-align: center;
}
/* line 616, ../sass/_template_specific.scss */
footer .footer_column h3.footer_column_title {
  color: #6B6766;
  font-size: 24px;
  font-weight: 300;
  margin-bottom: 25px;
}
/* line 623, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description {
  font-size: 12px;
  color: #605E5D;
  line-height: 17px;
}
/* line 628, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description a {
  text-decoration: none;
}
/* line 630, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description a:hover {
  opacity: 0.8;
}
/* line 635, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description .description {
  width: 300px;
  margin: auto;
}
/* line 641, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description #newsletter #title_newsletter, footer .footer_column #footer_column_description #newsletter #form-newsletter label {
  display: none !important;
}
/* line 645, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description #newsletter #form-newsletter {
  margin-top: 26px;
}
/* line 648, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description #newsletter #form-newsletter input#suscEmail {
  width: 220px;
  height: 25px;
  border: 0px;
  display: inline-block;
}
/* line 655, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description #newsletter #form-newsletter button#newsletter-button {
  background: #EF8E25;
  color: white;
  border: 0;
  width: 95px;
  height: 28px;
  display: inline-block;
  text-transform: uppercase;
  font-size: 14px;
  cursor: pointer;
}
/* line 666, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description #newsletter #form-newsletter button#newsletter-button:hover {
  opacity: 0.8;
}
/* line 670, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description #newsletter #form-newsletter div#newsletterButtonExternalDiv {
  display: inline-block;
}
/* line 673, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description #newsletter #form-newsletter .newsletter_checkbox {
  margin-top: 10px;
}
/* line 675, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description #newsletter #form-newsletter .newsletter_checkbox a {
  text-decoration: underline;
  color: #605E5D;
}
/* line 679, ../sass/_template_specific.scss */
footer .footer_column #footer_column_description #newsletter #form-newsletter .newsletter_checkbox label[for="promotions"] {
  display: inline !important;
}
/* line 688, ../sass/_template_specific.scss */
footer div#facebook_like {
  width: 49.5%;
  float: left;
  text-align: right;
}
/* line 694, ../sass/_template_specific.scss */
footer #google_plus_one {
  width: 49.5%;
  float: right;
}
/* line 699, ../sass/_template_specific.scss */
footer div#div-txt-copyright {
  width: 57%;
  font-size: 13px;
  float: left;
}
/* line 705, ../sass/_template_specific.scss */
footer .footer-copyright.links {
  width: 40%;
  float: right;
  text-align: right;
  font-size: 12px;
  color: #605E5D;
}
/* line 712, ../sass/_template_specific.scss */
footer .footer-copyright.links a {
  text-decoration: none;
  font-size: 12px;
  color: #605E5D;
}

/*========= Rooms ========*/
/* line 721, ../sass/_template_specific.scss */
.rooms {
  background: #F0F0F0;
  margin-bottom: 20px;
  width: 560px;
  float: left;
  position: relative;
}

/* line 729, ../sass/_template_specific.scss */
.rooms_wrapper {
  display: table;
  margin-top: 10px;
  margin-bottom: 35px;
}

/* line 735, ../sass/_template_specific.scss */
.rooms-description {
  padding: 20px 20px 0;
  font-size: 12px;
  background: #F0F0F0;
  display: table;
}
/* line 741, ../sass/_template_specific.scss */
.rooms-description .description {
  color: #86858a;
  font-size: 14px;
  line-height: 18px;
  margin-bottom: 20px;
  font-weight: 500;
  padding-bottom: 10px;
}

/* line 751, ../sass/_template_specific.scss */
.rooms h3 {
  line-height: 16px;
  padding: 10px 10px 10px 0px;
  text-align: left;
  color: #EF8E25;
  font-size: 18px;
  text-transform: uppercase;
}

/* line 760, ../sass/_template_specific.scss */
.rooms-description .sub-description-room {
  font-family: 'nexabold';
  margin-top: 10px;
}

/* line 765, ../sass/_template_specific.scss */
.room-description .description-room {
  font-family: 'nexaregular';
  margin-top: 10px;
}

/* line 770, ../sass/_template_specific.scss */
.rooms .rooms-links {
  position: absolute;
  right: 10px;
  top: 251px;
}
/* line 775, ../sass/_template_specific.scss */
.rooms .rooms-links a {
  color: white;
}
/* line 778, ../sass/_template_specific.scss */
.rooms .rooms-links a img {
  padding-right: 10px;
}

/* line 785, ../sass/_template_specific.scss */
.rooms-img {
  display: block;
}

/* line 789, ../sass/_template_specific.scss */
.camera-room {
  position: absolute;
  top: 20px;
  right: 20px;
}

/* line 795, ../sass/_template_specific.scss */
.destino {
  font-family: Roboto, sans-serif;
}

/* line 799, ../sass/_template_specific.scss */
#room_block_1 h3 {
  background: url("/img/holis/icon-polynesia.png") no-repeat;
}

/* line 803, ../sass/_template_specific.scss */
#room_block_2 h3 {
  background: url("/img/holis/icon-hydros.png") no-repeat;
}

/* line 807, ../sass/_template_specific.scss */
#room_block_3 h3 {
  background: url("/img/holis/icon-palace.png") no-repeat;
}

/* line 811, ../sass/_template_specific.scss */
#room_block_4 h3 {
  background: url("/img/holis/icon-village.png") no-repeat;
}

/* line 815, ../sass/_template_specific.scss */
#rooms_block_1, #rooms_block_3 {
  margin-left: 0;
  margin-right: 10px;
}

/* line 820, ../sass/_template_specific.scss */
#rooms_block_2, #rooms_block_4 {
  margin-left: 10px;
  margin-right: 0;
}

/* line 825, ../sass/_template_specific.scss */
.room-description-hide {
  padding: 20px;
}
/* line 828, ../sass/_template_specific.scss */
.room-description-hide h4 {
  color: #EF8E25;
  margin-bottom: 15px;
  text-transform: uppercase;
}

/* line 835, ../sass/_template_specific.scss */
.btn-corporate {
  padding: 7px 10px 5px;
  background: #EF8E25;
  font-size: 18px;
  text-transform: uppercase;
}
/* line 841, ../sass/_template_specific.scss */
.btn-corporate a {
  text-decoration: none;
}

/* line 847, ../sass/_template_specific.scss */
a.visita-room {
  position: absolute;
  z-index: 100;
  top: 20px;
  right: 100px;
}

/*===== Interior ====*/
/* line 855, ../sass/_template_specific.scss */
body.inner_section {
  background-size: 100% auto !important;
  background-repeat: no-repeat !important;
  /*===== Flexslider ====*/
}
/* line 859, ../sass/_template_specific.scss */
body.inner_section div#wrapper_booking {
  height: 320px;
}
/* line 863, ../sass/_template_specific.scss */
body.inner_section .booking_widget {
  top: 140px;
}
/* line 867, ../sass/_template_specific.scss */
body.inner_section .content_subtitle_wrapper {
  background: white;
  padding: 40px 0px 55px;
  margin-top: 11px;
}
/* line 874, ../sass/_template_specific.scss */
body.inner_section .flex-inner-sliderprev {
  display: inline-block;
  background: url(/img/sunse/left_flexslider.png) no-repeat 0;
  background-color: #000;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#000, endColorstr=#000);
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
  background-color: rgba(0, 0, 0, 0.3);
  color: black;
  text-indent: -666em;
  overflow: hidden;
  margin-left: 30px;
  margin-top: -200px;
  z-index: 6;
  position: absolute;
  width: 34px;
  height: 59px;
  left: 30px;
}
/* line 893, ../sass/_template_specific.scss */
body.inner_section .flex-inner-slidernext {
  display: inline-block;
  background: url(/img/sunse/right_flexslider.png) no-repeat 0;
  background-color: #000;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#000, endColorstr=#000);
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=30)";
  background-color: rgba(0, 0, 0, 0.3);
  color: black;
  text-indent: -666em;
  overflow: hidden;
  margin-left: 880px;
  margin-top: -200px;
  z-index: 6;
  position: absolute;
  width: 34px;
  height: 59px;
  right: 60px;
}
/* line 912, ../sass/_template_specific.scss */
body.inner_section .top-slider-sections {
  position: relative;
  width: 980px;
  margin: auto;
  margin-bottom: 40px;
}

/*====== Galeria =======*/
/* line 922, ../sass/_template_specific.scss */
.section_title {
  font-size: 77px;
  font-weight: 200;
  color: #15abc3;
  text-align: center;
  width: 700px;
  margin: 25px auto 55px;
  line-height: 65px;
}

/* line 932, ../sass/_template_specific.scss */
.gallery_1 li .crop {
  width: 283px;
}

/* line 936, ../sass/_template_specific.scss */
.gallery_1 li {
  margin: 1px;
  float: left;
}

/* line 941, ../sass/_template_specific.scss */
ul.gallery_1 {
  margin: 12px 0px 35px;
  display: table;
}

/* line 946, ../sass/_template_specific.scss */
.gallery_1 li .crop img {
  min-width: 100%;
  min-height: 212px;
  width: auto;
  max-width: none;
}

/* line 953, ../sass/_template_specific.scss */
.wrapper_filt .gallery_1 li:first-of-type .crop img {
  width: auto;
}

/* line 957, ../sass/_template_specific.scss */
.content_automatic_wrapper {
  background: white;
  padding: 5px 15px;
  margin-bottom: 50px;
}

/* line 963, ../sass/_template_specific.scss */
.wrapper_filt .gallery_1 li {
  width: 275px !important;
}

/* line 967, ../sass/_template_specific.scss */
.wrapper_filt .gallery_1 li:first-of-type .crop img {
  width: auto !important;
}

/* line 971, ../sass/_template_specific.scss */
.gallery_filt_title {
  color: #EF8E25 !important;
}

/*===== Promotions =====*/
/* line 976, ../sass/_template_specific.scss */
.filters-wrappers {
  position: relative;
  width: 100%;
  min-width: 1140px;
  background-color: white;
  filter: progid:DXImageTransform.Microsoft.gradient(startColorstr=#255255255, endColorstr=#255255255);
  -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=85)";
  background: rgba(255, 255, 255, 0.85);
  margin-bottom: 50px;
  margin-top: 130px;
  height: 50px;
  padding-top: 30px;
}

/* line 990, ../sass/_template_specific.scss */
#nav-tabs {
  list-style-type: none;
  text-align: justify;
}

/* line 996, ../sass/_template_specific.scss */
#nav-tabs li {
  display: inline-block;
}

/* line 1000, ../sass/_template_specific.scss */
#nav-tabs .last-promo-filter {
  display: inline-block;
  width: 100%;
  /* if you need IE6/7 support */
  *display: inline;
  zoom: 1;
}

/* line 1009, ../sass/_template_specific.scss */
.offer-btn-filter a {
  color: #EF8E25;
  font-size: 18px;
  font-weight: 700;
  text-decoration: none;
  cursor: pointer;
  padding: 3px 10px 3px 10px;
}

/* line 1019, ../sass/_template_specific.scss */
.offer-btn-filter :hover, .offer-btn-filter.active a {
  color: #FFFFFF;
  background-color: #EF8E25;
  height: 50px;
  padding: 3px 10px 3px 10px;
  border-radius: 5px;
}

/* line 1028, ../sass/_template_specific.scss */
.message-top-promotions {
  box-sizing: border-box;
  background: white;
  padding: 20px;
  margin-bottom: 40px;
  width: 1130px;
  text-align: center;
}
/* line 1036, ../sass/_template_specific.scss */
.message-top-promotions p {
  font-weight: 400;
  font-size: 15px;
  line-height: 20px;
}

/* line 1043, ../sass/_template_specific.scss */
.promotion_wrapper_1 {
  float: left;
  width: 570px;
  margin-bottom: 10px;
}

/* line 1050, ../sass/_template_specific.scss */
.contFotoRooms {
  float: left;
  overflow: hidden;
  height: 185px;
  width: 285px;
}

/* line 1057, ../sass/_template_specific.scss */
.contFotoRooms img {
  height: 185px;
  width: 285px;
}

/* line 1062, ../sass/_template_specific.scss */
.block_description {
  float: left;
  width: 255px;
  height: 154px;
  background-color: white;
  padding: 20px 10px 10px 10px;
  text-align: center;
  position: relative;
}

/* line 1072, ../sass/_template_specific.scss */
.promotion-title {
  color: #EF8E25;
  font-weight: 700;
  font-size: 14px;
  line-height: 13px;
  margin-bottom: 10px;
}

/* line 1080, ../sass/_template_specific.scss */
.promotions_description {
  height: 64px;
  overflow: hidden;
  font-weight: 100;
  font-size: 12px;
  line-height: 16px;
  color: #787878;
}

/* line 1089, ../sass/_template_specific.scss */
.promotions-buttons-wrapper {
  position: absolute;
  display: block;
  top: 142px;
  left: 32px;
}

/* line 1096, ../sass/_template_specific.scss */
.promotion-left-center {
  left: 90px !important;
}

/* line 1100, ../sass/_template_specific.scss */
.promotion-view-more {
  cursor: pointer;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  color: white;
  border: 0;
  background-color: #606060;
  font-size: 12px;
  text-transform: uppercase;
  width: 100px;
  padding: 5px 20px 5px 20px;
  text-decoration: none;
  margin-right: 10px;
}

/* line 1118, ../sass/_template_specific.scss */
.promotion-view-more:hover {
  color: #EF8E25;
  background-color: #edeef0;
}

/* line 1123, ../sass/_template_specific.scss */
.title-full-promo {
  color: black;
  text-align: left;
  font-weight: 700;
  font-size: 14px;
  margin-bottom: 10px;
  color: #EF8E25;
}

/* line 1132, ../sass/_template_specific.scss */
.offers-wrappers {
  margin-bottom: 45px;
  margin-top: 10px;
}

/* line 1137, ../sass/_template_specific.scss */
.offers-wrappers button.btn-corporate {
  cursor: pointer;
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  -ms-border-radius: 2px;
  -o-border-radius: 2px;
  border-radius: 2px;
  color: white;
  border: 0;
  background: #EF8E25;
  font-size: 12px;
  text-transform: uppercase;
  width: 110px;
  padding: 5px 20px 5px 20px;
  text-decoration: none;
}

/* line 1155, ../sass/_template_specific.scss */
.description_popup *:not(.title-full-promo) {
  font-size: 14px;
  color: #787878;
}

/*========= My booking ========*/
/* line 1162, ../sass/_template_specific.scss */
#my-bookings-form-fields {
  text-align: center;
}

/* line 1166, ../sass/_template_specific.scss */
#my-bookings-form {
  margin-top: 30px;
}

/* line 1170, ../sass/_template_specific.scss */
#my-bookings-form-fields .bordeInput {
  border: 1px solid #787878 !important;
  height: 17px;
}

/* line 1175, ../sass/_template_specific.scss */
#my-bookings-form-fields label {
  display: block;
  text-align: center;
  margin-bottom: 5px;
  margin-top: 10px;
}

/* line 1182, ../sass/_template_specific.scss */
#my-bookings-form-fields input {
  display: block;
  margin: 0 auto;
  width: 170px;
}

/* line 1188, ../sass/_template_specific.scss */
#my-bookings-form-search-button {
  background: #EF8E25;
  color: white;
  width: 100px;
  font-size: 14px;
  margin-top: 15px;
  border: 0;
  border-radius: 5px;
}

/* line 1198, ../sass/_template_specific.scss */
#reservation {
  margin-left: 200px;
}

/* line 1202, ../sass/_template_specific.scss */
#cancel-button-container {
  margin-top: 20px;
  margin-left: 200px;
}

/* line 1208, ../sass/_template_specific.scss */
#cancelButton {
  display: block;
  background-color: #EF8E25;
  color: white;
  border: 0px;
  border-radius: 5px;
  height: 30px;
  width: 150px;
  font-size: 12px;
  text-transform: uppercase;
  display: none;
}

/* line 1221, ../sass/_template_specific.scss */
#cancelButton:hover {
  background-color: #9D9CA1;
  color: #EF8E25;
}

/* line 1226, ../sass/_template_specific.scss */
#cancellation-confirmation-button {
  margin-top: 10px;
  display: block;
  background-color: #EF8E25;
  color: white;
  border: 0px;
  border-radius: 5px;
  height: 30px;
  width: 180px;
  font-size: 12px;
  text-transform: uppercase;
}

/* line 1239, ../sass/_template_specific.scss */
#cancellation-confirmation-button:hover {
  background-color: #9D9CA1;
  color: #EF8E25;
}

/*====== Location and Contact ======*/
/* line 1246, ../sass/_template_specific.scss */
.page-localizacion #wrapper_content, .page-localizacion .how-to {
  background: rgba(252, 241, 235, 0.86);
  padding: 20px;
  margin-top: 200px;
  width: 1100px;
}
/* line 1253, ../sass/_template_specific.scss */
.page-localizacion .container12 .column6 {
  width: 530px;
}

/* line 1258, ../sass/_template_specific.scss */
.location-info-and-form-wrapper {
  background: white;
  display: table;
  padding-top: 20px;
  padding-bottom: 20px;
  margin-bottom: 55px;
  margin-top: 10px;
}

/* line 1267, ../sass/_template_specific.scss */
.location-info-and-form-wrapper h1 {
  font-weight: 700;
  font-size: 19pt;
  text-align: left;
  text-transform: uppercase;
  border-bottom: solid 1px #bebebe !important;
  margin-bottom: 30px;
  padding-bottom: 6px;
  color: #605E5D;
  width: 95%;
}

/* line 1279, ../sass/_template_specific.scss */
.location-info strong {
  font-weight: bold;
}

/* line 1283, ../sass/_template_specific.scss */
#location-description-intro {
  margin-bottom: 30px;
}

/* line 1287, ../sass/_template_specific.scss */
.iframe-google-maps-wrapper {
  margin-top: 30px;
  width: 100%;
  margin-bottom: 30px;
}

/* line 1294, ../sass/_template_specific.scss */
li.how-to-go {
  cursor: pointer;
  color: #EF8E25;
  background: url("/img/amera/icons_maps/walk.png") left center no-repeat;
  padding-left: 30px;
  margin-left: -10px;
}
/* line 1300, ../sass/_template_specific.scss */
li.how-to-go .car {
  background: url("/img/amera/icons_maps/car.png") left center no-repeat;
}

/* line 1307, ../sass/_template_specific.scss */
.form-contact #title {
  display: none !important;
}

/* line 1311, ../sass/_template_specific.scss */
.form-contact #google-plus {
  display: none !important;
}

/* line 1315, ../sass/_template_specific.scss */
.form-contact .fb-like {
  display: none !important;
}

/* line 1323, ../sass/_template_specific.scss */
.form-contact #contactContent .title {
  float: none !important;
  width: 100% !important;
}

/* line 1328, ../sass/_template_specific.scss */
.form-contact #contactContent .bordeInput {
  width: auto;
  margin-right: 5px;
}

/* line 1333, ../sass/_template_specific.scss */
.form-contact #contact .contInput label {
  display: block;
  margin-bottom: 10px;
  color: black;
  font-weight: 300;
}

/* line 1341, ../sass/_template_specific.scss */
.form-contact #contact .contInput input {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px !important;
  height: 30px;
  background-color: #E2E2E2;
  color: white;
}

/* line 1351, ../sass/_template_specific.scss */
.form-contact #contact .contInput textarea {
  display: initial;
  margin: 0 auto;
  width: 500px;
  border: 0px;
  background-color: #E2E2E2;
  color: white;
  margin-right: 35px;
}

/* line 1361, ../sass/_template_specific.scss */
.form-contact #contact-button-wrapper {
  padding-right: 0px !important;
  margin-right: 35px;
}

/* line 1366, ../sass/_template_specific.scss */
.form-contact #contact-button {
  border-radius: 0px !important;
  height: 30px !important;
  width: 130px !important;
  background: #605E5D !important;
  color: white !important;
  margin: auto !important;
  text-transform: uppercase !important;
  border: 0px !important;
  text-align: center !important;
  font-size: 18px;
  padding: 0px !important;
  line-height: 32px;
}

/* line 1382, ../sass/_template_specific.scss */
.form-contact #contact-button:hover {
  background-color: #EF8E25 !important;
}

/* line 1386, ../sass/_template_specific.scss */
.location-info {
  font-weight: 300;
  padding-left: 20px;
  box-sizing: border-box;
  font-size: 15px;
  color: #272727;
}

/* line 1394, ../sass/_template_specific.scss */
.how-to {
  margin-top: 0px !important;
  margin-bottom: 30px;
  font-weight: 300;
}
/* line 1400, ../sass/_template_specific.scss */
.how-to h3 {
  font-size: 30px;
  margin-bottom: 20px;
  color: #EF8E25;
}

/*====== Automatic Content =======*/
/* line 1408, ../sass/_template_specific.scss */
.content_automatic_wrapper {
  text-align: center;
  padding-top: 7px;
  color: #787878;
  font-size: 13px;
  margin-top: 12px;
  margin-bottom: 55px;
}
/* line 1416, ../sass/_template_specific.scss */
.content_automatic_wrapper h3.section-title {
  font-size: 21px;
  color: #EF8E25;
  margin-bottom: 24px;
  text-align: center;
  margin-top: 30px;
}

$fontawesome5: true;
@import "defaults";
@import "styles_mobile/2/2";
@import "mobile/club_styles";

body {
  font-family: $text_family;
  padding: 80px 0 130px 0;

  #ui-datepicker-div {
    .ui-datepicker-header a .ui-icon {
      background-image: none;
      transform: none !important;

      &:before {
        font-weight: 300;
        font-family: "Font Awesome 5 Pro", sans-serif;
      }
    }
  }
}

#full_wrapper_booking {
  .wrapper_booking_button {
    .submit_button {
      background-color: $corporate_1;
    }
  }
}

.btn_personalized_1 {
  @include btn_styles;
}

.btn_personalized_2 {
  @include btn_styles_2;
}

.icon_link {
  @include icon_link;
}

.section_content {
  .location_content .section-title {
    display: none;
  }

  > h1, .content_subtitle_title, .section_title, .location_content .section-subtitle {
    @include title_styles;
  }

  div.content, div.content_subtitle_description, .section-content, .contact_content_element {
    @include text_styles;
    width: auto;
    padding: 0 20px;
  }
}

.mobile_engine {
  &.open {
    height: 310px;

    .mobile_engine_action {
      bottom: 350px;
    }
  }
}

body {
  .main-owlslider {
    position: relative;

    .description_text {
      @include full_size;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 30;

      .description_wrapper {
        background-color: rgba(black, .2);
        padding: 15px 30px;
        border-radius: 10px;
        width: 60%;

        .title {
          display: block;
          font-family: $title_family;
          font-weight: 700;
          font-size: 20px;
          line-height: 26px;
          color: white;
        }

        .text {
          font-family: $text_family;
          font-size: 15px;
          font-weight: 400;
          color: white;
        }

        .link_section {
          @include full_size;
        }
      }
    }
  }

  .normal_section_mobile {
    padding-top: 20px;
  }
}

#full_wrapper_booking {
  padding-top: 30px;

  .booking_form_title {
    display: none;
  }

  .destination_wrapper {
    background: white !important;
    margin-bottom: 10px;
    width: calc(100% - 20px);
    position: relative;

    &:before {
      content: '\f594';
      display: block;
      font-family: "Font Awesome 5 Pro", sans-serif;
      font-weight: 300;
      font-size: 14px;
      color: #666;
      @include center_y;
      left: 7px;
      z-index: 2;
    }

    &:after {
      content: '\f078';
      display: block;
      font-family: "Font Awesome 5 Pro", sans-serif;
      font-weight: 300;
      font-size: 18px;
      color: #666;
      @include center_y;
      right: 15px;
      z-index: 2;
    }

    select {
      width: 100%;
      height: 45px;
      padding-left: 35px;
      box-sizing: border-box;
    }
  }

  .wrapper_booking_button .submit_button {
    background-color: $corporate_1;
  }
}

#my-bookings-form-fields {
  .selectHotel {
    font-size: 15px;
    padding: 1em;
    border-width: 0;
    background-color: white;
    box-sizing: border-box;
    width: 100%;
    border-radius: 5px;
    text-align: left;
    margin: 1em auto 0;
    border: 0.5em solid #F9F9F9;
  }

  #my-bookings-form-search-button, #my-bookings-form-modify-button {
    display: block;
    padding: 10px 0;
    box-sizing: border-box;
    font-size: 22px;
    text-transform: uppercase;
    width: 100%;
    border-radius: 5px;
    margin: auto;
    background-color: $corporate_1;
    color: white;
    border: none;
  }

  #my-bookings-form-modify-button {
    background: $corporate_2;
    margin-bottom: 10px;
  }
}

/* Banner club top */

.banner_club_top_full_wrapper {
  position: relative;
  overflow: hidden;
  padding: 20px;
  background-image: none !important;

  .banner_club_top_content {
    position: relative;
    z-index: 5;

    .content_title {
      @include title_styles;
      padding-bottom: 20px;
      text-align: center;
    }

    .content_text {
      @include text_styles;
    }
  }

  .banner_club_top_wrapper {
    .club_content {
    }

    .text {
      @include text_styles;
    }

    .carg_img_wrapper {
      .card_img {
        margin: 10px auto 30px;

        img {
          max-width: 100%;
        }
      }

      .title {
        @include title_styles;
        display: inline-block;
        margin-bottom: 20px;
      }

      .list {
        list-style: none;
        padding-right: 50px;

        li {
          position: relative;

          @for $number from 1 through 10 {
            &:nth-of-type(#{$number}) {
              &:before {
                content: "#{$number}";
                position: absolute;
                top: -3px;
                left: -40px;
                width: 30px;
                height: 30px;
                background-color: $corporate_1;
                border-radius: 50%;
                color: white;
                font-size: 18px;
                font-family: $title_family;
                font-style: italic;
                box-sizing: border-box;
                padding: 3px 0 0 1px;
              }
            }
          }

          &:not(:last-of-type) {
            padding-bottom: 20px;
          }
        }
      }
    }
  }
}

/* Club content */

.content_subtitle_wrapper.club_register {
  text-align: center;
  padding: 20px;

  .content_subtitle_title {
    @include title_styles;
    background-color: $black;
    font-style: italic;
    font-weight: 400;
    padding: 10px;
    margin: 0;
  }

  .content_subtitle_description {
    @include text_styles;
    border: 1px solid $corporate_1;
    padding: 15px 15px 15px 40px;

    .club_content_list {
      text-align: left;
      font-size: 15px;
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        position: relative;
        margin-bottom: 5px;

        &:before {
          content: '\f00c';
          font-family: "Font Awesome 5 Pro";
          position: absolute;
          top: 0;
          left: -28px;
          box-sizing: border-box;
          padding-left: 2px;
          font-weight: 700;
          color: #c1a47c;
          width: 20px;
          height: 20px;
          font-size: 12px;
          line-height: 18px;
          border: 2px solid $corporate_1;
          border-radius: 50%;
        }
      }
    }
  }
}

/* Banner icons */

.banner_icons_full_wrapper {
  position: relative;
  overflow: hidden;
  padding: 20px;

  .banner_icons_content {
    position: relative;
    z-index: 5;

    .content_title {
      @include title_styles;
      padding-bottom: 20px;
      text-align: center;
    }

    .content_text {
      @include text_styles;
      padding-top: 10px;
    }
  }

  .banner_icons_wrapper {
    position: relative;
    z-index: 5;

    .owl-stage-outer {
      padding: 20px 0;

      .owl-item {
        text-align: center;
      }
    }

    .banner_icon {
      position: relative;
      display: inline-block;
      vertical-align: middle;
      padding: 10px;
      text-align: center;

      .icon {
        position: relative;
        display: inline-block;

        i {
          color: $corporate_2;
          font-size: 46px;
        }
      }

      .text {
        position: relative;
        z-index: 1;
        @include title_styles;
        font-size: 14px;
        line-height: 20px;
        padding: 20px;
      }
    }

    .owl-nav {
      @include center_y;
      top: 28%;
      display: block;
      width: 100%;
      height: 0;
      text-align: center;
      bottom: 0;

      .owl-prev, .owl-next {
        position: absolute;
        right: 0;
        display: inline-block;
        vertical-align: middle;

        i {
          font-size: 26px;
          color: $corporate_1;
        }

        &.disabled {
          display: none;
        }
      }

      .owl-prev {
        right: auto;
        left: 0;
      }
    }
  }
}

/* Banner block */

.banner_block_full_wrapper {
  position: relative;
  overflow: hidden;
  padding: 20px;
  background-image: none !important;

  .banner_block_content {
    position: relative;
    z-index: 5;

    .content_title {
      @include title_styles;
      padding-bottom: 10px;
      text-align: center;
    }

    .content_text {
      @include text_styles;
      padding-top: 10px;
    }
  }

  .banner_block_wrapper {
    position: relative;
    z-index: 5;
    text-align: center;

    .banner_block {
      position: relative;
      display: inline-block;
      vertical-align: middle;
      width: 100%;
      height: 290px;
      overflow: hidden;

      img {
        @include center_image;
      }

      .banner_content {
        @include full_size;
        background-color: rgba(black, .8);
        @include transition(all, .6s);

        .banner_text {
          min-width: 50%;
          @include center_xy;

          .title {
            @include title_styles;
            text-transform: uppercase;
            margin-bottom: 10px;
            margin-top: 0;
          }

          .text {
            color: white;
            padding: 0 10px 20px;

            &.hotels_number {
              font-family: $title_family;
              font-size: 20px;
              text-transform: uppercase;

              .number {
                font-size: 56px;
                display: block;
              }
            }
          }
        }
      }

      &:not(:last-of-type) {
        margin-bottom: 20px;
      }
    }
  }
}

/* Banner carousel */

.banner_carousel_full_wrapper {
  position: relative;
  overflow: hidden;
  padding: 20px 0;
  background-image: none !important;

  .banner_carousel_content {
    position: relative;
    z-index: 5;

    .banner_carousel_title {
      @include title_styles;
      padding-bottom: 20px;
      text-align: center;
    }

    .banner_carousel_content {
      @include text_styles;
      padding-top: 10px;
    }
  }

  .banner_carousel_wrapper {
    position: relative;
    z-index: 5;
    text-align: center;

    .banner_carousel {
      position: relative;
      text-align: left;
      padding: 20px 0;

      .banner_content {
        position: relative;
        margin-top: 70px;
        padding: 20px;
        text-align: center;
        background-color: white;
        display: inline-block;
        width: 60%;
        z-index: 5;
        @include box_shadow;

        .title {
          @include title_styles;
        }

        .text {
          @include text_styles;
          font-size: 14px;
          padding: 20px 0;
        }
      }

      .img_wrapper {
        position: absolute;
        width: 90%;
        height: 450px;
        overflow: hidden;
        right: 0;
        @include box_shadow;

        img {
          @include center_image;
          width: auto;
        }
      }
    }

    .owl-nav {
      position: absolute;
      right: 20px;
      bottom: 0;
      display: inline-block;

      .owl-prev, .owl-next {
        display: inline-block;
        vertical-align: middle;

        &.disabled {
          display: none;
        }
      }

      .prev, .next {
        color: $corporate_1;
        font-size: 12px;
        margin: 0 5px;
        @include transition(opacity, .6s);

        i {
          margin: 0 5px;
        }

        &:hover {
          opacity: .8;
        }
      }
    }
  }

  &.individual_hotel, &:nth-of-type(odd) {
    background-color: $corporate_rgba;

    .img_wrapper {
      box-shadow: none !important;
      -webkit-box-shadow: none !important;
      -moz-box-shadow: none !important;
    }
  }
}

/* Banner offers */

.banner_offers_full_wrapper {
  position: relative;
  overflow: hidden;
  text-align: center;
  padding: 20px;

  .banner_offers_content {
    position: relative;
    z-index: 5;

    .content_title {
      @include title_styles;
      padding-bottom: 20px;
      text-align: center;
    }

    .content_text {
      @include text_styles;
      padding-top: 10px;
    }
  }

  .banner_offers_wrapper {
    position: relative;
    z-index: 5;
    text-align: center;
    @include box_shadow;

    .banner_offer {
      position: relative;
      display: inline-block;
      width: 100%;
      overflow: hidden;
      margin-bottom: 20px;

      .img_wrapper {
        position: relative;
        width: 100%;
        height: 250px;
        overflow: hidden;

        img {
          @include center_image;
        }
      }

      .banner_content {
        background-color: white;
        padding: 20px 40px 60px;

        .title {
          @include title_styles;
        }

        .text {
          @include text_styles;
          padding: 10px 0 20px;

          .hide_in_home {
            display: none;
          }
        }

        .btn_personalized_1, .btn_personalized_2 {
          margin: 15px 0 10px;
          height: 40px;
        }

        .btn_personalized_2 {
          padding: 10px 20px;
        }
      }

      &.hidden {
        display: none;
      }
    }


    .owl-nav {
      > div {
        position: absolute;
        bottom: 30px;
        transform: translateY(-50%);

        i {
          font-size: 35px;
          color: $corporate_1;
          font-weight: 300;
        }

        &.owl-prev {
          left: 40px;
        }

        &.owl-next {
          right: 40px;
        }
      }
    }
  }

  .see_more {
    display: none;
  }
}

/* Banner Club */

.banner_club_full_wrapper {
  position: relative;
  overflow: hidden;
  padding: 20px;
  text-align: right;
  background-image: none !important;

  .banner_club_content {
    position: relative;
    z-index: 5;

    .content_title {
      @include title_styles;
      padding-bottom: 20px;
      text-align: center;
    }

    .content_text {
      @include text_styles;
      padding-top: 10px;
    }
  }

  .banner_club_wrapper {
    position: relative;
    width: 90%;
    z-index: 5;
    margin: 10px auto;
    text-align: center;
    padding: 20px;
    display: inline-block;
    @include box_shadow;

    .title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 400;
      color: $corporate_1;
      font-family: $text_family;

      .subtitle {
        display: block;
        font-size: 14px;
        line-height: 20px;
        padding-top: 10px;
        font-weight: 400;
        color: $corporate_2;
      }
    }

    .banner_club_login {
      padding: 20px 0;
      text-align: center;

      .club_user_login {
        button {
          border-radius: 5px;
          border: none;
        }
      }

      .links_wrapper {
        text-align: center;
        padding-left: 20px;

        .forget_password, .register_club_link {
          display: inline-block;
          padding-top: 5px;
          font-size: 12px;
          font-family: $text_family;
          color: $corporate_2;
          cursor: pointer;
          @include transition(opacity, .6s);

          &:hover {
            opacity: .6;
          }
        }
      }
    }

    .text {
      @include text_styles;
      font-size: 14px;
      padding-bottom: 20px;
    }
  }
}

.input_club {
  display: inline-block;
  width: 180px;
  height: 45px;
  margin-bottom: 15px;
  border: 1px solid $corporate_1;
  border-radius: 5px;
  padding-left: 15px;
  font-size: 16px;
  font-family: $title_family;
  color: $corporate_1;

  &::-webkit-input-placeholder, &::-moz-placeholder, &:-ms-input-placeholder, &:-moz-placeholder {
    color: $corporate_1;
    font-family: $title_family;
    font-size: 16px;
  }

  &::placeholder {
    color: $corporate_1;
    font-family: $title_family;
    font-size: 16px;
  }
}

.main_recovery_title {
  font-family: "Spectral", serif;
  font-size: 26px;
  line-height: 38px;
  font-weight: 700;
  color: $corporate_1;
}

.forger_password_fancybox {
  width: 700px !important;
  @include center_xy;
  top: 50% !important;
  left: 50% !important;

  .fancybox-inner {
    width: 100% !important;
    padding: 20px;
  }

  .forget_password_popup {
    text-align: center;

    .main_recovery_title {
      @include title_styles;
    }

    #forget_email.input_club {
      width: 100%;
      margin: 20px 0;
    }
  }
}

.forget_password_popup {
  #forget_email.input_club {
    width: 100%;
  }
}

input.error {
  outline: 1px solid red;
}

/* Hoteles */
.hotel_list_wrapper,
.rooms_list_wrapper {
  padding: 20px;

  .hotel_list,
  .rooms_list {
    .hotel,
    .room {
      @include box_shadow;
      margin-bottom: 50px;

      .room_content {
        background-color: white;
        padding: 35px 20px;

        .title {
          @include title_styles;
          margin: 0 0 20px 0;
        }

        .text {
          @include text_styles;

          .hide_in_hotels {
            display: none;
          }
        }

        .hotel_icons_wrapper {
          margin-top: 30px;

          .hotels_icon_text {
            color: $corporate_2;
            font-size: 18px;
            font-weight: 400;
            font-family: $text_family;
            display: block;
          }

          .hotel_icons {
            display: inline-block;
            border-top: 1px solid $corporate_1;
            border-bottom: 1px solid $corporate_1;
            margin-top: 20px;

            .icon {
              position: relative;
              display: inline-block;
              vertical-align: middle;
              padding: 20px 5px;

              > i {
                display: inline-block;
                vertical-align: middle;
                color: $corporate_1;
                font-size: 34px;
              }

              .tooltiptext {
                display: inline-block;
                vertical-align: middle;
                font-size: 12px;
                margin-left: 3px;
                text-align: left;
                white-space: nowrap;
              }

              &:not(:last-of-type) {
                margin-right: 10px;
              }
            }
          }
        }

        .btn_personalized_1, .btn_personalized_2 {
          width: 200px;
          //height: 50px;
          font-size: 19px;
          font-weight: 300;
          letter-spacing: 2px;
          padding: 10px 20px;
          text-align: center;
          margin-top: 25px;

          &.button_promotion {
            margin-top: 20px;
          }
        }
      }

      .img_wrapper {
        position: relative;
        width: 100%;
        height: 300px;
        overflow: hidden;

        img {
          @include center_image;
          max-height: 100%;
        }
      }


      .gallery_wrapper {
        .owl-nav {
          > div {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);

            i {
              font-size: 35px;
              color: white;
              font-weight: 300;
            }

            &.owl-prev {
              left: 20px;
            }

            &.owl-next {
              right: 20px;
            }
          }
        }
      }
    }
  }
}

/* Form section club */
.form_suscribe_wrapper {
  #identifier {
    color: #71716d;

    &::-webkit-input-placeholder {
      color: #71716d;
    }
  }

  .hotel_selection_label {
    display: none;
  }
}

/* Form section contact */
.location_wrapper {
  form#contact {
    .hotels_select {
      display: none;
    }
  }
}

/* Banner gallery */

.banner_gallery_wrapper {
  .banner_gallery {
    padding: 50px 20px;
    background: #ECECEC 70%;

    .pic {
      display: inline-block;
      width: 100%;
      height: 200px;
      overflow: hidden;
      position: relative;
      margin-right: 5px;

      img {
        position: absolute;
        top: 50%;
        left: 50%;
        -webkit-transform: translate(-50%, -50%);
        -moz-transform: translate(-50%, -50%);
        -ms-transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        min-width: 100%;
        min-height: 100%;
        max-width: none;
        @include transition(all, 3s);
      }

      &:hover {
        img {
          transform: translate(-50%, -50%) scale(1.5);
        }

        .see_more {
          width: 100%;
          left: 0;

          i.fa {
            opacity: 1;
          }
        }
      }

      .see_more {
        @include full_size;
        left: auto;
        background-color: rgba($corporate_1, .8);
        width: 0;
        overflow: hidden;
        @include transition(width, 1s);

        i.fa {
          @include center_xy;
          background-color: $corporate_1;
          border-radius: 50%;
          border: 1px solid white;
          color: white;
          font-size: 30px;
          width: 50px;
          height: 50px;
          opacity: 0;
          @include transition(opacity, .6s);

          &:before {
            @include center_xy;
          }
        }
      }

      &:nth-child(4n) {
        margin-right: 0;
      }
    }
  }
}

/* section gallery */

.gallery_1 {
  &:last-child {
    margin-bottom: 40px;
  }
}

/* section my bookings */

.my_reservation_section {
  .section-title {
    @include title_styles;
    margin-top: 20px;
  }

  .default_reservation_text {
    @include text_styles;
    width: auto;
    padding: 0 20px;
  }
}

/* Individual hotel */

.individual_room_picture {
  .individual_room_title {
    font-family: $title_family;
  }
}

.iframe_map_wrapper {
  iframe {
    width: 100%;
  }
}

/* Accordion banner */

.accordion_banner_wrapper {
  display: block;
  box-sizing: border-box;
  padding: 20px 15px;

  * {
    box-sizing: border-box;
  }

  .accordion {
    display: inline-block;
    vertical-align: top;
    width: 100%;

    .accordion_element {
      margin-bottom: 10px;
    }

    .accordion_title {
      position: relative;
      display: block;
      padding: 10px;
      background-color: $corporate_1;
      text-transform: uppercase;
      font-weight: 600;
      font-size: 14px;
      color: white;

      i.fa {
        margin-right: 10px;
      }

      &:before {
        content: '\f107';
        display: inline-block;
        font: normal normal normal 14px/1 "Font Awesome 5 Pro";
        font-weight: 900;
        text-rendering: auto;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        @include center_y;
        right: 10px;
      }
    }

    .accordion_description {
      background-color: $lightgrey;
      padding: 10px;
      margin: 5px 0;
      font-size: 14px;
      display: none;

      strong {
        font-weight: 700;
      }

      a {
        color: $corporate_1;
      }
    }
  }

  .side_text {
    background-color: $lightgrey;
    padding: 17px 20px;
    width: 100%;
    line-height: 20px;
    font-size: 14px;
    font-weight: 400;

    strong {
      font-weight: 700;
    }

    a {
      color: $corporate_1;
    }
  }
}

.form_suscribe_full_wrapper {
  label.error {
    display: block;
  }
}


form#contact {
  .fieldContain.hotels_select select {
    font-size: 15px;
    background: #eaeaea;
    width: 100%;
    box-sizing: border-box;
    border: 0;
    border-radius: 5px;
    margin-top: 1em;
    padding: 1.6em 2em;
    font-family: "Arial", sans-serif;
  }


  .g-recaptcha {
    margin-top: 40px;
  }
}

@media screen and (max-width: 320px) {
  .hotel_list_wrapper .filter_hotels_wrapper .filter_destiny .btn_personalized_2 {
    padding: 10px 12px;
  }
}

.banner_block_full_wrapper,
.banner_block_rooms_full_wrapper {
  position: relative;
  overflow: hidden;
  padding: 20px;
  background-image: none !important;

  .banner_block_content {
    position: relative;
    z-index: 5;

    .content_title {
      @include title_styles;
      padding-bottom: 10px;
      text-align: center;
    }

    .content_text {
      @include text_styles;
      padding-top: 10px;
    }
  }

  .banner_block_wrapper {
    position: relative;
    z-index: 5;
    text-align: center;

    .banner_block {
      position: relative;
      display: inline-block;
      vertical-align: middle;
      width: 100%;
      height: 290px;
      overflow: hidden;

      img {
        @include center_image;
      }

      .banner_content {
        @include full_size;
        background-color: rgba(black, .8);
        @include transition(all, .6s);

        .banner_text {
          min-width: 50%;
          @include center_xy;

          .title {
            @include title_styles;
            text-transform: uppercase;
            margin-bottom: 10px;
            margin-top: 0;
          }

          .text {
            color: white;
            padding: 0 10px 20px;

            &.hotels_number {
              font-family: $title_family;
              font-size: 20px;
              text-transform: uppercase;

              .number {
                font-size: 56px;
                display: block;
              }
            }
          }
        }
      }

      &:not(:last-of-type) {
        margin-bottom: 20px;
      }
    }

    .owl-nav {
      display: flex;
      justify-content: space-between;
      position: absolute;
      left: 20px;
      right: 20px;
      bottom: -50px;

      > div {
        margin: 0 10px;

        span {
          display: none;
        }

        i {
          font-size: 35px;
          color: $corporate_1;
          font-weight: 300;
        }

        &.disabled {
          opacity: 0;
        }
      }
    }
  }
}

.banner_block_rooms_full_wrapper {
  padding-bottom: 70px;
}

.banner_cards_carousel_wrapper {
  padding: 70px 0;
  position: relative;


  .bg_image {
    @include full_size;
    opacity: .15;

    img {
      @include cover_image;
      filter: grayscale(100%);
    }
  }

  .cards_container {
    position: relative;
    width: 100%;
    z-index: 1;

    .top_banner {
      .content_title {
        text-align: center;

        .title {
          @include title_styles;
        }
      }
    }

    .cards_wrapper {
      display: flex;
      justify-content: space-between;

      &.owl-carousel {
        .card {
          width: 100%;
        }
      }

      .card {
        width: calc((100% / 3) - 20px);
        margin-right: 20px;

        &:last-child {
          margin-right: 0;
        }

        .picture_wrapper {
          height: 280px;

          img {
            @include cover_image;
          }
        }

        .card_content {
          padding: 30px;

          .content_title {
            text-align: center;
            margin-bottom: 20px;

            .title {
              @include title_styles;
              font-weight: 400;

              span {
                text-transform: uppercase;
              }
            }
          }

          .desc {
            @include text_styles;
            text-align: center;
            margin-bottom: 20px;
          }

          .links_wrapper {
            display: block;
            text-align: center;

            .btn_link_more {
              @include btn_styles;
            }
          }
        }
      }


      .owl-nav {
        display: flex;
        justify-content: space-between;
        position: absolute;
        left: 20px;
        right: 20px;
        bottom: -30px;
        //transform: translateX(-50%);

        > div {
          margin: 0 10px;

          i {
            font-size: 35px;
            color: $corporate_1;
            font-weight: 300;
          }

          &.disabled {
            opacity: 0;
          }
        }
      }
    }
  }
}
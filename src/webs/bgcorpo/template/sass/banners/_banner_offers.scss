.banner_offers_full_wrapper {
  position: relative;
  overflow: hidden;
  text-align: center;
  padding: 50px calc((100% - 1140px) / 2) 20px;
  .banner_offers_content {
    position: relative;
    z-index: 5;
    .content_title {
      @include title_styles;
      padding-bottom: 30px;
      text-align: center;
    }
    .content_text {
      @include text_styles;
      padding-top: 20px;
    }
  }
  .banner_offers_wrapper {
    position: relative;
    z-index: 5;
    text-align: center;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    .banner_offer {
      position: relative;
      display: inline-block;
      width: calc((100% / 3) - 30px);
      overflow: hidden;
      margin-bottom: 30px;
      @include box_shadow;
      .img_wrapper {
        position: relative;
        width: 100%;
        height: 280px;
        overflow: hidden;
        img {
          @include center_image;
        }
      }
      .banner_content {
        background-color: white;
        padding: 20px 40px 90px;

        .title {
          @include title_styles;
          font-weight: 400;
          font-style: italic;
        }

        .text {
          @include text_styles;
          padding: 10px 0 20px;

          .hide_in_home {
            display: none;
          }
        }

        .offers_buttons {
          bottom: 30px;
          @include center_x;
          width: 300px;
          .btn_personalized_1 {
            margin-left: 10px;
          }
          .icon_link {
            width: 40px;
            height: 40px;
          }
        }
      }
      &:not(:nth-of-type(3n)):not(:last-of-type) {
        margin-right: 15px;
      }
      &.hidden {
        display: none;
      }
    }
  }

  .see_more {
    padding: 10px 40px;
  }
}
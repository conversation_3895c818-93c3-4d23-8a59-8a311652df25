.users_registered_info {
  display: table;
  margin-bottom: 40px;
  width: 100%;

  .steps_block {
    width: 250px;
    float: left;
    border: 1px solid $corporate_1;

    .step_title {
      text-align: center;
      background: $corporate_1;
      color: white;
      padding: 15px 0;
      font-size: 13px;
    }

    .step_element {
      margin-bottom: 20px;

      .step_number, .step_description {
        display: inline-block;
      }

      .step_number {
        float: left;
        width: 35px;
        height: 35px;
        border-radius: 45px;
        text-align: center;
        background: $corporate_1;
        color: white;
        line-height: 36px;
        margin-right: 6px;
      }

      .step_description {
        width: 170px;
        line-height: 20px;
        color: #646464;
        font-weight: 300;
        font-size: 13px;
      }

      img.image_club {
        margin-top: 10px;
      }

      .conditions_step {
        line-height: 20px;
        color: #646464;
        font-weight: 300;
        font-size: 13px;
      }
    }

    .steps_follow {
      padding: 15px;
    }
  }

  .advatages_block {
    float: right;
    width: 865px;

    .advantages_title {
      text-align: center;
      background: $corporate_1;
      color: white;
      padding: 15px 0;
      font-size: 13px;
    }

    .advantages_description {
      border: 1px solid $corporate_2;
      padding: 30px 30px 21px;

      ul {
        -webkit-column-count: 2;
        -moz-column-count: 2;
        column-count: 2;

        li {
          background: url(/img/#{$base_web}/tick_circle.png) no-repeat top left;
          margin-bottom: 25px;
          padding-left: 27px;
          font-weight: 300;
          font-size: 13px;
          padding-top: 1px;
          line-height: 20px;
          color: #646464;
        }
      }
    }
  }
}

.form_suscribe_full_wrapper {
  position: relative;
  overflow: hidden;
  padding: 50px calc((100% - 1140px) / 2);
  &:before {
    background-color: $corporate_rgba !important;
  }
}

.form_suscribe_conditions {
  position: relative;
  overflow: hidden;
  padding: 50px calc((100% - 1140px) / 2);
  @include text_styles;
  font-size: 15px;
  text-align: center;
  line-height: 21px;
  background-color: white;
}

.form_suscribe_wrapper {
  background: #fafafa;
  padding: 40px 70px;
  @include box_shadow;

  .legend_label {
    font-size: 14px;
    font-weight: 300;
    color: #5a5a5a;
    display: block;
    text-align: center;
  }
}

.suscribe_form {
  input, textarea, select {
    position: relative;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    height: 40px;
    width: 100%;
    margin-bottom: 10px;
    border: 0;
    padding: 12px;
    font-weight: 300;
    font-size: 14px;
    background-color: white;
    border-bottom: 2px solid #d8d8d8;
    box-sizing: border-box;

    &.half_field {
      height: 45px;
      width: 495px;
      box-sizing: border-box;
      display: inline-block;
    }

    &.third_field {
      width: 164px;
       height: 45px;
      display: inline-block;
    }
  }

  input[type="radio"] {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 1px solid #ababab;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    vertical-align: middle;
    margin-right: 11px;

    &:checked {
      background-size: 24px;
      background-color: $corporate_1;
      text-align: center;
    }
  }

  input[type="checkbox"] {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 1px solid #ababab;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    vertical-align: middle;
    margin-right: 11px;
    margin-top: 9px;

    &:checked {
      background-size: 24px;
      background-color: $corporate_1;
      text-align: center;
    }
  }

  .privacy_wrapper label {
    display: inline-block;
    font-size: 14px;
    font-weight: 300;
    color: #5a5a5a;
  }

  button {
    display: block;
    width: 100%;
    color: white;
    font-weight: lighter;
    background: $corporate_2;
    -webkit-appearance: none;
    -moz-appearance: none;
    font-size: 15px;
    appearance: none;
    border: 0;
    line-height: 35px;
    text-transform: uppercase;
    margin-top: 30px;
    -webkit-transition: background .8s;
    -moz-transition: background .8s;
    -ms-transition: background .8s;
    -o-transition: background .8s;
    transition: background .8s;
    cursor: pointer;

    &:hover {
      background: $corporate_1 !important;
    }
  }

  label.error {
    display: none !important;
  }

  .input-error {
    border: 1px solid red !important;
  }
  #country_code, #gender {
    color: #646464;
  }
}

h2.suscribe_form_title {
  font-size: 43px;
  font-weight: 300;
  color: $corporate_1;
  font-family: $title_family;
  text-align: center;
  margin: 0 auto 27px;
}

.privacy_wrapper {
  text-align: center;
  margin-top: 15px;
}

.hotel_selection_box {
  width: 100%;
  -webkit-column-count: 3;
  -moz-column-count: 3;
  column-count: 3;
  margin-top: 0;
}

.hotel_selection_label {
  color: #5a5a5a;
  font-weight: 500;
  margin-bottom: 20px;
  margin-top: 30px;
  font-size: 14px;
}

.user_register_conditions {
  text-align: center;
  margin: 30px auto;
  padding: 0 160px;
  font-size: 12px;
  color: #646464;
  font-weight: 300;
  padding-bottom: 40px;
  line-height: 25px;
  border-bottom: 1px solid #e0e0e0;
}

input#identifier {
  text-align: center;
  color: $corporate_2;
  text-transform: uppercase;

  &::-webkit-input-placeholder {
    color: $corporate_2;
  }
  &::-moz-placeholder {
    color: $corporate_2;
  }
  &:-ms-input-placeholder {
    color: $corporate_2;
  }
  &:-moz-placeholder {
    color: $corporate_2;
  }
}

.welcome_login {
  font-size: 35px;
  font-weight: 300;
  color: white;
  margin-bottom: 40px;
}

.forget_password_popup {

  .main_recovery_title {
    font-size: 27px;
    font-weight: 300;
    color: $corporate_1;
    margin-bottom: 20px;
    text-align: center;
  }

  .email_input_label {
    display: block;
    width: 100% !important;
    color: #646464;
    line-height: 20px;
    font-weight: 300;
    font-size: 13px;
  }

  .recovery_button {
    color: white;
    font-weight: lighter;
    background: $corporate_1;
    -webkit-appearance: none;
    -moz-appearance: none;
    font-size: 15px;
    appearance: none;
    border: 0;
    line-height: 35px;
    text-transform: uppercase;
    width: 100%;
    text-align: center;
  }

  input#forget_email {
    width: 100%;
    margin-bottom: 10px;
    border: 0;
    padding: 12px;
    font-weight: 300;
    font-size: 14px;
    border-bottom: 2px solid #d8d8d8;
    box-sizing: border-box;
    text-align: center;
  }
}

/*======= User details table =======*/
.profile_modification_wrapper {
  .user_info_element_wrapper {
    width: 1140px;
    display: block;
    margin: auto;
    text-align: center;

    table {
      display: table;
      margin: auto;
      text-align: center;
      //border: 1px solid gainsboro;
      font-weight: 300;
      font-size: 13px;
      color: #646464;

      input {
        width: 350px;
        border: 0;
        padding: 12px;
        font-weight: 300;
        font-size: 14px;
        border-bottom: 2px solid #d8d8d8;
        box-sizing: border-box;
        display: block;
        text-align: center;
        margin: 0 auto 10px;
      }

      td {
        padding: 15px 30px;
        height: 62px;
        box-sizing: border-box;

        &:first-of-type {
          text-align: left;
        }
      }
    }
  }

  .user_info_element_wrapper {
    margin-bottom: 40px;

    h4.login_title {
      font-size: 43px;
      font-weight: 300;
      color: $corporate_1;
      text-align: center;
      width: 590px;
      margin: 0 auto 27px;
    }
  }
}

.modify_club_button, .resend_club_button {
  display: inline-block !important;
}

.modify_club_button {
  background: $corporate_2 !important;
}
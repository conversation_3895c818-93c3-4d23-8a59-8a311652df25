body {
  font-family: <PERSON>o;
  background-color: #1D1D1D;
  background-image: linear-gradient(to bottom right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0) calc(50% - 1px), rgba($gray-1, .2) 50%, rgba(0, 0, 0, 0) calc(50% + 1px), rgba(0, 0, 0, 0));
  background-size: 20px 20px;
  strong {
    font-weight: 700;
  }
  .aviso_cookie {
    position: fixed;
    top: 10px;
    right: 10px;
    max-width: 530px;
    width: 100%;
    height: auto;
    padding: 20px 30px;
    background: rgba(0, 0, 0, 0.8);
    p {
      padding: 0;
      text-align: left;
      line-height: 20px;
    }
  }
}

*, ::after, ::before {
  box-sizing: border-box;
}

.responsive_sections_wrapper {
  display: none;
}

// Header
header {
  background-color: #555658 !important;
  height: 120px;
  #mainHeaderContainer {
    margin: 12px auto;
  }
}

header, .hide_menu {
  background: transparent;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1001;
  .after {
    position: absolute;
    bottom: -3px;
    left: 0;
    right: 0;
    width: 0;
    display: block;
    content: '';
    height: 3px;
    background-color: $corporate-1;
  }

  #main_sections {
    display: block;
    text-align: right;
  }

  #logo {
    margin-top: 24px;
    float: left;
    width: 170px;
  }

  #main_header {
    width: 970px;
    float: right;

  }

  .section_element, .section_element top_sections {
    display: inline-block;
    font-size: 16px;
    font-weight: 300;
    position: relative;
    cursor: pointer;

    .link {
      color: white;
      text-decoration: none;
      cursor: pointer;
      padding: 5px 10px 5px;
      display: inline-block;

      &:hover {
        color: darken(white, 10%);
      }
    }

    &:first-child {
      .link {
        padding-left: 0px;
      }
      .subsections_wrapper {
        left: 0;
      }
    }

    &:last-child .link {
      padding-right: 0;
    }

    .subsections_wrapper {
      background: white;
      position: absolute;
      top: 39px;
      left: 10px;
      opacity: 0;
      margin-top: -25px;
      text-align: left;
      border-radius: 5px;
      overflow: visible !important;
      @include transition(all, .6s);

      .subsection_element {
        display: inline-block;
        padding: 7px 30px 7px 15px;
        width: 100%;
        white-space: nowrap;
        font-weight: 300;
        text-transform: uppercase;
        @include transition(all, .6s);

        .hide_img {
          display: inline-block;
          width: 24px;
          vertical-align: middle;
        }
        a {
          display: inline-block;
          text-decoration: none;
          font-size: 14px;
          color: #333;
          vertical-align: middle;
          padding-left: 15px;
        }
        &:hover {
          background-color: rgba(0, 0, 0, .1);
        }
      }
      &:before {
        content: '';
        position: absolute;
        top: -20px;
        left: 15px;
        display: block;
        border: 10px solid transparent;
        border-bottom-color: white;
      }
      &.active {
        opacity: 1;
        margin-top: 0;
      }
    }
  }

  #top_header {
    text-align: right;
    color: white;
    width: auto;
    font-size: 14px;
    font-weight: 500;
    margin-top: 12px;
    margin-bottom: 12px;

    .social {
      display: inline-block;
      vertical-align: middle;
      color: white;
      background-color: white;
      border-radius: 5px;
      padding: 10px;
      margin-right: 10px;

      a {
        .fa {
          color: $corporate-4;
          font-size: 18px;
          margin-right: 10px;
          vertical-align: middle;
          &:hover {
            color: $gray-2;
          }
        }
      }
    }

    #lang {
      display: inline-block;
      color: $corporate-4 !important;
      margin-right: 10px;
    }

    #selected-language {
      padding-right: 18px;
      display: inline-block;
      position: relative;
      color: $corporate-4;
      //background: url("/img/datar/flecha-select-idioma.png") no-repeat right;
      background-position-y: 7px;
      &:after {
        content: "\f107";
        font-family: "Fontawesome";
        color: $corporate-4;
        @include center_y;
        right: 0;

      }
      &:hover {
        color: $gray-2;
        &:after {
          content: "\f107";
          font-family: "Fontawesome";
          color: $gray-2;
          @include center_y;
          right: 0;
        }
      }
    }

    .language-option-flag {
      display: inline-block;
    }

    .language-option-flag a {
      color: $corporate-4;
    }

    a {
      text-decoration: none;
      color: white;
      margin: 0 2px;

      &:hover {
        color: darken($corporate-4, 10%);
      }
      &.top_menu {
        color: white;
        background-color: $corporate-4;
        border-radius: 5px;
        display: inline-block;
        vertical-align: middle;
        padding: 11px 12px;
        &:hover {
          background-color: darken($corporate-4, 10%);
        }

      }

      &#section-active:not(.top_menu) {
        color: darken($corporate-4, 10%);
      }
    }

    .login {
      display: inline-block;
      background: $corporate-1;
      padding: 5px 10px;
      color: white;
      text-transform: uppercase;
      cursor: pointer;
      margin-left: 10px;

      a {
        color: white;
        font-size: 13px;
        font-weight: 400;

        &:hover {
          color: darken(white, 10%);
        }
      }
    }
  }

  #section-active {
    font-weight: 500;
  }
}

.inner_slider {
  width: 100%;
  height: calc(30vh + 120px);
  position: relative;
  overflow: hidden;
  img {
    @include center_image;
  }
  &.inner_slider_big {
    height: 400px;
    margin-top: 115px;
    .inner_cartela {
      @include center_y;
      left: 0;
      right: 0;
      padding: 0 calc((100% - 1140px) / 2) 0;
      color: white;
      .title {
        font-size: 66px;
        line-height: 70px;
        font-weight: bold;
        padding: 0 20px 10px;
      }
      .desc {
        font-size:18px;
        line-height: 20px;
        font-weight: normal;
        padding: 0 20px;
        strong, b {
          font-weight: bold;
        }
      }
    }
  }
}

// Hide header
.hide_subsections {
  background: #f7f7f7;

  .subsections_element {
    text-align: center;
    height: 90px;

    .hide_element {
      display: inline-block;
      padding: 19px 0;

      a {
        color: black;
        text-decoration: none;
        padding: 3px 19px;
        display: inline-block;
        font-size: 12px;
        color: #333;

        &:hover {
          color: lighten(#333, 20%);
        }
      }
    }
  }
}

.hide_menu {
  position: fixed;
  top: 0;
  //background: rgba(249, 249, 249, 0.9);
  background: rgba(85, 86, 88, 0.9);
  display: none;
  //overflow: visible!important;
  box-shadow: 0px -2px 10px black;
  height: 120px;

  #mainHeaderContainer {
    margin: 14px auto;
  }

  #top_header .social a .fa {
    color: $corporate-4;

    &:hover {
      opacity: .8;
    }
  }

  #logo {
    margin-top: 25px;
  }

  .section_element {
    a {
      color: white;

      &:hover {
        color: darken(white, 20%) !important;
      }
    }
  }
}

/*=== Slider ===*/
#slider_container {
  position: relative;

  .tp-rightarrow.default {
    background: none;
    &:before {
      content: '\f105';
      display: block;
      color: white;
      font-size: 50px;
      font-family: "fontawesome", sans-serif;
    }
  }

  .tp-leftarrow.default {
    background: none;
    &:before {
      content: '\f104';
      display: block;
      color: white;
      font-size: 50px;
      font-family: "fontawesome", sans-serif;
    }
  }

  .default {
    height: 72px;
  }

  .tp-bullets.simplebullets.round .bullet {
    width: 10px;
    margin-right: 10px;
    margin-left: 0;
    display: inline-block;
    //border: 1px solid #ccc;
    border-radius: 50%;
    background: #ccc;
    color: transparent;
    height: 10px;
  }

  .tp-bullets.simplebullets.round .bullet.last {
    margin-right: 0;
  }

  .tp-bullets.simplebullets.round .bullet.selected {
    background-color: $corporate-4;
  }

  .icon_down {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    width: 70px;
    padding: 7px 0 3px;
    margin: auto;
    text-align: center;
    z-index: 100;
    &:before {
      content: '\f107';
      display: block;
      color: $corporate_1;
      font-size: 30px;
      font-family: "fontawesome", sans-serif;
    }
  }

  .tp-banner-container {
    height: 80vh !important;
  }

  .tp-caption {
    position: absolute;
    text-align: center;
    top: 200px;

    .revolution_description {
    }

    .revolution_text {
      text-align: center;
      margin-top: 63px;
      line-height: 1;

      .title {
        font-weight: 300;
        font-size: 65px;
        color: white;
        font-family: 'Caveat';
        font-weight: bold;
      }

      .description {
        font-weight: 100;
        font-size: 39px;
        margin-top: 7px;
        color: white;

      }
    }

    .links {
      margin-top: -5px;
      line-height: 1;

      .know_more {
        font-size: 24px;
        color: $corporate-2;
        margin-top: 10px;
        font-weight: 300;
        display: inline-block;
        padding: 10px;
        text-decoration: none;
        line-height: 1;
        background-color: $corporate-4;
        border-radius: 5px;

        //background-position-y: 1px;

        &:hover {
          color: darken(white, 10%);
          background-color: darken($corporate-4, 20%);
        }
      }
    }
  }
}

.left_slider_arrow {
  position: absolute;
  top: 315px;
  z-index: 22;
  left: 20%;
  cursor: pointer;
}

.right_slider_arrow {
  position: absolute;
  top: 315px;
  z-index: 22;
  right: 20%;
  -moz-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
  filter: FlipH;
  -ms-filter: "FlipH";
  cursor: pointer;
}

.tparrows {
  z-index: 30;
}

.tp-bullets {
  bottom: 56px !important;
  opacity: 1 !important;
  -moz-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  -webkit-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  margin-left: 0 !important;
  left: 50%;
}

.slide_inner {
  height: 480px;
  width: 100%;
  overflow: hidden;
  display: inline-block;
  position: relative;

  img {
    position: absolute;
    left: -100%;
    right: -100%;
    top: -100%;
    bottom: -100%;
    margin: auto;
    min-height: 100%;
    min-width: 100%;
    max-width: initial;
  }
}

.down_slider_arrow {
  position: absolute;
  bottom: 175px;
  z-index: 22;
  right: 20%;
  cursor: pointer;

  &:hover {
    opacity: 0.8;
  }
}

// Bannersx3

.bannerx3_wrapper {
  display: flex;
  width: 100%;
  background-color: white;
  .bannerx3 {
    position: relative;
    overflow: hidden;
    display: inline-block;
    width: calc(100% / 3);
    height: 250px;
    border: 5px solid white;
    border-left: none;
    img {
      @include center_image;
    }
    .title {
      @include center_xy;
      z-index: 2;
      color: white;
      text-align: center;
      font-size: 56px;
      font-family: 'Caveat';
    }
    &:last-of-type {
      border-right: none;
    }
    &:before {
      content: '';
      @include full_size;
      z-index: 1;
      background-color: rgba(black, .4);
      @include transition(background-color, .6s);
    }
    &:hover {
      &:before {
        background-color: rgba($corporate-4, .6);
      }
    }
  }
}

// Bannerx5
.bannerx5_wrapper {
  padding-top: 40px;
  padding-bottom: 50px;
  width: 1140px;
  margin: 0 auto;

  .col-2 {
    width: 564.5px;
    height: 380px;
    margin-bottom: 10px;
  }

  .col-3 {
    height: 250px;
    width: 372.66px;
  }

  .banner-left {
    margin-right: 10px;
  }

  .banner-mid {
    margin-right: 10px;
  }

  .bannerx5_element {
    position: relative;
    float: left;
    overflow: hidden;

    .bannerx5_gallery {
      .flex-control-nav {
        position: absolute;
        bottom: 10px;
        right: 0;
        left: 0;
        text-align: center;
        z-index: 2;

        li {
          display: inline-block;
          padding: 5px;
          margin: 5px;
          width: 10px;

          a {
            display: inline-block;
            border-radius: 50%;
            //border: 1px solid #ccc;
            background: #ccc;
            color: transparent;
            height: 10px;
          }

          a.flex-active {
            background: $corporate-4;
            background-size: contain;
          }
        }
      }
    }
    .element_text, .bannerx5_text {
      position: relative;
      background-color: #555658;
      padding: 15px 25px 25px 25px;
      color: white;
      text-align: center;
      width: 100%;

      .title {
        font-weight: 300;
        font-size: 19px;
        color: white;

        b {
          font-weight: 500;
        }
      }

      .description {
        font-weight: 300;
        font-size: 16px;
        color: white;
      }
    }

    .element_img, .bannerx5_img {
      position: relative;
      width: 100%;
      height: 300px;
      overflow: hidden;
      img {
        @include center_image;
      }
    }
    &:nth-of-type(n + 3) {
      .element_img, .bannerx5_img {
        height: 170px;
      }
    }
  }
  .links {
    color: white;
    background-color: $corporate-4;
    border-radius: 5px;
    padding: 10px;
    @include center_xy;
    .know_more {
      font-size: 16px;
      color: white;
      font-weight: 300;
      display: inline-block;
      text-decoration: none;
      background-position-y: 6px;

      &:hover {
        color: darken(white, 10%);
      }
    }

    .link_video {
      font-size: 16px;
      color: $corporate-4;
      margin-top: 10px;
      font-weight: 300;
      background: url("/img/datar/bot-play-banner.png") no-repeat right;
      display: inline-block;
      padding-right: 23px;
      margin-left: 5px;
      text-decoration: none;
      background-position-y: 2px;

      &:hover {
        color: darken($corporate-4, 10%);
      }
    }

    .highlight {
      font-size: 16px;
      color: $corporate-4;
      margin-top: 10px;
      font-weight: 300;
      display: inline-block;
      padding-right: 12px;
    }
  }
}

// Bannerx4
.bannerx4_wrapper {
  padding: 50px 0;
  margin-bottom: 50px;

  .bannerx4_element {
    text-align: center;
    font-weight: 300;
    width: 25%;
    display: inline-block;
  }

  .bannerx4_img {
    margin-bottom: 10px;
    img {
      width: 70px;
      height: 70px;
    }
  }

  .bannerx4_title {
    margin-bottom: 0px;
    font-size: 30px;
    color: white;
    font-family: 'Caveat';
  }

  .bannerx4_description {
    line-height: 21px;
    font-size: 16px;
    color: white;
  }
}

// Slider with Text
.slider_text_wrapper {
  //height: 430px;
  position: relative;
  padding: 50px 0 0;
  background: lightgreen;
  overflow: hidden;

  .icon_block {
    text-align: center;
    color: white;
    //border: 2px solid $corporate_1;
    background-color: $corporate-4;
    border-radius: 5px;
    font-size: 40px;
    width: 75px;
    padding: 14px 0 10px;
    margin: 0 auto 20px;
  }

  .slider_text_title {
    margin-bottom: 30px;
    font-weight: 100;
    font-size: 50px;
    color: #c8c8c8;

    b {
      display: block;
      //color: #919191;
      color: white;
    }
  }

  .slider_text_description {
    font-weight: 300;
    line-height: 29px;
    color: white;
    width: 530px;
    margin: 0 auto;
  }

  .slides {
    width: 60%;
    margin: 0 auto;
    text-align: center;
  }

  .flex-direction-nav {
    .flex-nav-prev {
      position: absolute;
      top: calc(50% - 35.5px);
      left: 30px;
      width: 33px;
      z-index: 2;
      -ms-transform: rotate(180deg);
      -webkit-transform: rotate(180deg);
      transform: rotate(180deg);

      a {
        background: url("/img/datar/flecha-slider.png") no-repeat;
        color: transparent;
        height: 71px;
        display: inline-block;

      }
    }

    .flex-nav-next {
      position: absolute;
      right: 0;
      top: calc(50% - 35.5px);
      position: absolute;
      right: 30px;
      z-index: 2;

      a {
        background: url("/img/datar/flecha-slider.png") no-repeat;
        color: transparent;
        height: 71px;
        display: inline-block;
      }
    }
  }
  .customers_line_wrappers {
    clear: both;
    margin-top: 20px;
    padding: 20px 0;
    background-color: rgba(black, .4);
    .customers_line_title {
      width: 80%;
      max-width: 800px;
      color: lightgrey;
      text-align: center;
      font-size: 30px;
      font-weight: lighter;
      margin: 0 auto 20px;
    }
    .customers_line {
      width: 80%;
      max-width: 800px;
      margin: auto;
      .owl-dots {
        text-align: center;
        margin-top: 10px;

        .owl-dot {
          display: inline-block;
          vertical-align: middle;
          padding: 1px;
          margin: 9px;

          span {
            display: block;
            width: 7px;
            height: 7px;
            background-color: white;
            border-radius: 50%;
          }
          &.active {
            span {
              background-color: $corporate-4;
            }
          }
        }
      }
    }
  }
}

// Banner Partner
.banner_partner_wrapper {
  padding: 50px 0;
  background: #fafafa;

  .icon_block {
    text-align: center;
  }

  .partner_text {
    margin: 0 auto;
    width: 60%;
    text-align: center;
    padding-bottom: 38px;
    margin-top: -16px;

    .partner_title {
      margin-bottom: 30px;
      margin-top: 30px;
      font-weight: 100;
      font-size: 50px;
      color: #323232;

      b {
        display: block;
        color: #8d8d8d;
        margin-top: -5px;
      }
    }

    .partner_description {
      width: 80%;
      margin: 0 auto;
      font-weight: 300;
      font-size: 17px;
      line-height: 29px;
      color: #323232;
    }
  }

  .partner_badges {
    text-align: center;
    li.col-md-3 {
      width: calc(100% / 4 - 40px);
      display: inline-block;
      vertical-align: middle;
      margin: 0 20px;
    }
  }
}

.wordpress_block {
  background: url("/img/datar/img-blog.jpg?v=1") no-repeat bottom center;
  background-size: 500px;
  padding: 50px 0;

  .icon_block {
    text-align: center;
    color: $corporate_1;
    border: 1px solid $corporate_1;
    border-radius: 50%;
    font-size: 40px;
    width: 75px;
    padding: 16px 0 10px;
    margin: 0 auto 20px;
  }

  .wordpress_text .title {
    text-align: center;
    font-weight: 100;
    font-size: 50px;
    color: #323232;
    padding-bottom: 38px;
    margin-top: 15px;

    b {
      display: block;
      color: #8d8d8d;
    }
  }

  .wordpress_sections {
    text-align: center;
    .wordpress_element {
      display: inline-block;
      vertical-align: top;
      width: calc(100% / 3 - 40px);
      border: 1px solid #DDD;
      margin: 0 20px;
      text-align: center;

      .title {
        text-align: left;
        padding: 20px;
        color: $corporate-1;
        font-weight: bold;
        font-size: 18px;
      }
      .img {
        position: relative;
        width: 100%;
        height: 150px;
        overflow: hidden;
        img {
          @include center_xy;
        }
        &:after {
          content: '';
          border: 10px solid transparent;
          border-bottom-color: white;
          position: absolute;
          bottom: 0px;
          right: 20px;
        }
      }
      .description {
        text-align: left;
        padding: 0 20px;
        font-size: 16px;
        font-weight: 300;
        color: #555;
      }
      .extra {
        position: absolute;
        bottom: 0;
        right: 0;
        left: 0;
        text-align: left;
        background: rgba(0, 0, 0, 0.8);
        background: linear-gradient(rgba(black, 0), rgba(black, 0.6));
      }
      .date, .comments {
        display: inline-block;
        vertical-align: bottom;
        box-sizing: border-box;
        padding: 5px;
        color: white;
        font-size: 16px;
        font-weight: 300;
        margin-left: 20px;
        text-shadow: 0 0 5px rgba(0, 0, 0, 0.8);
      }
      .date {
        font-size: 12px;
        padding: 10px 10px 8px;
      }
      .comments {
        i.fa {
          border: 1px solid #DDD;
          border-radius: 50%;
          font-size: 10px;
          padding: 5px;
        }
        span {
          color: white;
          background-color: $corporate-4;
          display: inline-block;
          border-radius: 50%;
          margin-left: 5px;
          font-size: 10px;
          padding: 3px 5px;
        }
      }

      .news_text {
        .title {
          font-weight: bold;
          font-size: 17px;
          line-height: 29px;
          color: #323232;
          width: 320px;
          margin: 0 auto;
        }

        .content {
          font-size: 17px;
          font-weight: 300;
          color: #323232;
          line-height: 29px;
          width: 320px;
          margin: 0 auto;
          max-height: 116px;
          overflow: hidden;
        }
      }
      .links {
        text-align: right;
        margin-top: 15px;

        .read_more {
          position: relative;
          display: inline-block;
          font-size: 14px;
          font-weight: 300;
          text-decoration: none;
          color: $corporate_4;
          padding: 5px 10px;
          @include transition(all, .6s);
          span {
            position: relative;
            z-index: 2;
          }
          &:before {
            content: '';
            display: block;
            position: absolute;
            top: 0;
            right: 0;
            width: 0px;
            bottom: 0;
            background-color: $corporate_4;
            @include transition(all, .6s);
          }
          &:after {
            content: '\f105';
            font-family: "fontawesome", sans-serif;
            margin: 0 5px;
            display: inline-block;
            position: relative;
            z-index: 2;
          }
          &:hover {
            color: white;
            &:before {
              width: 100%;
            }
          }
        }
      }
    }

    .wordpress_element.middle {
      margin: 0 5px;
    }
  }

  .newsletter_wrapper {
    text-align: center;
    margin-top: 60px;

    .newsletter_element {
      display: inline-block;
      vertical-align: top;
      margin: auto;
      text-align: center;
      margin-bottom: 20px;
      padding: 0 30px;

      label.error {
        display: none !important;
      }
      &#newsletter_web {
        width: 500px;

        .lopd_button, .promotions_button {
          display: inline-block;
          vertical-align: middle;
          width: 100%;
          text-align: left;
          margin-top: 10px;
          color: lightgrey;

          a {
            text-decoration: none;
            font-size: 12px;
            color: lightgrey;
          }
        }
      }

      .bordeInput {
        display: inline-block;
        vertical-align: middle;
        border: 2px solid $corporate-4;
        padding: 0 10px;
        width: 380px;
        height: 60px;

        &::-webkit-input-placeholder {
          color: #8d8d8d;
        }

        &:-moz-placeholder {
          color: #8d8d8d;
        }

        &:-ms-input-placeholder {
          color: #8d8d8d;
        }

        &:focus {
          outline-color: white;
        }
      }

      #newsletterButtonExternalDiv {
        display: inline-block;
        vertical-align: middle;

        #newsletter-button {
          position: relative;
          background: $corporate-4;
          padding: 20.5px 0;
          height: 60px;
          width: 60px;
          cursor: pointer;
          @extend .fa-angle-right;
          &:before {
            @include center_xy;
            @extend .fa;
            color: white;
            font-size: 30px;
          }
        }
      }

    }
  }

  .newsletter_element#newsletter_blog {
    position: relative;
    max-width: 500px;
    width: 100%;
    background-color: $corporate_1;
    .text {
      position: relative;
      z-index: 5;
      display: inline-block;
      float: left;
      padding: 20px;
      color: white;
      font-size: 20px;
      height: auto;
      box-sizing: border-box;
    }
    &:before {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 0;
      background-color: $corporate_4;
      @include transition(all, .6s);
    }
    #newsletterButtonExternalDiv {
      @include center_y;
      right: 0;
      #newsletter-button {
        position: relative;
        z-index: 5;
        background: transparent;
        @extend .fa-angle-right;
        &:before {
          font-family: "Fontawesome", sans-serif;
          font-size: 30px;
          color: white;
          @include center_xy;
        }
      }
    }

    &:hover {
      &:before {
        height: 100%;
      }
    }
  }
}

// Submenu flotante
.submenu_flotante_wrapper {
  background-color: white;
  height: 50px;
  padding: 15px 0;
  border-bottom: 1px solid #e3e3e3;
  width: 100%;
  z-index: 100;

  &.floating {
    position: fixed;
    top: 0;
    .after {
      position: absolute;
      bottom: -3px;
      left: 0;
      right: 0;
      width: 0;
      display: block;
      content: '';
      height: 3px;
      background-color: $corporate-1;

    }
  }

  .logo {
    float: left;
    margin-top: -7px;
    img {
      max-height: 35px;
    }
  }

  .submenu_content {
    float: right;
    text-transform: capitalize;
    padding-top: 4px;

    a {
      text-decoration: none;
      padding: 0 9.5px;
      font-size: 13px;
      color: #333;

      &:hover {
        color: lighten(#333, 20%);
      }
    }

    a:last-child {
      padding-right: 0;
    }
  }
}

.main_content_wrapper {
  background-color: #fafafa;
  //height: 900px;
  text-align: center;

  .main_content_title {
    font-weight: 100;
    font-size: 64px;
    opacity: 0;
    color: $corporate-4;
    margin-top: 128px;
    line-height: 66px;
  }

  .empty_block {
    height: 100px;
  }

  .main_content_description {
    opacity: 0;
    font-weight: 100;
    font-size: 24px;
    width: 820px;
    margin: 0 auto;
    margin-top: 26px;
    line-height: 33px;
    color: #333;

    p {
      padding: 0px 10px;
      padding-bottom: 24px;
    }
  }

  .main_content_img {
    padding-top: 80px;
  }

  .main_content_img img {
    vertical-align: middle;
  }

  .links {
    margin-top: 45px;

    .know_more {
      font-size: 24px;
      color: $corporate-4;
      margin-top: 10px;
      font-weight: 300;
      background: url("/img/datar/flecha-enlace-slider.png") no-repeat right;
      display: inline-block;
      padding-right: 15px;
      text-decoration: none;
      background-position-y: 8px;
      margin-right: 20px;

      &:hover {
        color: darken($corporate-4, 10%);
      }
    }

    .link_video {
      font-size: 24px;
      color: $corporate-4;
      margin-top: 10px;
      font-weight: 300;
      background: url("/img/datar/bot-play-banner.png") no-repeat right;
      display: inline-block;
      padding-right: 23px;
      text-decoration: none;
      background-position-y: 7px;

      &:hover {
        color: darken($corporate-4, 10%);
      }
    }
  }
}

.black_sections_wrapper {
  &.black_sections_fit {
    .black_element {
      padding: 0;
      .element_text {
        padding: 20px 0;
      }
    }
  }
  .black_element {
    color: white;
    padding: 140px 0;
    font-weight: 100;
    .container12 {
      display: flex;
      align-items: center;
    }

    .element_text {
      width: 50%;
      display: inline-block;
      vertical-align: middle;

      .title {
        font-size: 52px;
        line-height: 56px;
        padding-bottom: 20px;
      }

      .description {
        font-size: 17px;
        line-height: 29px;
        //width: 485px;
      }
    }

    .work {
      text-align: center !important;
      float: none !important;
      margin: 0 auto;
      width: 100%;

      .title {
        width: 760px;
        margin: 0 auto;
        color: $corporate-4;

        span {
          display: block;
          color: white;
        }
      }

      .description {
        width: 710px;
        margin: 0 auto;
        padding-left: 0 !important;
        padding-right: 0 !important;
        text-align: left;
        margin-top: 4px;

        .button_mail {
          display: block;
          padding-top: 49px;
          cursor: pointer;

          span {
            text-decoration: none;
            color: white;
            padding: 15px 30px;
            background: $corporate-4;
            color: white;
            display: inline-block;
          }
        }
      }
    }

    .element_img {
      width: 50%;
      display: inline-block;
      vertical-align: middle;
      position: relative;
      text-align: center;

      .icon_maxi {
        position: absolute;
        top: 10px;
        right: 10px;
        width: 40px;
      }
    }

    .element {
      width: 395px;
      margin: 0 auto;
      padding: 12px 0;
      border-bottom: 1px solid white;

      .title {
        color: $corporate-4;
        font-size: 52px;

        .location {
          width: 200px;
          margin: 0 auto;
          text-align: left;
        }
      }

      .description {
        color: white;
        font-size: 17px;
      }
    }

    .element_text_additional .element:first-child {
      padding-top: 0;
    }

    .element_text_additional .element:last-child {
      border-bottom: none;
    }
  }

  .gray {
    background: #333;

    .element_text {
      .title, .description {
        padding-right: 80px;
      }
    }

    .element_img {
      text-align: right;
    }

    .element_text_additional {
      float: right;
      width: 50%;
      text-align: center;

      .element {
        margin-right: 0;
      }
    }
  }

  .black {
    background: #2b2b2b;
    .container12 {
      flex-direction: row-reverse;
    }

    .element_text {

      .title, .description {
        padding-left: 80px;
      }
    }

    .element_img {
      text-align: left;
    }

    .element_text_additional {
      float: left;
      width: 50%;
      text-align: center;

      .element {
        margin-left: 0;

        .location {
          background: url("/img/datar/ico-localizacion-nosotros.png") no-repeat left;
          padding-top: 16px;
          padding-left: 56px;
          margin-top: 18px;
          background-position-y: 0;
          width: 258px;
        }

        &:first-child .location {
          margin-top: 0;
        }
      }
    }
  }
}

// Minigallery
.mini_gallery_wrapper {
  padding: 10px 0;
  .mini_gallery_element {
    width: 49.6%;
    display: inline-block;

    img {
      width: 100%;
    }
  }

  .mini_gallery_element:last-child {
    float: right;
  }
}

// Blue section
.blue_sections_wrapper {
  //height: 475px;
  padding: 128px 0;
  background: $corporate-4;
  color: white;
  text-align: center;
  font-weight: 100;

  .blue_title {
    font-size: 52px;
    line-height: 56px;
  }

  .blue_description {
    width: 800px;
    margin: 0 auto;
    font-size: 17px;
    line-height: 29px;
    margin-top: 20px;
  }

  .blue_images, .blue_images_hidden {
    width: 890px;
    margin: 0 auto;

    .images {
      width: 33%;
      display: inline-block;
      padding: 5px 7px;
      overflow: hidden;
    }
  }

  .blue_images {
    padding-top: 80px;
  }

  .more_images {
    position: relative;
    cursor: pointer;
    &:before, &:after {
      content: '';
      @include center_y;
      background-color: white;
      left: 131px;
      width: 34%;
      height: 1px;
    }
    &:after {
      left: auto;
      right: 131px;
    }

    .icon_wrapper {
      position: relative;
      display: inline-block;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      border: 1px solid white;
      i {
        @include center_xy;
        font-size: 32px;
        color: white;
      }
    }
  }

  .icon_text {
    width: 400px;
  }

}

//suscriptcion iframe maps
.suscripcion-google-maps-wrapper {

  padding: 45px 0px 90px;
  background: white;
  color: white;
  text-align: center;
  font-weight: 100;

  .blue_title {
    font-size: 52px;
    line-height: 56px;
    color: $corporate-4;
    margin-bottom: 40px;
  }
}

.newsletter_wrapper {
  position: relative;
  background-color: $corporate-4;
  border-bottom: 1px solid white;
  padding: 30px 0 70px;
  .newsletter_container {
    text-align: center;
    .newsletter_title {
      display: inline-block;
      vertical-align: middle;
      width: 50%;
      color: white;
      font-size: 18px;
      font-weight: 300;
    }
    .newsletter_description {
      display: inline-block;
    }
    .newsletter_form {
      display: inline-block;
      vertical-align: middle;
      width: 40%;

      .input_email {
        display: inline-block;
        vertical-align: middle;
        width: 50%;
        height: 45px;
        margin-right: 15px;
        border-radius: 5px;
        padding-left: 20px;
        border: none;
        color: #555658;
        font-size: 16px;
        font-weight: 400;

        &::placeholder {
          color: #555658;
          font-size: 16px;
          font-weight: 400;
        }
      }
      .button_newsletter {
        position: relative;
        display: inline-block;
        vertical-align: middle;
        width: 45%;
        height: 45px;
        border-radius: 5px;
        cursor: pointer;
        background-color: #555658;
        @include transition(background-color, .6s);
        &:hover {
          background-color: $corporate-1;
        }
        span {
          @include center_xy;
          color: white;
        }
      }
      .check_newsletter {
        position: absolute;
        bottom: 20px;
        display: block;
        left: calc((100% - 950px) / 2);
        margin: 0 auto;
        text-align: center;
        a, label {
          display: inline-block;
          vertical-align: middle;
          color: rgba(white, .4);
          font-size: 12px;
          @include transition(color, .4s);
        }
        &:last-of-type {
          left: auto;
          right: calc((100% - 850px) / 2);
        }
        .check_privacy {
          display: inline-block;
          vertical-align: middle;
          -webkit-appearance: none;
          background-color: transparent;
          border: 1px solid rgba(white, .4);
          border-radius: 5px;
          width: 15px;
          height: 15px;
          margin-right: 5px;
          @include transition(background-color, .4s);
          &:checked {
            background-color: white;
            & + a, & + label {
              color: white;
            }
          }
        }
      }
    }
  }
}

.form_contact_wrapper {
  .blue_title {
    font-size: 52px;
    line-height: 56px;
    color: white;
    text-align: center;
    margin-bottom: 40px;
    font-weight: 100;
  }
}

// White section
.price_tab {
  height: 80px;
  background-color: #f5f5f5;
  border-bottom: 1px solid #e3e3e3;
  color: #333;
  text-align: center;

  .price_element {
    padding: 24px 25px;
    display: inline-block;
    font-size: 24px;
    font-weight: 300;
    padding-top: 25px;
    cursor: pointer;
  }

  .selected {
    color: $corporate-4;
    border-bottom: 2px solid $corporate-4;
  }
}

.white_block_wrapper {
  background: white;
  text-align: center;
  padding: 130px 0 138px;

  .white_title {
    color: $corporate-4;
    font-size: 52px;
    font-weight: 100;
    line-height: 56px;
  }

  .collage_wrapper {
    margin-top: 80px;

    img {
      width: 100%;
    }
  }

  .white_content {
    .table_text {
      display: none;
    }

    table {
      width: 100%;
      margin-top: 80px;

      td, th {
        border: 2px solid white;
      }

      td {
        background-color: #f5f5f5;
        vertical-align: middle;
        height: 60px;
        font-size: 17px;
        color: #333;
        font-weight: 300;
      }

      .text {
        width: 40%;
        padding: 0 10px;
      }

      .title {
        width: 20%;
        text-align: center;
        background: green;
        font-size: 25px;
        font-weight: 300;
        color: white;
        padding: 19px 0 8px;

        .price {
          font-size: 52px;
          font-weight: 100;
        }
      }

      .empty {
        background: transparent;
      }

      .plata {
        background-color: #afaeb4;
      }

      .oro {
        background-color: #e59721;
      }

      .platino {
        background-color: #c2c4a6;
      }

      .tick {
        background: #f5f5f5 url("/img/datar/tick-tabla.png") no-repeat center center;
        //background-size: contain;
      }
    }
  }

  .white_gallery {
    padding-top: 78px;

    .gallery_element {
      width: 33%;
      display: inline-block;
      position: relative;
      overflow: hidden;
      height: 250px;

      .element_text {
        position: absolute;
        height: 110px;
        width: 100%;
        padding: 26px 0;
        bottom: 0;
        background: rgba(black, 0.6);
        display: none;

        .title {
          color: white;
          font-size: 19px;
        }

        .description {
          color: #8d8d8d;
        }

        .social {
          .social_element {
            color: $corporate-4;
            display: inline-block;
            font-weight: 300;

            a {
              color: inherit;
              text-decoration: none;
            }
          }

          .linkdin a {
            background: url("/img/datar/flecha-enlace-banner.png") no-repeat right;
            padding-right: 10px;
            background-position-y: 6px;
          }
        }
      }

      .element_image {
        .gray_img {
          filter: grayscale(100%);
          -webkit-filter: grayscale(100%);
          -moz-filter: grayscale(100%);
          -ms-filter: grayscale(100%);
          -o-filter: grayscale(100%);
          vertical-align: middle;
          transition: 1s all;
        }
      }

      &:hover .gray_img {
        filter: grayscale(0%);
        -webkit-filter: grayscale(0%);
        -moz-filter: grayscale(0%);
        -ms-filter: grayscale(0%);
        -o-filter: grayscale(0%);
        transition: 1s all;
      }

      .icon {
        position: absolute;
        width: 100%;
        top: 95px;
        bottom: 0;
        text-align: center;
      }
    }
  }
}

// Footer
footer {
  background: rgba(255, 255, 255, 0.2);
  padding: 60px 0;
  border-top: 1px solid #c8c8c8;
  font-size: 14px;
  color: white !important;

  hr {
    border: 0 solid transparent;
    border-top: 1px solid #c8c8c8;
    margin: 12px 0;
  }

  .footer_element.arrow {
    padding-right: 10px;
    color: $corporate-4;
  }

  .footer_element.arrow a {
    //background: url("/img/datar/flecha-footer.png") no-repeat right;
    display: block;
    font-size: 14px;
    &:after {
      content: "\f054";
      font-family: "Fontawesome";
      color: $corporate-4;
      @include center_y;
      right: 0;
    }

    &:hover {
      color: lighten(black, 60%);
    }

    &#section-active {
      font-weight: bold;
    }
  }

  .footer_element {
    position: relative;
  }

  .footer_element .text {
    display: inline-block;
  }

  .footer_element a {
    color: white;
    text-decoration: none;
  }

  .lang_footer {
    float: right;

    a {
      padding: 0 10px;

      img {
        width: 20px;
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }

  #social {
    position: absolute;
    right: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
    -moz-transform: translateY(-50%);
    -ms-transform: translateY(-50%);
    -o-transform: translateY(-50%);
    transform: translateY(-50%);

    a {
      padding: 0 10px;
      display: inline-block;

      .fa {
        font-size: 24px;
        color: $corporate-4;

        &:hover {
          opacity: .8;
        }
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .texto_legal {
    margin-top: 43px;
    font-weight: 400;
    font-size: 14px;
    line-height: 22px;
    color: white;
    text-align: center;

    .powered {
      color: $corporate-4;
    }
  }

  .logos_footer {
    text-align: center;
    margin-top: 20px;

    img {
      height: 40px;
    }
  }

  .popup_legal_wrapper {
    display: inline-block;
    width: 100%;
    text-align: center;

    a {
      color: $corporate-4;
      display: inline-block;
      margin: 0 5px;
      text-decoration: none;

      &:hover {
        opacity: .8;
      }
    }
  }
}

.fancybox-opened {
  z-index: 8030;
}

.fancybox-opened .fancybox-skin {
  padding: 0px !important;
  box-shadow: 0 0 0 rgba(0, 0, 0, 0);

}

/*/ Popup idioma /*/
.fancybox-lang {
  width: 400px !important;
  //height: 350px!important;

  &.fancybox-opened .fancybox-skin {
    padding: 0 0 40px !important;
  }
  .fancybox-outer {
    height: 100% !important;
    padding: 1px !important;
    border-radius: 0 !important;
    box-shadow: none;
  }

  .fancybox-inner {
    overflow: visible !important;
    width: 100% !important;
  }

  .fancybox-close {
    //display: none;
    background: black;
    text-align: center;
    top: 0 !important;
    right: 0 !important;
    width: 42px !important;
    height: 42px !important;

    &:before {
      content: '\f00d';
      @include center_xy;
      display: block;
      color: white;
      font-size: 23px;
      font-family: "fontawesome", sans-serif;
    }
    &:hover {
      opacity: 0.8;
    }
  }

  #popup_lang_content {
    padding: 40px;
    margin: 40px;
    border: 1px solid #ddd;
    text-align: center;

    .title {
      font-size: 16px;
      margin-bottom: 20px;
      text-transform: uppercase;
      color: #666;

      b {
        display: block;
        font-size: 28px;
        color: black;
      }
    }

    li {
      display: block;
      text-transform: uppercase;
      margin: 0 auto;
      width: 70%;

      &:first-child a {
        border: none;
      }

      a {
        line-height: 40px;
        border-top: 1px dotted #ccc;
        display: block;
        color: #666;
        text-decoration: none;
        font-size: 16px;
        letter-spacing: 0.05em;

        &:hover {
          color: lighten(#666, 20%);
        }
      }
    }
  }

  #popup_login {
    padding: 40px;
    margin: 40px;
    border: 1px solid #ddd;
    text-align: center;

    .title {
      font-size: 16px;
      margin-bottom: 20px;
      text-transform: uppercase;
      color: #666;

      strong {
        display: block;
        font-size: 28px;
        color: black;
      }
    }

    .login_button {
      background: $corporate-1;
      border: 0;
      color: white;
      padding: 10px 67px;
      margin: 0 auto;
      text-decoration: none;
    }

    .input_login {
      margin-bottom: 10px;

      input {
        border: 1px solid #ccc;
        color: #666;
        width: 176px;
        padding: 7.5px 10px;
        background: transparent;
        font-size: 12px;
      }
    }

    #select-tool {
      margin-bottom: 10px;
      -webkit-appearance: none;
      border-radius: 0;
      border: 1px solid #ccc;
      width: 176px;
      padding: 7.5px 10px;
      color: #666;
      background: url(/img/datar/flecha-select-idioma.png) no-repeat 90% center;
      font-size: 12px;
    }

    .others_tools {
      margin-top: 10px;

      .login_button {
        padding: 10px 52px;
      }
    }
  }
}

// Popup Video
.fancybox-video {
  .fancybox-outer {
    padding: 0 !important;
    background: transparent !important;
  }

  .fancybox-close {
    display: none;
  }
}

// Popup Contact
.fancybox-contact {
  //width: 400px !important;
  //height: 350px!important;

  .fancybox-outer {
    height: 255px !important;
    padding: 1px !important;
    border-radius: 0 !important;
  }

  .fancybox-inner {
    overflow: visible !important;
  }

  .fancybox-close {
    //display: none;
    background: url("/img/datar/cerrar-idioma.png") !important;
    top: 0 !important;
    right: 0 !important;
    width: 42px !important;
    height: 42px !important;

    &:hover {
      opacity: 0.8;
    }
  }

  .popup_validation {
    padding: 40px;
    margin: 40px;
    border: 1px solid #ddd;
    text-align: center;

    .title {
      font-size: 16px;
      margin-bottom: 20px;
      text-transform: uppercase;
      color: #666;

      strong {
        display: block;
        font-size: 28px;
        color: black;
      }
    }
  }
}

// News Section
.news_sections_wrapper {
  background: #333;
  padding-top: 105px;
  padding-bottom: 20px;
  font-weight: 100;

  .news_element {
    color: white;
    width: 49%;
    display: inline-block;
    text-align: center;
    margin-bottom: 70px;
    vertical-align: top;
  }

  .image_new {
    float: left;
  }

  .new_text {
    .date_text {
      font-size: 52px;
      line-height: 56px;
      padding: 20px 0;
    }

    .new_description {
      font-size: 17px;
      line-height: 29px;
      width: 420px;
      text-transform: uppercase;
      margin: 0 auto;
    }

    .link {
      margin-top: 40px;

      a {
        font-size: 24px;
        text-decoration: none;
        background: url("/img/datar/flecha-enlace-wordpress.png") no-repeat right;
        padding: 15px;
        color: $corporate-1;

        &:hover {
          color: darken($corporate-1, 10%);
        }
      }
    }
  }

  .new_text.cover {
    text-align: left;
    float: left;
    margin-left: 30px;

    .new_description {
      width: 270px;
    }

    .link a {
      text-align: left;
      padding-left: 0;
    }
  }
}

@import "_contact";

/************************************************/
/**************** Bottom Pop-up *****************/
/************************************************/

.bottom_popup {
  position: fixed;
  width: 100%;
  height: 60px;
  background: rgb(90, 90, 90);
  left: 0;
  bottom: 0;
  z-index: 1000;
  display: none !important;
}

.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

.bottom_popup .bottom_popup_text {
  width: 590px;
  float: left;
  color: white;
  font-family: Arial, sans-serif;
  font-size: 14px;
}

.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

.close_button {
  float: right;
  cursor: pointer;
}

button.bottom_popup_button {
  background: darkblue;
  width: 150px;
  height: 40px;
  font-size: 18px;
  margin-top: 10px;
  &:hover {
    background: lighten(darkblue, 10%);
  }
}

#wrapper2 {
  width: 980px;
  margin: 0 auto;
}

/************************************************/
/**************** pop-up inicial ****************/
/************************************************/

.popup_inicial {
  position: relative;
  //width: 512px;
}

.popup_img {
  position: relative;
  overflow: hidden;
  //height: 430px;
  //min-width: 440px;

  img {
    //width: 100%;
    position: relative;
    //top: -100%;
    //bottom: -100%;
    //left: -100%;
    //right: -100%;
    margin: auto;
    max-width: 200%;
    vertical-align: middle;
  }
}

.fancybox-news {
  .fancybox-outer {
    padding: 0 !important;
  }
}

.popup_text {
  position: absolute;
  top: 85px;
  width: 100%;
  text-align: center;
}

.popup_title {
  font-size: 24px;
  color: white !important;
  font-weight: 400;
  text-transform: uppercase;
  padding: 0;
}

.popup_description {
  font-size: 16px;
  line-height: 27px;
  color: white !important;
  margin-top: 0px;

  hr {
    width: 150px;
    margin: 10px auto;
  }

  .line1 {
    font-size: 18px;
    font-weight: lighter;
    text-transform: uppercase;
  }

  .line2 {
    text-transform: uppercase;
    font-size: 14px;
    font-weight: lighter;
  }

  .line3 {
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 400;
  }

  a.button_download {
    background: $corporate_4;
    padding: 5px 25px;
    text-transform: uppercase;
    font-size: 14px;
    font-weight: 400;
    color: white;
    display: inline-block;
    margin-top: 10px;
  }
}

.popup_lopd {
  font-size: 13px;
}

.lopd_button {
  margin-top: 2px !important;
}

.popup_form {
  text-align: center;
  position: absolute;
  width: 100%;
  left: 0;
  bottom: 90px;
}

.popup_inicial form {
  padding: 20px;
  padding-bottom: 25px;
  font-size: 18px;
  display: inline-block;
  position: relative;
}

.popup_inicial form input {
  width: 250px;
  height: 30px;
  display: inline-block;
  vertical-align: middle;
}

.popup_inicial form .lopd_button {
  margin-top: 10px;
  text-align: left;

  input[type="checkbox"] {
    width: auto;
    vertical-align: middle;
  }

  a {
    display: inline;
    vertical-align: middle;
    text-decoration: none;
    color: white;

    &:hover {
      opacity: 0.8;
    }
  }
}

.popup_inicial button {
  background: $corporate-4;
  width: 150px;
  height: 30px;
  font-size: 18px;
  border: 1px solid #CECCCF;
  color: #FFFFFF;
  margin-left: -6px;
  display: inline-block !important;
  vertical-align: middle;
  border-radius: 0;
  border-left-color: white;
  text-transform: uppercase;

  &:hover {
    background: lighten($corporate-4, 10%);
  }

}

.popup_message {
  color: black;
  margin-top: 20px;
}

#id_email {
  background: white;
  border: 1px solid #CECCCF;
  border-right-color: transparent;
  font-size: 14px;
  border-radius: 0;
  padding-left: 10px;

  &.error {
    border-color: red;
    border-right-color: transparent;
  }
}

.fancybox-inner {
  //height: auto !important;
}

.form_popup label {
  color: white;
  display: none !important;
}

// Blue Icons
.icons_blue {
  margin-top: 30px;

  .icon_element {
    display: inline-block;
    float: left;
    width: 50%;
    border-right: 1px solid white;
    margin-bottom: 20px;
    text-align: right;

    &.even {
      float: right;
      clear: right;
      border: none;
      text-align: left;

      .icon_image {
        margin-right: 0px;
        margin-left: 20px;
      }

      .icon_text {
        margin-right: 0px;
        margin-left: 10px;
      }
    }

    .icon_image {
      display: inline-block;
      vertical-align: middle;
      margin-right: 20px;
    }

    .icon_text {
      display: inline-block;
      vertical-align: middle;
      margin-right: 10px;
    }
  }

  hr {
    clear: both;
  }
}

.icons_blue_hidden {
  display: none;
}

// Popup LOPD
.fancybox-lopd {
  width: 800px !important;
}

#lopd_content {
  width: 800px;
  padding: 1em;
}

.col-sm-12 {
  width: 100%;
  box-sizing: border-box;
  padding-right: 10px;
  textarea {
    width: 100%;
  }
}

.col-sm-6 {
  display: inline-block;
  box-sizing: border-box;
  padding-right: 10px;
  width: 50%;
  .box {
    input {
      width: 100%;
    }
  }
}

.hidden_download_popup {
  .fancybox-skin, .hidden_see_more_info, .fancybox-outer {
    background: transparent;
    box-shadow: none;
  }

  .buttons_wrapper {
    .button_element {
      display: table;
      margin: auto;
      padding: 10px 20px;
      margin-top: 15px;
      background: $corporate-4;
      color: white;
      text-decoration: none;
    }
  }
}

.newsletter_additional_wrapper {
  margin-top: 65px;

  form {
    .input_wrapper {
      display: inline-block;
      width: calc((100% - 10px) / 2);
      float: left;
      margin-right: 10px;
      margin-bottom: 10px;
      position: relative;

      &:before {
        font-family: "FontAwesome";
        color: white;
        font-size: 16px;
        z-index: 2;
        @include center_xy;
        left: 19.5px;
      }

      &:nth-of-type(1):before {
        content: '\f0e0';
      }

      &:nth-of-type(2):before {
        content: '\f007';
      }

      &:nth-of-type(3):before {
        content: '\f007';
      }

      &:after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        height: 39px;
        width: 39px;
        background: $corporate_1;
        z-index: 1;
      }

      &:nth-of-type(2n) {
        margin-right: 0;
      }
    }

    input:not(.check_privacy) {
      display: inline-block;
      width: 100%;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      height: 39px;
      padding: 0 10px 0 49px;
      border: 1px solid lightgrey;
      background: white;

      &.error {
        border-color: red;
      }

      & + label {
        display: none !important;
      }
    }

    .popup_policy_newsletter {
      display: inline-block;
      float: left;
      margin-top: 5px;
      width: 100%;

      label.error {
        display: none !important;
      }

      a {
        color: $corporate_1;
        text-decoration: none;
        font-size: 14px;
      }
    }

    #send_newsletter_additional {
      display: inline-block;
      float: right;
      width: calc((100% - 10px) / 2);
      height: 39px;
      background: $corporate_1;
      color: white;
      cursor: pointer;
      padding: 0px 30px;
      font-size: 13px;
      text-transform: uppercase;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      @include transition(opacity, .4s);

      &:hover {
        opacity: .8;
      }
    }
  }
}

@media (max-width: 768px) {
  .newsletter_additional_wrapper {
    width: 100%;

    form {
      .input_wrapper {
        width: 100%;
      }

      #client_select {
      }

      input:not(.check_privacy) {
      }

      .popup_policy_newsletter {
        margin-top: 0;
      }

      #send_newsletter_additional {
        width: 100%;
      }
    }
  }
}

//
//.fancybox-overlay.fancybox-overlay-fixed {
//  background-color: $corporate-4;
//  .fancybox-wrap.fancybox-desktop.fancybox-type-iframe.fancybox-opened {
//    .fancybox-skin {
//      .fancybox-outer {
//        .fancybox-inner {
//
//        }
//      }
//    }
//  }
//}

.full-popup {
  .fancybox-outer {
    -webkit-border-radius: 0;
    -moz-border-radius: 0;
    border-radius: 0;
  }

  div#wrapper_booking_fancybox {
    display: table;
    width: 100%;
    position: absolute;
    bottom: 0;
    top: 0;
    margin: auto;

    @include center_xy();
  }
}

.full-popup .fancybox-close {
  position: absolute;
  top: 30px;
  right: 30px;
  background: none;
  text-decoration: none;
  color: white;

  &:before {
    content: "x";
    color: white;
    font-size: 85px;
    line-height: 36px;
    font-family: 'Montserrat', sans-serif;
  }
}

.full-popup .fancybox-outer {
  background: $corporate-4;
  .fancybox-inner {
    color: white;
    padding: 200px 270px;
    width: 100% !important;
  }
}

#fancybox-loading {
  display: none !important;
}
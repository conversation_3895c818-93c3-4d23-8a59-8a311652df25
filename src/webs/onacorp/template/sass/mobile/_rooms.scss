.rooms_wrapper {
  padding: 30px 0;
  .rooms_wrapper_title {
    padding: 0 20px;
    text-align: left;
  }
  .rooms {
    display: block;
    padding: 20px;
    box-sizing: border-box;
    .extra_banner {
      display: none;
    }
    .room {
      width: calc(100% - 2px);
      margin: auto;
      text-align: left;
      .pics {
        display: block;
        width: 100%;
      }
      .content {
        width: 100%;
        padding: 0 10px;
        box-sizing: border-box;
        .top {
          text-align: center;
          .icon {
            margin-bottom: 5px;
          }
        }
      }
      .price {
        display: block;
        text-align: center;
        padding: 0 10px 10px;
        .btn {
          position: unset;
          margin: auto;
        }
      }
    }
    .owl-nav {
      .owl-prev {
        left: 0;
      }
      .owl-next {
        right: 0;
      }
    }
  }
  .room_popup {
    .close_room_popup {
      z-index: 50;
      width: 30px;
      height: 30px;
      font-size: 30px;
      top: 0;
      right: 0;
    }
    .center_xy {
      .pics a img {
        max-width: none;
        max-hegiht: 100%;
      }
      .content {
        box-sizing: border-box;
      }
    }
  }
}
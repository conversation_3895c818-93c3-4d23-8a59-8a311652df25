<!DOCTYPE html>
<html lang="{{ language }}"  xml:lang="{{ language }}">
<head>
{% if gtm_head_opening %}{{ gtm_head_opening|safe }}{% endif %}
    {% if facebook_head_opening %}{{ facebook_head_opening|safe }}{% endif %}

    <title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %}
        {{ sectionName|safe }} - {{ hotel_name|safe }}{% else %}
        {{ hotel_name|safe }} {% endif %} {% endif %}</title>
    <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
    {% if namespace %}
        <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico?v=1.4" type="image/x-icon">
    {% else %}
        <link rel="icon" href="/static_1/images/favicon.ico?v=1.1" type="image/x-icon">
    {% endif %}
    <meta name="keywords" content="{{ keywords|safe }}"/>
    <meta name="description" content="{{ description|safe }}"/>
    {% if share_picture %}
        <meta property="og:image" content="{{ share_picture|safe }}" />
    {% endif %}
    <meta name="revisit-after" content="2 days"/>
    <meta http-equiv="Content-Language" content="{{ language }}"/>
    <meta name="dc.title" content="{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %}
        {{ sectionName|safe }}{% else %} {{ hotel_name|safe }} {% endif %} {% endif %}"/>
    <meta name="dc.description" content="{{ description|safe }}"/>
    <meta name="dc.keywords" content="{{ keywords|safe }}"/>
    <meta name="dc.language" content="{{ language }}"/>
    <meta name="dc.creator" content="{{ hotel_name }}"/>
    <meta name="dc.format" content="text/html"/>
    <meta name="dc.identifier" content="{{ hostWithoutLanguage }}{{ path }}"/>
    <meta name="format-detection" content="telephone=no">
    <!-- REVOLUTION BANNER CSS SETTINGS -->
    <link rel="stylesheet" type="text/css" href="/static_1/lib/rs-plugin/css/settings_unified.min.css" media="screen"/>
    <link rel="stylesheet" href="/static_1/lib/lightbox/css/lightbox.css" type="text/css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/templateBaseline.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/css/1140/1140.css"/>
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/pricescalendar/styles.css">
    <link rel="stylesheet" type="text/css" href="/static_1/plugins/dates-selector/css/datepicker_ext_inf.css">
    <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/banners.css?v=11.99"/>
    <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles.css?v=11.89"/>
    <!--[if IE 8]><link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/ie8.css" /><![endif]-->
    <!--[if lte IE 7]><script type="text/javascript">alert('{{ T_explorer6_no_soportado }}');</script><![endif]-->
    <!--[if lte IE 8]><script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script><![endif]-->
    <script type="text/javascript">
        if (navigator.userAgent.match(/Android/i)
                || navigator.userAgent.match(/webOS/i)
                || navigator.userAgent.match(/iPhone/i)
                || navigator.userAgent.match(/iPad/i)
                || navigator.userAgent.match(/iPod/i)
                || navigator.userAgent.match(/BlackBerry/i)
                || navigator.userAgent.match(/Windows Phone/i)) {
            document.write('<meta name="viewport" content="width=1160, initial-scale=1, user-scalable=yes">');
        }
    </script>
    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/snap.svg-min.js"></script>
    <script src="https://kit.fontawesome.com/c37d933413.js" crossorigin="anonymous"></script>
    {{ jquery|safe }}
    {{ extra_head|safe }}

    {{ all_tracking_codes_header|safe }}
</head>
<body itemscope itemtype="//schema.org/Hotel" class="
{% if not home %}inner_section {% else %}home {% endif %}
{% if hotel_info and hotel_info.namespace %}inner_section_hotel{% endif %}
">
{% if gtm_body_opening %}{{ gtm_body_opening|safe }}{% endif %}

{{ all_tracking_codes_body|safe }}
{{ rich_snippet|safe }}
<!--{% if home %}-->
<!--    <div class="preloading">-->
<!--        <div class="waves"></div>-->
<!--        <div class="waves2"></div>-->
<!--        <div class="ona-o">O</div>-->
<!--        <div class="loading"></div>-->
<!--    </div>-->
<!--{% endif %}-->
<meta itemprop="description" content="{{ description_microdata }}">
{% if lang_management %}<input type="hidden" id="lang_management" value="{{ lang_management }}">{% endif %}
{% if lang_default %}<input type="hidden" id="lang_default" value="{{ lang_default }}">{% endif %}
{% block content %}<!--EDIT HERE YOUR PAGE-->{% endblock %}
{% block additional_js %}
    {{ scripts_to_render_desktop|safe }}
    <script type="text/javascript" src="/static_1/i18n/messages_{{ language }}.js"></script>
    <script async defer src="/static_1/lib/owlcarousel/owl.carousel.min.js"></script>
    <script type="text/javascript" src="/static_1/scripts/common.js?v=1.3"></script>
    <script type="text/javascript" src="/static_1/plugins/pricescalendar/calendar.plugin.js"></script>
    <script type="text/javascript" src="/static_1/plugins/dates-selector/datepicker_v1.js?v=1.12"></script>
    <script type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/datepicker_custom.js?v=1.12"></script>
    <script>$(function () {
        DP_extend_info.config.booking_version = '7';
        DP_extend_info.config.months_show = 2;
        DP_extend_info.config.months_show_highlight = true;
        DP_extend_info.config.force_hightlight = true;
        DP_extend_info.config.hide_callback = function () {
            if($("#full_wrapper_booking").hasClass("fixed")){
                $(".booking_steps .step_2").removeClass("current_step");
                $(".booking_steps .step_3").addClass("current_step");
                $(".room_list_wrapper").slideDown();
                load_new_room_dates();
            }
        };
        DP_extend_info.config.custom_format_day_month = function (dateComponents) {
            dateComponents = dateComponents.split("/");
            var month_short = $.datepicker._defaults['monthNamesShort'][parseInt(dateComponents[1], 10) - 1];
            return "<span class='day'>" + dateComponents[0] + "</span><span class='month'>" + month_short + "</span>";
        };
    });
    $(window).load(function () {
        DP_extend_info.init();
        DP_extend_info.config.datepicker_sd_wrapper.datepicker( "option", "beforeShowDay", sd_beforeShowDay );
        DP_extend_info.config.datepicker_sd_wrapper.datepicker( "option", "onChangeMonthYear", datepicker_changeMonth );
        DP_extend_info.config.datepicker_ed_wrapper.datepicker( "option", "beforeShowDay", ed_beforeShowDay );
        DP_extend_info.config.datepicker_ed_wrapper.datepicker( "option", "onChangeMonthYear", datepicker_changeMonth );
        setTimeout(function(){
            $("#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room1 .room_title").html("{{ T_alojamiento }} 1");
            $("#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room2 .room_title").html("{{ T_alojamiento }} 2");
            $("#full_wrapper_booking .booking_form .room_list_wrapper .room_list .room3 .room_title").html("{{ T_alojamiento }} 3");
        }, 3000);
        $(".guest_selector label").html("{{ T_personas }}");
        prepare_countdowns("{{ T_dias }}", "{{ T_horas }}", "{{ T_minutos }}", "{{ T_segundos }}");
    });

        transfer_url_params_to_booking(['psh_ctid', 'psh_cpid']);
    </script>
    <script type="text/javascript" src="/static_1/lib/addAnimation.js"></script>
    <script async type="text/javascript" src="/static_1/scripts/booking_7.js"></script>
    <script async type="text/javascript" src="/static_1/scripts/booking_full_popup.js"></script>
    <script src="/static_1/scripts/hotel_selector_2.js"></script>
{#    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/jquery.scrollify.js?v=1"></script>#}
    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/booking_engine_personalize.js?v=1.39"></script>
    <script async type="text/javascript" type="text/javascript" src="/js/{{ base_web }}/functions.js?v=1.68"></script>
    <script async type="text/javascript" src="//www.tripadvisor.com/js3/conversion/pixel.js"></script>

    <div style="display: none;">{% include "/svg/svg2.html" %}</div>
{% endblock %}
{{ extra_content_website|safe }}
{{ all_tracking_codes_footer|safe }}
</body>
</html>
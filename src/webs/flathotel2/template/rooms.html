 <div id="wrapper-ourhotels">
    {% for banner in rooms %}


    {% if banner.visibleInWeb %}


    <div class="ourhotels-wrapper column4" id="room-{{ forloop.counter }}" >

        <div class="ourhotels-title">

          {{banner.name|safe}}

           <a class="cboxelement" id="image_ourhoteles" href="javascript:showGallery2([ {% for picture in banner.pictures %} {href : '{{ picture.servingUrl }}=s800', title : '{{banner.newtitle|safe}}'}, {% endfor %} ]);">
                   <img src="/img/{{ base_web }}/icono_fotos.jpg" class="ourhotels-img-lupa"  {% if not banner.pictures %} style="visibility: hidden;" {%  endif %} alt="icono lupa">
            </a>

        </div>

       <div class="ourhotels-img-whrapper">

           <a class="room_see_more-2"  href="#descripcion_completa_oculta" >
                <span >{{ T_ver_mas }}</span>
            <div class="crop-img-ourhotels">
                    <img class="ourhotels-top-img" src="{{banner.pictures.0.servingUrl|safe}}=s360" alt="{{banner.name|safe}}">
                </div>
            </a>

        </div>
        <a class="circle-mas room_see_more-1"  href="#descripcion_completa_oculta" >
                <img src="/img/{{ base_web }}/mas_hoteles.png" alt="icono ver mas"/>

        </a>



        <div class="ourhotels-description">{{banner.description|safe}}</div>
        <div  class="ourhotels-buttons">

                 <div class="ourhotels-button1"><a href="#descripcion_completa_oculta" class="room_see_more"> {{ T_ver_mas }} </a></div>



            <div class="ourhotels-button2"><a href="#data" class="button-promotion"> {{ T_reservar }} </a></div>
        </div>


    </div>



        {%  endif %}

    {% endfor %}


    {% if banner_icons %}
        <div class="banner_icons_wrapper container12"><!--
            {% for icon in banner_icons %}
                --><div class="icon">
                    <i class="fa {{ icon.title|safe }}"></i>
                    <span>{{ icon.description|safe }}</span>
                </div><!--
            {% endfor %}
        --></div>
    {% endif %}



</div>
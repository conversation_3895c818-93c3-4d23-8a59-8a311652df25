{% spaceless %}
    <!DOCTYPE html>
    <html lang="{{ language_code }}">
    <head>
        <title>{% if title_page %} {{ title_page|safe }} {% else %} {% if sectionName %} {{ hotel_name|safe }} |
            {{ sectionName|safe }} {% else %}  {{ hotel_name|safe }} {% endif %} {% endif %}</title>
        <meta http-equiv="content-type" content="text/html; charset=UTF-8"/>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">

        {% if namespace %}
            <link rel="icon" href="/static_1/images/favicon_{{ namespace }}.ico" type="image/x-icon">
        {% else %}
            <link rel="icon" href="/static_1/images/favicon.ico" type="image/x-icon">
        {% endif %}

        <!-- styles -->
        <link rel="stylesheet" type="text/css" href="/static_1/plugins/pricescalendar/styles.css?v=3">
        <link rel="stylesheet" type="text/css" href="/static_1/plugins/dates-selector/css/datepicker_ext_inf.css?v=1.111">
        <link href="//fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,200italic,300italic" rel="stylesheet">
        <link rel="stylesheet" type="text/css" href="/static_1/css/booking/booking_engine_2_vertical.css"/>
        <link rel="stylesheet" type="text/css" href="/css/{{ base_web }}/styles_landing.css?v=2"/>

        {{ jquery|safe }}
        {{ scripts_to_render_desktop|safe }}
        <script type="text/javascript" src="/static_1/i18n/messages_{{ language_code }}.js"></script>
        <script type="text/javascript" src="/js/{{ base_web }}/booking_popup_personalized.js"></script>
        <script type="text/javascript" src="/js/{{ base_web }}/functions_landing.js?v=1.1"></script>
        <script type="text/javascript" src="/static_1/plugins/pricescalendar/calendar.plugin.js?v=1"></script>
        <script type="text/javascript" src="/static_1/plugins/dates-selector/datepicker_v1.js?v=1.123"></script>
        <script>$(function () {
            DP_extend_info.config.booking_version = '2';
            DP_extend_info.init();
        })</script>
        <script src="/static_1/scripts/booking.js?v=1.1"></script>
        <script type="text/javascript" src="/static_1/scripts/common.js"></script>
        <script src="/static_1/lib/owlcarousel/owl.carousel.min.js"></script>
        {% if gtm_tag %}
            <!-- Google Tag Manager -->
            <script>(function (w, d, s, l, i) {
                w[l] = w[l] || [];
                w[l].push({'gtm.start': new Date().getTime(), event: 'gtm.js'});
                var f = d.getElementsByTagName(s)[0],
                        j = d.createElement(s), dl = l != 'dataLayer' ? '&l=' + l : '';
                j.async = true;
                j.src =
                        'https://www.googletagmanager.com/gtm.js?id=' + i + dl;
                f.parentNode.insertBefore(j, f);
            })(window, document, 'script', 'dataLayer', 'GTM-{{ gtm_tag }}');</script>
            <!-- End Google Tag Manager -->
        {% endif %}
        <script>
            $(window).load(function () {

                (function () {
                    var po = document.createElement('script');
                    po.type = 'text/javascript';
                    po.async = true;
                    po.src = 'https://apis.google.com/js/plusone.js';
                    var s = document.getElementsByTagName('script')[0];
                    s.parentNode.insertBefore(po, s);
                })();

                $(".myFancyPopup").fancybox({
                                                maxWidth: 800,
                                                maxHeight: 600,
                                                fitToView: false,
                                                width: '70%',
                                                height: '70%',
                                                autoSize: false,
                                                aspectRatio: false,
                                                closeClick: false,
                                                openEffect: 'none',
                                                closeEffect: 'none'
                                            });

{#                $(".button-promotion, .button_promotion").fancybox({#}
{#                    width: 'auto',#}
{#                    wrapCSS: 'fancy_data',#}
{#                    beforeLoad: function () {#}
{#                        $(".room-selector").val("1");#}
{#                        $(".datepicker1").val("");#}
{#                        $(".datepicker2").val("");#}
{#                        $(".hab2").hide();#}
{#                        $(".hab3").hide();#}
{#                        var widget = $(".full_width #booking").detach();#}
{#                        $("#data").html(widget);#}
{#                        //updateDates($.datepicker.formatDate("dd/mm/yy", new Date()));#}
{#                    },#}
{#                    afterClose: function () {#}
{#                        var widget = $("#data #booking").detach();#}
{#                        console.log(widget);#}
{#                        $(".full_width #wrapper_booking").html(widget);#}
{#                    }#}
{#                });#}

                $(".promocode_input_class").attr("placeholder", "{{ T_promocode }}");
                $("input[name='promocode']").each(function(){
                    $(this).val("{{ promocode|safe }}");
                });
            });
        </script>
        {% if countdown %}
            <script>
                var countDownDate = new Date("{{countdown|safe}}").getTime();

                $(window).load(function () {
                    var x = setInterval(function () {
                        updatecounter();
                    }, 1000);
                });

                function updatecounter() {
                    var now = new Date().getTime();
                    var distance = countDownDate - now;
                    var days = Math.floor(distance / (1000 * 60 * 60 * 24));
                    var hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                    if (hours < 10) {
                        hours = "0" + hours
                    }
                    var minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                    if (minutes < 10) {
                        minutes = "0" + minutes
                    }
                    var seconds = Math.floor((distance % (1000 * 60)) / 1000);
                    if (seconds < 10) {
                        seconds = "0" + seconds
                    }
                    document.getElementById("countdown").innerHTML = "<div class='countdown_element'>" + days + "<span> {{ T_dias }}</span></div><div class='countdown_separator'>:</div><div class='countdown_element'>" + hours + "<span> {{ T_horas }}</span></div><div class='countdown_separator'>:</div><div class='countdown_element'>"
                            + minutes + "<span> {{ T_minutos }}</span></div><div class='countdown_separator'>:</div><div class='countdown_element'>" + seconds + "<span> {{ T_segundos }}</span></div>";
                    if (distance < 0) {
                        clearInterval(x);
                        document.getElementById("countdown").innerHTML = "EXPIRED";
                    }
                }
            </script>
        {% endif %}
    </head>
    <body class="{{ language }}">
    <header>
        <div class="container12">
            <div id="logoDiv">
                <a>
                    <img itemprop="logo" src="{{ logotype }}=s120" alt="{{ hotel_name|safe }}"
                         title="{{ hotel_name|safe }}"/>
                </a>
            </div>
        </div>
    </header>

    <div id="slider_container">
        <div class="inner_picture">
            <img src="{{ slider.servingUrl|safe }}=s1900"/>

            <div class="text_slider">
                <div class="title_slider">{{ slider.title|safe }}</div>
                {{ slider.description|safe }}
                {% if countdown %}
                    <div id="countdown"></div>
                {% endif %}
            </div>
        </div>

        <div class="full_width">
            <div id="full_wrapper_booking">
                <div id="wrapper_booking" class="container12">
                    <div id="booking">
                        {{ booking_engine|safe }}
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="bannersx4_wrapper">
        {% for x in blockx4 %}
            <div class="banner_element">
                <div class="banner_image">
                    <img src="{{ x.servingUrl|safe }}"/>
                    <a class="button-promotion" href="#data"
                       {% if x.smartadults %}data-smartadults="{{ x.smartadults }}"{% endif %}
                       {% if x.smartkids %}data-smartkids="{{ x.smartkids }}"{% endif %}
                       {% if x.smartdateini %}data-smartdateini="{{ x.smartdateini }}"{% endif %}
                       {% if x.smartdatefin %}data-smartdatefin="{{ x.smartdatefin }}"{% endif %}
                       {% if x.smartpromocode %}data-promocode="{{ x.smartpromocode }}"{% endif %}
                            >{{ T_reservar }}</a>
                </div>

                <div class="banner_content">
                    <div class="center_content center_y">
                        {{ x.description|safe }}
                    </div>
                </div>
            </div>
        {% endfor %}
    </div>

    <div class="banner_text_wrapper">
        {{ banner.description|safe }}
    </div>

    <section class="banners-hotel">
        <div class="banners-hotel-wrapper cleardiv">
            {% for banner in hotel %}
                <div class="banner-hotel">
                    <div class="overlay"></div>
                    <div class="banner_image">
                        <img src="{{ banner.servingUrl }}=s600" alt="{{ banner.description|safe }}"/>
                        <a class="button-promotion" href="#data"
                           {% if banner.namespace %}data-namespace="{{ banner.namespace }}"{% endif %}
                           {% if banner.hotel_name %}data-hotelname="{{ banner.hotel_name|safe }}"{% endif %}
                           {% if banner.smartadults %}data-smartadults="{{ banner.smartadults }}"{% endif %}
                           {% if banner.smartkids %}data-smartkids="{{ banner.smartkids }}"{% endif %}
                           {% if banner.smartdateini %}data-smartdateini="{{ banner.smartdateini }}"{% endif %}
                           {% if banner.smartdatefin %}data-smartdatefin="{{ banner.smartdatefin }}"{% endif %}
                           {% if banner.smartpromocode %}data-promocode="{{ banner.smartpromocode }}"{% endif %}
                                >{{ T_reservar }}</a>
                    </div>
                    <div class="description-hotel">{{ banner.description|safe }}</div>
                </div>
            {% endfor %}
        </div>
    </section>

    {% if minigallery %}
        <div class="mini_gallery_wrapper">
            <div class="container12">
                <div class="flexslider_mini_gallery owl-carousel">
                    {% for x in minigallery %}
                        <a href="{{ x.servingUrl|safe }}=s1900" rel="lightbox[gallery]">
                            <img src="{{ x.servingUrl|safe }}" class="center_image">
                        </a>
                    {% endfor %}
                </div>
            </div>
        </div>

        <script>
            function showDatepickerFlexible() {
                console.log("Overwrited");
            }

            $(window).load(function () {
                $(".flexslider_mini_gallery").owlCarousel({
                    loop: true,
                    nav: true,
                    dots: false,
                    items: 3,
                    navText: ['<i class="fa fa-angle-left"></i>', '<i class="fa fa-angle-right"></i>'],
                    margin: 0,
                    autoplay: true,
                    responsive: {
                        0: {
                            items: 1
                        },
                        480: {
                            items: 2
                        },
                        768: {
                            items: 3
                        }
                    }
                });
            });
        </script>
    {% endif %}

    <div class="main_content_wrapper">
        <div class="main_content_subtitle">{{ content.subtitle|safe }}</div>
        <div class="main_content_description">{{ content.content|safe }}</div>
    </div>

    <footer>
        <div class="container12 social_like_wrapper">

            <div id="facebook_like">
                <div id="fb-root"></div>
                <script src="https://connect.facebook.net/es_ES/all.js#appId=128897243865016&amp;xfbml=1"
                        async></script>
                <div>
                    <fb:like font="" href="" layout="button_count" send="false" show_faces="false"
                             width="110"></fb:like>
                </div>
            </div>
            <div id="google_plus_one">
                <div class="g-plusone"></div>
            </div>
        </div>

        <div class="full-copyright">
            <div class="footer-copyright container12">

                <a class="myFancyPopup fancybox.iframe"
                   href="/{{ language }}/?sectionContent=politica-de-privacidad.html"
                   rel="nofollow">{{ T_politica_de_privacidad }}</a> |
                <a target="_blank" href="http://www.paratytech.com"
                   title="{{ T_motor_de_reservas }}">{{ T_motor_de_reservas }}</a> |
                <a target="_blank" href="/sitemap.xml" title="">Site Map</a> |
                <a target="_blank" href="/rss.xml">RSS</a>

            </div>

            {% if texto_legal %}
                <div id="div-txt-copyright" class="footer-copyright container12">
                    {{ texto_legal|safe }}
                </div>
            {% endif %}

        </div>
    </footer>

    <div style="display: none;">
        <div id="data">
            <div id="wrapper_booking_fancybox">
                <div id="booking_widget_popup" class="booking_widget_fancybox">
                    {{ booking_engine2 }}
                </div>
            </div>
        </div>
    </div>
    </body>
    </html>
{% endspaceless %}

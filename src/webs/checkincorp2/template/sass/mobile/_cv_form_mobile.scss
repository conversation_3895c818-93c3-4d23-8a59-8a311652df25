.cv_form_wrapper {
  position: relative;
  width: 100%;
  margin: 1em auto 0;
  padding: 1em 0;
  text-align: center;
  border: 1px solid lightgrey;
  box-sizing: border-box;
  .divInput {
    display: block;
    vertical-align: top;
    width: 70%;
    margin:  0 auto;
    label {
      color: #4B4B4B;
      font-size: 12px;
      &.error {
        display: inline-block;
        padding: 5px 10px;
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        border-radius: 5px;
        color: #721c24;
        .fa {
          color: black;
          font-size: 16px;
          margin-right: 10px;
        }
      }
      strong {
        font-weight: bold;
      }
    }
    input[type=text], input[type=file], select {
      display: block;
      width: 100%;
      padding: 10px;
      height: 33px;
      box-sizing: border-box;
      margin-bottom: 10px;
      border-width: 0;
      border-radius: 3px;
      color: #4B4B4B;
      background: #ededed;
      &.error {
        border:1px solid red;
      }
    }
    input[type=file] {
      height: auto;
      border: 2px dashed lightgrey;
    }
  }
  button {
    display: block;
    width: 50%;
    margin: 20px auto 0;
    padding: 20px 18px;
    color: white;
    letter-spacing: 1px;
    font-size: 20px;
    background-color: $corporate_2;
    @include transition(all,.6s);
     &:hover {
        background-color: $corporate_1;
        padding-right: 80px;
        background-image: url('/img/#{$base_web}/sending_email_1.gif');
        background-position: right center;
        background-size: contain;
        background-repeat: no-repeat;
     }
  }
  .fa-spinner, .overlay {
    display: none;
  }
  &.sending {
    background-color: #999;
    #cv_form {
      opacity: .3;
    }
    .overlay {
      position: absolute;
      top:0;bottom: 0;left: 0;right: 0;
    }
    .fa-spinner {
      display: block;
      color: white;
      position: absolute;
      bottom: 30px;left:0;right:0;
      margin: auto;
    }
  }
}

<div class="hotels_section" id="hotels_section">

<div class="filter_content container12">
    <div class="selector_content" data-content="hoteles_wrapper" >
        <a href="#" class="selector_map"><i class="fa icon-building"></i>{{ T_hoteles }}</a>
    </div>
    <div class="selector_content" data-content="banner_map_wrapper" >
        <a href="#" class="selector_map"><i class="fa icon-mapmarker"></i>{{ T_ver_mapa }}</a>
    </div>
</div>


<div class="hoteles_wrapper content_select container12">
    <div class="hotels_filter">
        <div class="filter_selector" data-filter="destinos"><span>{{ T_destinos }}</span></div>
        <div class="filter_selector" data-filter="categorias"><span>{{ T_categoria }}</span></div>
        <div class="filter_selector" data-filter="services"><span>{{ T_servicios }}</span></div>
        {% if brands %}{% for brand in brands %}
            <div class="filter_brand" data-filter="{{ brand.title|safe }}">
                <img src="{{ brand.servingUrl }}">
            </div>
        {% endfor %}{% endif %}
        <div class="filter_search">
            <input type="text" class="search_hotel_filter" placeholder="{{ T_buscar }}...">
        </div>

        <div class="filter_list destinos">
            {% for class, destiny in hotels_destinies.items() %}
                <div class="filter_element" data-filter="{{ class|safe }}">{{ destiny|safe }}</div>
            {% endfor %}
        </div>

        <div class="filter_list categorias">
            {% for label, info in hotels_categories.items() %}
                {% for star in info.stars %}
                    <div class="filter_element" data-filter="{{ star }}{{ label|safe }}">{% if star > 0 %}{{ star }} {% endif %}{{ info.text|safe }}</div>
                {% endfor %}
            {% endfor %}
        </div>

        <div class="filter_list services">
            {% for service in hotels_services %}
                <div class="filter_element" data-filter="ser{{ service.title|safe }}"><i class="fa {{ service.altText|safe }}"></i>{{ service.description|safe }}</div>
            {% endfor %}
        </div>
    </div>

<script>$(window).load(function () {
    $(".selector_content a").click(function (e) {e.preventDefault();});
    $(".selector_content").click(function () {
        $(".selector_content").removeClass("active");
        $(this).addClass("active");
        $(".content_select").slideUp();
        $("."+$(this).attr('data-content')).slideDown();
    });
    setTimeout(function () {
        $(".selector_content:first-of-type").click();
    },500);
    $(".filter_selector").click(function () {
        $(this).toggleClass("active");
        $("."+$(this).attr('data-filter')).slideToggle();
        $("."+$(this).attr('data-filter')).toggleClass("showed");
    });
    $(".filter_element, .filter_brand").click(function () {
        $(this).toggleClass("active");
        $(".hotel").slideUp();
        if($(".filter_element.active, .filter_brand.active").length){
            $(".hotel").each(function() {
                var hotel = $(this);
                var filter_actives = $(".filter_element.active, .filter_brand.active");
                hotel.removeClass("active");
                var filters_avaiable = {
                    "destiny": [],
                    "category": [],
                    "services": [],
                    "brand": []
                };
                filter_actives.each(function(){
                    var actual_filter = $(this).attr("data-filter"),
                        actual_filter_parent = $(this).parent();
                    if(actual_filter_parent.hasClass("destinos")) {
                        filters_avaiable['destiny'].push(actual_filter);
                    } else if(actual_filter_parent.hasClass("categorias")) {
                        filters_avaiable['category'].push(actual_filter);
                    } else if (actual_filter_parent.hasClass("services")) {
                        filters_avaiable['services'].push(actual_filter);
                    } else if ($(this).hasClass("filter_brand")) {
                        filters_avaiable['brand'].push(actual_filter);
                    }
                });

                if(filters_avaiable['destiny'].length && filters_avaiable['destiny'].indexOf(hotel.attr("data-destiny")) > -1) {
                    hotel.addClass("active");
                }
                if(filters_avaiable['brand'].length && filters_avaiable['brand'].indexOf(hotel.attr("data-brand")) > -1) {
                    hotel.addClass("active");
                }

                if ((hotel.hasClass("active") || hotel.is(":visible")) && filters_avaiable['category'].length) {
                    if (filters_avaiable['category'].indexOf(hotel.attr("data-stars")) > -1) {
                        hotel.addClass("active");
                    } else {
                        hotel.removeClass("active");
                    }
                }

                if ((hotel.hasClass("active") || hotel.is(":visible")) && filters_avaiable['services'].length) {
                    if(searchServices(hotel, filters_avaiable['services'])) {
                        hotel.addClass("active");
                    } else {
                        hotel.removeClass("active");
                    }
                }
            });

            $(".hotel.active").stop().slideDown();
        } else {
            $(".hotel").slideDown();
        }
    });
    $(".search_hotel_filter").keydown(function () {
         setTimeout(function(){
             searchHotelSection();
         }, 100);
    });
    $(".show_hotel_map").click(function () {
        var poi_lat = $(this).attr("data-lat"),
            poi_lng = $(this).attr("data-lng"),
            poi_bounds = [];

            poi_bounds.push({lat: poi_lat, lng: poi_lng});

            console.log(poi_lng);
            poi_lng = poi_lng-0.03;
            console.log(poi_lng);
            poi_bounds.push({lat: poi_lat, lng: poi_lng});
            poi_lng = poi_lng + 0.06;
            console.log(poi_lng);
            poi_bounds.push({lat: poi_lat, lng: poi_lng});

        $(".selector_content[data-content=banner_map_wrapper]").click();

        initMap(poi_bounds);
    });
    if (getUrlParameter('filter_location')) {
        var locations = getUrlParameter('filter_location').split(',');
        $(".filter_selector[data-filter=destinos]").click();
        $(".filter_element[data-filter="+locations[0]+"]").click();
    }
    if (getUrlParameter('filter_locations')) {
        var locations = getUrlParameter('filter_locations').split(','),
            filtered_locations = [];

        for (var x=0;x<locations.length;x++) {
            if (filtered_locations.indexOf(locations[x]) == -1){
                filtered_locations.push(locations[x]);
            }
        }
        $(".filter_selector[data-filter=destinos]").click();
        for (i = 0; i < filtered_locations.length; i++) {
            $(".filter_element[data-filter="+filtered_locations[i]+"]").click();
        }

    }
    if (getUrlParameter('filter_serv')) {
        var locations = getUrlParameter('filter_serv').split(',');
        $(".filter_selector[data-filter=services]").click();
        for (i = 0; i < locations.length; i++) {
            $(".filter_element[data-filter=ser"+locations[i]+"]").click();
        }

    }
});
function getUrlParameter(sParam) {
    var sPageURL = decodeURIComponent(window.location.search.substring(1)),
        sURLVariables = sPageURL.split('&'),
        sParameterName,
        i;

    for (i = 0; i < sURLVariables.length; i++) {
        sParameterName = sURLVariables[i].split('=');

        if (sParameterName[0] === sParam) {
            return sParameterName[1] === undefined ? true : sParameterName[1];
        }
    }
};
function searchServices(hotel, list_services) {
    var hotel_services = hotel.attr("data-services") ? hotel.attr("data-services").split(" ") : "";
    var all_services_ok = true;

    for(var i = 0; i < list_services.length ; i++) {
        if (hotel_services.indexOf(list_services[i]) < 0) {
            all_services_ok = false;
            break;
        }
    }

    return all_services_ok;
}
function searchHotelSection(){
    var searched_hotel = $(".search_hotel_filter").val();

    $(".hotel").each(function(){
        var actual_html = $(this).find(".hotel_info h2").html();
        actual_html = actual_html.toLowerCase();
        searched_hotel = searched_hotel.toLowerCase();

        if(actual_html.indexOf(searched_hotel) < 0){
            $(this).slideUp();
        }else {
            $(this).slideDown();
        }

        if(searched_hotel == ""){
            $(this).slideDown();
        }
    });
}</script>

{% for hotel in hoteles_list %}
    <div class="hotel" data-services="{% for service in hotel.services %}ser{{ service|safe }} {% endfor %}" {% if hotel.brand %}data-brand="{% for brand in hotel.brand %}{{ brand|safe }}{% endfor %}"{% endif %} data-stars="{{ hotel.stars|safe }}{{ hotel.stars_class|safe }}" data-destiny="{{ hotel.destino_class|safe }}">
        <div class="hotel_image">
            {% if hotel.gallery and hotel.gallery.0 %}
                <img src="{{ hotel.gallery.0.servingUrl|safe }}" alt="{{ hotel.gallery.0.altText|safe }}">
            {% endif %}
        </div>
        <div class="hotel_info">
            {% if hotel.fecha_apertura %}
                <span class="date_label">{{ T_proxima_apertura }} <strong>{{ hotel.fecha_apertura|safe }}</strong></span>
            {% endif %}
            <h2><a href="{{ hotel.link|safe }}" target="_blank">{{ hotel.name|safe }}</a></h2>
            <h3>{{ hotel.destino|safe }}, {{ hotel.region|safe }}</h3>
            <p>{{ hotel.description|safe }}</p>
            <div class="hotel_services">
                {% for service in hotel.services_icons %}
                <span class="tooltip"><i class="fa {{ service.ico|safe }}" {% if service.color %}styles="color: {{ service.color }}"{% endif %}></i><span class="tooltiptext">{{ service.description|safe }}</span></span>
                {% endfor %}
            </div>
            <div class="hotel_links">
                <a href="javascript:showGalleryFixed([{% for image in hotel.gallery %}{href : '{{ image.servingUrl|safe }}=s1140', title : '{{ image.title|safe }}'}{% if not loop.last %},{% endif %}{% endfor %}]);"><i class="fa icon-pictures"></i>{{ T_ver_fotos }}</a>
                <a href="#hotels_section" class="show_hotel_map" data-lat="{{ hotel.lat }}" data-lng="{{ hotel.lng }}"><i class="fa icon-mapmarker"></i>{{ T_ver_mapa }}</a>
                <a href="tel:{{ hotel.telefono|safe }}"><i class="fa icon-phone2"></i>{{ hotel.telefono|safe }}</a>
                <a href="mailto:{{ hotel.email|safe }}"><i class="fa icon-email"></i>{{ hotel.email|safe }}</a>
            </div>
            <div class="hotel_links_2">
                {% if hotel.link %}<a href="{{ hotel.link|safe }}" target="_blank"><i class="fa fa-external-link"></i></a>{% endif %}
                {% if not hotel.hide_booking %}
                    <a href="{% if hotel.namespace == "checkin-masia" %}mailto:<EMAIL>{% else %}javaScript:booking_click('{{hotel.namespace|safe}}'){% endif %}"><span>{{ T_reservar }}</span></a>
                {% endif %}
            </div>
        </div>
    </div>
{% endfor %}

</div>

<div class="banner_map_wrapper content_select container12">
{% if google_maps_api %}
    <script>
    hotels = [];
    locations = [];
    </script>
    {% for continente,info in hoteles.items() %}
    {% for pais,hoteles in info.paises.items() %}
        {% for hotel in hoteles %}
            <script>
            hotels.push("{{hotel.poi_description|safe}} <a href='javaScript:booking_click(\"{{hotel.namespace|safe}}\")'>{{T_reservar}}</a>");
            locations.push({lat: {{hotel.lat|safe}}, lng: {{hotel.lng|safe}}});
            if(typeof(poi_bounds_{{ continente|safe }}) != "undefined") {
                poi_bounds_{{ continente|safe }}.push({lat: {{hotel.lat|safe}}, lng: {{hotel.lng|safe}}});
            } else {
                poi_bounds_{{ continente|safe }} = [];
                poi_bounds_{{ continente|safe }}.push({lat: {{hotel.lat|safe}}, lng: {{hotel.lng|safe}}});
            }

            </script>
        {% endfor %}
    {% endfor %}
    <div class="selector_map_wrapper" data-bounds="poi_bounds_{{ continente|safe }}" >
        <a href="#" class="selector_map">{{ info.continente_label|safe }}</a>
    </div>

    {% if loop.last %}<script>$(window).load(function () {
        var header_map = $(".content_subtitle_title").html();
        header_map = header_map.replace("@@C@@", "{{ info.n_continentes|safe }}").replace("@@P@@", "{{ info.n_paises|safe }}").replace("@@H@@", "{{ info.n_hoteles|safe }}");
        $(".content_subtitle_title").html(header_map);
        $(".selector_map_wrapper a").click(function (e) {e.preventDefault();});
        $(".selector_map_wrapper").click(function () {
            $(".selector_map_wrapper").removeClass("active");
            $(this).addClass("active");
            initMap(eval($(this).attr("data-bounds")));
        });
        setTimeout(function () {
            $(".selector_map_wrapper:first-of-type").click();
        },500);
    });</script>{% endif %}

    {% endfor %}
    {% include "banners/_map.html" %}
{% else %}
    {% for continente,info in hoteles.items() %}
        <div class="selector_map_wrapper iframe_target" iframe_url="{{ info.iframe_maps|safe }}">
            <a href="#" class="selector_map">{{ info.continente_label|safe }}</a>
        </div>

        {% if loop.last %}
            <script>$(window).load(function () {
                if ($(".content_subtitle_title").length) {
                    var header_map = $(".content_subtitle_title").html();
                    header_map = header_map.replace("@@C@@", "{{ info.n_continentes|safe }}").replace("@@P@@", "{{ info.n_paises|safe }}").replace("@@H@@", "{{ info.n_hoteles|safe }}");
                    $(".content_subtitle_title").html(header_map);
                }
                $(".selector_map_wrapper a").click(function (e) {
                    e.preventDefault();
                });
                $(".selector_map_wrapper").click(function () {
                    $(".selector_map_wrapper").removeClass("active");
                    $(this).addClass("active");
                    $(".iframe_target_shower").attr('src', $(this).attr('iframe_url'));
                });
                setTimeout(function () {
                    $(".selector_map_wrapper:first-of-type").click();
                }, 500);
            });</script>
        {% endif %}
    {% endfor %}


    <iframe class="iframe_target_shower" src="https://www.google.com/maps/d/embed?mid=1FwhCGW96ldQnwqlqolOoLFK9nAYWgLgu&hl=es" width="100%" height="400"></iframe>


{% endif %}
</div>

</div>
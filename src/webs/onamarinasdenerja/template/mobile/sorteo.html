    <script type="text/javascript" src="/js/maria/sorteo.js?v=2"></script>

    <div class="sorteo_content">
        <h1 class="sorteo_title">{{ actual_content.subtitle|safe }}</h1>
        <div class="sorteo_text">{{ actual_content.content|safe }}</div>
    </div>

    {% if sorteo_video %}
    <div class="video-sorteo" {% if i_am_mobile %} style="text-align:center"  {% endif %}>
        <p class="disabled part">{{ sorteo_base.subtitle|safe }}</p>

        <span style="display: none;" class="subject_sorteo">{{ sorteo_base_2.content|safe }}</span>

        <p class="part two" style="display: none;">{{ sorteo_base_2.subtitle|safe }}</p>


        <div class="video_wrapper">
            {% if not i_am_mobile %}<div class="overlay_video"></div>{% endif %}
            {{ sorteo_video.content|safe }}
        </div>

    </div>
    {% endif %}

    <div class="sort-content">
        <div class="sort-wrapper container12">
            <div class="sort-form" style="display:none">
                <form action="/utils/?action=contact" id="sorteo" method="post" novalidate="novalidate">
                    <ul>
                        <li>
                            <label>{{ T_nombre_y_apellidos }}</label>
                            <input type="text" id="name" name="name" class="bordeInput" value=""/>
                        </li>
                        <li>
                            <label>{{ T_email }}</label>
                            <input type="text" id="email" name="email" class="bordeInput" value=""/>
                        </li>
                        <li>
                            <label>{{ T_confirm_email }}</label>
                            <input type="text" id="confirm_email" name="confirm_email" class="bordeInput" value=""/>
                        </li>
                        <li>
                            <label>{{ T_opina_video }}</label>
                            <textarea name="comment" id="comment" class="bordeInput"></textarea>
                        </li>
                        <li><input type="button" value="{{ T_enviar|safe }}" id="contact-button-sorteo" onclick="analytics_register_event('contact', 'lead', window.location.pathname);"/></li>
                    </ul>
                    <input type="hidden" id="destination_email" name="destination_email"  value="<EMAIL>"/>
                </form>
            </div>
            {{ sorteo_base.content|safe }}
        </div>
    </div>


    {% if i_am_mobile %}



        <script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
        <script type="text/javascript">
            //Analytics events register
            function analytics_register_event(category, action, label){
                try {
                    ga('send', {
                        hitType: 'event',
                        eventCategory: category,
                        eventAction: action,
                        eventLabel: label
                    });

                    dataLayer.push({
                        'event': 'event',
                        'eventCategory': category,
                        'eventAction': action
                    });
                }catch(err){
                    console.log(err);
                    console.log("Cant register calendar event")
                }
            }

            $(function () {
                $("iframe").each(
                        function (index, elem) {
                            elem.setAttribute("width", "95%");
                            elem.setAttribute("height", "auto");
                        }
                );

                setTimeout(function () {
                    $(".disabled").hide();
                    $(".sort-form").slideDown("slow");
                    $(".two").show().click(function () {
                        $('html, body').animate({
                            scrollTop: $(".sort-form").offset().top - 100
                        }, 1000);
                    });
                }, 160000);
            });

        </script>
        <style>
            .sort-form form {
                background: #ededed;
                padding: 16px;
                box-sizing: border-box;
                width: 95%;
                text-align: center;
                margin: 20px auto;
            }

            .sort-form form ul li {
                margin: 30px 0;
                text-align: center;
            }

            .sort-form form textarea {
                height: 70px;
                width: 100%;
                margin: auto;
                padding: 10px;
                border: 1px solid #BDBBBB;
                box-sizing: border-box;
            }

            .sort-form form input {
                width: 100%;
                box-sizing: border-box;
                margin: auto;
                border: 1px solid #BDBBBB;
                padding: 10px;
            }

            .sort-form form input.input-error{
                border: 1px solid red;
            }

            .sort-form form input[type="button"]#contact-button {
                width: 100% !important;
                margin: auto;
                border: 1px solid #BDBBBB;
                padding: 10px;
                background: #2c73b5 !important;
                color: white;
                font-size: 2em;
            }

            .sort-form form label {
                position: relative;
                font-size: 2em;
                color: #2c73b5;
                margin-bottom: 10px;
                display: block;
            }

            .sort-form label.error {
                color: red;
                font-size: 12px;
            }

            .video-sorteo p.part {
                display: block;
                margin: 0 auto 2em;
                color: white;
                font-weight: 300;
                text-transform: uppercase;
                background: #2c73b5;
                width: 95%;
                padding: 10px 10px;
                text-align: center;
                line-height: 30px;
                font-size: 2em;
            }

            .sorteo-base {
                font-size: 2em;
                line-height: 1.3em;
                text-align: center;
                color: gray;
                margin-top: 2em;
            }
        </style>
    {% endif %}
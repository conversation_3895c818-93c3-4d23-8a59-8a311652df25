.banner_v2_grid_wrapper {
  display: table;
  width: 100%;
  background: $terracota;
  .section {
    position: relative;
    width: 50%;
    height: 540px;
    float: left;
    background: $terracota;
    color: white;
    .content {
      @include center_y;
      left: 0;
      width: 100%;
      max-width: 550px;
      padding: 0 0 0 100px;
      .title {
        margin-bottom: 20px;
      }
      .btn {
        margin-left: -10px;
        margin-top: 10px;
        text-transform: uppercase;
        svg {
          * {
            fill: $charcoal;
          }
        }
      }
    }
  }
  .banner_v2_grid {
    display: table;
    width: 50%;
    float: right;
    a {
      position: relative;
      width: 50%;
      height: 270px;
      display: block;
      float: left;
      overflow: hidden;
    }
    img {
      @include center-image;
      min-width: 101%;
    }
  }
}
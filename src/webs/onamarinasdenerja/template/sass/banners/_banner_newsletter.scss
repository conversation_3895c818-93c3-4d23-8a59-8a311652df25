.newsletter_social_wrapper {
  padding: 30px 0;
  height: auto;
  background-image: url("/img/#{$base_web}/bg-bottom.png");
  background-size: 1400px 120px;
  background-position: center bottom;
  background-color: white;

  * {
    box-sizing: border-box;
  }

  .center_newsletter_social {
    margin: 15px auto 0;
    padding: 30px 0;
    background: white;
    box-shadow: 0 0 5px rgba(0,0,0,.10);
    a {
      text-decoration: none;
    }
  }

  .newsletter_wrapper {
    display: none; //disabled for ona traveller
    padding: 30px 0 0;
    margin: 0;
    width: 100%;
    background-image: url("/img/#{$base_web}/bg-ones.png");
    background-size: 1400px 120px;
    background-position: center;
    background-color: #EDEDED;

    .container12 {
      background: white;
      padding: 20px 40px;
      box-shadow: 0 0 5px rgba(0,0,0,.05);
    }

    .newsletter_content {
      display: inline-block;
      vertical-align: middle;
      width: 370px;
      padding-right: 20px;

      .newsletter_title {
        text-transform: uppercase;
        color: $corporate-4;
        font-weight: 600;
        font-size: 25px;
      }

      .newsletter_description {
        text-transform: uppercase;
        color: $corporate-4;
        font-size: 14px;
        margin-top: 20px;
      }
    }

    .newsletter_icos_link_wrapper {
      display: inline-block;
      vertical-align: middle;
      width: 690px;
      background-color: white;
      padding: 20px 0;

      .newsletter_icos_wrapper {
        display: inline-block;
        vertical-align: middle;
        width: 500px;

        .icon_element {
          display: inline-block;
          vertical-align: top;
          text-align: center;
          width: calc((100% - 15px) / 4);

          .icon_picture {
            border-radius: 50%;
            display: inline-block;
            position: relative;
            overflow: hidden;
            width: 60px;
            height: 60px;

            i.fa {
              color: $corporate_1;
              font-size: 46px;
            }
          }

          .icon_title {
            color: $corporate_4;
            text-transform: uppercase;
            font-size: 16px;
            margin-top: 10px;
          }
        }
      }

      .newsletter_link {
        display: inline-block;
        vertical-align: middle;
        margin-left: 10px;

        a {
          text-decoration: none;
          display: inline-block;
          height: 50px;
          width: 170px;
          background-color: $corporate_1;
          color: white;
          position: relative;
          overflow: hidden;

          &:hover {
            .arrow {
              background-color: darken($corporate_4, 5%);
            }
          }

          .newsletter_link_text {
            text-transform: uppercase;
            @include center_y;
            left: 0;
            width: calc(100% - 50px);
            text-align: center;
          }

          .arrow {
            font-family: 'fontawesome', sans-serif;
            display: inline-block;
            position: absolute;
            top: 0;
            right: 0;
            bottom: 0;
            background-color: darken($corporate_1, 10%);
            width: 50px;
            @include transition(background-color, .4s);

            &:before {
              content: '\f054';
              color: white;
              @include center_xy;
              font-size: 25px;
            }
          }
        }
      }
    }
  }
}

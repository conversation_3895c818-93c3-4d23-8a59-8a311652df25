body footer {
  a {
    color: white;
    @include transition(all, .6s);

    &:hover {
      opacity: .6;
    }
  }

  .footer_content {
    @include base_banner_styles;
    background-color: $black;
  }

  .footer_columns_wrapper {
    @include display_flex;
    width: 100%;
    justify-content: space-between;

    .footer_column {
      display: inline-flex;
      flex-wrap: nowrap;
      width: 50%;
      justify-content: flex-start;
      padding-right: 40px;
      font-size: 20px;
      line-height: 35px;

      &:nth-of-type(even) {
        justify-content: center;
      }

      &:nth-of-type(even) {
        padding: 0 0 0 40px;
      }

      .logo_wrapper {
        display: inline-block;
        vertical-align: top;
        padding-right: 80px;

        img {
          max-width: 190px;
        }
      }

      .desc {
        @include text_styles(white);
        text-align: left;
        display: inline-block;
        vertical-align: top;

        .title {
          font-family: $title_family;
          font-weight: 900;
          font-size: 14px;
          line-height: 23px;
          letter-spacing: 0;
          padding-bottom: 15px;
        }

        .menu_links_column {
          @include display_flex;

          li {
            width: 100%;

            a {
              display: inline-block;
              padding: 4px 0;
              margin: 0;
              text-transform: uppercase;
              color: white;
              @include transition(all, .6s);

              &:hover {
                opacity: .6;
              }
            }
          }
        }
      }

      .social_footer {
        display: block;
        padding-top: 10px;

        a {
          font-size: 20px;
          color: white;
          display: inline-block;
          vertical-align: middle;
          margin-right: 10px;
          @include transition(all, .6s);

          &:hover {
            color: $corporate_1;
          }
        }
      }
    }
  }

  .footer_legal_text_wrapper {
    padding: 30px 0 20px;
    color: white;
    text-align: center;

    .legal_text {
      display: block;
      font-size: 14px;
    }

    .footer_links_wrapper {
      display: block;

      a {
        font-size: 14px;
        line-height: 20px;
        color: white;
        text-transform: uppercase;
        @include transition(all, .6s);

        &:hover {
          opacity: .6;
        }
      }
    }
  }
}
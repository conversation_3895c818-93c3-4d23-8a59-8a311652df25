<div class="filter_offers_group_wrapper">
    <select name="filter_offers" id="filter_offers" multiple>
        <option value="" disabled selected hidden>{{ T_elija_tipo_oferta }}</option>
        <optgroup label="{{ T_destino }}">
            {% for x in destiny_elements %}
                <option value="{% for hotel in x.hotels_sections %}{{ hotel|safe }}{% if not loop.last %};{% endif %}{% endfor %}">{{ x.title|safe }}</option>
            {% endfor %}
        </optgroup>
    </select>
</div>

<div class="owlslider owl-carousel promotions_wrapper">
{% for promotion in promotions %}
    <div class="offer">
        <div class="offer_background" style="background-image: url('{{promotion.pictures.0}}');"></div>
        <div class="offer_content">
            <div class="picture"><img src="{{promotion.pictures.0}}"></div>
            <h3>{{promotion.name|safe}}</h3>
            <div class="desc">{{promotion.description|safe}}</div>
            <div class="offer_links_wrapper">
                {% if promotion.link %}
                    <a href="{{promotion.link|safe}}" class="offer_link"><i class="fa fa-plus"></i></a>
                {% endif %}
                <div class="button-promotion"><i class="fa fa-calendar"></i>{{T_reservar}}</div>
            </div>
        </div>
    </div>
{% endfor %}
</div>

<div class="hidden_promotions" style="display: none">
    {% for x in promotions %}
        <div class="offer" data-filter="{% for hotel in x.hotels_availables %}{{ hotel }};{% endfor %}{% for promo_type in x.promotion_filters %}{{ promo_type }}{% if not loop.last %};{% endif %}{% endfor %}">
            <div class="offer_background" style="background-image: url('{{x.pictures.0}}');"></div>
            <div class="offer_content">
                <div class="picture"><img src="{{x.pictures.0}}"></div>
                <h3>{{x.name|safe}}</h3>
                <div class="desc">{{x.description|safe}}</div>
                <div class="offer_links_wrapper">
                    {% if x.link %}
                        <a href="{{x.link|safe}}" class="offer_link"><i class="fa fa-plus"></i></a>
                    {% endif %}
                    <div class="button-promotion"><i class="fa fa-calendar"></i>{{T_reservar}}</div>
                </div>
            </div>
        </div>
    {% endfor %}
</div>

<script>
$(window).load(function () {
    /* Main slider*/
    var offerowl_params = {
        loop: {% if elements|length > 1 %}true{% else %}false{% endif %},
        nav: true,
        dots: false,
        items: 1,
        lazyLoad: true,
        margin: 0,
        autoplay: false
    };
    var owl_carousel = $(".promotions_wrapper").owlCarousel(offerowl_params);

    $("#filter_offers").change(function(){
        owl_carousel.trigger('destroy.owl.carousel');
        owl_carousel.find(".owl-stage").remove();
        owl_carousel.find(".offer").remove();
        console.log("Carousel destroyed. Creating a new carousel");
        var options_selected = $(this).val();
        var select_lenght = options_selected ? $(this).val().length : 0;
        if (select_lenght == 0) {
            $(".hidden_promotions .offer").each(function(){
                $(".promotions_wrapper").append($(this).clone(true, true));
            });
            owl_carousel = $(".promotions_wrapper").owlCarousel(offerowl_params);
            return false;
        }
        var options_explode = [];
        for(var i=0; i<select_lenght; i++){
            var splitted_list = options_selected[i].split(";");
            $.each(splitted_list,function(n){
                options_explode.push(splitted_list[n]);
            });
        }
        var options_unique = new Set(options_explode);

        $(".hidden_promotions .offer").each(function(){
            var current_offer = $(this);
            var filter_options = $(this).attr("data-filter").split(";");
            $.each(filter_options, function(n){
                if (options_unique.has(filter_options[n])) {
                    console.log("Coincide una opcion");
                    console.log(filter_options[n]);
                    $(".promotions_wrapper").append(current_offer.clone(true, true));
                    return false;
                }
            });
        });

        owl_carousel = $(".promotions_wrapper").owlCarousel(offerowl_params);
    });
});
</script>
<style>
    body {
        padding-bottom: 0;
    }
</style>
{# Blocks bottom x3 x4 x5 #}
{% if blocks_x5 %}
    <div class="blocks_x5_background">
        <img class="general_backgorund_x5" src="{{ pictures.0.servingUrl }}=s1900">
        <div class="owlslider owl-carousel blocks_x5_wrapper">

            {% for block_element in blocks_x5 %}
                <div class="block_element">
                    <div class="block_element_image">
                        <a  href="{{ block_element.servingUrl }}=s800" rel="lightbox[blocks]" title="{{ block_element.name|safe}}" class="swipebox">
                            <img class="block_element_background" src="{{ block_element.servingUrl }}">
                        </a>
                    </div>
                    <div class="room_wrapper_element">
                        <h3 class="block_element_title">{{ block_element.title|safe }}</h3>
                        <div class="block_element_description">{{ block_element.description|safe }}</div>
                        {% if block_element.linkUrl %}
                            <a href="{{ block_element.linkUrl }}" class="block_element_link">
                                <span>{% if block_element.button %}{{ block_element.button|safe }}{% else %} <i class="fa fa-plus"></i> {{ T_ver_mas }} {% endif %}</span>
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>
{% endif %}

<script>
$(window).load(function () {
    /* Main slider*/
    offerowl_params = {
        loop: true,
        nav: true,
        dots: false,
        items: 1,
        lazyLoad: true,
        margin: 0,
        autoplay: false
    };
    var owl_carousel = $(".blocks_x5_wrapper").owlCarousel(offerowl_params);
});
</script>

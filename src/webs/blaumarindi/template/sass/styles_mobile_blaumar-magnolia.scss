$fontawesome5: true;
$is_mobile: true;
$mobile_padding: 20px;

// colors definitions
$white: rgb(255, 255, 255);
$gray-1: rgb(90, 90, 90);
$gray-2: rgb(120, 120, 120);
$gray-3: rgb(190, 190, 190);
$gray-4: rgb(230, 230, 230);

// corporative colors definitions
$corporate_1: #FDBE87;
$corporate_2: #F3EFE8;
$corporate_3: #696158;

$black: #212322;
$lightgrey: #F4F4F4;
$grey: #CFD1D3;
$grey1: rgba($grey, .7);
$grey2: #707070;
$grey3: #707070;
$color_text: $black;


@mixin base_mobile_styles() {
  position: relative;
  padding: $mobile_padding;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  * {
    box-sizing: border-box;
  }
}

@import "defaults";
@import "styles_mobile/2/2";
@import "mobile/mobile_styles";


body {
  font-family: $text_family;

  .hi-widget-container {
    .hi-launcher-container {
      bottom: 210px !important;
    }
  }

  &.inner_section {
    .hi-widget-container {
      .hi-launcher-container {
        bottom: 120px !important;
      }
    }
  }

  a {
    text-decoration: none;
    color: $corporate_2;
  }

  strong, b {
    font-weight: 700;
  }

  .main_menu {
    background-color: $corporate_3;
  }

  nav a {
    color: $black;
  }

  header {
    .top_sections {
      @include center_y;
      right: 20px;

      a {
        display: inline-block;

        i {
          font-size: 28px;
          color: $corporate_3;
        }
      }
    }
  }

  #full_wrapper_booking {
    .entry_label_calendar, .departure_label_calendar {
      background-color: $corporate_1;
    }

    .wrapper_booking_button {
      .submit_button {
        background-color: $corporate_1;
        font-family: $title_family;
        color: $black;
        font-size: 24px;
      }
    }

    .dates_selector_personalized .start_end_date_wrapper {
      .start_date_personalized, .end_date_personalized {
        padding: 15px 10px 15px;

        .day {
          font-size: 50px;
          line-height: 60px;
        }

        .month {
          font-size: 22px;
        }

        .year {
          color: #666;
          font-size: 19px;
        }
      }
    }
  }

  .mobile_engine {
    font-family: $title_family;

    &:not(.open) {
      .mobile_engine_action {
        i, .fa {
          display: none;
        }
      }
    }
    .mobile_engine_action {
      font-size: 22px;
      color: $black;
      font-family: $title_family;
      letter-spacing: 1px;
      span {
        margin-left: 0;
        vertical-align: baseline;
        font-size: 22px;
      }
    }


    #full_wrapper_booking {
      .booking_form_title {
        .best_price {
          font-family: $title_family;
        }
      }

      .dates_selector_personalized {
        .start_end_date_wrapper {
          .end_date_personalized, .start_date_personalized {
            font-family: $subtitle_family;

            .day, .month, .year {
              color: $black;
            }
          }
        }

        .dates_selector_label {
          font-family: $subtitle_family;
        }
      }

      .guest_selector {
        .placeholder_text {
          font-family: $subtitle_family;
        }
      }

      .promocode_wrapper {
        input.promocode_input {
          font-family: $subtitle_family;

          ::placeholder {
            font-family: $subtitle_family;
          }
        }
      }

      .num_nights_label, .entry_date_label, .children_label, .rooms_label, .adults_label, .promocode_label, .departure_date_label {
        font-family: $subtitle_family;
      }

      .room_list_wrapper .room_list_wrapper_close {
        background-color: $corporate_1;
        font-family: $title_family;
        color: $black;
        font-size: 24px;
      }
    }
  }

  header, nav {
    i, .mailto i {
      color: $corporate_3;
    }
  }

  .main_menu {
    .social_menu {
      a i {
        background-color: transparent;
      }
    }
  }

  .breadcrumbs {
    background-color: $corporate_3;
  }

  .promotions_wrapper .offer_background {
    background-image: none !important;
    background-color: $corporate_3;
  }

  .main-owlslider {
    .owl-item {
      &:before {
        content: '';
        @include full_size;
        z-index: 1;
        background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0));
      }
    }

    .description_text {
      @include center_xy;
      top: 40%;
      width: 80%;
      z-index: 30;
      @include title_styles;
      font-size: 42px;
      line-height: 48px;
      color: white;
      text-align: center;

      .subtitle {
        color: white;
        font-size: 18px;
        line-height: 24px;
      }

      a {
        display: block;
        margin-top: 20px;
        color: white;
        text-transform: uppercase;
        font-size: 14px;
        font-family: $text_family;
      }
    }
  }

  .section_content {
    .location_content .section-title {
      display: none;
    }

    > h1, h1.section-title, .content_subtitle_title, .section_title, h2.section_title, .location_content .section-subtitle {
      @include title_styles;
      text-align: center;
      padding: 20px 20px 0;

      span {
        color: inherit;
      }

      #section_title_img {
        display: inline-block !important;
        width: 230px;
        max-width: 100%;
        overflow: hidden;
        position: relative;

        img {
          max-width: 100%;
          max-height: 100%;
        }
      }
    }

    div.content, div.content_subtitle_description, .section-content, .contact_content_element, .default_reservation_text {
      @include text_styles;
      text-align: center;
      width: auto;
      padding: 20px 20px 0;
    }
  }

  .btn_personalized_1 {
    @include btn_styles;
  }

  .btn_personalized_2 {
    @include btn_styles_2;
  }

  .rooms_wrapper .room_block, .promotions_wrapper {
    .room_info, .offer_content {
      h1, h3 {
        @include title_styles;
        text-align: center;
        font-size: 30px;
        padding: 10px;
        line-height: 36px;

        .subtitle {
          font-size: 20px;
          line-height: 24px;
        }
      }

      .room_description, .desc {
        @include text_styles;
        line-height: 20px;
        font-size: 13px;
      }
    }

    .buttons a:not(.button-promotion) {
      background-color: $black;
    }
  }
  .rooms_wrapper .room_block .buttons {
    display: flex;
    align-items: center;
    justify-content: center;
    a {
      padding: 8px 20px;
    }
    a:nth-child(2) {
      color: $black;
    }

  }
  .rooms_wrapper .room_block .room_info {
    .label_extra_info {
        position: relative;
        display: inline-block;
        box-sizing: border-box;
        padding: 0px 10px 0px 10px;
        margin: 0 5px;
        background-color: $corporate_1;
        font-size: 16px;
        color: white;
        top: -5px;
        border-radius: 9px;
    }
  }

  .gallery_divided_title span {
    font-family: $title_family;
    font-size: 20px;
    font-weight: 400;
    bottom: 23px;
  }

  .minigallery_wrapper {
    .owl-prev, .owl-next {
      background: rgba($corporate_2, .8);
    }
  }

  .form_subtitle {
    @include title_styles;
    margin-top: 20px;
  }

  .promotions_wrapper {
    .offer_content {
      .button-promotion {
        color: $black;
        font-size: 22px;
        font-family: $title_family;
      }

      .desc {
        padding-bottom: 40px !important;
      }
    }
  }

  .my_reservation_section {
    .my-reservation-form {
      a[data-role=button] {
        background-color: #e55558;
        font-family: $title_family;
        font-size: 21px;
        &.modify_booking {
          background-color: $corporate_3;
        }
      }
    }
  }
}

.location_wrapper {
  display: flex;
  flex-flow: column nowrap;

  .map {
    order: 3;
  }
}

.minigallery_wrapper {

  .owl-nav {
    z-index: 10;

    .owl-prev {
      background: rgb(0, 0, 0);
      background: linear-gradient(90deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0) 100%);
    }

    .owl-next {
      background: rgb(0, 0, 0);
      background: linear-gradient(270deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0) 100%);
    }
  }
}

.my_reservation_section .my-reservation-form a[data-role=button]:not(:first-of-type) {
  background: $corporate_2;
}

.loading_animation_popup .spincircle:after {
  border: 6px solid $corporate_1 !important;
  border-color: transparent transparent $corporate_1 transparent !important;
}


.offers_wrapper.banner_oferta_extra {
  background-color: white;
  .banner{
    background-color: aliceblue;
  }

  .pictures img {
    position: relative;
    z-index: 2;
    width: 100%;
    border-radius: 10px 10px 0 0;
    height: 50%;
    overflow: hidden;
  }
  .extra_info{
    margin-top: -20px;
    animation-name: promotion_down;
    animation-duration: 2s;

    a {
      z-index: 2;
    }
  }
  .content{
    padding-bottom: 20px;

    .title {
      font-size: 20px;
      font-weight: 600;
    }
  }
}

.extra_text_background {
  background-color: $corporate_2;
}

.banner_img_width img{
  width: 100%;
}

.banner_packages_wrapper {
  background-color: $corporate_2;
  padding-bottom: 60px;

  h2 {
    @include title_styles;
    padding-top: 40px;
  }

  img {
    max-height: 275px;
  }

  .extra_info a {
    position: absolute;
    left: 20px;
    top: 215px;
  }

  .title {
    font-family: $title_family;
    padding-bottom: 15px;
    padding-top: 40px;
    text-align: left;
    font-size: 30px;
    line-height: 34px;
    letter-spacing: 1.2px;
    font-weight: 700;

    small {
      font-family: 'Oswald';
      font-size: 18px;
      font-weight: 100;
      line-height: 2;
    }
  }

  .owl-nav {
    display: none;
    position: absolute;
    top: 100%;
    width: 100%;

    .owl-nav, .owl-prev {
      position: absolute;

      img {
        filter: invert(100%);
      }
    }

    .owl-next {
      position: absolute;
      right: 0;
      bottom: -38px;
      transform: rotate(180deg);

      img {
        filter: invert(100%);
      }
    }
  }

  .owl-dots {
    display: block;
    padding-top: 30px;
    text-align: right;
    position: absolute;
    top: 275px;
    width: 90%;

    .owl-dot {
      @include owl_dots_styles;
    }
  }

  .btn_personalized_2 {
    width: 90%;
    padding-top: 20px;
    text-align: right;
  }
}
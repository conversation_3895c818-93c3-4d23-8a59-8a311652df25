// colors definitions
$white: rgb(255, 255, 255);
$gray-1: rgb(90, 90, 90);
$gray-2: rgb(120, 120, 120);
$gray-3: rgb(190, 190, 190);
$gray-4: rgb(230, 230, 230);

// corporative colors definitions
$corporate_1: #FDBE87;
$corporate_2: #F3EFE8;
$corporate_3: #696158;

$black: #212322;
$lightgrey: #F4F4F4;
$grey: #CFD1D3;
$grey1: rgba($grey, .7);
$grey2: #707070;
$grey3: #707070;
$color_text: $black;


@import "defaults";

@import "styles_general";


#full_wrapper_booking #full-booking-engine-html-7 .booking_form .room_list_wrapper .room_list .room {
  .children_selector, .babies_selector {
    display: none;
  }
  .adults_selector {
    width: 100%;
    border-right: none;
  }

  .room_selector .selectric {
    .label {
      &:before{
        left: unset;
        right: 10px;
      }
    }

    .button {
        left: -5px;
    }
  }
}

.banner_club_wrapper.animate_reveal_on_scroll.inview_class {
  background: $corporate_2;
}

.cycle_banner_wrapper {
  .cycle_banner {
    .banner {
      background-color: $corporate_2;

      &:nth-of-type(odd) {
        background-color: white;
      }
    }
  }
}
.rooms_wrapper .rooms .room .content .label_extra_info {
    position: relative;
    display: inline-block;
    box-sizing: border-box;
    padding: 5px 10px 5px 10px;
    margin: 0 5px;
    background-color: $corporate_1;
    border-radius: 15px;
    font-size: 16px;
    color: white;
    top: -5px;
}

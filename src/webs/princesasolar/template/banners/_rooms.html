<div class="rooms_wrapper">
    {% for room in rooms %}
        <div class="room">
            <div class="room_gallery {% if room.gallery %}owl-carousel{% endif %}">
                {% if room.gallery %}
                    {% for image in room.gallery %}
                        <div class="room_img">
                            <a class="search_icon" href="{{ image.servingUrl|safe }}=s1800" rel="lightbox[{{ room.key|safe }}]">
                                <i class="fal fa-search-plus"></i>
                            </a>
                            <img src="{{ image.servingUrl|safe }}=s800">
                        </div>
                    {% endfor %}
                {% else %}
                    {% if room.servingUrl %}
                        <div class="room_image">
                            <a class="search_icon" href="{{ room.servingUrl|safe }}=s1800" rel="lightbox[{{ room.key|safe }}]">
                                <i class="fal fa-search-plus"></i>
                            </a>
                            <img src="{{ room.servingUrl }}=s800" {% if room.altText %}alt="{{ room.altText|safe }}" {% endif %}>
                        </div>
                    {% endif %}
                {% endif %}
            </div>
            {% if room.title or room.description %}
                <div class="room_content">
                    {% if room.title %}
                        <div class="content_title">
                            <h3 class="title">{{ room.title|safe }}</h3>
                        </div>
                    {% endif %}
                    {% if room.description %}
                        <div class="text">{{ room.description|safe }}</div>
                    {% endif %}
                    {% if room.since %}
                        <span class="room_price">{{ T_desde }} <strong>{{ room.since|safe }}/{{ T_noche }}</strong></span>
                    {% endif %}
                    {% if room.room_icons %}
                        <div class="room_icons">
                        {% for icon in room.room_icons %}
                            <span class="tooltip">
                                <i class="fa {{ icon.ico|safe }}"></i>
                                <span class="tooltiptext">{{ icon.description|safe }}</span>
                            </span>
                        {% endfor %}
                        </div>
                    {% endif %}
                    <div class="links_wrapper">
                        {% if room.linkUrl %}
                            <a href="{{ room.linkUrl|safe }}" {% if "http" in room.linkUrl %}target="_blank" {% endif %} class="icon_link {% if room.btn_booking %}with_btn_booking{% endif %}">
                                <i class="fal fa-plus"></i>
                            </a>
                        {% endif %}
                        {% if room.btn_booking %}
                            <a href="#data" class="button_promotion btn_primary">
                                {{ T_reservar }}
                            </a>
                        {% endif %}
                    </div>
                </div>
            {% endif %}
        </div>
    {% endfor %}
</div>

<script>
    $(window).on("load", function () {
        $(".room_gallery.owl-carousel").owlCarousel({
            loop: true,
            nav: true,
            dots: false,
            items: 1,
            navText: ['<i class="fal fa-arrow-left"></i>', '<i class="fal fa-arrow-right"></i>'],
            margin: 0,
            navSpeed: 500,
            autoHeight: false,
            autoplay: false
        });

    })
</script>
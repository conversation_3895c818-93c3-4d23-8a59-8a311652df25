
.normal_section_mobile{
    .section_title{
        padding: 20px;
    }

    .section_content{
        padding: 20px;

        table.table_white {
            //table-layout: fixed;
            width: 100%;
            border-spacing: 5px;
            border-collapse: separate;
            td, th {
              display: flex;
              flex-direction: column;
              width: 100% !important;
              background-color: white;
              vertical-align: middle;
              text-align: center;
              strong {
                font-weight: 700;
              }
              a {
                color: #2D2D2D;
                &:hover {
                  text-decoration: underline;
                }
              }
            }

            th {

              font-size: 120%;
              strong {
                color: $corporate_1;
              }
            }
        }
    }
}


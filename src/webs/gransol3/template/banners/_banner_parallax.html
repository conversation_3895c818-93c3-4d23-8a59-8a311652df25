<div class="banner_parallax_full_wrapper">
    {% for banner in banner_parallax_pics %}
    <div class="banner" {% if banner.servingUrl %} style="background-image: url('{{ banner.servingUrl }}')" {% endif %}>
        <div class="banner_content">
            {% if banner.title %}<h3 class="title">{{ banner.title|safe }}</h3>{% endif %}
            {% if banner.description %}
            <div class="text">{{ banner.description|safe }}</div>
            {% endif %}
            {% if banner.linkUrl %}
            <a href="{{ banner.linkUrl|safe }}" {% if "http" in banner.linkUrl %}target="_blank" {% endif %}
            class="link">
            {% if banner.link_text %}{{ banner.link_text|safe }}{% else %}{{ T_ver_mas }}{% endif %}
            </a>
            {% endif %}
        </div>
    </div>
    {% endfor %}
</div>
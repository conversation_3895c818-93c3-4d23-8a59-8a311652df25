<div class="offers_full_wrapper">
    <div class="offers">
        {% for banner in offers %}
        <div class="banner">
            {% if banner.servingUrl %}
            <div class="img_wrapper">
                <img src="{{ banner.servingUrl|safe }}=s800" {% if banner.altText %}alt="{{ banner.altText|safe }}" {%
                     endif %}>
            </div>
            {% endif %}
            <div class="banner_content">
                {% if banner.title %}<h4 class="title"><b>{{ banner.title|safe }}</b></h4>{% endif %}
                {% if banner.description %}
                <div class="text">{{ banner.description|safe }}</div>
                {% endif %}
                <div class="btn_wrapper">
                    <a href="#data" class="button-promotion btn_personalized_1">{{ T_reservar }}</a>
                    {% if banner.linkUrl %}
                    <a href="{{ banner.linkUrl|safe }}" {% if "http" in banner.linkUrl %}target="_blank" {% endif %}
                    class="btn_personalized_2">
                    {% if banner.link_text %}{{ banner.link_text|safe }}{% else %}{{ T_ver_mas }}{% endif %}
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>

{% if not is_mobile %}
<script>
    $(window).load(function () {
        $(".offers .banner").each(function () {
            var banner_height = $(this).outerHeight();
            $(this).find(".img_wrapper").outerHeight(banner_height);
        });
    })
</script>
{% endif %}
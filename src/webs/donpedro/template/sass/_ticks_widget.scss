.ticks_widget {
  @include display_flex;
  justify-content: center;
  align-items: center;
  padding-top: 20px;

  .tick {
    display: inline-block;
    width: 22%;
    padding: 0 10px;
    position: relative;

    .icon_wrapper {
      position: relative;
      display: inline-block;
      vertical-align: middle;
      z-index: 1;
      width: 38px;
      height: 38px;
      border-radius: 50%;
      background-color: $corporate_1;
      cursor: pointer;

      i {
        @include center_xy;
        color: white;
        font-size: 21px;
      }

      &:hover {
        &:after {
          max-width: 38px;
          max-height: 38px;
        }
      }

      &:after {
        content: '';
        @include center_xy;
        width: 48px;
        height: 48px;
        max-width: 48px;
        max-height: 48px;
        z-index: -1;
        background-color: rgba($corporate_1, .4);
        border-radius: 50%;
        @include transition(all, .6s);
      }
    }

    .icon_text {
      display: inline-block;
      vertical-align: middle;
      margin-left: 15px;
      color: $corporate_2;
      font-size: 12px;
      text-transform: uppercase;
      b {
        display: block;
        font-weight: 700;
      }
    }
    .icon_tooltip {
      opacity: 0;
      display: block;
      position: absolute;
      width: 200px;
      bottom: 116%;
      padding: 15px;
      background: rgba($corporate_1, .9);
      color: white;
      visibility: hidden;
      @include transition(all, .5s)
    }
    .icon_wrapper:hover ~ .icon_tooltip {
      opacity: 1;
      visibility: visible;
      @include transition(all, .5s)
    }
  }
}
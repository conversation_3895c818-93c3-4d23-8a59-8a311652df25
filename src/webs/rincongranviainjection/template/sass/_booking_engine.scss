#full_wrapper_booking {
  position: absolute;
  padding: 20px 0;
  width: 100%;
  min-width: 1140px;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1000;
  bottom: 80px;

  /*======== Booking Widget =======*/

  .selectricItems {
    overflow: auto !important;
  }

  #full-booking-engine-html-7 {
    width: auto;
    display: table;
    margin: auto !important;
    position: relative;

    .promocode_header {
      display: none;
    }
  }

  #full-booking-engine-html-7 form.booking_form {
    background: transparent;
    position: relative;
  }

  .booking_form_title .best_price {
    display: none;
    color: black;
    font-size: 16px;
    padding: 20px;
    font-weight: 600;
    text-align: center;
  }
  .promocode_header p.first_offer_name {
    color: black;
  }
  .booking_widget .date_box, .booking_widget .selectricWrapper, #booking_widget_popup .date_box, #booking_widget_popup .selectricWrapper {
    border: 0;
  }

  .booking_widget .date_box .date_day, #booking_widget_popup .date_box .date_day {
    border-bottom: 0 !important;
  }

  .selectric {
    height: 38px;
    background: transparent;
  }

  .room_list_wrapper .adults_selector, .room_list_wrapper .children_selector, .room_list_wrapper .babies_selector {
    width: 50% !important;
    height: auto;
    float: left;
    box-sizing: border-box;
  }

  .booking_widget .web_support_label_1, .booking_widget .web_support_label_1 span.web_support_number {
    font-size: 11px !important;
    padding: 0;
  }

  .wrapper-new-web-support .web_support_number, .web_support_label_1 {
    line-height: 15px !important;
    font-size: 14px !important;
  }

  .wrapper-new-web-support.booking_form_title {
    text-align: center;
    background: none;
    opacity: 1;
    margin-top: 7px;
    font-size: 13px !important;

    .web_support_label_2 {
      display: inline-block;
      margin: 0 10px;
    }

    .phone_support_image {
      display: none;
    }
  }

  .date_box.entry_date, .date_box.departure_date {
    margin-top: 6px;
    background: url(/img/#{$base_web}/entry_ico.png) no-repeat center;
    background-position-x: left;

    .date_year {
      display: none;
    }

    .date_day {
      border-bottom: 0 !important;
      font-weight: 300;
      font-size: 16px !important;
      color: black;
    }
  }

  .date_box.departure_date {
    background: url(/img/#{$base_web}/departure_ico.png) no-repeat center;
    background-position-x: left;
  }

  .selectricWrapper {
    width: 100% !important;

    .selectric {
      margin-top: 0;
    }
  }

  #slider_inner_container #full-booking-engine-html-7 {
    margin-top: -17px !important;
  }

  .promocode_text {
    display: none;
  }

  .stay_selection {
    display: inline-block;
    vertical-align: top;
    float: left;

    .entry_date_wrapper, .departure_date_wrapper {
      display: inline-block;
      vertical-align: top;
      float: left;
      margin-right: 5px;
      border: 0 !important;
      background: white;
      width: 212px;
      height: 47px;
    }

    .departure_date_wrapper {
      border-left: 0;
      border-right: 0;
    }

    .nights_number_wrapper {
      display: inline-block;
      width: 95px;
      float: left;
      vertical-align: top;
      border-top: 1px solid lightgrey;
    }
  }

  .rooms_number_wrapper {
    float: left;
    display: inline-block;
    vertical-align: top;
    width: 115px;
    height: 47px;
    margin-right: 5px;
    background: white;
    position: relative;

    .rooms_number {
      padding-left: 45px;
      box-sizing: border-box;
      background: url(/static_1/images/booking_5/rooms_number.png) no-repeat center left;
      background-position-y: 40%;
    }
  }

  .room_list_wrapper {
    display: none;
    vertical-align: top;
    float: left;
    background: white;
    width: 225px;
    position: absolute;
    left: 415px;
    top: 67px;
    padding: 25px 18px 15px;
    background: rgba($corporate_1, 0.8);

    .room {
      background: white;
      height: 45px;

      &.room1, &.room2, &.room3 {
        .children_selector, .babies_selector, .adults_selector {
          position: relative;
          height: 45px;
          border-left: 1px solid #d3d3d3;
        }

        .selectric {
          height: 20px;

          .label {
            line-height: 20px;
          }

          .button {
            margin-top: 0;
          }
        }
      }

      &.room3, &.room2 {
        border-bottom: 1px solid lightgray;
        height: 35px;

        .children_selector, .babies_selector, .adults_selector {
          position: relative;
          height: 35px;
        }
      }

      &.room3 {
        border-top: 0;
      }
    }
  }

  .wrapper_booking_button {
    display: inline-block;
    width: auto;
    float: left;
    height: 47px;

    label.promocode_label {
      display: none;
    }

    .promocode_wrapper {
      display: inline-block;
      vertical-align: top;
      float: left;
      width: 125px;
      margin-right: 5px;
      height: 47px;
      background: white;
      position: relative;
      padding-top: 5px;
    }

    .submit_button {
      width: 125px;
      height: 47px;
      display: inline-block;
      vertical-align: top;
      float: left;
      color: white;
      font-size: 15px;
      background: #F3D132;
      font-weight: 500;
      @include transition(border-radius, .6s);

      &:hover {
        border-radius: 10px;
      }
    }
  }
}

body.home_section #full_wrapper_booking.fixed_booking {
  width: 100%;
}

.babies_selector {
  width: 33.3%;
  display: inline-block;
  padding: 7px 10px 5px;
  box-sizing: border-box;

  label {
    text-transform: uppercase;
    font-size: 10px;
  }
}

/*=== Ocupancy selector ====*/
.guest_selector {
  float: left;
  display: inline-block;
  vertical-align: top;
  width: 210px;
  height: 47px;
  padding: 7px 10px 5px;
  box-sizing: border-box;
  cursor: pointer;
  margin-right: 5px;
  background: white;
  position: relative;

  span.placeholder_text {
    @include center_y;
    left: 10px;
    font-size: 14px;
    font-weight: 300;
    display: block;
    padding-left: 33px;
    box-sizing: border-box;
    background: url(/static_1/images/booking_5/ocupancy.png) no-repeat center left;
    background-position-y: 0;

    &.selected_value {
      color: #585d63;
      font-size: 21px;
      padding-top: 3px;
      background-position-y: 8px;
      font-weight: 600;
    }
  }

  & > label {
    text-transform: uppercase;
    font-size: 10px;
    cursor: pointer;
  }

  b.button {
    @include center_y;
    right: 0;
    background: none;
    line-height: 0 !important;
    height: 0;

    &:before {
      content: "\f0d7";
      font-family: "FontAwesome";
      color: black;
    }
  }
}

#booking label {
  cursor: pointer;
}

input.promocode_input {
  margin-top: 0;
  color: black;
  background: white;
  text-align: center;

  &::-webkit-input-placeholder {
    color: black;
    font-size: 11px;
    font-weight: 300;
    text-transform: capitalize;
  }
  &::-moz-placeholder {
    color: black;
    font-size: 11px;
    font-weight: 300;
    text-transform: capitalize;
  }
  &:-ms-input-placeholder {
    color: black;
    font-size: 11px;
    font-weight: 300;
    text-transform: capitalize;
  }
  &:-moz-placeholder {
    color: black;
    font-size: 11px;
    font-weight: 300;
    text-transform: capitalize;
  }
}

.selectricWrapper .selectric .label {
  font-weight: 300;
  font-size: 16px;
  line-height: 37px;
  color: black;
}

#booking .room_list label {
  display: block !important;
}

#booking .room_list .room2 label, #booking .room_list .room3 label {
  display: none !important;
}

#full_wrapper_booking {
  .rooms_number {
    .selectricItems {
      width: 113px !important;
      margin-left: -10px !important;
    }
  }
}

#booking label {
  display: none;
}

.hotel_selector {
  display: none;
}

.destination_wrapper {
  display: inline-block;
  float: left;
  margin-right: 5px;
  cursor: pointer;

  input {
    height: 46px;
    box-sizing: border-box;
    font-weight: 300;
    font-size: 13px;
    padding-left: 15px;
    cursor: pointer;
    color: black;
    width: 220px;
  }
  .destination_field {
    position: relative;
  }
  .destination_field:after {
    background: transparent url(/static_1/images/booking_5/arrow.png) no-repeat center center !important;
    color: #585d63;
    font-size: 23px;
    margin-left: 0;
    text-indent: 999px;
    font-weight: 600;
    float: right;
    width: 30px;
    height: 30px;
    position: absolute;
    top: 10px;
    right: 10px;
    content: '';
    display: block;
  }
}

div#full_wrapper_booking.floating_booking.showed {
  position: fixed;
  top: 0;
  bottom: auto;
  width: 100%;
}


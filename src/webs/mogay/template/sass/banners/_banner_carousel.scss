.banner_carousel_full_wrapper {
  position: relative;
  width: 100%;
  overflow: hidden;
  padding: 40px 0;
  @include display_flex;
  z-index: 1;
  flex-direction: row-reverse;
  .svg_logo {
    position: absolute;
    top: 20px;
    right: -250px;
    z-index: -1;
    height: 85%;
    * {
      fill: rgba(0, 0, 0, .05);
    }
  }
  .banner_carousel_wrapper {
    display: inline-block;
    width: 75%;
    .owl-stage-outer {
      padding: 0 20px 40px 20px;
      .owl-stage {
        @include display_flex;
        -webkit-flex-wrap: nowrap;
        -moz-flex-wrap: nowrap;
        -ms-flex-wrap: nowrap;
        -o-flex-wrap: nowrap;
        flex-wrap: nowrap;
        .owl-item.active + .owl-item.active + .owl-item.active {
          margin-right: 40px;
        }
      }
    }
    .banner {
      width: 100%;
      height: 100%;
      //box-shadow: 3px 15px 35px 0 rgba(0, 0, 0, 0.3);
      box-shadow: 6px 16px 25px 0 rgba(0, 0, 0, 0.1);
      overflow: hidden;
      .img_wrapper {
        position: relative;
        width: 100%;
        height: 310px;
        overflow: hidden;
        img {
          @include cover_image;
        }
      }
      .banner_content {
        padding: 50px 20px 100px;
        text-align: center;
        background-color: white;
        .banner_icon {
          @include center_x;
          top: 280px;
          background: $corporate_1;
          border-radius: 50%;
          width: 60px;
          height: 60px;
          i {
            color: white;
            @include center_xy;
            font-size: 30px;
          }
        }
        .title {
          @include banner_title_styles;
          color: $black;
          font-size: 25px;
          position: relative;
          font-family: $primary_font;
          &:after {
            @include center_x;
            content: "";
            width: 100px;
            height: 2px;
            background: $corporate_1;
            display: block;
            position: absolute;
            top: 100%;
          }
        }
        .text {
          @include text_styles;
          padding: 20px;
          font-size: 14px;
        }
        .btn_personalized_1 {
          @include center_x;
          bottom: 20px;
          min-width: 210px;
        }
        .icon_link {
          @include center_x;
          bottom: 30px;
        }
      }
    }

    .owl-item {
      margin-right: 20px;
      //opacity: 0;
      //visibility: hidden;
      //@include transition(all, .6s);
      //&.active {
      //  margin-right: 20px;
      //  opacity: 1;
      //  visibility: visible;
      //}
    }

    .owl-nav {
      text-align: right;
      margin-right: 8%;
      .owl-prev, .owl-next {
        display: inline-block;
        top: 100%;
        vertical-align: middle;
        box-sizing: border-box;
        i {
          font-size: 44px;
          color: $corporate_2;
          @include transition(color, .6s);
          box-sizing: border-box;
        }
        &.disabled {
          cursor: default;
          opacity: .4;
        }
        &:not(.disabled) {
          &:hover {
            i {
              color: $corporate_1;
            }
          }
        }
        margin: 0 35px 0 0;
        i {
          font-size: 48px;
        }
      }
      .owl-next {
        margin: 0 0 0 35px;
        right: 330px;
      }
      .owl-prev {
        left: 250px;
      }
    }
  }

  .banner_carousel_content {
    display: inline-block;
    margin-top: 50px;
    width: 25%;
    padding-left: 40px;
    .content_title {
      @include title_styles;
      font-family: $primary_font;
      font-weight: 300;
      text-align: left;
      padding-left: 0;
      margin-bottom: 20px;
      strong {
        font-weight: bold;
      }
    }
    .content_text {
      @include text_styles;
      padding-right: calc((100% - 280px) / 2);
    }
  }
}


<div class="rooms_blocks_wrapper">
    <div class="block_rooms">
        {% for block in rooms %}
            <div class="block">
                <div class="block_pick owl-carousel">
                    {% for img in block.gallery_pics %}
                        <div class="image">
                            {% if not is_mobile %}
                                <a href="{{ img.servingUrl }}=s1900" rel="lightbox[{{ block.key|safe }}]" class="gallery_icon">
                                    <i class="fal fa-search-plus"></i>
                                </a>
                            {% endif %}
                            <img src="{{ img.servingUrl }}=s800" {% if img.altText %}alt="{{ img.altText|safe }}"{% endif %}>
                        </div>
                    {% endfor %}
                </div>
                <div class="block_content">
                    {% if block.title %}<h3 class="title">{{ block.title|safe }}</h3>{% endif %}
                    {% if block.description %}<div class="text">{{ block.description|safe }}</div>{% endif %}
                    <div class="links">
                        <a href="#data" class="button-promotion">
                           {{ T_reservar }}
                        </a>
                    </div>
                    {% if block.services_icons|length > 1 %}
                        <div class="room_icons owl-carousel">
                            {% for service in block.services_icons %}
                                <span class="tooltip">
                                    <i class="fa {{ service.ico|safe }}"></i>
                                    <span class="tooltiptext">{{ service.description|safe }}</span>
                                </span>
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        {% endfor %}
    </div>
</div>

<script>
    $(window).load(function () {
   {% if not is_mobile %}
       $(".block_rooms .block").each(function () {
            var content_height = $(this).find(".block_content").outerHeight(),
                image_height = content_height + 140;
            $(this).find(".block_pick").outerHeight(image_height);
        });
    {% endif %}

   $(".block_pick.owl-carousel").owlCarousel({
        loop: true,
        nav: true,
        dots: false,
        items: 1,
        navText: ['<i class="fal fa-chevron-left" aria-hidden="true"></i>', '<i class="fal fa-chevron-right aria-hidden="true"></i>'],
        margin: 0,
        autoplay: false,
        mouseDrag: true
    });

   $(".room_icons.owl-carousel").owlCarousel({
        loop: false,
        nav: true,
        dots: false,
        items: 4,
        navText: ['<i class="fal fa-chevron-left" aria-hidden="true"></i>', '<i class="fal fa-chevron-right aria-hidden="true"></i>'],
        margin: 0,
        autoplay: false,
       mouseDrag: false
    });

    {% if not is_mobile and not user_isIpad %}
        function banner_room_fx() {
            $(".rooms_blocks_wrapper .owl-item").addnimation({parent:$(".rooms_blocks_wrapper"), class:"fadeInUpBig", reiteration: false});
        }
        banner_room_fx();
        $(window).scroll(banner_room_fx);
    {% endif %}
});
</script>
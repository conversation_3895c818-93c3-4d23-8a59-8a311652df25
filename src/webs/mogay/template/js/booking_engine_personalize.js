$(window).load(function () {
    fade_out_widget();
    prepare_guests_selector();
    set_occupancy_number();

    $(document).mouseup(function (e) {
      var container = $(".guest_selector, .room_list_wrapper");
      if(!container.is(e.target) &&
      container.has(e.target).length === 0) {
         $('.room_list_wrapper').slideUp().removeClass('active');
      }
  });
});

function add_room(){
   var number_rooms = parseInt($("select.rooms_number").val());

   if (number_rooms < 3) {
       $($(".rooms_number .selectricItems li").get(number_rooms)).trigger("click");
       set_occupancy_number();
       $(".add_remove_room_wrapper .remove_room").removeClass("disabled");
   }
   if (number_rooms >= 2) {
        $(this).addClass("disabled");
    }
}

function remove_room(){
   var number_rooms = parseInt($("select.rooms_number").val());

   if (number_rooms > 1) {
       $($(".rooms_number .selectricItems li").get(number_rooms - 2)).trigger("click");
       set_occupancy_number();
       $(".add_remove_room_wrapper .add_room").removeClass("disabled");
   }
   if (number_rooms <= 2) {
        $(this).addClass("disabled");
    }
}

function prepare_guests_selector() {
   $('body').on('click', '.guest_selector, .close_guesst_button, .save_guest_button, .close_room_selector', function() {
      toggle_guest_selector();
   });

   $("select.room_selector").unbind("change");
    $(".room_selector").selectric('destroy');
    $(".room_selector").selectric({disableOnMobile: false});
   $("select.room_selector, select.rooms_number").change(function(){
      set_occupancy_number();
   });

   var add_room_html = "<div class='add_remove_room_wrapper clearfix'><div class='add_room'>+</div><div class='remove_room disabled'>-</div></div>",
       close_room_selector = "<span class='close_room_selector'></span>";
   $(".room_list_wrapper").append(add_room_html).append(close_room_selector);
   $(".add_remove_room_wrapper .add_room").click(add_room);
   $(".add_remove_room_wrapper .remove_room").click(remove_room);

   $(".adults_selector .room_selector .label").click(function(){
      plus_room_selector($(this),0, 8);
   });
   $(".adults_selector .room_selector .button").click(function(){
      minus_room_selector($(this),1, 9);
   });
   $(".children_selector .room_selector .label").click(function(){
      plus_room_selector($(this),-1, 4);
   });
   $(".children_selector .room_selector .button").click(function(){
      minus_room_selector($(this),0, 5);
   });
   $(".babies_selector .room_selector .button").click(function(){
      minus_room_selector($(this),0, 3);
   });
   $(".babies_selector .room_selector .label").click(function(){
      plus_room_selector($(this),-1, 2);
   });

}
function plus_room_selector(element,min_val,max_val){
   var selectric_element = element.closest(".room_selector").find("select.room_selector");
   if(parseInt(selectric_element.val()) > min_val &&
      parseInt(selectric_element.val()) < max_val){
     var new_select_val = parseInt(selectric_element.val()) + 1;
     selectric_element.val(new_select_val);
     selectric_element.selectric('refresh');
     set_occupancy_number();
   }
}
function minus_room_selector(element,min_val,max_val){
   var selectric_element = element.closest(".room_selector").find("select.room_selector");
   if(parseInt(selectric_element.val()) > min_val &&
      parseInt(selectric_element.val()) < max_val){
     var new_select_val = parseInt(selectric_element.val()) - 1;
     selectric_element.val(new_select_val);
     selectric_element.selectric('refresh');
     set_occupancy_number();
   }
}
function toggle_guest_selector(){
   var target_room_wrapper = $(".room_list_wrapper");
   if(!target_room_wrapper.hasClass('active')) {
      target_room_wrapper.addClass('active');
      target_room_wrapper.show();
      console.log("showing");
   } else {
      target_room_wrapper.removeClass('active');
      target_room_wrapper.hide();
   }
   set_occupancy_number();
}

function set_occupancy_number(){
   var number_of_rooms = parseInt($("select[name='numRooms']").val()),
       adults_number = 0,
       kids_number = 0,
       babies_number = 0;

   if (number_of_rooms){
      for (var room_loop = 1;room_loop <= number_of_rooms;room_loop++){
         var actual_select_adults = $("select[name='adultsRoom" + room_loop + "']").val(),
             actual_select_kids = $("select[name='childrenRoom" + room_loop + "']").val(),
             actual_select_baby = $("select[name='babiesRoom" + room_loop + "']").val();

         if(actual_select_adults || actual_select_kids){
            adults_number += parseInt(actual_select_adults);
            kids_number += parseInt(actual_select_kids);
            babies_number += parseInt(actual_select_baby);
         }
      }
   }

   var target_placeholder = $(".guest_selector .placeholder_text"),
       room_label = (number_of_rooms > 1)?$.i18n._("habitaciones"):$.i18n._("habitacion"),
       placeholder_string = "<span class='rooms_number'>" + number_of_rooms + " " + room_label + "</span>";

   adults_number = parseInt(adults_number);
   kids_number = parseInt(kids_number);
   babies_number = parseInt(babies_number);

   placeholder_string += "<span class='guest_adults'> " + adults_number + "</span>";

   if(!$(".adults_only_selector").length){
      placeholder_string += "-" + kids_number;
   }

   if($(".babies_selector").length) {
      placeholder_string += "-" + babies_number;
   }

   target_placeholder.html(placeholder_string);
}
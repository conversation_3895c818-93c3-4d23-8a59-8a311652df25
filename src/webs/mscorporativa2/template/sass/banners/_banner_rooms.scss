.banner_rooms_wrapper {
    padding: 50px 0;
    
    .banner_top {
        width: 1140px;
        margin: 0 auto;
        text-align: center;
        position: relative;
        
        > div {
            display: inline-block;
            vertical-align: middle;
            width: calc(50% - 20px);
            margin: 0;
            
            &.content_title {
                text-align: left;
                margin-left: 15px;
            }
            
            &.desc {
                text-align: left;
                margin-right: 15px;
                line-height: 28px;
            }
        }
        
        &::after {
            position: absolute;
            content: '';
            top: -30px;
            left: 50%;
            transform: translateX(-50%);
            width: 70px;
            height: 15px;
            background-repeat: no-repeat;
            background-size: 100% auto;
            opacity: .2;
            background-image: url("/img/#{$base_web}/bg_title_top.png");
        }
    }
    
    .banner_rooms {
        padding: 75px 0;
        
        &.owl-carousel > .owl-nav {
            position: absolute;
            bottom: 50px;
            right: calc((100% - 1140px) / 2);
            left: auto;
            width: auto;
            display: table;
            text-align: left;
            
            .owl-prev, .owl-next {
                display: inline-block;
                vertical-align: middle;
                position: unset;
                font-size: 12px;
                background: $lightgrey;
                
                i {
                    display: inline-block;
                    vertical-align: middle;
                    width: 40px;
                    height: 50px;
                    color: $corporate_1;
                    text-align: center;
                    
                    &:before {
                        @include center_xy;
                        font-size: 40px;
                    }
                }
            }
            
            div.owl-prev {
                padding-right: 20px;
                
                &:after {
                    content: '';
                    @include center_y;
                    right: -2px;
                    height: 40px;
                    width: 4px;
                    background: $corporate_1;
                }
                
                i:before {
                    content: '\f104';
                }
            }
            
            div.owl-next {
                padding-left: 20px;
                
                i:before {
                    content: '\f105';
                }
            }
        }
        
        .room {
            width: 1140px;
            margin: auto;
            //
            //&:before {
            //    content: '';
            //    position: absolute;
            //    top: 20px;
            //    height: 400px;
            //    left: 0;
            //    right: 0;
            //    background: $lightgrey;
            //}
            
            .room_gallery, .room_content {
                display: inline-block;
                vertical-align: top;
                position: relative;
            }
            .room_gallery {
                z-index: 2;
            }
            .room_content {
                z-index: 1;
            }
            
            .room_gallery {
                width: 740px;
                
                a {
                    display: block;
                    width: 740px;
                    height: 530px;
                    overflow: hidden;
                    position: relative;
                    
                    img {
                        @include center_image;
                        max-height: 100%;
                    }
                    
                    i {
                        position: absolute;
                        top: 10px;
                        right: 10px;
                        color: white;
                        font-size: 30px;
                    }
                }
                
                &.owl-carousel div.owl-nav {
                    position: relative;
                    bottom: 20px;
                    left: 0;
                    right: 0;
                    z-index: 30;
                    
                    .owl-prev, .owl-next {
                        i {
                            width: 50px;
                            height: 50px;
                            color: white;
                            text-align: center;
                            
                            &:before {
                                @include center_xy;
                                font-size: 35px;
                            }
                        }
                    }
                    
                    div.owl-prev {
                        left: 0;
                        
                        i:before {
                            content: '\f104';
                        }
                    }
                    
                    div.owl-next {
                        right: 0;
                        
                        i:before {
                            content: '\f105';
                        }
                    }
                }
            }
            
            .room_content {
                margin-top: 20px;
                width: calc(100% - 740px);
                padding: 30px;
                
                .title {
                    @include title_styles(35px);
                }
                
                .desc {
                    max-height: 150px;
                    @include ellipsis(6);

                }
                
                a.button-promotion {
                    margin-top: 20px;
                    padding: 15px 50px 15px 30px;
                }

                &:before {
                    content: '';
                    position: absolute;
                    top: 0;
                    bottom: 0;
                    left: calc(((-100vw + 1140px) / 2) - 740px);
                    right: calc((-100vw + 1140px) / 2);
                    background: #F6F6F6;
                    z-index: -1;
                }
            }
        }
    }
}


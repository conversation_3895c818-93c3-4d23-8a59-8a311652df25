.contact_form_wrapper {
    background-color: darken($lightgrey, 2px);
    overflow: hidden;
    @include sec_pad;
    
    &.events {
        margin: 50px 0 0;
        border-bottom: 1px solid white;
    }
    
    h3 {
        padding: 0 0 25px;
        font-size: 33px;
    }
    #contact {
        .info {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            grid-template-rows: auto;
            grid-gap: 20px;
            position: relative;
        }

        .contInput {
            position: relative;
            
            &.area {
                grid-column: 1 / span 3;
            }

            input:not([type="checkbox"]) {
                -webkit-appearance: none;
                -moz-appearance: none;
                appearance: none;
                display: block;
                height: 40px;
                border-radius: 0;
                font-size: 14px;
                width: 100%;
                border: solid 2px darken($lightgrey, 2%);
                box-shadow: none;
            }

            textarea {
                border-radius: 0;
                font-size: 14px;
                width: 100%;
                border: solid 2px darken($lightgrey, 2%);
                box-shadow: none;
                min-height: 150px;
            }

            input,
            textarea {
                padding: 15px;
                font-family: "Raleway", sans-serif !important;
                &.error {
                    outline: 1px solid red;
                }
            }

            #accept-term,
            &#privacity {
                width: auto;
                height: auto;
                display: inline-block;
                vertical-align: middle;
            }

            &.policy-terms {
                margin: 0;
            }

            input[type= "checkbox" ] {
                -webkit-appearance: none;
                border: solid 2px $corporate_2;
                border-radius: 0;
                height: 15px;
                width: 15px;
                vertical-align: middle;
                padding: 5px;

                &:checked {
                    background-color: $corporate_1;
                }
            }
        }



        .policy-terms {
            display: inline-block;
            width: auto;
            float: left;
            color: $black;
            font-size: 12px;
            margin: 0;
        }

        a.myFancyPopup {
            display: inline-block;
            vertical-align: middle;
            color: $black;
        }

        #contact-button {
            @extend .btn;
            @extend .btn_secondary;
            position: absolute;
            right: 0;
            bottom: -20px;
            padding: 22px 54px;
        }
    }

    label {
        display: block;
        text-align: center;
        text-transform: uppercase;
        color: #4B4B4B;
        font-weight: 100;
    }
}
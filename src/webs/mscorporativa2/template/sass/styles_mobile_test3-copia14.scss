@import "styles_mobile";

@mixin base_mobile_styles() {
  position: relative;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
  width: 100%;
  * {
    box-sizing: border-box;
  }
}

.wrapper_content.banner_carousel_wrapper .container_fluid.sticky_top .carousel_wrapper .owl-carousel .owl-item img{
    height: 240px !important;
}

.gallery_divided_title span{
    color: white;
}

.hidden_text{
    display:none;
}
.banner_rooms_wrapper .room_content .desc .text_overflow {
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    display: block;
}


.cycle_banner_wrapper {
    .links_wrapper {
        left: 0!important;
        right: 0!important;
    }
}

.services_icon_block {
  @include base_mobile_styles;
  @include display_flex;
  align-items: center;
  width: 100%;
  .title {
    @include banner_title_styles_2($corporate_3);
    padding-bottom: 30px;
    width: 100%;
  }
  .icon {
    display: inline-block;
    width: 50%;
    padding: 0 10px;
    margin-bottom: 25px;
    text-align: center;
    i {
      display: block;
      font-size: 30px;
      margin-bottom: 5px;
      color: $corporate_1;
    }
    span {
      display: block;
      font-size: 17px;
      letter-spacing: 1px;
    }
  }
}


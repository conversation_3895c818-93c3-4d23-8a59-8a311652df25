<div class="wrapper_content blog_entry_wrapper has_transition">
    <div class="container12">
        <div class="content_title">
            <h1 class="title">{{ individual_news_entry.name|safe }}</h1>
            <span class="autor">{{ individual_news_entry.author|safe }}</span> <span class="date">{{ individual_news_entry.creationDate|safe }}</span><span class="category">{{ individual_news_entry.tags|safe }}</span>
        </div>
        <div class="entry_body">
            <div class="content_wrapper">
                <div class="picture_wrapper">
                     <img src="{{ individual_news_entry.picture }}" alt="">
                </div>
                {% if individual_news_entry.description %}
                <div class="desc">
                    {{ individual_news_entry.description|safe }}
                </div>
                {% endif %}
            </div>
            <div class="entry_sidebar">

                <!–– block ––>
                <div class="block_news related_news">
                    <div class="content_title sub_border">
                        <h4 class="title">
                            {{ T_noticias_relacionadas }}
                        </h4>
                    </div>
                    {% for related_element in related_news %}
                        <div class="new_item">
                            <div class="content_title">
                                <h2 class="title"><a href="{% if not language_code == "es" %}/{{ language_code }}/{{ T_noticias }}/{{ related_element.friendlyUrl }}{% else %}/{{ T_noticias }}/{{ related_element.friendlyUrl }}{% endif %}">{{ related_element.name|safe }}</a></h2>
                                <span class="autor">{{ related_element.author|safe }}</span> <span class="date">{{ related_element.creationDate|safe }}</span> <span class="category">{{ related_element.tags|safe }}</span>
                            </div>
                            <div class="picture_wrapper">
                                <img src="{{related_element.picture}}=s1900">
                            </div>
                            <a href="{% if not language_code == "es" %}/{{ language_code }}/{{ T_noticias }}/{{ related_element.friendlyUrl }}{% else %}/{{ T_noticias }}/{{ related_element.friendlyUrl }}{% endif %}" class="vermas">{{ T_leer_mas }}</a>
                        </div>
                    {% endfor %}
                    <a href="{% if not language_code == "es" %}/{{ language_code }}/{{ T_noticias }}/{{ news_section.0.friendlyUrlInternational }}{% else %}/{{ T_noticias }}/{{ news_section.0.friendlyUrlInternational }}{% endif %}" class="btn btn_primary">{{T_descubre_mas}}</a>
                </div>
                <!–– end block ––>
                <!–– block ––>
                <div class="block_news">
                    <div class="content_title sub_border">
                        <h4 class="title">
                            {{ T_ultimas_noticias }}
                        </h4>
                    </div>
                    {% for related_element in sorted_news[:3] %}
                        <div class="new_item">
                            <div class="content_title">
                                <h2 class="title"><a href="{% if not language_code == "es" %}/{{ language_code }}/{{ T_noticias }}/{{ related_element.friendlyUrl }}{% else %}/{{ T_noticias }}/{{ related_element.friendlyUrl }}{% endif %}">{{ related_element.name|safe }}</a></h2>
                                <span class="autor">{{ related_element.author|safe }}</span><span class="date">{{ related_element.creationDate|safe }}</span><span class="category">{{ related_element.tags|safe }}</span>
                            </div>
                            <div class="picture_wrapper">
                                <img src="{{related_element.picture}}=s1900">
                            </div>
                            <a href="{% if not language_code == "es" %}/{{ language_code }}/{{ T_noticias }}/{{ related_element.friendlyUrl }}{% else %}/{{ T_noticias }}/{{ related_element.friendlyUrl }}{% endif %}" class="vermas">{{ T_leer_mas }}</a>
                        </div>
                    {% endfor %}
                </div>
                <!–– end block ––>
            </div>
        </div>
    </div>
</div>
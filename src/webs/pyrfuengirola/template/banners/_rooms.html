<div class="rooms_wrapper">
    <div class="rooms_filter {% if user_isIpad %}ipad{% endif %}">
        <a href="#all" class="active">{{ T_ver_todas }}</a>
        {% for room in rooms %}
            <a href="#{{ room.class }}">{{ room.title|safe }}</a>
        {% endfor %}
    </div>
    <div class="rooms" id="all">
        {% for room in rooms %}
            <div class="room" id="{{ room.class }}">
                <div class="room_gallery owl-carousel">
                    {% for pic in room.pics %}
                        <a href="{{ pic.servingUrl }}=s1900" rel="lightbox[room_{{ loop.index }}]">
                            <img src="{{ pic.servingUrl }}=s800" alt="{{ pic.altText|safe }}">
                        </a>
                    {% endfor %}
                </div>
                <div class="room_info">
                    <div class="icons">
                        {% for icon in room.icon_list %}
                            <div class="icon">
                                <i class="{{ icon.ico }}"></i>
                                <span>{{ icon.description|safe }}</span>
                            </div>
                        {% endfor %}
                    </div>
                    <div class="title">{{ room.title|safe }}</div>
                    <div class="desc">{{ room.description|safe }}</div>
                    {% if 'hide' in room.description %}
                        <a id="inline" class="button-promotion myFancyPopupAuto" href="#dataDesc">{{ T_ver_mas }}</a>
                        <div style="display:none">
                            <div id="dataDesc">{{ room.description|safe }}</div>
                        </div>
                    {% endif %}
                    <a href="#data" class="button-promotion">{{ T_reservar }}</a>
                </div>
            </div>
        {% endfor %}
    </div>
</div>
<script>
$(window).load(function () {
    $(".room_gallery").owlCarousel({
        loop: true,
        nav: true,
        dots: false,
        items: 1,
        navText: ['<i class="fal fa-long-arrow-left"></i>', '<i class="fal fa-long-arrow-right"></i>'],
        autoplayTimeout: 10000,
        autoplaySpeed: 1000,
        navSpeed: 1000,
        autoplay: true
    });
    $(".rooms_filter a").click(function (e) {
        e.preventDefault();
        let room_class = $(this).attr("href");
        $(this).addClass("active").siblings().removeClass("active");
        if(room_class == "#all") {
            $(".room").slideDown();
        } else {
            $(room_class).slideDown().siblings().slideUp();
        }
    })
});
</script>
header {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: 900;
  padding: 15px calc((100% - 1140px) / 2);
  min-width: 1140px;
  @include display_flex;
  align-items: center;

  &:before {
    content: '';
    @include full_size;
    z-index: -1;
    background: linear-gradient(to bottom, rgba(0, 0, 0, .4), rgba(0, 0, 0, 0));
  }

  .left_header {
    display: inline-block;
    width: calc(50% - 95px);
    text-align: left;

    .phone {
      color: white;
      @include transition(opacity, .6s);

      &:hover {
        opacity: .8;
      }

      i {
        display: inline-block;
        vertical-align: middle;
        font-size: 20px;
      }

      span {
        display: inline-block;
        vertical-align: middle;
        font-family: $text_family;
        font-weight: 600;
        font-size: 18px;
        letter-spacing: 2px;
        line-height: 22px;
        padding: 0 5px;
      }
    }
  }

  .right_header {
    display: inline-block;
    width: calc(50% - 95px);
    text-align: right;

    .social_header {
      display: inline-block;
      vertical-align: middle;

      a {
        font-size: 22px;
        line-height: 26px;
        color: white;
        display: inline-block;
        vertical-align: middle;
        margin-right: 15px;
        @include transition(all, .6s);

        &:hover {
          color: $corporate_1;
        }
      }
    }
  }


  .language_selector {
    position: relative;
    display: inline-block;
    vertical-align: middle;
    padding: 0 15px;
    border-left: 1px solid white;

    #header_language_selector {
      display: inline-block;
      vertical-align: middle;
      color: white;
      cursor: pointer;

      i {
        display: inline-block;
        vertical-align: middle;
        font-size: 16px;
        @include transition(all, .6s);
      }

      span {
        display: inline-block;
        vertical-align: middle;
        font-family: $text_family;
        font-weight: 600;
        font-size: 18px;
        line-height: 22px;
        letter-spacing: 2px;
        padding: 0 5px;
      }
    }

    .header_lang {
      @include center_x;
      top: calc(100% + 10px);
      background-color: white;
      max-height: 0;
      overflow: hidden;
      z-index: 5;
      @include transition(max-height, .6s);

      a {
        padding: 10px 15px;
        display: block;
        text-align: center;
        color: $color_text;
        font-size: 16px;
      }
    }

    &.active {
      #header_language_selector {
        i.fa-chevron-down {
          @include transform(rotate(-180deg));
        }
      }

      .header_lang {
        max-height: 600px;
      }
    }
  }

  #logoDiv {
    width: 190px;
    text-align: center;
    padding: 0 20px;
    margin: 0;

    img {
      max-width: 100%;
    }
  }

  .main-sections-inner {
    @include display_flex;
    width: 100%;
    justify-content: space-between;
    margin-top: 20px;

    .main-section-div-wrapper {
      position: relative;
      margin-right: 15px;

      &:last-child {
        margin-right: 0;
      }

      a {
        text-transform: uppercase;
        color: $white;
        font-size: 16px;
        line-height: 22px;
        letter-spacing: 2px;
        font-family: $text_family;
        font-weight: 400;
        @include transition(all, .6s);

        &:hover {
          color: $white;
        }
      }

      &#section-active a {
        color: $white;
      }

      ul {
        opacity: 0;
        position: absolute;
        padding: 10px;
        top: 35px;
        border-left: 1px solid $corporate_1;
        @include transition(all, .6s);

        .main-section-subsection {
          position: relative;
          padding-bottom: 3px;
          margin-bottom: 3px;

          a {
            position: relative;
            color: white;
            font-size: 16px;
            line-height: 22px;
            letter-spacing: 2px;
            font-family: $text_family;
            font-weight: 400;
            white-space: nowrap;
            @include transition(all, .6s);

            &:hover {
              color: $corporate_1;
            }
          }

          &:not(:last-child) {

            a {

              &::before {
                position: absolute;
                content: '';
                height: 5px;
                width: 5px;
                border-radius: 50%;
                background-color: $corporate_1;
                bottom: -10px;
                left: -13px;
              }
            }
          }
        }

        &::before {
          position: absolute;
          content: '';
          top: 0;
          bottom: 0;
          left: 0;
          right: 0;
          background-color: #00000080;
        }
      }

      &:hover {

        ul {
          opacity: 1;
        }
      }
    }
  }
}
/*FONFS*/
@font-face {
    font-family: '<PERSON>';
    src: url('/static_1/fonts/oswald/<PERSON>-<PERSON>.eot');
    src: url('/static_1/fonts/oswald/<PERSON>-Light.eot?#iefix') format('embedded-opentype'),
         url('/static_1/fonts/oswald/<PERSON>-Light.woff2') format('woff2'),
         url('/static_1/fonts/oswald/Oswald-Light.woff') format('woff'),
         url('/static_1/fonts/oswald/oswald-lighter2.ttf') format('truetype'),
         url('/static_1/fonts/oswald/<PERSON>-Light.svg#oswaldlight') format('svg');
    font-weight: 300;
    font-style: normal;

}

@font-face {
    font-family: 'Oswald';
    src: url('/static_1/fonts/oswald/Oswald-Regular.eot');
    src: url('/static_1/fonts/oswald/<PERSON>-Regular.eot?#iefix') format('embedded-opentype'),
         url('/static_1/fonts/oswald/<PERSON>-Regular.woff2') format('woff2'),
         url('/static_1/fonts/os<PERSON>/<PERSON>-Regular.woff') format('woff'),
         url('/static_1/fonts/oswald/oswald-regular2.ttf') format('truetype'),
         url('/static_1/fonts/oswald/Oswald-Regular.svg#oswaldregular') format('svg');
    font-weight: 700;
    font-style: bold;
}

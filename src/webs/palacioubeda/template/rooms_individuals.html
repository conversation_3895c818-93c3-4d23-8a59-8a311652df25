<div class="detailed_room_wrapper">
    {% if not offer_individual %}
    <div class="rooms_controls_wrapper">
        <a href="/{{ language }}/{{ previous_room }}">
            <div class="previous_room_button">
                <img class="previous_room_image" src="/img/{{ base_web }}/prev_arrow_room.png">
                <span>{{ T_previous_room|upper }}</span>
            </div>
        </a>

        <a href="/{{ language }}/{{ next_room }}">
            <div class="next_room_button">
                <span>{{ T_next_room|upper }}</span>
                <img class="next_room_image" src="/img/{{ base_web }}/prev_arrow_room.png">
            </div>
        </a>
    </div>
    {% endif %}


    <div class="room_detail_image_wrapper">
        <img class="room_detail_image" src="{{ room_details.pictures.0.servingUrl|safe }}=s1900">
        {% if not offer_individual %}
            <a href="javascript:showGallery2([ {% for picture in room_details.pictures %} {href : '{{ picture.servingUrl }}=s1900'}, {% endfor %} ]);" class="see_more_pictures_detailed">
                <span>{{ T_ver_fotos }}</span><img class="see_more_img" src="/img/{{ base_web }}/more_room.png">
            </a>
        {% endif %}
    </div>

    <div class="room_details_text">
        <h1 class="room_title">
            {{ room_details.subtitle|safe }}
        </h1>
        <a href="#data" class="button-promotion" {% if promocode_offer %}data-promocode="{{ promocode_offer }}" {% endif %}>{{ T_reserva_ahora }}</a>
        <div class="room_description">{{ room_details.content|safe }}</div>
        {% if offer_individual %}
            <div id="shareSocialArea">
                <script type="text/javascript"> var addthis_config = {ui_language: "es"} </script>
                <div class="addthis_toolbox addthis_default_style" addthis:title="{{ room_details.subtitle|safe }}" addthis:description='{{ room_details.description_share|safe }}'>
                    <a href="//www.addthis.com/bookmark.php?v=250&amp;pub=ra-4f0324800a8b2d9d"
                       class="addthis_button_compact" style="color:#5A5655"><span class="share_text">{{ T_compartir }}</span></a>
                    <span class="addthis_separator">|</span>
                    <a class="addthis_button_facebook"></a>
                    <a class="addthis_button_google"></a>
                    <a class="addthis_button_twitter"></a>
                    <a class="addthis_button_favorites"></a>
                </div>
                <script type="text/javascript" src="//s7.addthis.com/js/250/addthis_widget.js?pub=ra-4f0324800a8b2d9d"></script>
            </div>
        {% endif %}
    </div>

</div>

</div>
<div class="booking_detailed_room">
    <div id="horizontal_booking_selector" class="detailed_rooms_booking_engine">
        <div class="detailed_booking_titles">
            <h3 class="detailed_title_big">{{ T_reserva_ahora }}</h3>
            <div class="detailed_title_small">{{ T_mejor_precio_garantizado }}</div>
        </div>
        <div id="full_wrapper_booking" class="booking_horizontal">
            <div class="boking_widget_inline">
                {{ booking_engine }}
            </div>
        </div>
    </div>
</div>
<div id="wrapper_content" class="container12">
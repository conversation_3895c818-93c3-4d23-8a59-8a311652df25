@import "gallerys/gallery_full_width";

.gallery_filters {
  text-align: center;
  padding:0;
  border-bottom:1px solid lightgrey;
  margin: 0 auto 30px;
  a {
    display: inline-block;
    padding: 15px 20px;
    text-transform: uppercase;
    color: $corporate_1;
    &:hover {
      background-color: #EFEFEF;
    }
    &.active {
      background-color: #FAFAFA;
      color: $corporate_2;
    }
    &:first-of-type {
      border-right: 1px solid lightgrey;
      font-weight: bold;
    }
  }
}
.gallery_filter_wrapper {
  .gallery_filter {
    position: relative;

    .gallery_title {
      display: none;
    }
    .gallery_photos {
      width: 100%;
      text-align: center;
      padding-top: 5px;
      a {
        display: inline-block;
        float: none;
        margin-top: -5px;
        img {
          float: left;
        }
      }
    }
  }
}
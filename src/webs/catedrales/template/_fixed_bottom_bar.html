{% if bottom_popup %}
    <div class="bottom_popup" style="display: none">
        <div class="close_button">
            <img src="/static_1/images/close.png" alt="close" title="close"/>
        </div>
        <a class="myFancyPopup" href=".popup_inicial">
            <div id="wrapper2">
                <div class="bottom_popup_text"><img class="image_before_text" src="{{ bottom_popup.pictures.0 }}">

                    <div class="bottom_popup_text_inside">{{ bottom_popup.content|safe }}</div>
                </div>
                <a href="#data" class="button-promotion">
                    <button class="bottom_popup_button_book">{{ T_reservar|safe }}</button>
                </a>
            </div>
        </a>
    </div>
    <div style="display:none;">
        <div class="popup_inicial" style="background:url({{ bottom_popup_text.pictures.0 }});">

            {{ bottom_popup_text.content|safe }}

            <form action="" method="post" class="form_popup">
                <ul>
                    <li>
                        <input id="id_email" type="text" name="email" maxlength="150"
                               placeholder="{{ T_introduce_email_placeholder }}">
                    </li>
                </ul>
                <button class="popup_button">{{ bottom_popup_text.subtitle|safe }}</button>
                <div class="spinner_wrapper_faldon" style="display:none;"><img src='/static_1/images/spinner.gif'
                                                                               width="30" height="30" alt="spinner"
                                                                               title="spinner"></div>
            </form>

            {% if bottom_popup_background.0.description %}
                <div id="new_gracias_newsletter"
                     style="display: none">{{ bottom_popup_background.0.description|safe }}</div>
            {% endif %}
        </div>
    </div>

{% endif %}
@import "compass";

@import "defaults";


$corporate_1: #675886;
$corporate_2: #333333;
$corporate_3: #666;
$corporate_4: #828080;

@import "plugins/lightbox";
@import "plugins/fancybox_2_1_5";
@import "plugins/_jquery_ui_1_8_16_custom";
@import "plugins/owlcarousel";
@import "plugins/mixins";
@import "plugins/fontawesomemin";
@import "plugins/iconmoon";
@import "plugins/effects";
@import "gallerys/gallery_full_width";

@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,400;0,700;1,400&display=swap');

@import "booking/booking_engine_7";
@import "booking/selectric";
@import "booking/my_bookings";
@import "widget/booking_engine";
@import "widget/booking_popup";

@import "entry";
@import "news_widget";
@import "banners/fixed_banners";
@import "banners/banner_x3";

@import "template_specific";

#adapting-table {
    .table_content {
        background: $corporate_1;
        padding: 2em 1em;
        min-height: 450px;
        height: auto;
        &:nth-child(even) {
            background-color: $corporate_2;
        }
    }
}

.offer_element {
    .offer_content {
        .offer_link {
            background: $corporate_2;
        }
        .offer_booking {
            &:before {
                background: $corporate_4;
            }
        }
    }
}

.newsletter_and_icons_footer_wrapper {
    .newsletter_wrapper {
        .newsletter_container {
            .newsletter_form {
                .button_newsletter {
                    &:before {
                        background: $corporate_4;
                    }
                }
            }
        }
    }
}

#full_wrapper_booking {
    .wrapper_booking_button {
        .submit_button {
            &:before {
                background: $corporate_4;
            }
        }
    }
}

.datepicker_wrapper_element, .datepicker_wrapper_element_2, .datepicker_wrapper_element_3 {
    .specific_month_selector, .go_back_button {
        strong {
            color: $corporate_1;
        }
    }
}

.minigallery_content_wrapper .minigallery_wrapper .owl-item img {
    max-height: 100%;
}
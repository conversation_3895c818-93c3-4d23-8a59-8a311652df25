body {
  font: {
    family: "Roboto";
    weight: 300;
  }

  strong {
    font-weight: bold;
  }

  a {
    text-decoration: none;
  }

  * {
    box-sizing: border-box;
  }
}

.datepicker_wrapper_element {
  .header_datepicker {
    background: $corporate_1 !important;
  }
  .specific_month_selector {
    strong {
      color: $corporate_2;
    }
  }
}

.fancybox-skin {
  background: none !important;
}

body:not(.es) {
  .rooms_wrapper .room_element .room_buttons_wrapper .room_book {
    width: auto !important;
  }
}

/*=== Header ===*/
header {
  position: relative;
  z-index: 1001;
  width: 100%;
  background-color: $corporate_2;
  padding: 20px 0;
  box-shadow: 1px 1px 1px grey;

  #wrapper-header {
    .header_red {
      .red_top {
        position: relative;

        #logoDiv {
          position: absolute;
          top: 0;
          left: 0;
          margin: 0;
          width: 300px;
          z-index: 24;

          img {
            vertical-align: bottom;
            @include center_xy;
          }
        }

        .content_red_top {
          width: 670px;
          float: right;

          .left_top {
            font-size: 13px;
            color: white;
            display: inline-block;
            vertical-align: middle;
            margin-top: 6px;

            .web_assistant {
              display: inline-block;
              vertical-align: middle;
            }

            .fa {
              color: white;
              font-size: 15px;
              vertical-align: middle;
              margin-right: 10px;
            }

            .ticks_header_wrapper {
              display: inline-block;
              vertical-align: middle;
              margin-left: 20px;

              & * {
                display: inline-block;
                vertical-align: middle;
              }
            }
          }

          .right_top {
            float: right;

            .list_sections {
              display: inline-block;
              vertical-align: middle;
              margin-right: 20px;

              a {
                color: white;
                margin-left: 15px;
                font-size: 13px;
                display: inline-block;

                &:hover span {
                  opacity: .8;;
                }

                span {
                  @include transition(opacity, .4s);
                  vertical-align: middle;
                  display: inline-block;
                }

                .fa {
                  margin-right: 10px;
                  font-size: 15px;
                  color: white;
                }
              }
            }

            .phone_wrapper {
              display: inline-block;
              vertical-align: middle;
              color: white;

              .fa {
                vertical-align: middle;
                font-size: 15px;
              }

              span {
                display: inline-block;
                vertical-align: middle;
                font-size: 13px;
              }
            }

            #social {
              display: inline-block;
              vertical-align: middle;
              margin-left: 20px;

              & * {
                display: inline-block;
                vertical-align: middle;
              }

              a {
                position: relative;
                margin-right: 5px;

                &:last-child {
                  margin-right: 0;
                }

                &:hover {
                  opacity: .8;
                }

                .fa {
                  color: white;
                  font-size: 16px;
                }
              }
            }

            #lang {
              display: inline-block;
              vertical-align: middle;
              margin-left: 20px;

              .lang_wrapper {
                position: relative;

                .lang_selected_image {
                  position: relative;
                  padding: 10px 100px 10px 10px;
                  cursor: pointer;
                  box-sizing: border-box;
                  border: 1px solid white;
                }

                span {
                  display: inline-block;
                  vertical-align: middle;
                  color: white;
                  font-size: 14px;
                  text-transform: uppercase;
                }

                .fa {
                  color: white;
                  font-weight: bold;
                  vertical-align: middle;
                  @include center_y;
                  right: 10px;
                }

                .lang_selected_image {
                  .language_selected, .arrow {
                    vertical-align: middle;
                  }
                }

                .lang_options {
                  position: absolute;
                  top: 100%;
                  display: none;
                  left: 0;
                  z-index: 100;
                  background: white;
                  width: 100%;
                  line-height: 2em;

                  a {
                    display: block;
                    color: $font_color;
                    text-transform: capitalize;
                    padding: 10px 15px;
                    &:hover {
                        background: $corporate_1;
                        color: white;
                    }
                  }
                }
              }
            }
          }
        }
      }

      .red_bottom {
        margin-top: 30px;
        #main_menu {
          width: 800px;
          float: right;

          #main-sections-inner {
            text-align: justify;

            &:after {
              content: " ";
              display: inline-block;
              margin-left: 100%;
            }
          }

          .main-section-div-wrapper {
            display: inline-block;
            vertical-align: top;

            a {
              color: white;
              text-transform: uppercase;
              font-size: 14px;
              position: relative;
              font-weight: 400;
              @include transition(opacity, .4s);

              &:hover {
                opacity: .6;
              }
            }

            &:hover, &#section-active {
              a:after {
                opacity: 1;
              }
            }
          }
        }
      }
    }
  }
}

// Fixing Datepicker
.fixing_datepicker {
  top: 130px !important;
  z-index: 1001 !important;
}

/*=== Slider Container ===*/
body:not(.inner_section) {
  #slider_container .forcefullwidth_wrapper_tp_banner {
    /*height: 80vh!important;*/
  }
}

#fancy_contact {
  text-align: center;
  margin-bottom: 30px;

  .input_element {
    label {
      display: block;
      color: $corporate_1;
      font-weight: bold;
      margin: 5px 0;
      &.error {
        color: #900;
      }
    }

    input, textarea {
      display: block;
      margin: auto;
      width: 300px;
      box-sizing: border-box;
      padding: 10px;
    }

    .check_privacy {
      display: inline-block;
      width: auto;
      vertical-align: middle;
    }

    .title {
      display: inline-block;
      vertical-align: middle;
      margin-left: 5px;

      a {
        color: $corporate_1;
      }
    }
  }

  .thanks_popup_wrapper {
    font-weight: bold;
    margin-top: 5px;
  }

  #contact-button-wrapper {
    #popup_form_button {
      display: inline-block;
      padding: 10px 30px;
      background: $corporate_1;
      color: white;
      text-transform: uppercase;
      margin-top: 10px;
    }
  }
}

#slider_container {
  position: relative;

  .tp-bullets {
    bottom: 50px !important;

    .bullet {
      width: 20px;
      height: 20px;
      background: transparent;
      border: 1px solid white;
      border-radius: 50%;
      display: inline-block;
      box-sizing: border-box;

      &.selected {
        background: white;
        border: 3px solid $corporate_1;
      }
    }
  }

  .revolution_card {
    position: absolute;
    width: 100%;
    background: rgba($black, .6);
    color: white;
    z-index: 21;
    padding: 30px 0;
    bottom: 0;

    .revolution_content {
      position: relative;
    }

    .revolution_title {
      text-transform: uppercase;
      font-size: $size_title;
      margin-bottom: 10px;
    }

    .revolution_description {
      font-size: $size_description;
    }

    .revolution_link {
      @include center_y;
      left: 75%;
      display: inline-block;
      //font-size: 72px;
      color: white;

      &.square {
        background: $corporate_1;
        padding: 10px;
        text-transform: uppercase;
      }

      .fa {
        font-size: 72px;
      }
    }
  }

  .ticks_slider_wrapper {
    position: relative;
    z-index: 20;
    width: 100%;
    background: white;
    text-align: center;
    padding: 10px 0;

    .tick_element {
      color: $corporate_1;
      text-transform: uppercase;
      margin: 0 30px;

      &, & * {
        display: inline-block;
        vertical-align: middle;
      }

      .tick_image {
        .fa {
          vertical-align: initial;
          font-size: 25px;
        }
      }

      .tick_title {
        font-size: 12px;
        margin-left: 10px;
      }
    }
  }
}

body.inner_section {
  #slider_container {
    height: 300px;

    .exceded_revolution {
      height: 100%;
      width: 100%;
      overflow: hidden;
      position: relative;

      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }

    div#full_wrapper_booking.inner_engine {
      bottom: 0;
    }
  }
}

/*=== Content Subtitle ===*/
.content_subtitle_wrapper {
  padding: 60px 0;

  .content_subtitle_title {
    text-align: center;
    text-transform: uppercase;
    font-size: $size_title;
    color: $font_color;
    font-family: $font_title;
  }

  .content_subtitle_description {
    width: 700px;
    margin: 20px auto 0;
    text-align: justify;
    columns: $font_color;
    line-height: $line_description;
    font: {
      size: $size_description;
      weight: normal;
    }
  }
}

/*=== Automatic Content ===*/
.automatic_content_wrapper {
  padding: 60px 0;

  .section-title {
    text-align: center;
    text-transform: uppercase;
    font-size: $size_title;
    color: $font_color;
    font-family: $font_title;
  }

  & > div {
    width: 700px;
    margin: 20px auto 0;
    text-align: justify;
    line-height: $line_description;
    columns: $font_color;
    font: {
      size: $size_description;
      weight: normal;
    }
  }

  #my-bookings-form-fields {
    display: table;
    margin: auto;
    margin-top: 20px;

    label#my-bookings-email-label, label#my-bookings-localizador-label {
      font-family: 'Oswald', sans-serif;
      font-weight: lighter;
      margin-right: 15px;
    }

    #emailInput {
      padding: 10px;
      margin-right: 25px;
    }

    input#localizadorInput {
      padding: 10px;
    }

    & > ul {
      margin-top: 20px;
      text-align: center;

      & > li {
        display: inline-block;

      }
    }

    .modify-reservation {
      background: $corporate_1;
    }

    .searchForReservation {
      background: $corporate_2;
    }

    .modify-reservation, .searchForReservation {
      color: white;
      font-weight: 300;
      font-size: 15px;
      text-transform: uppercase;
      padding: 10px 20px;
      border: 0;
      font-family: 'Oswald', sans-serif;
    }
  }

  #cancel-button-container {
    display: table;
    margin: auto;
    margin-top: 20px;

    button {
      color: white;
      font-weight: 300;
      font-size: 15px;
      text-transform: uppercase;
      padding: 10px 20px;
      border: 0;
      background: $corporate_2;
      display: none;
      font-family: 'Oswald', sans-serif;
      cursor: pointer;

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .my-bookings-booking-info {
    margin: auto !important;
    display: table;
  }

  .fResumenReserva {
    //background: $corporate_4;
    border: 1px solid $corporate_1;

    .txtCosteTotal {
      color: $corporate_1;
    }
  }
}

/*=== Carousel Top & Bottom ===*/
.minigallery_title {
  font-size: $size_title;
  font-family: $font_title;
  color: $font_color;
  text-align: center;
  padding: 40px 0;
}

.carousel_top_wrapper, .carousel_bottom_wrapper {
  display: block;
  width: 100%;
  margin-top: 5px;
  position: relative;

  .carousel_element {
    position: relative;
    width: calc(100% / 3);
    height: 300px;
    float: left;
    overflow: hidden;

    .carousel_image {
      img {
        @include center_xy;
        max-width: none;
        min-width: 100%;
        min-height: 100%;
        -webkit-filter: grayscale(40%) brightness(70%); /* Safari 6.0 - 9.0 */
        filter: grayscale(40%) brightness(70%);
        -ms-filter: grayscale(40%) brightness(70%);
        -webkit-transition: all 1s;
        -moz-transition: all 1s;
        -ms-transition: all 1s;
        -o-transition: all 1s;
        transition: all 1s;
      }
    }

    .carousel_title {
      @include center_y;
      text-align: center;
      width: 100%;
      z-index: 1;
      color: white;
      text-transform: uppercase;
      font-size: $size_title;
      font-family: $font_title;

    }
    &:hover {
      .carousel_image {
        img {
          -webkit-filter: grayscale(0%) brightness(100%); /* Safari 6.0 - 9.0 */
          filter: grayscale(0%) brightness(100%);
        }

      }
    }
  }

  .flex-direction-nav {
    .flex-nav-prev {
      position: absolute;
      bottom: 120px;
      left: 10px;
    }

    .flex-nav-next {
      position: absolute;
      bottom: 120px;
      right: 10px;
    }

    .flex-prev, .flex-next {
      position: relative;
      width: 42px;
      height: 42px;
      display: inline-block;
      border-radius: 50%;
      background: $corporate_1;
    }

    .fa {
      @include center_xy;
      font-size: 36px;
      color: white;
    }
  }
}

/*======== Footer =======*/
footer {
  background: #4A4D4B;
  padding: 40px 0;
  position: relative;

  .footer_links_wrapper {
    text-align: center;

    .footer_link {
      display: inline-block;
      vertical-align: middle;
      color: white;
      margin-right: 10px;
      @include transition(opacity, .4s);

      &:hover {
        opacity: .8;
      }

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .separator_footer {
    background: white;
    width: 590px;
    margin: 40px auto;
    height: 1px;
  }

  .hotel_name_element {
    text-align: center;
    text-transform: uppercase;
    color: white;
    margin-bottom: 7px;
    margin-top: 40px;
  }

  .footer-copyright-links {
    text-align: center;
    color: #d8d8d8;
    font-size: 14px;
    font-weight: lighter;

    a {
      text-decoration: none;
      color: #d8d8d8;
      font-size: 13px;
      font-weight: lighter;

      &:hover {
        opacity: .8;
      }
    }
  }

  #div-txt-copyright {
    margin-top: 7px;
    text-align: center;

    p {
      color: #d8d8d8;
      font-size: 13px;
      font-weight: lighter;
    }
  }
}

/*=== Cycle Banners ===*/
.cycle_banners_wrapper {
  display: inline-block;
  width: 100%;
  background: #F9F9F9;
  padding: 60px 0;

  .cycle_element {
    display: block;
    width: 100%;
    height: 570px;
    position: relative;

    &:nth-child(odd) {
      .cycle_image {
        float: left;
      }

      .cycle_content {
        float: right;
      }

      .flex-control-nav {
        right: 55px;
      }
    }

    &:nth-child(even) {
      .cycle_image {
        float: right;
      }

      .cycle_content {
        float: left;
      }

      .flex-control-nav {
        right: 55%;
      }
    }

    .flex-control-nav {
      position: absolute;
      bottom: 65px;

      li {
        display: inline-block;
        vertical-align: middle;
        width: 20px;
        height: 20px;
        border: 1px solid $corporate_1;
        border-radius: 50%;
        position: relative;
        margin-right: 5px;

        &:last-child {
          margin-right: 0;
        }

        a {
          @include center_xy;
          width: 16px;
          height: 16px;
          display: inline-block;
          color: transparent;
          border-radius: 50%;
          background: transparent;

          &.flex-active {
            background: $corporate_1;
          }
        }
      }
    }

    .cycle_image {
      display: inline-block;
      width: 50%;
      height: 570px;
      position: relative;
      overflow: hidden;

      img {
        @include center_xy;
        max-width: none;
        min-width: 100%;
        min-height: 100%;
      }
    }

    .cycle_content {
      display: inline-block;
      width: 50%;
      height: 100%;
      height: 570px;
      background: white;
      box-sizing: border-box;
      padding: 55px;
      position: relative;

      .center_block {
        width: 100%;
        height: 100%;
        position: relative;
      }

      .cycle_title {
        text-transform: uppercase;
        font-size: $size_title;
        color: $font_color;
        font-family: $font_title;
        margin-bottom: 40px;
      }

      .cycle_description {
        font-size: $size_description;
        line-height: $line_description;
        text-align: justify;
        color: $font_color;
        font-weight: normal;

        hide {
          display: none;
        }
      }

      .links_block {
        display: inline-block;
        position: absolute;
        bottom: 0;

        .booking_button {
          display: inline-block;
          vertical-align: middle;

          a.button-promotion {
            display: inline-block;
            vertical-align: middle;
            background: $corporate_1;
            color: white;
            font: {
              size: 18px;
              weight: lighter;
            }
            text-transform: uppercase;
            padding: 10px 50px;
            @include transition(opacity, .4s);

            &:hover {
              opacity: .8;
            }
          }
        }

        .see_more {
          display: inline-block;
          vertical-align: middle;
          width: 41px;
          height: 41px;

          a {
            display: inline-block;
            width: 41px;
            height: 41px;
            position: relative;
            background: $corporate_2;

            &:hover {
              opacity: .8;
            }

            .fa {
              @include center_xy;
              color: white;
            }
          }
        }
      }
    }
  }
}

.fancy_hide {
  .fancybox-outer {
    padding: 15px !important;
  }
}

/*====== Location =====*/
.location_section_wrapper {
  display: block;
  width: 100%;
  margin-top: 55px;

  img.location_image {
    width: 100%;
  }

  .image_location_wrapper {
    height: 400px;
    overflow: hidden;
  }

  .location_wrapper_text {
    display: table;
    margin-left: 30px;
    background: white;
    margin-top: -120px;
    width: 825px;
    z-index: 2;
    padding: 40px;
    position: relative;
    box-sizing: border-box;

    h3.location_title {
      color: $corporate_1;
      font-size: $size_title;

      &:after {
        content: '';
        display: block;
        width: 65px;
        height: 4px;
        background: $corporate_1;
        margin: 21px 0 27px;
      }
    }
  }

  .location_content {
    margin-top: 10px;
    line-height: $line_description;
    font-size: $size_description;
    color: #4e4e4e;

    strong {
      display: block;
      color: $corporate_1;
    }
  }
}

#contactContent .info {
  padding-left: 0 !important;
}

.contact_iframe_background {
  background: #eeeeee;
  padding: 59px 0;
  margin-bottom: 35px;

  h1#title {
    display: none;
  }

  div#google-plus, .fb_iframe_widget {
    display: none;
  }

  .contact_form {
    background: white;
    width: 100%;
    float: left;
    padding: 0 41px;
    box-sizing: border-box;

    label.title {
      display: block;
      clear: both;
      width: 100% !important;
      font-weight: 400;
      margin-bottom: 15px;
      color: #585858;
      font-size: 17px;
    }

    .bordeInput {
      margin-left: 0 !important;
      width: 100% !important;
      box-sizing: border-box;
      border: 0 !important;
      background: #eeeeee;
      height: 40px;
      padding-left: 30px;
    }

    textarea.bordeInput {
      padding-top: 20px;
    }

    div#contact-button {
      width: 155px !important;
      height: 42px !important;
      background: $corporate_1 !important;
      text-transform: uppercase;
      text-align: center;
      box-sizing: border-box;
      padding: 11px 0 !important;
      border-radius: 0 !important;

      &:hover {
        opacity: .8;
      }
    }

    div#contact-button-wrapper {
      padding-right: 0 !important;
    }

    input#privacy, input#has_reservation {
      display: inline-block;
      float: left;
      width: auto !important;
      vertical-align: middle;
      height: auto;
      margin-right: 10px;
      margin-top: 4px;
    }

    input#privacy + .title {
      margin-top: 0;
      width: auto;
    }

    input#privacy + span a {
      font-size: 11px;
      margin-bottom: 15px;
      color: #585858;
      text-decoration: none;
    }

    .has_reservation_wrapper {
      display: block;
      margin-top: 7px;

      span {
        font-size: 11px;
        margin-bottom: 15px;
        color: #585858;
        text-decoration: none;
      }
    }
  }
}

#contactContent .title {
  font-weight: 300;
  margin-bottom: 10px;
}

.iframe_wrapper {
  width: 50%;
  float: right;
  overflow: hidden;
}

div#contact-button {
  width: 125px;
  height: 47px;
  display: inline-block;
  vertical-align: top;
  float: left;
  color: white;
  background: $corporate_1;
  font-size: 17px;
}

/*===== Bottom bannersx2 ====*/
.full_bannersx2_bottom_wrapper {
  display: table;
  width: 100%;
  min-width: 1140px;

  .iframe_wrapper {
    width: 75%;
    float: left;
    height: 400px;
    overflow: hidden;

    iframe {
      width: 100%;
      height: 400px;
    }
  }

  .location_info_wrapper {
    width: 25%;
    float: right;
    position: relative;
    height: 400px;
    overflow: hidden;

    img.background_image_decorative {
      position: absolute;
      z-index: 3;
      left: 0;
      right: 0;
      margin: auto;
      width: 70%;
      top: 0;
      bottom: 0;
    }

    .black_overlay {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 100%;
      background: rgba(0, 0, 0, 0.25);
    }

    .location_background_image {
      @include center_xy;
      min-width: 100%;
      min-height: 100%;
      max-width: none;
    }

    .centered_text_wrapper {
      color: white;
      position: absolute;
      width: 65%;
      box-sizing: border-box;
      left: 0;
      right: 0;
      margin: auto;
      @include center_y;

      .description_location {
        font-size: $size_description;
        font-weight: lighter;
        line-height: $line_description;
      }
    }

    h1.title_location, .description_location {
      line-height: 26px;
    }

    h1.title_location {
      font-size: $size_title;
    }
  }
}

/*======== Image Gallery ========*/
.gallery-image-wrapper {
  height: 780px !important;
  overflow: hidden;
}

.gallery-image {
  background: white;
  padding: 0 0 35px;
  margin-top: 30px;
}

.filter-gallery {
  background: $corporate-1;
  height: 75px;
  line-height: 75px;
  color: white;
  text-transform: uppercase;
  font-size: 24px;
  font-weight: 200;
  margin-bottom: 50px;
  cursor: pointer;
  position: relative;

  .element_hide {
    display: none;
  }

  h3 {
    padding-left: 30px;
  }

  span {
    display: inline-block;
    position: absolute;
    height: 75px;
    width: 75px;
    background: $corporate-2 url(/img/#{$base_web}/arrow-newsletter.png) no-repeat center center;
    right: 0px;
    top: 0px;
    border-left: 2px solid white;
    //transform: rotate(90deg);
  }
  ul {
    background: lighten($corporate-1, 33%);
    font-size: 18px;
    line-height: 1;
    display: none;
    //width: calc(100% - 76px);

  }
  li {
    padding: 10px 30px;
    cursor: pointer;
    color: $corporate-1;
  }
  li:hover {
    background: lighten($corporate-1, 25%);
  }
}

.big-img {
  text-align: center;
  max-height: 700px;
  overflow: hidden;
  -webkit-transition: all 2s;
  -moz-transition: all 2s;
  -ms-transition: all 2s;
  -o-transition: all 2s;
  transition: all 2s;
  position: relative;

  .image_filters_wrapper {
    position: absolute;
    top: 20px;
    right: 20px;

    .filter_element {
      width: 175px;
      text-align: center;
      font-size: 12px;
      background: rgba(0, 0, 0, 0.6);
      color: white;
      margin-bottom: 3px;
      padding: 8px 0;
      text-transform: uppercase;
      font-family: Raleway, sans-serif;
      cursor: pointer;

      &.active {
        color: $corporate_1;
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }

  .gallery_image_title {
    position: absolute;
    bottom: 20px;
    font-size: 13px;
    left: 20px;
    background: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 10px 15px;
    font-family: Raleway, sans-serif;
  }

  img.main_image {
    width: 100%;
  }

  img.gallery_previous_image, img.gallery_next_image {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 30px;
    margin: auto;
    cursor: pointer;
    height: 60px;

    &:hover {
      opacity: 0.8;
    }
  }

  img.gallery_previous_image {
    -ms-transform: rotate(180deg); /* IE 9 */
    -webkit-transform: rotate(180deg); /* Chrome, Safari, Opera */
    transform: rotate(180deg);
    right: auto;
    left: 30px;
    height: 60px;
  }
}

.video_iframe {
  max-height: 700px;
  overflow: hidden;
  position: relative;

  .image_filters_wrapper {
    position: absolute;
    top: 20px;
    right: 20px;

    .filter_element {
      width: 175px;
      text-align: center;
      font-size: 12px;
      background: rgba(0, 0, 0, 0.6);
      color: white;
      margin-bottom: 3px;
      padding: 8px 0;
      text-transform: uppercase;
      font-family: Raleway, sans-serif;
      cursor: pointer;

      &.active {
        color: $corporate_1;
      }

      &:hover {
        opacity: 0.8;
      }
    }
  }

  img.gallery_previous_image, img.gallery_next_image {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 30px;
    margin: auto;
    cursor: pointer;

    &:hover {
      opacity: 0.8;
    }
  }

  img.gallery_previous_image {
    -ms-transform: rotate(180deg); /* IE 9 */
    -webkit-transform: rotate(180deg); /* Chrome, Safari, Opera */
    transform: rotate(180deg);
    right: auto;
    left: 30px;
  }
}

.image-grid {
  position: relative;
  margin-top: 20px;

  .slides li {
    height: 50px;
    overflow: hidden;
    position: relative;
    //display: inline-block;
    //margin: 0 -4px!important;
    //float: none!important;

    img {
      position: absolute;
      top: 0;
      left: -50%;
      bottom: 0;
      right: -50%;
      margin: 0 auto;
      min-width: 120%;
      min-height: 50px;
      height: auto;
      vertical-align: bottom;
      cursor: pointer;
    }
  }
}

div#gallery_list_1 {
  width: 1140px;
  margin: auto;
  padding: 0 55px;
  box-sizing: border-box;

  ul.slides {
    display: table;
    margin: auto;
    //width: auto!important;
    //text-align: center;
  }

  ul.flex-direction-nav {
    position: absolute;
    left: 0;
    top: 0;
    right: 0;

    a.flex-prev {
      position: absolute;
      left: 0;
      background: $corporate_1 url(/img/#{$base_web}/arrow_button.png) no-repeat center;
      width: 55px;
      height: 50px;
      z-index: 2;
      text-indent: 999px;
      overflow: hidden;
      -ms-transform: rotate(180deg);
      -webkit-transform: rotate(180deg);
      transform: rotate(180deg);
      &:hover {
        opacity: 0.8;
      }

      &.flex-disabled {
        display: none;
      }
    }

    a.flex-next {
      position: absolute;
      right: 1px;
      background: $corporate_1 url(/img/#{$base_web}/arrow_button.png) no-repeat center;
      width: 55px;
      height: 50px;
      z-index: 2;
      text-indent: 999px;
      overflow: hidden;

      &:hover {
        opacity: 0.8;
      }

      &.flex-disabled {
        display: none;
      }
    }
  }
}

.gallery-wrapper .big-img {
  width: 100%;
  height: 650px;
  overflow: hidden;
}

/*====== Habitaciones ====*/
.rooms_wrapper {
  margin-bottom: 60px;
  margin-top: 30px;

  .room_element {
    height: auto;
    margin-bottom: 20px;
    width: 100%;
    display: table;

    &:nth-child(even) {
      .exceded {
        float: right;
      }

      .room_description_wrapper {
        float: left;
      }
    }

    h3.room_title {
      font-size: $size_title;
      color: $corporate_1;
      margin-bottom: 12px;
      font-family: $font_title;

      span.capacity {
        font-weight: lighter;
        font-size: 16px;
        text-transform: capitalize;
        vertical-align: top;
        margin-top: 6px;
        display: inline-block;

        .capacity_image {
          vertical-align: middle;
          padding-bottom: 4px;
        }
      }
    }

    .see_more_room {
      background-color: $corporate_2;
      color: white;
      padding: 10px 25px;
      display: inline-block;
      @include transition(opacity, .4s);

      &:hover {
        opacity: .8;
      }
    }

    .room_description {
      font-weight: normal;
      font-size: 14px;
      line-height: $line_description;
      color: $font_color;
      padding-bottom: 22px;
      border-bottom: 1px solid #CECECE;
      margin-bottom: 22px;

      .hide_me {
        display: none;
      }
    }

    .service_elements li {
      display: inline-block;
      padding-right: 20px;
      font-size: 13px;
      color: $corporate_1;
      font-weight: lighter;

      img {
        vertical-align: middle;
        margin-right: 5px;
      }

      .fa {
        width: 35px;
        height: 35px;
        vertical-align: middle;
        background: $corporate_1;
        color: white;
        position: relative;
        border-radius: 50%;
        margin-right: 5px;
        font-size: 16px;

        &:before {
          @include center_xy;
        }
      }
    }

    .exceded {
      width: 32%;
      min-height: 278px;
      float: left;
      position: relative;
      overflow: hidden;

      li {
        position: relative;
        overflow: hidden;
      }

      .slides li {
        height: 278px;
      }

      img.room_image {
        min-height: 100%;
        max-width: none;
        position: absolute;
        left: 50%;
        top: 50%;
        -webkit-transform: translateY(-50%) translateX(-50%);
        @include center_xy();
      }

      .plus_image {
        position: absolute;
        left: 20px;
        top: 20px;
        z-index: 1;

        &:hover {
          opacity: .8;
        }

        .fa {
          color: $corporate_1;
          background: white;
          padding: 5px;
          border-radius: 5px;
        }
      }
    }

    .flex-nav-prev {
      position: absolute !important;
      left: 0;
      top: 0;
      bottom: 0;
      height: 45px;
      margin: auto;
    }

    .flex-nav-next {
      position: absolute !important;
      right: 0;
      top: 0;
      bottom: 0;
      height: 45px;
      margin: auto;
    }

    .room_description_wrapper {
      background: #F8F8F8;
      float: right;
      width: 68%;
      padding: 25px 40px;
      min-height: 278px;
      box-sizing: border-box;
      position: relative;
    }

    .service_elements {
      .divide_0, .divide_1, .divide_2 {
        width: 32%;
        display: inline-table;

        li {
          font-size: 13px;
          color: $corporate_2;
          font-weight: lighter;

          img {
            vertical-align: middle;
            margin-right: 5px;
          }
        }
      }
    }

    .room_buttons_wrapper {
      position: absolute;
      top: 17px;
      right: 40px;

      img {
        vertical-align: middle;
        width: auto;
        display: none;
        height: 37px;
      }

      .room_book {
        color: white;
        padding: 8px;
        background: $corporate_1;
        width: 117px;
        box-sizing: border-box;
        height: 37px;
        display: inline-block;
        text-align: center;
        text-decoration: none;
        text-transform: uppercase;
        float: right;

        &:hover {
          opacity: .8;
        }
      }
    }

    .room_types {
      position: absolute;
      top: 16px;
      right: 27%;

      a {
        text-decoration: none;
      }
    }
  }
}

/* Rooms icons tooltips */
.tooltip {
  display: inline;
  position: relative;
  cursor: pointer;
}

.tooltip:hover:after {
  background: $corporate-1;
  border-radius: 5px;
  bottom: 26px;
  color: white;
  content: attr(title);
  left: 20%;
  top: -54px;
  height: 22px;
  padding: 5px 15px;
  position: absolute;
  z-index: 98;
  text-align: center;
  text-transform: uppercase;
}

.iframe_maps_wrapper {
  position: relative;

  iframe {
    vertical-align: middle;
    width: 100%;
  }

  .iframe_maps_title {
    @include full_size;
    width: 100%;
    text-transform: uppercase;
    color: white;
    font-family: $font_title;
    font-size: $size_title;
    background: rgba(black, .6);
    cursor: pointer;
    @include transition(top, .6s);

    &.open {
      top: 400px;
    }
  }
}

.full_screen_menu {
  position: fixed;
  bottom: 0;
  left: 0;
  right: auto;
  background: rgba($corporate_2, 0.9);
  top: 86px;
  z-index: 24;
  width: 0;
  overflow-x: hidden;
  overflow-y: auto;
  @include transition(width, 1s);

  &.active {
    width: 40%;
    #mainMenuDiv {
      opacity: 1;
    }
  }

  div#mainMenuDiv ul#main-sections-inner {
    display: block;
    justify-content: none;
  }

  .separator_element {
    display: none;
  }

  div#logoDiv {
    display: table;
    margin: auto;
    margin-bottom: 40px;
  }

  #mainMenuDiv {
    position: absolute;
    right: auto;
    left: auto;
    top: 0;
    bottom: 0;
    transform: none;
    display: table;
    width: 400px;
    margin: auto;
    max-width: 100%;
    opacity: 0;
    margin-top: 30px;
    @include center_xy;
    @include transition(opacity, 1s);

    .main-section-div-wrapper {
      text-align: center;
      text-transform: uppercase;
      font-weight: lighter;
      width: auto !important;
      margin: 0 auto 10px;
      .menu_icon {
        display: none;
      }

      a {
        display: block;
        padding: 0 0 5px;
        text-decoration: none;
        color: white;
        cursor: pointer;
        font-size: 18px;
        font-family: $font_title;
        @include transition(all, .6s);

        &:before {
          content: '';
          display: inline-block;
          vertical-align: middle;
          background-color: $corporate_1;
          height: 1px;
          width: 0;
          @include transition(width, .6s);
        }

        &:hover {
          color: $corporate_1;
          &:before {
            width: 100px;
            margin-right: 10px;
          }
        }
      }
    }
  }
}

.bannersx2_wrapper {
  display: inline-block;
  width: 100%;

  .banner_element {
    display: inline-block;
    width: 50%;
    height: 480px;
    position: relative;
    float: left;
    overflow: hidden;

    &:hover {
      .banner_content {
        width: 100%;
      }
    }

    &.right {
      .banner_content {
        right: 0;
        left: auto;
      }
    }

    .banner_content {
      position: absolute;
      top: 0;
      bottom: 0;
      left: 0;
      width: 330px;
      background: rgba(black, .6);
      padding: 0 30px;
      text-align: center;
      @include transition(width, .6s);

      .banner_title {
        @include center_y;
        left: 0;
        width: 100%;
        color: white;
        font-family: $font_title;
        font-size: $size_title;
        text-align: center;
        padding: 0 30px;
      }
    }
  }
}

.newsletter_wrapper_container {
  background: #F4EFEB;
  text-align: center;
  .newsletter_wrapper {
    width: 100%;
    height: 100%;
    background-position: center;
    background-size: cover;
    background-attachment: fixed;
    padding: 50px 0;
  }

  .newsletter_title {
    font-size: $size_title;
    color: white;
    font-family: $font_title;
    margin-bottom: 40px;
    text-transform: uppercase;
  }

  .newsletter_description {
    font-size: $size_description;
    color: white;
  }

  .newsletter_form {
    margin-top: 40px;

    .input_email {
      width: 210px;
      height: 50px;
      background: white;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      border: 0;
      text-align: center;
      font-size: 14px;
      color: black;
      display: inline-block;
      vertical-align: middle;
    }

    .button_newsletter {
      background-color: $corporate_1;
      color: white;
      display: inline-block;
      vertical-align: middle;
      text-transform: uppercase;
      padding: 15.5px 80px;
      cursor: pointer;
      @include transition(opacity, .4s);

      &:hover {
        opacity: .6;
      }
    }

    .check_newsletter {
      margin-top: 10px;

      a, label {
        color: white;
        font-size: 14px;
      }
      a {
        text-decoration: underline;
      }
    }
  }
}

.datepicker_wrapper_element .datepicker_ext_inf_ed {
  display: none;
}

@import "individual_room";
@import "form_contact";
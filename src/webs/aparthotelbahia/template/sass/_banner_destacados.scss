.banner_destacados_wrapper {
  position: relative;
  .title_background {
    color: $corporate_1;
    font-size: 32px;
    font-weight: 700;
    letter-spacing: 1px;
    text-align: center;
    padding-top: 55px;
  }

  .services {
    position: relative;
    text-align: center;
    padding: 50px 0 30px;
    .service {
      margin: 0px 10px;
      text-decoration: none;
      position: relative;
      display: inline-block;
      border-radius: 10px;
      width: 315px;
      background-color: $corporate_1;
      span {
        display: block;
        width: 100%;
        padding: 20px 0;
        text-align: center;
        font-size: 18px;
        font-weight: 300;
        color: white;
      }
      i.fa {
        @include center_y;
        left: 30px;
        color: white;
        width: 30px;
        height: 30px;
        font-size: 50px;
        &:before {
          @include center_xy;
        }
      }
      &.service_link:hover {
        background-color: $corporate_1;
      }
    }
    .owl-nav {
      @include center_y;
      height: 30px;
      width: 100%;
      .owl-prev, .owl-next {
        color: $corporate_3;
        font-size: 40px;
        position: absolute;
      }
      .owl-prev {
        left: -20px;
      }
      .owl-next {
        right: -20px;
      }
    }
  }
}
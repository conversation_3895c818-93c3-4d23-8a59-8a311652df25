.news_widget_background {
	background-color: white;
	margin-top: 50px;
}
.news_widget_wrapper {
	font-size: $description_size;
	text-align: center;
	padding: 60px 0;
	.entry_widget {
		display: inline-block;
		background-color: white;
		vertical-align: top;
		width: 360px;
		text-align: center;
		margin: 0 5px 20px;
		color: #666;
		border:1px solid #DDD;
	 	.image {
			position: relative;
			width: 100%;
			height: 150px;
			overflow: hidden;
			img {
				@include center_xy;
				width: 100%;
			}
		}
		.tags {
			font-size: 80%;
			color: $corporate_2;
			padding: 20px;
		}
		.title {
			font-weight: bold;
			text-transform: uppercase;
			padding: 0 20px;
			a {
				color: #666;
				&:hover {
					color: $corporate_2;
				}
			}
		}
		.content {
			font-size: 80%;
			padding: 20px;
			line-height: 180%;
			height: 200px;
			width: 100%;
			box-sizing: border-box;
			overflow: hidden;
		}
		.links {
			display: table;
			width: 100%;
			box-sizing: border-box;
			border-top:1px solid #DDD;
			padding: 10px;
			.comments, .author, .date {
				display: table-cell;
				position: relative;
				font-size: 80%;
				height: 50px;
				text-align: center;
				span {
					display: block;
					width: 100%;
					@include center_xy;
				}
			}
			.comments {
				border-left: 1px solid transparent;
				border-right: 1px solid #DDD;
				i {
					border: 1px solid #DDD;
					border-radius: 50%;
					padding: 10px;
					font-size: 25px;
					color: #AAA;
					vertical-align: middle;
				}
				b {
					background-color: $corporate_2;
					color: white;
					padding: 3px 10px;
					border-radius: 50%;
					font-size: 10px;
					margin-left: -10px;
					vertical-align: middle;
				}
			}
			.author {
				border-right: 1px solid transparent;
				border-left: 1px solid transparent;
			}
			.date {
				border-left: 1px solid #DDD;
				border-right: 1px solid transparent;
				color: $corporate_2;
			}
		}
	}
}
.see_more_news {
	text-align:center;
	a {
		display: inline-block;
		margin: 0 60px 60px;
		background-color: $corporate_2;
		color: white;
		text-transform: uppercase;
		padding: 20px 60px;
	}
}
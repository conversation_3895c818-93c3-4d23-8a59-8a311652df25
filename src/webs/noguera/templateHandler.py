# -*- coding: utf-8 -*-
import os

from booking_process.utils.booking.normalizationUtils import normalizeForClassName
from booking_process.constants.advance_configs_names import CONTACT_PHONES, PUBLIC_CAPTCHA_KEY, TOP_SECTIONS
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape, build_friendly_url, sanitize_html
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name, getPicturesForKey
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, get_sections_from_type
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_code, get_language_title
from utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.templates.template_utils import buildTemplate
from webs.BaseTemplateHandler2 import BaseTemplateHandler2WithRedirection

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "nogua"
TEMPLATE_NAME = "noguera"

class TemplateHandler(BaseTemplateHandler2WithRedirection):

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)


		result_params_dict = {'base_web': base_web,
							  'booking_engine_2': self.buildSearchEngine2(language),
							  'scripts_to_render_desktop': self.scriptsToRenderDesktop(language, dict_params={"allow_messages": False}),
							  'language_selected': get_language_title(language),
							  'text_web_oficial': get_section_from_section_spanish_name("web oficial", language),
							  'phone_contact': get_config_property_value(CONTACT_PHONES),
							  'language_code': get_language_code(language),
							  'ticks_slider': get_pictures_from_section_name("_ticks_header", language),
							  'maps_content': get_section_from_section_spanish_name("_banner_map", language),
							  'maps': get_section_from_section_spanish_name("Iframe google maps", language),
							  'newsletter': self.buildNewsletter2(language, social=True, check_newsletter=True),
		                      'corporate_logo_footer': get_pictures_from_section_name('_footer_logo', language),
		                      'footer_extra_images': get_pictures_from_section_name('_footer_extra_images', language),
		                      'custom_footer_menu': get_pictures_from_section_name('_custom_footer_menu', language),
							  'recaptcha_publickey': get_config_property_value(PUBLIC_CAPTCHA_KEY)
							  }

		news_section = build_friendly_url(get_web_dictionary(language)['T_noticias'])
		path = get_language_code(language) + "/" + news_section.replace(".html", "")
		result_params_dict['path'] = path

		all_sections = self.getSections(language)
		top_sections = self.getSectionsFor(language, all_sections, TOP_SECTIONS)
		for top_element in top_sections:
			section_pictures = getPicturesForKey(language, top_element.get('key'), [])
			top_element['icon'] = list(filter(lambda x: x.get('title') == 'ico', section_pictures))
		self.internationalizeUrls(top_sections)
		result_params_dict['top_sections'] = top_sections

		if sectionToUse and sectionToUse.get('subtitle'):
			result_params_dict['content_subtitle'] = sectionToUse

		#Automatic Content
		automatic_content = {
		'Mis Reservas': True
		}
		if automatic_content.get(section_type):
			result_params_dict['content_access'] = True

		if section_type == "Inicio":
			result_params_dict['home'] = True
			result_params_dict['offers_banner'] = self.getOffers(language)[:2]


		elif section_type == "Habitaciones":
			result_params_dict['rooms'] = get_pictures_from_section_name('_habitaciones_blocks',language)

		elif section_type == u"Habitación Individual":
			result_params_dict['rooms_individual'] = self.getRoom(sectionToUse, language)
			result_params_dict['room_individual_section'] = True

		elif section_type == "Ofertas":
			result_params_dict['offers'] = self.getOffers(language)

		elif section_type == "Oferta Individual":
			offers_section = get_sections_from_type(section_type, language)
			result_params_dict['offers_section'] = offers_section

		elif section_type == "Galeria de Imagenes":
			result_params_dict['gallery_section'] = self.getGalleryPictures(language)

		elif section_type == u"Localización":
			result_params_dict['form_contact'] = True

		elif section_type == "Mis Reservas":
			result_params_dict['my_bookings'] = True

		elif section_type == "Noticias":
			result_params_dict['news'] = self.getNews(language)

		elif not sectionToUse:
			news = self.getNews(language)

			result_params_dict['entry'] = self.getCurrentNewsItem(news, language)
			# result_params_dict['instagram_pictures'] = get_instagram_recent_media()
			result_params_dict['foto_collage'] = self.get_hotel_gallery(language)
			result_params_dict['social'] = self.getSocialDictionary()
			result_params_dict['banner_mosaic_audio_player_section'] = get_section_from_section_spanish_name(
				'_testimonial', language)
			result_params_dict['banner_mosaic_audio_player'] = get_pictures_from_section_name('_testimonial', language)
			result_params_dict['news_widget'] = self.getNews(language, 5)

		result_params_dict = self.getExtraBanners(advance_properties, result_params_dict, language)


		return result_params_dict

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "735"

	def get_revolution_full_screen(self):
		return "on"

	def get_revolution_initializer(self):
		return True

	def getOffers(self, language):
		offers = self.buildPromotionsInfo(language)
		for x in offers:
			pictures_offers = getPicturesForKey(language, str(x.get("offerKey")), [])
			if pictures_offers:
				x['linkUrl'] = pictures_offers[0].get("linkUrl")
		return offers

	def getRoom(self, sectionToUse, language):
		rooms_block = sectionToUse
		rooms_services = self.getPicturesProperties(language, "_icons_list", ['icon'])
		rooms_block['gallery'] = list(filter(lambda l: not l.get("title"), get_pictures_from_section_name(rooms_block.get("sectionName"), language)))
		rooms_block['services'] = list(filter(lambda l: l.get("title") and l.get("title") != "slider", get_pictures_from_section_name(rooms_block.get("sectionName"), language)))
		advance_room = self.getSectionAdvanceProperties(rooms_block, language)
		if advance_room.get("services_title"):
			rooms_block['services_title'] = unescape(advance_room.get("services_title"))
		rooms_block['slug_name'] = normalizeForClassName(rooms_block.get("title"))
		return rooms_block

	def getRooms(self, language):
		rooms_blocks = get_sections_from_type(u"Habitación Individual", language)
		rooms_services = self.getPicturesProperties(language, "_icons_list", ['icon'])
		for x in rooms_blocks:
			x['gallery'] = list(filter(lambda l: not l.get("title"), get_pictures_from_section_name(x.get("sectionName"), language)))
			x['services'] = list(filter(lambda l: l.get("title") and l.get("title") != "slider", get_pictures_from_section_name(x.get("sectionName"), language)))
			advance_room = self.getSectionAdvanceProperties(x, language)
			if advance_room.get("services_title"):
				x['services_title'] = unescape(advance_room.get("services_title"))
			x['slug_name'] = normalizeForClassName(x.get("title"))
		return rooms_blocks

	def getExtraBanners(self, advance_properties, result_params, language):
		if advance_properties.get("minigallery"):
			minigallery_images = get_pictures_from_section_name(advance_properties.get("minigallery"), language)
			mini_dict = {'minigallery': minigallery_images,'num_items': 5}
			minigallery_html = self.buildTemplate_2("_minigallery.html", mini_dict, False, 'noguera')
			result_params["minigallery"] = minigallery_html
			result_params['minigallery_content'] = get_section_from_section_spanish_name(advance_properties.get("minigallery"), language)

		if advance_properties.get("bannerx2_text"):
			bannersx2 = self.getPicturesProperties(language, advance_properties.get("bannerx2_text"), ['button_text'])
			result_params['bannerx2_text'] = bannersx2

		if advance_properties.get("bannerx4"):
			bannersx4 = self.getPicturesProperties(language, advance_properties.get("bannerx4"), ['icon', 'text_link', 'button_icon', 'button_text'])
			result_params['bannersx4'] = bannersx4

		if advance_properties.get("services_slider"):
			carousel_icon = get_pictures_from_section_name(advance_properties.get("services_slider"), language)
			result_params['services_slider'] = carousel_icon

		if advance_properties.get("booking_button"):
			result_params['booking_button'] = advance_properties.get("booking_button")

		if advance_properties.get("cycle_banners"):
			bannersx4 = self.getPicturesProperties(language, advance_properties.get("cycle_banners"), ['icon', 'text_link'])
			result_params['bannersx4'] = bannersx4
			result_params['cycle_banners'] = True

		if advance_properties.get("fixed_banners"):
			cycle_banners = self.getPicturesProperties(language, advance_properties.get("fixed_banners"), ['gallery'])
			result_params["cycle_banner"] = list(filter(lambda x: not x.get("title") or x.get("title") != "background", cycle_banners))
			result_params['cycle_banner_back'] = list(filter(lambda x: x.get("title") == "background", cycle_banners))
			for pic in result_params["cycle_banner"]:
				if pic.get('gallery'):
					pic['gallery'] = get_pictures_from_section_name(pic.get('gallery'), language)

		return result_params

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_7/_booking_widget.html', params, allowMobile=False)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		options['departure_date_select'] = True
		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		return options

	def getNews(self, language, limit=None):
		news = self.buildNewsInfo(language)
		authors = get_pictures_from_section_name('_authors', language)

		for item in news:
			item['shortDesc'] = sanitize_html(item.get('description', None))

			item['comments'] = []
			for pic in item.get('pictures'):
				if pic.get("enabled"):
					comment = pic
					if not comment.get('servingUrl'):
						for author in authors:
							if comment.get('name', False) and author.get('title', False) and author.get('title') == comment.get('name', False):
								comment['pic'] = author.get('servingUrl')
							else:
								comment['pic'] = 'https://lh3.googleusercontent.com/PxhCbuVTWji6swvvbH5lEytdDHGaiCJ8qavBeBEW5TnR1G-DVbrhX6KHP24BMVDpNSC4FLKqlb-SWF2juCXrPmR86gXAKQ'
						item['comments'].append(comment)

			for author in authors:
				if item.get('author', False) and author.get('title', False) and author.get('title') == item.get('author', False):
					item['author_pic'] = author.get('servingUrl')
				else:
					item['author_pic'] = 'https://lh3.googleusercontent.com/PxhCbuVTWji6swvvbH5lEytdDHGaiCJ8qavBeBEW5TnR1G-DVbrhX6KHP24BMVDpNSC4FLKqlb-SWF2juCXrPmR86gXAKQ'

		if limit:
			return news[:limit]
		else:
			return news


	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
			currentSection = self.getSectionParams(sectionFriendlyUrl, language)
			advance_properties = self.getSectionAdvanceProperties(currentSection, language)
			additionalParams['custom_elements'] = ''
			base_path = os.path.dirname(__file__)
			section_name = ''
			section_type = ''
			if currentSection:
				section_name = currentSection['sectionName'].lower().strip()
				section_type = currentSection.get("sectionType")


			if user_agent_is_mobile():

				if advance_properties.get("cycle_banners"):
					cycle_dict = dict(get_web_dictionary(language))
					cycle_dict['cycle_banners_mobile'] = get_pictures_from_section_name(advance_properties.get("cycle_banners"), language)
					cycle_html = self.buildTemplate_2("mobile_templates/1/_cycle_banners_v1.html", cycle_dict, False)
					additionalParams['custom_elements'] += cycle_html

				if advance_properties.get('services_slider', False):
					mini_dict = {
						'carousel_icon': get_pictures_from_section_name(advance_properties.get('services_slider'), language)
					}
					mini_html = self.buildTemplate_2("mobile_templates/1/_carousel_icon.html", mini_dict, False)
					additionalParams['custom_elements'] += mini_html

				if advance_properties.get("minigallery"):
					mini_dict = {
						'minigallery_mobile': get_pictures_from_section_name(advance_properties.get("minigallery"), language)
					}
					mini_html = self.buildTemplate_2("mobile_templates/1/_minigallery_v1.html", mini_dict, False)
					additionalParams['custom_elements'] += mini_html

				if advance_properties.get("bannersx2"):
					mini_dict = {
						'bannersx2': self.getPicturesProperties(language, advance_properties.get("bannersx2"), ['icon', 'text_link'])
					}
					mini_html = self.buildTemplate_2("mobile_templates/1/_bannersx2.html", mini_dict, False)
					additionalParams['custom_elements'] += mini_html

				if section_type == u'Localización':
					currentSection['subtitle'] = None

				if section_type == u'Habitación Individual':
					section_params = {}
					section_params['actual_section'] = get_section_from_section_spanish_name(section_name,language)
					section_params['actual_section']['gallery'] = list(filter(lambda l: not l.get("title"), get_pictures_from_section_name(section_name, language)))
					section_params['actual_section']['services'] = list(filter(lambda l: l.get("title") and l.get("title") != "slider",get_pictures_from_section_name(section_name, language)))
					advance_room = self.getSectionAdvanceProperties(currentSection, language)
					if advance_room.get("services_title"):
						section_params['actual_section']['services_title'] = unescape(advance_room.get("services_title"))
					mini_dict = {'carousel_icon': section_params['actual_section']['services']}
					section_params['services'] = self.buildTemplate_2("mobile/_carousel_icon.html", mini_dict, False, 'noguera')
					result = dict(list(section_params.items()) + list(get_web_dictionary(language).items()))
					fullPath = os.path.join(base_path, 'template/mobile/_room_individual.html')

					return buildTemplate(fullPath, result)

				if advance_properties.get('bannersx4'):
					mini_dict = {
						'bannersx2': self.getPicturesProperties(language, advance_properties.get("bannersx4"),['icon', 'text_link'])
					}
					mini_html = self.buildTemplate_2("mobile_templates/1/_bannersx2.html", mini_dict, False)
					additionalParams['custom_elements'] += mini_html

				if advance_properties.get('fixed_banners'):
					mini_dict = {
						'cycle_banner': self.getPicturesProperties(language, advance_properties.get("fixed_banners"))
					}
					mini_html = self.buildTemplate_2("mobile/_cycle_banner.html", mini_dict, False, TEMPLATE_NAME)
					additionalParams['custom_elements'] += mini_html


			return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)
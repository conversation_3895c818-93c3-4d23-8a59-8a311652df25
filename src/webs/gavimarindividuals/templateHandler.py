# -*- coding: utf-8 -*-
from booking_process.handlers.base_handler import getSocialIDs
from booking_process.constants.advance_configs_names import PROMOCODE_IN_EMAIL, PUBLIC_CAPTCHA_KEY, WHATSAPP_ID
from booking_process.utils.data_management.configs_utils import get_config_property_value
from paraty_commons_3.data_structures.advanced_dictionaries import ImmutableDict
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.promotions_utils import get_offers
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.development.dev_booking_utils import DEV
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_title
from utils.mobile.mobile_utils import user_agent_is_mobile
from utils.web.content.builders.section_values_builder import get_rooms
from webs.BaseTemplateHandler2 import BaseTemplateHandler2
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "gavis"


calagranhotelMob = ImmutableDict({
'id': 'https://gavimarcalagranhotel-dot-gavimar-hoteles.appspot.com/booking1',
'domain': 'https://gavimarcalagranhotel-dot-gavimar-hoteles.appspot.com',
'value':'Gavimar Cala Gran Hotel'
})


hotels_list_4_mobile = [calagranhotelMob]

translates_local = {
	'SPANISH': {
		'T_wifi_terms': 'Condiciones: Oferta exclusiva para reservas realizadas en nuestra web oficial. Incluye acceso GRATUITO a wifi en las habitaciones y en todo el complejo para el uso de un máximo de 2 dispositivos simultaneamente por habitación',
	},
	'ENGLISH': {
		'T_wifi_terms': 'Terms & Conditions: Exclusive offer for bookings made on our official website. Includes FREE wifi access in the rooms and all over the complex for a maximum of 2 devices simultaneously per room.',
	},
	'GERMAN': {
		'T_wifi_terms': 'Konditionen: Exklusives Angebot für Reservierungen, die über unsere offizielle Webseite getätigt wurden. FREE Wi-Fi-Zugang in den Zimmern und auf dem gesamten Komplex für maximal 2 Geräte gleichzeitig.',
	}
}

default_language = translates_local['ENGLISH']



class TemplateHandler(BaseTemplateHandler2):


	def get_hotel_list(self, language):
		'''
		My bookings corporative

		'''
		options = {'selectOptions': hotels_list_4_mobile}
		return options

	def get_revolution_initial_height(self):
		return "550"

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)

		options['ticks_on_booking_footer'] = True


		return options




	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)

		host = os.environ.get('HTTP_HOST')


		if "apartament" in host or 'localhost' in host:
			params['roomtype_hide_filter'] = '.*studio.*|.*apartament.*'
			params['boardtype_hide_filter'] = u'Sólo Alojamiento'
			params['forced_logotype_hide_filter'] = u'http://lh6.ggpht.com/W2xlk88RKlylWEV1xVHQYXkOp_NwXNPyW8t2aJck6OyxuDpZKQrZ4ZmKoNKKu9AQexPxxauO7__LRqJHK1mOxqE'
			params['forced_host_logotype_hide_filter'] = u'http://www.calagranapartaments.com/'

			if DEV:
				params['bookingUrl'] = "http://localhost:8090/booking1"
			else:
				params['bookingUrl'] = "https://gavimarcalagranhotel-dot-gavimar-hoteles.appspot.com/booking1"
				params['namespace'] = "gavimarcalagranhotel"


		return self.buildTemplate('booking/booking_engine_3/_booking_widget.html', params)


	def getAdditionalParams(self, currentSectionName, language, allSections):
		host = os.environ.get('HTTP_HOST')
		sectionToUse = self.getCurrenSection(allSections)
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)
		section_name = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()

		result_params_dict = {'base_web': base_web,
							  'footer_columns': get_pictures_from_section_name("footer columns", language),
							  'newsletter_info': get_section_from_section_spanish_name("Newsletter", language),
							  'language_selected': get_language_title(language),
							  'newsletter': self.buildNewsletter2(language, social=False, check_newsletter=True),
							  'web_oficial_txt': get_section_from_section_spanish_name("web oficial", language),
							  'bottom_popup': get_section_from_section_spanish_name("popup inicio footer", language),
							  'bottom_popup_text': get_section_from_section_spanish_name("promocion pop up", language),
							  'bottom_popup_background': get_pictures_from_section_name("promocion pop up", language),
							  'bottom_popup_promocode': get_config_property_value(PROMOCODE_IN_EMAIL),
							  'accionistas_section': get_section_from_section_spanish_name(u"información a los socios", language),
							 }

		social_ids = getSocialIDs()

		if social_ids.get("whatsapp_id"):
			result_params_dict["whatsapp_link"] = social_ids["whatsapp_id"].replace("+", "").replace(" ", "")

		result_params_dict['popup_inicio_automatico'] = get_pictures_from_section_name('popup inicio', language)

		faldon_footer = get_section_from_section_spanish_name("popup inicio footer", language)
		if faldon_footer:
			faldon_pictures = get_pictures_from_section_name("popup inicio footer", language)
			if faldon_pictures:
				faldon_footer['background'] = faldon_pictures[0].get("servingUrl")
				faldon_footer['title_thanks'] = faldon_pictures[0].get("title")
				faldon_footer['description_thanks'] = faldon_pictures[0].get("description")
			advance_faldon = self.getSectionAdvanceProperties(faldon_footer, language)
			if advance_faldon.get("send_email"):
				faldon_footer['send_email'] = advance_faldon.get("send_email")
				faldon_footer['promocode'] = advance_faldon.get("promocode")
				faldon_footer['promocode_text'] = advance_faldon.get("promocode_text")
			else:
				faldon_footer['text_link'] = advance_faldon.get("text_link")
				faldon_footer['url_link'] = advance_faldon.get("url_link")
			if advance_faldon.get("use_small_faldon"):
				faldon_footer['use_small_faldon'] = advance_faldon.get("use_small_faldon")
			result_params_dict['faldon_footer'] = faldon_footer

		sections_with_bigbanner = {
			"inicio": True
		}
		section_with_bb = sections_with_bigbanner.get(section_name, False)
		result_params_dict['section_with_big_banner'] = section_with_bb


		sections_with_content_home = {
			"inicio": True
		}
		section_with_home_content = sections_with_content_home.get(section_name, False)
		result_params_dict['section_with_home_content'] = section_with_home_content



		sections_with_main_content = {
			"servicios": True,
			"servicios extras": True,
			"servicios adicionales": True,
			"mis reservas": True,
			u"todo incluido": True,
			u"imágenes": True,
			u"apartamentos": True,
			u"habitaciones": True,
		}
		section_with_main_content = sections_with_main_content.get(section_name, False)
		result_params_dict['section_with_main_content'] = section_with_main_content

		if advance_properties.get('section_with_content'):
			result_params_dict['section_with_main_content'] = True



		if section_with_main_content:
			special_classes_main_content = {
				"servicios": "servicios-main-content",
				"servicios extras": "servicios-main-content",
				"servicios adicionales": "servicios-main-content",
				"mis reservas": "misreservas-main-content",
				u"todo incluido": "ti-main-content",
				u"imágenes": "images-main-content",
				u"apartamentos": "apartaments-main-content",
				u"habitaciones": "rooms-main-content",
			}


			special_class_main_content = special_classes_main_content.get(section_name, "")
			result_params_dict['special_class_main_content'] = special_class_main_content


		if 'ariel' in host or 'localhost' in host:
			result_params_dict['ariel_chico'] = True
			result_params_dict['num_iters_slice'] = ":13"
		else:
			result_params_dict['num_iters_slice'] = ":12"



		if section_with_home_content:
			result_params_dict['captura_video'] = "/img/"+ base_web + "/video_inicio_corpo.jpg"

			result_params_dict["video_home_popup"] = get_section_from_section_spanish_name(u"Video popup home", language)
			result_params_dict["fotos"] = get_section_from_section_spanish_name('fotos home', language)

			result_params_dict["home_description"] = get_section_from_section_spanish_name('inicio', language)




		if section_name == u'localización y contacto':
			additionalParams4Contact={}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True
			additionalParams4Contact['privacy_checkbox'] = True
			if get_config_property_value(PUBLIC_CAPTCHA_KEY):
				additionalParams4Contact['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)

			sectionTemplate = 'secciones/contact.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			location_html=get_section_from_section_spanish_name(u"Localización", language)
			iframe_google_map=get_section_from_section_spanish_name("Iframe google maps", language)

			subtitle_form=sectionToUse['subtitle']


			result_params_dict['contact_html'] = contact_html
			result_params_dict['location_html'] = location_html
			result_params_dict['iframe_google_map'] = iframe_google_map
			result_params_dict['subtitle_form'] = subtitle_form

		elif section_name == 'ofertas':
			result_params_dict['promotions'] = get_offers(language)

		elif section_name == u'información a los socios':
			#Split
			spliter = get_pictures_from_section_name(u'información a los socios', language)
			for x in spliter:
				if x['title']:
					info = x['title'].split("@@")
					if len(info)> 1:
						x['date'] = info[1]
					x['title']  = info[0]

			result_params_dict['noticias'] = spliter
			result_params_dict['accionistas'] = True

		if user_agent_is_mobile() and advance_properties.get('whatsapp_banner'):
			mini_dict = {
				'whatsapp_mobile_id': get_config_property_value(WHATSAPP_ID)
			}
			after_block_bottom = self.buildTemplate_2("/mobile/_custom_block_mobile.html", mini_dict, False, 'gavimarindividuals')
			result_params_dict['after_block_bottom'] = after_block_bottom

		result_params_dict = dict(list(result_params_dict.items()) + list(translates_local.get(language, default_language).items()))

		return result_params_dict

	def getTemplateUrl(self, section=None):
		return thisUrl



	def getParamsForSection(self, section, language):
		result = {}
		if section['sectionType'] == 'Galeria de Imagenes':
			visita_or_video_mobile = get_section_from_section_spanish_name(u"Video popup home", language)

			result['visita_or_video_mobile'] = visita_or_video_mobile
			if (not visita_or_video_mobile):
				quita_huecos = True
				result['quita_huecos'] = quita_huecos

			result['picturesInfo'] = self.additionalInfoForPictures(section, language)


		elif section['sectionType'] == 'Habitaciones':

			result['sizeFullImgGoogleApi'] = "=s800"
			result['sizeImgGoogleApi'] = "=s400"
			result['sourceMoreImgs'] =  "<img src='/img/gavis/masinfo-rooms.png'/>"
			result['has_button_more_info'] = True
			result['elements'] = get_rooms(language)




		if result:
			return result

		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)


	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):


		templateSectionsDict = {
			'Galeria de Imagenes': 'secciones/gallerys_new/gallery_with_videos.html',
			'Habitaciones': 'secciones/rooms_new/rooms_3.html',
		}


		template = templateSectionsDict.get(sectionType, super(TemplateHandler, self).getTemplateForSectionType(sectionType, sectionTemplate))

		return template


	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):
			currentSection = self.getSectionParams(sectionFriendlyUrl, language)
			advance_properties = self.getSectionAdvanceProperties(currentSection, language)
			additionalParams['custom_elements'] = ''

			if user_agent_is_mobile():
				if advance_properties.get("minigallery"):
					mini_dict = {
						'minigallery_mobile': get_pictures_from_section_name(advance_properties.get("minigallery"), language)
					}
					mini_html = self.buildTemplate_2("mobile_templates/2/_minigallery_v1.html", mini_dict, False)
					additionalParams['custom_elements'] += mini_html


			return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)


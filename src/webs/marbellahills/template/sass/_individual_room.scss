.detailed_room_wrapper {
  margin-top: 70px;
  display: inline-block;
  width: 100%;

  .room_detail_image_wrapper {
    height: 325px;
    position: relative;
    overflow: hidden;
  }

  a.see_more_pictures_detailed {
    position: absolute;
    z-index: 1;
    bottom: 25px;
    right: 25px;
    text-transform: uppercase;
    text-decoration: none;
    color: white;

    &:hover {
      .fa {
        color: $corporate_1;

        &:after {
          left: 0;
          width: 46px;
        }
      }
    }

    span {
      display: inline-block;
      vertical-align: middle;
    }

    .fa {
      border: 2px solid white;
      position: relative;
      vertical-align: middle;
      width: 42px;
      height: 42px;
      margin-left: 10px;
      overflow: hidden;
      @include transition(color, .3s);

      &:before {
        @include center_xy;
        font-size: 21px;
        z-index: 2;
      }

      &:after {
        background: white;
        content: "";
        height: 46px;
        position: absolute;
        right: 0;
        width: 0px;
        z-index: 1;
        @include transition(width, .3s);
      }
    }
  }

  .room_details_text {
    display: block;
    width: 547px;
    margin-top: -125px;
    z-index: 2;
    position: relative;
    background: white;
    padding: 40px;
    float: left;
    box-sizing: border-box;

    &.offer_section {
      width: 777px;

      h1.room_title {
        width: 470px;
      }
    }

    &:after {
      content: "";
      width: 22px;
      height: 22px;
      border-left: 2px solid $corporate_1;
      border-bottom: 2px solid $corporate_1;
      display: inline-block;
      position: absolute;
      left: 0;
      bottom: 0;
    }

    a.button-promotion {
      background: $corporate_1;
      color: white;
      text-decoration: none;
      float: right;
      text-transform: uppercase;
      width: 205px;
      height: 65px;
      box-sizing: border-box;
      text-align: center;
      padding: 22px 0;
      position: relative;
      overflow: hidden;
      @include transition(opacity, .4s);

      span {
        position: relative;
        z-index: 2;
      }

      &:hover {
        opacity: .8;
      }
    }

    h1.room_title {
      font-size: $title_size;
      float: left;
      color: $color_title;
      width: 230px;

      &:after {
        content: '';
        display: block;
        width: 65px;
        height: 4px;
        background: $corporate_1;
        margin: 21px 0 27px;
      }
    }

    .room_description {
      margin-top: 8px;
      font-size: $description_size;
      color: $color_description;
      clear: both;
      line-height: $line_height;
    }

    #shareSocialArea {
      float: right;
    }
  }

  .minigallery_room_wrapper {
    display: inline-block;
    width: 593px;
    float: right;
    margin-top: 30px;

    .minigallery_element {
      display: inline-block;
      float: left;
      width: calc(25% - 10px);
      height: 138px;
      margin-right: 13px;
      position: relative;
      overflow: hidden;
      margin-top: 13px;

      &:nth-child(4n), &:last-child {
        margin-right: 0;
      }

      &:nth-child(-n+4) {
        margin-top: 0;
      }

      img {
        @include center_xy;
        min-width: 100%;
        min-height: 100%;
        max-width: none;
      }
    }
  }
}
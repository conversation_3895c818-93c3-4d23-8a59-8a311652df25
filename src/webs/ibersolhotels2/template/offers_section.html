<div class="offers_section_wrapper">
    {% for offer_element in offers %}
        <div class="offer_element" {% if forloop.counter|divisibleby:3 %}style="margin-right: 0;" {% endif %}>
            <div class="image_title_wrapper">
                <div class="hover_overlay"></div>
                <img class="image" src="{{ offer_element.picture }}">
                {% if offer_element.pictures_info.0.description|safe %}<div class="title_picture">{{ offer_element.pictures_info.0.description|safe }}</div>{% endif %}
            </div>
            <div class="offer_title_description_wrapper">
                <h2 class="offer_title">{{ offer_element.name|safe }}</h2>
                <div class="offer_description">{{ offer_element.description|safe }}</div>
                <div class="buttons_offer_wrapper">
                    <div href="#hidden_offer_{{ forloop.counter }}" class="more_info_button" style="display: none;">{{ T_ver_mas }}</div>
                    <a href="#data" class="button-promotion" {% if offer_element.promocode  %} data-promocode="{{ offer_element.promocode }}" {% endif %} {{ offer_element.smartDatasAttributes }}>{{ T_reservar }}</a>
                </div>
            </div>

            <div class="hidden_offer_description" id="hidden_offer_{{ forloop.counter }}" style="display: none;">
                <h2 class="offer_title">{{ offer_element.name|safe }}</h2>
                <div class="offer_description">{{ offer_element.description|safe }}</div>
            </div>
        </div>
    {% endfor %}
</div>

<script async>
    $(function(){
        $(".offer_element").each(function () {
            var offer_description_hide = $(this).find(".offer_description").find("hide");
            if (offer_description_hide.length) {
                $(this).find("hide").wrap("<div class='hidden_description_offer'></div>");
                $(this).find(".more_info_button").css('display', 'inline-block');
            }
        });

        $(".offer_element .more_info_button").click(function() {
            $(this).closest(".offer_title_description_wrapper").find(".hidden_description_offer").slideToggle();
        });
    });
</script>
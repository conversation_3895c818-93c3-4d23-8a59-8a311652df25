<div id="main-sections">
	<ul itemscope itemtype="//schema.org/SiteNavigationElement" id="main-sections-inner" class="container">
		{% for section in main_sections %}
            {% if section.sectionType == "Inicio" %}
                <li class="main-section-div-wrapper sections_menu" {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
                    <a itemprop="url" href="{{host|safe}}/{{seoLinkString}}{{section.friendlyUrl}}">
                        <i class="fa fa-home" style="font-size: 30px"></i>
                    </a>
                </li>
                {% if not forloop.last %}<li class="barra"></li>{% endif %}
                
            {% else %}
             
                <li class="main-section-div-wrapper sections_menu" {% if sectionToUse.title == section.title %}id="section-active" {% endif %}>
                    {% if section.subsections %}
                            <a>{{ section.title|safe }}</a>
                    {% else %}
        
                        {% if section.title %}
        
                        <a itemprop="url" href="{{host|safe}}/{{seoLinkString}}{{section.friendlyUrl}}" {% if section.title == "Apartamentos" %} class="big-menu" {% endif %}>
                            <span itemprop="name">{{ section.title|safe }}</span>
                        </a>
        
                        {% endif %}
        
                    {% endif %}
        
                    {% if section.subsections %}
                    <ul>
                        {% for subsection in section.subsections %}
                            <li class="main-section-subsection {{ subsection.title|lower }}">
        
                                 {% if subsection.title %}
        
        
                                    <a href="{{host|safe}}/{{seoLinkString}}{{subsection.friendlyUrl}}" {% if sectionToUse.title == subsection.title %}id="subsection-active" {% endif %}>
                                    {{ subsection.title|safe}}
                                    </a>
        
                                  {% endif %}    
        
                            </li>
                        {% endfor %}
                    </ul>
                    {% endif %}
        
        
                </li>
                {% if not forloop.last %}<li class="barra"></li>{% endif %}
            {%  endif %}
		{% endfor %}
        <li class="main-section-div-wrapper">
            <a href="#data" class="button-promotion">
                <span>{{ T_reservar }}</span>
            </a>
		</li>
	</ul>
</div>
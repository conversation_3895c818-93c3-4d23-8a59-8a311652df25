@import "compass";


//Base web (change too in templateHandler and in config.rb)
$base_web: "relaa";
$namespace: "relais-bocca-leona";

// colors definitions
$white: rgb(255,255,255);
$black: rgb(0,0,0);
$gray-1: rgb(90,90,90);
$gray-2: rgb(120,120,120);
$gray-3: rgb(190,190,190);
$gray-4: rgb(230,230,230);

// corporative colors definitions
$corporate_1: #BA9142;
$corporate_2: #8E0E1D;
$corporate_3: #585d63;

// colors for booking widget
$booking_widget_color_1: $white;//body back ground & year input text color
$booking_widget_color_2: $corporate_1;//header background & input texts
$booking_widget_color_3: gray;//label texts
$booking_widget_color_4: gray;//not used, but must be defined

@import "plugins/mixins";
@import "plugins/owlcarousel";
@import "plugins/fontawesomemin";
@import "plugins/iconmoon";

@import "booking/booking_engine_5";
@import "booking_engine";
@import "booking_widget_modal.scss";
@import "booking/selectric";
@import "fonts";

@import "template_specific";

.hidden_menu {
  #logoDiv img {
    width: 130px;
  }
}

.habitaciones_index_wrapper {
  .habitaciones_elements_relais {
    display: inline-block;
    width: 100%;

    .image_wrapper {
      display: inline-block;
      width: 50%;
      box-sizing: border-box;
      float: left;
      position: relative;
      height: 570px;
      overflow: hidden;

      .image_element {
        position: relative;
        height: 570px;
        overflow: hidden;
      }

      img.image {
        min-width: 100%;
        max-width: none;
        position: absolute;
        top: 50%;
        left: 50%;
        -moz-transform: translate(-50%, -50%);
        -o-transform: translate(-50%, -50%);
        -webkit-transform: translate(-50%, -50%);
        transform: translate(-50%, -50%);
        z-index: 1;
        max-height: 570px;
      }

      .prev_arrow {
        width: 75px;
        height: 75px;
        display: inline-block;
        background: $corporate_1 url(/img/relaa/flecha-izq.png?v=1) no-repeat center;
        cursor: pointer;
        position: absolute;
        bottom: 0;
        left: 0;
        z-index: 1;
      }

      .next_arrow {
        width: 75px;
        height: 75px;
        display: inline-block;
        background: $corporate_1 url(/img/relaa/flecha-der.png?v=1) no-repeat center;
        cursor: pointer;
        position: absolute;
        bottom: 0;
        right: 0;
        z-index: 1;
      }
    }

    .description {
      display: inline-block;
      width: 50%;
      height: 570px;
      box-sizing: border-box;
      float: right;
      position: relative;
      background: #f3f3f3;

      .center_block {
        position: absolute;
        left: 0;
        right: 0;
        padding: 0 80px;
        box-sizing: border-box;
        height: 275px;
        top: 0px;
        bottom: 75px;
        margin: auto;

        .title_room {
          color: $corporate_1;
          display: inline-block;
          margin-bottom: 15px;
        }

        .description_room {
          font-size: 13px;
          line-height: 30px;
          text-align: justify;
        }
      }

      .buttons_room_wrapper {
        position: absolute;
        width: 100%;
        bottom: 0;

        .button_info {
          background: #DBC8A0;
          float: left;
        }

        .button-promotion {
          background: $corporate_1;
          font-style: italic;
          float: right;
        }

        a {
          width: 50%;
          height: 75px;
          text-align: center;
          text-decoration: none;
          text-transform: uppercase;
          padding: 27px 0;
          color: white;
          font-size: 21px;
          box-sizing: border-box;
        }
      }
    }
  }
}

header div#logoDiv{
  height: 130px;

  img{
    height: 100%;
  }
}

.hidden_menu{
  header div#logoDiv{
    height: 60px;
    padding: 2px;

    img{
      width: auto;
      height: 100%;
    }
  }
}
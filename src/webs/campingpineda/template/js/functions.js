$(function () {

    $("#pikame").PikaChoose({showTooltips: true, carousel: false});

    $(".myFancyPopup").fancybox({
        maxWidth: 800,
        maxHeight: 600,
        fitToView: false,
        width: '70%',
        height: '70%',
        autoSize: false,
        aspectRatio: false,
        closeClick: false,
        openEffect: 'none',
        closeEffect: 'none'
    });


    $(".myFancyPopupAuto").fancybox({
        width: 650,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });

    $(".myFancyPopupVideo").fancybox({
        width: 640,
        height: 'auto',
        fitToView: false,
        autoSize: false
    });

    $(".button-promotion, .button_promotion").fancybox({
        width: 800,
        beforeLoad: function () {
            $(".room-selector").val("1");
            $(".datepicker1").val("");
            $(".datepicker2").val("");
            $(".hab2").hide();
            $(".hab3").hide();
        }
    });



    //Adding class to Main Item in Main Menu Item Menu when child are selected
    $("#subsection-active").parent().parent().parent().attr('id', 'section-active');


    if (window.PIE) {
        $(".css3").each(function () {
            PIE.attach(this);
        });
    }

    if (typeof(TAPixel) !== "undefined") {TAPixel.impressionWithReferer("001F000000vA4u0");}

    $.simpleWeather({
        location: 'Benidorm',
        woeid: '',
        unit: 'c',
        success: function (weather) {
            var html = "<div class='weather-text'><span class='weather-location'>" + 'La Pineda  ' + '</div>' +
                '<span class="icon"><img class="weather-icon" src="/static_1/images/weather/1/' + weather.code + '.png"></span> <span class="temp">' +weather.temp+'&deg; '+weather.units.temp + '</span>';
            $(".weather").html(html);
        },
        error: function (error) {
            $("#weather").html('<p>' + error + '</p>');
        }
    });


    if(navigator.userAgent.match(/Android/i)
        || navigator.userAgent.match(/webOS/i)
        || navigator.userAgent.match(/iPhone/i)
        || navigator.userAgent.match(/iPad/i)
        || navigator.userAgent.match(/iPod/i)
        || navigator.userAgent.match(/BlackBerry/i)
        || navigator.userAgent.match(/Windows Phone/i)) {

        $(".menu_subsections").click(function(){
             $(".menu_subsections ul").slideUp();

            $(this).find("ul").slideToggle();
        });

    }
    else{

        $(".menu_subsections").hover(function(){

            $(this).find("ul").slideToggle();
        }, function(){
            $(".menu_subsections ul").slideUp();
        });

    }


});

(function () {
    var po = document.createElement('script');
    po.type = 'text/javascript';
    po.async = true;
    po.src = 'https://apis.google.com/js/plusone.js';
    var s = document.getElementsByTagName('script')[0];
    s.parentNode.insertBefore(po, s);
})();


function showGallery2(elements) {
    $.fancybox(elements, {
        'prevEffect': 'none',
        'nextEffect': 'none',
        'type': 'image',
        'arrows': true,
        'nextClick': true,
        'mouseWheel': true,
        'helpers': {
            title: {
                type: 'outside'
            },
            overlay: {
                opacity: 0.8,
                css: {
                    'background-color': '#000'
                }
            },
            thumbs: {
                width: 50,
                height: 50
            }
        }
    });
}

// Can also be used with $(document).ready()
$(window).load(function() {
  $('.flexslider').flexslider({
    controlNav: false,
    directionNav: true,
    animation: "slide",
    prevText: "<img src='/img/campa/left_flexslider.png'>",
    nextText: "<img src='/img/campa/right_flexslider.png'>"
  });
});
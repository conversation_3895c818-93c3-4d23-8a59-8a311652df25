.news_wrapper {
  .entry {
    padding-bottom: 50px;
    .banner_pic, .content {
      display: inline-block;
      vertical-align: top;
      width: 50%;
    }
    .banner_pic {
      .social_widget {
        text-align: right;
        a {
          display: inline-block;
          font-size: 30px;
          color: $corporate_1;
          padding: 0 10px;
          &:hover {
            color: $corporate_2;
          }
        }
      }
      .news_widget {
        padding-left: calc((100vw - 900px) / 2);
        h3 {
          position: relative;
          text-align: right;
          text-transform: uppercase;
          color: $corporate_2;
          font-size: 20px;
          font-family: "Roboto Slab", serif;
          line-height: 20px;
          margin-bottom: 10px;
          margin-top: 30px;
          span {
            position: relative;
            background: white;
            padding-left: 20px;
          }
          &:before {
            content: '';
            @include center_y;
            left: 0;
            right: 0;
            height: 1px;
            background: $corporate_1;
          }
        }
        .news_item {
          .banner_pic, .content {
            vertical-align: middle;
          }
          .content {
            padding: 10px 20px 10px 0;
            text-align: right;
            .news_title {
              color: $corporate_1;
              font-size: 20px;
              font-family: "Roboto Slab", serif;
              line-height: 20px;
            }
            a {
              display: inline-block;
              padding: 10px 40px;
              background: $corporate_1;
              color: white;
              text-transform: uppercase;
              position: relative;
              z-index: 2;
              letter-spacing: 1px;
              font-size: 14px;
              margin-left: 10px;
              margin-top: 10px;
              &:before,
              &:after {
                content: '';
                @include full_size;
                bottom: 100%;
                z-index: -1;
                background: rgba($corporate_2,.6);
                @include transition(all,.4s);
              }
              &:after {
                content: '';
                @include full_size;
                bottom: 100%;
                background: rgba($corporate_2,.8);
                @include transition(all,.4s);
                transition-delay: .3s;
              }
              &:hover {
                &:before, &:after {
                  bottom: 0;
                }
              }
            }
          }
        }
      }
    }
    .content {
      padding: 30px 0 30px 50px;
      .banner_title {
        position: relative;
        color: $corporate_1;
        font-family: "Roboto Slab", serif;
        font-size: 50px;
        line-height: 50px;
        padding-right: calc((100vw - 1140px) / 2);
        font-weight: 300;
        padding-bottom: 30px;
        margin-bottom: 10px;
        border-bottom: 1px solid $corporate_2;
      }
      small {
        display: block;
        padding-right: calc((100vw - 1140px) / 2);
        font-size: 20px;
        font-family: "Roboto Slab", serif;
        line-height: 20px;
        margin-bottom: 20px;
        color: $corporate_2;
      }
      .desc {
        padding-right: calc((100vw - 1140px) / 2);
        color: $corporate_4;
        strong {
          font-weight: 700;
        }
      }
    }
  }
  .news {
    .news_item {
      padding: 0;
      margin: 50px auto;
      background: linear-gradient(white 50px, $corporate_5 50px, $corporate_5);
      &:nth-of-type(even) {
        .banner_pic {
          padding: 0 calc((100vw - 1140px) / 2) 30px 0;
          text-align: left;
          a {
            margin-left: 0;
            margin-right: 10px;
          }
        }
        .content {
          padding: 30px 50px 30px 0;
          .banner_title, .desc, small {
            text-align: right;
            padding-right: 0;
            padding-left: calc((100vw - 1140px) / 2);
          }
        }
      }
      .banner_pic, .content {
        display: inline-block;
        vertical-align: middle;
        width: 50%;
      }
      .banner_pic {
        text-align: right;
        padding: 0 0 30px calc((100vw - 1140px) / 2);
        a {
          display: inline-block;
          padding: 10px 40px;
          background: $corporate_1;
          color: white;
          text-transform: uppercase;
          position: relative;
          z-index: 2;
          letter-spacing: 1px;
          font-size: 20px;
          margin-left: 10px;
          &:before,
          &:after {
            content: '';
            @include full_size;
            bottom: 100%;
            z-index: -1;
            background: rgba($corporate_2,.6);
            @include transition(all,.4s);
          }
          &:after {
            content: '';
            @include full_size;
            bottom: 100%;
            background: rgba($corporate_2,.8);
            @include transition(all,.4s);
            transition-delay: .3s;
          }
          &:hover {
            &:before, &:after {
              bottom: 0;
            }
          }
        }
      }
      .content {
        padding: 30px 0 30px 50px;
        .banner_title {
          position: relative;
          color: $corporate_1;
          font-family: "Roboto Slab", serif;
          font-size: 50px;
          line-height: 50px;
          padding-right: calc((100vw - 1140px) / 2);
          font-weight: 300;
          padding-bottom: 30px;
          margin-bottom: 10px;
          border-bottom: 1px solid $corporate_2;
        }
        small {
          display: block;
          padding-right: calc((100vw - 1140px) / 2);
          font-size: 20px;
          font-family: "Roboto Slab", serif;
          line-height: 20px;
          margin-bottom: 20px;
          color: $corporate_2;
        }
        .desc {
          color: $corporate_4;
          padding-right: calc((100vw - 1140px) / 2);
          strong {
            font-weight: 700;
          }
        }
      }
    }
  }
}
  html:lang(de) {
    body {
      .news_wrapper .news .news_item .content small {
        display: none;
      }
    }
  }
  html:lang(en) {
    body {
      .news_wrapper .news .news_item .content small {
        display: none;
      }
    }
  }
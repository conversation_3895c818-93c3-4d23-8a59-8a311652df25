.banner_rooms_wrapper {
  margin: 50px 0;
  padding: 50px calc((100% - 1140px) / 2);
  background: #efefef;
  .banner_rooms {
    .room {
      .room_content {
        display: inline-block;
        vertical-align: middle;
        width: 50%;
        position: relative;
      }
      .room_pic {
        overflow: hidden;
        display: inline-block;
        vertical-align: middle;
        width: 50%;
        height: 450px;
        position: relative;
        img {
          @include center_image;
          width: auto;
        }
        .icons {
          @include center_y;
          left: 0;
          .icon {
            position: relative;
            display: block;
            width: 40px;
            height: 40px;
            margin: 10px;
            border-radius: 50%;
            background: $corporate_2;
            color: white;
            cursor: pointer;
            &:hover {
              background-color: $corporate_1;
              span.tooltip {
                opacity: 1;
                left: 60px;
              }
            }
            i.fa {
              @include center_xy;
              font-size: 20px;
            }
            span.tooltip {
              background-color: $corporate_2;
              display: block;
              white-space: nowrap;
              padding: 5px 10px;
              height: 30px;
              line-height: 20px;
              font-weight: bold;
              @include center_y;
              left: 40px;
              opacity: 0;
              @include transition(all, .6s);
              &:before {
                content: '';
                @include center_y;
                left: -30px;
                border: 15px solid transparent;
                border-color: transparent $corporate_2 transparent transparent;
              }
            }
          }
        }
      }
      .room_content {
        background: white;
        padding: 50px 50px 100px;
        text-align: center;
        h2 {
          text-align: center;
          font-size: 50px;
          line-height: 45px;
          font-weight: 300;
          padding: 30px 0;
          margin-bottom: 50px;
          small, big {
            font-family: "Satisfy", sans-serif;
            color: $corporate_1;
          }
          small {
            font-size: 40px;
          }
          big {
            font-size: 80px;
          }
        }
        .desc {
          strong {
            font-weight: bold;
          }
          .subtitle {
            position: relative;
            display: block;
            margin-top: -60px;
            margin-bottom: 60px;
            font-size: 24px;
            color: #5B5B5B;
          }
        }
        .links {
          a {
            position: relative;
            display: inline-block;
            padding: 15px 25px;
            margin: 30px 10px 0;
            background: $corporate_3;
            color: white;
            text-transform: uppercase;
            font-size: 14px;
            font-weight: bold;
            letter-spacing: 1px;
            &.button_promotion {
              background-color: $corporate_1;
            }
            i.fa {
              font-size: 20px;
              vertical-align: middle;
              margin: 0 0 0 20px;
            }
            span {
              position: relative;
              z-index: 2;
            }
            &:hover {
              &:before {
                left: 0;
                width: 100%;
              }
            }
            &:before {
              content: '';
              @include full_size;
              left: auto;
              width: 0;
              background-color: rgba(0,0,0,0.3);
              @include transition(width, .6s);
            }
          }
        }
      }
    }
    .owl-nav {
      width: 50%;
      position: absolute;
      bottom: 0;
      right: 0;
      &:after {
        content: '';
        width: 1px;
        height: 25px;
        background-color: white;
        @include center_xy;
      }
      .owl-prev, .owl-next {
        position: relative;
        display: inline-block;
        vertical-align: bottom;
        width: 50%;
        text-align: center;
        padding: 15px;
        background-color: $corporate_2;
        color: white;
        span {
          position: relative;
          z-index: 2;
        }
        i.fa {
          background-color: rgba(0,0,0,0.15);
          position: absolute;
          top: 0;
          bottom: 0;
          width: 50px;
          &:before {
            @include center_xy;
          }
        }
        &:before {
          content: '';
          @include full_size;
          top: auto;
          height: 0;
          background-color: $corporate_2;
          @include transition(height, .6s);
        }
        &:hover {
          &:before {
            height: 100%;
          }
        }
      }
      .owl-prev {
        i.fa {
          -webkit-transform: rotate(180deg);
          -moz-transform: rotate(180deg);
          -ms-transform: rotate(180deg);
          -o-transform: rotate(180deg);
          transform: rotate(180deg);
          left: 0;
        }
      }
      .owl-next {
        i.fa {
          right: 0;
        }
      }
    }
  }
}
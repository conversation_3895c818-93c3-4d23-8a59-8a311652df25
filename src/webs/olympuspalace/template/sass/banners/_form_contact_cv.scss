.contact_form_wrapper_cv {
  padding: 0 calc((100% - 1140px) / 2) 30px;
  overflow: hidden;

  .content_title {
    @include title_styles;
    margin-bottom: 30px;
  }
  #contact {
    position: relative;
    margin: auto;
    .info {
      @include display_flex;
      width: 100%;
      justify-content: center;
      align-items: center;
      position: relative;
    }
    .form_group_wrapper {
      padding-bottom: 15px;
      @include display_flex;
      width: 100%;
      justify-content: center;
      align-items: center;
      .form_title_group {
        display: block;
        width: 100%;
        text-align: center;
        font-size: 20px;
        padding-bottom: 10px;
        font-family: $title_family;
        color: $corporate_2;
      }
      .form_group {
        width: 100%;
        .title {
          display: block;
          width: 100%;
          text-align: left;
          font-size: 16px;
          padding-bottom: 10px;
          color: $corporate_2;
          border-bottom: 1px solid $corporate_2;
          margin-bottom: 10px;
        }
      }
    }
    .contInput {
      position: relative;
      display: inline-block;
      padding-top: 10px;
      width: calc((100% - 9px) / 3);
      border: none;

      &:not(:nth-of-type(3n)) {
        padding-right: 20px;
      }

      &.half_size_input {
        width: 50%;

        #file_cv {
          position: relative;
          z-index: 5;
          cursor: pointer;
          height: 48px;
          width: 50%;
          margin-left: 50%;
          padding: 15px 0;
          border-left: 0;
          border-right: 0;
          cursor: pointer;
        }
        #uploadFile {
          position: absolute;
          top: 10px;
          left: 20px;
          width: calc(100% - 20px);
          z-index: 1;
        }
      }
      &.area {
        width: 100%;
        margin-right: 0;
      }

      &:nth-of-type(3n) {
        margin-right: 0;
      }

      label, .radio_text, input[type="radio"] {
        display: inline-block;
        vertical-align: middle;
        color: $corporate_2;
      }

      input:not([type="checkbox"]):not([type="radio"]), textarea, select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: white;
        display: inline-block;
        vertical-align: middle;
        padding: 15px 25px;
        font-size: 14px;
        width: 100%;
        margin-bottom: 20px;
        border: 1px solid $grey;
        &:focus {
          outline: 3px solid $grey;
        }

        &::-webkit-input-placeholder {
          font-family: $text_family;
        }
        &:-moz-placeholder {
          font-family: $text_family;
        }
        &::-moz-placeholder {
          font-family: $text_family;
        }
        &:-ms-input-placeholder {
          font-family: $text_family;
        }
        &::placeholder {
          font-family: $text_family;
        }
      }
      textarea {
        height: 150px;
      }
      input, textarea {
        &.error {
          outline: 1px solid red;
        }
      }

      #accept-term, &#privacity, #accept-communications {
        @include checkbox_styles;
      }
    }
    .policy-terms {
      display: inline-block;
      width: auto;
      float: left;
      color: $black;
      font-size: 12px;
      margin: 20px 50px;
      .accept_communications{
        max-width: 550px;
      }
    }
    a.myFancyPopup {
      display: inline-block;
      vertical-align: middle;
      color: $black;
    }

    #contact-button {
      position: absolute;
      right: 0;
      bottom: 20px;
    }
    .g-recaptcha{
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
  .extra-policy-info{
    color: $color_text;
    font-size: 12px;
    table{
      td, th{
        border: 1px solid $color_text;
        padding: 5px;
      }
      td:first-of-type{
        width: 20%;
      }
      th{
        text-decoration: underline;
      }
      a.myFancyPopup{
        vertical-align: initial !important;
        text-decoration: underline;
      }
    }
  }
}
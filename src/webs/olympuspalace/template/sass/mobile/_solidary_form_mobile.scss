$border_color: #83bec1;
.solidary_form_wrapper {
  @include base_banner_styles;

  .contact_subtitle {
    font-family: $title_family;
    font-style: italic;
    font-size: 38px;
    font-weight: 400;
    line-height: 44px;
    color: $corporate_1;
    text-align: left;
    padding-bottom: 15px;
    display: block;
    margin: 0;
    text-align: center;
    font-style: normal;
    font-weight: 600;
  }

  #contact {
    display: table;
    margin: auto;
    .info {
      display: table;
      position: relative;
    }
    .contInput {
      display: inline-block;
      position: relative;
      width: 100%;

      input:not([type="checkbox"]), textarea, select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        background: white;
        padding: 15px 25px;
        font-size: 14px;
        border: 1px solid $border_color;
        width: 100%;
        margin-bottom: 20px;
        font-family: $text_family;
      }
      textarea {
        height: 150px;
      }
      input, textarea {
        &.error {
          outline: 1px solid red;
        }
      }

      #accept-term, &#privacity {
        width: auto;
        height: auto;
        display: inline-block;
        vertical-align: middle;
      }
    }
    .policy-terms {
      display: inline-block;
      width: auto;
      float: left;
      color: $black;
      font-size: 12px;
      margin: 20px 50px;
    }
    a.myFancyPopup {
      display: inline-block;
      vertical-align: middle;
      color: $black;
    }

    #contact-button {
      @include btn_styles;
    }
  }
}
.error{
  color: red;
}
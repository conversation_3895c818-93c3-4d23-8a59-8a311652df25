<div id="contact_form" class="contact_form_wrapper" {% if not contact_form %}style="display: none"{% endif %}>
    <div class="container12">
        <h3>{{ T_formulario_contacto }}</h3>
        <form name="contact" id="contact" method="post" action="/utils/?action=contact">
            <input type="hidden" name="action" id="action" value="contact"/>
            <div class="info">
                <input type="hidden" name="section" id="section-name" value="{{ sectionName|safe }}"/>
                <div class="contInput">
                    <input type="text" id="name" name="name" class="bordeInput" value="" placeholder="{{ T_nombre }}" required/>
                </div>
                <div class="contInput">
                    <input type="email" id="email" name="email" class="bordeInput" value="" placeholder="{{ T_email }}" required/>
                </div>
                <div class="contInput">
                    <input type="number" id="telephone" name="telephone" class="bordeInput" value="" placeholder="{{ T_telefono }}" required/>
                </div>
                <div class="contInput area">
                    <textarea type="text" id="comments" name="comments" class="bordeInput" value="" placeholder="{{ T_comentarios }}" required></textarea>
                </div>
                {% if captcha_box %}
                    <div class="contInput captcha">
                        <script src='https://www.google.com/recaptcha/api.js'></script>
                        <div class="g-recaptcha" data-sitekey="{{ captcha_box }}"></div>
                    </div>
                {% endif %}
                <div class="contInput policy-terms">
                    <div>{{ T_campos_obligatorios }}</div>
                    <input type="checkbox" id="accept-term" name="accept_term" required/>
                    <a class="myFancyPopup fancybox.iframe"
                       href="{{ language }}/?sectionContent=politica-de-privacidad.html" rel="nofollow">{{ T_lopd }}</a>
                </div>
                <button id="contact-button" onClick="return false;">{{ T_enviar }}</button>
            </div>
        </form>
    </div>
</div>

<script type="text/javascript">
    $(window).load(function () {

        $("#contact-button").click(function () {
            var form = $(this).closest("#contact");

            if (form.valid()) {
                var form_data = form.serialize();
                if ($("#g-recaptcha-response").val()) {
                $.post(
                    "/utils/?action=contact", form_data,
                    function (data) {
                        alert($.i18n._("gracias_contacto"));
                        form.trigger("reset");
                    }
                );
                } else {
                      $(".g-recaptcha > div").css('border', '1px solid red');
                    }
                }
        });

        {% if not is_mobile and not user_isIpad %}
            function contact_form_fx() {
                $(".contact_form_wrapper .container12").addnimation({parent:$(".contact_form_wrapper"),class:"fadeInUp", classOut: "fadeOutDown"});
            }
            contact_form_fx();
            $(window).scroll(contact_form_fx);
        {% endif %}
    });
</script>
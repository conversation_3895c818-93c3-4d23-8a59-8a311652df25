# -*- coding: utf-8 -*-
from collections import OrderedDict

from booking_process.constants.web_configs_names import BONO_GIFT_CUSTOM
from booking_process.constants.advance_configs_names import AUTOMATIC_POPUP_INFO, PUBLIC_CAPTCHA_KEY
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name, getPicturesForKey
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.development.dev_booking_utils import DEV
from booking_process.utils.language.language_utils import get_web_dictionary
from utils.mobile.mobile_utils import user_agent_is_mobile
from utils.mobile.sections_mobile import get_params_mobile
from booking_process.utils.namespaces.namespace_utils import get_namespace
from booking_process.utils.templates.template_utils import buildTemplate
from utils.web.content.builders.contentBuilder import content_by_config_processor
from webs.BaseTemplateHandler2 import BaseTemplateHandler2
import os

thisWeb = os.path.dirname(os.path.abspath(__file__)).split("/")[-1]
thisUrl = "%s/template/index.html"	% thisWeb

#Change this value too in default.scss and in config.rb!!
base_web = "solha"

class TemplateHandler(BaseTemplateHandler2):

	def get_name_hotel(self, language):
		host = os.environ.get('HTTP_HOST')
		name = ""
		if "brisasol" in host: #or "localhost" in host:
			name = "brisasol"
		elif "tropicalsol" in host or DEV:
			name = "tropicalsol"
		else:
			name = "vialmoura"

		return name

	def buildRoomsFromSections(self, language):
		all_rooms = get_pictures_from_section_name("_habitaciones_blocks", language)
		for room in all_rooms:
			sect_gallery = room['linkUrl']
			room['pictures'] = get_pictures_from_section_name(sect_gallery, language)
			room['name']=room['title']
		return all_rooms

	def getAdditionalParams(self, currentSectionName, language, allSections):
		sectionToUse = self.getCurrenSection(allSections)
		section_name = ''
		section_type = ''
		if sectionToUse:
			section_name = sectionToUse['sectionName'].lower().strip()
			section_type = sectionToUse['sectionType']

		#Advance properties of section
		advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

		result_params_dict = {'base_web': base_web,
							  'footer_columns': get_pictures_from_section_name("_footer_column", language),
							  'popup_slider': get_section_from_section_spanish_name('_popup_slider', language),
							  'responsive_banners': get_pictures_from_section_name('_banners_bottom', language),
							  'carousel_section': get_pictures_from_section_name('_carousel inicio', language),
							  'carousel_title': get_section_from_section_spanish_name('_carousel inicio', language),
							  'maps': get_section_from_section_spanish_name('_maps', language),
							  'bannersx3': get_pictures_from_section_name('_bannersx3', language),
							  'bannersx3_full': get_pictures_from_section_name('_bannersx3_vialmoura', language),
							  'name': self.get_name_hotel(language),
							  'booking_engine_2': self.buildSearchEngine2(language),
							  }

		#Content Access
		content_sections = {
			'Galeria de Imagenes': True,
			'Mis Reservas': True
		}

		bono_gift_custom = get_web_configuration(BONO_GIFT_CUSTOM)
		if bono_gift_custom and (section_name == bono_gift_custom.get("section").lower().strip()):
			result_params_dict['content_access'] = True
			result_params_dict['bonos_sec'] = True

		if user_agent_is_mobile():
			automatic_popup_info = get_config_property_value(AUTOMATIC_POPUP_INFO)
			if automatic_popup_info and section_type == 'Inicio':
				automatic_popup_info = automatic_popup_info.replace('section=', '')
				result_params_dict["popup_automatico"] = get_pictures_from_section_name(automatic_popup_info, language)


		if content_sections.get(section_type, False):
			result_params_dict['content_access'] = True
			result_params_dict['maps'] = False
			result_params_dict['bannersx3'] = False
			result_params_dict['full_gallery'] = False

		if section_type == u'Localización':
			result_params_dict['maps'] = False
			result_params_dict['bannersx3'] = False

		#Cycle Banners
		if advance_properties.get('banners_cycle', False):
			banners_cycle = advance_properties['banners_cycle'].split("@")
			result_params_dict['banners_cycle'] = [get_section_from_section_spanish_name(x, language) for x in banners_cycle]
			for x in result_params_dict['banners_cycle']:
				if self.getSectionAdvanceProperties(get_section_from_section_spanish_name(x.get('sectionName', ''), language), 'SPANISH').get('video', False):
					x['video'] = unescape(self.getSectionAdvanceProperties(get_section_from_section_spanish_name(x['sectionName'], language), 'SPANISH')['video'])

		actual_section = get_section_from_section_spanish_name(sectionToUse['sectionName'], language)
		if actual_section.get('subtitle', False):
			result_params_dict['normal_content'] = actual_section

		if advance_properties.get('service_icons'):
			result_params_dict['service_icons'] = self.getPicturesProperties(language, '_services_blocks', ['icon'])

			for icon in result_params_dict['service_icons']:
				icons = icon.get('icon', '').split(';')
				icon['icon_text'] = icons[0]
				if len(icons) > 1:
					icon['icon_color'] = icons[1]


		if advance_properties.get('blocks_mobile'):
			bottom_carousel_pictures = get_pictures_from_section_name('_banners_bottom', language)
			for carousel_element in bottom_carousel_pictures:
				carousel_element_properties = self.getSectionAdvanceProperties(carousel_element, language)
				if carousel_element_properties.get('button'):
					carousel_element['button'] = carousel_element_properties['button']

			result_params_dict['carousel_bottom'] = []
			result_params_dict['carousel_bottom'] = bottom_carousel_pictures


		# Habitaciones
		if section_name == 'habitaciones':
			if get_namespace() in ['brisa-sol', 'vialmoura-golf', 'tropical-sol'] or DEV:
				all_rooms = get_pictures_from_section_name('_habitaciones_blocks', language)
				for x in all_rooms:
					room_advance = self.getSectionAdvanceProperties(x, language)
					if room_advance.get('capacidad', False):
						x['capacity'] = [None] * int(room_advance['capacidad'])

					x['pictures'] = get_pictures_from_section_name(x.get('linkUrl', ''), language)
					x['services'] = get_section_from_section_spanish_name(x.get('linkUrl', ''), language).get('content', '')

				result_params_dict['rooms'] = all_rooms

			else:
				result_params_dict['rooms'] = self.getRooms(language)

		# Ofertas
		if section_type == "Ofertas":
			result_params_dict['offers'] = self.getOffers(language)

		# Golf
		if section_name == 'golf':
			result_params_dict["golf_blocks"] = get_pictures_from_section_name('golf', language)

		if section_type == u'Localización':
			result_params_dict['normal_content'] = ''
			additionalParams4Contact={}
			additionalParams4Contact['language'] = language
			additionalParams4Contact['extra'] = None
			additionalParams4Contact['picturesInSlider'] = True
			additionalParams4Contact['privacy_checkbox'] = True
			additionalParams4Contact['captcha_box'] = get_config_property_value(PUBLIC_CAPTCHA_KEY)
			locationProperties = get_config_property_value('Not required promotions in contact')
			if locationProperties:
				additionalParams4Contact['not_required_promotions'] = True
			sectionTemplate = 'secciones/contact.html'

			mySectionParams = dict(list(additionalParams4Contact.items()) + list(sectionToUse.items()) + list(get_web_dictionary(language).items()))
			mySectionParams['content'] = ''
			contact_html = self.buildTemplate(sectionTemplate, mySectionParams)

			location_html = get_section_from_section_spanish_name(u"localización", language)
			iframe_google_map = get_section_from_section_spanish_name("Iframe google maps", language)



			result_params_dict['contact_html'] = contact_html
			result_params_dict['location_html'] = location_html
			result_params_dict['iframe_google_map'] = iframe_google_map
			result_params_dict['subtitle_form'] = location_html.get('subtitle', '')
			result_params_dict['localizacion_access'] = True
			result_params_dict['location_links'] = get_pictures_from_section_name('location links', language)

		result_params_dict['full_gallery'] = True

		if section_type == u"Galeria de Imagenes":
			result_params_dict['full_gallery'] = False

		if section_name in ('ofertas', 'golf', 'entorno', u'imágenes', 'mis reservas' ) and result_params_dict['name'] == "vialmoura" :
			result_params_dict['bannersx3_full'] = False
			result_params_dict['maps'] = False

		return result_params_dict

	def getTemplateUrl(self, section=None):
		return thisUrl

	def get_revolution_initial_height(self):
		return "550"

	def get_revolution_transition(self):
		return "fade"

	def getRooms(self, language, room_section_name="_habitaciones_blocks"):
		all_sections = self.getSections(language)
		rooms = self.getPicturesProperties(language, room_section_name, ['price', 'services', 'gallery'])
		for room in rooms:
			if room.get("services"):
				room['services'] = self.servicesFiltered(room['services'], language)

			if room.get("gallery"):
				room['gallery'] = get_pictures_from_section_name(room['gallery'], language)

			if room.get("linkUrl"):
				for section in all_sections:
					if section.get("friendlyUrlInternational") == room['linkUrl']:
						pictures_section = get_pictures_from_section_name(section['sectionName'], language)
						room['gallery'] = list(filter(lambda l: not l.get("title") == "slider", pictures_section))
						break

		return rooms

	def servicesFiltered(self, list_services, language):
		list_services = list_services.split(";")
		services_icons = self.getServices(language)
		result = []

		for service_code in list_services:
			for service in services_icons:
				if service.get("code_icon") == service_code:
					result.append(service)

		return result

	def getServices(self, language):
		services_icons = self.getPicturesProperties(language, "_services_icons", ['code_icon', 'icon'])
		return services_icons


	def getOffers(self, language):
		offers = self.buildPromotionsInfo(language)
		for x in offers:
			offer_pictures = getPicturesForKey(language, str(x['offerKey']), '')
			for picture in offer_pictures:
				if picture.get("linkUrl"):
					x['linkUrl'] = picture.get("linkUrl")

		return offers

	def buildSearchEngine(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions(language, selectOptions)
		actual_namespace = get_namespace()

		if actual_namespace == 'brisa-sol':
			params['departure_date_select'] = True
			template = 'booking/booking_engine_7/_booking_widget.html'
		else:
			template = 'booking/booking_engine_3/_booking_widget.html'
		return self.buildTemplate(template, params)

	def getBookingWidgetOptions(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		return options

	def buildSearchEngine2(self, language, selectOptions=None):
		params = self.getBookingWidgetOptions2(language, selectOptions)
		return self.buildTemplate('booking/booking_engine_2/motor_busqueda.html', params)

	def getBookingWidgetOptions2(self, language, selectOptions=None):
		options = super(TemplateHandler, self).getBookingWidgetOptions(language)
		options['caption_submit_book'] = True
		return options

	def getTemplateForSectionType(self, sectionType="Normal", sectionTemplate='secciones/defaultSectionTemplate2.html'):
		if get_namespace() in ['brisa-sol', 'vialmoura-golf']:
			templateSectionsDict = {
				'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter_flexslider.html',
			}
			template = templateSectionsDict.get(sectionType,
												super(TemplateHandler, self).getTemplateForSectionType(sectionType,
																									   sectionTemplate))
		else:
			templateSectionsDict = {
				'Galeria de Imagenes': 'secciones/gallerys_new/gallery_filter.html',
			}
			template = templateSectionsDict.get(sectionType,
			                                    super(TemplateHandler, self).getTemplateForSectionType(sectionType,
			                                                                                           sectionTemplate))

		return template

	def getParamsForSection(self, section, language):
		result = {}

		if get_namespace() in ['brisa-sol', 'vialmoura-golf']:
			result = self.getParamsForSection2(section, language)

		elif section['sectionType'] == 'Galeria de Imagenes':
			result['automatic_content'] = False
			result['gallery_section'] = True
			result['filters_gallery'] = True
			result['pictures_dict'] = True
			result['old_gallery'] = True

			picture_gallery = get_pictures_from_section_name(section['sectionName'], language)
			filters = OrderedDict()
			for x in picture_gallery:

				nameFilter = x.get('title', "")
				if not nameFilter:
					nameFilter = ""

				if x.get('linkUrl', False):
					x['servingUrl'] = x['linkUrl']

				if not filters.get(nameFilter, False):
					filters[nameFilter] = [{'servingUrl': x['servingUrl'], 'description': x['description']}]
				else:
					filters[nameFilter].append({'servingUrl': x['servingUrl'], 'description': x['description']})
			result['filters_gallery'] = filters

		if result:
			return result
		else:
			return super(TemplateHandler, self).getParamsForSection(section, language)

	def getParamsForSection2(self, section, language):
		result = {}

		if section['sectionType'] == 'Galeria de Imagenes':
			result['images'] = OrderedDict()
			gallery_pictures = self.get_hotel_gallery(language)
			if not gallery_pictures:
				gallery_pictures = get_pictures_from_section_name(section.get('sectionName'), language)
			filtered_filters = filter(
				lambda x: x.get('title') and not x.get('title') in ['ico', 'slider'] and not 'youtube' in [
					x.get('description', '')], gallery_pictures)
			all_filter_together = map(lambda x: x.get('title'), filtered_filters)
			all_available_filters = []
			for filter_element in all_filter_together:
				if not filter_element in all_available_filters:
					all_available_filters.append(filter_element)
			result['filters'] = all_available_filters
			for picture_element in gallery_pictures:
				if picture_element.get('linkUrl'):
					picture_element['video'] = picture_element['linkUrl']
			gallery_pictures_filtered = list(filter(lambda x: x.get('title') != 'slider', gallery_pictures))
			slider_pictures = list(filter(lambda x: x.get('title') == 'slider', gallery_pictures))
			result['images'] = {'images_blocks': gallery_pictures_filtered}

		return result

	def buildContentForSection(self, sectionFriendlyUrl, language, sectionTemplate='secciones/defaultSectionTemplate.html', additionalParams={}):

		sectionToUse = self.getSectionParams(sectionFriendlyUrl, language)
		base_path = os.path.dirname(__file__)
		resultParams = {
			'base_web': base_web
		}


		if sectionToUse:
			name_current_section = sectionToUse['sectionName'].lower().strip()
			type_current_section = sectionToUse['sectionType']
			advance_properties = self.getSectionAdvanceProperties(sectionToUse, language)

			if user_agent_is_mobile():


				if advance_properties.get('mini_gallery', False):
					resultParams['mini_gallery'] = get_section_from_section_spanish_name(advance_properties['mini_gallery'], language)

				if advance_properties.get('service_icons', False):
					resultParams['service_icons'] = self.getPicturesProperties(language, '_services_blocks', ['icon'])

					for icon in resultParams['service_icons']:
						icons = icon.get('icon', '').split(';')
						icon['icon_text'] = icons[0]
						if len(icons) > 1:
							icon['icon_color'] = icons[1]

				if advance_properties.get('banners_cycle', False):
					events_blocks = advance_properties['banners_cycle'].split("@")
					events_sections = []
					for x in events_blocks:
						events_sections.append(get_section_from_section_spanish_name(x, language))

					resultParams['events_blocks'] = events_sections

				bono_gift_custom = get_web_configuration(BONO_GIFT_CUSTOM)
				if bono_gift_custom and (bono_gift_custom.get("section") == sectionToUse.get('sectionName')):
					section_template = 'secciones/bono_gift_custom.html'
					bono_params = self.get_bono_params(sectionToUse, language)
					bono_params.update(get_web_dictionary(language))
					resultParams['bono_gift_html'] = self.buildTemplate_2(section_template, bono_params, False)

				if type_current_section == 'Normal':
					resultParams['section_params'] = sectionToUse
					fullPath = os.path.join(base_path, 'template/mobile_version/default_template.html')
					result = dict(list(additionalParams.items()) + list(get_web_dictionary(language).items()) + list(resultParams.items()))
					return buildTemplate(fullPath, result)



		return super(TemplateHandler, self).buildContentForSection(sectionFriendlyUrl, language, sectionTemplate, additionalParams)

	def get_bono_params(self, section, language):
		bono_params = {
			'language': language
		}
		default_section_template = 'secciones/bono_gift_custom.html'
		processed_content = content_by_config_processor(section, bono_params, default_section_template, language)
		bono_params = processed_content.additional_params
		default_section_template = processed_content.section_template
		if user_agent_is_mobile():
			bono_params, default_section_template, mobile_allow = get_params_mobile(self, bono_params, section, default_section_template, language)
		return dict(list(bono_params.items()) + list(section.items()))
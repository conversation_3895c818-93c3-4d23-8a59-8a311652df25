$fontawesome5: true;
@import "plugins/fontawesome5pro";
@import "defaults";

$corporate_1: black;
$corporate_2: #c9ab44;
$corporate_3: black;
$corporate_4: #9fd015;

@import "styles_mobile/2/2";
@import "general_mobile";
@import "mobile/banner_bubbles_mobile";
@import "banner_club";
@import "banner_links";
@import "banner_videos";
@import "plugins/animations";
//@import "plugins/fancybox_3_2_5";

body:not(.inner_section) .whatsapp_button {
  bottom: auto!important;
  top: 130px;
  right: 15px;
  z-index: 99 !important;
}

.mobile_engine {
  &.open {
    height: 305px;
  }
  .mobile_engine_action {
    background-color: $corporate_4;
  }
}

.mobile_engine.open.has_web_support {
  z-index: 21;
  height: 345px;
  .mobile_engine_action {
    bottom: 345px;
  }
  #full_wrapper_booking {
    .wrapper_booking_button {
      .submit_button {
        background: $corporate_4;
      }
    }
    .wrapper-new-web-support {
      display: none;
    }
    .wrapper-new-web-support.booking_form_title {
      top: 142%;
      text-align: center;
      display: block;
      a {
        color: white;
      }
    }
  }
}

.main_menu {
  background-color: $corporate_4;
}

@media (max-width: 1000px) {
  .banner_bubbles {
    .bubble {
      margin: 10px auto;
      cursor: pointer;
    }
  }
  .banner_icons_wrapper {
    .banner_icons {
      h2 {
        position: relative;
        z-index: 3;
        font-size: 20px;
        font-family: $font1;
        letter-spacing: 10px;
        color: $corporate_4;
        text-transform: uppercase;
        text-align: left;
        margin-top: 50px;
        padding: 0 20px;
        &:before {
          content: '';
          @include center_y;
          left: 0;
          width: 100%;
          height: 2px;
          background: linear-gradient(to left, transparent, rgba($corporate_1, .3), transparent);
        }
      }
      .icons {
        .icon {
          padding: 0 20px;
          text-align: center;
          img {
            width: auto;
            margin: auto;
          }
        }
      }
    }
  }
  .banner_videos_wrapper {
    padding: 30px 0;
    h2 {
      font-size: 30px;
      line-height: 30px;
      letter-spacing: 2px;
      padding: 0 30px;
      text-align: left;
      small {
        font-size: 20px;
        letter-spacing: 2px;
      }
      &:before {
        bottom: 15px;
      }
    }
    .banner_videos {
      margin-top: 0;
      padding: 30px 0;
      .banner_videos_content {
        width: 100%;
        height: auto;
        margin-top: 0;
        img, iframe {
          width: 100%;
        }
      }
      .banner_videos_list {
        width: 100%;
        padding: 0 20px;
        box-sizing: border-box;
        span {
          font-size: 14px;
        }
        &:before {
          right: 40px;
        }
      }
    }
  }
  .banner_club_wrapper {
    h2 {
      font-size: 30px;
      line-height: 30px;
      letter-spacing: 2px;
      padding: 0 30px;
      small {
        font-size: 20px;
        letter-spacing: 2px;
      }
      &:before {
        bottom: 15px;
      }
    }
    .banner_club {
      margin-top: 0;
      padding: 30px 0;
      .banner_club_image, .banner_club_content {
        width: 100%;
        box-sizing: border-box;
        padding: 0 20px;
        font-size: 14px;
        line-height: 20px;
        a.btn {
          padding: 20px 30px;
          text-align: center;
        }
      }
    }
  }
  .banner_links_wrapper {
    h2 {
      font-size: 30px;
      line-height: 30px;
      text-align: left;
      letter-spacing: 2px;
      padding: 30px 20px 0;
      small {
        font-size: 20px;
        letter-spacing: 2px;
        color: $corporate_4;
      }
      &:before {
        bottom: 15px;
      }
    }
    .banner_links {
      margin-top: 0;
      padding: 30px 20px;
      a {
        width: 100%;
        font-size: 18px;
        padding: 5px 0;
      }
    }
  }
}

.button_download {
  display: inline-block;
  padding: 10px 40px;
  background-color: #ce6c5e;
  color: white;
  border-radius: 40px;
  text-transform: uppercase;
  letter-spacing: 5px;
  font-family: $font1;
}

.owl-carousel {
  .owl-nav {
    .owl-next, .owl-prev {
      @include center_y;
      color: white;
      cursor: pointer;
      font-size: 25px;
    }
    .owl-prev {
      right: 90%;
    }
    .owl-next {
      left: 90%;
    }
  }
  .owl-item {
    a {
      span {
        position: absolute;
        bottom: 10%;
        @include center_x;
        background: rgba(0, 0, 0, 0.5);
        width: 100%;
        padding: 1em 0;
        color: $corporate_2;
      }
    }
  }
}

.banner_bubbles {
  .bubble {
    .content {
      h2 {
        font-size: 20px;
      }
    }
  }
}

.rooms_wrapper {
  .room_block {
    .room_picture {
      img {
        zoom: 50%;
      }
    }
  }
}

.owl-carousel {
  &.promotions_wrapper {
    .owl-nav {
      .owl-next, .owl-prev {
        display: none;
      }
    }
  }
}


@import "banner_list_content";
.banner_list_content_wrapper{
  padding: 50px 0;
  .banner_list_content_title {
      margin-bottom: 20px;
    i {
      display: block;
      width: 100%;
      text-align: center;
    }
  }
  .banner_list_content {
    .banner_list_content_list, .banner_list_content_content {
      display: block;
      width: 100%;
      box-sizing: border-box;
      padding: 10px 20px;
    }
    .banner_list_content_content {
      .banner {
        padding: 5px 0;
      }
    }
  }
    .banner_list_content_bottom {
    padding: 0 20px;
  }
}

.whatsapp_button {
  background: #00bb2d;
  position: fixed;
  width: 50px;
  height: 50px;
  border-radius: 50%;

  i {
    color: white;
    font-size: 30px;
    @include center_xy;
    padding-left: 2px
  }
}
.normal_section_mobile .section-content {
  img {
    max-width: 100%;
  }
}
.banner_service_logo{
  img {
    max-width: 100%;
  }
}

.banner_icons_content.owl-carousel, .logos_group_wrapper {
  img {
    display: inherit !important;
    max-width: 50%;
  }
  span {
    display: none;
  }

  .owl-nav {
    .owl-prev, .owl-next {
      background: none;

      &:before {
        content: '';
        position: absolute;
        display: block;
        top: 50%;
        left: 50%;
        width: 30px;
        height: 30px;
        -webkit-transform: translate(-50%, -50%) rotate(45deg);
        -moz-transform: translate(-50%, -50%) rotate(45deg);
        -ms-transform: translate(-50%, -50%) rotate(45deg);
        -o-transform: translate(-50%, -50%) rotate(45deg);
        transform: translate(-50%, -50%) rotate(45deg);

      }
    }

    .owl-prev {
      &:before {
        margin-left: 5px;
        border-left: 1px solid $corporate_1;
        border-bottom: 1px solid $corporate_1;
      }
    }

    .owl-next {
      float: right;

      &:before {
        margin-left: -5px;
        border-right: 1px solid $corporate_1;
        border-top: 1px solid $corporate_1;
      }
    }
  }
}
.logos_group_wrapper{
  margin-top: 25px;
}
.newsletter_wrapper {
  clear: both;
  background-attachment: fixed;
  overflow-x: hidden;
  .newsletter_container {
    padding: 100px 0;
    .newsletter_title {
      text-align: center;
      font-size: 38px;
      font-weight: 100;
      letter-spacing: 5px;
      color: $corporate_3;
      text-transform: uppercase;
    }
    .newsletter_form {
      padding-top: 50px;
      text-align: center;
      .input_email {
        padding: 20px;
        border: 1px solid $corporate_4;
        background: white;
        margin: 0 30px 50px 20px;
        font-size: 20px;
        font-family: $font2;
        color: $corporate_4;
        border-radius: 50px;
        text-align: center;
        text-transform: uppercase;
        width: 80%;
        outline: none;
        &::-webkit-input-placeholder {
          color: $corporate_4;
        }
        &::-moz-placeholder {
          color: $corporate_4;
        }
        &:-ms-input-placeholder {
          color: $corporate_4;
        }
        &:-moz-placeholder {
          color: $corporate_4;
        }
      }
      .button_newsletter {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        display: inline-block;
        padding: 20px;
        background: $corporate_3;
        color: white;
        margin: 0 30px 50px 20px;
        font-size: 20px;
        font-family: $font2;
        border-radius: 50px;
        text-align: center;
        text-transform: uppercase;
        width: 80%;
        cursor: pointer;
        &:hover {
          background: $corporate_1;
        }
      }
      .check_newsletter {
        display: inline-block;
        padding: 0 30px 5px 20px;
        color: #666;
        font-size: 12px;
        font-family: "Raleway", sans-serif;
        input[type=checkbox] {
          position: relative;
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          background: white;
          border: 1px solid #4b4b4b;
          border-radius: 50%;
          width: 16px;
          height: 16px;
          display: inline-block;
          vertical-align: middle;
          outline: none;
          &:before {
            content: '';
            @include center_xy;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: white;
          }
          &:checked:before {
            background: $corporate_4;
          }
        }
        a {
          color: #666;
          &:hover {
            text-decoration: underline;
          }
        }
      }
    }
  }
}
.carousel_logos{
  img {
    width: auto !important;
  }
}

.contact_form_wrapper.effects_sass{
  display: none;
}
body {
  font-family: "Lato";
  * {
    box-sizing: border-box;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
  }
  a {
    text-decoration: none;
  }
  strong {
    font-weight: 700;
  }
  .aviso_cookie {
    position: fixed;
    top: auto;
    bottom: 10px;
    right: 10px;
    width: 530px;
    height: auto;
    padding: 20px 30px;
    background: rgba(0, 0, 0, 0.8);
    p {
      padding: 0;
      text-align: left;
      line-height: 20px;
    }
  }

  #slider_container {
    .overlay {
      @include full_size;
      background: linear-gradient(to right, transparent, rgba(black, .3));
    }

    .cartela_slider {
      color: white;
      text-align: center;
      .text1 {
        font-family: $text_family;
        font-size: 22px;
        white-space: nowrap;
        text-shadow: 1px 0px 6px rgba(0, 0, 0, 0.4);
      }
      .text2 {
        font-family: $title_family;
        font-size: 48px;
        white-space: nowrap;
        line-height: 84px;
        text-shadow: 1px 0px 6px rgba(0, 0, 0, 0.4);
      }
      .btn {
        position: relative;
        z-index: 5;
        display: inline-block;
        vertical-align: middle;
        text-transform: uppercase;
        white-space: nowrap;
        padding: 15px 105px 15px 25px;
        margin-left: 10px;
        color: white;
        background: transparent;
        border-width: 0;
        letter-spacing: 1px;
        cursor: pointer;
        font-size: 18px;
        font-weight: 700;
        text-shadow: 1px 0px 6px rgba(0, 0, 0, 0.4);
        @include transition(opacity, .6s);
        @extend .icon-longarrow;
        &:before {
          display: block;
          @include center_y;
          right: 25px;
          font-family: "icomoon", sans-serif;
          color: $corporate_3;
          font-size: 20px;
          text-shadow: 0 0 20px rgba(0,0,0,0.8);
        }
        &:after {
          content: '';
          @include center_y;
          position: absolute;
          left: 0;
          z-index: -1;
          width: 70px;
          height: 70px;
          background-color: rgba($corporate_1, .8);
          border-radius: 50px;
          @include transition(width, .6s);
        }
        &:hover {
          color: white;
          &:after {
            width: 100%;
          }
        }
      }
    }
  }

  .inner_slider {
    width: 100%;
    height: 400px;
    position: relative;
    overflow: hidden;
    img {
      @include center_image;
    }
  }

  .content_subtitle_wrapper {
    margin-top: 50px;
    text-align: center;
    padding-bottom: 50px;
    .content_subtitle_title {
      @include title_style;
      padding-bottom: 50px;
    }
    .content_subtitle_description {
      font-family: $text_family;
      font-size: 17px;
      line-height: 26px;
      font-weight: 400;
      padding: 0 70px;
      color: $corporate_1;
    }
  }

  .content_access {
    .gallery-image {
      display: none;
    }
  }

  .banner_ventajas {
    @include center_x;
    top: 154px;
    text-align: center;
    overflow: hidden;
    background-color: rgba($corporate_1, .8);
    color: white;
    border-bottom-left-radius: 15px;
    border-bottom-right-radius: 15px;
    z-index: 100;
    .banner_ventajas_ticks {
      max-height: 0;
      overflow: hidden;
      @include transition(all, .6s);
      .tick {
        padding: 10px 40px;
        i {
          font-size: 32px;
          display: inline-block;
          vertical-align: middle;
          margin-right: 10px;
        }
        span {
          display: inline-block;
          vertical-align: middle;
          font-size: 13px;
        }
        &:last-child {
          padding-bottom: 20px;
        }
      }
    }
    .banner_ventajas_cta {
      display: block;
      font-size: 13px;
      font-weight: 700;
      letter-spacing: 1px;
      padding: 10px 40px;
       i {
          font-weight: 300;
          margin-left: 5px;
        }
    }
    &:hover {
      .banner_ventajas_ticks {
        max-height: 400px;
      }
    }
  }

  /* Mis reservas */

  #my-bookings-form {
    #reservation {
      .modify_reservation_widget {
        margin: auto;
        margin-top: 40px;
        margin-bottom: 0;
        padding: 20px;

        #info_ninos {
          display: none !important;
        }

        #contenedor_opciones {
          margin: 0 auto 10px;

          .ninos-con-babies {
            margin-right: 15px;
          }
        }

        #contenedor_fechas {
          text-align: center;

          #fecha_entrada, #fecha_salida {
            display: inline-block;
            float: none;
          }
        }

        #contenedor_habitaciones {
          text-align: center;

          label {
            display: inline-block;
            float: none;
          }

          select {
            display: inline-block;
            float: none;
          }
        }

        #envio {
          text-align: center;
        }
      }

      .my-bookings-booking-info {
        margin: 40px auto 0;

        .fResumenReserva {
          margin: auto;
        }
      }
    }
    #modify-button-container {
      display: none;
    }

    #my-bookings-form-fields {
      label {
        display: block;
        text-align: center;
        text-transform: uppercase;
        color: #4B4B4B;
        font-weight: 100;
      }

      input, select {
        -webkit-appearance: none;
        -moz-appearance: none;
        appearance: none;
        display: block;
        width: 300px;
        margin: 10px auto;
        height: 40px;
        border-radius: 0;
        text-align: center;
        font-size: 14px;
        border: 1px solid #DDD;
      }

      select {
        padding: 0 0 0 15px;
      }

      ul {
        text-align: center;
        margin-top: 30px;

        li {
          display: inline-block;
          width: 200px;
          vertical-align: middle;

          button {
            height: 40px;
            text-transform: uppercase;
            font-size: 16px;
            color: white;
            border: 0;
            cursor: pointer;
            width: 100%;
            font-weight: 100;
            background: $corporate_1;
            @include transition(background, .4s);

            &.modify-reservation {
              background: $corporate_1;

              &:hover {
                background: darken($corporate_1, 10%);
              }
            }

            &.searchForReservation {
              background: $corporate_2;

              &:hover {
                background: darken($corporate_3, 10%);
              }
            }
          }
        }
      }
    }

    #cancelButton {
      display: none;
      background: $corporate_2;
      height: 40px;
      text-transform: uppercase;
      font-size: 16px;
      color: white;
      border: 0;
      cursor: pointer;
      width: 200px;
      font-weight: 100;
      margin: 40px auto 0;
      @include transition(background, .4s);

      &:hover {
        background: darken($corporate_3, 10%);
      }
    }
  }

  .iframe_map {
    iframe {
      width: 100%;
    }
  }

  /* Covid-19 */

  .covid__wrapper {
    .covid__item {
      margin-bottom: 30px;

      p {
        margin-bottom: 10px;
      }
      h3 {
        font-size: 20px;
        font-weight: 700;
        letter-spacing: 2px;
      }
      .covid__list {
        margin-top: 30px;

        .covid__list-item {
          margin-bottom: 10px;
        }
      }
      .covid__button {
        border-radius: 10px;
      }
    }
  }

}

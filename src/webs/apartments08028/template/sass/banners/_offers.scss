.section_offers_wrapper {
    padding: 40px 20px;
}

.banner_offers {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: auto;
    grid-gap: 40px;
    
    .offer {
        @include bg_pattern(.7);
        position: relative;
        box-shadow: $light_shadow;
        &:hover {
            .offer_image {
                &:before {
                    opacity: 1;
                }
                
                .center_xy {
                    transform: scale(1);
                    
                    span {
                        opacity: 1;
                    }
                }
            }
        }
        
        .picture_wrapper {
            height: 250px;
            overflow: hidden;
            .offer_hotel_name {
                position: absolute;
                background: rgba(0, 0, 0, 0.5);
                color: white;
                width: 100%;
                text-align: center;
                padding: 20px;
                font-size: 18px;
            }
            .picture_description {
                position: absolute;
                top: 30px;
                right: 30px;
                width: 60px;
                height: 60px;
                border-radius: 50%;
                display: flex;
                justify-content: center;
                align-items: center;
                text-align: center;
                background-color: $corporate_1;
                
                span {
                    color: white;
                    font-size: 12px;
                    
                    strong {
                        display: block
                    }
                }
            }
        }
        
        .content_wrapper {
            text-align: center;
            padding: 40px 20px 80px;
            position: relative;
            
            .icon_wrapper {
                position: relative;
                background: #E5D69A;
                width: 50px;
                height: 50px;
                border-radius: 50%;
                position: absolute;
                top: 0;
                left: 50%;
                transform: translate(-50%, -50%);
                z-index: 1;
                
                i {
                    color: white;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 30px;
                }
            }
            
            .content_title {
                margin-bottom: 20px;
                
                .title {
                    font-size: 21px;
                    line-height: 28px;
                }
            }
            
            .desc {
                margin-bottom: 20px;
            }
        }
        
        .btn_wrapper {
            position: absolute;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: $corporate_1;
            color: white;
            
            &.btn_half {
                .button_promotion {
                    &:before {
                        display: none;
                    }
                }
                .btn {
                    padding: 30px 30px;
                    display: inline-block;
                    width: 50%;
                }
            }
            
            
            .btn {
                display: block;
            }
        }
    }
}

///To check
.offers_filter {
    margin-bottom: 40px;
    
    .filter_selector,
    .filter_remove {
        position: relative;
        display: inline-block;
        font-size: 16px;
        border: 1px solid $black;
    }
    
    .filter_selector {
        padding: 10px 10px 10px 40px;
        width: 350px;
        
        span {
            font-family: $primary_font;
            text-transform: capitalize;
            color: $corporate_2;
        }
        
        &:nth-child(1), &:nth-child(2) {
            display: none;
        }
        
        &:before {
            font-family: "icomoon";
            content: "\e9c9";
            font-size: 25px;
            @include center_y;
            color: $corporate_2;
            left: 10px;
            right: auto;
        }

         &:after {
            font-family: "font awesome 5 pro";
            content: "\f107";
            font-size: 22px;
            @include center_y;
            color: $corporate_2;
            right: 20px;
            left: auto;
        }
    }
    
    .filter_remove {
        position: relative;
        float: right;
        padding: 10px 30px 10px 10px;
        width: 150px;
        color: #DFE2E3;
        border: 1px solid #DFE2E3;
        
        &:before {
            position: absolute;
            font-family: "Font Awesome 5 Pro";
            content: "\f00d";
            color: DFE2E3;
            right: 10px;
        }
        
        &.active {
            border: none;
            background-color: $corporate_1;
            color: white;
            
            &:before {
                color: white;
            }
        }
    }
    
    .filter_list_wrapper {
        margin-top: 10px;
        
        .filter_list {
            display: none;
            padding: 5px 0;
            background-color: $lightgrey;
            margin: 2px 0;
            
            .filter_element {
                position: relative;
                display: inline-block;
                font-size: 14px;
                padding: 5px 10px 5px 35px;
                
                &.active {
                    
                    &:before {
                        font-family: "Font Awesome 5 Pro";
                        content: "\f00c";
                        font-weight: 700;
                        color: #cdacc5;
                        position: absolute;
                        top: 6px;
                        bottom: 0;
                        left: 12px;
                        right: 0;
                    }
                }
                
                i.fa {
                    display: inline-block;
                    vertical-align: middle;
                    color: $corporate_2;
                    margin-right: 5px;
                    font-size: 150%;
                }
                
                &:after {
                    content: '';
                    display: block;
                    @include center_y;
                    left: 10px;
                    border: 1px solid #999;
                    width: 13px;
                    height: 13px;
                }
            }
        }
    }
}

#contact_offer_blocks {
    &.active {
        display: block;
    }

    @include full_size;
    position: fixed;
    z-index: 1101;
    background: rgba($corporate_2, .8);

    .close_form {
      position: absolute;
      right: 40px;
      top: 40px;
      cursor: pointer;
      i {
        font-size: 40px;
        color: white;
      }
    }

    .popup_wrapper {
      @include display_flex;
      @include center_xy;
      flex-flow: column;
      width: 80%;
      background: white;
      margin: auto;
      align-items: center;
      justify-content: center;
      padding: 20px 20px 30px;
    }

    #contact {
      min-width: 50%;
      .info {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: auto;
        grid-gap: 20px;
        position: relative;
      }

      .contInput {
        position: relative;
        margin: 10px 0;

        &.area {
          grid-column: 1 / span 3;
        }

        input:not([type="checkbox"]) {
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;
          display: block;
          height: 40px;
          border-radius: 0;
          font-size: 14px;
          width: 100%;
          border: solid 2px darken($lightgrey, 2%);
          box-shadow: none;
        }

        textarea {
          border-radius: 0;
          font-size: 14px;
          width: 100%;
          border: solid 2px darken($lightgrey, 2%);
          box-shadow: none;
          min-height: 150px;
        }

        input,
        textarea {
          padding: 15px;
          font-family: $secondary_font;

          &.error {
            outline: 1px solid red;
          }
        }

        #accept-term,
        &#privacity {
          width: auto;
          height: auto;
          display: inline-block;
          vertical-align: middle;
        }

        input[type= "checkbox" ] {
          -webkit-appearance: none;
          border: solid 2px $corporate_2;
          border-radius: 0;
          height: 15px;
          width: 15px;
          vertical-align: middle;
          padding: 5px;

          &:checked {
            background-color: $corporate_1;
          }
        }
      }
      .policy-terms {
        display: inline-block;
        width: auto;
        color: $black;
        font-size: 12px;
        margin-bottom: 20px;
      }

      a.myFancyPopup {
        display: inline-block;
        vertical-align: middle;
        color: $black;
      }
      .send_button_element {
        display: block;
      }
  }
}



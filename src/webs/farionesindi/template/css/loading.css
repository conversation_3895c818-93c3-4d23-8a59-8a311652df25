@import url("https://fonts.googleapis.com/css?family=Caveat:400,700|Poppins:300,400,600,700&display=swap");
/* line 3, ../../../../sass/plugins/_mixins.scss */
.center_xy {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 8, ../../../../sass/plugins/_mixins.scss */
.center_xy_before:before {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

/* line 13, ../../../../sass/plugins/_mixins.scss */
.center_x {
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
}

/* line 17, ../../../../sass/plugins/_mixins.scss */
.center_y {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 21, ../../../../sass/plugins/_mixins.scss */
.center_y_before:before {
  position: absolute;
  top: 50%;
  -webkit-transform: translate(0%, -50%);
  -moz-transform: translate(0%, -50%);
  -ms-transform: translate(0%, -50%);
  -o-transform: translate(0%, -50%);
  transform: translate(0%, -50%);
}

/* line 25, ../../../../sass/plugins/_mixins.scss */
.center_image {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  min-width: 100%;
  min-height: 100%;
  max-width: none;
}

/* line 30, ../../../../sass/plugins/_mixins.scss */
.fs:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
}

/* line 110, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:before, .icon-xcross:after {
  content: '';
  width: 100%;
  height: 2px;
  background: white;
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%) rotate(45deg);
  -moz-transform: translate(-50%, -50%) rotate(45deg);
  -ms-transform: translate(-50%, -50%) rotate(45deg);
  -o-transform: translate(-50%, -50%) rotate(45deg);
  transform: translate(-50%, -50%) rotate(45deg);
}
/* line 124, ../../../../sass/plugins/_only_mixins.scss */
.icon-xcross:after {
  -webkit-transform: translate(-50%, -50%) rotate(-45deg);
  -moz-transform: translate(-50%, -50%) rotate(-45deg);
  -ms-transform: translate(-50%, -50%) rotate(-45deg);
  -o-transform: translate(-50%, -50%) rotate(-45deg);
  transform: translate(-50%, -50%) rotate(-45deg);
}

@-webkit-keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
@keyframes sk-stretchdelay {
  0%, 40%, 100% {
    transform: scaleY(0.4);
  }
  20% {
    transform: scaleY(1);
  }
}
/* line 61, ../../../../sass/plugins/_mixins.scss */
.display_flex {
  display: -webkit-box;
  display: -moz-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-wrap: wrap;
  -moz-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  -o-flex-wrap: wrap;
  flex-wrap: wrap;
}

/* line 1, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown {
  margin: 40px 20px 20px;
  text-align: center;
  position: absolute;
  left: 50%;
  -webkit-transform: translate(-50%, 0%);
  -moz-transform: translate(-50%, 0%);
  -ms-transform: translate(-50%, 0%);
  -o-transform: translate(-50%, 0%);
  transform: translate(-50%, 0%);
  bottom: 180px;
  left: 48% !important;
  color: white;
}
/* line 9, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown div {
  display: inline-block;
  font-size: 16px;
  list-style-type: none;
  padding-left: 5px;
  padding-right: 5px;
  padding-top: 0;
  padding-bottom: 0;
  text-transform: uppercase;
}
/* line 20, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .days {
  font-weight: 600;
}
/* line 23, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .date {
  display: block;
  font-size: 4.5rem;
}
/* line 28, ../../../../sass/plugins/_slider_countdown.scss */
.slider_countdown .title_format {
  font-weight: 600;
}

@media screen and (max-width: 800px) {
  /* line 35, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown {
    display: inline-block;
    width: 380px;
    top: 330px;
    left: 45% !important;
  }
  /* line 40, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown div {
    font-size: 10px;
  }
  /* line 43, ../../../../sass/plugins/_slider_countdown.scss */
  .slider_countdown .date {
    font-size: 2.5rem;
  }
}
/* line 3, ../sass/loading.scss */
.loading_site, .white_overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10000;
  position: fixed;
  background-position: center;
  background-repeat: no-repeat;
  background-color: white;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  overflow: hidden !important;
}
/* line 14, ../sass/loading.scss */
.loading_site:before, .white_overlay:before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}
/* line 19, ../sass/loading.scss */
.loading_site .loading_content, .white_overlay .loading_content {
  position: absolute;
  top: 50%;
  left: 50%;
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  -o-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
  top: 35% !important;
}
/* line 22, ../sass/loading.scss */
.loading_site .loading_content h2, .white_overlay .loading_content h2 {
  font-family: "Montserrat", sans-serif;
  font-size: 20px;
  letter-spacing: 3px;
  font-weight: lighter;
  color: white;
}

/* line 32, ../sass/loading.scss */
.white_overlay {
  background: #002f6c !important;
}
/* line 34, ../sass/loading.scss */
.white_overlay:before {
  background: #002f6c;
}

/* line 39, ../sass/loading.scss */
.sk-cube-grid {
  width: 40px;
  height: 40px;
  margin: 18px auto;
  transform: translateY(1300%);
}

/* line 47, ../sass/loading.scss */
.sk-cube-grid .sk-cube {
  width: 33%;
  height: 33%;
  background-color: #002f6c;
  float: left;
  -webkit-animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
  animation: sk-cubeGridScaleDelay 1.3s infinite ease-in-out;
}

/* line 56, ../sass/loading.scss */
.sk-cube-grid .sk-cube1 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

/* line 61, ../sass/loading.scss */
.sk-cube-grid .sk-cube2 {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

/* line 66, ../sass/loading.scss */
.sk-cube-grid .sk-cube3 {
  -webkit-animation-delay: 0.4s;
  animation-delay: 0.4s;
}

/* line 71, ../sass/loading.scss */
.sk-cube-grid .sk-cube4 {
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

/* line 76, ../sass/loading.scss */
.sk-cube-grid .sk-cube5 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

/* line 81, ../sass/loading.scss */
.sk-cube-grid .sk-cube6 {
  -webkit-animation-delay: 0.3s;
  animation-delay: 0.3s;
}

/* line 86, ../sass/loading.scss */
.sk-cube-grid .sk-cube7 {
  -webkit-animation-delay: 0s;
  animation-delay: 0s;
}

/* line 91, ../sass/loading.scss */
.sk-cube-grid .sk-cube8 {
  -webkit-animation-delay: 0.1s;
  animation-delay: 0.1s;
}

/* line 96, ../sass/loading.scss */
.sk-cube-grid .sk-cube9 {
  -webkit-animation-delay: 0.2s;
  animation-delay: 0.2s;
}

@-webkit-keyframes sk-cubeGridScaleDelay {
  0%, 70%, 100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }
  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}
@keyframes sk-cubeGridScaleDelay {
  0%, 70%, 100% {
    -webkit-transform: scale3D(1, 1, 1);
    transform: scale3D(1, 1, 1);
  }
  35% {
    -webkit-transform: scale3D(0, 0, 1);
    transform: scale3D(0, 0, 1);
  }
}

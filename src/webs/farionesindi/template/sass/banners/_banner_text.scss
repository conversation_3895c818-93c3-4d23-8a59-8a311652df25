.banner_text_wrapper {
  position: relative;
  overflow: hidden;
  margin-bottom: 120px;

  .text_wrapper {
    width: 75%;
    text-align: center;
    position: relative;
    z-index: 4;
    padding-top: 30px;
    padding-right: 0;
    box-sizing: content-box;
    margin: 0 auto;
  }
  .content_wrapper {
    font-size: 14px;
    line-height: 25px;
    color: $dark_gray;
    letter-spacing: 0.05em;

    em {
      font-style: italic;
    }

    hide {
      max-height: 0;
      display: block;
      overflow: hidden;
      @include transition(all, 1s);

      &.visible {
        max-height: 800px;
      }
    }
    .see_more {
      margin-top: 30px;
      font-family: $montserrat;
      font-size: 14px;
      color: $corporate_2;
      cursor: pointer;
      letter-spacing: 0.04em;

      .less_text {
        display: none;
      }

      i {
        @include transition(all, .2s);
      }

      &.active {
        .more_text {
          display: none;
        }

        i {
          @include transform(rotate(180deg));
        }

        .less_text {
          display: inline-block;
        }
      }
    }

    strong {
      font-weight: inherit;
      position: relative;
      background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxNiA0MyI+PGRlZnM+PHN0eWxlPi5jbHMtMXtmaWxsOiM2OWIzZTc7b3BhY2l0eTowLjI7fTwvc3R5bGU+PC9kZWZzPjx0aXRsZT5SZWN1cnNvIDI8L3RpdGxlPjxnIGlkPSJDYXBhXzIiIGRhdGEtbmFtZT0iQ2FwYSAyIj48ZyBpZD0iQ2FwYV8xLTIiIGRhdGEtbmFtZT0iQ2FwYSAxIj48cmVjdCBjbGFzcz0iY2xzLTEiIHdpZHRoPSIxNiIgaGVpZ2h0PSI0MyIvPjwvZz48L2c+PC9zdmc+");
      background-position-y: 10px;
      background-repeat: repeat-x;
      padding: 0 2px 2px;
      margin: 0 -2px -2px;
    }
  }

  .background_image_container {

    .first_image {
      position: relative;
      width: 300px;
      height: 335px;
      float: right;
      z-index: 0;
      cursor: pointer;
      &:before {
        content: '';
        position: absolute;
        top: 0;
        bottom: 0;
        height: 120%;
        right: 100%;
        width: 135%;
        @include background-image(linear-gradient(left, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.8) 100%));
        z-index: 2;
      }

      &:after {
        content: '';
        position: absolute;
        top: 0;
        height: 120%;
        bottom: 0;
        left: 100%;
        width: 150%;
        background: rgba(255, 255, 255, 0.8);
        z-index: 2;
      }

      .zoom_icon {
        position: absolute;
        top: 10px;
        right: 15px;
        z-index: 2;
        color: white;
        pointer-events: none;
        font-size: 24px;
        @include transition(all, .25s);
      }

      &:hover {
        .zoom_icon {
          right: 13px;
          top: 8px;
          font-size: 30px;
        }
      }
      img {
        top: 60% !important;
        left: 20% !important;
        margin: auto;
        max-width: none;
        max-height: 400px;
        @include center_image;
      }

      .bottom_left_bg, .bottom_right_bg, .top_left_bg, .top_right_bg, .top_bg, .bottom_bg, .right_bg, .left_bg {
        position: absolute;
        bottom: -100px;
        right: 100%;
        width: 200%;
        height: 50%;
        @include background-image(linear-gradient(bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%));
        z-index: 2;
      }

      .bottom_right_bg {
        left: 100%;
        right: auto;
        @include background-image(linear-gradient(to top, rgba(255, 255, 255, 1) 30%, rgba(255, 255, 255, 0) 100%));
      }
      .bottom_bg {
        left: 0;
        right: 0;
        width: 100%;
        height: 25%;
        @include background-image(linear-gradient(to top, rgba(255, 255, 255, 1) 30%, rgba(255, 255, 255, 0) 100%));
        &:before {
          content: '';
          position: absolute;
          bottom: 0;;
          left: 0;
          right: 0;
          width: 100%;
          height: 100%;
          @include background-image(linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0.8) 100%));
          z-index: 2;
        }
      }
      .left_bg {
        bottom: auto;
        right: 100%;
        height: 120%;
        top: 0;
        @include background-image(linear-gradient(to right, rgba(255, 255, 255, 1) 40%, rgba(255, 255, 255, 0) 100%));
      }
      .right_bg {
        bottom: auto;
        left: 100%;
        height: 120%;
        top: 0;
        @include background-image(linear-gradient(to left, rgba(255, 255, 255, 1) 90%, rgba(255, 255, 255, 0) 100%));
      }
      .top_left_bg {
        bottom: auto;
        top: 0;
        @include background-image(linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%));
      }
      .top_right_bg {
        bottom: auto;
        top: 0;
        @include background-image(linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 100%));
      }

      .top_bg {
        bottom: 100%;
        background: white;
        left: -250%;
        right: -250%;
        width: 530%;
      }
      .top_right_bg {
        left: 100%;
        right: auto;
      }
    }
    .secondary_image {
      position: relative;
      width: 300px;
      height: 335px;
      margin-top: 160px;
      float: right;
      margin-right: 20px;
      cursor: pointer;
      overflow: hidden;

      .zoom_icon {
        position: absolute;
        top: 10px;
        right: 15px;
        pointer-events: none;
        z-index: 2;
        color: white;
        font-size: 24px;
        @include transition(all, .25s);
      }

      img {
        top: 60% !important;
        left: 60% !important;
        margin: auto;
        max-width: none;
        max-height: 400px;
        @include center_image;
      }

      &:hover {
        .zoom_icon {
          right: 13px;
          top: 8px;
          font-size: 30px;
        }
      }
    }
  }
}
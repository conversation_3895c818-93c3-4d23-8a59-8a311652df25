$fontawesome5: true;

@import "defaults";
@import "styles_mobile/2/2";

#full_wrapper_booking {
  .wrapper_booking_button {
    .submit_button {
      background-color: $corporate_2;
    }
  }
}

h3, h4 {
  margin: 0;
}

.btn_personalized {
  @include btn_styles;
}

.button-promotion {
  background-color: $corporate_2 !important;
}

.section_content {
  .location_content .section-title {
    display: none;
  }

  > h1, .content_subtitle_title, .section_title, .location_content .section-subtitle {
    @include title_styles;
  }

  div.content, div.content_subtitle_description, .section-content, .contact_content_element {
    @include text_styles;
    width: auto;
    padding: 0 20px;
    .hide {
      max-height: 0;
      overflow: hidden;
      @include transition(all,.1s);
      &.active {
        max-height: 1000px;
      }
    }
  }
}

.normal_section_mobile .section_title {
  margin-top: 20px !important;
}

/* Banner icons */

.banner_icons_full_wrapper {
  position: relative;
  padding: 20px;
  overflow: visible;

  .banner_icons_content {
    text-align: center;

    .content_title {
      @include title_styles;
      padding-bottom: 30px;
    }

    .content_text {
      @include text_styles;
      padding-bottom: 30px;
    }
  }

  .banner_icons_wrapper {
    display: flex;
    flex-wrap: wrap;
    width: 100%;

    .banner_icon {
      display: inline-block;
      text-align: center;
      width: 50% !important;
      margin-bottom: 20px;
      position: relative;
      &:hover {
         .text hide {
          float: left;
          opacity: 1;
          max-height: 200px;
          overflow: visible;
        }
      }

      .icon {
        position: relative;
        display: inline-block;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background-color: rgba($corporate_1, .3);

        i {
          @include center_xy;
          font-size: 44px;
          color: $corporate_2;
        }
      }

      .text {
        display: block;
        text-align: center;
        font-size: 16px;
        padding-top: 10px;
        line-height: 18px;
        color: $corporate_2;
        font-weight: 700;

        .subtitle {
          display: block;
          font-weight: 300;
        }
        hide {
          position: absolute;
          top: 100%;
          left: 0;
          z-index: 999;
          right: 0;
          display: block !important;
          background-color: rgba(0, 0, 0, 0.6);
          color: white;
          font-weight: lighter;
          padding: 14px;
          opacity: 0;
          -webkit-transition: opacity 0.5s, max-height 0.5s;
          -moz-transition: opacity 0.5s, max-height 0.5s;
          -ms-transition: opacity 0.5s, max-height 0.5s;
          -o-transition: opacity 0.5s, max-height 0.5s;
          transition: opacity 0.5s, max-height 0.5s;
          max-height: 0;
          overflow: visible;

          &:after {
            content: "";
            position: absolute;
            bottom: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: transparent transparent #555 transparent;
          }
        }
      }
    }
  }
}

/* Banner images */

.banner_images_full_wrapper {
  position: relative;
  padding: 30px 20px;
  overflow: hidden;
  background-color: $grey;

  .banner_images_wrapper {
    position: relative;
    width: 100%;

    .banner_images_content {
      position: relative;
      z-index: 10;
      .icon_svg {
        position: absolute;
        left: 50%;
        top: 50px;
        opacity: .6;
        z-index: -1;
        svg {
          width: 200px;
          height: 200px;
          * {
            fill:white;
          }
        }
      }
      .content_title {
        @include title_styles;
        padding-bottom: 30px;
        padding-left: 0;
        text-align: left;
      }

      .content_text {
        @include text_styles;
        padding-bottom: 30px;
        text-align: left;

        .hide {
          max-height: 0;
          visibility: hidden;
          @include transition(all, .8s);

          &.active {
            max-height: 800px;
            visibility: visible;
          }
        }
      }
    }
  }

  &.with_images {
    .banner_images {
      .image_wrapper {
        width: 100%;

        .image {
          position: relative;
          overflow: hidden;
          z-index: 1;
          width: 100%;
          height: 250px;
          margin-bottom: 20px;

          img {
            @include center_image;
          }
        }
      }
    }
  }
}

/* Banner block */

.banner_block_full_wrapper {
  position: relative;
  padding: 20px;
  overflow: hidden;

  .banner_block_content {
    text-align: center;

    .content_title {
      @include title_styles;
      padding-bottom: 30px;
    }

    .content_text {
      @include text_styles;
      padding-bottom: 30px;
    }
  }

  .banner_block_wrapper {
    text-align: center;

    .banner_block{
      .image{
        position: relative;
        width: 370px;
        height: 280px;
        display: inline-block;
        overflow: hidden;

        img {
          @include center_image;
        }
      }
        .title{
          padding: 20px 45px;
          font-size: 18px;
          color: $corporate_2;
          font-weight: 700;
        }

        .text{
          @include text_styles;
          padding-bottom: 55px;

          .hide_in_banner_block {
            display: none !important;
          }
          .section_text_hide {
            display: none !important;
          }
        }

      .buttons_wrapper {
        position: relative;
        left: 0;
        right: 0;
        bottom: 0;

        a {
          @include btn_styles;
          width: 100%;
        }

        .button-promotion {
          i {
            display: none;
          }
        }

        &.half_btn {
          a {
            width: 50%;
            float: left;
          }
        }
      }
    }
  }

  .owl-nav {
    @include center_xy;
    height: 0;
    width: 100%;
    top: 315px;

    .owl-prev, .owl-next {
      @include center_y;
      left: 0;
      right: auto;
      height: 50px;
      width: 50px;
      border-radius: 50%;
      cursor: pointer;
      @include transition(all, .6s);

      i {
        @include center_xy;
        color: $corporate_2;
        top: 30px;
        font-size: 38px;
      }

      &:hover {
        i {
          color: $corporate_1;
        }
      }
    }

    .owl-next {
      right: 0;
      left: auto;
    }
  }
}

/* Rooms section */
.rooms_wrapper .room_block {
  .room_description .description {
    display: none !important;
  }
}

/* Banner carousel */

.banner_carousel_full_wrapper {
  position: relative;
  padding: 20px 0;
  overflow: hidden;
  background-color: $grey;

  .banner_carousel_content {
    text-align: center;

    .content_title {
      @include title_styles;
      padding-bottom: 30px;
    }

    .content_text {
      @include text_styles;
      padding-bottom: 30px;
    }
  }

  .banner_carousel_wrapper {
    .banner_carousel {
      position: relative;
      width: 100%;

      .banner_carousel_text {
        padding: 0 20px 50px;
        background-color: $corporate_2;

        .title {
          position: relative;
          font-family: $title_family;
          color: white;
          font-size: 34px;
          text-align: center;
          padding: 40px 20px;

          .icon {
            @include center_xy;
            font-size: 72px;
            color: rgba($corporate_1, .2);
          }
        }

        .text {
          @include text_styles;
          color: white;
          padding: 0 20px 50px;
        }

        .button_wrapper {
          text-align: center;

          a {
            @include btn_styles;
          }
        }
      }

      .image {
        position: relative;
        display: inline-block;
        width: 100%;
        height: 220px;
        overflow: hidden;

        i {
          display: none;
        }

        img {
          @include center_image;
        }
      }
    }

    .owl-nav {
      @include center_x;
      bottom: 230px;

      .owl-prev, .owl-next {
        display: inline-block;
        vertical-align: middle;

        i {
          color: white;
          font-size: 42px;
        }

        &.disabled {
          cursor: default;

          i {
            color: rgba(white, .4);
          }
        }
      }

      .owl-next {
        margin-left: 20px;
      }
    }
  }
}

/* Banner offers */

.banner_offers_full_wrapper {
  position: relative;
  padding: 20px;
  overflow: hidden;

  .banner_offers_content {
    text-align: center;

    .content_title {
      @include title_styles;
      padding-bottom: 30px;
    }

    .content_text {
      @include text_styles;
      padding-bottom: 30px;
    }
  }

  .banner_offers_wrapper {
    text-align: center;

    .banner_offer {
      .image {
        position: relative;
        width: 370px;
        height: 280px;
        display: inline-block;
        overflow: hidden;

        img {
          @include center_image;
        }
      }

      .content {
        display: block;
        vertical-align: top;
        color: white;

        .title {
          padding: 20px;
          font-size: 18px;
          color: $corporate_2;
          font-weight: 700;
        }

        .text {
          @include text_styles;
          padding-bottom: 80px;
          .hide_banner_home {
            display: none;
          }
        }
      }

      .buttons_wrapper {
        position: relative;
        left: 0;
        right: 0;
        bottom: 0;

        a {
          @include btn_styles;
          width: 100%;
        }

        .button-promotion {
          i {
            display: none;
          }
        }

        &.half_btn {
          a {
            width: 50%;
            float: left;
          }
        }
      }
    }
  }

  .owl-nav {
    @include center_xy;
    height: 0;
    width: 100%;
    top: 330px;

    .owl-prev, .owl-next {
      @include center_y;
      left: 0;
      right: auto;
      height: 50px;
      width: 50px;
      border-radius: 50%;
      cursor: pointer;
      @include transition(all, .6s);

      i {
        @include center_xy;
        color: $corporate_2;
        top: 30px;
        font-size: 38px;
      }

      &:hover {
        i {
          color: $corporate_1;
        }
      }
    }

    .owl-next {
      right: 0;
      left: auto;
    }
  }
}

/* Banner info */

.banner_info_full_wrapper {
  position: relative;
  padding: 20px;
  overflow: hidden;
  background-color: $grey;

  .banner_info_content {
    .content_title {
      @include title_styles;
      text-align: left;
      padding-bottom: 30px;
    }

    .content_text {
      @include text_styles;
      text-align: left;

      .btn_personalized {
        margin-top: 40px;
      }
    }
  }

  .banner_info_wrapper {
    padding-top: 60px;

    .banner_info {
      position: relative;
      padding-top: 100px;

      .image {
        position: absolute;
        width: 80%;
        height: 300px;
        overflow: hidden;
        left: 10%;
        top: 0;

        img {
          @include center_image;
        }
      }

      .banner_text {
        position: relative;
        padding: 10px 20px;
        width: 60%;
        background-color: $corporate_2;

        .title {
          font-family: $title_family;
          color: white;
          font-size: 32px;
          line-height: 26px;
          padding-bottom: 20px;

          .subtitle {
            display: block;
            color: $corporate_1;
            font-family: $text_family;
            font-size: 16px;
          }
        }

        .text {
          @include text_styles;
          color: white;
          text-align: left;
          padding-bottom: 20px;
        }

        .button_wrapper {
          margin-top: 10px;
          text-align: center;

          a {
            @include btn_styles;
            background-color: transparent;
            border: 1px solid white;
          }
        }
      }
    }

    .owl-nav {
      position: absolute;
      display: inline-block;
      right: 0px;
      bottom: 0;

      .owl-prev, .owl-next {
        display: inline-block;
        vertical-align: middle;

        i {
          font-size: 42px;
          color: $corporate_2;
          @include transition(color, .6s);
        }

        &.disabled {
          cursor: default;

          i {
            color: rgba($corporate_2, .4);
          }
        }
      }

      .owl-next {
        margin-left: 20px;
      }
    }
  }
}


/* Cycle Banner */

.cycle_banner_wrapper {
  position: relative;
  overflow: hidden;

  .cycle_banner {
    position: relative;
    display: block;
    padding-bottom: 30px;
    width: 100%;

    .pics_summary_text {
      display: inline-block;
      vertical-align: top;
      width: 40%;
      box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);

      .pics_image {
        position: relative;
        display: block;
        width: 100%;
        height: 270px;
        overflow: hidden;

        img {
          @include center_image;
        }

        .lupa {
          position: absolute;
          z-index: 10;
          top: 15px;
          left: 15px;

          i {
            font-size: 20px;
            color: white;
          }
        }
      }

      .pics_summary_text {
        position: relative;
        z-index: 1;
        @include text_styles;
        padding: 20px 10px;
        text-align: left;
        background-color: white;

        .description {
          display: none;
        }

        svg {
          @include center_xy;
          z-index: -1;
          width: 160px;

          * {
            fill: rgba($corporate_2, .3);
          }
        }
      }

      .button_promotion {
        @include btn_styles;
        width: 100%;
      }
    }

    .pics_content {
      position: relative;
      z-index: 0;
      display: inline-block;
      vertical-align: top;
      width: 60%;

      .title {
        font-size: 32px;
        color: $corporate_2;
        font-family: $title_family;
      }

      .pics_text {
        @include text_styles;
        text-align: left;

        .summary {
          display: none;
        }
      }

      .icon_svg {
        @include svg_styles;
        right: 80px;
        top: 30px;
      }
    }

    &:nth-of-type(even) {
      flex-direction: row-reverse;

      .pics_content {
        .icon_svg {
          svg {
            * {
              fill: $grey;
            }
          }
        }
      }
    }

    &:nth-of-type(odd) {
      background-color: $grey;
    }
  }
}
@import "banners/banner_recon";
.banner_logos_wrapper {
  &:before, &:after {
    width: calc(100% - 40px);
    margin: auto;
  }
  .banner_logos {
    .owl-nav {
      .owl-prev {
        margin-left: 10px;
      }
      .owl-next {
        margin-right: 10px;
      }
    }
    .logo img {
      filter: grayscale(0%);
      -webkit-transform: scale(1.3);
      -moz-transform: scale(1.3);
      -ms-transform: scale(1.3);
      -o-transform: scale(1.3);
      transform: scale(1.3);
    }
  }
}
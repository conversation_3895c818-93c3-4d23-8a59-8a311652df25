<div class="rooms_wrapper">
    <a class="button-promotion" href="#data"
       {% if rooms_individual.smartdateini %}data-smartdateini="{{ rooms_individual.smartdateini|safe }}"{% endif %}
       {% if rooms_individual.smartdatefin %}data-smartdatefin="{{ rooms_individual.smartdatefin|safe }}"{% endif %}>
        <span>{{ T_reservar }}</span>
    </a>

    {% if rooms_individual.gallery %}
        <div class="minigallery_room_wrapper container12">
            <div class="minigallery_big">
                <ul class="slides">
                    {% for x in rooms_individual.gallery %}
                        <li class="big_element" data-thumb="{{ x.servingUrl|safe }}=s242-c">
                            <img src="{{ x.servingUrl|safe }}=s700" class="center_image"/>
                        </li>
                    {% endfor %}
                </ul>
            </div>
        </div>

        <script>
            $(function () {
                $(".minigallery_big").flexslider({
                                                animation: "slide",
                                                controlNav: "thumbnails",
                                                directionNav: true,
                                                prevText: "<div class='prev_arrow'><img src='/img/{{ base_web }}/flex-arrow-left.png?v=2'/></div>",
                                                nextText: "<div class='next_arrow'><img src='/img/{{ base_web }}/flex-arrow-right.png?v=2'/></div>"
                                            })
            })
        </script>
    {% endif %}

    {% if rooms_individual.services %}
        <div class="paralax_block_wrapper"
             {% if rooms_individual.paralax %}style="background: url({{ rooms_individual.paralax.0.servingUrl|safe }}=s1900);background-attachment: fixed !important;background-size: cover"{% endif %}>
            <div class="paralax_content container12">
                {% for x in rooms_individual.services %}
                    <div class="paralax_element">
                        <div class="center_blocks center_y">
                            {% if x.title %}
                                <i class="fa {{ x.title|safe }}"></i>
                            {% else %}
                                <img src="{{ x.servingUrl|safe }}">
                            {% endif %}
                            <div class="paralax_text">
                                {{ x.description|safe }}
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}

    {% if rooms_individual.list_rooms %}
        <div class="list_rooms_wrapper container12">
            <div class="list_rooms_content owl-carousel">
                {% for x in rooms_individual.list_rooms %}
                    <div class="room_element">
                        <div class="room_image">
                            <img src="{{ x.servingUrl|safe }}" class="center_image"/>
                        </div>

                        <div class="room_content">
                            <div class="room_title">{{ x.title|safe }}</div>
                            <div class="room_links">
                                <div class="see_room"><a href="{{ x.linkUrl|safe }}">{{ T_ver_habitaciones }}</a></div>
                                <div class="booking_button"><a class="button-promotion" href="#data">{{ T_reservar }}</a></div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>

    <script>
        $(window).load(function () {
            owl_params = {
                loop: true,
                nav: true,
                dots: false,
                items: 1,
                navText: ['<i class="fa fa-angle-left" aria-hidden="true"></i>', '<i class="fa fa-angle-right" aria-hidden="true"></i>'],
                margin: 5,
                autoplay: true,
                autoplayTimeout: 10000
            };
            $(".list_rooms_content").owlCarousel(owl_params);
        })
    </script>
    {% endif %}
</div>
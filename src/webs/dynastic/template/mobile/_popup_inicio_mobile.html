{#<script>#}
{#    {% if mobile %}#}
{#        function searchCookie(key){#}
{##}
{#           var encontrado = -1;#}
{#           var x = document.cookie;#}
{#           if (x) {#}
{#                var y = x.split(";");#}
{#                for (var i=0;i< y.length; i++) {#}
{#                    encontrado = y[i].search(key);#}
{#                    if (encontrado > -1) {#}
{#                        resultado = y[i].split("=");#}
{#                        return resultado[1];#}
{#                    }#}
{#                }#}
{#           }#}
{#        }#}
{#    {% endif %}#}
{#    {% if popup_inicio_automatico %}#}
{#        $(document).ready(function () {#}
{#            {% if cookies_on_popup_inicio_automatico %}#}
{##}
{#                window.setTimeout(function () {#}
{#                    if (searchCookie("anuncio_fancy_{{ language }}")) {#}
{#                    }#}
{#                    else {#}
{#                        $.fancybox.open($(".popup_inicio"), {wrapCSS: 'popup-start'});#}
{#                        document.cookie = "anuncio_fancy_{{ language }}=1"#}
{#                    }#}
{#                }, 800);#}
{##}
{#            {%  else %}#}
{##}
{#                window.setTimeout(function() {#}
{#                    $.fancybox.open($(".popup_inicio"), {wrapCSS : 'popup-start'});#}
{#                },800);#}
{##}
{#            {% endif %}#}
{#        });#}
{#    {% endif %}#}
{#</script>#}
{#{% if popup_inicio_automatico %}#}
{#    {% if popup_inicio_automatico.0.servingUrl %}#}
{#        <div style="display:none">#}
{#            <div class="popup_inicio" style="position:relative;display:table;">#}
{#                <a {% if popup_inicio_automatico.0.linkUrl %} href="{{ popup_inicio_automatico.0.linkUrl }}"{% endif %}>#}
{#                    <img src="{{ popup_inicio_automatico.0.servingUrl }}=s850">#}
{#                </a>#}
{#            </div>#}
{#        </div>#}
{#    {% endif %}#}
{#{% endif %}#}

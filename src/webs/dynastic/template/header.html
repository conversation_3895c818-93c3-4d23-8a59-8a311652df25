<header {% if extra_top_header %}class="with_top_header"{% endif %}>
{% if extra_top_header %}
    <div class="extra_top_header">
        {% if extra_top_header.0.custom_link or extra_top_header.0.linkUrl %}
            <a href="{{ extra_top_header.0.custom_link|safe if extra_top_header.0.custom_link else extra_top_header.0.linkUrl|safe }}" target="_blank">
        {% endif %}
        {% if extra_top_header.0.icon %}
            <i class="{{ extra_top_header.0.icon|safe }}"></i>
        {% endif %}
        {% if extra_top_header.0.description %}
            <p class="extra_top_header_desc">{{ extra_top_header.0.description|safe }}</p>
        {% endif %}
        {% if extra_top_header.0.custom_link or extra_top_header.0.linkUrl %}</a>{% endif %}
    </div>
    <script>
    $(document).ready(function () {
        $('body, body.inner_section #slider_container, body.inner_section #full_wrapper_booking').addClass('with_top_header')
    });
    </script>
{% endif %}
    <div id="wrapper-header" class="container12">


        <div id="logoDiv" class="column3">

            <div class="header_info">
                {{header_info.content|safe}}
            </div>
            <a href="{{host|safe}}/">
                <img itemprop="logo" src="{{ logotype }}" alt="{{ hotel_name|safe }}" title="{{ hotel_name|safe }}"/>
            </a>
        </div>
        <div class="right-header column9">

            <div id="social">
                {%if facebook_id %}
                    <a href="http://www.facebook.com/{{facebook_id}}" target="_blank">
                        <i class="fa fa-facebook" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if twitter_id %}
                    <a href="https://twitter.com/#!/{{twitter_id}}" target="_blank">
                        <i class="fa fa-twitter" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if google_plus_id %}
                    <a href="https://plus.google.com/u/0/{{google_plus_id}}" target="_blank" rel="publisher">
                        <i class="fa fa-google-plus" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if youtube_id %}
                    <a href="https://www.youtube.com/{{youtube_id}}" target="_blank">
                        <i class="fa fa-youtube" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if pinterest_id %}
                    <a href="http://es.pinterest.com/{{ pinterest_id }}" target="_blank">
                        <i class="fa fa-pinterest-p" aria-hidden="true"></i>
                    </a>
                {% endif %}
                {% if instagram_id %}
                    <a href="http://www.instagram.com/{{ instagram_id }}" target="_blank">
                        <i class="fa fa-instagram" aria-hidden="true"></i>
                    </a>
                {% endif %}
            </div>

            <div id="top-sections">
                {% for section in top_sections %}
                    <a href="{{ host|safe }}/{{ section.friendlyUrl }}">
                        {% if section.icon %}
                            {% if section.icon.0.description %}
                                <i class="fa {{ section.icon.0.description|safe }}"></i>
                            {% else %}
                                <img src="{{ section.icon.0.servingUrl|safe }}"/>
                            {% endif %}
                            <span>{{ section.title|safe }}</span>
                        {% endif %}
                    </a>
                {% endfor %}
            </div>

            <div id="lang">
                <div class="lang_selected"><img src="/static_1/images/flags/flag_{{ language }}.png"/></div>
                <div class="lang_options_wrapper">
                    {% for key, value in language_codes.items() %}
                        {% if key != language %}
                        <a class="lang_option" href="{{ hostWithoutLanguage }}/{{ key }}/">
                            <img src="/static_1/images/flags/flag_{{ key }}.png"/>
                        </a>
                        {% endif %}
                    {% endfor %}
                </div>
            </div>


            <nav id="main_menu">
                <div id="mainMenuDiv" class="container12">
                    {% include "main_div.html" %}
                </div>
            </nav>

        </div>
    </div>
</header>
# -*- coding: utf-8 -*-
import os

from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name_with_properties, \
    get_sections_from_type
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.language.language_utils import get_web_dictionary
from utils.web.BaseInjectionHandler import InjectionScript, InjectionWidgetHandler


class q10widget(InjectionWidgetHandler):

    def getBookingWidgetOptions(self, language, selectOptions=None):
        options = super(q10widget, self).getBookingWidgetOptions(language)

        widget_config = get_section_from_section_spanish_name_with_properties("_widget_config", language)
        if widget_config:
            if widget_config.get('extra_widget_class'):
                options['ExtraElementBeforeRoomList'] = '<input type="hidden" id="extra_widget_class" value="%s"/>' % widget_config['extra_widget_class']

            widget_pics = get_pictures_from_section_name("_widget_config", language)
            filter_options = [x for x in widget_pics if x.get('title') == 'filter_option']
            options['filter_options'] = []
            for option in filter_options:
                option.update(get_properties_for_entity(option.get('key'), language))
                options['filter_options'].append(option)

        options['custom_hotel_selector_placeholder'] = get_web_dictionary(language).get('T_hotel_selector_placeholder')
        options['hotels'] = self.get_hotels(language)
        all_namespaces = ''
        for hotel in options['hotels']:
            all_namespaces += hotel['namespace'] + ';'

        options['hotels_list_html'] = self.buildTemplate_2("q10/_hotel_selector.html", options, False)

        options['custom_promocode_label'] = get_web_dictionary(language).get('T_promocode')
        options['inline_ages'] = False
        options['namespace'] = ''

        return options


    def get_hotels(self, language):
        hotels_list = get_sections_from_type(u"Hotel Individual", language)
        result_hotels_list = []
        application_ids = []
        for hotel_section in hotels_list:
            hotel = {}
            hotel.update(self.getSectionAdvanceProperties(hotel_section, language))
            hotel['title'] = hotel_section.get('title')
            hotel['namespace'] = hotel.get("namespace")
            hotel['destiny'] = hotel.get("destiny")

            if hotel.get("namespace") and hotel.get("url_booking"):
                hotel['url_booking'] = "https://%s%s" % (hotel['namespace'], hotel['url_booking'])
                if not hotel.get('corpo'):
                    application_ids.append(hotel['namespace'])

            result_hotels_list.append(hotel)
        result_hotels_list.sort(key=lambda k: int(k.get('order', 99999)))

        return result_hotels_list


class q10Script(InjectionScript):

    def params_base_script_controller(self):
        context = {
            "widget_url": "q10widget",
            "widget_css": "q10-corpo",
            "static_version": "1.07",
            "booking_version": "7",
            "calendar_version": "5"
        }

        return context

    def template_controller_name(self):
        return "q10_injection.js"

    def template_controller_path(self):
        return os.path.join(os.path.dirname(__file__), self.template_controller_name())


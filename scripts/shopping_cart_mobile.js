shopping_cart_room_cookie = [];

var shoppingCart = function () {
    return {
        configs: {
            header_height: '',
            hidden_cart_selector: $("#hidden_header_shopping_cart"),
            shopping_cart_cookie_name: 'cart_rooms_info'
        },

        init: function () {
            this.check_hidden_header();
            this.restore_rooms_from_cookie();
            this.set_click_listeners();
            this.check_screen_height();

            var actual_path = window.location.pathname;
            if (!(actual_path.indexOf('booking1') > -1)) {
                $("#hidden_header_shopping_cart").find(".finish_room_selection").remove();
            }

            if (actual_path.indexOf('booking4') > -1) {
                this.clean_cookies();
            }
        },

        update: function () {
            this.recalculate_rooms_number();
            this.recalculate_total_prices();
        },

        get_number_of_rooms_in_cart: function(selected_room_name) {
            var number_founded = 0;

            for(var i=0; i < shopping_cart_room_cookie.length; i++) {
                if(shopping_cart_room_cookie[i].room_name === selected_room_name) {
                    number_founded++;
                }
            }

            return number_founded;
        },

        add_room: function(selected_room) {
            var selected_uuid = selected_room.attr('uuid'),
                room_name_selected = selected_room.closest(".result_list").prev().find(".room_title").html(),
                rooms_available = selected_room.closest('.result_list').data("availability");

            console.log(selected_room);
            console.log(selected_uuid);

            if (shoppingCart.get_number_of_rooms_in_cart(room_name_selected) >= parseInt(rooms_available)) {
                return false;
            }

            var selected_button = selected_room.find(".add_room_icon");
            this.add_button_animation(selected_button);

            //Option information
            if (selected_room.hasClass("perform_package_booking_search")) {
                this.build_package_info(selected_room.closest(".package_element_wrapper"), selected_uuid);
            } else {
                var regimen_wrapper = selected_room,
                regimen_name = regimen_wrapper.find(".room_list_name").html().trim(),
                selected_price = regimen_wrapper.find(".precio_final.currencyValue");

                if (selected_price.length) {
                    selected_price = selected_price.html().trim();
                } else {
                    selected_price = regimen_wrapper.find(".precioTotal .currencyValue").html().trim();
                }

                var room_occupancy = this.retreive_occupancy(selected_room),
                    rate_wrapper = regimen_wrapper.closest(".rate_wrapper_element"),
                    rate_name = rate_wrapper.find(".rate_name").html(),
                    room_wrapper = rate_wrapper.closest(".result_list").prev(),
                    room_name = room_wrapper.find(".room_title").html(),
                    random_identifier = Math.floor(Math.random() * 500);

                this.build_room_info(room_name, selected_price, rate_name, regimen_name, room_occupancy, random_identifier, selected_uuid, false);

                var prepared_cookie = {
                    room_name: room_name,
                    rate_name: rate_name,
                    regimen_name: regimen_name,
                    room_price: selected_price,
                    occupancy_string: room_occupancy,
                    random_identifier: random_identifier,
                    uuid: selected_uuid,
                    is_additional_service: false
                };

                shopping_cart_room_cookie.push(prepared_cookie);
            }

            this.update();
        },

        build_package_info: function(package_element, package_uuid) {
            var package_info = {
                room_name: package_element.find(".package_select").val(),
                rate_name: package_element.find(".package_rate_select").val(),
                regimen_name: package_element.find(".package_regimen_select").val(),
                room_price: package_element.find(".since_prices_values .currencyValue").html(),
                occupancy_string: "",
                random_identifier: Math.floor(Math.random() * 500),
                uuid: package_uuid,
                rate_key: "",
                is_additional_service: false,
                unique_permanent_id: "",
                is_package: "true"
            };

            shopping_cart_room_cookie.push(package_info);

            var baseline_room_card = $("#room_cart_baseline").clone();
            baseline_room_card.attr('id', '');

            baseline_room_card.find(".room_name").html(package_info.room_name);
            baseline_room_card.attr('uuid', package_uuid);
            baseline_room_card.find('.selected_rate').html(package_info.rate_name);
            baseline_room_card.find(".rate_conditions").attr('rate_key', package_info.rate_key);
            baseline_room_card.find('.selected_regimen').html(package_info.regimen_name);
            baseline_room_card.find('.selected_price .currencyValue').html(package_info.room_price);
            baseline_room_card.attr('price', package_info.room_price);
            baseline_room_card.attr('ran_id', package_info.random_identifier);
            baseline_room_card.find('.occupancy_value').html(package_info.occupancy_string);

            var actual_path = window.location.pathname;
            if (!(actual_path.indexOf('booking1') > -1)) {
                baseline_room_card.addClass('hide_remove_button');
            }

            baseline_room_card.css('display', 'block');
            var second_baseline = baseline_room_card.clone();

            $("#hidden_header_shopping_cart").find(".selected_rooms_summary").append(baseline_room_card);
            $(".booking-3-price--partial").append(second_baseline);
        },

        add_button_animation: function (selected_button) {
            selected_button.addClass('added_animation');

            setTimeout(function () {
                selected_button.addClass('transparent');
            }, 500);

            setTimeout(function () {
                selected_button.removeClass('added_animation');
            }, 1100);

            setTimeout(function () {
                selected_button.removeClass('transparent');
            }, 1600);
        },

        retreive_occupancy: function (selected_button) {
            var selected_occupancy = selected_button.closest('.rate_wrapper_element').attr('occupancy');

            selected_occupancy = selected_occupancy.split("-");

            //Labels
            var adults_label = $("#adult_title_label").val(),
                kids_label = $("#kids_title_label").val(),
                babies_label = $("#babies_title_label").val();

            //Default Values
            var adults_number = 0,
                kids_number = 0,
                babies_number = 0,
                occupancy_string = '';

            adults_number = selected_occupancy[0] ? selected_occupancy[0] : adults_number;
            kids_number = selected_occupancy[1] ? selected_occupancy[1] : kids_number;
            babies_number = selected_occupancy[2] ? selected_occupancy[2] : babies_number;

            if (adults_number) occupancy_string = occupancy_string + adults_number + " " + adults_label;
            if (kids_number != '0') occupancy_string = occupancy_string + " + " + kids_number + " " + kids_label;
            if (babies_number != '0') occupancy_string = occupancy_string + " + " + babies_number + " " + babies_label;

            return occupancy_string
        },

        check_screen_height: function() {
            var screen_height = $(window).height(),
                website_height = $("body").height();

            if (screen_height > website_height) {
                $("#hidden_header_shopping_cart").insertAfter("#header");
                $("#hidden_header_shopping_cart").addClass('static_view');
            }
        },

        clean_cookies: function() {
            deleteCookie(this.configs.shopping_cart_cookie_name);
        },

        restore_rooms_from_cookie: function(){
            var room_cookie_info = searchCookie(this.configs.shopping_cart_cookie_name);

            if (!room_cookie_info) return;

            var loaded_rooms_info = JSON.parse(room_cookie_info),
                obj = null;

            for (var x=0;x<loaded_rooms_info.length;x++){
                obj = loaded_rooms_info[x];
                var is_additional_service = obj.is_additional_service;
                this.build_room_info(obj.room_name, obj.room_price, obj.rate_name,
                    obj.regimen_name, obj.occupancy_string, obj.random_identifier, obj.uuid, is_additional_service);

                if (is_additional_service) this.restore_additional_service_selection(obj);
            }

            this.update();
        },

        build_room_info: function(room_name, room_price, rate, regimen, occupancy, id, uuid, additional_service) {
            var baseline_room_card = $("#room_cart_baseline").clone();
            baseline_room_card.attr('id', '');

            if (additional_service){
                baseline_room_card.addClass('additional_service');
            }

            //Set room info
            baseline_room_card.find(".room_name").html(room_name);
            baseline_room_card.attr('uuid', uuid);
            baseline_room_card.find('.selected_rate').html(rate);
            baseline_room_card.find('.selected_regimen').html(regimen);
            baseline_room_card.find('.selected_price .currencyValue').html(room_price);
            baseline_room_card.attr('price', room_price);
            baseline_room_card.attr('ran_id', id);
            baseline_room_card.find('.occupancy_value').html(occupancy);

            //Hide remove button at some cases
            var actual_path = window.location.pathname;
            if (!(actual_path.indexOf('booking1') > -1)) {
                baseline_room_card.addClass('hide_remove_button');
            }

            //Room element styles
            baseline_room_card.css('display', 'block');
            var second_baseline = baseline_room_card.clone();

            //Add element to cart
            $("#hidden_header_shopping_cart").find(".selected_rooms_summary").append(baseline_room_card);
            $(".booking-3-price--partial").append(second_baseline);
        },

        remove_selected_room: function (selected_room) {
            var parent_wrapper = selected_room.closest('.room_cart_element'),
                target_ran_id = parent_wrapper.attr('ran_id');

            var match_index = 0;
            for (var i = 0; i < shopping_cart_room_cookie.length; i++) {
                if (shopping_cart_room_cookie[i].ran_id == target_ran_id) {
                    match_index = i;
                }
            }

            shopping_cart_room_cookie.splice(match_index,1);

            $(".room_cart_element[ran_id='" + target_ran_id + "']").each(function () {
                $(this).slideUp(function () {
                    var is_additional_service = $(this).hasClass('additional_service');
                    if (is_additional_service){
                        var remove_default_button = $(".remove_service_element[ran_id='" + target_ran_id + "']");
                        if (remove_default_button.length) remove_default_button.trigger('click');
                    }
                    $(this).remove();
                    shoppingCart.update();
                });
            })
        },

        recalculate_rooms_number: function () {
            var shopping_cart = $("#hidden_header_shopping_cart"),
                selected_rooms_amount = shopping_cart.find(".room_cart_element:not(#room_cart_baseline)").length; //Avoid baseline

            $(".selected_cart_amount").html(selected_rooms_amount);
            shopping_cart.find(".number_rooms_selected").html('(' + selected_rooms_amount + ')');

            var booking3_shopping_cart = $("#shopping_cart_b3_wrapper");
            if (booking3_shopping_cart.length){
                booking3_shopping_cart.find(".number_rooms_selected").html('(' + selected_rooms_amount + ')');
            }

            if (selected_rooms_amount == 1) {
                $(".shopping_cart_wrapper").addClass('active');
                $(".hidden_cart").addClass('visible');
            }
        },

        recalculate_total_prices: function () {
            var shopping_cart = $("#hidden_header_shopping_cart"),
                total_prices_amount = 0,
                room_price = 0;

            shopping_cart.find(".room_cart_element").each(function () {
                room_price = parseFloat($(this).attr('price'));
                if (room_price) total_prices_amount += room_price;
            });

            total_prices_amount = total_prices_amount.toFixed(2);

            shopping_cart.find(".total_cart_amount .currencyValue, .total_price_cart .currencyValue").html(total_prices_amount);

            var search_box_wrapper = $(".booking-box--search");
            if (search_box_wrapper.length) {
                var total_price_element = search_box_wrapper.find(".total_price_label #total");
                total_price_element.html(total_prices_amount);
            }
        },

        toggle_details: function(clicked_element) {
            var room_cart_element = clicked_element.closest('.room_cart_element');
            room_cart_element.toggleClass('details_visible');
            room_cart_element.find(".room_more_details").toggleClass('visible');
        },

        set_click_listeners: function() {
            $("#hidden_header_shopping_cart").find(".cart_header").click(function(){
                $(this).closest('.shopping_cart_wrapper').toggleClass('active');
                $("#hidden_header_shopping_cart").find(".hidden_cart").toggleClass('visible');
            });

            $("#searchForm").find("#search-button").click(function(){
                shoppingCart.clean_cookies();
            });

            $(".perform_package_booking_search").unbind("click").click(function(){
                shoppingCart.add_room($(this));
            });

            //Additional services
            var additional_supplements_wrapper = $(".ul_additionalservice");
            if (additional_supplements_wrapper.length){
                additional_supplements_wrapper.find(".add_additional_service_button").click(function () {
                    var added_element = shoppingCart.add_additional_service($(this));
                    if (!added_element) return;
                    $(this).addClass('hide');
                    $(this).closest(".supplement_buttons_wrapper").find(".remove_additional_service_button").removeClass('hide');
                });

                additional_supplements_wrapper.find(".remove_additional_service_button").click(function () {
                    $(this).addClass('hide');
                    $(this).closest(".supplement_buttons_wrapper").find(".add_additional_service_button").removeClass('hide');
                    shoppingCart.remove_additional_service($(this));
                });

                $(".booking2 form").on('submit', function () {
                    $(".remove_additional_service_button.hide").each(function () {
                        var supplement_wrapper = $(this).closest('.supplement_element'),
                            amount_selector = supplement_wrapper.find(".amount_sup select"),
                            nights_selector = supplement_wrapper.find(".nights_amount select");

                        amount_selector.find("option[value='0']").attr('selected', 'selected');
                        nights_selector.find("option[value='0']").attr('selected', 'selected');
                    });
                });
            }
        },

        perform_cart_search: function(){
            var available_uuids = [],
                shopping_cart_wrapper = $("#hidden_header_shopping_cart"),
                room_uuid = '';

            shopping_cart_wrapper.find(".room_cart_element").each(function(){
                available_uuids.push($(this).attr('uuid'));
            });

            var joined_uuids = available_uuids.join(';');
            $("#selectedUUID").val(joined_uuids);

            $("#selectionForm").submit();

            this.save_rooms_on_cookie();
        },

        check_hidden_header: function() {
            this.configs.header_height = $("#header").height();

            $(window).scroll(function () {
                var actual_scroll = $(window).scrollTop();
                if (actual_scroll > shoppingCart.configs.header_height) {
                    $("#hidden_header_shopping_cart").addClass('active');
                } else {
                    $("#hidden_header_shopping_cart").removeClass('active');
                }
            });
        },

        save_rooms_on_cookie: function() {
            json_info = JSON.stringify(shopping_cart_room_cookie);
            createCookie(this.configs.shopping_cart_cookie_name, json_info);
        },

        add_additional_service: function(button_element) {
            var supplement_wrapper = button_element.closest('.supplement_element');

            var service_name = supplement_wrapper.find(".supplement_title").html(),
                service_price = this.calculate_additional_service_price(supplement_wrapper),
                nights_value = supplement_wrapper.find(".nights_amount select").val(),
                amount_value = supplement_wrapper.find(".amount_sup select").val(),
                additional_service_common_label = $(".step_indicator .additional_services_label").html(),
                random_identifier = Math.floor(Math.random() * 500),
                is_additional_service = true;

            if (parseInt(nights_value) == 0 || parseInt(amount_value) == 0){
                return false;
            }

            supplement_wrapper.attr('ran_id', random_identifier);

            var default_nights_label = supplement_wrapper.find(".nights_amount select option[value='0']").html(),
                default_amount_label = supplement_wrapper.find(".amount_sup select option[value='0']").html(),
                nights_label = default_nights_label + ": " + nights_value,
                amount_label = default_amount_label + ": " + amount_value;

            this.build_room_info(service_name, service_price, nights_label, amount_label, additional_service_common_label, random_identifier, 'uuid', is_additional_service);
            this.update();
            recalculateTotal();

            supplement_wrapper.find(".nights_amount select").attr('disabled', 'disabled');
            supplement_wrapper.find(".amount_sup select").attr('disabled', 'disabled');

            return true;
        },

        remove_additional_service: function(button_element) {
            var additional_service_wrapper = button_element.closest('.supplement_element'),
                selected_ran_id = additional_service_wrapper.attr('ran_id');

            $("#hidden_header_shopping_cart").find(".room_cart_element[ran_id='" + selected_ran_id + "'] .remove_room_icon").trigger('click');

            additional_service_wrapper.find(".amount_sup select").removeAttr('disabled');
            additional_service_wrapper.find(".nights_amount select").removeAttr('disabled');
        },

        calculate_additional_service_price: function(supplement_element){
            var base_price = supplement_element.find(".add_additional_service_button").attr('price'),
                service_amount = supplement_element.find(".amount_sup select").val(),
                service_nights = supplement_element.find(".nights_amount select").val();

            return parseFloat(base_price) * parseFloat(service_amount) * parseFloat(service_nights);
        }
    }
}();

$(function () {
    shoppingCart.init();
});
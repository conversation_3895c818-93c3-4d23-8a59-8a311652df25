{% if hotels %}
    <div class="destination_wrapper">
        <label for="destination">
            {{ T_hotel_selector_text }}
        </label>
        <div class="destination_field">
            <input class="destination" readonly="readonly" type="text" name="destination" placeholder="{{ T_destino }}">
            <input type="hidden" id="default_destination_placeholder" name="default_destination_placeholder" class="default_destination_placeholder" value="">
        </div>
    </div>

    <div class="hotel_selector custom_selector_v2">
        <div class="hotel_selector_inner">
            <ul>
                {% for hotel in hotels %}
                    <li id="{{ hotel.namespace }}"
                        class="{{ hotel.namespace }} hotel_selector_option">
                        <div class="destiny_selector">{{ hotel.destiny|safe }}</div>
                        <div class="title_selector">{{ hotel.title|safe }}</div>
                    </li>
                    <input type="hidden" id="url_booking_{{ hotel.namespace }}" value="{{ hotel.url_booking }}">
                    <input type="hidden" id="namespace_{{ hotel.namespace }}" value="{{ hotel.namespace }}">
                {% endfor %}
            </ul>
        </div>
    </div>
{% endif %}

{% if filter_options %}
    <div class="filter_wrapper">
        <label for="filters">
            {{ T_tipo_de }}
        </label>
        <div class="filter_field">
            <div class="filters">{{ T_alquiler }}</div>
            <input type="hidden" readonly="readonly" id="filters" name="advanced_filters">
        </div>
    </div>

    <div class="filter_selector custom_selector_v2">
        <div class="filter_options_inner">
            <ul>
                {% for option in filter_options %}
                    <li class="filter_selector_option" data-filters="{{ option.filter_list }}">
                        <div class="title_selector">{{ option.description|safe }}</div>
                    </li>
                {% endfor %}
            </ul>
        </div>
    </div>
{% endif %}

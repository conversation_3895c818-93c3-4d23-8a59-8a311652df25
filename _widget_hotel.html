<div class="hotel_wrapper">
    <input type="hidden" name="destination" class="destination">
    <label class="hotel_label">{{ T_hotel }} / {{ T_apartamentos }}</label>
    <div class="hotel_text_selected_wrapper">
        <div class="hotel_text">{{ T_seleccionar_hotel }}</div>
    </div>

    <div class="hotel_list_wrapper hotel_selector">
        {% for hotel in hotels %}
            <div class="hotel_element"
                 {% if hotel.namespace %}data-namespace="{{ hotel.namespace|safe }}"{% endif %}
                 {% if hotel.url %}data-url="{{ hotel.url|safe }}"{% endif %}
                 {% if hotel.disable_kids %}data-disable-kids="true"{% endif %}
                 {% if hotel.external_link %}data-external-link="{{ hotel.external_link|safe }}"{% endif %}>
                {{ hotel.title|safe }}
            </div>
        {% endfor %}
    </div>
</div>
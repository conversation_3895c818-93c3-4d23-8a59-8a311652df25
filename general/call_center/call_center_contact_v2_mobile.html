<div class="floating_icon_call_center">
  	<a data-fancybox href="#call_center_popup">
        <i class="fal fa-headset"></i>
    </a>
</div>

<div id="call_center_popup" style="display:none;{% if background_gradient %}{{ background_gradient }}{% endif %}">
    <div class="main_content_popup">
        <i class="fal fa-headset"></i>
        <p class="description">{{ T_dejanos_nombre_tlf }}</p>
        <div class="form">
            <div class="name_call_center_wrapper">
                <input type="text" id="name_call_center" placeholder="{{ T_nombre_y_apellidos }}" class="input_call_center_form">
            </div>
            <div class="phone_call_center_wrapper">
                <input type="text" id="phone_call_center" placeholder="{{ T_telefono }}" class="input_call_center_form">
            </div>
            <button id="call_me_center" {% if call_properties and call_properties.background_start %} style="color:{{ call_properties.background_start }}"{% endif %}>{{ T_llama_ahora }}</button>
            <div class="policies_wrapper">
                <input type="checkbox" id="privacy_call_center">
                <label for="privacy_call_center">
                    <a data-fancybox data-options='{"caption" : "{{ T_politica_privacidad }}", "src" : "/{{ language_code }}/?sectionContent=politica-de-privacidad.html", "type" : "iframe", "width" : "100%", "max-width" : "100%"}' href="/{{ language_code }}/?sectionContent=politica-de-privacidad.html">
                        {{ T_lopd }}
                    </a>
                </label>
            </div>
        </div>
    </div>
    <div class="thanks_message hide">
        <div class="tick_wrapper">
            <i class="fa fa-check" aria-hidden="true"></i>
        </div>
        <p class="text">
            {{ T_breve_contacto }}
        </p>
    </div>
</div>

<style>
    {% if call_properties.background_start and call_properties.background_end %}
    body .floating_icon_call_center i.fa-headset::before{
        background: linear-gradient(90deg, {{ call_properties.background_start }} 0%, {{ call_properties.background_end }} 100%);
        -webkit-background-clip: text !important;
        color: transparent;
    }
    {% endif %}
</style>

<link rel="stylesheet" href="/static_1/css/call_center/styles_v2_mobile.css?v=1.0">
<script>
   $(window).load(function () {
       var call_wrapper = $("#call_center_popup");

       $('#call_me_center').click(function() {
            client_name = call_wrapper.find("#name_call_center").val(),
            client_phone = call_wrapper.find("#phone_call_center").val(),
            privacy_input = call_wrapper.find("#privacy_call_center");

            let regex = new RegExp(/^[0-9 \+]\d+$/);

            var something_missing = false;
            if (!privacy_input.is(':checked')) {
                privacy_input.addClass('error');
                something_missing = true;
            } else {
                privacy_input.removeClass('error');
            }

            if (!client_name) {
                call_wrapper.find(".name_call_center_wrapper").addClass('error');
                something_missing = true;
            } else {
                call_wrapper.find(".name_call_center_wrapper").removeClass('error');
            }

            if (!client_phone) {
                call_wrapper.find(".phone_call_center_wrapper").addClass('error');
                something_missing = true;
            } else if((client_phone.length < 7) || (client_phone.length > 13) || !regex.test(client_phone)){
                call_wrapper.find(".phone_call_center_wrapper").addClass('error');
                something_missing = true;
            } else {
                call_wrapper.find(".phone_call_center_wrapper").removeClass('error');
            }

            if (something_missing) return;

            var final_post_dict = {
                client_phone: client_phone
            };

            $.ajax({
                url: "/utils?action=call-center-contact",
                type: 'GET',
                data: jQuery.param(final_post_dict),
                success: function (response) {
                    call_wrapper.find(".main_content_popup").addClass('hide');
                    call_wrapper.find(".thanks_message").removeClass('hide');
                    setTimeout(function(){
                        call_wrapper.find('.fancybox-close-small').trigger('click');
                    }, 2500)
                }
            });
      });
       $('[data-fancybox]').fancybox({
            afterClose: function (){
                call_wrapper.find(".thanks_message").addClass('hide');
                call_wrapper.find(".main_content_popup").removeClass('hide');
            }
        });
    });

</script>

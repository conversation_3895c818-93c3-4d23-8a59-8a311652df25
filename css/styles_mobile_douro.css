@charset "UTF-8";
/* line 5, ../../../../../../../Library/Ruby/Gems/2.0.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
html, body, div, span, applet, object, iframe,
h1, h2, h3, h4, h5, h6, p, blockquote, pre,
a, abbr, acronym, address, big, cite, code,
del, dfn, em, img, ins, kbd, q, s, samp,
small, strike, strong, sub, sup, tt, var,
b, u, i, center,
dl, dt, dd, ol, ul, li,
fieldset, form, label, legend,
table, caption, tbody, tfoot, thead, tr, th, td,
article, aside, canvas, details, embed,
figure, figcaption, footer, header, hgroup,
menu, nav, output, ruby, section, summary,
time, mark, audio, video {
  margin: 0;
  padding: 0;
  border: 0;
  font: inherit;
  font-size: 100%;
  vertical-align: baseline;
}

/* line 22, ../../../../../../../Library/Ruby/Gems/2.0.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
html {
  line-height: 1;
}

/* line 24, ../../../../../../../Library/Ruby/Gems/2.0.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
ol, ul {
  list-style: none;
}

/* line 26, ../../../../../../../Library/Ruby/Gems/2.0.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* line 28, ../../../../../../../Library/Ruby/Gems/2.0.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
caption, th, td {
  text-align: left;
  font-weight: normal;
  vertical-align: middle;
}

/* line 30, ../../../../../../../Library/Ruby/Gems/2.0.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
q, blockquote {
  quotes: none;
}
/* line 103, ../../../../../../../Library/Ruby/Gems/2.0.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
q:before, q:after, blockquote:before, blockquote:after {
  content: "";
  content: none;
}

/* line 32, ../../../../../../../Library/Ruby/Gems/2.0.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
a img {
  border: none;
}

/* line 116, ../../../../../../../Library/Ruby/Gems/2.0.0/gems/compass-core-1.0.3/stylesheets/compass/reset/_utilities.scss */
article, aside, details, figcaption, figure, footer, header, hgroup, main, menu, nav, section, summary {
  display: block;
}

/*
 * jQuery FlexSlider v2.2.0
 * http://www.woothemes.com/flexslider/
 *
 * Copyright 2012 WooThemes
 * Free to use under the GPLv2 license.
 * http://www.gnu.org/licenses/gpl-2.0.html
 *
 * Contributing author: Tyler Smith (@mbmufffin)
 */
/* Browser Resets
*********************************/
/* line 14, ../../mobile_design_2/sass/_flexslider.scss */
.flex-container a:active,
.flexslider a:active,
.flex-container a:focus,
.flexslider a:focus {
  outline: none;
}

/* line 21, ../../mobile_design_2/sass/_flexslider.scss */
.slides,
.flex-control-nav,
.flex-direction-nav {
  margin: 0;
  padding: 0;
  list-style: none;
}

/* Icon Fonts
*********************************/
/* Font-face Icons */
@font-face {
  font-family: 'flexslider-icon';
  src: url("../../../Downloads/FlexSlider-version-2.2/fonts/flexslider-icon.eot");
  src: url("../../../Downloads/FlexSlider-version-2.2/fonts/flexslider-icon.eot?#iefix") format("embedded-opentype"), url("../../../Downloads/FlexSlider-version-2.2/fonts/flexslider-icon.woff") format("woff"), url("../../../Downloads/FlexSlider-version-2.2/fonts/flexslider-icon.ttf") format("truetype"), url("../../../Downloads/FlexSlider-version-2.2/fonts/flexslider-icon.svg#flexslider-icon") format("svg");
  font-weight: normal;
  font-style: normal;
}
/* FlexSlider Necessary Styles
*********************************/
/* line 45, ../../mobile_design_2/sass/_flexslider.scss */
.flexslider {
  margin: 0;
  padding: 0;
}

/* line 50, ../../mobile_design_2/sass/_flexslider.scss */
.flexslider .slides > li {
  display: none;
  -webkit-backface-visibility: hidden;
}

/* Hide the slides before the JS is loaded. Avoids image jumping */
/* line 56, ../../mobile_design_2/sass/_flexslider.scss */
.flexslider .slides img {
  min-width: 100%;
  display: block;
  height: 26.87em;
  max-width: none;
}

/* line 63, ../../mobile_design_2/sass/_flexslider.scss */
.flexslider .slides li {
  overflow: hidden;
}
/* line 66, ../../mobile_design_2/sass/_flexslider.scss */
.flexslider .slides li .description_text {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 100%;
  display: table;
  margin: auto;
  text-align: center;
  font-size: 2.06em;
  color: white;
  line-height: 1.2em;
  font-family: 'Montserrat', sans-serif;
  z-index: 2;
}
/* line 81, ../../mobile_design_2/sass/_flexslider.scss */
.flexslider .slides li .black_overlay {
  background: rgba(0, 0, 0, 0.3);
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

/* line 91, ../../mobile_design_2/sass/_flexslider.scss */
.flex-pauseplay span {
  text-transform: capitalize;
}

/* Clearfix for the .slides element */
/* line 96, ../../mobile_design_2/sass/_flexslider.scss */
.slides:after {
  content: "\0020";
  display: block;
  clear: both;
  visibility: hidden;
  line-height: 0;
  height: 0;
}

/* line 105, ../../mobile_design_2/sass/_flexslider.scss */
html[xmlns] .slides {
  display: block;
}

/* line 109, ../../mobile_design_2/sass/_flexslider.scss */
* html .slides {
  height: 1%;
}

/* No JavaScript Fallback */
/* If you are not using another script, such as Modernizr, make sure you
 * include js that eliminates this class on page load */
/* line 116, ../../mobile_design_2/sass/_flexslider.scss */
.no-js .slides > li:first-child {
  display: block;
}

/* FlexSlider Default Theme
*********************************/
/* line 122, ../../mobile_design_2/sass/_flexslider.scss */
.flexslider {
  margin: 0em 0 3.75em;
  background: #fff;
  position: relative;
  -webkit-border-radius: 4px;
  -moz-border-radius: 4px;
  -o-border-radius: 4px;
  border-radius: 4px;
  zoom: 1;
  overflow: hidden;
  padding-top: 7.1em;
}

/* line 135, ../../mobile_design_2/sass/_flexslider.scss */
.flex-viewport {
  max-height: 2000px;
  -webkit-transition: all 1s ease;
  -moz-transition: all 1s ease;
  -o-transition: all 1s ease;
  transition: all 1s ease;
}

/* line 143, ../../mobile_design_2/sass/_flexslider.scss */
.loading .flex-viewport {
  max-height: 300px;
}

/* line 147, ../../mobile_design_2/sass/_flexslider.scss */
.flexslider .slides {
  zoom: 1;
}

/* line 151, ../../mobile_design_2/sass/_flexslider.scss */
.carousel li {
  margin-right: 5px;
}

/* Direction Nav */
/* line 156, ../../mobile_design_2/sass/_flexslider.scss */
.flex-direction-nav {
  *height: 0;
}

/* line 160, ../../mobile_design_2/sass/_flexslider.scss */
.flex-direction-nav a {
  display: block;
  width: 40px;
  height: 40px;
  margin: -20px 0 0;
  position: absolute;
  top: 50%;
  z-index: 10;
  overflow: hidden;
  opacity: 0;
  cursor: pointer;
  color: rgba(0, 0, 0, 0.8);
  text-shadow: 1px 1px 0 rgba(255, 255, 255, 0.3);
  -webkit-transition: all .3s ease;
  -moz-transition: all .3s ease;
  transition: all .3s ease;
}

/* line 178, ../../mobile_design_2/sass/_flexslider.scss */
.flex-direction-nav .flex-prev {
  left: -50px;
}

/* line 182, ../../mobile_design_2/sass/_flexslider.scss */
.flex-direction-nav .flex-next {
  right: -50px;
  text-align: right;
}

/* line 187, ../../mobile_design_2/sass/_flexslider.scss */
.flexslider:hover .flex-prev {
  opacity: 0.7;
  left: 10px;
}

/* line 192, ../../mobile_design_2/sass/_flexslider.scss */
.flexslider:hover .flex-next {
  opacity: 0.7;
  right: 10px;
}

/* line 197, ../../mobile_design_2/sass/_flexslider.scss */
.flexslider:hover .flex-next:hover, .flexslider:hover .flex-prev:hover {
  opacity: 1;
}

/* line 201, ../../mobile_design_2/sass/_flexslider.scss */
.flex-direction-nav .flex-disabled {
  opacity: 0 !important;
  filter: alpha(opacity=0);
  cursor: default;
}

/* line 207, ../../mobile_design_2/sass/_flexslider.scss */
.flex-direction-nav a:before {
  font-family: "flexslider-icon";
  font-size: 40px;
  display: inline-block;
  content: '\f001';
}

/* line 214, ../../mobile_design_2/sass/_flexslider.scss */
.flex-direction-nav a.flex-next:before {
  content: '\f002';
}

/* Pause/Play */
/* line 219, ../../mobile_design_2/sass/_flexslider.scss */
.flex-pauseplay a {
  display: block;
  width: 20px;
  height: 20px;
  position: absolute;
  bottom: 5px;
  left: 10px;
  opacity: 0.8;
  z-index: 10;
  overflow: hidden;
  cursor: pointer;
  color: #000;
}

/* line 233, ../../mobile_design_2/sass/_flexslider.scss */
.flex-pauseplay a:before {
  font-family: "flexslider-icon";
  font-size: 20px;
  display: inline-block;
  content: '\f004';
}

/* line 240, ../../mobile_design_2/sass/_flexslider.scss */
.flex-pauseplay a:hover {
  opacity: 1;
}

/* line 244, ../../mobile_design_2/sass/_flexslider.scss */
.flex-pauseplay a.flex-play:before {
  content: '\f003';
}

/* Control Nav */
/* line 249, ../../mobile_design_2/sass/_flexslider.scss */
.flex-control-nav {
  width: 100%;
  position: absolute;
  bottom: -40px;
  text-align: center;
}

/* line 256, ../../mobile_design_2/sass/_flexslider.scss */
.flex-control-nav li {
  margin: 0 6px;
  display: inline-block;
  zoom: 1;
  *display: inline;
}

/* line 263, ../../mobile_design_2/sass/_flexslider.scss */
.flex-control-paging li a {
  width: 11px;
  height: 11px;
  display: block;
  background: #666;
  background: #f2f2f4;
  cursor: pointer;
  text-indent: -9999px;
  -webkit-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
  -o-box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.3);
}

/* line 277, ../../mobile_design_2/sass/_flexslider.scss */
.flex-control-paging li a:hover {
  background: #333;
  background: rgba(0, 0, 0, 0.7);
}

/* line 282, ../../mobile_design_2/sass/_flexslider.scss */
.flex-control-paging li a.flex-active {
  background: #000;
  background: #0a0a0a;
  cursor: default;
}

/* line 288, ../../mobile_design_2/sass/_flexslider.scss */
.flex-control-thumbs {
  margin: 5px 0 0;
  position: static;
  overflow: hidden;
}

/* line 294, ../../mobile_design_2/sass/_flexslider.scss */
.flex-control-thumbs li {
  width: 25%;
  float: left;
  margin: 0;
}

/* line 300, ../../mobile_design_2/sass/_flexslider.scss */
.flex-control-thumbs img {
  width: 100%;
  display: block;
  opacity: .7;
  cursor: pointer;
}

/* line 307, ../../mobile_design_2/sass/_flexslider.scss */
.flex-control-thumbs img:hover {
  opacity: 1;
}

/* line 311, ../../mobile_design_2/sass/_flexslider.scss */
.flex-control-thumbs .flex-active {
  opacity: 1;
  cursor: default;
}

@media screen and (max-width: 860px) {
  /* line 317, ../../mobile_design_2/sass/_flexslider.scss */
  .flex-direction-nav .flex-prev {
    opacity: 0;
    left: 10px;
    display: none;
  }

  /* line 322, ../../mobile_design_2/sass/_flexslider.scss */
  .flex-direction-nav .flex-next {
    opacity: 0;
    right: 10px;
    display: none;
  }
}
/* line 329, ../../mobile_design_2/sass/_flexslider.scss */
.flexslider {
  border: none !important;
}

/* line 1, ../../mobile_design_2/sass/_mmenu.scss */
.mm-page,
.mm-fixed-top,
.mm-fixed-bottom,
.mm-menu.mm-horizontal > .mm-panel {
  -webkit-transition: none 0.4s ease;
  -moz-transition: none 0.4s ease;
  -ms-transition: none 0.4s ease;
  -o-transition: none 0.4s ease;
  transition: none 0.4s ease;
  -webkit-transition-property: top, right, bottom, left, border;
  -moz-transition-property: top, right, bottom, left, border;
  -ms-transition-property: top, right, bottom, left, border;
  -o-transition-property: top, right, bottom, left, border;
  transition-property: top, right, bottom, left, border;
}

/* line 17, ../../mobile_design_2/sass/_mmenu.scss */
html.mm-opened .mm-page,
html.mm-opened #mm-blocker {
  left: 0;
  top: 0;
  margin: 0;
  border: 0px solid transparent;
}

/* line 25, ../../mobile_design_2/sass/_mmenu.scss */
html.mm-opening .mm-page,
html.mm-opening #mm-blocker {
  border: 0px solid rgba(100, 100, 100, 0);
}

/* line 30, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu .mm-hidden {
  display: none;
}

/* line 34, ../../mobile_design_2/sass/_mmenu.scss */
.mm-fixed-top,
.mm-fixed-bottom {
  position: fixed;
  left: 0;
}

/* line 40, ../../mobile_design_2/sass/_mmenu.scss */
.mm-fixed-top {
  top: 0;
}

/* line 44, ../../mobile_design_2/sass/_mmenu.scss */
.mm-fixed-bottom {
  bottom: 0;
}

/* line 48, ../../mobile_design_2/sass/_mmenu.scss */
html.mm-opened .mm-page,
.mm-menu > .mm-panel {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  -ms-box-sizing: border-box;
  -o-box-sizing: border-box;
  box-sizing: border-box;
}

/* line 57, ../../mobile_design_2/sass/_mmenu.scss */
html.mm-opened {
  overflow-x: hidden;
  position: relative;
}

/* line 62, ../../mobile_design_2/sass/_mmenu.scss */
html.mm-opened .mm-page {
  position: relative;
}

/* line 66, ../../mobile_design_2/sass/_mmenu.scss */
html.mm-background .mm-page {
  background: inherit;
}

/* line 70, ../../mobile_design_2/sass/_mmenu.scss */
#mm-blocker {
  background: url(data:image/gif;base64,R0lGODlhAQABAIAAAP///wAAACH5BAEAAAAALAAAAAABAAEAAAICRAEAOw==) transparent;
  display: none;
  width: 100%;
  height: 100%;
  position: fixed;
  z-index: 999999;
}

/* line 79, ../../mobile_design_2/sass/_mmenu.scss */
html.mm-opened #mm-blocker,
html.mm-blocking #mm-blocker {
  display: block;
}

/* line 84, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu.mm-current {
  display: block;
}

/* line 88, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu {
  background: inherit;
  display: none;
  overflow: hidden;
  height: 100%;
  padding: 0;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 0;
}

/* line 100, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu > .mm-panel {
  background: inherit;
  -webkit-overflow-scrolling: touch;
  overflow: scroll;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  height: 100%;
  padding: 20px;
  position: absolute;
  top: 0;
  left: 100%;
  z-index: 0;
}

/* line 115, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu > .mm-panel.mm-opened {
  left: 0%;
  top: 36px;
}

/* line 121, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu > .mm-panel.mm-subopened {
  left: -40%;
}

/* line 125, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu > .mm-panel.mm-highest {
  z-index: 1;
}

/* line 129, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu > .mm-panel.mm-hidden {
  display: block;
  visibility: hidden;
}

/* line 134, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu .mm-list {
  padding: 20px 0;
}

/* line 138, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu > .mm-list {
  padding: 20px 0 40px 0;
}

/* line 142, ../../mobile_design_2/sass/_mmenu.scss */
.mm-panel > .mm-list {
  margin-left: -20px;
  margin-right: -20px;
}

/* line 147, ../../mobile_design_2/sass/_mmenu.scss */
.mm-panel > .mm-list:first-child {
  padding-top: 0;
}

/* line 151, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list,
.mm-list > li {
  list-style: none;
  display: block;
  padding: 0;
  margin: 0;
}

/* line 159, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list {
  font: inherit;
  font-size: 14px;
}

/* line 164, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list a,
.mm-list a:hover {
  text-decoration: none;
}

/* line 169, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list > li {
  position: relative;
}

/* line 173, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list > li > a,
.mm-list > li > span {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  color: inherit;
  line-height: 20px;
  display: block;
  padding: 20px 10px 20px 20px;
  margin: 0;
}

/* line 185, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list > li:not(.mm-subtitle):not(.mm-label):not(.mm-noresults):after {
  content: '';
  border-bottom-width: 1px;
  border-bottom-style: solid;
  display: block;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
}

/* line 196, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list > li:not(.mm-subtitle):not(.mm-label):not(.mm-noresults):after {
  width: auto;
  margin-left: 20px;
  position: relative;
  left: auto;
}

/* line 203, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list a.mm-subopen {
  width: 40px;
  height: 100%;
  padding: 0;
  position: absolute;
  right: 0;
  top: 0;
  z-index: 2;
}

/* line 213, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list a.mm-subopen:before {
  content: '';
  border-left-width: 1px;
  border-left-style: solid;
  display: block;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
}

/* line 224, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list a.mm-subopen.mm-fullsubopen {
  width: 100%;
}

/* line 228, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list a.mm-subopen.mm-fullsubopen:before {
  border-left: none;
}

/* line 232, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list a.mm-subopen + a,
.mm-list a.mm-subopen + span {
  padding-right: 5px;
  margin-right: 40px;
}

/* line 238, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list > li.mm-selected > a.mm-subopen {
  background: transparent;
}

/* line 242, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list > li.mm-selected > a.mm-fullsubopen + a,
.mm-list > li.mm-selected > a.mm-fullsubopen + span {
  padding-right: 45px;
  margin-right: 0;
}

/* line 248, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list a.mm-subclose {
  text-indent: 20px;
  padding-top: 22px;
  margin-top: -20px;
}

/* line 254, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list > li.mm-label {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-size: 10px;
  text-transform: uppercase;
  text-indent: 20px;
  line-height: 25px;
  padding-right: 5px;
}

/* line 265, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list > li.mm-spacer {
  padding-top: 40px;
}

/* line 269, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list > li.mm-spacer.mm-label {
  padding-top: 25px;
}

/* line 273, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list a.mm-subopen:after,
.mm-list a.mm-subclose:before {
  content: '';
  border: 2px solid transparent;
  display: block;
  width: 7px;
  height: 7px;
  margin-bottom: -5px;
  position: absolute;
  bottom: 50%;
  -webkit-transform: rotate(-45deg);
  -moz-transform: rotate(-45deg);
  -ms-transform: rotate(-45deg);
  -o-transform: rotate(-45deg);
  transform: rotate(-45deg);
}

/* line 290, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list a.mm-subclose:before {
  bottom: 66%;
}

/* line 294, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list a.mm-subopen:after {
  border-top: none;
  border-left: none;
  right: 18px;
}

/* line 300, ../../mobile_design_2/sass/_mmenu.scss */
.mm-list a.mm-subclose:before {
  border-right: none;
  border-bottom: none;
  margin-bottom: -15px;
  left: 22px;
}

/* line 307, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu.mm-vertical .mm-list .mm-panel {
  display: none;
  padding: 10px 0 10px 10px;
}

/* line 312, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu.mm-vertical .mm-list .mm-panel li:last-child:after {
  border-color: transparent;
}

/* line 316, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu.mm-vertical .mm-list li.mm-opened > .mm-panel {
  display: block;
}

/* line 320, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu.mm-vertical .mm-list > li.mm-opened > a.mm-subopen {
  height: 40px;
}

/* line 324, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu.mm-vertical .mm-list > li.mm-opened > a.mm-subopen:after {
  -webkit-transform: rotate(45deg);
  -moz-transform: rotate(45deg);
  -ms-transform: rotate(45deg);
  -o-transform: rotate(45deg);
  transform: rotate(45deg);
  top: 16px;
  right: 16px;
}

/* line 334, ../../mobile_design_2/sass/_mmenu.scss */
html.mm-opened .mm-page {
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
}

/* line 338, ../../mobile_design_2/sass/_mmenu.scss */
.mm-ismenu {
  background: #333333;
  color: rgba(255, 255, 255, 0.6);
}

/* line 343, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu .mm-list > li:after {
  border-color: rgba(0, 0, 0, 0.15);
}

/* line 347, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu .mm-list > li > a.mm-subclose {
  background: rgba(0, 0, 0, 0.1);
  color: rgba(255, 255, 255, 0.7);
}

/* line 352, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu .mm-list > li > a.mm-subopen:after, .mm-menu .mm-list > li > a.mm-subclose:before {
  border-color: rgba(255, 255, 255, 0.6);
}

/* line 356, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu .mm-list > li > a.mm-subopen:before {
  border-color: rgba(0, 0, 0, 0.15);
}

/* line 360, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu .mm-list > li.mm-selected > a:not(.mm-subopen),
.mm-menu .mm-list > li.mm-selected > span {
  background: rgba(0, 0, 0, 0.1);
}

/* line 365, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu .mm-list > li.mm-label {
  background: rgba(255, 255, 255, 0.05);
}

/* line 369, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu.mm-vertical .mm-list li.mm-opened > a.mm-subopen,
.mm-menu.mm-vertical .mm-list li.mm-opened > ul {
  background: rgba(255, 255, 255, 0.05);
}

/* line 374, ../../mobile_design_2/sass/_mmenu.scss */
html.mm-opening .mm-page,
html.mm-opening #mm-blocker,
html.mm-opening .mm-fixed-top,
html.mm-opening .mm-fixed-bottom {
  left: 80%;
}

/* line 381, ../../mobile_design_2/sass/_mmenu.scss */
.mm-menu {
  width: 80vw;
}

@media all and (max-width: 175px) {
  /* line 386, ../../mobile_design_2/sass/_mmenu.scss */
  .mm-menu {
    width: 140px;
  }

  /* line 390, ../../mobile_design_2/sass/_mmenu.scss */
  html.mm-opening .mm-page,
  html.mm-opening #mm-blocker,
  html.mm-opening .mm-fixed-top,
  html.mm-opening .mm-fixed-bottom {
    left: 140px;
  }
}
@media all and (min-width: 550px) {
  /* line 399, ../../mobile_design_2/sass/_mmenu.scss */
  .mm-menu {
    width: 440px;
  }

  /* line 403, ../../mobile_design_2/sass/_mmenu.scss */
  html.mm-opening .mm-page,
  html.mm-opening #mm-blocker,
  html.mm-opening .mm-fixed-top,
  html.mm-opening .mm-fixed-bottom {
    left: 440px;
  }
}
/* line 411, ../../mobile_design_2/sass/_mmenu.scss */
.wrap {
  background: white;
}

/* line 8, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.wrap {
  background-color: white;
}

/* line 12, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.hide_mobile {
  display: none;
}

/* line 16, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
body {
  width: 100%;
  min-width: 320px;
  font-family: 'Source Sans Pro', sans-serif;
  font-size: 1em;
  line-height: 1.3em;
  background-color: white;
}

/* line 26, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#simple-menu {
  float: left;
  width: 40px;
  height: 40px;
  margin: 3px 3px 0;
}

/* line 33, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
header {
  height: 7.06em;
  position: fixed;
  width: 100%;
  background: white;
  top: 0 !important;
  z-index: 200;
  -webkit-box-shadow: 0 10px 18px 0 rgba(0, 0, 0, 0.5);
  -moz-box-shadow: 0 10px 18px 0 rgba(0, 0, 0, 0.5);
  box-shadow: 0 10px 18px 0 rgba(0, 0, 0, 0.5);
}

/* line 45, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.logo {
  float: none;
  text-align: center;
  height: 48px;
  width: auto;
  padding-top: 5px;
}

/* line 53, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.logo img {
  height: 6em;
}

/* line 57, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.default_content_wrapper .home_section {
  padding: 2.187em 5%;
  background: #ECEAEB;
  margin-top: 1em;
}

/* line 63, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.contact .mail img {
  padding-bottom: 7px;
}

/* line 67, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.fancybox-overlay-fixed {
  background: rgba(0, 0, 0, 0.34) !important;
}

/*-----------------CONTAINER_100-------------------*/
/* line 73, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.container_100 {
  width: 100%;
  background-color: white;
}

/* line 78, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.menu_display {
  width: 33%;
}

/* line 82, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.menu_display img {
  margin: 10px;
  max-width: 40px;
}

/* line 87, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#simple-menu img {
  background: #008BAC;
  height: 6.4em;
}

/* line 92, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.logo a {
  width: 281px;
  display: block;
  margin: auto;
}

/*====== Normal Sections =====*/
/* line 100, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.normal_section_mobile h2.section_title {
  font-family: 'Montserrat', sans-serif;
  font-weight: bolder;
  color: #008BAC;
  font-size: 2.437em;
  margin: 0.3em 0;
  line-height: 1em;
  padding-top: 0.5em;
  text-align: center;
}
/* line 111, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.normal_section_mobile .section-content {
  font-size: 1.9em;
  line-height: 1.4em;
  font-weight: lighter;
  padding-bottom: 1em;
  text-align: center;
  margin-top: 2em;
}

/*-------------- Booking Widget ------------*/
/* line 122, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.booking_form_title {
  text-align: center;
}
/* line 125, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.booking_form_title .booking_title_1 {
  display: none;
}
/* line 129, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.booking_form_title h4.booking_title_2 {
  font-size: 2.43em;
  margin-bottom: 0.4em;
  color: #313234;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 700;
}
/* line 137, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.booking_form_title h4.best_price {
  font-size: 1.87em;
  letter-spacing: 1px;
  font-family: 'Source Sans Pro', sans-serif;
  color: #313234;
  font-weight: 100;
}

/* line 147, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .end_date_wrapper {
  float: right;
}
/* line 151, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .calendar_1, form.paraty-booking-form .calendar_2, form.paraty-booking-form .selectricHideSelect, form.paraty-booking-form .selectric-hide-select {
  display: none;
}
/* line 156, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .select_hotel .selectric {
  display: block;
  position: relative;
  min-height: 5.93em;
  background: white;
  padding: 0 2em;
  box-sizing: border-box;
  text-align: center;
  border: 0.5em solid #F9F9F9;
}
/* line 166, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .select_hotel .selectric p.label, form.paraty-booking-form .select_hotel .selectric span.label {
  float: left;
  line-height: 1em;
  color: #333;
  font-size: 1.87em;
  position: absolute;
  top: 0;
  bottom: 0;
  display: table;
  margin: auto;
}
/* line 178, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .select_hotel .selectric b.button {
  float: right;
  position: absolute;
  right: 0;
  top: 0;
  color: #eaeaea;
  background: #eaeaea url(/static_1/images/mobile_img/renovation/arrow_darkgray.png) no-repeat center;
  background-size: 1.5em;
  height: 100%;
  width: 4.8em;
}
/* line 191, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .selectricItems, form.paraty-booking-form .selectric-items {
  width: 100% !important;
  max-height: 0;
  height: auto !important;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 201, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .selectricItems ul li, form.paraty-booking-form .selectric-items ul li {
  text-align: left;
  display: block;
  position: relative;
  font-size: 1.87em;
  background: white;
  padding: 0.9em 1.2em;
  box-sizing: border-box;
  border-bottom: 2px solid #BFBFBF;
  border-left: 2px solid #BFBFBF;
  border-right: 2px solid #BFBFBF;
  color: #333;
  margin: 0;
}
/* line 215, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .selectricItems ul li.selected, form.paraty-booking-form .selectric-items ul li.selected {
  background: #008BAC;
  color: white;
}
/* line 220, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .selectricItems ul li.selectric-group-label, form.paraty-booking-form .selectric-items ul li.selectric-group-label {
  background: #52ADCA;
  color: white;
}
/* line 227, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .selectricInput, form.paraty-booking-form .selectric-input {
  display: none;
}
/* line 232, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .selectricWrapper.selectricOpen .selectricItems, form.paraty-booking-form .selectric-wrapper.selectric-hover .selectricItems {
  max-height: 1000px !important;
}
/* line 236, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .selectricWrapper.selectricOpen .selectric-items, form.paraty-booking-form .selectric-wrapper.selectric-hover .selectric-items {
  max-height: 1000px !important;
}

/* line 242, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.hidden_booking_widget {
  padding: 2.187em 5%;
  background: #ECEAEB;
  display: none;
}

/* line 248, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.booking_general_button {
  margin-top: 8em;
  margin-bottom: 0.3em;
  padding: 2em;
  background: #ECEAEB;
}
/* line 254, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.booking_general_button div.button {
  background-color: #008BAC;
  margin: 0 auto;
  width: 100%;
  border: none;
  border-radius: 0;
  color: white;
  font-size: 2.625em;
  font-weight: lighter;
  text-transform: uppercase;
  padding: 13px 0;
  font-family: 'Montserrat', sans-serif;
  -webkit-appearance: none;
  display: block;
  text-align: center;
  line-height: 1em;
}

/* line 273, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.loading_gif_wrapper {
  text-align: center;
}
/* line 276, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.loading_gif_wrapper .text_loading_popup {
  color: #52ADCA;
  text-align: center;
  font-family: 'Montserrat', sans-serif;
  text-transform: uppercase;
  line-height: 1em;
  font-size: 1.2em;
}
/* line 284, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.loading_gif_wrapper .text_loading_popup strong {
  font-size: 2.6em;
  margin-bottom: 0.4em;
  display: block;
}

/*===== Header =====*/
/* line 293, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
header .contact {
  position: absolute;
  right: 1em;
  top: 0;
}
/* line 298, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
header .contact .header_separator {
  height: 5.8em;
  border-left: 1px solid #eaeaea;
  position: absolute;
  right: 6.7em;
  top: 0.6em;
}
/* line 306, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
header .contact img {
  height: 6.4em;
  background: #008BAC;
  margin-top: 0.5em;
}

/*-----------------CONTAINER_90-------------------*/
/* line 315, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.container_90 {
  width: 90%;
  margin: 0 auto;
}

/* line 320, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.content {
  width: 100%;
  margin: 0;
}
/* line 324, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.content > .section {
  padding: 2.187em 5%;
}
/* line 327, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.content > .section > h2 {
  font-family: 'Montserrat', sans-serif;
  font-weight: bolder;
  color: #52ADCA;
  font-size: 2.437em;
  margin: 0.3em 0;
  margin-top: 0;
  line-height: 1em;
  padding-top: 0.5em;
  text-align: center;
}
/* line 339, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.content > .section .section-content {
  font-size: 1.9em;
  line-height: 1.4em;
  font-weight: lighter;
  padding-bottom: 1em;
  text-align: center;
  margin-top: 2em;
}

/* line 350, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
a {
  text-decoration: none;
}

/* line 354, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.direct_link_offers {
  margin-bottom: 10px;
}

/* line 358, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.direct_link_offers a {
  color: white;
}

/**
 * --------------------------------------------------------------------------
 * FORMS
 * --------------------------------------------------------------------------
 */
/* line 368, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form {
  width: 100%;
  margin-top: 4%;
  overflow: hidden;
}
/* line 373, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form label {
  line-height: 1em;
  font-size: 1.87em;
  color: #333;
  width: 80px;
  position: absolute;
  z-index: 4;
  top: 10%;
  left: 1px;
  padding-top: 5px;
  padding-left: 10px;
  letter-spacing: 2px;
}
/* line 387, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form input,
form.paraty-booking-form textarea,
form.paraty-booking-form select {
  display: block;
  width: 100%;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
/* line 399, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form input {
  border: none;
  margin: 0 auto;
}
/* line 404, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form textarea {
  height: 70px;
}
/* line 408, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .habitacion, form.paraty-booking-form #room_1, form.paraty-booking-form #room_2, form.paraty-booking-form #room_3 {
  border: 2px solid #BFBFBF;
}
/* line 411, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .habitacion, form.paraty-booking-form .habitacion div, form.paraty-booking-form #room_1, form.paraty-booking-form #room_1 div, form.paraty-booking-form #room_2, form.paraty-booking-form #room_2 div, form.paraty-booking-form #room_3, form.paraty-booking-form #room_3 div {
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  display: block;
  position: relative;
  min-height: 5.93em;
  background: white;
  margin-bottom: 20px;
  padding: 2em;
  box-sizing: border-box;
  text-align: center;
}
/* line 422, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .habitacion span, form.paraty-booking-form .habitacion div span, form.paraty-booking-form #room_1 span, form.paraty-booking-form #room_1 div span, form.paraty-booking-form #room_2 span, form.paraty-booking-form #room_2 div span, form.paraty-booking-form #room_3 span, form.paraty-booking-form #room_3 div span {
  display: inline-block;
  font-size: 1.87em;
  font-weight: 100;
  text-transform: lowercase;
}
/* line 430, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .habitacion .title, form.paraty-booking-form #room_1 .title, form.paraty-booking-form #room_2 .title, form.paraty-booking-form #room_3 .title {
  text-align: center;
  background: none;
  font-size: 2.43em;
  margin-bottom: 0.4em;
  color: #313234;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 700;
  padding-top: 1em;
}
/* line 442, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form #room_1, form.paraty-booking-form #room_2, form.paraty-booking-form #room_3 {
  background: none;
  border: 0;
  padding: 0;
}
/* line 447, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form #room_1 .adulto, form.paraty-booking-form #room_1 .nino, form.paraty-booking-form #room_2 .adulto, form.paraty-booking-form #room_2 .nino, form.paraty-booking-form #room_3 .adulto, form.paraty-booking-form #room_3 .nino {
  background: white;
  border: 2px solid #BFBFBF;
}
/* line 453, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .start_date_wrapper, form.paraty-booking-form .end_date_wrapper {
  border: 0;
  background: #008BAC;
  display: inline-block;
  position: relative;
  width: 48%;
  margin: 0 0 2em;
}
/* line 461, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .start_date_wrapper:before, form.paraty-booking-form .end_date_wrapper:before {
  content: "";
  display: block;
  padding-top: 86%;
}
/* line 467, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .start_date_wrapper label, form.paraty-booking-form .end_date_wrapper label {
  width: 100%;
  color: white;
  padding-left: 0;
  text-align: center;
  text-transform: uppercase;
  font-weight: 100;
}
/* line 477, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .start_date_wrapper .dates_box_text .day, form.paraty-booking-form .end_date_wrapper .dates_box_text .day {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1em;
  margin: auto;
  text-align: center;
  line-height: 1em;
  font-size: 10em;
  color: white;
  font-weight: 100;
}
/* line 491, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .start_date_wrapper .dates_box_text .month, form.paraty-booking-form .end_date_wrapper .dates_box_text .month {
  position: absolute;
  bottom: 10%;
  left: 0;
  right: 0;
  margin: auto;
  text-align: center;
  color: white;
  line-height: 1em;
  font-size: 1.87em;
  letter-spacing: 2px;
  font-weight: 100;
}
/* line 506, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .start_date_wrapper input, form.paraty-booking-form .end_date_wrapper input {
  display: none;
}
/* line 511, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form #rooms_container {
  border: 0;
}
/* line 515, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form ul li {
  margin: 30px 0;
  text-align: center;
}
/* line 520, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .promocode_text {
  text-align: center;
  font-size: 1.43em;
  margin-bottom: 0.7em;
  color: #545454;
  font-family: 'Source Sans Pro', sans-serif;
  font-weight: 700;
  line-height: 2em;
}
/* line 530, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form input[name="promocode"] {
  display: none;
  -moz-border-radius: 3px;
  -webkit-border-radius: 3px;
  border-radius: 3px;
  position: relative;
  font-size: 1.87em !important;
  min-height: 2em;
  background: white !important;
  margin-bottom: 20px;
  padding: 2em;
  box-sizing: border-box;
  text-align: center;
  border: 2px solid #BFBFBF;
}
/* line 545, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form input[name="promocode"]::-webkit-input-placeholder {
  font-weight: lighter;
  color: #6B6B6B;
  font-size: 1em;
}

/* line 557, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form input[type="text"] {
  line-height: 1em;
  padding: 10px 20px 10px 100px;
  color: #333;
  background: transparent;
  font-size: 1.87em;
  margin-right: 3px;
}

/* line 566, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form div.datapicker_1, form.paraty-booking-form div.datapicker_2 {
  border: none;
  display: none;
  margin: 0;
}
/* line 571, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form div.datapicker_1 .ui-corner-all, form.paraty-booking-form div.datapicker_2 .ui-corner-all {
  border: 0 !important;
}

/* line 576, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form ul li input[type="range"] {
  width: 100%;
  margin-top: 20px;
  display: inline-block;
}

/* line 582, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .ninoedad {
  font-weight: lighter;
  margin-left: 0;
}

/* line 587, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .ver_edad {
  font-weight: lighter;
  margin-left: 10px;
}

/* line 592, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .age_kid {
  display: none;
}

/* line 596, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form input[name=numRooms],
form.paraty-booking-form input[name="childrenRoom1"],
form.paraty-booking-form input[name="childrenRoom2"],
form.paraty-booking-form input[name="childrenRoom3"],
form.paraty-booking-form input[name="adultsRoom1"],
form.paraty-booking-form input[name="adultsRoom2"],
form.paraty-booking-form input[name="adultsRoom3"],
form.paraty-booking-form input[name="babiesRoom1"],
form.paraty-booking-form input[name="babiesRoom2"],
form.paraty-booking-form input[name="babiesRoom3"] {
  display: inline !important;
  width: 1.75em;
  padding: 0;
  text-align: right;
}

/* line 612, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .select_hotel input[type="text"] {
  padding: 13px 10px;
  border: none;
}

/* line 617, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form input[type="text"]:focus {
  outline: none;
}

/* line 621, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#llegada:focus, #salida:focus {
  border: 2px solid #52ADCA;
}

/* line 625, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form input[type="submit"] {
  background-color: #008BAC;
  margin: 0 auto;
  width: 100%;
  border: none;
  border-radius: 0px;
  color: white;
  font-size: 2.625em;
  font-weight: lighter;
  text-transform: uppercase;
  padding: 13px 20px;
  font-family: 'Montserrat', sans-serif;
  -webkit-appearance: none;
}

/* line 640, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form input[type="submit"]:focus {
  outline: none;
  border: 1px solid #52ADCA;
}

/* line 645, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
input::-webkit-input-placeholder {
  font-weight: lighter;
  color: black;
  font-family: 'Source Sans Pro', sans-serif;
}

/* line 651, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#envio {
  border: none;
}
/* line 654, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#envio + img {
  width: 4em;
}

/* line 659, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.select_hotel {
  border: none;
  overflow: hidden;
  margin-bottom: 2em;
}
/* line 664, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.select_hotel select {
  width: 100%;
  line-height: 100%;
  font-size: 20px;
  background: #52ADCA;
  color: white;
  font-family: inherit;
  padding: 10px 0;
  border-radius: 1px;
  text-align: center;
  text-align-last: center;
}

/* line 678, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#place {
  padding-left: 20px;
  padding-top: 14px;
  text-align: center;
}

/* line 684, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form .block_hidden {
  border: none;
}

/* line 688, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form #room_2, form.paraty-booking-form #room_3 {
  display: none;
}

/* line 692, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
form.paraty-booking-form img {
  margin-left: 40%;
  display: none;
}

/* line 697, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.title {
  margin-bottom: 15px;
  text-transform: uppercase;
  font-size: 1em;
  font-weight: 400;
}

/* line 704, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.hidden {
  display: none;
}

/* line 708, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
input::-moz-placeholder {
  font-weight: lighter;
  color: black;
}

/* line 713, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
input::-ms-input-placeholder {
  font-weight: lighter;
  color: black;
}

/* line 718, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.iconos {
  width: 100%;
}
/* line 721, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.iconos ul {
  overflow: auto;
  text-align: center;
  width: 100%;
  display: table;
}
/* line 729, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.iconos li {
  width: 32%;
  text-align: center;
  margin-top: 1.562em;
  display: inline-table;
  vertical-align: top;
}
/* line 736, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.iconos li img {
  width: 60%;
}
/* line 740, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.iconos li p {
  font-size: 1em;
  text-transform: uppercase;
  color: #686867;
  font-weight: bold;
  text-align: center;
  width: 80%;
  margin: auto;
}

/* line 752, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
nav {
  background: white;
}
/* line 755, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
nav ul.submenu_list {
  display: none;
  background: #eaeaea;
  -moz-box-shadow: inset 0 8px 8px -8px #000, inset 0 -8px 8px -8px #000;
  -webkit-box-shadow: inset 0 8px 8px -8px #000, inset 0 -8px 8px -8px #000;
  box-shadow: inset 0 8px 8px -8px #000, inset 0 -8px 8px -8px #000;
}
/* line 765, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
nav ul.submenu_list a {
  color: #008BAC;
}
/* line 770, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
nav ul li {
  border-bottom: 1px solid white;
  position: relative;
}
/* line 774, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
nav ul li .plus_menu_deploy {
  position: absolute;
  top: 0;
  bottom: 0;
  right: 5%;
  margin: auto;
  height: 1.3em;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
}
/* line 787, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
nav ul li .plus_menu_deploy.active {
  -ms-transform: rotate(45deg);
  /* IE 9 */
  -webkit-transform: rotate(45deg);
  /* Chrome, Safari, Opera */
  transform: rotate(45deg);
}
/* line 794, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
nav ul li.active {
  -moz-box-shadow: inset 0 8px 8px -4px #ffffff, inset 0 -8px 8px -5px #ffffff;
  -webkit-box-shadow: inset 0 8px 8px -4px #ffffff, inset 0 -8px 8px -5px #ffffff;
  box-shadow: inset 0 8px 8px -4px #ffffff, inset 0 -8px 8px -5px #ffffff;
}
/* line 803, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
nav ul li a, nav ul li span {
  text-decoration: none;
  padding: 0.8em;
  font-size: 1.6em;
  width: 100%;
  font-family: 'Montserrat', sans-serif;
  display: block;
  color: white;
  padding-left: 0;
  text-align: center;
  text-transform: uppercase;
  font-weight: 500;
}

/* line 820, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.language {
  border-top: 0;
  text-decoration: none;
  padding: 2em 0 2em 9%;
  color: white;
  width: 95%;
  box-sizing: border-box;
  margin: auto;
  background: rgba(128, 128, 128, 0.7) url(/static_1/images/mobile_img/renovation/arrow_gray.png) no-repeat;
  background-position: 90% center;
  background-size: 1.5em;
  font-size: 1.3em;
}

/* line 834, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
ul.submenu_footer li {
  border-top: 1px solid #e2e4e7;
  background-position: 90%;
  background-repeat: no-repeat;
  padding: 2.187em 0 2.187em 9%;
}

/* line 841, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
ul.submenu_footer li a {
  font-size: 1.1em;
  text-decoration: none;
  color: white;
}

/* line 847, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
footer {
  background-color: #303636;
  padding: 1.5em 0;
}
/* line 851, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
footer .language {
  border-top: 0;
  text-decoration: none;
  padding: 2em 0 2em 9%;
  color: white;
  width: 95%;
  box-sizing: border-box;
  margin: auto;
  background: rgba(128, 128, 128, 0.3) url(/static_1/images/mobile_img/renovation/arrow_gray.png) no-repeat;
  background-position: 90% center;
  background-size: 1.5em;
  font-size: 1.3em;
}
/* line 866, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
footer .iconos span.icon-euro, footer .iconos span.icon-shield, footer .iconos span.icon-uniE603 {
  width: 5.312em;
  height: 5.312em;
  display: block;
  margin: auto;
  background-size: 5em !important;
}
/* line 873, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
footer .iconos span.icon-euro {
  background: url(/static_1/images/mobile_img/renovation/ticks/tick_eur.png) no-repeat center;
}
/* line 876, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
footer .iconos span.icon-shield {
  background: url(/static_1/images/mobile_img/renovation/ticks/tick_shield.png) no-repeat center;
}
/* line 879, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
footer .iconos span.icon-uniE603 {
  background: url(/static_1/images/mobile_img/renovation/ticks/tick_pig.png) no-repeat center;
}

/* line 885, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
footer ul.submenu_footer li {
  border-top: 1px solid #e2e4e7;
  background-position: 90%;
  background-repeat: no-repeat;
  padding: 2.187em 0 2.187em 9%;
}

/* line 892, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
footer ul li a {
  font-size: 1.1em;
  text-decoration: none;
  color: white;
}

/* line 906, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.submenu_footer {
  display: none;
  text-decoration: none;
  color: white;
  background: rgba(128, 128, 128, 0.3);
  font-size: 1.1em;
  width: 95%;
  box-sizing: border-box;
  margin: auto;
}
/* line 916, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.submenu_footer li {
  background-image: none;
}

/* line 921, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.desktop-version-link a {
  display: block;
  text-align: center;
  color: white;
  text-decoration: none;
  margin-top: 10px;
  margin-bottom: 10px;
  font-size: 1.3em;
}

/* Datapicker*/
/* line 932, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
body > #ui-datepicker-div {
  display: none;
}

/* line 936, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-datepicker table.ui-datepicker-calendar thead span {
  font-size: 1.4em;
}

/* line 940, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-datepicker td {
  border: 1px solid #eaeaea;
}
/* line 943, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-datepicker td:not(.ui-state-disabled) a {
  font-weight: 500;
}

/* line 949, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.datapicker_1, .datapicker_2 {
  display: none;
  overflow: hidden;
}
/* line 953, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.datapicker_1 .ui-widget-header, .datapicker_2 .ui-widget-header {
  background: none;
  border: 0 !important;
}
/* line 958, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.datapicker_1 .ui-corner-all, .datapicker_2 .ui-corner-all {
  border-radius: 0;
  padding: 0;
  border: 0;
}
/* line 964, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.datapicker_1 .ui-datepicker-title span, .datapicker_2 .ui-datepicker-title span {
  color: #222222;
  font-weight: 300;
}

/* line 970, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
td.ui-datepicker-highlighted {
  box-shadow: inset 0 0 10px #D8D4D4;
  border: 0;
}

/* line 975, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-datepicker-start_date {
  box-shadow: inset 0 0 10px #636363;
  border: 0 !important;
  background: #eaeaea;
}

/* line 981, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.loadig_gif_fancybox .fancybox-skin {
  background: white;
}

/* line 985, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.loading_booking_widget {
  background: #52ADCA;
  width: 90px;
  margin-bottom: 15px;
}

/* line 991, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-datepicker table {
  margin: 0;
}

/* line 995, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-widget-content {
  display: block;
  width: auto !important;
  text-align: center;
}

/* line 1001, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.calendar_1, .calendar_2 {
  background: url("/static_1/images/mobile_img/calendario.png") no-repeat;
  width: 40px;
  height: 40px;
  position: absolute;
  top: 5px;
  right: 1px;
  text-align: center;
}

/* line 1011, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-datepicker-title span {
  margin-left: 0;
}

/* line 1015, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.icon_right {
  background: url("/static_1/images/mobile_img/mas.png") no-repeat;
  background-color: #e8e8e8;
  background-position: center;
  background-size: 20px;
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  width: 5.93em;
  text-align: center;
}

/* line 1028, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.icon_left {
  background: url("/static_1/images/mobile_img/menos.png") no-repeat;
  background-color: #e8e8e8;
  background-position: center;
  background-size: 20px;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 5.93em;
  text-align: center;
}

/* line 1041, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-state-default, .ui-widget-content .ui-state-default, .ui-widget-header .ui-state-default {
  background: none;
  font-family: Circular, "Helvetica Neue", Helvetica, Arial, sans-serif;
  border: 0;
  color: #555555;
  text-align: center;
  height: 1.7em;
  width: 1.7em;
  line-height: 1.7em;
  font-size: 1.7em;
  margin: auto;
}

/* line 1054, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-datepicker .ui-datepicker-title {
  font-size: 1.5em;
  margin: 1em 2.3em;
}

/* line 1059, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-datepicker-current-day {
  background: #eaeaea;
}

/* line 1063, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-datepicker .ui-datepicker-prev {
  left: 10%;
  top: 32%;
}
/* line 1066, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-datepicker .ui-datepicker-prev span {
  background: url("/static_1/images/mobile_img/renovation/flecha_izq.png") no-repeat center;
}

/* line 1071, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-datepicker .ui-datepicker-next {
  right: 10%;
  top: 32%;
}
/* line 1074, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.ui-datepicker .ui-datepicker-next span {
  background: url("/static_1/images/mobile_img/renovation/flecha_der.png") no-repeat center;
}

/* sections */
/* line 1081, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.section {
  line-height: 1.25em;
}

/* line 1085, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.section h2 {
  font-size: 1.25em;
  margin-bottom: 20px;
}

/* line 1090, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.promotions-item h3 {
  font-size: 1.5em;
  line-height: 1.5em;
  font-weight: 100;
  margin-bottom: 20px;
}

/* line 1097, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.promotions-item img {
  display: block;
  margin: 0 auto;
  width: 100%;
}

/* line 1103, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.info .contInput label {
  line-height: 1.2em;
  padding-top: 8px;
}

/* line 1108, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.title {
  font-size: 0.8em;
}

/* line 1112, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.info {
  border: none;
}

/* line 1116, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#submit-button {
  font-size: 1.5em;
  padding: 3% 7%;
  margin: 0 auto;
  color: whitesmoke;
  display: block;
  text-align: center;
  text-transform: uppercase;
  border: none;
  margin-top: 1em;
  width: 100%;
}

/* line 1129, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#myLink {
  float: none !important;
}

/* line 1133, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.gallery {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* line 1139, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.gallery:after {
  clear: both;
  display: block;
  height: 0;
  visibility: hidden;
}

/* line 1146, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.gallery li {
  float: left;
  width: 33.2%;
}

/* line 1151, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.gallery li a {
  display: block;
  margin: 5px;
  border: 1px solid #3c3c3c;
}

/* line 1157, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.gallery li img {
  display: block;
  width: 100%;
  height: auto;
}

/* line 1163, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#photo-gallery {
  overflow: hidden;
}

/* line 1167, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#reservation {
  border: none;
}

/* line 1171, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#reservation + div {
  display: none;
  border: none;
  width: 50% !important;
  font-size: 10px;
  height: 100px;
  padding: 5px;
}

/* line 1180, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.section-title {
  font-family: 'Montserrat', sans-serif;
  font-weight: bolder;
  color: #52ADCA;
  font-size: 2.437em;
  margin: 0.3em 0;
  line-height: 1em;
  padding-top: 0.5em;
  text-align: center;
}

/* line 1191, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
[data-role="button"] {
  background-color: #008BAC;
  margin: 0 auto;
  width: 100%;
  border: none;
  border-radius: 0;
  color: white;
  font-size: 2.625em;
  font-weight: lighter;
  text-transform: uppercase;
  padding: 13px 0;
  font-family: 'Montserrat', sans-serif;
  -webkit-appearance: none;
  display: block;
  text-align: center;
  line-height: 1em;
}

/* line 1209, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#cancelButton {
  background-color: #324F55;
  margin: 0 auto;
  width: 100%;
  border: none;
  border-radius: 3px;
  color: #52ADCA;
  font-size: 1.5em;
  font-weight: lighter;
  text-transform: uppercase;
  padding: 13px 0;
  display: block;
  text-align: center;
  display: none;
}

/* line 1225, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#visitus {
  margin-top: 10px;
  text-align: center;
  padding: 0.625em 0;
}

/* line 1231, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#visitus span {
  width: 5.312em;
  height: 5.312em;
  display: inline-block;
  margin: auto 0.3em;
  background-size: 5em !important;
}
/* line 1238, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#visitus span.icon-facebook {
  background: url(/static_1/images/mobile_img/renovation/social/facebook.png) no-repeat center;
}
/* line 1242, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#visitus span.icon-instagram {
  background: url(/static_1/images/mobile_img/renovation/social/instagram.png) no-repeat center;
}
/* line 1246, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#visitus span.icon-twitter {
  background: url(/static_1/images/mobile_img/renovation/social/twitter.png) no-repeat center;
}
/* line 1250, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#visitus span.icon-youtube-square {
  background: url(/static_1/images/mobile_img/renovation/social/youtube.png) no-repeat center;
}
/* line 1254, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#visitus span.icon-flickr {
  background: url(/static_1/images/mobile_img/renovation/social/flickr.png) no-repeat center;
}
/* line 1258, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#visitus span.icon-google-plus {
  background: url(/static_1/images/mobile_img/renovation/social/plus.png) no-repeat center;
}

/* line 1264, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.paraty-tech {
  width: 65%;
  text-align: center;
  margin: auto;
}
/* line 1269, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.paraty-tech a {
  display: block;
  text-align: center;
  color: white;
  text-decoration: none;
  margin-top: 0.625em;
  margin-bottom: 0.625em;
  font-size: 1.3em;
}

/* line 1281, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#my-bookings-form #reservation {
  text-align: left;
  border: 3px solid #c8e3fd;
  background-color: #e3f1fe;
  margin: 10px 0;
}
/* line 1287, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#my-bookings-form #reservation .form-info {
  width: 94%;
  border: none;
  border-bottom: 3px solid #c8e3fd;
  background-color: #e3f1fe;
  padding: 10px 3%;
  margin: 5px 0;
  text-align: left;
}
/* line 1296, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#my-bookings-form #reservation .form-info:last-child {
  border-bottom: none;
}
/* line 1300, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#my-bookings-form #reservation .form-info span {
  margin-left: 0;
}
/* line 1303, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#my-bookings-form #reservation .form-info span div {
  background-color: #e3f1fe;
  margin: 0;
  border: none;
}
/* line 1310, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#my-bookings-form #reservation .form-info strong {
  font-weight: bold;
}
/* line 1314, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#my-bookings-form #reservation .form-info .search-item {
  margin-bottom: 0;
  min-height: 0;
  background-color: #e3f1fe;
}
/* line 1322, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#my-bookings-form #my-bookings-form-search-button {
  background-color: #52ADCA;
  margin: 0 auto;
  width: 100%;
  border: none;
  border-radius: 0;
  color: white;
  font-size: 2.625em;
  font-weight: lighter;
  text-transform: uppercase;
  padding: 13px 0;
  font-family: 'Montserrat', sans-serif;
  -webkit-appearance: none;
  display: block;
  text-align: center;
  line-height: 1em;
}

/*========== Contact ========*/
/* line 1343, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.info div {
  border: none;
}
/* line 1346, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.info div label {
  position: relative;
  padding: 0;
}
/* line 1350, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.info div #privacity {
  display: block;
  width: 5%;
  float: left;
  height: 10%;
}
/* line 1356, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.info div span {
  display: block;
  width: 95%;
  float: left;
  margin: 0;
  position: relative;
  top: -3px;
}

/*============ Web Support Mobile ============*/
@font-face {
  font-family: 'icomoon';
  src: url("/static_1/fonts/mobile_icons/icomoon.eot?excc31");
  src: url("/static_1/fonts/mobile_icons/icomoon.eot?#iefixexcc31") format("embedded-opentype"), url("/static_1/fonts/mobile_icons/icomoon.woff?excc31") format("woff"), url("/static_1/fonts/mobile_icons/icomoon.ttf?excc31") format("truetype"), url("/static_1/fonts/mobile_icons/icomoon.svg?excc31#icomoon") format("svg");
  font-weight: normal;
  font-style: normal;
}
/* line 1375, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.wrapper-new-web-support {
  color: #008BAC;
  display: table;
  margin: auto;
  margin-bottom: 0 !important;
  font-size: 2em;
  text-decoration: underline;
  margin-top: 0.5em;
}
/* line 1384, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.wrapper-new-web-support .web_support_number {
  white-space: nowrap;
}
/* line 1388, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.wrapper-new-web-support div {
  margin: auto;
  text-align: center;
  width: 100%;
}
/* line 1394, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.wrapper-new-web-support .web_support_label_2:before, .wrapper-new-web-support .web_support_label_1:before {
  content: "\e600";
  font-family: 'icomoon';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  display: inline-block;
  margin-right: 2px;
  border: 1px solid;
  border-radius: 30px;
  font-size: 15px;
  padding: 5px;
}
/* line 1411, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.wrapper-new-web-support .web_support_label_1_special {
  line-height: 2em;
}

/*============ My Reservation Mobile ============*/
/* line 1418, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my_reservation_section h1.section-title {
  color: #52ADCA;
}
/* line 1422, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my_reservation_section .default_reservation_text {
  font-size: 1.9em;
  line-height: 1.4em;
  font-weight: lighter;
  padding-bottom: 1em;
  text-align: center;
  margin-top: 2em;
}

/* line 1432, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form {
  margin-top: 1.5em;
}
/* line 1435, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form input {
  height: 2.5em;
  background: #eaeaea;
  width: 100%;
  box-sizing: border-box;
  border: 0;
  margin-bottom: 1em;
  padding: 1.6em 2em;
  font-size: 2em;
}
/* line 1445, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form input::-webkit-input-placeholder {
  font-weight: lighter;
  color: gray;
}
/* line 1451, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form #reservation {
  text-align: left;
  border: 3px solid #eaeaea;
  margin: 10px 0;
}
/* line 1457, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form div {
  border: none;
  background-color: white;
}
/* line 1462, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .form-info {
  width: 94%;
  border-bottom: 3px solid #969696;
  background-color: #eaeaea;
  padding: 10px 3%;
  margin: 5px 0;
}
/* line 1469, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .form-info:last-child {
  border-bottom: 3px solid #969696;
}
/* line 1473, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .form-info span {
  margin-left: 0;
}
/* line 1476, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .form-info span div {
  background-color: #e3f1fe;
  margin: 0;
}
/* line 1482, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .form-info strong {
  font-weight: bold;
}
/* line 1486, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .form-info .search-item {
  margin-bottom: 0;
  min-height: 0;
  background-color: #eaeaea;
}
/* line 1493, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form #cancelButton {
  font-family: inherit;
  background-color: inherit;
  margin: inherit;
  width: 100%;
  border: none;
  border-radius: 3px;
  color: inherit;
  font-size: inherit;
  font-weight: inherit;
  text-transform: uppercase;
  padding: 0;
  text-align: center;
}
/* line 1508, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .selectricHideSelect, .my-reservation-form .selectric-hide-select {
  display: none;
}
/* line 1512, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .selectric {
  display: block;
  position: relative;
  min-height: 5.93em;
  background: white;
  padding: 0 2em;
  box-sizing: border-box;
  text-align: center;
  border: 0.5em solid #F9F9F9;
}
/* line 1522, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .selectric p.label, .my-reservation-form .selectric span.label {
  float: left;
  line-height: 1em;
  color: #333;
  font-size: 1.87em;
  position: absolute;
  top: 0;
  bottom: 0;
  display: table;
  margin: auto;
}
/* line 1534, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .selectric b.button {
  float: right;
  position: absolute;
  right: 0;
  top: 0;
  color: #eaeaea;
  background: #eaeaea url(/static_1/images/mobile_img/renovation/arrow_darkgray.png) no-repeat center;
  background-size: 1.5em;
  height: 100%;
  width: 4.8em;
}
/* line 1547, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .selectricItems, .my-reservation-form .selectric-items {
  width: 100% !important;
  max-height: 0;
  height: auto !important;
  overflow: hidden;
  -webkit-transition: all 0.5s;
  -moz-transition: all 0.5s;
  -ms-transition: all 0.5s;
  -o-transition: all 0.5s;
  transition: all 0.5s;
  margin-top: -1.5em;
}
/* line 1559, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .selectricItems ul li, .my-reservation-form .selectric-items ul li {
  text-align: left;
  display: block;
  position: relative;
  font-size: 1.87em;
  background: white;
  padding: 0.9em 1.2em;
  box-sizing: border-box;
  border: 0.2em solid #F9F9F9;
  border-top: 0;
  color: #333;
  margin: 0;
}
/* line 1572, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .selectricItems ul li.selected, .my-reservation-form .selectric-items ul li.selected {
  background: #008BAC;
  color: white;
}
/* line 1577, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .selectricItems ul li.selectric-group-label, .my-reservation-form .selectric-items ul li.selectric-group-label {
  background: #52ADCA;
  color: white;
}
/* line 1584, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .selectricInput, .my-reservation-form .selectric-input {
  display: none;
}
/* line 1588, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .selectricWrapper.selectricOpen .selectricItems, .my-reservation-form .selectricWrapper.selectricOpen .selectric-items, .my-reservation-form .selectric-wrapper.selectric-hover .selectricItems, .my-reservation-form .selectric-wrapper.selectric-hover .selectric-items {
  max-height: 1000px !important;
}
/* line 1593, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-reservation-form .selectric {
  margin-bottom: 1.5em;
}

/* line 1598, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-bookings-booking-info {
  max-width: 100%;
}
/* line 1601, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-bookings-booking-info .fResumenReserva {
  max-width: 100%;
  box-sizing: border-box;
  border: 0;
  background: none;
}
/* line 1607, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.my-bookings-booking-info .fResumenReserva .mt15 {
  color: #52ADCA;
  font-size: 2em;
}

/* line 1614, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.flexslider .slides {
  height: 26.87em;
  position: relative;
  overflow: hidden;
}

/* line 1623, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.blocks_section_container .block_activity_title {
  line-height: 25px;
}
/* line 1627, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.blocks_section_container .main-block-image {
  width: 100%;
}
/* line 1631, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.blocks_section_container .block_description {
  height: 60px;
  overflow: hidden;
}
/* line 1636, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.blocks_section_container .button-block-more {
  font-size: 1.2em;
  border: none;
  padding: 3% 0;
  background-color: #52ADCA;
  margin: 0 auto;
  color: white !important;
  display: block;
  width: 100%;
  text-align: center;
  text-transform: uppercase;
  font-weight: lighter !important;
}

/*==== Contact footer =====*/
/* line 1652, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.contact_mobile {
  background: #008BAC;
  color: white;
  text-align: center;
  padding: 2.187em 0;
  font-family: 'Montserrat', sans-serif;
  margin-top: 10px;
}
/* line 1660, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.contact_mobile h3.contact_title {
  font-size: 2em;
  text-transform: uppercase;
}
/* line 1665, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.contact_mobile .info {
  margin: 2.6em 0 0;
}
/* line 1668, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.contact_mobile .info a {
  text-decoration: none;
}
/* line 1672, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.contact_mobile .info span.mail_contact, .contact_mobile .info .phone_contact, .contact_mobile .info .phone_web_support {
  text-align: center;
  display: block;
  font-weight: 100;
  font-size: 1.6em;
  font-family: 'Source Sans Pro', sans-serif;
  margin-bottom: 0.4em;
  color: white;
}

/*=== Gallery ===*/
/* line 1686, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.gallery_1 li img {
  width: 100%;
}

/*==== Config banners blocks =====*/
/* line 1692, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.banners_advanced_bottom {
  margin-top: 1.5em;
}
/* line 1694, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.banners_advanced_bottom .banner_bottom_element {
  position: relative;
  width: 100%;
  margin-bottom: 1em;
  overflow: hidden;
}
/* line 1700, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.banners_advanced_bottom .banner_bottom_element:before {
  content: "";
  display: block;
  padding-top: 80%;
  /* initial ratio of 1:1*/
}
/* line 1706, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.banners_advanced_bottom .banner_bottom_element .banner_bottom_overlay {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1;
}
/* line 1716, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.banners_advanced_bottom .banner_bottom_element .banner_bottom_background {
  position: absolute;
  left: -100%;
  margin: auto;
  max-width: none;
  top: -100%;
  right: -100%;
  bottom: -100%;
  min-height: 100%;
  min-width: 100%;
}
/* line 1728, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.banners_advanced_bottom .banner_bottom_element .banner_bottom_title {
  position: absolute;
  top: 0;
  bottom: 0;
  display: table;
  width: 100%;
  margin: auto;
  text-align: center;
  color: white;
  font-family: 'Montserrat', sans-serif;
  font-size: 2.6em;
  line-height: 1.4em;
  z-index: 2;
}

/*===== Navigation menú ====*/
/* line 1746, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
nav#menu {
  background: #008BAC;
  /* For browsers that do not support gradients */
  background: -webkit-linear-gradient(#008BAC, #00b8de);
  /* For Safari 5.1 to 6.0 */
  background: -o-linear-gradient(#008BAC, #00b8de);
  /* For Opera 11.1 to 12.0 */
  background: -moz-linear-gradient(#008BAC, #00b8de);
  /* For Firefox 3.6 to 15 */
  background: linear-gradient(#008bac, #00b8de);
  position: fixed;
  top: 7em;
  width: 100%;
  z-index: 200;
  display: none;
  box-shadow: 0 13px 44px -10px black;
}

/*============== Bottom Pop-up ============*/
/* line 1761, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.bottom_popup {
  position: fixed;
  width: 100%;
  height: auto;
  background: #c7e5ee;
  left: 0;
  bottom: 0;
  z-index: 1000;
}

/* line 1771, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.bottom_popup #wrapper2 img {
  position: relative;
  float: left;
  width: 185px;
}

/* line 1777, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.bottom_popup .bottom_popup_text {
  width: 100%;
  color: white;
  font-family: Arial, sans-serif;
  font-size: 14px;
  padding: 10px 5%;
  box-sizing: border-box;
  line-height: 21px;
  text-align: center;
}

/* line 1788, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.bottom_popup .bottom_popup_text p {
  padding: 10px;
}

/* line 1792, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.close_button {
  float: right;
  cursor: pointer;
}

/* line 1797, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
button.bottom_popup_button {
  width: 120px;
  background: white;
  border: 0;
  height: 36px;
  color: #008BAC;
  background-position: center;
  border-radius: 5px;
  cursor: pointer;
  bottom: 12px;
  font-size: 16px;
  margin: 5px auto 20px;
  display: block;
}

/* line 1812, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#wrapper2 {
  width: 100%;
  margin: 0 auto;
}

/* line 1817, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#popup_inicial {
  width: 100%;
  height: 100%;
  background-size: cover !important;
}
/* line 1822, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#popup_inicial .email, #popup_inicial .discount, #popup_inicial .compra {
  text-align: center;
}
/* line 1825, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#popup_inicial .compra {
  padding-top: 15px;
  color: white;
  font-size: 18px;
  line-height: 18px;
  font-weight: lighter;
}
/* line 1832, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#popup_inicial .discount {
  padding-top: 15px;
  color: white;
  font-size: 18px;
  line-height: 18px;
  text-shadow: 3px 3px black;
  text-transform: uppercase;
  font-family: 'Oswald', sans-serif;
}
/* line 1841, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#popup_inicial .email {
  padding-top: 39px;
  color: white;
  font-size: 18px;
  line-height: 18px;
  font-weight: lighter;
}
/* line 1848, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#popup_inicial form.form_popup {
  text-align: center;
  padding-top: 50px;
}
/* line 1851, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#popup_inicial form.form_popup li {
  text-align: center;
}
/* line 1854, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#popup_inicial form.form_popup input#id_email {
  height: 26px;
  text-align: center;
  width: 245px;
  font-size: 17px;
  box-shadow: 2px 2px black;
  border: 0px;
  color: #008BAC;
}
/* line 1863, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#popup_inicial form.form_popup button.popup_button {
  margin: 7px auto 0px;
  width: 245px;
  height: 40px;
  background: #008BAC;
  font-size: 17px;
  border: 0px;
  text-transform: uppercase;
  color: white;
  cursor: pointer;
}
/* line 1875, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#popup_inicial .spinner_wrapper_faldon {
  padding-top: 20px;
}
/* line 1878, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
#popup_inicial .popup_message {
  color: white;
  padding-top: 25px;
  font-size: 18px;
  line-height: 18px;
  font-weight: lighter;
}

/* line 1888, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.fancy_popup_mobile #fancybox-loading, .fancy_popup_mobile .fancybox-close, .fancy_popup_mobile .fancybox-prev span, .fancy_popup_mobile .fancybox-next span {
  background-image: url("/static_1/lib/fancybox/fancybox_sprite.png");
}

/* Mini Gallery */
/* line 1895, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.flexslider_mini_gallery .slides {
  height: 32em;
}
/* line 1898, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.flexslider_mini_gallery .slides li {
  height: 27em;
  overflow: hidden;
}
/* line 1902, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.flexslider_mini_gallery .slides li img {
  width: auto;
  height: 100%;
  max-width: none;
  position: absolute;
  left: -100%;
  right: -100%;
  top: -100%;
  bottom: -100%;
  margin: auto;
  min-height: 100%;
  min-width: 100%;
}

/* line 1920, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.dest_wrapper {
  position: relative;
}
/* line 1923, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.dest_wrapper .slides {
  height: 32em;
}
/* line 1926, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.dest_wrapper .slides li {
  height: 27em;
  overflow: hidden;
}
/* line 1930, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.dest_wrapper .slides li iframe {
  width: 100%;
  height: 100%;
}
/* line 1937, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.dest_wrapper img {
  width: 100%;
  min-height: 100%;
}
/* line 1942, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.dest_wrapper .title-dest {
  font-family: 'Montserrat', sans-serif;
  font-weight: bolder;
  color: #444444;
  font-size: 2.437em;
  margin: 0.3em 0 1em;
  line-height: 1em;
}
/* line 1951, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.dest_wrapper .desc-dest {
  font-size: 1.9em;
  line-height: 1.4em;
  font-weight: lighter;
  padding-bottom: 1em;
}

/* line 1960, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.landing_section_mobile {
  margin-top: 8em;
}
/* line 1963, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.landing_section_mobile .section_title {
  font-family: 'Montserrat', sans-serif;
  font-weight: bolder;
  color: #52ADCA;
  font-size: 2.437em;
  margin: 0.3em 0;
  line-height: 1em;
  padding-top: 0.5em;
  text-align: center;
}
/* line 1973, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.landing_section_mobile .section_title:first-letter {
  text-transform: uppercase;
}
/* line 1979, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.landing_section_mobile .section-subtitle p {
  width: auto !important;
  text-align: center !important;
  color: black !important;
  margin: auto !important;
  line-height: 1em;
}
/* line 1989, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.landing_section_mobile .section-content div {
  width: auto !important;
  text-align: center !important;
  color: black !important;
  margin: auto !important;
}
/* line 1995, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.landing_section_mobile .section-content div span {
  font-weight: normal !important;
  font-size: 18px !important;
}
/* line 2000, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.landing_section_mobile .section-content div * {
  color: black !important;
}
/* line 2006, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.landing_section_mobile .booking_general_button {
  margin-top: 2em;
  margin-bottom: 2em;
}
/* line 2011, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.landing_section_mobile form.paraty-booking-form input[name="promocode"] {
  display: block;
}

/*=== Legal Text ===*/
/* line 2017, ../../mobile_design_2/sass/_styles_mobile_base_2.scss */
.legal_text_wrapper {
  text-align: center;
  font-size: 1.3em;
  display: block;
  color: white;
  padding: 0 1em;
  margin: 1.5em 0;
  line-height: 1.5em;
}

/* line 2, ../../mobile_design_2/sass/_styles_mobile_base_2_personalized.scss */
form.paraty-booking-form .start_date_wrapper, form.paraty-booking-form .end_date_wrapper {
  background: #52ADCA;
}

/* line 7, ../../mobile_design_2/sass/_styles_mobile_base_2_personalized.scss */
form.paraty-booking-form input[type="submit"], form.paraty-booking-form .selectricItems ul li.selected, .contact_mobile, #simple-menu img {
  background: #52ADCA;
}

/* line 11, ../../mobile_design_2/sass/_styles_mobile_base_2_personalized.scss */
nav#menu {
  background: #52ADCA;
  /* For browsers that do not support gradients */
  background: -webkit-linear-gradient(#52ADCA, #00b8de);
  /* For Safari 5.1 to 6.0 */
  background: -o-linear-gradient(#52ADCA, #00b8de);
  /* For Opera 11.1 to 12.0 */
  background: -moz-linear-gradient(#52ADCA, #00b8de);
  /* For Firefox 3.6 to 15 */
  background: linear-gradient(#52adca, #00b8de);
  position: fixed;
  top: 7em;
  width: 100%;
  z-index: 200;
  display: none;
  box-shadow: 0 13px 44px -10px black;
}

/* line 25, ../../mobile_design_2/sass/_styles_mobile_base_2_personalized.scss */
.wrapper-new-web-support {
  color: #52ADCA;
}

/* line 29, ../../mobile_design_2/sass/_styles_mobile_base_2_personalized.scss */
header .contact img {
  background: #52ADCA;
}

/* line 34, ../../mobile_design_2/sass/_styles_mobile_base_2_personalized.scss */
.normal_section_mobile h2.section_title {
  color: #52ADCA;
}

/* line 39, ../../mobile_design_2/sass/_styles_mobile_base_2_personalized.scss */
.my-reservation-form .selectricItems ul li.selected {
  background: #52ADCA;
}

/* line 43, ../../mobile_design_2/sass/_styles_mobile_base_2_personalized.scss */
.room_element .see_more_room {
  color: #52ADCA !important;
}

/* line 47, ../../mobile_design_2/sass/_styles_mobile_base_2_personalized.scss */
.booking_general_button div.button {
  background: #52ADCA;
}

/* line 51, ../../mobile_design_2/sass/_styles_mobile_base_2_personalized.scss */
.promotions-item .promotion_name {
  color: #52ADCA !important;
}

{{ content|safe }}

<form id="promocodeForm" action="/utils?action=requestPromcode" method="post">
	<fieldset>
		<p>
			<label for="name">{{ T_nombre_y_apellidos }}</label>
			<input type="text" id="name" name="name" value="" />
		</p>
		<p>
			<label for="email">{{ T_email }}</label>
			<input type="text" id="email" name="email" value="" />
		</p>
		<p>
			<button type="submit">{{ T_enviar }}</button>
		</p>
	</fieldset>
</form>

<script type="text/javascript" src="/static_1/lib/jquery.validate.js"></script>
<script type="text/javascript" charset="utf-8">

$(document).ready(function(){

	$("#promocodeForm").validate({
		rules: {
			name: "required",
			email: {
				required: true,
				email: true
			},
		},
		messages: {
			name: "{{ T_campo_obligatorio|safe }}",
			email: "{{ T_campo_valor_invalido|safe }}"
		},
		submitHandler: function(form){
			$.post("/utils?action=requestPromcode",
				{
					'email': $("#email").val(),
					'name': $("#name").val()
				},
				
				function(data) {
					alert("{{ T_gracias_contacto }}");
					$("#name").val("");
					$("#email").val("");
				}
			);
		}
	});

});

</script>
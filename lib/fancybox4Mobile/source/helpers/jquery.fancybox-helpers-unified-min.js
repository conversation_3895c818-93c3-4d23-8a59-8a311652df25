(function(e){var d=e.fancybox;d.helpers.buttons={defaults:{skipSingle:!1,position:"top",tpl:'<div id="fancybox-buttons"><ul><li><a class="btnPrev" title="Previous" href="javascript:;"></a></li><li><a class="btnPlay" title="Start slideshow" href="javascript:;"></a></li><li><a class="btnNext" title="Next" href="javascript:;"></a></li><li><a class="btnToggle" title="Toggle size" href="javascript:;"></a></li><li><a class="btnClose" title="Close" href="javascript:;"></a></li></ul></div>'},list:null,buttons:null,
beforeLoad:function(a,c){a.skipSingle&&2>c.group.length?(c.helpers.buttons=!1,c.closeBtn=!0):c.margin["bottom"===a.position?2:0]+=30},onPlayStart:function(){this.buttons&&this.buttons.play.attr("title","Pause slideshow").addClass("btnPlayOn")},onPlayEnd:function(){this.buttons&&this.buttons.play.attr("title","Start slideshow").removeClass("btnPlayOn")},afterShow:function(a,c){var b=this.buttons;b||(this.list=e(a.tpl).addClass(a.position).appendTo("body"),b={prev:this.list.find(".btnPrev").click(d.prev),
next:this.list.find(".btnNext").click(d.next),play:this.list.find(".btnPlay").click(d.play),toggle:this.list.find(".btnToggle").click(d.toggle),close:this.list.find(".btnClose").click(d.close)});0<c.index||c.loop?b.prev.removeClass("btnDisabled"):b.prev.addClass("btnDisabled");c.loop||c.index<c.group.length-1?(b.next.removeClass("btnDisabled"),b.play.removeClass("btnDisabled")):(b.next.addClass("btnDisabled"),b.play.addClass("btnDisabled"));this.buttons=b;this.onUpdate(a,c)},onUpdate:function(a,c){if(this.buttons){var b=
this.buttons.toggle.removeClass("btnDisabled btnToggleOn");c.canShrink?b.addClass("btnToggleOn"):c.canExpand||b.addClass("btnDisabled")}},beforeClose:function(){this.list&&this.list.remove();this.buttons=this.list=null}}})(jQuery);
(function(e){var d=function(a,c,b){b=b||"";"object"===e.type(b)&&(b=e.param(b,!0));e.each(c,function(b,c){a=a.replace("$"+b,c||"")});b.length&&(a+=(0<a.indexOf("?")?"&":"?")+b);return a};e.fancybox.helpers.media={defaults:{youtube:{matcher:/(youtube\.com|youtu\.be|youtube-nocookie\.com)\/(watch\?v=|v\/|u\/|embed\/?)?(videoseries\?list=(.*)|[\w-]{11}|\?listType=(.*)&list=(.*)).*/i,params:{autoplay:1,autohide:1,fs:1,rel:0,hd:1,wmode:"opaque",enablejsapi:1},type:"iframe",url:"//www.youtube.com/embed/$3"},
vimeo:{matcher:/(?:vimeo(?:pro)?.com)\/(?:[^\d]+)?(\d+)(?:.*)/,params:{autoplay:1,hd:1,show_title:1,show_byline:1,show_portrait:0,fullscreen:1},type:"iframe",url:"//player.vimeo.com/video/$1"},metacafe:{matcher:/metacafe.com\/(?:watch|fplayer)\/([\w\-]{1,10})/,params:{autoPlay:"yes"},type:"swf",url:function(a,c,b){b.swf.flashVars="playerVars="+e.param(c,!0);return"//www.metacafe.com/fplayer/"+a[1]+"/.swf"}},dailymotion:{matcher:/dailymotion.com\/video\/(.*)\/?(.*)/,params:{additionalInfos:0,autoStart:1},
type:"swf",url:"//www.dailymotion.com/swf/video/$1"},twitvid:{matcher:/twitvid\.com\/([a-zA-Z0-9_\-\?\=]+)/i,params:{autoplay:0},type:"iframe",url:"//www.twitvid.com/embed.php?guid=$1"},twitpic:{matcher:/twitpic\.com\/(?!(?:place|photos|events)\/)([a-zA-Z0-9\?\=\-]+)/i,type:"image",url:"//twitpic.com/show/full/$1/"},instagram:{matcher:/(instagr\.am|instagram\.com)\/p\/([a-zA-Z0-9_\-]+)\/?/i,type:"image",url:"//$1/p/$2/media/?size=l"},google_maps:{matcher:/maps\.google\.([a-z]{2,3}(\.[a-z]{2})?)\/(\?ll=|maps\?)(.*)/i,
type:"iframe",url:function(a){return"//maps.google."+a[1]+"/"+a[3]+""+a[4]+"&output="+(0<a[4].indexOf("layer=c")?"svembed":"embed")}}},beforeLoad:function(a,c){var b=c.href||"",k=!1,g,h;for(g in a)if(a.hasOwnProperty(g)){var f=a[g];if(h=b.match(f.matcher)){k=f.type;b=e.extend(!0,{},f.params,c[g]||(e.isPlainObject(a[g])?a[g].params:null));b="function"===e.type(f.url)?f.url.call(this,h,b,c):d(f.url,h,b);break}}k&&(c.href=b,c.type=k,c.autoHeight=!1)}}})(jQuery);
(function(e){e.fancybox.helpers.thumbs={defaults:{width:50,height:50,position:"bottom",source:function(d){var a;d.element&&(a=e(d.element).find("img").attr("src"));!a&&"image"===d.type&&d.href&&(a=d.href);return a}},wrap:null,list:null,width:0,init:function(d,a){var c=this,b=d.width,k=d.height,g=d.source;var h="";for(var f=0;f<a.group.length;f++)h+='<li><a style="width:'+b+"px;height:"+k+'px;" href="javascript:jQuery.fancybox.jumpto('+f+');"></a></li>';this.wrap=e('<div id="fancybox-thumbs"></div>').addClass(d.position).appendTo("body");
this.list=e("<ul>"+h+"</ul>").appendTo(this.wrap);e.each(a.group,function(d){var f=g(a.group[d]);f&&e("<img />").load(function(){var a=this.width,f=this.height;if(c.list&&a&&f){var g=a/b;var h=f/k;var l=c.list.children().eq(d).find("a");1<=g&&1<=h&&(g>h?(a=Math.floor(a/h),f=k):(a=b,f=Math.floor(f/g)));e(this).css({width:a,height:f,top:Math.floor(k/2-f/2),left:Math.floor(b/2-a/2)});l.width(b).height(k);e(this).hide().appendTo(l).fadeIn(300)}}).attr("src",f)});this.width=this.list.children().eq(0).outerWidth(!0);
this.list.width(this.width*(a.group.length+1)).css("left",Math.floor(.5*e(window).width()-(a.index*this.width+.5*this.width)))},beforeLoad:function(d,a){2>a.group.length?a.helpers.thumbs=!1:a.margin["top"===d.position?0:2]+=d.height+15},afterShow:function(d,a){if(this.list)this.onUpdate(d,a);else this.init(d,a);this.list.children().removeClass("active").eq(a.index).addClass("active")},onUpdate:function(d,a){this.list&&this.list.stop(!0).animate({left:Math.floor(.5*e(window).width()-(a.index*this.width+
.5*this.width))},150)},beforeClose:function(){this.wrap&&this.wrap.remove();this.list=this.wrap=null;this.width=0}}})(jQuery);
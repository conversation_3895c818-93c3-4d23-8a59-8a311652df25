#!/usr/bin/env python3
"""
Debug the specific quote escaping issue for JavaScript template literals
"""

import json
from utils.compatibility.compatibility_utils import dumps_json_for_javascript

def debug_quote_escaping():
    """Debug the quote escaping issue step by step"""
    
    test_data = {"property_name": '<PERSON><PERSON> balinesa "Fresquito" '}
    
    print("=== Debug Quote Escaping Issue ===")
    print(f"Input: {test_data}")
    
    # Step 1: Current function output
    current_result = dumps_json_for_javascript(test_data)
    print(f"\nCurrent function output: {current_result}")
    print(f"Current function repr: {repr(current_result)}")
    
    # Step 2: Test Python json.loads
    try:
        parsed = json.loads(current_result)
        print(f"✓ Python json.loads works: {parsed}")
    except Exception as e:
        print(f"✗ Python json.loads failed: {e}")
    
    # Step 3: Simulate Django template rendering
    print(f"\n=== Django Template Simulation ===")
    django_template = f"JSON.parse(`{current_result}`)"
    print(f"Django template generates: {django_template}")
    
    # Step 4: What JavaScript would see
    print(f"\n=== What JavaScript Sees ===")
    print("When Django renders the template, JavaScript sees:")
    print(f"JSON.parse(`{current_result}`)")
    print("\nThe issue: The quotes inside the template literal are not properly escaped!")
    
    # Step 5: What we need
    print(f"\n=== What We Need ===")
    # For JavaScript template literals, quotes need to be double-escaped
    needed_result = current_result.replace('\\"', '\\\\"')
    print(f"Needed output: {needed_result}")
    print(f"Needed repr: {repr(needed_result)}")
    
    # Test if the needed result works
    django_template_needed = f"JSON.parse(`{needed_result}`)"
    print(f"Django template would generate: {django_template_needed}")
    
    # Test if it's still valid JSON
    try:
        parsed_needed = json.loads(needed_result)
        print(f"✓ Python json.loads still works: {parsed_needed}")
    except Exception as e:
        print(f"✗ Python json.loads failed with needed format: {e}")

def test_javascript_simulation():
    """Simulate what happens in JavaScript"""
    
    print(f"\n=== JavaScript Simulation ===")
    
    test_data = {"property_name": 'Cama balinesa "Fresquito" '}
    
    # Current approach
    current_result = dumps_json_for_javascript(test_data)
    print(f"Current result: {current_result}")
    
    # What Django template literal would produce
    print(f"\nDjango template literal: JSON.parse(`{current_result}`)")
    
    # The problem: In JavaScript template literals, \" becomes just "
    # So JSON.parse(`{"property_name":"Cama balinesa \"Fresquito\" "}`) 
    # becomes JSON.parse(`{"property_name":"Cama balinesa "Fresquito" "}`)
    # which is invalid JSON because of unescaped quotes
    
    print("\nThe problem:")
    print("1. Django renders: JSON.parse(`{\"property_name\":\"Cama balinesa \\\"Fresquito\\\" \"}`)")
    print("2. JavaScript sees: JSON.parse(`{\"property_name\":\"Cama balinesa \"Fresquito\" \"}`)")
    print("3. This breaks because quotes are not escaped for JSON inside the template literal")
    
    # Solution: Double-escape quotes
    fixed_result = current_result.replace('\\"', '\\\\"')
    print(f"\nFixed result: {fixed_result}")
    print(f"Django template literal: JSON.parse(`{fixed_result}`)")
    print("This should work because \\\\\" becomes \\\" in JavaScript, which is valid JSON")

if __name__ == "__main__":
    debug_quote_escaping()
    test_javascript_simulation()

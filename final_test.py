#!/usr/bin/env python3
"""
Final comprehensive test of the fixed dumps_json_for_javascript function
"""

import json
from utils.compatibility.compatibility_utils import dumps_json_for_javascript

def test_original_problem():
    """Test the original problem that was reported"""
    print("=== Testing Original Problem ===")
    
    # Original problematic case
    test_data = {"property_name": '<PERSON><PERSON> balinesa "Fresquito" '}
    
    print(f"Input: {test_data}")
    
    result = dumps_json_for_javascript(test_data)
    print(f"Output: {result}")
    
    # Test 1: Python json.loads should work
    try:
        parsed = json.loads(result)
        print("✓ Python json.loads: SUCCESS")
        print(f"  Parsed data: {parsed}")
        
        if parsed == test_data:
            print("✓ Data integrity: PRESERVED")
        else:
            print("⚠ Data integrity: MODIFIED (expected for safety)")
            
    except Exception as e:
        print(f"✗ Python json.loads: FAILED - {e}")
        return False
    
    # Test 2: Django template safety
    django_template = f"JSON.parse(`{result}`)"
    print(f"Django template generates: {django_template}")
    
    # Check for safety issues
    issues = []
    if '`' in result and result.count('`') != result.count('\\`'):
        issues.append("Unescaped backticks")
    if '${' in result and result.count('${') != result.count('\\${'):
        issues.append("Unescaped template expressions")
    if '\n' in result or '\t' in result:
        issues.append("Unescaped newlines/tabs")
    
    if issues:
        print(f"✗ Django template safety: FAILED - {', '.join(issues)}")
        return False
    else:
        print("✓ Django template safety: PASSED")
    
    return True

def test_comprehensive_cases():
    """Test comprehensive edge cases"""
    print("\n=== Testing Comprehensive Cases ===")
    
    test_cases = [
        {
            "name": "Quotes only",
            "data": {"text": 'Text with "quotes"'},
            "expect_modification": False
        },
        {
            "name": "Backticks only",
            "data": {"text": "Text with `backticks`"},
            "expect_modification": True
        },
        {
            "name": "Template expressions only",
            "data": {"text": "Text with ${expr}"},
            "expect_modification": True
        },
        {
            "name": "Newlines and tabs",
            "data": {"text": "Text with\nnewlines\tand\ttabs"},
            "expect_modification": True  # Whitespace gets replaced
        },
        {
            "name": "Mixed dangerous content",
            "data": {"text": 'Mixed "quotes" `backticks` ${expr}\nnewlines'},
            "expect_modification": True
        },
        {
            "name": "Safe content",
            "data": {"text": "Normal safe text", "number": 123, "bool": True},
            "expect_modification": False
        }
    ]
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test {i}: {test_case['name']} ---")
        
        result = dumps_json_for_javascript(test_case['data'])
        
        # Test JSON validity
        try:
            parsed = json.loads(result)
            print("✓ Valid JSON")
        except Exception as e:
            print(f"✗ Invalid JSON: {e}")
            all_passed = False
            continue
        
        # Test Django template safety
        safety_issues = []
        if '`' in result and result.count('`') != result.count('\\`'):
            safety_issues.append("backticks")
        if '${' in result and result.count('${') != result.count('\\${'):
            safety_issues.append("template expressions")
        if '\n' in result or '\t' in result:
            safety_issues.append("newlines/tabs")
        
        if safety_issues:
            print(f"✗ Safety issues: {', '.join(safety_issues)}")
            all_passed = False
        else:
            print("✓ Django template safe")
    
    return all_passed

def main():
    """Run all tests"""
    print("Testing Fixed dumps_json_for_javascript Function")
    print("=" * 50)
    
    # Test original problem
    original_passed = test_original_problem()
    
    # Test comprehensive cases
    comprehensive_passed = test_comprehensive_cases()
    
    # Final result
    print("\n" + "=" * 50)
    if original_passed and comprehensive_passed:
        print("🎉 ALL TESTS PASSED!")
        print("✓ Original problem is fixed")
        print("✓ Function is safe for Django templates")
        print("✓ Function handles all edge cases correctly")
    else:
        print("❌ SOME TESTS FAILED")
    print("=" * 50)

if __name__ == "__main__":
    main()

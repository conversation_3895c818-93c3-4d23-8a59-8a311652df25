($ => {
    if (['interactive', 'complete'].indexOf(document.readyState) < 0) {
        document.addEventListener("DOMContentLoaded", function () {
            _set_datepicker_regional($);
        });
    } else {
        _set_datepicker_regional($);
    }

    function _set_datepicker_regional($){
        $.datepicker.regional['ja'] = {
            closeText: 'Done',
            prevText: 'Prev',
            nextText: 'Next',
            currentText: 'Today',
            preposition: '',
            monthNames: ['1月','2月','3月','4月','5月','6月',
            '7月','8月','9月','10月','11月','12月'],
            monthNamesShort: ['1月','2月','3月','4月','5月','6月',
            '7月','8月','9月','10月','11月','12月'],
            dayNames: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
            dayNamesShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
            dayNamesMin: ['Su','Mo','Tu','We','Th','Fr','Sa'],
            dayNamesShorter: ['S','M','T','W','T','F','S'],
            weekHeader: 'Wk',
            dateFormat: 'dd/mm/yy',
            firstDay: 1,
            isRTL: false,
            showMonthAfterYear: false,
            yearSuffix: ''};
        $.datepicker.setDefaults($.datepicker.regional['ja']);
    }
    window._set_datepicker_regional = _set_datepicker_regional;
})(jQuery);
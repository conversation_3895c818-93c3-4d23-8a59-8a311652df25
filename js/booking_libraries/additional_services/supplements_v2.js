var Supplements_v2 = {
    config: {
        initialized: false,
        is_mobile: false,
        is_counter_selector: false,
        thousands_separator: null,
        decimal_separator: null,
        force_x_decimals: null,
        filters_selected: {}
    },

    init: function() {
        Supplements_v2.config.initialized = true;
        Supplements_v2.config.thousands_separator = $('#thousands_separator').val();
        Supplements_v2.config.decimal_separator = $('#decimal_separator').val();
        Supplements_v2.config.force_x_decimals = $('#force_x_decimals').val();
        this.prepare_listeners();
        this.preselect_supplements();
        if(this.config.force_x_decimals){
            this.force_x_decimals();
        }
    },

    prepare_listeners: function() {
        // Add / Remove listeners for the supplements
        $(".additional_service_element .add_service_button").click(function () {
            if($(this).attr('data-reconfirm-selection')){
                const supplement_wrapper = $(this).closest(".additional_service_element");
                if(!supplement_wrapper.hasClass('selected')){
                    $(document).trigger('add_service_action.additional_services.controller', [{
                        element: $(this)
                    }]);
                    return;
                }
            }
            Supplements_v2.add_remove_supplement($(this));
        });

        if(this.config.is_counter_selector){
            $(".additional_service_element .service_select").find(".sign_element").click(function () {
                Supplements_v2.check_sign_controll($(this));
            });
        }

        $(".additional_services_total_wrapper .perform_additional_services_booking").click(function() {
            SupplementsGeneralController.perform_additional_services_booking();
        });

        $(".top_continue_booking .perform_additional_services_booking").click(function() {
            SupplementsGeneralController.perform_additional_services_booking();
        });

        $(".all_additional_services_wrapper .filter_option_title").click(function() {
            if ($(this).closest('.filter_option').hasClass("active")) {
                $(this).closest('.filter_option').removeClass("active");
            } else {
            $('.filter_option').removeClass("active");
            $(this).closest('.filter_option').toggleClass("active");
            }
        });

        $(document).on("click", function(event) {
            if (!$(event.target).closest('.filter_option').length) {
                $('.filter_option').removeClass("active");
            }
        });

        $(document).on('continueBooking.booking.controller', ()=>{
             SupplementsGeneralController.perform_additional_services_booking();
        });

        $(document).on('add_service_action.additional_services.controller', (event, data)=>{
            $(document).one('confirm_service.additional_services.controller', ()=>{
                 Supplements_v2.add_remove_supplement(data.element);
            });
        });


        $(".all_additional_services_wrapper .filter_option_element").click(function() {
            const filter = $(this).data('filter');
            const category_id = $(this).closest('.category_wrapper').attr('id');
            const is_selection = !$(this).hasClass('active');
            Supplements_v2.toggle_add_filter(category_id, filter, is_selection);

            $(this).toggleClass("active");
        });

        $(".all_additional_services_wrapper .select_all_option").click(function() {
            const category_id = $(this).closest('.category_wrapper').attr('id');

            $(this).closest('.filter_option_list').find('.filter_option_element').each(function() {
                const filter = $(this).data('filter');
                Supplements_v2.toggle_add_filter(category_id, filter, true);

                $(this).addClass("active");
            });
        });

        if (this.config.is_mobile) {
            this.bind_mobile_listeners();
        }

        this.bind_upgrade_disabling_listeners();
    },

    bind_mobile_listeners: function() {
        $(".additional_service_element .preview_wrapper").click(function() {
            $(this).closest('.additional_service_element').toggleClass("active");
        });

        $(".additional_service_element .button-selection").click(function() {
            Supplements_v2.handle_quantity_update_mobile($(this));
        });

        // Prepare buttons to set automatically disabled on needed ones
        $(".additional_service_element").each(function() {
             Supplements_v2.check_mobile_quantity_buttons($(this));
             $(this).find(".button-selection[data-action='minus']").each(function() {
                $(this).trigger('click');
             });
             if($(this).find(".quantity_supplement").val() === '0'){
                 $(this).find('.add_service_button').addClass('disabled');
             }
        });
    },

    bind_upgrade_disabling_listeners: function () {

        document.addEventListener("shopping_cart_v2_retrieved", function (event) {
            const shoppingCart = event.detail;
            if (shoppingCart?.upgrade_success) {
                Supplements_v2.disable_upgrading();
            }
        });

        const disabledUpgradeSelector = ".category_wrapper.upgrade .additional_service_element.locked.upgrades_aplied";

        $(document).on('mouseenter', disabledUpgradeSelector, function () {
            $(this).find('.upgrade_blocked_tooltip').stop(true, true).fadeIn(150);
        });

        $(document).on('mouseleave', disabledUpgradeSelector, function () {
            $(this).find('.upgrade_blocked_tooltip').stop(true, true).fadeOut(150);
        });
    },

    disable_upgrading: function () {
        $(".category_wrapper.upgrade .additional_service_element").addClass("locked upgrades_aplied");
    },

    preselect_supplements: function () {
        $('.additional_service_element.preselect .add_service_button').each(function () {
            Supplements_v2.add_remove_supplement($(this));
        });
    },

    handle_quantity_update_mobile: function (clicked_element) {
        let service_element_wrapper = clicked_element.closest('.additional_service_element');

        if (clicked_element.hasClass("disabled") || service_element_wrapper.hasClass('selected')) {
            return;
        }

        let parent_element = clicked_element.closest('.amount_selection'),
            min_value = parseInt(parent_element.attr("min-value")),
            max_value = parseInt(parent_element.attr("max-value"));

        let current_value = parseInt(parent_element.find("input[type='hidden']").val());

        if (clicked_element.data("action") === "plus") {
            current_value++;
        }

        if (clicked_element.data("action") === "minus") {
            current_value--;
        }

        if (current_value >= min_value && current_value <= max_value) {
            parent_element.find("input[type='hidden']").val(current_value);
            parent_element.find(".selection_view .amount").text(current_value);
        }

        this.check_mobile_quantity_buttons(clicked_element.closest('.additional_service_element'));
        this.check_mobile_booking_button(clicked_element);
    },

    check_mobile_quantity_buttons: function(supplement_element) {
        supplement_element.find(".amount_selection").each(function() {
            let min_value = parseInt($(this).attr("min-value")),
                max_value = parseInt($(this).attr("max-value"));

            let current_value = parseInt($(this).find("input[type='hidden']").val());

            let minus_button = $(this).find(".button-selection[data-action='minus']"),
                plus_button = $(this).find(".button-selection[data-action='plus']");

            if (current_value <= min_value) {
                minus_button.addClass("disabled");
            } else {
                minus_button.removeClass("disabled");
            }

            if (current_value >= max_value) {
                plus_button.addClass("disabled");
            } else {
                plus_button.removeClass("disabled");
            }
        });
    },

    check_mobile_booking_button: function (clicked_element) {
        let service_wrapper = clicked_element.closest('.additional_service_element'),
            selected_elements = service_wrapper.find(".amount_selection input[type='hidden']");

        // Get values of selected_elements
        let selected_values = [];
        selected_elements.each(function() {
            selected_values.push($(this).val());
        });

        if (selected_values.indexOf('0') > -1) {
            service_wrapper.find('.add_service_button').addClass("disabled");
        } else {
            service_wrapper.find('.add_service_button').removeClass("disabled");
        }
    },

    toggle_add_filter: function(category_id, filter, is_selection) {
        if (is_selection) {
            Supplements_v2.config.filters_selected[category_id]
                ? Supplements_v2.config.filters_selected[category_id].push(filter)
                : Supplements_v2.config.filters_selected[category_id] = [filter];
        } else {
            Supplements_v2.config.filters_selected[category_id] =
                Supplements_v2.config.filters_selected[category_id].filter(_filter => _filter !== filter);

                if (Supplements_v2.config.filters_selected[category_id].length === 0) {
                    delete Supplements_v2.config.filters_selected[category_id];
                }
        }

        this.apply_filters(category_id);
    },

    apply_filters: function(category_id) {
        if (Object.keys(Supplements_v2.config.filters_selected).length === 0){
            $(`.category_wrapper[id="${category_id}"] .additional_service_element`).each(function() {
                if(!$(this).hasClass("visible")){
                    $(this).addClass('visible')
                }
            })
        } else {

        Object.entries(Supplements_v2.config.filters_selected).forEach(([category_id, filters]) => {
            $(`.category_wrapper[id="${category_id}"] .additional_service_element`).each(function() {
                const filters = $(this).data('filters') ? $(this).data('filters').split(';') : [];
                const is_match = Supplements_v2.config.filters_selected[category_id].length && Supplements_v2.config.filters_selected[category_id].some(filter => filters.includes(filter));

                is_match ? $(this).addClass('visible') : $(this).removeClass('visible');
            });
        });
    }
    },

    add_remove_supplement: function(clicked_element) {
        const supplement_wrapper = clicked_element.closest(".additional_service_element");
        const service_selected = supplement_wrapper.hasClass('selected');
        const is_add_action = !service_selected;
        const is_shopping_cart = clicked_element.hasClass("shopping_cart_active");
        const is_upgrade = clicked_element.hasClass('upgrade');
        const supplement_key = supplement_wrapper.find(".supplement_key").val();
        const supplement_amount = supplement_wrapper.find(".quantity_supplement").val();
        const supplement_days = supplement_wrapper.find(".days_supplement").val() || 1;
        const blocked_service = supplement_wrapper.hasClass('blocked') && service_selected;
        const is_desktop = $('body').hasClass("booking_process_version_1");

        if ((supplement_wrapper.hasClass("locked") && is_add_action) || clicked_element.hasClass('disabled') || blocked_service) {
            return;
        }

        if (is_shopping_cart && is_desktop) {
            if(is_upgrade){
                shopping_cart_v2_controller.add_upgrade(clicked_element, !is_add_action);
            } else{
                shopping_cart_v2_controller.add_service_desktop(clicked_element, !is_add_action);
            }
        } else if (is_shopping_cart) {
            if(is_upgrade){
                shopping_cart_v2_controller.add_upgrade(clicked_element, !is_add_action);
            } else{
                shopping_cart_v2_controller.add_service(clicked_element, !is_add_action);
            }
        }

        let is_transfer = clicked_element.hasClass('transfer'),
            selected_room = supplement_wrapper.attr('data-room'),
            allow_only_one = supplement_wrapper.hasClass('allow_only_one');

        let selectionLimit = supplement_wrapper.data('selection-limit');

        let has_calendar = clicked_element.closest(".additional_service_element").hasClass('with_calendar');

        if (is_add_action) {
            /*if(supplement_wrapper.attr('data-only-one')){
                let already_selected = 0;
                already_selected = $('.additional_service_element.selected').filter(function (){
                    return $(this).attr('data-only-one');
                });
                if(already_selected.length) return;
            }*/

            supplement_wrapper.addClass("selected");
            if(Supplements_v2.config.is_counter_selector && !is_upgrade){
                supplement_wrapper.find(".quantity_selection_wrapper:not(.single_option)").fadeIn();
                supplement_wrapper.find(".days_selection_wrapper").fadeIn();
            } else {
                supplement_wrapper.find(".quantity_supplement").attr('disabled', true);
                supplement_wrapper.find(".days_supplement").attr('disabled', true);
            }

            if(allow_only_one){
                $('.additional_service_element.allow_only_one').each(function (){
                    $(this).addClass('locked');
                });
            } else if (selectionLimit) {
                const servicesWithLimitMatched = $(`.additional_service_element[data-selection-limit="${selectionLimit}"]`);
                const selectedServices = $(`.additional_service_element.selected[data-selection-limit="${selectionLimit}"]`);

                if (selectedServices.length >= selectionLimit) {
                    servicesWithLimitMatched.each(function () {
                        $(this).addClass('locked');
                    });
                }
            }

            if(is_transfer){
               $(".category_wrapper .additional_service_element.transfer").each(function(){
                   $(this).addClass('locked');
               });
            }
            if (is_upgrade) {
                SupplementsGeneralController.add_upgrade(supplement_key);
                $.each(selected_room.split(';'), function(index, val){
                    Supplements_v2.lock_upgrades(true, val);
                });

                if(!is_shopping_cart && (supplement_wrapper.attr('data-room') || '').split(';').length > 1){
                    Supplements_v2.show_all_room_alert();
                }

            } else {
                SupplementsGeneralController.add_supplement(supplement_key, supplement_amount, supplement_days);
                if(supplement_wrapper.attr('data-external-service')){
                    Supplements_v2.show_external_service_alert();
                }
            }
            if(!has_calendar){
                this.update_total_price();
            }
        } else {
            supplement_wrapper.removeClass("selected");


            if(Supplements_v2.config.is_counter_selector && !is_upgrade){
                supplement_wrapper.find(".quantity_selection_wrapper:not(.single_option)").fadeOut();
                supplement_wrapper.find(".days_selection_wrapper").fadeOut();
            } else {
                supplement_wrapper.find(".quantity_supplement").attr('disabled', false);
                supplement_wrapper.find(".days_supplement").attr('disabled', false);
            }
            SupplementsGeneralController.remove_supplement(supplement_key);
            if(allow_only_one){
                $('.additional_service_element.allow_only_one').each(function (){
                    $(this).removeClass('locked');
                });
            } else if (selectionLimit) {
                const servicesWithLimitMatched = $(`.additional_service_element[data-selection-limit="${selectionLimit}"]`);
                const selectedServices = $(`.additional_service_element.selected[data-selection-limit="${selectionLimit}"]`);

                if (selectedServices.length < selectionLimit) {
                    servicesWithLimitMatched.each(function () {
                        $(this).removeClass('locked');
                    });
                }
            }
            if(is_transfer){
               $(".category_wrapper .additional_service_element.transfer").each(function(){
                    $(this).removeClass('locked');
               });
            }
            if (is_upgrade) {
                $.each(selected_room.split(';'), function(index, val){
                    Supplements_v2.lock_upgrades(false, val);
                });
            }
            if(!has_calendar){
                this.update_total_price();
            }
        }
    },

    check_sign_controll: function (clicked_element) {
        const supplement_wrapper = clicked_element.closest(".additional_service_element");
        var is_minus = false,
            is_plus = false,
            target_input = clicked_element.closest(".signs_controlls").find("input"),
            actual_value = parseInt(target_input.val()),
            max_value = target_input.attr('max'),
            min_value = target_input.attr('min'),
            actual_days = supplement_wrapper.find(".days_supplement").val() || 1,
            actual_quantity = supplement_wrapper.find(".quantity_supplement").val(),
            supplement_price = Supplements_v2.parse_float(supplement_wrapper.find(".supplement_price").val());

        let has_calendar = supplement_wrapper.hasClass('with_calendar');
        let add_button = supplement_wrapper.find('.add_service_button');
        let is_desktop = $('body').hasClass("booking_process_version_1");
        let action_executed = false;

        if (clicked_element.hasClass('plus_sign')) {
            is_plus = true;
        }

        if (clicked_element.hasClass('minus_sign')) {
            is_minus = true;
        }


        if (is_plus) {
            if (actual_value < parseInt(max_value)) {
                actual_value = actual_value + 1;
                action_executed = true;
                if(target_input.hasClass('days_supplement')){
                    supplement_price = supplement_price * actual_quantity;
                } else{
                    supplement_price = supplement_price * actual_days;
                }
            }
        }

        if (is_minus) {
            if (actual_value > parseInt(min_value)) {
                action_executed = true;
                actual_value = actual_value - 1;
                if(target_input.hasClass('days_supplement')){
                    supplement_price = supplement_price * actual_quantity;
                } else{
                    supplement_price = supplement_price * actual_days;
                }
            }
        }

        target_input.val(actual_value).trigger('change');
        let target_input_type = '';
        if(target_input.hasClass('quantity_supplement')){
            target_input_type = 'quantity';
        } else{
            target_input_type = 'days';
        }
        Supplements_v2.service_selector_input_change(target_input, target_input_type);

        if(add_button.hasClass('shopping_cart_active') && is_desktop && action_executed){
            shopping_cart_v2_controller.add_service_desktop(add_button, true);
            shopping_cart_v2_controller.add_service_desktop(add_button, false);
            clicked_element.closest(".additional_service_element").addClass('selected');
        }
        if (!has_calendar) {
            Supplements_v2.update_total_price();
        }
    },

    service_selector_input_change: function(service_input, input_type){
        let new_value = $(service_input).val(),
            supplement_wrapper = service_input.closest(".additional_service_element"),
            supplement_key = supplement_wrapper.find(".supplement_key").val(),
            supplement_post_form = $("form#supplements_form_post");

        if(supplement_key && supplement_post_form){
            let input_name = '';
            if(input_type == 'days'){
                input_name = 'input[name="days_' + supplement_key + '"]';
            } else{
                input_name = 'input[name="amount_' + supplement_key + '"]';
            }
            let input_to_update = supplement_post_form.find(input_name);
            input_to_update.val(new_value);
        }
    },

    lock_upgrades: function(lock, room) {
        $(".category_wrapper.upgrade .additional_service_element").each(function(i, element) {
            let data_room = $(this).attr('data-room') || '',
                lock_counter = parseInt($(this).attr('data-lock-counter')) || 0;
            $.each(data_room.split(';'), function (index, val){
                if(val === room){
                    if(lock){
                        lock_counter += 1;
                        $(element).attr('data-lock-counter', lock_counter);
                        $(element).addClass("locked");
                    } else{
                        lock_counter -= 1;
                        $(element).attr('data-lock-counter', lock_counter);
                        if(lock_counter === 0){
                            $(element).removeClass("locked");
                        }
                    }
                }
            });
        });
    },

    update_total_price: function () {
        const selected_supplements = $(".additional_service_element.selected");
        const total_price_wrapper = $(".additional_services_total_wrapper .total_prices .currencyValue");
        let total_price = 0;
        selected_supplements.each(function () {
            let supplement_price = Supplements_v2.parse_float($(this).find(".supplement_price").val());
            let supplement_amount = Supplements_v2.parse_float($(this).find(".quantity_supplement").val());
            let supplement_days = Supplements_v2.parse_float($(this).find(".days_supplement").val()) || 1;
            let supplement_key = $(this).find(".supplement_key").val();
            let is_upgrade = $(this).hasClass('is_upgrade');
            total_price += supplement_price * supplement_amount * supplement_days;
            if(!is_upgrade) Supplements_v2.doublecheck_supplements_submit(supplement_key, supplement_amount, supplement_days);
        });
        total_price_wrapper.attr('latest_value', total_price);
        total_price_wrapper.html(Supplements_v2.to_fixed(total_price, 2));
    },

    doublecheck_supplements_submit: function (supplement_key, supplement_amount, supplement_days) {
        const form = $("form#supplements_form_post");
        if (!form.length) return;

        const setOrAppendInput = (name, value) => {
            let input = form.find(`input[name="${name}"]`);
            if (input.length) {
                input.val(value);
            } else {
                form.append($('<input>', {
                    type: 'hidden',
                    name: name,
                    value: value
                }));
            }
        };

        setOrAppendInput(`amount_${supplement_key}`, supplement_amount);
        setOrAppendInput(`days_${supplement_key}`, supplement_days);
    },

    force_x_decimals: function(){
        let price_wrapper = $(".currencyValue");
        price_wrapper.each(function(){
            let actual_total_price = Supplements_v2.parse_float($(this).html());
            $(this).html(Supplements_v2.to_fixed(actual_total_price, Supplements_v2.config.force_x_decimals));
        });
    },

    show_all_room_alert: function(){
        let alert = "<div class='all_room_alert'><i class='fa fa-close'></i><i class='far fa-check-circle'></i>" + $.i18n._("T_upgrade_todas") + "</div>";
        $('body').append(alert);
        $('.all_room_alert i.fa-close').click(function (){Supplements_v2.remove_room_alerts();});

        setTimeout(function(){
            Supplements_v2.remove_room_alerts();
        }, 5000);
    },

    show_external_service_alert: function(){
        let alert = "<div class='all_room_alert'><i class='fa fa-close'></i><i class='far fa-check-circle'></i>" + $.i18n._("T_external_service") + "</div>";
        $('body').append(alert);
        $('.all_room_alert i.fa-close').click(function (){Supplements_v2.remove_room_alerts();});

        setTimeout(function(){
            Supplements_v2.remove_room_alerts();
        }, 5000);
    },

    remove_room_alerts: function(){
        if($('.all_room_alert').length){
            $('.all_room_alert').remove();
        }
    },

    parse_float: function (value) {
        let ts = Supplements_v2.config.thousands_separator,
            ds = Supplements_v2.config.decimal_separator,
            result = value;

        if (typeof (value) === 'string') {
            if (ts) {
                result = result.replace(new RegExp(`\\${ts}(?=\\d{3}(?:[^\\d]|$))`, 'g'), '');
            }

            if (ds) {
                result = result.replace(ds, '.');
            }
        }

        return parseFloat(result);
    },

    to_fixed: function (value, precision) {
        let ts = Supplements_v2.config.thousands_separator,
            ds = Supplements_v2.config.decimal_separator,
            fd = Supplements_v2.config.force_x_decimals,
            power = Math.pow(10, precision || 0),
            result = String(Math.round(value * power) / power);

        if (ds) {
            result = result.replace('.', ds);
        }

        if (ts) {
            result = result.replace(/\B(?=(\d{3})+(?!\d))/g, ts);
        }

        if (fd) {
            result = parseFloat(result).toFixed(fd);
        }

        return result;
    }
};

document.addEventListener("currencyUpdated", function () {
    Supplements_v2.update_total_price()
});
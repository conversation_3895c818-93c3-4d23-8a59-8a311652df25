<div class="mobile_engine {% if web_support %}has_web_support{% endif %}">
    <div class="mobile_engine_action bgc_1">
        <i class="fa fa-calendar"></i><span>{{ T_reservar }}</span>
    </div>
    <div class="booking_engine_mobile">
        <div id="full-booking-engine-html-mobile"
             class="{% if adultsOnly %} adults_only_selector{% endif %}">

            {% if not hide_booking_form_title %}
                <div class="booking_form_title">
                    <div class="best_price">
                        <i class="fa fa-calendar-o"></i> {{ T_reserva_mejor|upper }}
                    </div>
                    {% if custom_title_html %}{{ custom_title_html|safe }}{% endif %}
                    {% if custom_new_title %}
                        <div class="booking_title_custom">{{ custom_new_title|safe }}</div>{% endif %}
                    {% if custom_web_oficial_txt %}
                        <div class="booking_oficial">{{ custom_web_oficial_txt.content|safe }}</div>{% endif %}
                    {% if ticks_on_booking_title %}{% include "_ticks_on_widget.html" %}{% endif %}
                </div>
            {% endif %}

            <form class="booking_form {% if showBabies %}booking-form-center-text{% endif %} paraty-booking-form"
                  action="{{ bookingUrl }}" method="post">

                <input type="hidden" id="language" name="language" value="{{ language }}"/>
                {% if showBabies %}<input type="hidden" id="babies_max_age" value="
                                    {% if babiesSelection %}{{ babiesSelection }}{% else %}1{% endif %}">{% endif %}
                <input type="hidden" id="agesKid1" name="agesKid1"/>
                <input type="hidden" id="agesKid2" name="agesKid2"/>
                <input type="hidden" id="agesKid3" name="agesKid3"/>
                <input id="roomType" type="hidden" name="roomType" value="{{ roomType|safe }}">
                <input class="roomTypeValue" type="hidden" name="roomTypeValue"
                       value="{{ roomTypeValue|safe }}">
                <input id="roomFilter" type="hidden" name="roomFilter"
                       value="{{ roomFilter|safe|default:'.*' }}">
                <input id="roomFilterName" type="hidden" name="roomFilterName" value="">
                <input type="hidden" id="roomtype_list_json" name="roomtype_list_json"
                       value="{{ roomtype_list_json }}">
                <input type="hidden" id="namespace" name="namespace" value="{{ namespace }}"/>
                <input type="hidden" id="gclid" name="gclid" value=""/>
                <input type="hidden" value="{{ priceSeekerHotel }}" name="priceSeekerHotel"
                       id="priceSeekerHotel"/>
{% if original_referer %}
            <input type="hidden" value="{{ original_referer }}" name="original_referer"/>
        {% endif %}
                <input name="startDate" type="hidden" class="entry_input"/>
                <input name="endDate" type="hidden" class="departure_input"/>

                {% if applicationIds %}
                    <input type="hidden" id="applicationIds" name="applicationIds"
                           value="{{ applicationIds }}"/>
                {% endif %}

                {% if selectOptions %} {% include "_hotel_selector.html" %} {% endif %}
                {% if hotels_list %} {% include "_hotel_selector_2.html" %} {% endif %}
                {% if hotels_list_mobile %} {% include "_hotel_selector_mobile.html" %} {% endif %}
                {% if selectOptions_html_grouped %}
                    {% include "hotel_selector_html_grouped.html" %} {% endif %}
                {% if hotels_list_with_groups %} {% include "_hotel_selector_groups.html" %} {% endif %}
                {% if hotels_list_with_groups_2steps %}
                    {% include "_hotel_selector_groups_2steps.html" %} {% endif %}
                {% if roomtype_list %}{% include "_type_room_selector.html" %}{% endif %}
                {% if hotels_list_html_mobile %} {{ hotels_list_html_mobile|safe }} {% endif %}

                {% if roomtype_hide_filter and not roomtype_list %}
                    <!-- Be careful, roomtype_hide_filter and roomtype_list are incompatible -->
                    <input id="roomFilter" type="hidden" name="roomFilter"
                           value="{{ roomtype_hide_filter }}">
                    {% if roomtype_list_json_hide %}
                        <input type="hidden" id="roomtype_list_json" name="roomtype_list_json"
                               value="{{ roomtype_list_json_hide }}">
                    {% endif %}

                {% endif %}

                {% if boardtype_hide_filter %}
                    <input id="boardFilter" type="hidden" name="boardFilter"
                           value="{{ boardtype_hide_filter }}">
                {% endif %}

                {% if forced_logotype_hide_filter %}
                    <input id="forcedLogotype" type="hidden" name="forcedLogotype"
                           value="{{ forced_logotype_hide_filter }}">
                {% endif %}

                {% if forced_host_logotype_hide_filter %}
                    <input id="forcedHostLogotype" type="hidden" name="forcedHostLogotype"
                           value="{{ forced_host_logotype_hide_filter }}">
                {% endif %}

                <div class="dates_selector_personalized">
                    <label for="" class="dates_selector_label">
                        <span>{{ T_entrada }}</span>
                        <span>{{ T_salida }}</span>
                    </label>
                    <div class="start_end_date_wrapper">
                        <span class="start_date_personalized"></span>
                        <span class="end_date_personalized"></span>
                    </div>
                    <div class="start_date_datepicker center_y" data-role="date" data-inline="true"
                         style="display: none">
                        <div class="close_calendar center_xy_before"></div>
                        <span class="entry_label_calendar">{{ T_fecha_entrada }}</span>
                    </div>
                    <div class="departure_datepicker center_y" data-role="date" data-inline="true"
                         style="display: none">
                        <div class="close_calendar"></div>
                        <span class="departure_label_calendar">{{ T_fecha_salida }}</span>
                    </div>
                </div>

                <!-- END SELECCION FECHAS DE ENTRADA Y HABITACIONES -->
                {% if ExtraElementBeforeRoomList %}{{ ExtraElementBeforeRoomList|safe }}{% endif %}
                <!-- LISTA DE HABITACIONES -->
                <div class="room_list_wrapper">{% include "_rooms.html" %}</div>
                <!-- END LISTA DE HABITACIONES -->


                <div class="wrapper_booking_button {% if promocode_animate %}special{% endif %}">
                    <div class="guest_selector">
                        <span class="placeholder_text center_y center_y_before">{{ T_seleccionar }}</span>
                        <b class="button center_y"></b>
                    </div>
                    <div class="promocode_wrapper">
                        <input type="text" class="promocode_input" placeholder="{{ T_promocode|upper }}"
                               name="promocode" value="{% if default_promocode %}{{ default_promocode|safe }}{% endif %}" tabindex="16">
                    </div>
                    {{ custom_promocode_html }}
                    {% if promocode_append %}<input type="hidden" value="{{ promocode_append }}"
                                                    class="promocode_append">{% endif %}
                    <button type="button"
                            {% if ageSelection %}onclick="$(this).css('display','none');bookingSearchWithAgeSelection();return false;"{% endif %}
                            class="submit_button buttonsearch-ratecheck bgc_1">

                        {% if custom_caption_submit_book %}
                            {{ custom_caption_submit_book }}
                        {% else %}
                            {% if caption_submit_book %}
                                {{ T_reservar }}
                            {% else %}
                                {{ T_buscar }}
                            {% endif %}
                        {% endif %}
                    </button>
                    {% if click2call %}{% include "_click2call.html" %}{% endif %}
                </div>

                <div class="loading_animation_popup" style="display: none">
                    <div class="center_xy">
                        <span class="spincircle"><img src="{{ small_logotype }}" class="main-logo"></span>
                        <span>{{ loading_text_popup|safe }}</span>
                    </div>
                </div>

                {% if booking_footer_message %}
                    <div class="booking_footer_message">{{ booking_footer_message|safe }}</div>{% endif %}

            </form>
            {% if ageSelection %}{% include "_age_selector.html" %}{% endif %}
            {% if ticks_on_booking_footer %}{% include "_ticks_on_widget.html" %}{% endif %}
            {% if web_support %}{% include "_web_support.html" %}{% endif %}
        </div>
        {% if popup_booknow %}{% include "_landing_page3.html" %}{% endif %}
        {#{{ rescueseeker|safe }}#}
        {{ ratecheck|safe }}
    </div>
</div>
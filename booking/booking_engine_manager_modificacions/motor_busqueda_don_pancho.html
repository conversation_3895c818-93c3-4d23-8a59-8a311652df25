<script>

{% if startDateDefault %}

    $("[name=startDate]").datepicker({ dateFormat: 'dd/mm/yy', changeYear: true,defaultDate: '{{startDateDefault}}'});

    {% if endDateDefault %}
         $("[name=endDate]").datepicker({ dateFormat: 'dd/mm/yy', changeYear: true,defaultDate: '{{endDateDefault}}'});
    (% else %}

        $("[name=endDate]").datepicker({ dateFormat: 'dd/mm/yy', changeYear: true,defaultDate: +1});

    {% endif %}

{% endif %}

</script>

<div id="motor_reserva">
    <!-- booking engine Don Pancho -->
	<form action="{{bookingUrl}}" name="searchForm" id="searchForm" method="post" class="paraty-booking-form">
		<input type="hidden" id="language" name="language" value="{{language}}" />
		<input type="hidden" id="agesKid1" name="agesKid1" />
		<input type="hidden" id="agesKid2" name="agesKid2" />
		<input type="hidden" id="agesKid3" name="agesKid3" />

         <input type="hidden" id="gclid" name="gclid" value="" />
    <input type="hidden" value="{{ priceSeekerHotel }}" name="priceSeekerHotel" id="priceSeekerHotel"/>


        {%  if applicationIds %}
        <input type="hidden" id="applicationIds" name="applicationIds" value="{{ applicationIds }}"  />
        {% endif %}

    <input type="hidden" id="namespace" name="namespace" value="{{ namespace }}" />

    <div class="dates_rooms_wrapper">
        <fieldset id="contenedor_fechas">
            <div id="fecha_entrada" class="colocar_fechas">
                <label id="titulo_fecha_entrada" class="titulo_fechas">{{T_fecha_entrada}}</label>
                <input type="text" class="float_fecha" name="startDate" disabled="disabled" id="datepicker1" tabindex="4" readonly="readonly" placeholder="{{ T_entrada}}"/>
            </div>
            <div id="fecha_salida" class="colocar_fechas">
                <label id="titulo_fecha_salida" class="titulo_fechas">{{T_fecha_salida}}</label>
                <input type="text" class="float_fecha" name="endDate" disabled="disabled" id="datepicker2" tabindex="5" readonly="readonly" placeholder="{{ T_salida }}"/>
            </div>
        </fieldset>

        <fieldset id="contenedor_habitaciones" {% if rooms_disabled %}style="display: none"{% endif %}>
          <label>{{T_habitaciones}}</label>
          <select class="selector_habitaciones" name="numRooms" onchange="mostrarHabitaciones(this.value);" tabindex="6">
              <option value="0" selected="selected">Habs</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
          </select>
        </fieldset>
    </div>

        <fieldset id="contenedor_opciones">
			<div id="hab1">
                <div class="adultos numero_personas">
                    <select class="selector_adultos" id="hab1_adultos" name="adultsRoom1" tabindex="7">
                        <option value="2" selected="selected">{{T_adultos}}</option>
                        {% for option in validAdults %}
                        <option value="{{option|safe}}">{{option|safe}}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="ninos numero_personas" title="{{T_bebes_gratis|safe}}">
                    <select id="hab1_ninos" class="selector_ninos" name="childrenRoom1" onchange="" tabindex="8">
                        <option value="0" selected="selected">{{T_ninos}}
                            {% if not kidsAge == '0' %}
                                {% if kidsAgeStart and kidsAgeEnd %}
                                ({{ kidsAgeStart}}-{{ kidsAgeEnd }}&nbsp;{{ T_anyos }})
                                {% else %}
                                  ({{T_de_2_a}}&nbsp;{{kidsAge}}&nbsp;{{T_anyos}})
                                {% endif %}
                            {% endif %}
                        </option>
                        {% for option in validKids %}
                        <option value="{{option|safe}}">
                        {% if golf %}
                        {{option|safe}}:00
                        {% else %}
                        {{option|safe}}
                        {% endif %}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                {% if showBabies %}
                <div class="numero_personas" class="bebes">
                    <label class="selector_bebes">{{T_bebes}}</label>
                    <select id="hab1_bebes" class="selector_bebes" name="babiesRoom1" onchange="" tabindex="9">
                        <option value="0" selected="selected">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                    </select>
                </div>
                {% endif %}
            </div>


            <div id="hab2">
                <div class="adultos numero_personas">
                    <select class="selector_adultos" id="hab2_adultos" name="adultsRoom2" tabindex="10">
                        <option value="0" selected="selected">{{T_adultos}}</option>
                        {% for option in validAdults %}
                        <option value="{{option|safe}}">{{option|safe}}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="ninos numero_personas" title="{{T_bebes_gratis|safe}}">
                    <select id="hab2_ninos" class="selector_ninos" name="childrenRoom2" onchange="" tabindex="11">
                        <option value="0" selected="selected">{{T_ninos}}
                            {% if not kidsAge == '0' %}
                                {% if kidsAgeStart and kidsAgeEnd %}
                                ({{ kidsAgeStart}}-{{ kidsAgeEnd }}&nbsp;{{ T_anyos }})
                                {% else %}
                                  ({{T_de_2_a}}&nbsp;{{kidsAge}}&nbsp;{{T_anyos}})
                                {% endif %}
                            {% endif %}
                        </option>
                        {% for option in validKids %}
                        <option value="{{option|safe}}">
                        {% if golf %}
                        {{option|safe}}:00
                        {% else %}
                        {{option|safe}}
                        {% endif %}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                {% if showBabies %}
                <div class="numero_personas" class="bebes">
                    <select id="hab2_bebes" class="selector_bebes" name="babiesRoom2" onchange="" tabindex="12">
                        <option value="0" selected="selected">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                    </select>
                </div>
                {% endif %}
            </div>

			<!---------------------------- END ROOM 2 ------------------------------->


			<!---------------------------- ROOM 3 ------------------------------->


            <div id="hab3">
                <div class="adultos numero_personas">
                    <select class="selector_adultos" id="hab3_adultos" name="adultsRoom3" tabindex="13">
                        <option value="0" selected="selected">{{T_adultos}}</option>
                        {% for option in validAdults %}
                        <option value="{{option|safe}}">{{option|safe}}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="ninos numero_personas" title="{{T_bebes_gratis|safe}}">
                    <select id="hab3_ninos" class="selector_ninos" name="childrenRoom3" onchange="" tabindex="14">
                        <option value="0" selected="selected">{{T_ninos}}
                            {% if not kidsAge == '0' %}
                                {% if kidsAgeStart and kidsAgeEnd %}
                                ({{ kidsAgeStart}}-{{ kidsAgeEnd }}&nbsp;{{ T_anyos }})
                                {% else %}
                                  ({{T_de_2_a}}&nbsp;{{kidsAge}}&nbsp;{{T_anyos}})
                                {% endif %}
                            {% endif %}
                        </option>
                        {% for option in validKids %}
                        <option value="{{option|safe}}">
                        {% if golf %}
                        {{option|safe}}:00
                        {% else %}
                        {{option|safe}}
                        {% endif %}
                        </option>
                        {% endfor %}
                    </select>
                </div>

                {% if showBabies %}
                <div class="numero_personas" class="bebes">
                    <select id="hab3_bebes" class="selector_bebes" name="babiesRoom3" onchange="" tabindex="15">
                        <option value="0" selected="selected">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                    </select>
                </div>
                {% endif %}
            </div>


		</fieldset>
		<fieldset id="envio">
            <input type="text" id="promocode" class="colocar_envio" placeholder="{{T_codigo_promocional}}" name="promocode" value="" tabindex="16">
			<button type="submit" onclick="return {% if ageSelection %} bookingSearchWithAgeSelection2() {% else %} bookingSearch() {% endif %}" id="search-button" class="colocar_envio buttonsearch-ratecheck" tabindex="17">{{T_reservar}}</button>
            <div id="envio_spinner"></div>
        </fieldset>
	</form>
 </div>
 
 
 <!-- Javascript at the bottom to increase speed -->

 {% if not languageCode == 'en' %} 
<script src="/static_1/js/datepicker/jquery.ui.datepicker-{{languageCode}}.js" type="text/javascript"></script>
{% endif %} 

<script type="text/javascript" src="/static_1/lib/spin.min.js"></script>

<!-- Javascript at the bottom to increase speed -->

{% if ageSelection %}
{% include "_age_selector.html" %}
{% endif %}

<script type="text/javascript">

$("#hotel_destino").change(function() {
	  var action = $(this).val();
	  $("#searchForm").attr("action", action);
      var newNamespace = namespaces[action];
      $("#namespace").val(newNamespace);

	});

{% if selectOptions %}

var namespaces = {};
{% for hotel in selectOptions %}
namespaces['{{hotel.id|safe}}'] = '{{ hotel.namespace }}';
{% endfor %}

{% endif %}

{%  if groupedSelectOptions %}

var namespaces = {};
{% for hotel_group in groupedSelectOptions %}
    {% for hotel in hotel_group.group_list %}
namespaces['{{hotel.id|safe}}'] = '{{ hotel.namespace }}';
    {% endfor %}
{% endfor %};

{% endif %}
	
{% if selectOptions or groupedSelectOptions %}
	var action = $("#hotel_destino").val();
	$("#searchForm").attr("action", action);
    var newNamespace = namespaces[action];
    $("#namespace").val(newNamespace);
{% endif %}

{% if languageCode == 'en' %} 

$.datepicker.regional['en'] = {
	closeText: 'Done',
	prevText: 'Prev',
	nextText: 'Next',
	currentText: 'Today',
	monthNames: ['January','February','March','April','May','June',
	'July','August','September','October','November','December'],
	monthNamesShort: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
	'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
	dayNames: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],
	dayNamesShort: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
	dayNamesMin: ['Su','Mo','Tu','We','Th','Fr','Sa'],
	weekHeader: 'Wk',
	dateFormat: 'dd/mm/yy',
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: ''};
$.datepicker.setDefaults($.datepicker.regional['en-GB']);

{% endif %}


$.datepicker.setDefaults($.datepicker.regional["{{languageCode}}"]);

var checkDates = function() {
	
	var date1_str = $("#datepicker1").val();
	var date2_str = $("#datepicker2").val();

	if (date1_str == "" || date2_str == ""){
		return false;
	} else {
		return true;
	}
}

$(document).ready(function (){
	
	$("#datepicker1").val("");
	$("#datepicker2").val("");
	
	$("#datepicker1").datepicker({
		minDate : new Date(),
		onClose: function(selectedDate) {
			if (selectedDate) {
				var theDate = $.datepicker.parseDate("dd/mm/yy", selectedDate)
				theDate.setDate(theDate.getDate() + 1 );
				$("#datepicker2").datepicker("option", "minDate", theDate);
			}
		}
	});

	$("#datepicker2").datepicker({
		minDate : new Date()
	});

	$("#datepicker1").change(function() {
		var date2 = $('#datepicker1').datepicker('getDate', '+3d');
		date2.setDate(date2.getDate() + 1);
		$('#datepicker2').datepicker('setDate', date2);
		checkDates();
	});

	$("#datepicker2").change(checkDates);

	$("#datepicker1").removeAttr('disabled');
	$("#datepicker2").removeAttr('disabled');

    mostrarHabitaciones(0);
});



function mostrarHabitaciones(selector_habitaciones) {

	for ( var i = 3; i > selector_habitaciones; i--) {
		$('#hab' + i).hide();
		$('#childrenRoom' + (i + 1)).val("0");
	}
	for ( var i = selector_habitaciones; i > 0; i--) {
		$('#hab' + i).show();
	}
}

function bookingSearch(){
	
		if (!checkDates()){
			return false;
		};
	
		var opts = {
		  lines: 13, // The number of lines to draw
		  length: 4, // The length of each line
		  width: 3, // The line thickness
		  radius: 10, // The radius of the inner circle
		  rotate: 0, // The rotation offset
		  color: '#e1e1e1', // #rgb or #rrggbb
		  speed: 1.5, // Rounds per second
		  trail: 60, // Afterglow percentage
		  shadow: false, // Whether to render a shadow
		  hwaccel: false, // Whether to use hardware acceleration
		  className: 'spinner', // The CSS class to assign to the spinner
		  zIndex: 2e9, // The z-index (defaults to 2000000000)
		  top: 'auto', // Top position relative to parent in px
		  left: 'auto' // Left position relative to parent in px
		};

		
		var target = document.getElementById('envio_spinner');
		$("#search-button").hide();
		var spinner = new Spinner(opts).spin(target);
};

</script>

<style type="text/css">

    body{
        background: transparent !important;
    }

    #search-button{
        background: rgba(121,121,121,1);
        background: -moz-linear-gradient(top, rgba(121,121,121,1) 0%, rgba(84,84,83,1) 49%, rgba(65,65,65,1) 50%, rgba(45,45,45,1) 100%);
        background: -webkit-gradient(left top, left bottom, color-stop(0%, rgba(121,121,121,1)), color-stop(49%, rgba(84,84,83,1)), color-stop(50%, rgba(65,65,65,1)), color-stop(100%, rgba(45,45,45,1)));
        background: -webkit-linear-gradient(top, rgba(121,121,121,1) 0%, rgba(84,84,83,1) 49%, rgba(65,65,65,1) 50%, rgba(45,45,45,1) 100%);
        background: -o-linear-gradient(top, rgba(121,121,121,1) 0%, rgba(84,84,83,1) 49%, rgba(65,65,65,1) 50%, rgba(45,45,45,1) 100%);
        background: -ms-linear-gradient(top, rgba(121,121,121,1) 0%, rgba(84,84,83,1) 49%, rgba(65,65,65,1) 50%, rgba(45,45,45,1) 100%);
        background: linear-gradient(to bottom, rgba(121,121,121,1) 0%, rgba(84,84,83,1) 49%, rgba(65,65,65,1) 50%, rgba(45,45,45,1) 100%);
        filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#797979', endColorstr='#2d2d2d', GradientType=0 );
        border: 1px solid #666666;
    }

    .colocar_fechas input {
        width: 48%;
    }

    #datepicker1{
        margin-right: 5px;
    }

</style>
 

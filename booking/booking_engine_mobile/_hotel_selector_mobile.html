<div class="destination_wrapper">
  <div class="destination_field">
  <select class="hotel_selector_mobile">
        <option>{{ T_seleccionar_hotel }}</option>
        {% for hotel in hotels_list_mobile %}
            <option id="{{ hotel.id }}" class="{{ hotel.class }} hotel_selector_option" data-url_booking="{{ hotel.url_booking }}" data-namespace="{{ hotel.namespace }}"
            {%  if hotel.kidsAgeRange %}
                data-kids_range_age="{{ hotel.kidsAgeRange }}"
            {%  endif %}
            {% if hotel.app_ids %}data-appsids="{{ hotel.app_ids|safe }}"{% endif %}
            {% if hotel.selected %}selected{% endif %}
            {% if hotel.closed_hotel %}closed_hotel="{{ hotel.closed_hotel|safe }}"{% endif %}
            >
            <h3 class="title_selector">{{ hotel.value|safe }}</h3></option>
        {% endfor %}
    </select>
  </div>
</div>
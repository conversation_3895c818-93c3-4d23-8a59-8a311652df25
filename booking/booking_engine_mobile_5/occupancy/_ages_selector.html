{% if ageSelection %}
    <div class="age_selection_wrapper room_element_{{ number }}">
        {% for kidIndex in "4"|create_range %}
            <div class="block_age_selection hide kid_ages_{{ kidIndex }}">
                <label class="age_label">{{ T_edad_nino }} {{ kidIndex }}</label>
                {% for age in kidsAgeRange %}
                    <div class="age_option" target="agesRoom{{ number }}_{{ kidIndex }}" room_index="{{ number }}">
                        <span>{{ age }}</span>
                    </div>
                {% endfor %}
            </div>
        {% endfor %}
    </div>
{% endif %}
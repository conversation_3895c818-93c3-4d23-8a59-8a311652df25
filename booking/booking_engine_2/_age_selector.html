<style>
.ui-dialog {
	position: absolute;
	top: 0;
	left: 0;
	padding: .2em;
	outline: 0;
}
.ui-dialog .ui-dialog-titlebar {
	padding: .4em 1em;
	position: relative;
}
.ui-dialog .ui-dialog-title {
	float: left;
	margin: .1em 0;
	white-space: nowrap;
	width: 90%;
	overflow: hidden;
	text-overflow: ellipsis;
}
.ui-dialog .ui-dialog-titlebar-close {
	position: absolute;
	right: .3em;
	top: 50%;
	width: 21px;
	margin: -10px 0 0 0;
	padding: 1px;
	height: 20px;
}
.ui-dialog .ui-dialog-content {
	position: relative;
	border: 0;
	padding: .5em 1em;
	background: none;
	overflow: auto;
	text-align: center;
}
.ui-dialog .ui-dialog-buttonpane {
	text-align: left;
	border-width: 1px 0 0 0;
	background-image: none;
	margin-top: .5em;
	padding: .3em 1em .5em .4em;
}
.ui-dialog .ui-dialog-buttonpane .ui-dialog-buttonset {
	/*float: right;*/
	float: none;
	text-align: center;
}
.ui-dialog .ui-dialog-buttonpane button {
	margin: .5em .4em .5em 0;
	cursor: pointer;
}
.ui-dialog .ui-resizable-se {
	width: 12px;
	height: 12px;
	right: -5px;
	bottom: -5px;
	background-position: 16px 16px;
}
.ui-dialog .ui-button-text {
	/*padding: 10px;*/
	/*margin: 10px;*/
}

.ui-dialog .ui-button{
	/*height: 30px;*/
}

.ui-draggable .ui-dialog-titlebar {
	cursor: move;
}

.ui-dialog {
	min-width: 300px;
    z-index: 9999;
}

</style>

 <style>
	.ui-dialog-titlebar-close {
 		display: none !important;
	}

	#dialog-form{
		padding: 10px;
	}

	.ui-dialog-titlebar{
		padding: 5px;
	}

	.kidAgesSelect{
		margin-left: 10px;

	}


</style>

<!-- New style Age popup -->
<style>
body .ui-widget-overlay.ui-front {
  background: rgba(0, 0, 0, 0.6);
  opacity: 1;
  z-index: 9998;
  position: fixed;
}
body .ui-dialog.ui-widget {
  padding: 20px 10px;
  border: 0;
  border-radius: 0;
  line-height: 1;
    max-width: 370px;
    width: 100% !important;
    box-sizing: border-box;
}
body .ui-dialog.ui-widget .ui-dialog-titlebar {
  background: none!important;
  padding: 0 0 20px 0;
  border-radius: 0;
  border: 0;
  color: black!important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.2);
  text-align: center;
  margin-bottom: 20px;
}
body .ui-dialog.ui-widget .ui-dialog-titlebar .ui-dialog-title {
  width: 100%;
  margin: 0;
}
body .ui-dialog.ui-widget #dialog-form {
  padding: 0;
  text-align: left;
  width: 100% !important;
    max-width: 350px;
}
body .ui-dialog.ui-widget #dialog-form form label {
  clear: both;
  width: 100%;
  float: left;
}
body .ui-dialog.ui-widget #dialog-form form .wrapper_age_kids {
  display: inline-block;
  width: 100%;
  float: left;
}
body .ui-dialog.ui-widget #dialog-form form .wrapper_age_kids select {
  width: calc((100% - 10px)/2);
  margin: 0px 10px 10px 0;
  float: left;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  border-radius: 0;
  padding: 5px 10px;
}
body .ui-dialog.ui-widget #dialog-form form .wrapper_age_kids select:nth-child(even) {
  margin-right: 0;
}
body .ui-dialog.ui-widget #dialog-form form .wrapper_age_kids select:nth-child(-n+2) {
  margin-top: 10px;
}
body .ui-dialog.ui-widget .ui-dialog-buttonpane {
  margin: 0;
  padding: 0;
  vertical-align: bottom;
  display: inline-block;
  float: left;
  width: 100% !important;
    max-width: 350px;
  border-radius: 0;
  margin-top: 10px;
}
body .ui-dialog.ui-widget .ui-dialog-buttonpane .ui-button {
  margin: 0;
  display: inline-block;
  width: 100% !important;
    max-width: 350px;
  border-radius: 0;
  background: #446ca9;
  color:white;
}
body .ui-dialog.ui-widget .ui-resizable-handle {
  display: none;
}
</style>
<!--  <link rel="stylesheet" href="http://code.jquery.com/ui/1.10.1/themes/base/jquery-ui.css" type="text/css"/> -->
 <div id="dialog-form"  title="{{T_edades_nino|safe}}" style="display:none">
    <form>
        {% for roomIndex in "123" %}
        <label id="label{{roomIndex}}" for="name">{{T_habitacion}} {{roomIndex}}</label>
        <div class="wrapper_age_kids clearfix">
          {%  if kidIndexRageList %}
              {% for kidIndexRange in kidIndexRageList %}
                     <select class="kidAgesSelect" id="agesRoom{{roomIndex}}_{{forloop.counter}}" name="name" tabindex="1">
                         {% for age in kidIndexRange %}
                              <option value="{{ age }}" {% if forloop.first %}selected="selected"{% endif %}>{{ age }}</option>
                         {% endfor %}
                    </select>
              {% endfor %}
        {%  else %}
                {% for kidIndex in "1234" %}

                    <select class="kidAgesSelect" id="agesRoom{{roomIndex}}_{{kidIndex}}" name="name" tabindex="1">
                    {% for age in kidsAgeRange %}
                      <option value="{{ age }}" {% if forloop.first %}selected="selected"{% endif %}>{{ age }}</option>
                    {% endfor %}
                    </select>

                {% endfor %}
        {% endif %}
        </div>

		{% endfor %}
    </form>
</div>

<script type="text/javascript">

    function sendActiveForm_booking_engine2() {

        $(".booking_form").each(function () {
            if ($(this).find(".submit_button").css("display") == "none" || $(this).find(".submit_button").hasClass('clicked_button')) {
                if ($('#booking-search-popup').length) {
                    show_booking_search_popup($(this));
                    return false;
                }

                $(this).submit();
                return true;
            }
        });

        $("#searchForm").each(function () {
            if ($(this).find("#search-button").css("display") == "none") {
                if ($('#booking-search-popup').length) {
                    show_booking_search_popup($(this));
                    return false;
                }

                $(this).submit();
                return true;
            }
        });


    }

function bookingSearchWithAgeSelection2(){
	if (!checkDates()){
		return false;
	};

	kids1 = $('#hab1_ninos').val();
	kids2 = $('#hab2_ninos').val();
	kids3 = $('#hab3_ninos').val();
    numRooms = $("input[name='numRooms']").val();

	if (kids1 == "0" && (kids2 == "0" || numRooms == '1') && (kids3 == "0" || numRooms == '1' || numRooms == '2')){
		if (bookingSearch()){

    		$('#searchForm').submit();
    	}
	} else {
		updateAgesOptions2();
		$("#dialog-form").dialog("open");
		return false;
	}
}

function updateAgesOptions2() {

	var numHab = $("#selector_habitaciones").val();

	for ( var i = 3; i > 0; i--) {

		$("#label" + i).hide();
		$("#dialog-form #agesRoom" + i + "_1, #full_wrapper_booking #agesRoom" + i +"_1").hide();
		$("#dialog-form #agesRoom" + i + "_2, #full_wrapper_booking #agesRoom" + i +"_2").hide();
		$("#dialog-form #agesRoom" + i + "_3, #full_wrapper_booking #agesRoom" + i +"_3").hide();
		$("#dialog-form #agesRoom" + i + "_4, #full_wrapper_booking #agesRoom" + i +"_4").hide();
	}
	for ( var i = numHab; i > 0; i--) {
		numKids = $("#hab" + i + "_ninos").val();
		if (numKids > 0){
			$("#label" + i).show();

			$("#dialog-form #agesRoom" + i + "_1, #full_wrapper_booking #agesRoom" + i +"_1").show();
			if (numKids > 1)
				$("#dialog-form #agesRoom" + i + "_2, #full_wrapper_booking #agesRoom" + i +"_2").show();
			if (numKids > 2)
			    $("#dialog-form #agesRoom" + i + "_3, #full_wrapper_booking #agesRoom" + i +"_3").show();
			if (numKids > 3)
			    $("#dialog-form #agesRoom" + i + "_4, #full_wrapper_booking #agesRoom" + i +"_4").show();
		}
	}
}

$(function() {
    if (!$("#dialog-form").hasClass('ui-dialog-content')) {
        $("#dialog-form").dialog({
            width: "auto",
            autoOpen: false,

            modal: true,
            buttons: {
                "{{T_continuar|safe}}": function() {
                    $( this ).dialog( "close" );
                    var dialog_wrapper = $(this);

                    $("input[name='agesKid1']").each(function() {
                        var age_1_1 = dialog_wrapper.find("#agesRoom1_1").val();
                        var age_1_2 = dialog_wrapper.find("#agesRoom1_2").val();
                        var age_1_3 = dialog_wrapper.find("#agesRoom1_3").val();
                        var age_1_4 = dialog_wrapper.find("#agesRoom1_4").val();
                        $(this).val(age_1_1 + ";" + age_1_2 + ";" + age_1_3 + ";" + age_1_4);
                    });
                    $("input[name='agesKid2']").each(function () {
                        var age_2_1 = dialog_wrapper.find("#agesRoom2_1").val();
                        var age_2_2 = dialog_wrapper.find("#agesRoom2_2").val();
                        var age_2_3 = dialog_wrapper.find("#agesRoom2_3").val();
                        var age_2_4 = dialog_wrapper.find("#agesRoom2_4").val();
                        $(this).val(age_2_1 + ";" + age_2_2 + ";" + age_2_3 + ";" + age_2_4);
                    });
                    $("input[name='agesKid3']").each(function () {
                        var age_3_1 = dialog_wrapper.find("#agesRoom3_1").val();
                        var age_3_2 = dialog_wrapper.find("#agesRoom3_2").val();
                        var age_3_3 = dialog_wrapper.find("#agesRoom3_3").val();
                        var age_3_4 = dialog_wrapper.find("#agesRoom3_4").val();
                        $(this).val(age_3_1 + ";" + age_3_2 + ";" + age_3_3 + ";" + age_3_4);
                    });

                    bookingSearch();
                    sendActiveForm_booking_engine2();
                }
            }
        });
    }

    updateAgesOptions2(1);
});
</script>
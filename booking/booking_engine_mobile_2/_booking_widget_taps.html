{% if not deactived_font %}<link href='//fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,200italic,300italic&display=swap' rel='stylesheet' type='text/css'>{% endif %}


<span id="full-booking-engine-html" style="margin:0px;padding:0px;">
<!-- MY BOOKINGS INTEGRATED -->

{% if my_booking_integrated %}
    {% include "_my_bookings_on_widget.html" %}
  {% endif %}


<!-- TITULO O PESTANAS -->
 {% if taps %}
      <div class="booking_form_title_taps">
        {% for tap in taps %}

            <div class="bookingtap tap{{ forloop.counter }}">
                <span><a href="{{ tap.link|safe }}">{{ tap.content|safe }}</a></span>

            </div>

        {% endfor %}
      </div>
     <div class="booking_button_bar_taps"></div>
  {% else %}


        <div class="booking_form_title">

             <h4 class="booking_title_1">{{T_reservas}}</h4>
              <h4 class="booking_title_2">{{T_reserva_ahora}}</h4>
              <h4 class="best_price">{{T_reserva_mejor|upper}}</h4>

          {% if custom_new_title %}   <h4 class="booking_title_custom">{{custom_new_title}}</h4>{% endif %}
          {% if custom_web_oficial_txt %}   <h4 class="booking_oficial">{{custom_web_oficial_txt.content|safe}}</h4>{% endif %}


          {% if ticks_on_booking_title %}
            {% include "_ticks_on_widget.html" %}
          {% endif %}


        </div>


{% endif %}
<!-- END TITULO -->

<form class="booking_form {% if showBabies %}booking-form-center-text{%  endif %} paraty-booking-form" action="{{bookingUrl}}" method="post">

  <input type="hidden" id="language" name="language" value="{{ language }}" />
  <input type="hidden" id="agesKid1" name="agesKid1" />
  <input type="hidden" id="agesKid2" name="agesKid2" />
  <input type="hidden" id="agesKid3" name="agesKid3" />

  <input type="hidden" id="namespace" name="namespace" value="{{ namespace }}" />
{% if original_referer %}
            <input type="hidden" value="{{ original_referer }}" name="original_referer"/>
        {% endif %}
  <input type="hidden" id="gclid" name="gclid" value="" />
  <input type="hidden" value="{{ priceSeekerHotel }}" name="priceSeekerHotel" id="priceSeekerHotel"/>

  <!-- BOOKING 0 -->
  {%  if applicationIds %}
    <input type="hidden" id="applicationIds" name="applicationIds" value="{{ applicationIds }}"  />
  {% endif %}
  <!-- END BOOKING 0-->

  <!-- SELECTOR DE HOTEL-->
  {% if selectOptions %} {% include "_hotel_selector.html" %} {% endif %}
  {% if hotels_list %} {% include "_hotel_selector_2.html" %} {% endif %}
  {% if hotels_list_with_groups %} {% include "_hotel_selector_groups.html" %} {% endif %}
  {% if hotels_list_with_groups_2steps %} {% include "_hotel_selector_groups_2steps.html" %} {% endif %}
  {% if roomtype_list %}{% include "_type_room_selector.html" %}{% endif %}
  <!-- END SELECTOR DE HOTEL-->


 <!-- HIDE FILTERS -->
 {% if roomtype_hide_filter and not roomtype_list%}
     <!-- Be careful, roomtype_hide_filter and roomtype_list are incompatible -->
     <input id="roomFilter"  type="hidden" name="roomFilter" value="{{ roomtype_hide_filter }}">
 {% endif %}
 {% if boardtype_hide_filter%}
     <input id="boardFilter"  type="hidden" name="boardFilter" value="{{ boardtype_hide_filter }}">
 {% endif %}

 {% if forced_logotype_hide_filter%}
     <input id="forcedLogotype"  type="hidden" name="forcedLogotype" value="{{ forced_logotype_hide_filter }}">
 {% endif %}

 {% if forced_host_logotype_hide_filter%}
     <input id="forcedHostLogotype"  type="hidden" name="forcedHostLogotype" value="{{ forced_host_logotype_hide_filter }}">
 {% endif %}



 <!-- HIDE FILTERS -->

  <!-- SELECCION FECHAS DE ENTRADA Y NUMERO HABITACIONES -->
  <div class="stay_selection">

    <div class="entry_date_wrapper">
      <label>{{T_entrada}}</label>
      <div class="date_box entry_date">
        <span class="date_day"></span>
        <span class="date_year"></span>
        <input name="startDate"  type="hidden" class="has_datepicker entry_input"/>
        <div></div>
      </div>
    </div>

    <div class="departure_date_wrapper">
      <label>{{T_salida}}</label >
      <div class="date_box departure_date">
        <span class="date_day"></span>
        <span class="date_year"></span>
        <input name="endDate" type="hidden" class="has_datepicker departure_input"/>
        <div></div>
      </div>
    </div>


        <div class="rooms_number_wrapper" {% if rooms_disabled %}style="display: none;" {% endif %}>
          <label>{% if custom_caption_rooms %}{{ custom_caption_rooms }}{%  else %}{{ T_habitaciones }}{% endif %}</label>
          <select name="numRooms" class="rooms_number">
              <option value="1" selected="selected">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
          </select >
        </div>

  </div>
  <!-- END SELECCION FECHAS DE ENTRADA Y HABITACIONES -->

  <div class="guest_selector"><label>{{ T_ocupacion }}</label><span class="placeholder_text">{{ T_seleccionar }}</span><b class="button"></b></div>

  <!-- LISTA DE HABITACIONES -->
  <div class="room_list_wrapper">
    {% include "_rooms.html" %}
  </div>
  <!-- END LISTA DE HABITACIONES -->


  <div class="wrapper_booking_button {% if promocode_animate %}special{% endif %}">
    {% if not promo_double %}
    <input type="text" class="promocode_input {% if promocode_animate %}special{% endif %}" placeholder="{{T_codigo_promocional}}" name="promocode" value="" tabindex="16">
    {% else %}
    <textarea class="promocode_input" placeholder="{{ T_codigo_promocional }}" name="promocode" value="" tabindex="16"></textarea>
    {% endif %}
    <button type="button" {% if ageSelection %}onclick="$(this).css('display','none');bookingSearchWithAgeSelection();return false;"{% endif %} class="submit_button buttonsearch-ratecheck">

       {% if custom_caption_submit_book %}
            {{ custom_caption_submit_book }}
       {%  else %}

            {% if caption_submit_book %}
                {{ T_reservar }}
            {% else %}
                {{ T_buscar }}
            {% endif %}
        {% endif %}
    </button>
    <div class="spinner_wrapper"></div>
  </div>

  <div style="clear:both"></div>

    {% if bookingSearchPopupPictures %}
        <input type="hidden" id="booking-search-popup" name="booking-search-popup" value="true">
    {% endif %}

      {% if booking_footer_message %}
          <div class="booking_footer_message">{{booking_footer_message|safe}}</div>
      {% endif %}

</form>

{% if ageSelection %}
{% include "_age_selector.html" %}
{% endif %}


{% if ticks_on_booking_footer %}
    {% include "_ticks_on_widget.html" %}
{% endif %}


{% if web_support %}
    {% include "_web_support.html" %}
{% endif %}

</span>


{% if popup_booknow %}
    {% include "_landing_page.html" %}
{% endif %}

{{ rescueseeker|safe }}
{{ ratecheck|safe }}

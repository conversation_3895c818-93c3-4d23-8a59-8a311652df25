import time
import logging

'''
Copied from http://www.saltycrane.com/blog/2009/11/trying-out-retry-decorator-python/

Fount at http://stackoverflow.com/questions/9446387/how-to-retry-urllib2-request-when-failed

'''

def retry(ExceptionToCheck, tries=4, delay=1, backoff=1, retry_counter=None):
    """Retry calling the decorated function using an exponential backoff.

    http://www.saltycrane.com/blog/2009/11/trying-out-retry-decorator-python/
    original from: http://wiki.python.org/moin/PythonDecoratorLibrary#Retry

    :param ExceptionToCheck: the exception to check. may be a tuple of
        excpetions to check
    :type ExceptionToCheck: Exception or tuple
    :param tries: number of times to try (not retry) before giving up
    :type tries: int
    :param delay: initial delay between retries in seconds
    :type delay: int
    :param backoff: backoff multiplier e.g. value of 2 will double the delay
        each retry
    :type backoff: int
    :param logger: logger to use. If None, print
    :type logger: logging.Logger instance
    """
    def deco_retry(f):
        def f_retry(*args, **kwargs):
            mtries, mdelay = tries, delay
            try_one_last_time = True
            counter = 0
            while mtries > 1:
                try:
                    return f(*args, **kwargs)
                except ExceptionToCheck as e:
                    msg = "%s, Retrying in %d seconds..." % (str(e), mdelay)
                    logging.warning(e)
                    logging.warning(msg)
                    time.sleep(mdelay)
                    mtries -= 1
                    mdelay *= backoff
                    if retry_counter:
                        counter += 1
                        kwargs['retry_counter'] = counter
            if try_one_last_time:
                return f(*args, **kwargs)
            return
        return f_retry  # true decorator
    return deco_retry



# @retry(Exception, tries=3, delay=1, backoff=2, retry_counter=True)
# def testing(**kwargs):
#     print kwargs
#     print kwargs.get('retry_counter')
#     x = z
#     return x
#
#
# testing()
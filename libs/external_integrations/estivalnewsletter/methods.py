import json
import logging
from datetime import datetime

from booking_process.libs.external_integrations.estivalnewsletter.constants import URL_ENDPOINT
from booking_process.utils.auditing import auditUtils
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.language.language_constants import SPANISH, ENGLISH, ITALIAN
from booking_process.utils.language.language_utils import get_language_code
import requests


class EstivalNewsletter:

    def __init__(self, language=SPANISH):
        self.language = language
        self.language_code = get_language_code(language)
        self.url_endpoint = URL_ENDPOINT

        #Custom configs
        available_configs = get_web_configuration('Estival Newsletter')

        self.customer_key = available_configs.get('customer_key')
        self.customer_pass = available_configs.get('customer_pass')
        self.key_encription = available_configs.get('key_encription')
        self.hotel_id = available_configs.get('hotel_id')


    def _get_language(self, code):

        if code == "ES":
            return "SPANISH"
        elif code == "ING":
            return "ENGLISH"
        elif code == "RUS":
            return "RUSSIAN"
        elif code == "FRA":
            return "FRENCH"
        else:
            return "ENGLISH"

    def language_estival_translate(self, language):
        if language == SPANISH:
            return 'esp'

        if language == ENGLISH:
            return 'eng'

        if language == ITALIAN:
            return 'ita'

        return 'eng'

    def add_subscriber(self, email, first, last, country, language, personal_title="", telephone="", birthday="", promotions=False, request=None):

        logging.info("Signup suscriber...")

        headers = {
            'Content-Type': 'application/json',
            'X-STW-CUSTOMER-KEY': self.customer_key,
            'X-STW-CUSTOMER-PASS': self.customer_pass
        }

        time_now = datetime.now()
        time_now = time_now.replace(microsecond=0)
        epoch = datetime.utcfromtimestamp(0)
        float_time_now = int((time_now - epoch).total_seconds())


        payload = {
            'mail': email,
            'location_id': self.hotel_id,
            'transaction_date': float_time_now,
            'transaction_id': '00000000',
            'language': self.language_estival_translate(language if language else self.language),
            'timezone': 'Europe/Madrid',
            'rgpd_ip': request.remote_addr,
            'rgpd_date': float_time_now,
            'telf': telephone,
            'birthdate': birthday  # 'yyyy-mm-dd' format
        }

        if request:
            if request.values.get('first_surname'):
                payload['first_surname'] = request.values.get('first_surname')
            if request.values.get('second_surname'):
                payload['second_surname'] = request.values.get('second_surname')
            if request.values.get('postal_code'):
                payload['postal_code'] = request.values.get('postal_code')

        # data = json.dumps(payload)

        logging.info("Headers: %s", headers)
        logging.info("Data: %s", payload)

        #Coding data
        # encrypted_data = AESCipher(self.key_encription).encrypt(data)
        post_data = {'type': 'NEWSLETTER', 'data': payload}

        try:
            logging.info("Post data newsletter: %s" % post_data)
            response = requests.post(self.url_endpoint, data=json.dumps(post_data), headers=headers)
            response.raise_for_status()

            logging.info("Response content: %s", response.text)

            response_json = json.loads(response.text)
            logging.info("response_json content: %s", response_json)

            return True

        except Exception as e:
            logging.warning("Exception signing up for a customer in the newsletter: %s", e)
            message = auditUtils.makeTraceback()
            logging.warning(message)

        return False

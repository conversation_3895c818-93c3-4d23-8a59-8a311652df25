{# Styles builded with https://templates.mailchimp.com/resources/inline-css/ #}
<link href='https://fonts.googleapis.com/css?family=Roboto:400,300,500,700|Montserrat' rel='stylesheet' type='text/css'>



<div id="prestay_wrapper" style="font-size:14px;font-family: Roboto, sans-serif;width:100%;margin: auto;">

    {% if target_link %}<div style="width: 900px;margin:auto;">
        <a class="link_external_view" target="_blank" href="{{ target_link|safe }}" style="margin-bottom:20px;color:black;text-decoration: underline;font-size: 12px;">{{ T_email_correctamente }}</a>
    </div>{% endif %}

    {% if mail_header %}
    {% if mail_header.0.description %}<table style="text-align:left;width:900px;margin:0 auto;font-weight: bold;font-size: 34px;line-height:38px;background: #373636;color:white;" cellspacing="0" cellpadding="0">
            <tr><td style="padding:40px;line-height:38px;">{{ mail_header.0.description|safe }}</td></tr>
        </table>{% endif %}
        <table class="header_table" style="width:900px;height:484px;padding: 0;margin:0 auto;" cellpadding="0" cellspacing="0"><tr><td style="width:900px;">
            <img src="{{ mail_header.0.servingUrl }}=s900" style="width: 900px;height: 484px">
        </td></tr></table>
    {% endif %}
    <table class="header_logo" style="background: #373636;color:white;width:900px;padding:5px;margin:0 auto;"><tr>
        <td style="padding: 3px 0;text-align: center;">
            {% if alternativeLogo %}
                <a href="{% if alternativeLinkLogo %}{{ alternativeLinkLogo }}{% else %}{{ domainLink }}{% endif %}" >
                  <img src="{{ alternativeLogo }}" alt="" style="max-width: 240px; height: auto;"/>
                </a >
              {% else %}
                <a href="{{ domainLink }}">
                  <img src="{{ logotype }}=s300" alt="" style="max-height: 60px;"/>
                </a >
            {% endif %}
        </td>
        {% if mail_phone %}<td style="padding: 3px 0;text-align: center;">
            <img src="{{ mail_phone.0.servingUrl }}" style="vertical-align: middle">
            <span style="font-size: 18px;font-family: 'Montserrat', sans-serif;">{{ mail_phone.0.description|safe }}</span>
        </td>{% endif %}
        {% if mail_email %}<td style="padding: 3px 0;text-align: center;">
            <img src="{{ mail_email.0.servingUrl }}" style="vertical-align: middle">
            <a href="mailto:{{ v4_email.description|safe }}" style="font-size: 18px;font-family: 'Montserrat', sans-serif;color:white;">{{ mail_email.0.description|safe }}</a>
        </td>{% endif %}
    </tr>
    </table>

    {% if rescue_section.subtitle %}
        <div class="user_content" style="width: 900px;margin: 35px auto 0;color: #4e4e4e;font-size: 35px;line-height: 40px">
            {{ rescue_section.subtitle|safe }}
        </div>
    {% endif %}
    {% if rescue_section.content %}
        <div class="rescue_booking_content" style="width: 900px;margin:20px auto;font-size: 25px;line-height:30px;color: #373636;">
            {{ rescue_section.content|safe }}
        </div>
    {% endif %}

    {# Rescue booking link #}
    <div style="background-color:#ededed;">
    <table class="booking_information" style="background-color: #ededed;border: 25px solid #ededed;width: 900px;margin:0 auto;font-size: 20px;line-height:25px;color:#666;">
        <tr><td style="font-size:30px;font-weight: bold;text-transform: uppercase;margin-bottom: 20px">
            {% if mail_custom_table_title %}
                {{ mail_custom_table_title.0.description|safe }}
            {% else %}
                {{ T_busqueda }}
            {% endif %}
        </td></tr>
        <tr><td>
        <table style="background: white;height: 106px;padding: 10px;float:left;">
            <tr><td style="width: 150px">
                <label class="label_booking_summary" style="font-weight: 300;margin-bottom: 2px;display: block;color: black;font-size: 18px;">{{ T_entrada }}</label>
                <div class="value_booking" style="font-weight: bold;font-size: 20px;line-height: 22px;color: #4f4f4f;margin-top: 10px;">
                    {{ rescue_info.dateStart|safe }}
                    {% if entry_hour %}<br>{{ entry_hour|safe }}{% endif %}
                </div>
            </td><td style="padding: 10px 20px">
                <span style="font-size: 30px;transform: scale(0.5,2);display: block">></span>
            </td><td style="width: 150px">
                <label class="label_booking_summary" style="font-weight: 300;margin-bottom: 2px;display: block;color: black;font-size: 18px;">{{ T_salida }}</label>
                <div class="value_booking" style="font-weight: bold;font-size: 20px;line-height: 22px;color: #4f4f4f;margin-top: 10px;">
                    {{ rescue_info.dateEnd|safe }}
                    {% if departure_hour %}<br>{{ departure_hour|safe }}{% endif %}
                </div>
            </td><td style="width: 100px">
                <label class="label_booking_summary" style="font-weight: 300;margin-bottom: 2px;display: block;color: black;font-size: 18px;">{% if rescue_info.nights_number == 1 or rescue_info.nights_number|safe == "1" %}{{ T_noche }}{% else %}{{ T_noches }}{% endif %}</label>
                <div class="value_booking" style="font-weight: bold;font-size: 20px;line-height: 22px;color: #4f4f4f;margin-top: 10px;">
                    {{ rescue_info.nights_number|safe }}
                </div>
            </td><td style="width: 200px">
                <label class="label_booking_summary" style="font-weight: 300;margin-bottom: 2px;display: block;color: black;font-size: 18px;">{{ T_habitacion }}</label>
                <div class="value_booking" style="font-weight: bold;font-size: 20px;line-height: 22px;color: #4f4f4f;margin-top: 10px;">
                    {{ rescue_info.rooms_names|safe }}
                </div>
            </td><td style="width: 150px">
                <label class="label_booking_summary" style="font-weight: 300;margin-bottom: 2px;display: block;color: black;font-size: 18px;">{{ T_regimen_comidas }}</label>
                <div class="value_booking" style="font-weight: bold;font-size: 20px;line-height: 22px;color: #4f4f4f;margin-top: 10px;">
                    {{ rescue_info.board|safe }}
                </div>
            </td></tr>
        </table>
        </td></tr>
        <tr><td>
        <table style="float: right;margin-top: 20px">
            <tr>
                <td>
                {% if mail_rescue_btn %}
                    <a href="{{ rescue_info.link }}" target="_blank" class="rescue_link" style="float:left;clear: left;margin-top: 20px;">
                        <img src="{{ mail_rescue_btn.0.servingUrl }}">
                    </a>
                {% else %}
                    <a href="{{ rescue_info.link }}" target="_blank" class="rescue_link" style="float:left;clear: left;text-decoration: none;padding: 14px 35px;background: #347cbb;margin-top: 20px;display: inline-block;text-transform: uppercase;color: white;font-weight: 400;font-size: 14px;">
                        {{ T_rescue_booking }}
                    </a>
                {% endif %}
                </td>
                <td style="padding: 10px;font-size: 30px;font-weight: bold;width: 150px;text-transform: uppercase;">{{ T_precio_total }}</td>
                <td style="background: #0060AC;padding: 10px;color: white;text-align: center" class="price">
                    <span style="font-size: 40px;font-weight: bold;">{{ rescue_info.price }}&nbsp;{{ currency }}</span><br>
                    {% if rescue_info.price_without_tax %}
                    <span style="font-size: 20px;font-weight: bold;">{{ rescue_info.price_without_tax }}&nbsp;{{ currency }}</span><br>
                    {% endif %}
                    {% if rescue_info.tax_differential %}
                    <span style="font-size: 20px;font-weight: bold;">{{ rescue_info.tax_differential }}&nbsp;{{ currency }}</span><br>
                    {% endif %}
                </td>
            </tr>
            <tr><td colspan="2" style="color: #999;font-style: italic;">{{ T_24_precio }}</td></tr>
        </table>
        </td></tr>
        </table>
    </div>
    </div>
    {# End #}

    {% for banner in mail_extra_banner %}
        <div style="width: 900px;margin: 25px auto;">
            {% if banner.description|safe %}
                <div style="font-size: 30px;line-height: 35px;font-weight: bold;margin-bottom:15px;">{{ banner.description|safe }}</div>
            {% endif %}
            {% if banner.linkUrl %}
                <a href="{{ banner.linkUrl }}">
                    <img src="{{ banner.servingUrl }}=s900">
                </a>
            {% else %}
                <img src="{{ banner.servingUrl }}=s900">
            {% endif %}
        </div>
    {% endfor %}

    {% for footer in mail_footer %}
        <div style="width: 900px;margin: 50px auto;">
            {{ footer.description|safe }}
        </div>
    {% endfor %}

</div>
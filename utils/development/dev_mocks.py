from models.reservations import Reservation
from models.users import UserClub


def build_mock(model, data, **kwargs):
    for key, value in data.items():
        setattr(model, key, value)

    if kwargs:
        for key, value in kwargs.items():
            if hasattr(model, key):
                setattr(model, key, value)

    return model

def build_mock_user(**kwargs):
    mock_data = {
        'idmember': 111111111111,
        'username': '<EMAIL>',
        'password': 'verylonooogpasswordtoberemoved13843038',
        'password_hash': '',
        'name': 'Name',
        'surname': 'Surname',
        'gender': 'F',
        'civil_status': 'married',
        'childs_number': '1',
        'job': '',
        'email': '<EMAIL>',
        'address': 'Address',
        'city': 'City',
        'province': 'Province',
        'pais': 'ES',
        'postal_code': '29111',
        'dni': '22333444A',
        'telephone': '666555666',
        'hotels': [""],
        'referal': '',
        'holiday_time': '',
        'holiday_mate': '',
        'favourite_destiny': '',
        'birthday': '1970-01-01',
        'timestamp': '2024-01-01 00:00:00',
        'fakebirthday': '1970-01-01',
        'language': 'SPANISH',
        'comments': 'User comments',
        'extraInfo': '{"promotions_accept": true}',
        'loyalty_level': 'CLASSIC'
    }
    return build_mock(UserClub(), mock_data, **kwargs)


def build_mock_reservation(**kwargs):
    mock_data = {
        'timestamp': '2020-01-01 00:00:00',
        'identifier': '111111111111',
        'startDate': '2020-01-01',
        'endDate': '2020-01-05',
        'promocode': 'PROMOCODE',
        'numRooms': 1,
        'adults1': 2,
        'kids1': 0,
        'babies1': 0,
        'adults2': 0,
        'kids2': 0,
        'babies2': 0,
        'adults3': 0,
        'kids3': 0,
        'babies3': 0,
        'comments': 'Comments',
        'roomType1': 'ag5zfm9hc2lzaG90ZWxlc3IVCxIIUm9vbVR5cGUYgICA2PrdiQoMogEWb2FzaXNob3RlbGVzLW9hc2lzcGFsbQ',
        'roomType2': '',
        'roomType3': '',
        'regimen': 'ag5zfm9hc2lzaG90ZWxlc3IUCxIHUmVnaW1lbhiAgICY1u-FCgyiARZvYXNpc2hvdGVsZXMtb2FzaXNwYWxt',
        'rate': 'ag5zfm9hc2lzaG90ZWxlc3IRCxIEUmF0ZRiAgID4oY2HCgyiARZvYXNpc2hvdGVsZXMtb2FzaXNwYWxt',
        'priceIncrease': '',
        'promotions': 'ag5zfm9hc2lzaG90ZWxlc3IWCxIJUHJvbW90aW9uGICAgOHA3aIIDKIBFm9hc2lzaG90ZWxlcy1vYXNpc3BhbG0',
        'price': '1000.00',
        'priceSupplements': '0',
        'amendedPrice': '',
        'prefix': '',
        'name': 'Name',
        'lastName': 'Last Name',
        'address': 'Adress',
        'country': 'ES',
        'geolocation': 'ES',
        'email': '<EMAIL>',
        'telephone': '666555444',
        'creditCard': 'pasarela@@1111111@@0.0',
        'invalidCreditCard': '',
        'cancelled': False,
        'additionalServices': '',
        'additionalServices2': '',
        'incidents': '',
        'language': 'SPANISH',
        'source': 'Web',
        'priceByDay': [],
        'discountByDay': [],
        'media': '',
        'birthday': '1970-01-01',
        'cancellationTimestamp': '',
        'modificationTimestamp': '',
        'agent': '',
        'extraInfo': '{"price_room_1": 46794.13, "agesByRoom": {}, "real_promotions_keys": ["ag5zfm9hc2lzaG90ZWxlc3IWCxIJUHJvbW90aW9uGICAwIKH0I4IDKIBGG9hc2lzaG90ZWxlcy1ncmFuZGNhbmN1bg", "ag5zfm9hc2lzaG90ZWxlc3IWCxIJUHJvbW90aW9uGICAgPj4-IoJDKIBGG9hc2lzaG90ZWxlcy1ncmFuZGNhbmN1bg"], "real_promotions_name": {"ag5zfm9hc2lzaG90ZWxlc3IWCxIJUHJvbW90aW9uGICAwIKH0I4IDKIBGG9hc2lzaG90ZWxlcy1ncmFuZGNhbmN1bg": "Oferta 3 day", "ag5zfm9hc2lzaG90ZWxlc3IWCxIJUHJvbW90aW9uGICAgPj4-IoJDKIBGG9hc2lzaG90ZWxlcy1ncmFuZGNhbmN1bg": "10% SILVER"}, "device": "Web", "utc_current_timestamp": "2024-02-09 22:26:59", "currency": "EUR", "original_referer": "https://booking.oasishoteles.com/booking2?sid=622c798d-224f-4740-b810-6526d2c2e0aa&namespace=oasishoteles-grandcancun", "geo_location_info": {"ip_address": "************"}, "accomodation_tax_total": 63.0, "gateway_type": "COBRADOR", "prices_per_day": {"1: ag5zfm9hc2lzaG90ZWxlc3IVCxIIUm9vbVR5cGUYgICAuKjsmQoMogEYb2FzaXNob3RlbGVzLWdyYW5kY2FuY3Vu": {"22/04/2024": ["16881.00", "10196.12", "6684.88"], "23/04/2024": ["16881.00", "10196.12", "6684.88"], "24/04/2024": ["16881.00", "10196.12", "6684.88"], "25/04/2024": ["16881.00", "10196.12", "6684.88"], "26/04/2024": ["16881.00", "10196.12", "6684.88"], "27/04/2024": ["16881.00", "10196.12", "6684.88"], "28/04/2024": ["16881.00", "10196.12", "6684.85"], "total": 46794.13}}, "birthday": "1900-1-6", "numfactu": "", "numflight": "", "stripe_customer_id": "cus_PX6g4aC25M5JbO", "personalId": "", "additional_services_keys": "ag5zfm9hc2lzaG90ZWxlc3IXCxIKU3VwcGxlbWVudBiAgICTo-2UCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4 - name: Transportaci\u00f3n Redondo Aeropuerto - Hotel - Aeropuerto - cantidad: 1 - dias: 1 - precio: 0.0;", "transfer_service": [{"to": {"service_name": "Transportaci\u00f3n Redondo Aeropuerto - Hotel - Aeropuerto", "service_quantity": 1.0, "service_info": {}}}, {"from": {"service_name": "Transportaci\u00f3n Redondo Aeropuerto - Hotel - Aeropuerto", "service_quantity": 1.0, "service_info": {}}}, {"extra_info_fields": {}}], "RateConditionsOrigin": {"key": "ag5zfm9hc2lzaG90ZWxlc3IRCxIEUmF0ZRiAgID44fKACgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "condition_key": "ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgID4of-PCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "rateConditionDescription": "<div><div><span style=\\"font-weight: 700;\\">PARA PODER HACER CHECK IN, ES NECESARIO PRESENTARSE CON LA CONFIRMACI\u00d3N DE RESERVA Y COMPROBANTE DE PAGO AL MOMENTO DE PRESENTARSE EN RECEPCI\u00d3N&nbsp;</span><span style=\\"font-weight: 700;\\"><u><br><br>Pol\u00edtica de Cancelaci\u00f3n y Modificaci\u00f3n</u></span></div><div>&nbsp;</div><div>&nbsp;<span style=\\"font-weight: 700;\\">Tarifa Flexible:&nbsp;</span>El tiempo relativo para la modificaci\u00f3n o cancelaci\u00f3n de la reserva depende de la temporada:</div><div>-&nbsp;<u>Temporada baja:</u>&nbsp;Puedes solicitar la cancelaci\u00f3n o modificaci\u00f3n de tu reservas hasta 1 d\u00eda antes de tu llegada sin penalizaci\u00f3n. Pasadas las 3 de la tarde del d\u00eda anterior a tu llegada, la penalidad ser\u00e1 del 100%.&nbsp;</div><div>-&nbsp;<u>Temporada alta:</u>&nbsp;Puedes solicitar la cancelaci\u00f3n o modificaci\u00f3n de tu reserva con m\u00e1s de 8 d\u00edas de anticipaci\u00f3n a la llegada. Las cancelaciones o modificaciones hechas con 8 d\u00edas o menos de anticipaci\u00f3n, tendr\u00e1n cargos de la estancia completa. Si la reserva ha sido modificada para llegar en fechas posteriores a la original, la pol\u00edtica de cancelaci\u00f3n y modificaciones siempre tomar\u00e1 como referencia la fecha de la llegada original.&nbsp;</div><div><br></div><div><div><div><div><span style=\\"font-weight: 700;\\">Special Season</span></div><div>- 23 de Marzo 2024 a 31 de Marzo 2024</div><div>- 24 de Diciembre 2024 a 2 de Enero 2025&nbsp;</div><div>- 11 de Abril 2025 a 19 de Abril 2025</div><div><br></div><div></div></div><div><span style=\\"font-weight: 700;\\">Temporada Alta</span></div></div><div>- 2 de enero 2024 a 22 de Marzo 2024</div><div>- 7 de Julio 2024 a 18 de Agosto 2024</div><div>- 2 de Enero 2025 a 31 de Marzo 2025</div><div>- 7 de Julio 2025 a 18 de Agosto 2025</div><div><br></div><div><span style=\\"font-weight: 700;\\">Temporada Baja&nbsp;</span></div><div>- 1 de Abril 2024 a 6 de Julio 2024</div><div>- 19 de Agosto 2024 a 23 de Diciembre 2024</div><div>- 1 de Abril 2025 a 10 de Abril 2025&nbsp;</div><div>- 20 de Abril 2025 a 6 de Julio 2025</div><div>- 19 de Agosto 2025 a 23 de Diciembre 2025</div><div><br></div></div><div>&nbsp;</div><div><div><u><span style=\\"font-weight: 700;\\">Pol\u00edtica de No Show</span></u></div></div><div>En caso de que el hu\u00e9sped no se presente en la fecha reservada, se cobrar\u00e1 la estancia completa.</div><div>Los no shows son considerados por cualquier situaci\u00f3n en la que el hu\u00e9sped no se presente en el hotel, incluyendo sin limitarse a: vuelos retrasados, tr\u00e1mites migratorios, documentos no v\u00e1lidos, etc.</div><div>&nbsp;</div><div><u><span style=\\"font-weight: 700;\\">Pol\u00edtica de Salidas Anticipadas</span></u><div>Se har\u00e1 el cargo de la estancia completa.</div><div>&nbsp;</div><div>&nbsp;</div><div><u><span style=\\"font-weight: 700;\\">Especificaciones</span></u></div><div>- En hospedaje existe un cobro directo en recepci\u00f3n por concepto de derecho de saneamiento ambiental y un cargo de fee short de $9 USD por habitaci\u00f3n por noche para todos los hoteles de playa. Para los hoteles Smart Canc\u00fan y Oh! The Urban Oasis, el cobro ser\u00e1 de $7 USD por habitaci\u00f3n por noche.&nbsp;</div><div><br></div><div>- En hospedaje los menores son considerados de 3 a 12 a\u00f1os. De 13 a\u00f1os cumplidos al momento del check-in en adelante se consideran como tarifa de adulto.</div><div><br></div><div>- En hospedaje, seguro de estudiantes: Los clientes hospedados en Pyramid, Grand Oasis Cancun, Grand Oasis Palm y Oasis Palm entre 18 y 25 a\u00f1os deber\u00e1n pagar el check-in, una p\u00f3liza no reembolsable de $30 USD por persona. Estudiantes&nbsp; / J\u00f3venes viajeros se definen como cualquier hu\u00e9sped de 25 a\u00f1os de edad y menor viajando sin padre / tutor legal y es aplicable en todas las propiedades Oasis mencionadas, durante las fechas de la temporada de Spring Brake durante los meses Febrero - Abril.</div></div><div>&nbsp;</div><div>- Para poder hacer check - in es necesario presentar una identificaci\u00f3n oficial de cada uno de los hu\u00e9spedes. En el caso de los menores de deber\u00e1 presentar una identificaci\u00f3n con fotograf\u00eda (puede ser escolar), su Curp, acta de nacimiento o Pasaporte.<div>&nbsp;</div><div></div><div><u><span style=\\"font-weight: 700;\\">Pago con meses sin intereses</span></u></div><div>Bancos participantes Afirme, American Express, BanBajio, Bancoppel, Banjercito, BBVA, Banca Mifiel, Banco Azteca, Banco Famsa, Banco Invex, Banco Multiva, Banorte, Banregio, CitiBanamex, Falabella, Hey Banco, HSBC, Inbursa, Konfio, Liverpool, NanoPay, Nubank, RappidCard, Santander, Scotiabank</div><div>Montos m\u00ednimos para meses sin intereses 600EUR</div><div>Aplican Restricciones: El pago a meses sin intereses s\u00f3lo puede realizarse a trav\u00e9s de la liga de pago. Los pagos directos en recepci\u00f3n aplican en una sola exhibici\u00f3n.</div><div>&nbsp;</div><div><u><span style=\\"font-weight: 700;\\">Facturaci\u00f3n</span></u></div><div>- En caso de requerir factura, por favor enviar correo a:</div><div>Para reservas en The Pyramid Cancun o Grand Oasis Cancun: <EMAIL>&nbsp;</div><div>Para reservas en The Sens Cancun, Grand Oasis Palm o Oasis Palm: <EMAIL>, <EMAIL></div><div>Para reservas en Grand Oasis Tulum: <EMAIL></div><div>Para reservas en Smart Cancun o Oh! The Urban Oasis: <EMAIL>,&nbsp;<EMAIL>&nbsp;</div><div>Es necesario enviar los datos para la factura antes de su llegada. No se podr\u00e1n realizar facturas una vez abandonada la propiedad.</div><div>- Para dep\u00f3sitos o anticipos hechos para reservas o servicios futuros, puede solicitar una factura de anticipo y, llegada la fecha de su servicio, se realizar\u00e1 la factura completa y la nota de cr\u00e9dito correspondiente a la factura del anticipo.&nbsp;</div><div>&nbsp;</div><div><u><span style=\\"font-weight: 700;\\">Compensaciones</span></u></div><div>Todas las compensaciones se har\u00e1n seg\u00fan corresponda.</div><div>&nbsp;</div></div></div>\\n", "cancellationPolicy": "1 d\u00eda", "name": "Tarifa Flexible Orewards Silver", "localName": "FLEXIBLE SILVER MXN", "cancellationPeriods": ["2023-04-17;2023-07-05;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgID4kqqUCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "2023-07-06;2023-08-20;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgICE3KqOCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "2023-08-21;2023-12-23;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgID4kqqUCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "2023-12-24;2024-04-07;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgICE3KqOCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "2024-04-08;2024-07-06;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgID4kqqUCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "2024-07-07;2024-08-18;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgICE3KqOCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "2024-08-19;2024-12-23;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgID4kqqUCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4"]}, "RateConditionsOriginForHotel": {"key": "ag5zfm9hc2lzaG90ZWxlc3IRCxIEUmF0ZRiAgID44fKACgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "condition_key": "ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgID4of-PCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "rateConditionDescription": "<div><div><span style=\\"font-weight: 700;\\">PARA PODER HACER CHECK IN, ES NECESARIO PRESENTARSE CON LA CONFIRMACI\u00d3N DE RESERVA Y COMPROBANTE DE PAGO AL MOMENTO DE PRESENTARSE EN RECEPCI\u00d3N&nbsp;</span><span style=\\"font-weight: 700;\\"><u><br><br>Pol\u00edtica de Cancelaci\u00f3n y Modificaci\u00f3n</u></span></div><div>&nbsp;</div><div>&nbsp;<span style=\\"font-weight: 700;\\">Tarifa Flexible:&nbsp;</span>El tiempo relativo para la modificaci\u00f3n o cancelaci\u00f3n de la reserva depende de la temporada:</div><div>-&nbsp;<u>Temporada baja:</u>&nbsp;Puedes solicitar la cancelaci\u00f3n o modificaci\u00f3n de tu reservas hasta 1 d\u00eda antes de tu llegada sin penalizaci\u00f3n. Pasadas las 3 de la tarde del d\u00eda anterior a tu llegada, la penalidad ser\u00e1 del 100%.&nbsp;</div><div>-&nbsp;<u>Temporada alta:</u>&nbsp;Puedes solicitar la cancelaci\u00f3n o modificaci\u00f3n de tu reserva con m\u00e1s de 8 d\u00edas de anticipaci\u00f3n a la llegada. Las cancelaciones o modificaciones hechas con 8 d\u00edas o menos de anticipaci\u00f3n, tendr\u00e1n cargos de la estancia completa. Si la reserva ha sido modificada para llegar en fechas posteriores a la original, la pol\u00edtica de cancelaci\u00f3n y modificaciones siempre tomar\u00e1 como referencia la fecha de la llegada original.&nbsp;</div><div><br></div><div><div><div><div><span style=\\"font-weight: 700;\\">Special Season</span></div><div>- 23 de Marzo 2024 a 31 de Marzo 2024</div><div>- 24 de Diciembre 2024 a 2 de Enero 2025&nbsp;</div><div>- 11 de Abril 2025 a 19 de Abril 2025</div><div><br></div><div></div></div><div><span style=\\"font-weight: 700;\\">Temporada Alta</span></div></div><div>- 2 de enero 2024 a 22 de Marzo 2024</div><div>- 7 de Julio 2024 a 18 de Agosto 2024</div><div>- 2 de Enero 2025 a 31 de Marzo 2025</div><div>- 7 de Julio 2025 a 18 de Agosto 2025</div><div><br></div><div><span style=\\"font-weight: 700;\\">Temporada Baja&nbsp;</span></div><div>- 1 de Abril 2024 a 6 de Julio 2024</div><div>- 19 de Agosto 2024 a 23 de Diciembre 2024</div><div>- 1 de Abril 2025 a 10 de Abril 2025&nbsp;</div><div>- 20 de Abril 2025 a 6 de Julio 2025</div><div>- 19 de Agosto 2025 a 23 de Diciembre 2025</div><div><br></div></div><div>&nbsp;</div><div><div><u><span style=\\"font-weight: 700;\\">Pol\u00edtica de No Show</span></u></div></div><div>En caso de que el hu\u00e9sped no se presente en la fecha reservada, se cobrar\u00e1 la estancia completa.</div><div>Los no shows son considerados por cualquier situaci\u00f3n en la que el hu\u00e9sped no se presente en el hotel, incluyendo sin limitarse a: vuelos retrasados, tr\u00e1mites migratorios, documentos no v\u00e1lidos, etc.</div><div>&nbsp;</div><div><u><span style=\\"font-weight: 700;\\">Pol\u00edtica de Salidas Anticipadas</span></u><div>Se har\u00e1 el cargo de la estancia completa.</div><div>&nbsp;</div><div>&nbsp;</div><div><u><span style=\\"font-weight: 700;\\">Especificaciones</span></u></div><div>- En hospedaje existe un cobro directo en recepci\u00f3n por concepto de derecho de saneamiento ambiental y un cargo de fee short de $9 USD por habitaci\u00f3n por noche para todos los hoteles de playa. Para los hoteles Smart Canc\u00fan y Oh! The Urban Oasis, el cobro ser\u00e1 de $7 USD por habitaci\u00f3n por noche.&nbsp;</div><div><br></div><div>- En hospedaje los menores son considerados de 3 a 12 a\u00f1os. De 13 a\u00f1os cumplidos al momento del check-in en adelante se consideran como tarifa de adulto.</div><div><br></div><div>- En hospedaje, seguro de estudiantes: Los clientes hospedados en Pyramid, Grand Oasis Cancun, Grand Oasis Palm y Oasis Palm entre 18 y 25 a\u00f1os deber\u00e1n pagar el check-in, una p\u00f3liza no reembolsable de $30 USD por persona. Estudiantes&nbsp; / J\u00f3venes viajeros se definen como cualquier hu\u00e9sped de 25 a\u00f1os de edad y menor viajando sin padre / tutor legal y es aplicable en todas las propiedades Oasis mencionadas, durante las fechas de la temporada de Spring Brake durante los meses Febrero - Abril.</div></div><div>&nbsp;</div><div>- Para poder hacer check - in es necesario presentar una identificaci\u00f3n oficial de cada uno de los hu\u00e9spedes. En el caso de los menores de deber\u00e1 presentar una identificaci\u00f3n con fotograf\u00eda (puede ser escolar), su Curp, acta de nacimiento o Pasaporte.<div>&nbsp;</div><div></div><div><u><span style=\\"font-weight: 700;\\">Pago con meses sin intereses</span></u></div><div>Bancos participantes Afirme, American Express, BanBajio, Bancoppel, Banjercito, BBVA, Banca Mifiel, Banco Azteca, Banco Famsa, Banco Invex, Banco Multiva, Banorte, Banregio, CitiBanamex, Falabella, Hey Banco, HSBC, Inbursa, Konfio, Liverpool, NanoPay, Nubank, RappidCard, Santander, Scotiabank</div><div>Montos m\u00ednimos para meses sin intereses EUR</div><div>Aplican Restricciones: El pago a meses sin intereses s\u00f3lo puede realizarse a trav\u00e9s de la liga de pago. Los pagos directos en recepci\u00f3n aplican en una sola exhibici\u00f3n.</div><div>&nbsp;</div><div><u><span style=\\"font-weight: 700;\\">Facturaci\u00f3n</span></u></div><div>- En caso de requerir factura, por favor enviar correo a:</div><div>Para reservas en The Pyramid Cancun o Grand Oasis Cancun: <EMAIL>&nbsp;</div><div>Para reservas en The Sens Cancun, Grand Oasis Palm o Oasis Palm: <EMAIL>, <EMAIL></div><div>Para reservas en Grand Oasis Tulum: <EMAIL></div><div>Para reservas en Smart Cancun o Oh! The Urban Oasis: <EMAIL>,&nbsp;<EMAIL>&nbsp;</div><div>Es necesario enviar los datos para la factura antes de su llegada. No se podr\u00e1n realizar facturas una vez abandonada la propiedad.</div><div>- Para dep\u00f3sitos o anticipos hechos para reservas o servicios futuros, puede solicitar una factura de anticipo y, llegada la fecha de su servicio, se realizar\u00e1 la factura completa y la nota de cr\u00e9dito correspondiente a la factura del anticipo.&nbsp;</div><div>&nbsp;</div><div><u><span style=\\"font-weight: 700;\\">Compensaciones</span></u></div><div>Todas las compensaciones se har\u00e1n seg\u00fan corresponda.</div><div>&nbsp;</div></div></div>\\n", "cancellationPolicy": "1 d\u00eda", "name": "Tarifa Flexible Orewards Silver", "localName": "FLEXIBLE SILVER MXN", "cancellationPeriods": ["2023-04-17;2023-07-05;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgID4kqqUCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "2023-07-06;2023-08-20;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgICE3KqOCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "2023-08-21;2023-12-23;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgID4kqqUCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "2023-12-24;2024-04-07;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgICE3KqOCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "2024-04-08;2024-07-06;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgID4kqqUCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "2024-07-07;2024-08-18;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgICE3KqOCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "2024-08-19;2024-12-23;ag5zfm9hc2lzaG90ZWxlc3IaCxINUmF0ZUNvbmRpdGlvbhiAgID4kqqUCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4"]}, "original_rooms": {"1": {"id": "ag5zfm9hc2lzaG90ZWxlc3IVCxIIUm9vbVR5cGUYgICAuKjsmQoMogEYb2FzaXNob3RlbGVzLWdyYW5kY2FuY3Vu", "adults": 3.0, "kids": 0.0, "babies": 0.0}}, "session_search_info": {"id": "3302bb21-847f-4996-b24c-8db83538663c", "timestamp": "2024-02-09 17:20:39"}, "clubMember": 110144.0, "clubMember_user_points": 7.0, "clubMember_user_points_by_reservation": 7.0, "clubMember_level": "Silver", "price_info": {"total_without_taxes": 41720.871968616266, "total_with_taxes": 46794.13, "total_with_all_taxes": 47869.2153242332, "taxes": {"country": {"value": 5073.258031383732, "included": true, "percentage": 12.16}, "accommodation": {"included": false, "value": 1075.0853242332}}}, "utm_label_generated": "Organic", "external_identifier": [{"Reserva": "GOCRS240236950", "Linea": 1}], "external_identifier_for_manager": "GOCRS240236950", "last_merchant_order_used": "46553984", "internal_comments": [{"user": "oasishoteles.shoffman", "comment": " se cxl por falta de pago ", "timestamp": "2024-04-23 06:27:43"}, {"user": "oasishoteles.jarias", "comment": "cliente no contesta llamada, se envia correo por falta de pago tkt 1261280 ", "timestamp": "2024-04-22 18:14:57"}], "history": [{"timestamp": "2024-04-22 23:27:47", "username": "oasishoteles.shoffman", "change": "Callcenter comments added:  se cxl por falta de pago "}, {"timestamp": "2024-04-22 23:27:47", "username": "oasishoteles.shoffman", "change": "Modified: additionalService 2 name: from  to {}"}], "room_owner_information": {"1": {"adults": [{"name": "", "lastname": "", "personalId": "", "email": ""}, {"name": "", "lastname": "", "personalId": "", "email": ""}], "kids": []}}, "comments_callseeker": " se cxl por falta de pago ", "history_modification": [{"username": "oasishoteles.shoffman", "timestamp": "2024-04-22 23:27:47", "rooms": [{"adults": 3.0, "babies": 0.0, "index_room": "1", "kids": 0.0, "price": "46794.13", "rate_key": "ag5zfm9hc2lzaG90ZWxlc3IRCxIEUmF0ZRiAgID44fKACgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "regimen_key": "ag5zfm9hc2lzaG90ZWxlc3IUCxIHUmVnaW1lbhiAgICYlpKTCwyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4", "room_key": "ag5zfm9hc2lzaG90ZWxlc3IVCxIIUm9vbVR5cGUYgICAuKjsmQoMogEYb2FzaXNob3RlbGVzLWdyYW5kY2FuY3Vu"}], "services": "ag5zfm9hc2lzaG90ZWxlc3IXCxIKU3VwcGxlbWVudBiAgICTo-2UCgyiARhvYXNpc2hvdGVsZXMtZ3JhbmRjYW5jdW4 - name: Transportaci\u00f3n Redondo Aeropuerto - Hotel - Aeropuerto - cantidad: 1 - dias: 1 - precio: 0.0;", "price": "46794.13", "priceSupplements": "0.0", "startDate": "22/04/2024", "endDate": "22/04/2024"}], "save_from_manager2": true, "cancelled_by": "oasishoteles.shoffman", "cancellation_reason": " se cxl por falta de pago "}'
    }
    return build_mock(Reservation(), mock_data, **kwargs)
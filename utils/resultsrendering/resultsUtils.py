# -*- coding: utf-8 -*-

'''

Utilities related to the way in which we present results to the user

i.e. If we want to structure them the same way that booking does


'''
import copy
import json
import logging
import re
import uuid
from collections import OrderedDict
from functools import cmp_to_key

from flask import request

from booking_process.components import render_hooks
from booking_process.constants import advance_configs_names
from booking_process.constants.advance_configs_names import REGIMEN_ORDER, ROUND_DECIMAL_BOOKING
from booking_process.constants.advance_configs_names import ROOM_ORDER, \
    USE_BOOKING_STRUCTURE_FOR_RESULTS, USE_HOLIDAY_STRUCTURE_FOR_RESULTS, TRUNCATE_DECIMAL_BOOKING, \
    USE_RATE_CONDITIONS_IN_PROMOTION, FILTER_NON_PROMOTED_RATES, RATES_LOCK_VERSION
from booking_process.utils.booking.conditions.rate_conditions import get_text_condition_by_condition_key, \
    get_condition_from_key
from booking_process.constants.session_data import SPECIAL_RATE_INFO, PRICE_OPTION_KEY_PREFIX, \
    PRICE_OPTION_KEY_PREFIX_V2
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import build_friendly_url
from booking_process.utils.development.dev_booking_utils import DEV
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.performance.timerdecorator import timeit

from booking_process.utils.session import session_manager
from booking_process.utils.users.users_methods import club_is_active


def sortRatesForSearch(a, b, order):
    nameA = a['rateStructure'][3]
    nameB = b['rateStructure'][3]

    if nameA and nameB:
        for current in order:
            if current in nameA:
                return -1
            if current in nameB:
                return 1

    # If not explicitly ordered whe use min price as criteria for sorting
    if a['minValue'] > b['minValue']:
        return 1
    else:
        return -1


def reorgRateResult(rateResult):
    rateOrder = get_config_property_value(advance_configs_names.RATE_ORDER)

    if rateOrder:
        rateOrder = rateOrder.split(";")
        rateResult = sorted(rateResult, key=cmp_to_key(lambda x, y: sortRatesForSearch(x, y, rateOrder)), reverse=False)
        return rateResult

    else:
        rateResult.sort(key=lambda x: x['minValue'], reverse=False)
        return rateResult


def hasPrefix(name, candidates):
    '''Returns true if any candidate is a prefix for the given name'''

    for candidate in candidates:
        if candidate in name and candidate != name:
            return True

    return False


def getSpanishRoomNameFromResult(result):
    return result['roomStructure'][0]['spanishName']


def getRoomNameFromResult(result):
    return result['roomStructure'][0]['roomName']


def getMainRooms(results):
    allNames = [getRoomNameFromResult(room) for room in results]

    mainRooms = [name for name in allNames if not hasPrefix(name, allNames)]

    logging.info("Main Rooms: %s" % mainRooms)

    return mainRooms


def findPrefixStructure(currentName, results):
    for room in results:
        currentName2 = getRoomNameFromResult(room)
        if currentName2 in currentName and currentName != currentName2:
            return room


def getCanonicalRateName(rateStructure):
    return rateStructure['rateStructure'][0].strip().replace(".", "").upper()


def reorgResultsTruncatePrices(result, round_decimal=False):
    '''
    it truncate all prices to int
    Pay attention, that's not a really round, is just a truncate
    '''
    price_func = lambda l: int(float(l))
    if round_decimal:
        price_func = lambda l: int("%.0f" % float(l))

    for room in result:
        for singleRate in room["rateResult"]:
            for regRes in singleRate["regimenResult"]:
                myPrice = regRes["roomResults"]
                if "perDay" in myPrice: myPrice["perDay"] = price_func(myPrice["perDay"])
                if "total" in myPrice: myPrice["total"] = price_func(myPrice["total"])
                if "promotionTotal" in myPrice: myPrice["promotionTotal"] = price_func(myPrice["promotionTotal"])
                if "promotionPerDay" in myPrice: myPrice["promotionPerDay"] = price_func(myPrice["promotionPerDay"])

    return result


def clean_deletable_rates_by_policy(django_results, resultStructure, by_days):
    '''
    :param results:
    :param deleteable_option: list with  format ["%s-%s-%s-%s" % (room_key, rate_key_to_delete, regimenKey, identifier)]
    :return:

    fmatheis: Note that we have to add the identifier of the rate as we might have different rates under the same rate structure

    '''

    logging.info("clean_deletable_rates_by_policy configured. By_days %s", by_days)

    # Loop all rates to get the bes by policies!!
    # the structure is this: condition_key-board_id -> {min price, rate_key]
    min_prices_by_condition_rate = {}
    delete_this_rates_boards_from_results = []

    logging.info("clean_deletable_rates_by_policy num rooms: %s", len(django_results))

    for room in django_results:
        if type(room) is dict:
            delete_this_rates_boards_from_results = find_expesieves_rates_by_policies(room, resultStructure, by_days,
                                                                                      min_prices_by_condition_rate,
                                                                                      delete_this_rates_boards_from_results)
        elif type(room) is list:
            for a_room in room:
                delete_this_rates_boards_from_results = find_expesieves_rates_by_policies(a_room, resultStructure,
                                                                                          by_days,
                                                                                          min_prices_by_condition_rate,
                                                                                          delete_this_rates_boards_from_results)

    logging.info("clean_deletable_options: %s", delete_this_rates_boards_from_results)

    for room in django_results:
        if type(room) is dict:
            updated_cleaned_rates_by_policies(room, delete_this_rates_boards_from_results)
        elif type(room) is list:
            for a_room in room:
                updated_cleaned_rates_by_policies(a_room, delete_this_rates_boards_from_results)

    return django_results


def find_expesieves_rates_by_policies(room, resultStructure, by_days, min_prices_by_condition_rate,
                                      delete_this_rates_boards_from_results):
    # be careful with searches of several rooms
    room_key = ""
    for single_room_option in room.get("roomStructure", [{}]):
        if room_key:
            room_key += "@@"
        room_key += single_room_option.get("selected_capacity_string", "") + single_room_option.get("roomKey", "")

    is_toggle_club = club_is_active() and get_config_property_value(RATES_LOCK_VERSION) == 'toggle_discount'

    for rate in room.get("rateResult", []):
        if len(rate.get("rateStructure")) > 2:
            rate_key = rate.get("rateStructure")[2]
            boards = rate.get("regimenResult", [])
            rate_identifier = rate['identifiers_list'][0] if rate.get('identifiers_list') else rate.get(
                'rateIdentifier')
            is_club_rate = rate.get("logged_users")
            for i, regimen in enumerate(boards):
                regimen_key = regimen.get("regimenStructure", {}).get("regimenKey")

                if regimen['roomResults'].get('promotionTotal'):
                    final_regimen_price = float(regimen['roomResults']['promotionTotal'])
                else:
                    final_regimen_price = float(regimen['roomResults']['total'])

                # obtein the of the condition
                is_not_a_package = True
                try:
                    complete_rate_structure = resultStructure.get(rate_key, ["", "", rate_key, "", {}])

                    # checkin if it nos a packege
                    if len(complete_rate_structure) > 5:
                        is_not_a_package = "__" not in complete_rate_structure[5]

                    if by_days:
                        rate_condition_key = complete_rate_structure[4].get("cancellationPolicy")
                    else:
                        rate_condition_key = complete_rate_structure[4].get("rate_condition_key")

                except Exception as e:
                    logging.warning("Exception trying to get rate_condition_key: %s", e)
                    rate_condition_key = ""

                if rate_condition_key and is_not_a_package:

                    condition_board_key = "%s-%s-%s" % (room_key, rate_condition_key, regimen_key)
                    if is_toggle_club and is_club_rate:
                        condition_board_key = f'{condition_board_key}-club_rate'

                    # save the best
                    info_min_prices_by_condition_rate = min_prices_by_condition_rate.get(condition_board_key, {})
                    if not info_min_prices_by_condition_rate:
                        # first condition-board found!
                        info_min_prices_by_condition_rate = {"min_price": final_regimen_price, "rate_key": rate_key,
                                                             'rate_identifier': rate_identifier}
                        min_prices_by_condition_rate[condition_board_key] = info_min_prices_by_condition_rate

                    if final_regimen_price < info_min_prices_by_condition_rate.get("min_price"):
                        # a best price FOUND for same BOARD CONDITION!!!!!
                        # save the rate_board to delete and remeenber the min price
                        logging.info("best prices found for condition and board %s", condition_board_key)
                        rate_key_to_delete = info_min_prices_by_condition_rate.get("rate_key")
                        rate_identifier_to_delete = info_min_prices_by_condition_rate.get('rate_identifier')
                        logging.info("deleting room rate board: %s %s %s %s", room_key, rate_key_to_delete, regimen_key,
                                     rate_identifier_to_delete)
                        delete_this_rates_boards_from_results.append(
                            "%s-%s-%s-%s" % (room_key, rate_key_to_delete, regimen_key, rate_identifier_to_delete))
                        min_prices_by_condition_rate[condition_board_key] = {"min_price": final_regimen_price,
                                                                             "rate_key": rate_key,
                                                                             'rate_identifier': rate_identifier}

                    elif not final_regimen_price == info_min_prices_by_condition_rate.get("min_price"):
                        # i'm sure this optionn is more expensive
                        logging.info("deleting2 room rate board: %s %s %s", room_key, rate_key, regimen_key)
                        logging.info(
                            "deleting2 final_regimen_price: %s info_min_prices_by_condition_rate.get-min_price %s",
                            final_regimen_price, info_min_prices_by_condition_rate.get("min_price"))
                        if DEV:
                            logging.info("Deleting rate identifier: %s", rate_identifier)
                            logging.info("Deleting room: %s" % room.get("roomStructure", [{}])[0].get("roomName"))
                            logging.info("Deleting board: %s" % regimen.get("regimenStructure", {}).get("regimenName"))
                            logging.info("Deleting rate: %s" % rate.get('rateStructure')[3])

                        delete_this_rates_boards_from_results.append(
                            "%s-%s-%s-%s" % (room_key, rate_key, regimen_key, rate_identifier))

    return delete_this_rates_boards_from_results


def updated_cleaned_rates_by_policies(room, delete_this_rates_boards_from_results):
    # room_key = room.get("roomStructure", [{}])[0].get("roomKey")

    # be careful with searches of several rooms
    room_key = ""
    for single_room_option in room.get("roomStructure", [{}]):
        if room_key:
            room_key += "@@"
        room_key += single_room_option.get("selected_capacity_string", "") + single_room_option.get("roomKey", "")

    final_rates_in_room = []
    for rate in room.get("rateResult", []):
        if len(rate.get("rateStructure")) > 2:
            rate_key = rate.get("rateStructure")[2]
            boards = rate.get("regimenResult", [])
            new_cleaned_boards = []
            rate_identifier = rate['identifiers_list'][0] if rate.get('identifiers_list') else rate['rateIdentifier']
            for i, regimen in enumerate(boards):
                regimen_key = regimen.get("regimenStructure", {}).get("regimenKey")
                if regimen_key:
                    possible_key_deletable = "%s-%s-%s-%s" % (room_key, rate_key, regimen_key, rate_identifier)
                    # logging.info("serching for posible_key_deletable %s ", possible_key_deletable)
                    if possible_key_deletable in delete_this_rates_boards_from_results:
                        logging.info("OPTION DELETABLE!!!")
                    else:
                        new_cleaned_boards.append(regimen)

            rate["regimenResult"] = new_cleaned_boards
            if not len(new_cleaned_boards):
                logging.info("rate full removed:  %s", rate_key)
            else:
                final_rates_in_room.append(rate)

    if final_rates_in_room:
        room["rateResult"] = final_rates_in_room
    else:
        logging.info("DONT CLEANING ROOM %s because it would be EMPTY!", room_key)


def reorgResultsByRoomType(results, filter_re, filter_board):
    '''
    We filter all results with a RE, searching in rooms's name

    We also filter boards in the case that we receive a board filter.
    '''
    result = []

    for room in results:

        # If a room filter is defined we use it to remove some rooms
        if filter_re:
            currentName = getSpanishRoomNameFromResult(room)
            try:
                if not re.search(filter_re, currentName, re.IGNORECASE):
                    continue
            except Exception as e:
                logging.error("Failed to search regex '%s' in room name '%s'" % (filter_re, currentName))
                logging.error(e)

        # if filter_board is defined we filter the boards
        if filter_board:

            # For each Rate, we filter boards and remove it if no board is left
            new_rate_result = []

            for singleRate in room["rateResult"]:

                allBoards = singleRate["regimenResult"]

                # If the board is valid we keep it, if not we filter it
                # Note that here we are not using a regular expression
                board_satisfies_expression = lambda x: x["regimenStructure"].get('spanishName',
                                                                                 '').lower().strip() in filter_board

                singleRate["regimenResult"] = list(filter(board_satisfies_expression, allBoards))

                if len(singleRate["regimenResult"]) > 0:
                    new_rate_result.append(singleRate)

            room["rateResult"] = new_rate_result

            if len(room["rateResult"]) == 0:
                continue

        # If we reach here it means that we have successfully survived to all filters
        result.append(room)

    return result


def reorgResultsForBookingStyleStructure(results):
    '''
    In charge of reorganizing the results so that it appears in the same way as in Booking.

    i.e.
    If we have the following rooms: Room 1, Room1 Non Refundable, Room1 with Breakfast
    It will be reorganized to:

    Room1
    - Stay Only
    - With Breakfast
    Room1 Non Refundable
    - Stay Only



    :param results: Standard estructure
    :return: Nothing (a new structure reorganized)
    '''
    result = []

    mainRooms = getMainRooms(results)

    for room in results:
        currentName = getRoomNameFromResult(room)

        # Main room
        if currentName in mainRooms:
            result.append(room)

        # Room with a different board, to be added to main Room
        else:
            mainRoomStructure = findPrefixStructure(currentName, results)
            mainRoomName = getRoomNameFromResult(mainRoomStructure)

            # Find out new board name
            newBoardName = currentName[len(mainRoomName):]

            for index in range(0, len(room['rateResult'])):
                myBoard = room['rateResult'][index]['regimenResult'][0]
                myBoard['regimenStructure']['regimenName'] = newBoardName.title()
                myBoard['regimenStructure']['spanishName'] = newBoardName.title()

                rate_used_in_room = room['rateResult'][index]['rateStructure'][2]

                found_main_structure = False

                for current_main_structure in mainRoomStructure['rateResult']:
                    rate_in_current_main_structure = current_main_structure['rateStructure'][2]

                    if rate_used_in_room == rate_in_current_main_structure:
                        current_main_structure['regimenResult'].append(myBoard)
                        found_main_structure = True
                        break

                if not found_main_structure:
                    mainRoomStructure['rateResult'].append(room['rateResult'][index])

    # Fix the case in which we have just one board and it is not the default one (i.e. con Desayuno)
    for room in results:
        for rateResult in room['rateResult']:
            if len(rateResult['regimenResult']) == 1 and _room_name_includes_board(room):
                _fix_names_of_room_and_board(room)

    return result


def _fix_names_of_room_and_board(room):
    language = session_manager.get('language')

    if room['roomStructure'][0]['spanishName'].lower().endswith("con desayuno"):
        if language and language == SPANISH:
            board_name = "Con Desayuno"
        else:
            board_name = "With Breakfast"

    else:
        if language and language == SPANISH:
            board_name = "Con media pensión"
        else:
            board_name = "Halfboard included"

    for board in room['rateResult']:
        board['regimenResult'][0]['regimenStructure']['regimenName'] = board_name


def _room_name_includes_board(room):
    '''
    Returns True if the given room we think it is a base room (i.e. without including a board) but it really includes it

    This is a fix to a bug that was happening at Solvasa Valencia when the only available boards was AD

    '''
    return room['roomStructure'][0]['spanishName'].lower().endswith("con desayuno") or room['roomStructure'][0][
        'spanishName'].lower().endswith("con media pensión")


def reorgResultsForHolidayStyleStructure(results):
    '''
    Input: roomA -> [RateA -> (SA, DA), RateB -> (MP, TI)]

    Output: roomA -> RateA -> (SA, DA, MP, TI)
    '''
    newResults = []
    for roomStructure in results:

        # Group rates in order to merge them
        ratesToMerge = OrderedDict()
        packages_to_avoid = {}

        for rateStructure in roomStructure['rateResult']:
            canonicalName = getCanonicalRateName(rateStructure)
            if rateStructure.get('package_info'):
                packages_to_avoid.setdefault(canonicalName, []).append(rateStructure)
                continue

            if canonicalName not in ratesToMerge:
                ratesToMerge[canonicalName] = []

            ratesToMerge[canonicalName].append(rateStructure)

        roomStructure['rateResult'] = []

        for name, rates_list in list(packages_to_avoid.items()):
            available_package_names = [x['package_info']['package_name'] for x in rates_list]
            for package_name in set(available_package_names):
                mergedRate = {'identifiers_list': []}
                mergedRate['rateStructure'] = [name] + rates_list[0]['rateStructure'][1:]

                # Note that we have to define an empty default identifier, because while the hotel is being created we might not find that identifier in the resultStructure
                mergedRate['identifiers_list'] += [x.get('rateIdentifier', '') for x in rates_list]
                newBoards = []
                for rate_element in rates_list:
                    if rate_element.get('package_info', {}).get('package_name') == package_name:
                        mergedRate['package_info'] = rate_element['package_info']
                        newBoards.extend(rate_element['regimenResult'])

                mergedRate['minValue'] = float(min([x.get('roomResults', {}).get('total') for x in newBoards]))
                mergedRate['regimenResult'] = newBoards

                roomStructure['rateResult'].append(mergedRate)

        for name, hotels in list(ratesToMerge.items()):

            # Note that we might run into a hotel with a cheaper Board (other than the canonical, i.e. MP cheaper than SA for an occupation like at Puentereal)
            hotels.sort(key=lambda x: x['rateStructure'][0])

            mergedRate = {'identifiers_list': []}
            mergedRate['rateStructure'] = [name] + hotels[0]['rateStructure'][1:]
            mergedRate['minValue'] = min([x['minValue'] for x in hotels])

            # Note that we have to define an empty default identifier, because while the hotel is being created we might not find that identifier in the resultStructure
            mergedRate['identifiers_list'] += [x.get('rateIdentifier', '') for x in hotels]
            newBoards = []
            for hotel in hotels:
                newBoards.extend(hotel['regimenResult'])
            mergedRate['regimenResult'] = newBoards

            roomStructure['rateResult'].append(mergedRate)

        roomStructure['rateResult'] = reorgRateResult(roomStructure['rateResult'])

        newResults.append(roomStructure)

    return newResults


def sortRoomsForSearch(a, b, order):
    nameA = build_friendly_url(a.get('roomStructure')[0].get('spanishName').replace(" ", ""))
    nameB = build_friendly_url(b.get('roomStructure')[0].get('spanishName').replace(" ", ""))

    for current in order:
        if nameA == build_friendly_url(current.replace(" ", "")):
            return -1
        if nameB == build_friendly_url(current.replace(" ", "")):
            return 1

    return 0


def sortRoomsByRegex(a, b, regex):
    nameA = a.get('roomStructure')[0].get('spanishName').lower()
    nameB = b.get('roomStructure')[0].get('spanishName').lower()
    regex = regex.lower()

    if re.search(regex, nameA):
        return -1

    if re.search(regex, nameB):
        return 1

    return 0


def sortRoomsForSearchKey(a, b, order):
    keyA = a.get('roomStructure')[0].get('roomKey')
    keyB = b.get('roomStructure')[0].get('roomKey')

    if keyA == order:
        return -1
    elif keyB == order:
        return 1

    return 0


def sortRoomsForSearchTupla(a, b, order):
    nameA = build_friendly_url(a[1].get('spanishName').replace(" ", ""))
    nameB = build_friendly_url(b[1].get('spanishName').replace(" ", ""))

    for current in order:
        if nameA == build_friendly_url(current.replace(" ", "")):
            return -1
        if nameB == build_friendly_url(current.replace(" ", "")):
            return 1

    return 0


def sortIconsRoom(a, b, order):
    orderA = a.get('title')
    orderB = b.get('title')

    for current in order:
        if orderA == current:
            return -1
        if orderB == current:
            return 1

    return 0


def sortRoomsForRoomsNames(x, y):
    try:
        nameA = x.get('roomStructure')[0].get('spanishName')
        nameB = y.get('roomStructure')[0].get('spanishName')

        if len(nameA) > len(nameB):
            return 1
        elif len(nameA) < len(nameB):
            return -1
        else:
            if nameA > nameB:
                return 1
            elif nameA < nameB:
                return -1

        return 0

    except Exception as e:
        logging.info("Error sorting rooms alphabetically")
        return 0


def sortRoomsForSearchBasedOnCheapest(x, y):
    x_list = [float(rateResult['minValue']) for rateResult in x['rateResult'] if _rate_is_eligible_for_price_sort(rateResult)]
    y_list = [float(rateResult['minValue']) for rateResult in y['rateResult'] if _rate_is_eligible_for_price_sort(rateResult)]

    minX = min(x_list) if x_list else 9999
    minY = min(y_list) if y_list else 9999

    if minX < minY:
        return -1
    else:
        return 1


def _rate_is_eligible_for_price_sort(rate_result):
    rates_to_test = rate_result['identifiers_list'] if isinstance(rate_result.get("identifiers_list", ""), list) else [rate_result.get("rateIdentifier", "")]
    invalid_rates_found = [i for i in rates_to_test if i and i.startswith("__")]
    return len(invalid_rates_found) < 1


def reorg_results_for_promotions_as_rates(result, language):
    '''
    Promotions with RateCondition are converted to new rates
    '''
    for room in result:
        new_rates = _reorg_rates_of_room_for_promotions_as_rates(language, room['rateResult'])
        new_rates = reorgRateResult(new_rates)

        room['rateResult'] = new_rates

    return result


def _is_room_result_a_promotion_to_be_converted_to_rate(room_results, language=SPANISH):
    if room_results.get('promotionRateConditionStruct'):
        return room_results.get('promotionRateCondition') and room_results.get('promotionAsRate')
    rate_conditions = get_condition_from_key(room_results.get('promotionRateCondition'), language)
    if rate_conditions:
        logging.info(
            'Rate conditions found for %s: %s' % (room_results.get('promotionName'), rate_conditions.get('name')))
        return room_results.get('promotionRateCondition') and room_results.get('promotionAsRate')

    return False


def _promotion_already_added_as_rate(new_rates, regimen):
    promotion_name = regimen['roomResults']['promotionAsRate']['name']
    return [x for x in new_rates if x['rateStructure'][0] == promotion_name]


def _remove_promoted_promotion_from_rate(rate):
    min_value = 1000000.0

    for board in rate['regimenResult']:
        if _is_room_result_a_promotion_to_be_converted_to_rate(board['roomResults']):

            promotion_as_rate_name = board['roomResults']['promotionAsRate']['name']
            promotion_as_rate_key = board['roomResults']['promotionAsRate']['key']

            board['roomResults']['promotionName'] = " + ".join(
                [x['name'] for x in board['roomResults']['promotionNameList'] if x['name'] != promotion_as_rate_name])
            board['roomResults']['promotionNameList'] = [x for x in board['roomResults']['promotionNameList'] if
                                                         x['name'] != promotion_as_rate_name]

            promotion_as_rate_per_day = board['roomResults']['promotionAsRate']['value_per_day']
            promotion_as_rate_value = board['roomResults']['promotionAsRate']['value']

            board['roomResults']['promotionPerDay'] = str(
                float(board['roomResults']['promotionPerDay']) + promotion_as_rate_value / len(
                    promotion_as_rate_per_day))
            board['roomResults']['promotionTotal'] = str(
                float(board['roomResults']['promotionTotal']) + promotion_as_rate_value)
            board['roomResults']['total4Class'] = board['roomResults']['promotionTotal']
            board['roomResults']['totalDiscount'] = board['roomResults']['totalDiscount'] - promotion_as_rate_value

            board['roomResults']['hasPromotion'] = len(board['roomResults']['promotionNameList']) > 0

            uuid = str(board['roomResults']['uuid'])

            price_option = session_manager.get(PRICE_OPTION_KEY_PREFIX + uuid)
            price_option[3] = board['roomResults']['total4Class']
            price_option[4] = board['roomResults']['promotionName'].replace(promotion_as_rate_name, '')
            price_option[5] = None

            price_option_v2 = session_manager.get(PRICE_OPTION_KEY_PREFIX_V2 + uuid)

            # The price per day should not decrease with the promotion
            # price_option_v2['pricesPerDay'] = [x - promotion_as_rate_per_day[i] for i, x in enumerate(price_option_v2['pricesPerDay'])]

            price_option_v2['promotion']['multiple_promotions'] = [x for x in
                                                                   price_option_v2['promotion']['multiple_promotions']
                                                                   if x['key'] != promotion_as_rate_key]
            price_option_v2['promotion']['value'] = price_option_v2['promotion']['value'] - promotion_as_rate_value
            price_option_v2['promotion']['valuesPerDay'] = [x - promotion_as_rate_per_day[i] for i, x in
                                                            enumerate(price_option_v2['promotion']['valuesPerDay'])]
            price_option_v2['promotion']['promotion'] = price_option_v2['promotion']['promotion'].replace(
                promotion_as_rate_key, '')
            price_option_v2['promotion']['promotionName'] = price_option_v2['promotion']['promotionName'].replace(
                promotion_as_rate_name, '')

            percentage = (float(board['roomResults']['totalDiscount']) / float(board['roomResults']['total'])) * 100
            promotion_percentage = str(round(percentage))
            board['roomResults']['promotionPercentage'] = promotion_percentage

            # If not other promotions let's remove it completely
            if not price_option_v2['promotion']['multiple_promotions']:
                price_option_v2['promotion'] = None

            session_manager.set(PRICE_OPTION_KEY_PREFIX + uuid, price_option)
            session_manager.set(PRICE_OPTION_KEY_PREFIX_V2 + uuid, price_option_v2)

        if board['roomResults'].get('promotionTotal') and float(board['roomResults']['promotionTotal']) < min_value:
            min_value = float(board['roomResults']['promotionTotal'])

        #Does not have sense to use this as min value, due to total4class remove decimals and could generate prices differences because of that
        #elif float(board['roomResults']['total4Class']) < min_value:
            #min_value = float(board['roomResults']['total4Class'])
        elif float(board['roomResults']['total']) < min_value:
            min_value = float(board['roomResults']['total'])

    rate['minValue'] = min_value


def _remove_everything_besides_promotion(new_rates, promoted_promotions):
    result = [x for x in new_rates if x.get('rateIdentifier') in promoted_promotions]
    return result


def _reorg_rates_of_room_for_promotions_as_rates(language, rates_in_room):
    new_rates = []

    promoted_promotions = []

    for rate in rates_in_room:

        update_old_rate = True
        new_rate = copy.deepcopy(rate)

        regimen_result_temp = []

        current_boards = new_rate['regimenResult']

        new_rate['regimenResult'] = []

        for board in current_boards:

            if _is_room_result_a_promotion_to_be_converted_to_rate(board['roomResults']):

                promoted_promotions.append(board['roomResults']['promotionAsRate']['name'])

                _transform_board_for_promotion(language, new_rate, board)

                # Have we already created a new Rate with this promotion? If Yes, we add the regimen to it
                existing_rate = _promotion_already_added_as_rate(new_rates, board)
                if not existing_rate:
                    # We only keep the previous rate if needed once,
                    if new_rate['rateStructure'][1] != rate['rateStructure'][1]:
                        update_old_rate = False

                    new_rates.append(new_rate)

            else:
                regimen_result_temp.append(board)

        if update_old_rate:
            # We might have removed all, in which case we don
            if regimen_result_temp:
                rate['regimenResult'] = regimen_result_temp
                _remove_promoted_promotion_from_rate(rate)
                new_rates.append(rate)

        else:
            _remove_promoted_promotion_from_rate(rate)
            new_rates.append(rate)

    if promoted_promotions and get_config_property_value(FILTER_NON_PROMOTED_RATES):
        new_rates = _remove_everything_besides_promotion(new_rates, promoted_promotions)

    return new_rates


def _transform_board_for_promotion(language, new_rate, board):
    promotion_name = board['roomResults']['promotionAsRate']['name']
    new_rate['rateStructure'][0] = promotion_name
    new_rate['rateStructure'][3] = promotion_name
    new_rate['rateIdentifier'] = promotion_name

    if new_rate.get('identifiers_list'):
        new_rate['identifiers_list'] = [promotion_name]

    if new_rate.get('extra_info_rate', {}).get('visual_rate_name'):
        new_rate['extra_info_rate']['visual_rate_name'] = promotion_name

    promotion_conditions = board['roomResults'].get('promotionAsRate', {}).get("description")
    if not promotion_conditions:
        promotion_conditions = get_text_condition_by_condition_key(board['roomResults'].get('promotionRateCondition'),
                                                                   language)
    new_rate['rateStructure'][1] = promotion_conditions
    new_rate['rateStructure'][2] = board['roomResults']['promotionAsRate']['key'] + "___" + new_rate['rateStructure'][2]

    # regimen['roomResults']['promotion_percentage'] = None
    # regimen['roomResults']['total'] = str(float(regimen['roomResults']['total']) - promotion_total)
    # regimen['roomResults']['totalDiscount'] = regimen['roomResults']['totalDiscount'] - promotion_total

    board['roomResults']['promotionName'] = " + ".join(
        [x['name'] for x in board['roomResults']['promotionNameList'] if x['name'] != promotion_name])
    board['roomResults']['promotionNameList'] = [x for x in board['roomResults']['promotionNameList'] if
                                                 x['name'] != promotion_name]

    # price_per_day = board['roomResults']['promotionAsRate']['value_per_day']
    # promotion_total = board['roomResults']['promotionAsRate']['value']
    # board['roomResults']['perDay'] = str(float(board['roomResults']['perDay']) - promotion_total / len(price_per_day))

    # regimen['roomResults']['promotionPercentage'] = str(int(round(100 * regimen['roomResults']['totalDiscount'] / float(regimen['roomResults']['total']))))

    new_rate['regimenResult'].append(board)

    rate_info_struct = {
        'name': promotion_name,
        'conditions': promotion_conditions,
        'language': language,
        'remaining_promotions_name': board['roomResults']['promotionName'],
        'rateConditionKey': board['roomResults'].get('promotionRateCondition')
    }
    # Note that sometimes we have to add new results, not only replace the old ones
    my_uuid = str(board['roomResults']['uuid'])
    new_uuid = str(uuid.uuid1())
    board['roomResults']['uuid'] = new_uuid

    session_manager.set(SPECIAL_RATE_INFO + new_uuid, rate_info_struct)
    session_manager.set(PRICE_OPTION_KEY_PREFIX + new_uuid,
                        copy.deepcopy(session_manager.get(PRICE_OPTION_KEY_PREFIX + my_uuid)))
    session_manager.set(PRICE_OPTION_KEY_PREFIX_V2 + new_uuid,
                        copy.deepcopy(session_manager.get(PRICE_OPTION_KEY_PREFIX_V2 + my_uuid)))


@timeit
def organize_results(result: list[dict], language: str) -> list[dict]:
    """
    Organize and order results based on hotel configuration.

    :param result: Final builded results
    :param language: Current language
    :return: Organized results
    """

    if get_config_property_value(USE_BOOKING_STRUCTURE_FOR_RESULTS):
        logging.info("Hotel with Booking like structure")
        result = reorgResultsForBookingStyleStructure(result)

    elif get_config_property_value(USE_HOLIDAY_STRUCTURE_FOR_RESULTS):
        logging.info("Hotel with Holiday like structure")
        result = reorgResultsForHolidayStyleStructure(result)

    if get_config_property_value(USE_RATE_CONDITIONS_IN_PROMOTION):
        logging.info("Hotel with Promotions as Rates")
        result = reorg_results_for_promotions_as_rates(result, language)

    room_filter = request.values.get('roomFilter', '')
    board_filter = request.values.get('boardFilter', '')
    if room_filter or board_filter:
        logging.info("Filtering results by room type. Room: %s. Board: %s" % (room_filter, board_filter))
        result = reorgResultsByRoomType(result, room_filter, board_filter.lower().strip())

    order_results(result)

    render_hooks('evaluate_booking1_results', result)

    return result


def order_results(results: list[dict]) -> list[dict]:
    """
    Order final results based on hotel configuration.

    :param results: Final builded results
    :return: Ordered results
    """

    # Sort by regimen
    for room in results:
        for rateResult in room['rateResult']:
            if custom_board_order := get_config_property_value(REGIMEN_ORDER):
                rateResult['regimenResult'].sort(
                    key=cmp_to_key(lambda x, y: sort_regimens_for_search(x, y, custom_board_order.split(";"))),
                    reverse=False)
            else:
                rateResult['regimenResult'].sort(
                    key=lambda x: float(x['roomResults']['total']) if not x['roomResults'].get(
                        'promotionTotal') else float(x['roomResults']['promotionTotal']), reverse=False)

    # Sort by names
    results.sort(key=cmp_to_key(lambda x, y: sortRoomsForRoomsNames(x, y)))

    # Sort by rates
    results.sort(key=cmp_to_key(lambda x, y: sortRoomsForSearchBasedOnCheapest(x, y)))

    # A Explicit order has been defined in Hotel Manager
    if roomOrder := get_config_property_value(ROOM_ORDER):
        orderList = roomOrder.split(";")
        results.sort(key=cmp_to_key(lambda x, y: sortRoomsForSearch(x, y, orderList)), reverse=False)

    elif request.values.get("roomOrder"):
        results.sort(key=cmp_to_key(lambda x, y: sortRoomsForSearchKey(x, y, request.values.get("roomOrder"))),
                    reverse=False)

    if request.values.get('roomSearch'):
        results.sort(key=cmp_to_key(lambda x, y: sortRoomsByRegex(x, y, request.values.get("roomSearch"))),
                    reverse=False)

    return results


def fix_prices_truncate_round_from_result(result):
    convert_to_apply = lambda v: v

    if get_config_property_value(TRUNCATE_DECIMAL_BOOKING):
        convert_to_apply = lambda v: int(v)

    elif get_config_property_value(ROUND_DECIMAL_BOOKING):
        convert_to_apply = lambda v: float("%.2f" % v)

    result['response'] = json.loads(result['response'])

    for promo in result['response'].get("promotions"):
        for k1, v1 in list(promo.items()):
            for k2, v2 in list(v1.items()):
                for k3, v3 in list(v2.items()):
                    v3['value'] = convert_to_apply(v3['value'])
                    for index, v4 in enumerate(v3.get("valuesPerDay")):
                        v3['valuesPerDay'][index] = convert_to_apply(v4)

                    for v5 in v3.get("multiple_promotions"):
                        for index, v6 in enumerate(v5.get("price")):
                            v5['price'][index] = convert_to_apply(v6)

    for option in result['response'].get("resultsPerDay"):
        for k1, v1 in list(option.items()):
            for k2, v2 in list(v1.items()):
                for k3, v3 in list(v2.items()):
                    for index, v4 in enumerate(v3):
                        v3[index] = convert_to_apply(v4)

    for index, option in enumerate(result['response'].get("result")):
        for k1, v1 in list(option.items()):
            for k2, v2 in list(v1.items()):
                for k3, v3 in list(v2.items()):
                    result['response']['result'][index][k1][k2][k3] = convert_to_apply(v3)

    result['response'] = json.dumps(result['response'])


def sort_regimens_for_search(a, b, order):
    nameA = build_friendly_url(a.get('regimenStructure').get('spanishName'))
    nameB = build_friendly_url(b.get('regimenStructure').get('spanishName'))

    for current in order:
        if nameA == build_friendly_url(current):
            return -1
        if nameB == build_friendly_url(current):
            return 1

    return 0


def get_cheapest_result(results: list) -> dict:
    # Results structure can be different depending on search, we need to unified it.
    def flatten(results):
        for item in results:
            if isinstance(item, list):
                yield from flatten(item)
            else:
                yield item

    flattened_results = list(flatten(results))
    cheapest_room = {}
    current_price = float('inf')

    try:
        for result in flattened_results:
            for rate in result.get('rateResult', []):
                for regimen in rate.get('regimenResult', []):
                    room_result = regimen.get('roomResults', {})
                    if not room_result:
                        continue

                    room_price = float(room_result.get('promotionTotal') if room_result.get('hasPromotion') else room_result.get('total'))
                    if room_price and room_price < current_price:
                        cheapest_room = room_result
                        current_price = room_price
    except Exception:
        logging.error(f"Can't get cheapest result from results: {results}")

    return cheapest_room
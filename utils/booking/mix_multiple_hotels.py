import datetime
import logging

from flask import request
from google.appengine.ext import db

from booking_process.constants import advance_configs_names
from booking_process.constants.session_data import PRICE_OPTION_KEY_PREFIX, MOTHER_NAMASPACE_FOR_MIXES, \
	MOTHER_SID_FOR_MIXES, SELECTED_OPTION_KEY
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.namespaces.namespace_utils import get_namespace, set_namespace
from booking_process.utils.packages import package_utils
from booking_process.utils.session import session_manager


def mix_separated_hotels_by_dates(config, results):
	dates_split = config.split(";dates=")
	main_definition = dates_split[0]

	get_datetime = lambda x: datetime.datetime.strptime(x, '%Y-%m-%d')

	search  = results['search']
	search_start_datetime = get_datetime(search['startDate'])
	search_end_datetime = get_datetime(search['endDate'])

	dates_configs = dates_split[1].split("@")
	datetime_config = lambda x: datetime.datetime.strptime(x, '%d-%m-%Y')
	for date_element in dates_configs:
		start_date, end_date, mix_config = date_element.split("|")
		start_datetime = datetime_config(start_date)
		end_datetime = datetime_config(end_date)

		if search_start_datetime >= start_datetime and search_start_datetime <= end_datetime:
			return mix_config

		if search_end_datetime >= start_datetime and search_end_datetime <= end_datetime:
			return mix_config


	return main_definition


def build_booking1_mix_hotels_context(my_params: list, language: str):
	"""
	Function to build the mix hotels context for booking1

	@param my_params: Parameters for template rendering
	@param language: Language of the search
	"""
	# TODO: Move mix hotel data from booking1 here (booking1.py:2048)
	custom_title_mix_hotels_b1 = get_config_property_value(advance_configs_names.SECTION_HOTEL_MIX_INFO)
	if custom_title_mix_hotels_b1:
		custom_title_mix_hotels_b1_section = get_section_from_section_spanish_name(custom_title_mix_hotels_b1, language)
		if custom_title_mix_hotels_b1_section:
			my_params['custom_title_mix_hotels_b1'] = custom_title_mix_hotels_b1_section.get("content", "")


def prepare_environment_for_mixed_hotels(mix_multiple_results):
	# Copy current session to new namespace
	# Change namespace
	current_session = session_manager.get_current_session()

	uuid = request.values.get("selectedUUID") or session_manager.get(SELECTED_OPTION_KEY)

	logging.info("SelectedUUID: %s", uuid)

	current_uuid = str(uuid.split(";")[0])
	current_price_option_key_prefix = session_manager.get(PRICE_OPTION_KEY_PREFIX + current_uuid)

	if not current_price_option_key_prefix:
		logging.info("It doesn't exist the session")
		return None, None

	selected_option = current_price_option_key_prefix[0]

	if package_utils.is_package(selected_option):
		package_key, selected_option = package_utils.get_package_and_rate_from_key(selected_option)

	new_namespace = db.Key(selected_option).namespace()
	candidate_namespace = [x for x in mix_multiple_results.split(";") if new_namespace in x]

	# Note that the namespace might be the default one
	if candidate_namespace:

		mother_of_namespaces = get_namespace()
		mother_of_sid = session_manager.get_session_id()

		session_manager.set(MOTHER_NAMASPACE_FOR_MIXES, mother_of_namespaces)
		session_manager.set(MOTHER_SID_FOR_MIXES, mother_of_sid)
		logging.info("setting for mixes original namespace and sid: %s %s", mother_of_namespaces, mother_of_sid)

		logging.info("Changing namespace to: %s", candidate_namespace[0])
		set_namespace(candidate_namespace[0])

	else:
		session_manager.set(MOTHER_NAMASPACE_FOR_MIXES, None)
		session_manager.set(MOTHER_SID_FOR_MIXES, None)

	# Copy current_session_to new_namespace
	session_manager.initSession(createNew=True)
	new_session = session_manager.get_current_session()
	new_session.content = current_session.content
	session_manager.safe_save_to_datastore()

	return new_namespace, candidate_namespace

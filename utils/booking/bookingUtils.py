# -*- coding: utf-8 -*-
import base64
import copy
import html.parser
import io
import json
import logging
import re
import unicodedata
import urllib
import urllib.error
import urllib.parse
import urllib.request
import uuid
import zipfile
from collections import OrderedDict
from datetime import datetime, timedelta

import requests
from flask import request
from pytz import timezone

from booking_process.components import render_hooks
from booking_process.constants import advance_configs_names
from booking_process.constants.advance_configs_names import SHOW_SUMMARY_PACKAGE_PRICE, \
    SAVE_SHOPPING_CART_OCCUPANCY_BY_ROOM, PAY_TAX_AT_HOTEL, FIXING_SHOPPING_CART_WITHOUT_MERGING, \
    CALLCENTER_RESERVATION_AS_BOOKING_ENGINE, APPLY_DISCOUNT_BONO_ANDALUZ, ROUND_DECIMAL_BOOKING
from booking_process.constants.advance_configs_names import USE_RATE_CONDITIONS_IN_PROMOTION, CUSTOM_DOMAIN, \
    ACCOMODATION_TAX_INCREMENT_BY_PAX_NIGHT, MANAGEMENT_LANGUAGE, \
    DISABLE_CREDIT_CARD, PASSBOOK_IN_BOOKING_CONFIRMATIONS, GATEWAY_SPECIAL_COUNTRIES_BY_TOKEN, \
    VALIDATE_PROMOCODE_BY_EMAIL, EMAIL_COPY_BY_TITLE
from booking_process.constants.session_data import COUNTRY, CURRENCY_BY_RATE, FROM_MY_BOOKING, GATEWAY_COMISSION, \
    GATEWAY_CURRENCY_CONVERSION, \
    EXTERNAL_SERVICES, BOOKING3_CUSTOM_FIELDS, GATEWAY_EXTERNAL_ID, UNIQUE_USAGE_LINK, FORCE_DATATRANS_BOOKING3_TPV, \
    FORCE_CANCEL_POLICY_DAYS, ON_REQUEST, PAYMENT_WAYS_BY_COBRADOR, FORCED_BOOKING_IDENTIFIER
from booking_process.constants.web_configs_names import BOOKING_TRANSFER_SERVICE, WHATSAPP_COMMUNICATION
from booking_process.libs.communication import directDataProvider, hotelManagerCommunicator
from booking_process.libs.communication.data.remote_data_provider import REMOTE_PREFIX
from booking_process.libs.communication.retryDecorator import retry
from booking_process.libs.pasarelas.gateway_constants import AMOUNT_SENT_TO_GATEWAY, PAYMENT_GATEWAY_FAILED, \
    TOTAL_PRICE_FORCED, \
    SERMEPA_CC_TOKEN_ID, SERMEPA_CC_EXPIREDATE, TPV_PAYMENT_PENDING, COMMENT_TRICK_TPV_BYPASS, DISCOUNT_APPLIED, \
    SERMEPA_TYPE_CC, SERMEPA_BRAND_CC, \
    RESERVATION_SENT_TO_MANAGER, CARD_TOKEN_TPV, GATEWAY_PROCESS_STARTED, SERMEPA_COF_TXNID, TPV_TYPE_PAYMENT_SELECTED, \
    PAYMENT_GATEWAY, DICT_TO_UPDATE_IN_EXTRA_INFO, NAME_METHOD_PAYMENT_IN_USE, AVOID_R_IDENTIFIER, RULE_APPLIED, \
    SERMEPA, BIZUM, GATEWAY_ORDER_ID, CODE, IDENTIFIER
from booking_process.libs.pasarelas.sibs.sibs_constants import SIBS_MULTIBANCO, SIBS_PAYMENT
from booking_process.models.payment.credit_card_model import CreditCardDetails
from booking_process.models.payment.datatrans.datatrans_credit_card_model import DatatransCreditCardDetails
from booking_process.utils.agencies.agencies_utils import _calculate_agency_prices, \
    is_agency_logged_with_disabled_club, is_agency_logged
from booking_process.utils.agencies.constants import AGENCY_INFO, MANAGER_SOURCE_AGENCY, AGENCY_WEB_CONFIGURATION
from booking_process.utils.analytics.analyticsUtils import save_analytics_campaigns_in_extra_info
from booking_process.utils.auditing import auditUtils
from booking_process.utils.auditing.auditUtils import makeTraceback
from booking_process.utils.booking import common_data_provider
from booking_process.utils.booking.additional_services.additional_services_methods import \
    retreive_days_and_amount_from_service_calendar, add_uuid_on_advanced_additional_services, \
    get_additional_services_from_reservation
from booking_process.utils.booking.additional_services.additional_services_seeker_utils import build_booked_service_data
from booking_process.utils.booking.additional_services.additional_services_utils import is_booking2_react_active
from booking_process.utils.booking.ages.ages_utils import get_rooms_ages_from_session
from booking_process.utils.booking.bank_transfer import is_bank_transfer_request
from booking_process.utils.booking.billing_data.billing_utils import reservation_has_billing_data
from booking_process.utils.booking.booking3_utils.booking3_context import get_config_autofill_booking3
from booking_process.utils.booking.common_data_provider import get_all_rates_entity_json_map, get_rate
from booking_process.utils.booking.extra_utils.gift_bonos import GiftBonoController
from booking_process.utils.booking.normalizationUtils import to_string_encoding
from booking_process.utils.booking.occupancy.occupancy_utils import get_room_occupancy_dict, \
    get_occupancy_data
from booking_process.utils.booking.personal_details import personalDetailsUtils
from booking_process.utils.booking.rates.rate_info import rateIsNonRefundable
from booking_process.utils.booking.reservations.modification_utils import build_modification_extra_info, \
    get_original_reservation_extra_info_to_persist
from booking_process.utils.booking.searchs.search_info import get_num_rooms
from booking_process.utils.booking.selections.selection_price_utils import getSelectedPrice, \
    get_original_price_from_selected_price, getTotalPrice, getPriceSupplements
from booking_process.utils.booking.selections.selection_utils import getSelectedRoomKey, getSelectedRegimen, \
    getSelectedRateKey, getSelectedRoom
from booking_process.utils.booking.supplements.supplements_utils import reservation_has_supplement
from booking_process.utils.bookingConstants import CANCELLATION_POLICY_MULTIPLE_POLICIES, RATE_CONDITIONS_ORIGIN, \
    RATE_CONDITIONS_ORIGIN_FOR_HOTEL, REMOVED_PROMOCODE, IDENTIFIER_CHECKER_URL, LATE_BOOKING, STATUS_RESERVATION, \
    ADMIN_MANAGER_SERVER_2
from booking_process.constants.session_data import AGENT_ID, SOURCE, SELECTED_ADDITIONAL_SERVICES, \
    SELECTED_OPTION_KEY, PRICE_OPTION_KEY_PREFIX, RATE_NAME_AND_DESC, MERGED_PRICES_SUPPLEMENTS, \
    SUPPLEMENT_PRICE_PER_AMOUNT_PREFIX, PRICE_OPTION_KEY_PREFIX_V2, INCLUDE_IN_EXTRA_INFO_FROM_SEARCH, \
    REAL_AGES_KIDS, BOOKING_IS_MOBILE, BOOKING_IS_TABLET, EXTERNAL_USER_ID, PULL_REALTIME_MAPPING, \
    FORCED_RESULT_STRUCTURE, ONLY_ROOM_MODIFICATION, EXTRA_INFO_RESERVATION, SEARCH_KEY_PREFIX, \
    DISCOUNT_PRICE_AUTOFILL_POPUP, VIRTUAL_SERVICES_SELECTED,  USER_LOGIN_INFO, LOCAL_RESIDENT_BOOKING, \
    FORCE_NET_PAYMENT, RESCUE_PROMOCODE_APPLIED, TOTAL_PETS, SPECIAL_RATE_INFO, MEMBER_CLUB_NAME, \
    LOCATION_MODIFICATION, NO_ASK_CREDIT_CARD, BONOGIFT_USED, ORIGINAL_PRICE_BEFORE_DISCOUNT, \
    TOTAL_BONO_GIFT_DISCOUNT, FORCED_PRICE_FROM_MANAGER, FORCED_ADDITIONAL_SERVICES_FROM_MANAGER, \
    MODIFICATION_FROM_MANAGER, OLD_RESERVATION, ANALYTICS_CAMPAIGN, PROMOCODE_USED, EXTRA_PARAMS_SEARCH, \
    C_TURNO_VALUE, C_ORIGEN_VALUE, HIDE_WIDGET_PROCESS, SELECTED_ADDITIONAL_SERVICES_CALENDARS, \
    PRICE_FORCED_CALLCENTER, COMMENTS_FORCED_CALLCENTER, CLUB_FUERTE_USER, CLUB_FUERTE_SESSION, PARTNER, \
    FORCED_SAVE_MANAGER2, CLUB_FUERTE_DATA_SESSION, EXTRA_DATA_PACKAGE_FORFAIT, \
    SHOPPING_CART_SELECTED, BOOKING3_LINK_TPV, USER_CALL, REFERER_BOOKING0, \
    ORIGINAL_REFERER, INFO_DETAILS_PREBOOKING, ORIGINAL_PRICE_FORCED_CALLCENTER, GCLID_INSESS, \
    RESERVATION_MODIFICATION, ERROR_CARD_SIBS, RESERVATION_DICT, COMMENTS_PREBOOKING, PROFILE_AGENCIES_FUERTE_ENABLED, \
    SERVICES_FROM_ADAPTER, CURRENCY, CURRENT_SEARCH, BOOKING_CONTENT,  PAYPAL_TOKEN, \
    BIZUM_FORCED, RETRY_MAS_OCUPACION_CAPACITY, PARAMS_SEARCH_STRING, ERROR_CC_FOUND, RESENDING_CONFIRMATION, \
    PERSONAL_DETAILS, CUSTOM_TAXES_INCREMENT_LIST, HIDE_PROCODE_IN_BOOKING_WIDGET, EXTRA_CHOICE_OPTION, \
    PAYMENT_GATEWAY_ORDER, NOT_SENT_AS_MODIFICATION, HOTELVERSE_RESERVATION_CANCELLED, \
    SELECTED_ADVANCED_ADDITIONAL_SERVICES, RESERVATION_MODIFICATION_PRICE_INFO, BOOKING_FINISHED, BOOKING_IN_PROGRESS, \
    MODIFICATION_SAVED_IN_PEP, I_AM_A_RESIDENT, BANK_TRANSFER_IS_ACTIVE, HIDE_PAYMENT_ENABLED, \
    ORIGINAL_AGENT_ID, MULTIPLE_ROOMS_WITHOUT_MERGING, \
    VALIDATE_PROMOCODE_EMAIL, CANCEL_RESERVATION_DICT, MIX_EXTERNAL_MAPPING
from booking_process.utils.callcenter.callcenter_utils import getAdditionalWebConfiguration
from booking_process.utils.clubs.club_levels import retreive_user_level
from booking_process.utils.clubs.clubs_bookings import handle_clubs_bookings
from booking_process.utils.country.configs.country_configs_utils import CountryConfigsBuilder
from booking_process.utils.currency.currencyUtils import get_currency, get_selected_currency_code, \
    get_params_for_currency_selector
from booking_process.utils.currency.currency_conversor import CurrencyConversor
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape, get_text_version, fix_unbalanced_html, \
    clean_unicode
from booking_process.utils.data_management.hotel_data import get_hotel_manager_email, get_internal_url, \
    get_account_manager
from booking_process.utils.data_management.integrations_utils import get_integration_configuration_properties, \
    get_integration_configuration
from booking_process.utils.data_management.packages_data import get_package_name
from booking_process.utils.data_management.pictures_utils import set_url_to_https, getLogotype, \
    getPicturesForKey, get_pictures_from_section_name
from booking_process.utils.data_management.promotions_utils import get_promotion
from booking_process.utils.data_management.rooms_data import getRoom, get_room
from booking_process.utils.data_management.sections_utils import get_section_from_link_url, \
    get_section_content_by_target_name
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.data_management.supplements_utils import get_special_tax_for_suplements, \
    get_all_supplements_map
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.dates.dates_management import change_string_date_format, parse_date
from booking_process.utils.development.dev_booking_utils import DEV, DEV_USE_LOCAL_HOTEL_MANAGER_2
from booking_process.utils.email.constants import PARATY_TEAM_EMAIL
from booking_process.utils.email.email_utils_gae import send_email as send_email_gae
from booking_process.utils.email.email_utils_third_party import notify_exception, send_email, \
    send_email_no_retries, notify_by_email_with_limit
from booking_process.utils.encryption import encryptionUtils
from booking_process.utils.encryption.encryptionUtils import build_encrypted_url, decrypt_data
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_web_dictionary, get_language_code, get_management_language
from booking_process.utils.manager_communicator.constants import MANAGER_SOURCE_CALL_CENTER
from booking_process.utils.marketing import starPromotion
from booking_process.utils.mobile.mobile_utils import user_agent_is_mobile, user_agent_is_tablet
from booking_process.utils.namespaces import namespace_utils
from booking_process.utils.namespaces.namespace_utils import get_namespace, get_application_id, get_hotel_code, \
    set_namespace
from booking_process.utils.on_request.constants import PENDING
from booking_process.utils.on_request.onrequest_utils import reservation_is_onrequest, get_onrequest_email_title, \
    is_onrequest_active
from booking_process.utils.packages import package_utils
from booking_process.utils.packages.package_utils import get_package_full_info, PACKAGE_PREFIX, PACKAGE_SEPARATOR
from booking_process.utils.paritymaker.paritymaker_utils import getPriceFromParityMaker, NEW_PRICE_FROM_RATECHECK, \
    ORIGINAL_PRICE_FROM_SEARCH, get_email_mark_paritymaker, \
    get_ota_paritymaker_origin, \
    PRICES_FROM_RATECHECK, normalizeBoardName
from booking_process.utils.passbook.passbook_utils import _get_passbook_generator_url, \
    _build_confirmation_passbooks_list
from booking_process.utils.pci.pci import PCIController
from booking_process.utils.pdftools import generate_pdf_with_dict, generate_pdf_with_html
from booking_process.utils.prices.prices_per_day_table_utils import build_dict_price_per_day_table, \
    build_dict_price_per_day_table_for_single_room, build_params_per_prices_table
from booking_process.utils.queues.queues_utils import defer
from booking_process.utils.request.request_utils import get_google_geo_info_from_request, get_filtered_param_value
from booking_process.utils.resultsrendering.resultsUtils import get_cheapest_result
from booking_process.utils.session import session_manager
from booking_process.utils.shopping_cart import booking_cart
from booking_process.utils.shopping_cart.booking_cart import is_enabled_shopping_cart
from booking_process.utils.shopping_cart.booking_cart_utils import is_booking_cart_v2, get_search_info_from_booking_cart
from booking_process.utils.taxes.accomodation_tax_utils import get_accomodation_total_increment
from booking_process.utils.taxes.tax_utils import get_tax_percentage, tax_is_included_in_price, remove_tax_from_price, \
    add_tax_to_price, get_package_tax_percentage, tax_is_included_in_base_price, _convert_pvp_price_to_net, \
    get_advanced_additional_services_taxes_breakdown
from booking_process.utils.templates.template_utils import build_template_2, buildTemplate
from booking_process.utils.timezone.timezone import get_current_timestamps
from booking_process.utils.transfer.transfer_utils import check_transfer_service
from booking_process.utils.transfer_service.constants import TRANSFER_SERVICE
from booking_process.utils.transfer_service.transfer_service_utils import check_external_transfer_provider_service, \
    update_info_transfer_services, get_transfer_service_for_extra_info
from booking_process.utils.validation.validate_promocode_by_email import add_promocode_email_database
from models import integrations
from models.reservations import Reservation
from utils.gift_bono.gift_bono_controller import GiftBonoPromocodeController
from utils.managers_cache.manager_cache import managers_cache
from urllib.parse import urlencode, quote


def is_test_reservation(reservation):
    """
    :unit_test: unit_tests.booking_process.utils.booking.test_bookingUtils.TestBookingUtils.test_is_test_reservation
    """
    if not reservation:
        return False

    if not reservation.comments:
        return False

    return '@@@TEST@@@' in reservation.comments


def is_last_minutes(reservation):
    start_date = datetime.strptime(reservation.startDate, '%Y-%m-%d')
    timestamp = datetime.strptime(reservation.timestamp, '%Y-%m-%d %H:%M:%S')
    if start_date < timestamp:
        return True
    return False


@retry(Exception, tries=3, delay=5, backoff=2)
def wait_booking_for_adapter_response_with_retries(adapter, identifier, hotel_email, payed_by_tpv):
    return send_booking_to_adapter(adapter, identifier, hotel_email, payed_by_tpv)


def send_booking_to_adapter(adapter, identifier, hotel_email, payed_by_tpv, simulate_error=False):
    url_adapter = get_push_reservation_url_adapter(adapter)

    if simulate_error:
        logging.info('Simulating adapter error')
        url_adapter = "https://una-web-que-no-existe.com/push_reservation?hotel_code=olee-calaceite"


    logging.info("send_booking_to_adapter: ")

    if url_adapter:

        identifier_path = "&identifier=%s" % identifier

        push_url = "%s%s" % (url_adapter, identifier_path)

        logging.info("posting modification to: %s", push_url)
        response = requests.post(push_url, timeout=60)

        if response.status_code == 200:

            logging.info("response adapter push reservation on air status: %s", response.status_code)
            logging.info("response adapter push reservation on air content: %s", response.text)

            if response.text == "OK":
                return "OK"

        logging.error("response adapter push reservation on air status: %s", response.status_code)
        logging.error("response adapter push reservation on air content: %s", response.text)





    #if reservation is already payed we cant cancel it so easily, and we have to send emails tp the hotel!!
    if  payed_by_tpv:
        logging.warning("RESERVATION PAYED BUT NOT DELIVERED TO ADATER. payed_by_tpv: %s", payed_by_tpv)
        #the adapter has return an error, BUT the reservation is already payed. What we have to do?
        #1. Make the reservation and send an alert email
        #2. Show an onrequest reservation and send an alert email

        onrequest_payed_reservation_if_error = get_config_property_value(advance_configs_names.ONREQUEST_PAYED_RESERVATION_IF_ERROR)
        if onrequest_payed_reservation_if_error:
            logging.warning("ON REQUEST CONFIRMATION")

        #send_mail_payed_reservation_not_delivered_to_manger(identifier, hotel_email, adapter, onrequest_payed_reservation_if_error)



    raise Exception


# @cache_on_datastore(lambda x: "get_push_reservation_url_adapter_%s" % x[0], tags="IntegrationConfiguration")
@managers_cache(entities='IntegrationConfiguration')
def get_push_reservation_url_adapter(adapter):
    default_url = ""

    if DEV:
        return "https://fuerte-adapter.appspot.com/push_reservation?hotel_code=olee-calaceite"

    integrationConfig = directDataProvider.get('IntegrationConfiguration', {'name': adapter})

    if integrationConfig and len(integrationConfig) > 0:
        result = integrationConfig[0]

        for item in result.configurations:
            key_value = item.split(integrations.MAP_SEPARATOR)
            if key_value[0] == "url push reservation":
                return key_value[1]
            if key_value[0] == "url":
                default_url = key_value[1]

    return default_url


# @cache_on_datastore(lambda x: "get_all_download_adapters_name", tags="IntegrationConfiguration")
@managers_cache(entities='IntegrationConfiguration')
def get_all_download_adapters_name():

    all_adapters = []
    integrations = directDataProvider.get('IntegrationConfiguration', {'downloadBooking': True})

    if integrations and len(integrations) > 0:

        for integration_config in integrations:
            all_adapters.append(integration_config.name)

    return all_adapters


def send_modification_to_adapter(identifier, adapter, delete_room_number=None, only_force_values=False, extra_get_params=""):

    logging.info("Call adapter %s for modify reservation %s", adapter, identifier)

    url_adapter = get_push_reservation_url_adapter(adapter)
    if url_adapter:


        identifier_path = "&identifier=%s" % identifier

        modification_forced = ""
        if only_force_values:
            modification_forced = "&only_force_values=True"

        only_room_modification_param = ""
        if session_manager.get(ONLY_ROOM_MODIFICATION):
            only_room_modification_param = "&only_room_modification=%s" % session_manager.get(ONLY_ROOM_MODIFICATION)

        if delete_room_number:
            only_room_modification_param = "&delete_room_number=%s" % delete_room_number


        modification_url = "%s%s%s%s%s" % (url_adapter, identifier_path, only_room_modification_param, modification_forced, extra_get_params)
        modification_url = modification_url.replace(" ","")
        logging.info("posting modification to: %s", modification_url)

        if not DEV:
            response = requests.post(modification_url, timeout=30)

            if response.status_code == 200:

                logging.info("response adapter modification status: %s", response.status_code)
                logging.info("response adapter modification content: %s", response.content)

            else:
                logging.error("response adapter modification status: %s", response.status_code)
                logging.error("response adapter modification content: %s", response.content)
                content_response = response.content
                if type(content_response) == bytes:
                    content_response = content_response.decode('utf-8')

                notify_exception("[Backend] URGENT MODIFICATION NOT DELIVERED TO ADAPTER", content_response, add_hotel_info=True)
                return False

    return True


def buildReservation(
        search,
        selectedPrice,
        personalDetails,
        creditCardDetails,
        bookingId,
        supplements,
        request,
        paymentOrderId=None,
        supplements_included=None,
        reservation_to_modify=None,
        pending_reservation=False
):
    '''
    Builds a reservation object based on the information provided during the search and booking process
    '''

    booking_shopping_cart = is_enabled_shopping_cart() and is_booking_cart_v2()
    shopping_cart_info_data = session_manager.get(SHOPPING_CART_SELECTED)

    current_timestamp, utc_current_timestamp = get_current_timestamps()
    modification_timestamp_utc = None
    modification_timestamp = None
    external_identifier = None
    keep_same_cupo = None #if same dates, same room, same rate and same board, we keep the cupo in the adpaters (only available for adpaters with this functionality: Prestige iE.)
    original_product_id = None

    original_rooms = {}

    original_extra_info = {}

    if reservation_to_modify:
        result = copy.deepcopy(reservation_to_modify)

        if result.extraInfo:
            original_extra_info = json.loads(result.extraInfo)
            external_identifier = original_extra_info.get("external_identifier", "")
            original_product_id = original_extra_info.get("original_product_id", "")

        update_modification_status = True
        if session_manager.get(BOOKING3_LINK_TPV) and original_extra_info.get("status_reservation", "") == "pending":
            logging.info(
                "Not updating status modification because it is a payment by link for a pending reservation! %s",
                result.identifier)
            update_modification_status = False
            session_manager.set(NOT_SENT_AS_MODIFICATION, True)

        # save external locator (needed normaly for modifications in adapter)
        if update_modification_status:
            #save external locator (needed normaly for modifications in adapter)
            modification_timestamp = current_timestamp
            modification_timestamp_utc = utc_current_timestamp

            result.modificationTimestamp = current_timestamp


        result.additionalServices = ""


        if  search and (result.roomType1 == selectedPrice[0][1]) and result.regimen == selectedPrice[0][2] and result.rate == selectedPrice[0][0]\
            and result.startDate == search['startDate'] and  result.endDate == search['endDate'] :
            keep_same_cupo = "True"

        try:
            for room in range(1, reservation_to_modify.numRooms + 1):
                original_rooms[room] = {
                    "id": eval("reservation_to_modify.roomType%s" % room),
                    "adults": eval("reservation_to_modify.adults%s" % room),
                    "kids": eval("reservation_to_modify.kids%s" % room),
                    "babies": eval("reservation_to_modify.babies%s" % room),
                }
        except Exception as e:
            message = auditUtils.makeTraceback()
            logging.error("Something has failed getting original rooms from the original reservation: %s" % message)

    else:
        #NEW RESERVATION
        result = Reservation()
        result.timestamp = current_timestamp
        result.identifier = bookingId

    language = session_manager.get('language', SPANISH)

    #ONLY_ROOM_MODIFICATION funcitonality, is only available for real modifications (when reservation_to_modify is populated)
    if session_manager.get(ONLY_ROOM_MODIFICATION) and reservation_to_modify:
        #if we are updating only a room of the reseration, we leave the same room number in the reservation,
        #and  we have to change adults1, kids1 and babies1 for given room
        room_number_modification = session_manager.get(ONLY_ROOM_MODIFICATION)
        logging.info("Modification of a single room in a multiple room reservation. Index room to update: %s", room_number_modification)

        if int(room_number_modification) == 1:
            result.adults1 = search['rooms'][0]['numAdults']
            result.kids1 = search['rooms'][0]['numKids']
            result.babies1 = search['rooms'][0]['numBabies']

            result.roomType1 = selectedPrice[0][1]
        if int(room_number_modification) == 2:
            result.adults2 = search['rooms'][0]['numAdults']
            result.kids2 = search['rooms'][0]['numKids']
            result.babies2 = search['rooms'][0]['numBabies']

            result.roomType2 = selectedPrice[0][1]

        if int(room_number_modification) == 3:
            result.adults3 = search['rooms'][0]['numAdults']
            result.kids3 = search['rooms'][0]['numKids']
            result.babies3 = search['rooms'][0]['numBabies']
            result.roomType3 = selectedPrice[0][1]

        room_number_modification = session_manager.get(ONLY_ROOM_MODIFICATION)

        # we have to: 1. get prices by room, 2. update the new one, and 3. sum all them again to the total
        price_of_room_modified = float(getSelectedPrice(selectedPrice))

        old_price_of_room_modified = 0
        extra_info = result.extraInfo
        if extra_info:
            extra_info = json.loads(extra_info)
            old_price_of_room_modified = float(extra_info.get("price_room_%s" % room_number_modification, 0))
            logging.info("old price of room %s found: %s", room_number_modification, old_price_of_room_modified)
        else:
            extra_info = {}

        if session_manager.get(BOOKING3_LINK_TPV):
            #TODO: maybe we can remove this
            logging.info("keeping original_extra_info for LINK TPV. In theory, we shouldn't be here")
            extra_info = original_extra_info

        if pending_reservation:
            extra_info["status_reservation"] = "pending"

        extra_info['price_room_%s' % room_number_modification] = price_of_room_modified

        price_of_rest_rest_of_rooms = float(result.price) - old_price_of_room_modified
        result.price = str(price_of_room_modified + price_of_rest_rest_of_rooms)

        #this line is only to show this prices in html build confirmation
        session_manager.set(TOTAL_PRICE_FORCED, result.price)

        logging.info("Final price of reservation: %s", result.price)

        apply_discount = True
        adapter = get_config_property_value(advance_configs_names.MANAGER_AS_PROXY)
        if adapter and "lincoln" in adapter.lower():
            apply_discount = False

        recalculate_prices = False
        if session_manager.get(FORCED_PRICE_FROM_MANAGER):
            recalculate_prices = True
            apply_discount = False

        #updated it in extra info also price per days
        params_per_prices = build_params_per_prices_table(result, price_forced=None, save_with_room_names=False)
        new_price_per_day_dict = build_dict_price_per_day_table_for_single_room(params_per_prices, room_number_modification, apply_discount=apply_discount, recalculate_prices=recalculate_prices)
        #get the first element of the orderedDict (we are sure that there is only one)
        for new_room_key, new_room_price_day in list(new_price_per_day_dict.items()):
            break

        price_per_day_dict = extra_info.get("prices_per_day", {})

        for room_key_prices, room_price_per_day in list(price_per_day_dict.items()):
            if "%s: " % room_number_modification in room_key_prices:
                #room found, change prices per day of this room
                #delete old one:
                price_per_day_dict.pop(room_key_prices, None)
                price_per_day_dict[new_room_key] = new_room_price_day


        #Leave the price suplements original
        #result.priceSupplements = str(getPriceSupplements())


        #we leave originals star date and end date, but we save new ones in extra info for specific room
        modification_room_info = {}

        if search:
            modification_room_info["start_date_room"] =  search['startDate']
            modification_room_info["end_date_room"] =  search['endDate']
        modification_room_info["rate_room"] = selectedPrice[0][0]
        selectedPrice[0][0] = result.rate #this line is becasue we want to show original info in html confirmation

        modification_room_info["regimen_room"] =  selectedPrice[0][2]
        selectedPrice[0][2] = result.regimen #this line is becasue we want to show original info in hmll confirmation

        extra_info['modification_room_%s' % room_number_modification] = modification_room_info

    else:
        #normal WAY
        if search:
            result.startDate = search['startDate']
            result.endDate = search['endDate']

        result.regimen = selectedPrice[0][2]
        result.rate = selectedPrice[0][0]


        result.price = getSelectedPrice(selectedPrice)
        result.priceSupplements = str(getPriceSupplements())

        if session_manager.get(TOTAL_PRICE_FORCED) and session_manager.get(DISCOUNT_APPLIED):
            adjust_totals_for_discount_applied(result)


        if booking_shopping_cart and not session_manager.get(MULTIPLE_ROOMS_WITHOUT_MERGING):
            result.numRooms = 1
            result.roomType1 = selectedPrice[0][1]

            if get_config_property_value(SAVE_SHOPPING_CART_OCCUPANCY_BY_ROOM):
                first_room = shopping_cart_info_data.get("rooms")[0]
                result.adults1, result.kids1, result.babies1 = [int(o) for o in first_room.get("occupancy").split("-")]
            else:
                result.adults1 = search['rooms'][0]['numAdults']
                result.kids1 = search['rooms'][0]['numKids']
                result.babies1 = search['rooms'][0]['numBabies']

            result.adults2, result.kids2, result.babies2 = 0, 0, 0
            result.adults3, result.kids3, result.babies3 = 0, 0, 0
        else:
            result.numRooms = len(selectedPrice)

            for i in range(1, 4):
                room = search['rooms'][i - 1] if i <= len(search['rooms']) else {}

                if i <= result.numRooms:
                    adults, kids, babies = room.get("numAdults", 0), room.get("numKids", 0), room.get("numBabies", 0)
                else:
                    adults, kids, babies = 0, 0, 0

                setattr(result, f'adults{i}', adults)
                setattr(result, f'kids{i}', kids)
                setattr(result, f'babies{i}', babies)

            for i, price in enumerate(selectedPrice, start=1):
                if i > 3 or i > result.numRooms:
                    break

                setattr(result, f'roomType{i}', price[1])

        #pay atteption that result.numRooms has to be definied here fro use these funtions
        params_per_prices = build_params_per_prices_table(result, save_with_room_names=False)

        apply_discount = True
        adapter = get_config_property_value(advance_configs_names.MANAGER_AS_PROXY)
        if adapter and "lincoln" in adapter.lower():
            apply_discount = False

        recalculate_prices = False
        if session_manager.get(FORCED_PRICE_FROM_MANAGER):
            recalculate_prices = True
            apply_discount = False


        forced_room_price = None
        if session_manager.get(PRICE_FORCED_CALLCENTER):
            forced_room_price = _build_callcenter_forced_prices_by_room_for_daily_prices(result.numRooms)


        price_per_day_dict = build_dict_price_per_day_table(
            params_per_prices,
            save_with_room_names=False,
            apply_discount=apply_discount,
            recalculate_prices=recalculate_prices,
            forced_room_price=forced_room_price
        )

        extra_info = {}

        for i in range(1, result.numRooms + 1):
            extra_info['price_room_%s' % i] = float(selectedPrice[i - 1][3])

    if session_manager.get(CURRENT_SEARCH) or session_manager.get(SEARCH_KEY_PREFIX) or session_manager.get(SHOPPING_CART_SELECTED):
        kidsAges = {}
        rooms_ages = get_rooms_ages_from_session()
        for index, ages_info in enumerate(rooms_ages):
            kidsAges['kidsAges%s' % (index + 1)] = ages_info

        extra_info['agesByRoom'] = kidsAges

    if session_manager.get(FORCED_ADDITIONAL_SERVICES_FROM_MANAGER):
        services_price_forced = session_manager.get(FORCED_ADDITIONAL_SERVICES_FROM_MANAGER)
        result.priceSupplements = services_price_forced

    #common staf for modifications and new reservations
    if session_manager.get(PROMOCODE_USED):
        result.promocode = search.get('promoCode', "").upper()
    elif search.get(REMOVED_PROMOCODE) and not session_manager.get(HIDE_PROCODE_IN_BOOKING_WIDGET):
        result.promocode = search.get(REMOVED_PROMOCODE).upper()
    else:
        result.promocode = ''

    if search.get('hidden_default_promocode'):
        extra_info['hidden_promocode_applied'] = search.get('hidden_default_promocode')

    result.comments = personalDetails.comments

    if result.comments and "SERMEPA" in result.comments:
        result.comments = result.comments.replace("SERMEPA", "")

    rescue_promocode_applied = session_manager.get(RESCUE_PROMOCODE_APPLIED)
    if rescue_promocode_applied:
        logging.info("Rescue promocode applied: %s", rescue_promocode_applied)
        if search.get('promoCode',""):
            result.promocode = "%s - %s" % (search['promoCode'], rescue_promocode_applied)

        else:
            result.promocode = rescue_promocode_applied

    # Added info about discount and total price to reservation when send to lincoln
    adapter = get_config_property_value(advance_configs_names.MANAGER_AS_PROXY)
    if adapter and "lincoln" in adapter.lower():
        price_without_promotions = 0
        price_with_promotions = 0
        for i in range(0, params_per_prices['numRooms']):
            price_with_promotions += float(params_per_prices['pricePerRoom'][i])
            price_without_promotions += float(params_per_prices['pricesPerDay'][i][0])
            discount = price_without_promotions - price_with_promotions

        result.comments += "\n\r"
        result.comments += "\n\r"
        result.comments += "Total Price: %s, Discount: %s, Promocode: %s" % (price_without_promotions, discount, result.promocode)

    if get_config_property_value(advance_configs_names.ADD_MARKETING_IN_COMMENTS):
        star_promotions_list = starPromotion.getFilteredStarPromotionsList(
            search, selectedPrice, SPANISH, "POPUP", False
        )

        for marketing_promo in star_promotions_list:
            if marketing_promo.get("pictures"):
                marketing_texts = marketing_promo.get("pictures")[0]
                if marketing_texts:
                    title_marketing = get_text_version(marketing_texts.get("name", ""))
                    desc_marketing = get_text_version(marketing_texts.get("description", ""))
                    result.comments += "\n\r%s\n\r%s\n\r" % (title_marketing, desc_marketing)


    if session_manager.get(COMMENTS_FORCED_CALLCENTER):
        logging.info("adding comments callcenter to extra info: %s", session_manager.get(COMMENTS_FORCED_CALLCENTER))
        extra_info['comments_callseeker'] = "Call-Seeker - Comentarios: %s" % session_manager.get(COMMENTS_FORCED_CALLCENTER)


    if session_manager.get(COMMENTS_PREBOOKING):
        logging.info("adding comments Prebooking to extra info: %s", session_manager.get(COMMENTS_PREBOOKING))
        extra_info['comments_prebooking'] = "Prereserva - Comentarios: %s" % session_manager.get(COMMENTS_PREBOOKING)

    if session_manager.get(PAYMENT_WAYS_BY_COBRADOR):
        extra_info['gateways_shown_in_b3_by_cobrador'] = session_manager.get(PAYMENT_WAYS_BY_COBRADOR)

    result.priceIncrease = None
    if package_utils.is_package(result.rate):
        package_key, ratekey_to_use = package_utils.get_package_and_rate_from_key(result.rate)
        result.rate = ratekey_to_use
        result.priceIncrease = package_key

        if get_config_property_value(SHOW_SUMMARY_PACKAGE_PRICE):
            package_info = get_package_full_info(package_key, "SPANISH")

            if package_info.get("summary_prices"):
                extra_info['package_additional_info'] = {
                    "stay_price": 0,
                    "package_price": 0,
                    "package_summary_price": {
                        "adults": 0,
                        "kids": 0,
                        "room": 0
                    }
                }

                total_price_separated = package_utils.get_package_total_price_separated(package_info)
                extra_info['package_additional_info']['package_summary_price']['adults'] = total_price_separated['adults']
                extra_info['package_additional_info']['package_summary_price']['kids'] = total_price_separated['kids']
                extra_info['package_additional_info']['package_summary_price']['room'] = total_price_separated['room']

                total_package_price = float("%.2f" % sum(total_price_separated.values()))
                stay_price_without_package = float(result.price) - total_package_price
                extra_info['package_additional_info']['package_price'] = total_package_price
                extra_info['package_additional_info']['stay_price'] = float("%.2f" % stay_price_without_package)

    #If we have promotions, add them to the reservation
    result.promotions = None
    if len(selectedPrice[0]) > 4:
        result.promotions = selectedPrice[0][5]

    original_offers_name = []
    for i, price in enumerate(selectedPrice):
        if len(price) > 6:
            original_offers_name.append(price[6])
    if original_offers_name:
        aux = "@@".join(original_offers_name)
        extra_info['original_offers_name'] = aux

    real_list_promotions = []
    real_list_promotions_name={}
    real_list_promotions_name_customer = {}
    selectedOption = session_manager.get(SELECTED_OPTION_KEY)
    for partialSelected in selectedOption.split(";"):
        promotion_info = session_manager.get(PRICE_OPTION_KEY_PREFIX_V2 + partialSelected)
        if promotion_info and promotion_info.get("promotion") and type(promotion_info['promotion']) == dict and promotion_info['promotion'].get("multiple_promotions"):
            for real_promotion in promotion_info["promotion"]["multiple_promotions"]:
                if not real_promotion.get("key") in real_list_promotions:
                    real_list_promotions.append(real_promotion.get('key'))
                    promotion = get_promotion(real_promotion.get("key"))
                    if promotion:
                        real_list_promotions_name[real_promotion.get("key")] = promotion.name
                        original_promotion_data = get_properties_for_entity(real_promotion.get("key"), language)
                        real_list_promotions_name_customer[real_promotion.get("key")] = original_promotion_data.get('promotionName', '')

    extra_info['real_promotions_keys'] = real_list_promotions
    extra_info['real_promotions_name'] = real_list_promotions_name
    extra_info['real_promotions_name_customer_language'] = real_list_promotions_name_customer

    if len(real_list_promotions) > 1:
        result.promotions = ";".join(real_list_promotions)

    #Special increments in total! Ie Latam taxes, packages taxes...
    total_increment_amount = 0
    total_sups_increment_amount = 0
    tax_rate_percent = 0

    room_number = session_manager.get("hvRoomNumber")
    hv_locator = session_manager.get("hvlocator")
    if room_number:
        hotelverse_reservation = {'room_number': room_number, 'hv_locator': hv_locator}
        logging.info("adding hotelverse reservation to extra info: %s", hotelverse_reservation)
        extra_info['hotelverse_reservation'] = hotelverse_reservation

    if session_manager.get(HOTELVERSE_RESERVATION_CANCELLED):
        hv_locator_old = session_manager.get(HOTELVERSE_RESERVATION_CANCELLED)
        extra_info[HOTELVERSE_RESERVATION_CANCELLED] = hv_locator_old

    if is_bank_transfer_request(request):
        extra_info['transfer_bank'] = 'transfer_bank'

        if request.values.get('transfer_payment_option'):
            extra_info['transfer_payment_option'] = request.values.get('transfer_payment_option')

    if get_web_configuration(WHATSAPP_COMMUNICATION):
            extra_info['whatsapp_communication'] = True if request.values.get('whatsapp_communication') else False

    custom_booking3_fields = get_config_property_value(advance_configs_names.BOOKING3_EXTRA_FIELDS)
    if custom_booking3_fields:
        final_extra_fields = session_manager.get(BOOKING3_CUSTOM_FIELDS)
        if not final_extra_fields:
            final_extra_fields = get_booking3_custom_fields_from_request()
        if final_extra_fields:
            extra_info["extra_fields"] = final_extra_fields

    if original_extra_info and original_extra_info.get("extra_fields"):
        original_extra_fields = original_extra_info["extra_fields"]
        if extra_info.get("extra_fields"):
            for new_field in extra_info["extra_fields"]:
                original_extra_fields[new_field] = extra_info["extra_fields"][new_field]
        extra_info["extra_fields"] = original_extra_fields


    _check_price_callcenter(extra_info, result)

    hotel_country_location = get_config_property_value(advance_configs_names.HOTEL_COUNTRY_LOCATION)
    hotel_country_location_without_taxes = get_config_property_value(advance_configs_names.HOTEL_COUNTRY_LOCATION_TAX_INCLUDED)
    tax_to_be_paid_at_hotel = get_config_property_value(PAY_TAX_AT_HOTEL)
    agency_info = session_manager.get(AGENCY_INFO)
    agency_config = get_web_configuration(AGENCY_WEB_CONFIGURATION)

    # TODO: All this should be at a method called 'is_tax_needed_to_be_paid_at_booking'
    if hotel_country_location and not hotel_country_location_without_taxes and not tax_to_be_paid_at_hotel:
        tax_rate = CountryConfigsBuilder(hotel_country_location).tax_rate
        if tax_rate:
            #tax_rate = (float(tax_rate.replace("%", "")) + 100) / 100
            #price_with_tax = float(getSelectedPrice(selectedPrice)) * tax_rate
            #result.price = str(price_with_tax)
            logging.info("Building reservation. Package selected and HOTEL_COUNTRY_LOCATION config found. Increasing totalPrice: %s with a  %s", result.price, tax_rate)

            tax_rate_percent = float(tax_rate.replace("%", ""))
            total_increment_amount += (float(result.price) * tax_rate_percent) / 100.0
            if get_special_tax_for_suplements() and not (agency_info and agency_info.get('type') and agency_info.get('type') == 'pvp'):
                all_price_supplements_with_tax = getAllPriceSupplements(apply_default_tax=tax_rate)
                for price_supplement_key in all_price_supplements_with_tax:
                    tax = all_price_supplements_with_tax[price_supplement_key]["tax"]
                    total_sups_increment_amount += float(tax)
            else:
                total_sups_increment_amount += (float(result.priceSupplements) * tax_rate_percent) / 100.0

            #tax_rate_factor = (float(tax_rate.replace("%", "")) + 100) / 100
            #supplement_with_tax = float(getPriceSupplements()) * tax_rate_factor
            #result.priceSupplements = str(supplement_with_tax)


    package_tax_percentage = get_config_property_value(advance_configs_names.PACKAGE_TAX_PERCENTAGE)
    if package_utils.is_package(getSelectedRateKey()) and package_tax_percentage:
        logging.info("Building reservation. Package selected and PACKAGE_TAX_PERCENTAGE config found. Increasing totalPrice: %s with a  %s", result.price, package_tax_percentage)
        package_tax_percentage_amount = float(package_tax_percentage.replace("%", ""))
        total_increment_amount += (float(result.price) * package_tax_percentage_amount) / 100.0
        logging.info("increment_amount PACKAGE_TAX_PERCENTAGE:  %s", total_increment_amount)

        total_sups_increment_amount += (float(result.priceSupplements) * package_tax_percentage_amount) / 100.0


    if total_increment_amount:
        result.price = str(float(result.price) + total_increment_amount)

    custom_taxes = session_manager.get(CUSTOM_TAXES_INCREMENT_LIST)
    if custom_taxes:
        price_all_with_tax = float(result.price)
        for tax in custom_taxes:
            included = not tax.get('tax_info', {}).get('not_included')
            tax_dict = {
                'name': tax.get('name'),
                'included': included,
                'value': tax.get('total_tax')
            }
            price_all_with_tax += float(tax.get('total_tax'))
        result.price = str(price_all_with_tax)

    if total_sups_increment_amount:
        result.priceSupplements = str(float(result.priceSupplements) + total_sups_increment_amount)

    result.prefix = personalDetails.prefix
    result.name = personalDetails.name

    my_last_name = personalDetails.lastName1
    if personalDetails.lastName2:
        my_last_name += " " + personalDetails.lastName2

    #Note that we remove \n to prevent errors: 'Property %s is not multi-line'
    result.lastName = my_last_name.replace("\n", "")

    target_address = personalDetails.address.replace(",", "@") if personalDetails.address else ""
    address_list = [target_address, personalDetails.postalCode, personalDetails.city, personalDetails.province]
    address_list = [x for x in address_list if x]
    result.address = ",".join(list(address_list))

    result.country = personalDetails.country

    result.geolocation = session_manager.get('country')

    result.email = personalDetails.email.lower()

    # In case of paymentOrderId, the pay is already done and there is not credit card data
    payment_method = request.values.get('direct_payment', '')
    logging.info("Payment method build reservation: %s" % payment_method)
    gatewayFailed = session_manager.get(PAYMENT_GATEWAY_FAILED)

    ds_MerchantIdentifier = session_manager.get(SERMEPA_CC_TOKEN_ID)
    ds_ExpiryDate = session_manager.get(SERMEPA_CC_EXPIREDATE)

    card_token_tpv = session_manager.get(CARD_TOKEN_TPV)

    result.creditCard = None
    if ds_MerchantIdentifier and ds_ExpiryDate:
        result.creditCard = "CREDIT CARD SAVED BY SERMEPA TOKEN"
    elif card_token_tpv:
        result.creditCard = "CREDIT CARD SAVED BY TOKEN"

    elif paymentOrderId and not gatewayFailed:
        paymentAmount = session_manager.get(AMOUNT_SENT_TO_GATEWAY)
        result.creditCard = "%s@@%s@@%s" % ("pasarela", paymentOrderId, paymentAmount)

    elif payment_method == 'transfer_bank':
        try:
            prefix_bank_transfer_booking = get_config_property_value(advance_configs_names.BANK_TRANSFER_PREFIX_IDENTIFIER)
            if prefix_bank_transfer_booking:
                bookingId_prefix = prefix_bank_transfer_booking[:2] + bookingId[2:]
                result.identifier = bookingId_prefix
            pending_reservation_when_transfer = get_config_property_value(advance_configs_names.PENDING_RESERVATION_WHEN_TRANSFER)
            if pending_reservation_when_transfer:
                extra_info["status_reservation"] = "pending"
        except Exception as e:
            message = auditUtils.makeTraceback()
            logging.exception(message)
            notify_exception('Bank transfer prefix - Reservation builder fail', "At application (%s) and namespace (%s) has occurred a error trying to build a prefix for identifier" % (namespace_utils.get_application_id(), namespace_utils.get_namespace()))

        result.creditCard = "BANK TRANSFER"

    elif request.values.get('direct_payment_no_reload', '') == "transfer_bank":
        logging.info("Payment method build reservation (no_reload): %s", request.values.get('direct_payment_no_reload', ''))
        result.creditCard = "BANK TRANSFER"

    elif request.values.get('direct_payment_no_reload', '') == "bizum_payment":
        logging.info("Payment method build reservation (no_reload): %s", request.values.get('direct_payment_no_reload', ''))
        result.creditCard = "BIZUM PAYMENT"

    elif request.values.get('direct_payment_no_reload', '') == "paypal_payment":
        logging.info("Payment method build reservation (no_reload): %s", request.values.get('direct_payment_no_reload', ''))
        result.creditCard = "PAYPAL PAYMENT"

    elif payment_method == 'late_booking':
        result.creditCard = 'CREDIT CARD CONDITIONAL'

    else:
        if isinstance(creditCardDetails, DatatransCreditCardDetails):
            if result.creditCard == creditCardDetails.alias_number:
                result.creditCard = creditCardDetails.alias_number[12:16]

        elif creditCardDetails:
           if creditCardDetails.number:
              result.creditCard = creditCardDetails.number[12:16]


    device = None
    #NEVER CHANGE THE SORUCE IN A MODIFICATION!!! (always keep original source)
    if not reservation_to_modify:

        #leave this for backguard compatiblity, but currently, we are going to save real device in extraInfo
        result.source = None
        device = "Web"
        if user_agent_is_mobile() or session_manager.get(BOOKING_IS_MOBILE):
            result.source = "Mobile"
            device = "Mobile"
        elif user_agent_is_tablet() or session_manager.get(BOOKING_IS_TABLET):
            result.source = "Tablet"
            device = "Tablet"


        result.telephone = personalDetails.telephone

        #We make this because the format used is 1900-MM-DD
        birthday_string = personalDetails.birthday
        if birthday_string:
            birthday_splitted = birthday_string.split("-")
            birthday_splitted[0] = '1900'
            birthday_string = '-'.join(birthday_splitted)
        result.birthday = birthday_string

        if session_manager.get(AGENT_ID):
            result.agent = session_manager.get(AGENT_ID)
            result.source = MANAGER_SOURCE_CALL_CENTER

        if session_manager.get(SOURCE):
            session_source = session_manager.get(SOURCE)
            if not session_source == 'hotelads' or (session_source == 'hotelads' and session_manager.get(GCLID_INSESS)):
                result.source = session_manager.get(SOURCE)
            if session_source == 'hotelads' and not session_manager.get(GCLID_INSESS):
                extra_info["fre_booking_link"] = session_source


        if get_config_property_value(APPLY_DISCOUNT_BONO_ANDALUZ):
            result.source = "alsamo"

    #EXTRA_INFO ZONE

    if session_manager.get(DICT_TO_UPDATE_IN_EXTRA_INFO):
        extra_info.update(session_manager.get(DICT_TO_UPDATE_IN_EXTRA_INFO))

    if paymentOrderId:
        extra_info["paymentOrderId"] = paymentOrderId

    if device:
        extra_info["device"] = device
    extra_info["utc_current_timestamp"] = utc_current_timestamp
    extra_info["currency"] = get_currency()
    if session_manager.get(GATEWAY_CURRENCY_CONVERSION):
        extra_info["currency_conversion_for_gateway"] = session_manager.get(GATEWAY_CURRENCY_CONVERSION)

    if session_manager.get(CURRENCY):
        visual_currency = session_manager.get(CURRENCY)
        currency = get_currency(only_code=True)
        if visual_currency != currency:
            extra_info["visual_currency"] = visual_currency
            extra_info["visual_currency_conversion_rate"] = CurrencyConversor(currency, visual_currency).currency_exchange

    if session_manager.get(ERROR_CC_FOUND):
        extra_info["reservation_after_cc_denied"] = True

    if personalDetails.billing_data:
        extra_info["billing_data"] = personalDetails.billing_data

    #Trivago conversion API
    try:
        if result.source and result.source.lower() == 'trivago':
            if session_manager.get('trv_reference'):
                extra_info['trv_reference'] = session_manager.get('trv_reference')
            elif request.cookies.get('trv_reference'):
                extra_info['trv_reference'] = request.cookies.get('trv_reference')
    except Exception as e:
        logging.error("Exception trying to set the trv_reference")


    if session_manager.get(TPV_TYPE_PAYMENT_SELECTED) and ('PLACETOPAY' in session_manager.get(TPV_TYPE_PAYMENT_SELECTED)) and paymentOrderId:
        extra_info["status_reservation"] = "pending"


    if session_manager.get(ORIGINAL_REFERER):
        extra_info["original_referer"] = session_manager.get(ORIGINAL_REFERER)
        logging.info("setting in extra info Original Referer: %s", extra_info["original_referer"])

    if session_manager.get(DISCOUNT_APPLIED):
        extra_info["early_discount_applied"] = session_manager.get(DISCOUNT_APPLIED)

    if session_manager.get(RETRY_MAS_OCUPACION_CAPACITY):
        extra_info["retry_mas_ocupacion_capacity"] = session_manager.get(RETRY_MAS_OCUPACION_CAPACITY)
        logging.info("setting in extra info retry_mas_ocupacion_capacity: %s", extra_info["retry_mas_ocupacion_capacity"])

    geo_location_info = get_google_geo_info_from_request()
    if geo_location_info:
        extra_info["geo_location_info"] = geo_location_info

    if modification_timestamp:
        extra_info["modification_timestamp"] = modification_timestamp
    if external_identifier:
        extra_info["external_identifier"] = external_identifier
    if original_product_id:
       extra_info["original_product_id"] = original_product_id
    if keep_same_cupo:
       extra_info["keep_same_cupo"] = keep_same_cupo

    if original_extra_info and original_extra_info.get("exclude_programmatically_payment_cobrador_ids"):
        extra_info["exclude_programmatically_payment_cobrador_ids"] = original_extra_info.get("exclude_programmatically_payment_cobrador_ids")

    if reservation_to_modify:
        if original_extra_info.get("modification_counter"):
            extra_info["modification_counter"] = int(original_extra_info.get("modification_counter")) + 1
        else:
            extra_info["modification_counter"] = 1

    not_include_accomodation_tax = get_config_property_value(advance_configs_names.NOT_INCLUDE_ACCOMODATION_TAX)
    accomodation_tax_property = get_config_property_value(ACCOMODATION_TAX_INCREMENT_BY_PAX_NIGHT)
    if accomodation_tax_property and not_include_accomodation_tax:
        increment_total, accomodation_tax_info = get_accomodation_total_increment(is_booking_confirmation=True)
        extra_info["accomodation_tax_total"] = increment_total

    if modification_timestamp_utc:
        extra_info["modification_timestamp_utc"] = modification_timestamp_utc

    if session_manager.get(INFO_DETAILS_PREBOOKING):
        extra_info["prebooking_reactivated"] = True

        callcenter_reservation_as_booking_engine = get_config_property_value(CALLCENTER_RESERVATION_AS_BOOKING_ENGINE)
        if callcenter_reservation_as_booking_engine:
            result.source = callcenter_reservation_as_booking_engine

    mix_external_mapping = session_manager.get(MIX_EXTERNAL_MAPPING)
    if mix_external_mapping:
        current_namespace = get_namespace()
        if mix_external_mapping.get(current_namespace):
            extra_info["pull_real_time_mapping"] = mix_external_mapping.get(current_namespace)
            logging.info(f'Setting in extra info External Mapping: {extra_info["pull_real_time_mapping"]} for namespace {current_namespace}')
        else:
            logging.warning(f'buildReservation. External mapping token not found for namespace {current_namespace}')
    elif session_manager.get(PULL_REALTIME_MAPPING):
        extra_info["pull_real_time_mapping"] = session_manager.get(PULL_REALTIME_MAPPING)
        logging.info("setting in extra info External Mapping: %s", extra_info["pull_real_time_mapping"])

    if session_manager.get(SELECTED_ADDITIONAL_SERVICES_CALENDARS):
        extra_info['additional_services'] = json.loads(session_manager.get(SELECTED_ADDITIONAL_SERVICES_CALENDARS))

    if session_manager.get(EXTERNAL_SERVICES):
        extra_info["external_additional_services"] = session_manager.get(EXTERNAL_SERVICES)

    if session_manager.get(SERMEPA_COF_TXNID):
        extra_info['sermepa_cof_txnid'] = session_manager.get(SERMEPA_COF_TXNID)

    if session_manager.get(RULE_APPLIED):
        extra_info["rule_applied"] = session_manager.get(RULE_APPLIED)

    if reservation_to_modify and not session_manager.get(BOOKING3_LINK_TPV):
        current_payed = session_manager.get(AMOUNT_SENT_TO_GATEWAY)
        logging.info("MODIFICATION current_payed %s ", current_payed)
        total_payed = original_extra_info.get('payed', 0)
        logging.info("MODIFICATION total_payed %s ", total_payed)
        payed_by_tpv_link = original_extra_info.get('payed_by_tpv_link', 0)

        if session_manager.get(MODIFICATION_SAVED_IN_PEP):
            logging.info("setting current_payed to 0 because of already MODIFICATION_SAVED_IN_PEP")
            current_payed = 0

        if total_payed and current_payed:
            total_payed = float(total_payed) + float(current_payed)
            extra_info["payed"] = total_payed
        elif current_payed:
            extra_info["payed"] = float(current_payed)
        elif total_payed:
            #keep the amount paid before
            extra_info["payed"] = float(total_payed)
        elif payed_by_tpv_link:
            extra_info["payed_by_tpv_link"] = payed_by_tpv_link

        logging.info("MODIFICATION total_final_payed %s ", total_payed)

        keys_to_check = [
            "programmatically_payment_cobrador_ids",
            "exclude_programmatically_payment_cobrador_ids",
            "paylands_internal_customer_id",
            "payland_credencials",
            "token_id",
            "last_merchant_order_used",
            "addon_credencials",
            "stripe_customer_id",
            "payment_gateway",
            "history_modification",
            "history",
        ]

        for key in keys_to_check:
            if key in original_extra_info:
                extra_info[key] = original_extra_info[key]

    elif session_manager.get(RESERVATION_MODIFICATION) and not session_manager.get(BOOKING3_LINK_TPV):
        extra_info = session_manager.get(RESERVATION_MODIFICATION).get('extraInfo')
        extra_info = json.loads(extra_info) if extra_info else {}
        total_payed = float(extra_info.get("payed", 0))

        extra_info["payed_preview_reservation"] = float(extra_info.get("payed", 0))

        total_payed_now = session_manager.get(AMOUNT_SENT_TO_GATEWAY)

        if total_payed_now and not session_manager.get(MODIFICATION_SAVED_IN_PEP):
            total_payed += float(total_payed_now)

        extra_info["payed"] = float("%.2f" % total_payed)

    elif not session_manager.get(BOOKING3_LINK_TPV):

        total_payed = session_manager.get(AMOUNT_SENT_TO_GATEWAY)

        if total_payed:
            extra_info["payed"] = total_payed


    if session_manager.get(PAYMENT_GATEWAY):
        extra_info["gateway_type"] = session_manager.get(PAYMENT_GATEWAY)

    if session_manager.get(PAYPAL_TOKEN) and session_manager.get(AMOUNT_SENT_TO_GATEWAY):
        extra_info['payment_method'] = "PayPal"

    if session_manager.get(BIZUM_FORCED) and session_manager.get(AMOUNT_SENT_TO_GATEWAY):
        extra_info['payment_method'] = "Bizum"

    if session_manager.get(USER_CALL) and session_manager.get(AGENT_ID):
        user = session_manager.get(USER_CALL)
        extra_info["user_from_callseeker"] = user

    original_user = session_manager.get(ORIGINAL_AGENT_ID)
    if original_user:
        extra_info["original_user_from_callseeker"] = original_user

    cc_type = session_manager.get(SERMEPA_TYPE_CC)
    if cc_type:
        extra_info["cc_type"] = cc_type

    cc_brand = session_manager.get(SERMEPA_BRAND_CC)
    if cc_brand:
        extra_info["cc_brand"] = cc_brand

    if original_extra_info.get("avirato_data"):
        extra_info["avirato_data"] = original_extra_info["avirato_data"]

    if original_extra_info and original_extra_info.get("wubook_id"):
        extra_info["wubook_id"] = original_extra_info["wubook_id"]

    if original_extra_info and not original_extra_info.get("payed") and session_manager.get(AMOUNT_SENT_TO_GATEWAY):
        list_payments_modifications = original_extra_info.get("payment_from_modification") or []
        list_payments_modifications.append({
            "payment_id": session_manager.get(PAYMENT_GATEWAY_ORDER),
            "timestamp": current_timestamp
        })
        extra_info["payment_from_modification"] = list_payments_modifications

    if original_extra_info and original_extra_info.get("payed_by_tpv_link"):
        extra_info['payed_by_tpv_link'] = original_extra_info.get("payed_by_tpv_link")

    if original_extra_info and original_extra_info.get('payed_by_cobrador'):
        extra_info['payed_by_cobrador'] = original_extra_info.get('payed_by_cobrador')

    if creditCardDetails and creditCardDetails.ccholder:
        extra_info["ownercreditcard"] = creditCardDetails.ccholder

    if not creditCardDetails and original_extra_info:
        if original_extra_info.get("cc_datas"):
            encryptedMessage = original_extra_info.get("cc_datas")
            extra_info['cc_datas'] = encryptedMessage

    extra_info["prices_per_day"] = price_per_day_dict
    logging.info("prices_per_day in extra info: %s", price_per_day_dict)

    if get_config_property_value(advance_configs_names.DAILY_PRICES_CURRENCY_CONVERSION):
        extra_info["prices_per_day_currency_converted"] = convert_currency_price_per_day_table(price_per_day_dict)

    if search_extra_info := session_manager.get(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH):
        extra_info["extra_from_search"] = search_extra_info

        if booking_shopping_cart:
            if not session_manager.get('reactivate_prebooking'):
                shopping_cart = session_manager.get(SHOPPING_CART_SELECTED)
                if extra_from_search := get_search_info_from_booking_cart(shopping_cart):
                    extra_info["extra_from_search"] = extra_from_search
                    logging.info(f'Search info from shopping cart: {extra_from_search}')

            elif forced_booking := session_manager.get(FORCED_BOOKING_IDENTIFIER):
                extra_info["extra_from_search"] = [f"PREBOOKING__{forced_booking}"]

    if session_manager.get("CONTACT_PUSHTECH"):
        extra_info["psh_ctid"] = session_manager.get("CONTACT_PUSHTECH")

    if session_manager.get("CAMPAIGN_PUSHTECH"):
        extra_info["psh_cpid"] = session_manager.get("CAMPAIGN_PUSHTECH")

    if session_manager.get(REAL_AGES_KIDS):
        logging.info("REAL_AGES_KIDS in sessions: %s", session_manager.get(REAL_AGES_KIDS))
        extra_info["real_ages_kids"] = session_manager.get(REAL_AGES_KIDS)

    if session_manager.get(EXTERNAL_USER_ID):
        extra_info["external_user_id"] = session_manager.get(EXTERNAL_USER_ID)

    real_birthday_string = personalDetails.birthday
    if real_birthday_string and len(real_birthday_string.split("-")) == 3:
        birthday_splitted = real_birthday_string.split("-")
        if not birthday_splitted[0]:
            birthday_splitted[0] = '1900'
        if not birthday_splitted[1]:
            birthday_splitted[1] = '01'
        if not birthday_splitted[2]:
            birthday_splitted[2] = '01'
        real_birthday_string = '-'.join(birthday_splitted)


    extra_info['birthday'] = real_birthday_string

    extra_info['numfactu'] = personalDetails.numfactu
    extra_info['numflight'] = personalDetails.numflight
    if request.values.get('hourflight'):
        extra_info['hourflight'] = request.values.get('hourflight')

    if request.values.get('extra-hotel-info'):
        extra_info['extra_hotel_info'] = request.values.get('extra-hotel-info')

    if session_manager.get(NO_ASK_CREDIT_CARD) and session_manager.get(LOCATION_MODIFICATION):
        extra_info['location_modification'] = session_manager.get(LOCATION_MODIFICATION)
        extra_info['booking_modification'] = True

    if card_token_tpv:
        extra_info['card_token'] = card_token_tpv

    if ds_MerchantIdentifier:
        extra_info['ds_MerchantIdentifier'] = ds_MerchantIdentifier
    elif original_extra_info and original_extra_info.get("ds_MerchantIdentifier"):
        #maye is a modification. Never lost already saved token ds_MerchantIdentifier
        extra_info['ds_MerchantIdentifier'] = original_extra_info.get("ds_MerchantIdentifier")


    if ds_ExpiryDate:
        extra_info['ds_ExpiryDate'] = ds_ExpiryDate

    gateway_extra_info = session_manager.get("GATEWAY_EXTRA_INFO")
    if gateway_extra_info and isinstance(gateway_extra_info, dict):
        extra_info.update(gateway_extra_info)

    if hasattr(personalDetails, 'personalId'):
        extra_info['personalId'] = personalDetails.personalId

    if get_config_property_value(advance_configs_names.EMAIL_NOTIFICATIONS) or \
            (get_config_property_value(advance_configs_names.BOOKING_PROCESS_VERSION)):


            allow_notifications = getattr(personalDetails, "allow_notifications", None)
            if  allow_notifications:
                extra_info['check-allow-notifications'] = allow_notifications

            accept_conditions_and_policies = getattr(personalDetails, "accept_conditions_and_policies", None)
            if accept_conditions_and_policies:
                extra_info['accept-conditions-and-policies'] = accept_conditions_and_policies

            share_info_group_chain = getattr(personalDetails, "share_info_group_chain", None)
            if share_info_group_chain:
                extra_info['share-info-group-chain'] = share_info_group_chain

    if creditCardDetails and (get_config_property_value(advance_configs_names.SAVE_CC_DATAS) or get_config_property_value(advance_configs_names.DATATRANS_NO_SHOW_API_CONFIG)):

        logging.info("Credit card details: %s", creditCardDetails.__dict__)

        message = ""
        if isinstance(creditCardDetails, DatatransCreditCardDetails):
            message += creditCardDetails.alias_number + "@@"
            message += creditCardDetails.company + "@@"
            message += creditCardDetails.expiryDate + "@@"
            message += creditCardDetails.alias_cvv
        else:
            message += creditCardDetails.number + "@@"
            message += creditCardDetails.company + "@@"
            message += creditCardDetails.expiryDate + "@@"

            show_cvv = show_cvv_card()
            if show_cvv:
                message += creditCardDetails.cvv + "@@"

            if hasattr(creditCardDetails, 'ccHolder') and get_config_property_value(advance_configs_names.CC_OWNER_NAME):
                if not show_cvv:
                    message += "@@"
                message += creditCardDetails.ccHolder



        encryptedMessage = ''
        #Some hotels prefer to have the credit card information encrypted
        encryptionPassword = get_config_property_value(advance_configs_names.CREDIT_CARD_ENCRYPTION)
        if creditCardDetails and encryptionPassword:
            encryptedMessage = encryptionUtils.encryptMessage(message, encryptionPassword)

            #We convert it to base64 so that we can easily move it around http
            encryptedMessage = base64.b64encode(encryptedMessage).decode('utf8') # Bytes to string

        elif not creditCardDetails and original_extra_info:
            if original_extra_info.get("cc_datas"):
                encryptedMessage = original_extra_info.get("cc_datas")

        extra_info['cc_datas'] = encryptedMessage
        extra_info['pci_integration'] = get_config_property_value(advance_configs_names.PCI_TOKEN)

    if session_manager.get(PROFILE_AGENCIES_FUERTE_ENABLED):
        huesped = [{
            "IdOrden": 0,
            "sIdCliente": "",
            "sNombre": personalDetails.name,
            "sApellido": personalDetails.lastName1,
            "sCIF_Passport": "",
            "sTipoCIF_Passport": "",
            "sCif_PassportFechaExp": "",
            "sIdNacionalidad": "",
            "sIdPais": personalDetails.country,
            "sEmail": personalDetails.agency_email,
            "sMovil": personalDetails.agency_telephone,
            "sEmailAgency": personalDetails.email,
            "sIdentifierAgency": personalDetails.agency_identifier,
            "nombreAgente": personalDetails.agency_agent_name,
            "sTelefonoAgency": personalDetails.telephone
        }]
        extra_info['form_agency_fuerte'] = json.dumps(huesped)
        try:
            response = requests.get("https://fuerte-adapter.appspot.com/get_new_identifier?hotel=%s&agency=1" % get_namespace())
            identifier = response.text
            if identifier:
                result.identifier = identifier
        except Exception as e:
            message = auditUtils.makeTraceback()
            logging.error(message)

    #In case we have used parity maker we indicate it in the reservation
    if session_manager.get(NEW_PRICE_FROM_RATECHECK) and session_manager.get(ORIGINAL_PRICE_FROM_SEARCH):
        try:
            ota_origin = get_ota_paritymaker_origin()

            extra_info['ota_origin'] =  ota_origin
            extra_info['price_web'] =  session_manager.get(ORIGINAL_PRICE_FROM_SEARCH)
            extra_info['price_ota'] =  session_manager.get(NEW_PRICE_FROM_RATECHECK)

        except Exception as e:
            logging.critical("Unexpected exception setting extraInfo: " + str(e))
            message = "Exception at %s (bookingUtils.py): %s" % (namespace_utils.get_application_id(), str(e))

            notify_exception("Exception setting extraInfo", message)

    supplement_keys = ""
    if supplements:
        supplementString = ''

        for supplement in supplements:

            clean_name = supplement.get('name', "")
            if not clean_name:
                clean_name = ""

            if agency_info and not agency_config.get("commission_exclude_supplements"):
                comision = float(agency_info.get("commision", 0))
                supplement_agency_amount = float(supplement['price']) - (supplement['price']/100) * comision
                supplement['price'] = float("%.2f" % supplement_agency_amount)

            if session_manager.get(DISCOUNT_APPLIED):
                discount = session_manager.get(DISCOUNT_APPLIED)
                sup_price = float(supplement['price'])
                sup_price_discounted = round((float(sup_price) - (float(sup_price) / float(100 / float(discount)))), 2)
                supplement['price'] = sup_price_discounted

            supplementString += clean_name + " - cantidad: " + str(supplement['amount']) + " - dias: " + str(supplement['days']) + " - precio: " + str(supplement['price']) + "; "
            supplement_keys += supplement['key'] + " - name: " + clean_name + " - cantidad: " + str(supplement['amount']) + " - dias: " + str(supplement['days']) + " - precio: " + str(supplement['price']) + ";"

        result.additionalServices = supplementString

    if session_manager.get(FORCED_ADDITIONAL_SERVICES_FROM_MANAGER):
        priceSupplement = session_manager.get(FORCED_ADDITIONAL_SERVICES_FROM_MANAGER)
        if not (float(priceSupplement) > 0):
            result.additionalServices = ''

    if supplements_included:
        for supplement in supplements_included:
            agency_info = session_manager.get(AGENCY_INFO) or {}
            if agency_info and not agency_config.get("commission_exclude_supplements"):
                comision = float(agency_info.get("commision", 0))
                supplement_agency_amount = float(supplement['price']) - (supplement['price'] / 100) * comision
                supplement['price'] = float("%.2f" % supplement_agency_amount)

            if session_manager.get(DISCOUNT_APPLIED) and not agency_config.get("commission_exclude_supplements"):
                discount = session_manager.get(DISCOUNT_APPLIED)
                sup_price = float(supplement['price'])
                sup_price_discounted = round((float(sup_price) - (float(sup_price) / float(100 / float(discount)))), 2)
                supplement['price'] = sup_price_discounted

            supplement_keys += supplement['key'] + " - name: " + str(supplement['name']) + " - cantidad: " + str(supplement['amount']) + " - dias: " + str(supplement['days']) + " - precio: " + str(supplement['price']) + ";"

    if supplement_keys:
        extra_info['additional_services_keys'] = supplement_keys

    if session_manager.get(SELECTED_ADVANCED_ADDITIONAL_SERVICES):
        advanced_additional_services = session_manager.get(SELECTED_ADVANCED_ADDITIONAL_SERVICES)
        transfer_service_config = get_web_configuration(BOOKING_TRANSFER_SERVICE)
        if transfer_service_config:
            advanced_additional_services = update_info_transfer_services(transfer_service_config, advanced_additional_services, request)
            session_manager.set(SELECTED_ADVANCED_ADDITIONAL_SERVICES, advanced_additional_services)

        add_uuid_on_advanced_additional_services(advanced_additional_services)

        services_dict = {
            'total_price': getPriceSupplements(),
            'services_list': build_booked_service_data(session_manager.get(SELECTED_ADVANCED_ADDITIONAL_SERVICES))
        }
        result.additionalServices2 = json.dumps(services_dict)

    if is_booking2_react_active():
        extra_info['advanced_additional_services_history'] = {}
        if reservation_to_modify:
            reservation_services_history = json.loads(reservation_to_modify.extraInfo).get('advanced_additional_services_history')
            if reservation_services_history:
                extra_info['advanced_additional_services_history'].update(reservation_services_history)

        extra_info['advanced_additional_services_history'][utc_current_timestamp] = json.loads(result.additionalServices2) if result.additionalServices2 else {}

    if session_manager.get(FORCED_ADDITIONAL_SERVICES_FROM_MANAGER):
        priceSupplement = session_manager.get(FORCED_ADDITIONAL_SERVICES_FROM_MANAGER)
        logging.info("Forced Additional Services from manager")
        if not (float(priceSupplement) > 0):
            logging.info("no priceSuplements")
            extra_info['additional_services_keys'] = ''

    if request.values.get('client_question'):
        extra_info['is_client'] = request.values.get('client_question')

    if request.values.get('meet_us_question'):
        extra_info['how_meet'] = request.values.get('meet_us_question')

    if request.values.get('how_meet_comments'):
        extra_info['how_meet_comments'] = request.values.get('how_meet_comments')

    if transfer_info := get_transfer_service_for_extra_info(supplements):
        logging.info('Transfer service found. Adding transfer info in reservation.extraInfo.transfer_service')
        extra_info[TRANSFER_SERVICE] = transfer_info

    if get_config_property_value(advance_configs_names.BILLING_DATA) and personalDetails.billing_name and personalDetails.billing_cif and personalDetails.billing_address:
        extra_info['billing_name'] = personalDetails.billing_name
        extra_info['billing_cif'] = personalDetails.billing_cif
        extra_info['billing_address'] = personalDetails.billing_address

    if session_manager.get(ANALYTICS_CAMPAIGN):
        extra_info.update(session_manager.get(ANALYTICS_CAMPAIGN))

    reservation_comments_json = get_config_property_value(advance_configs_names.RESERVATION_COMMENTS_JSON)


    if reservation_comments_json and reservation_comments_json == "version_donpancho":

        #pancho PMS needs at least one character after ']'
        client_comments = "."
        if result.comments:
            client_comments = result.comments


        politicas = 0
        publi = 0


        allow_notifications = getattr(personalDetails, "allow_notifications", None)
        accept_conditions_and_policies = getattr(personalDetails, "accept_conditions_and_policies", None)

        if allow_notifications or extra_info.get("check-allow-notifications", "") == "on":
            publi = 1
        if accept_conditions_and_policies or extra_info.get("accept-conditions-and-policies", "") == "on":
            politicas = 1


        special_comments = "[a:%s,b:%s]" % (politicas, publi)
        result.comments = special_comments + client_comments




    elif reservation_comments_json:
        json_comments = {}

        if result.comments:
            json_comments['comments'] = result.comments

        reservation_comments_json = reservation_comments_json.split(";")

        translation_comments_key={
            "personalId": "dni",
            "check-allow-notifications": "publi",
            "share-info-group-chain": "grupo",
            "accept-conditions-and-policies": "politicas"
        }
        mandatory_fields_always_present = ["check-allow-notifications", "share-info-group-chain", "accept-conditions-and-policies"]

        fields_in_address=["address", "city", "postalCode", "country"]

        for reservation_element in reservation_comments_json:
            reservation_value = getattr(result, reservation_element, None)
            reservation_value = reservation_value if reservation_value else extra_info.get(reservation_element)

            #last chance
            if not reservation_value:
                last_chance_value = getattr(personalDetails, reservation_element, None)
                if last_chance_value:
                    reservation_value = last_chance_value

            if reservation_value == "NA":
                reservation_value = ""

            if ((reservation_element in mandatory_fields_always_present) or reservation_value) and reservation_element not in fields_in_address:

                json_key = translation_comments_key.get(reservation_element, reservation_element)

                if reservation_element in mandatory_fields_always_present:

                    if not reservation_value or reservation_value == "false":
                        reservation_value = 0
                    else:
                        reservation_value = 1


                json_comments[json_key] = reservation_value


        accumulated_address = ""
        #only add address if we have postalCode!
        if getattr(result, "postalCode", None):
            for address_field in fields_in_address:
                address_value = getattr(result, address_field, None)
                if address_value:
                    accumulated_address += address_value + ";"

        if accumulated_address:
            json_comments["address"] = accumulated_address
        result.comments = json.dumps(json_comments)

    if session_manager.get(REFERER_BOOKING0):
        result.comments += "\n\r"
        result.comments += "RESERVA HECHA DESDE CORPORATIVA"
        result.comments += "\n\r"

    if get_config_property_value(advance_configs_names.SAVE_ADDRESS_BREAKDOWN):
        fields_in_address = ["address", "city", "postalCode", "country"]
        extra_info['address_breakdown'] = {}
        for field in fields_in_address:
            if hasattr(personalDetails, field) and getattr(personalDetails, field):
                extra_info['address_breakdown'][field] = getattr(personalDetails, field)

    #TODO: artur, please move this to lin 1618 (aprox) to have togueter all shoppingcart issues
    if tax_rate_percent:
        shopping_cart_apply_tax(tax_rate_percent)

    if booking_shopping_cart and not session_manager.get(BOOKING3_LINK_TPV):
        shopping_cart_extra = booking_cart.build_extra_shopping_cart_reservation()
        extra_info['shopping_cart'] = shopping_cart_extra
    elif original_extra_info.get("shopping_cart") and session_manager.get(BOOKING3_LINK_TPV):
        extra_info['shopping_cart'] = original_extra_info.get("shopping_cart")

    booking3_popup_autofill = get_config_property_value(advance_configs_names.BOOKING3_POPUP_AUTILFILL_INFORMATION)
    if booking3_popup_autofill:
        reservation_discount_recalculate(booking3_popup_autofill, result, request, extra_info)

    # Encrypt token PCIController
    pci_controller = PCIController()
    if pci_controller.pci_instance(creditCardDetails):
        pci_controller.encrypt_credit_card_info(creditCardDetails, result, extra_info)
    elif original_extra_info:
        if original_extra_info.get("datatransdata"):
            extra_info["datatransdata"] = original_extra_info.get("datatransdata")

    if original_extra_info and original_extra_info.get("payland_credencials"):
        for original_extra_info_key in original_extra_info:
            if original_extra_info_key.startswith("paylands_order_"):
                extra_info[original_extra_info_key] = original_extra_info[original_extra_info_key]

    extra_info_from_session = session_manager.get(EXTRA_INFO_RESERVATION)
    if type(extra_info_from_session) is dict:
        extra_info.update(extra_info_from_session)

    selected_option_uuid = session_manager.get(SELECTED_OPTION_KEY)
    if session_manager.get(SPECIAL_RATE_INFO + selected_option_uuid):
        extra_info[SPECIAL_RATE_INFO] = session_manager.get(SPECIAL_RATE_INFO + selected_option_uuid)

    if pending_reservation and paymentOrderId:
        extra_info[STATUS_RESERVATION] = PENDING
        extra_info["payed"] = 0
        extra_info["paymentOrderId"] = paymentOrderId

    if pending_reservation and is_onrequest_active():
        extra_info[STATUS_RESERVATION] = PENDING
        extra_info[ON_REQUEST] = True

    if original_extra_info.get(ON_REQUEST):
        result.cancelled = False
        extra_info[ON_REQUEST] = True

    #don't upste original condition, unless ithe is a change of rate
    change_of_rate_made = False
    if reservation_to_modify:
        change_of_rate_made = reservation_has_change_of_rates(result, original_extra_info)

        if original_extra_info.get('points_used_price_discount'):
            extra_info['original_reservation_points_used_price_discount'] = original_extra_info['points_used_price_discount']

        get_original_reservation_extra_info_to_persist(original_extra_info, extra_info)


    if (not reservation_to_modify) or change_of_rate_made:
        language = session_manager.get('language')
        managment_language = get_management_language()
        params = get_rate_conditions(language, selectedPrice, date_start=search['startDate'], date_end=search['endDate'])
        extra_info[RATE_CONDITIONS_ORIGIN] = params

        if managment_language != language:
            params = get_rate_conditions(managment_language, selectedPrice, date_start=search['startDate'], date_end=search['endDate'])

        extra_info[RATE_CONDITIONS_ORIGIN_FOR_HOTEL] = params

        logging.info("RateConditionsOrigin: %s", params)

    elif reservation_to_modify:
        rate_conditions_origin = original_extra_info.get(RATE_CONDITIONS_ORIGIN)
        extra_info[RATE_CONDITIONS_ORIGIN] = rate_conditions_origin
        extra_info[RATE_CONDITIONS_ORIGIN_FOR_HOTEL] = original_extra_info.get(RATE_CONDITIONS_ORIGIN_FOR_HOTEL)

        if rate_conditions_origin and rate_conditions_origin.get("cancellationPolicy"):
            session_manager.set(FORCE_CANCEL_POLICY_DAYS, rate_conditions_origin.get("cancellationPolicy"))




    if session_manager.get(VIRTUAL_SERVICES_SELECTED):
        virtual_services_text = ""
        for virtual_serv in session_manager.get(VIRTUAL_SERVICES_SELECTED):
            virtual_services_text +=  virtual_serv.get("text", "") + "||"
        extra_info['virtual_services_text'] = virtual_services_text

    bono_used = session_manager.get(BONOGIFT_USED)
    if bono_used:

        final_discounted_price = getTotalPrice(selectedPrice)

        original_price_before_discount = float(result.price) + float(result.priceSupplements)
        session_manager.set(ORIGINAL_PRICE_BEFORE_DISCOUNT, original_price_before_discount)

        extra_info['bono_gift_used'] = bono_used.bono_identifier
        extra_info['final_discounted_price'] = final_discounted_price
        extra_info['original_price_before_discount'] = original_price_before_discount


        bono_controller = GiftBonoPromocodeController()
        #and in this moment, we han change the price! we're going to make the discount only in rooms! not in supps!
        if bono_controller.bono_properties and bono_controller.bono_properties.get("save price in reservation"):

            total_discount = session_manager.get(TOTAL_BONO_GIFT_DISCOUNT)
            new_rooms_prices = float(result.price) - float(total_discount)

            logging.info("save price in reservation found in bono_properties. Changing ROOM prices from: %s to %s", result.price, new_rooms_prices)

            #what happen if is a negative amount? (posible if bono comes fron a payed bigger NR reservation)
            if new_rooms_prices < 0:
                # let's discount the supps! Note that we have to ADD because new_rooms_prices is negative!

                original_sups = result.priceSupplements

                if float(result.priceSupplements) + new_rooms_prices > 0:
                    result.priceSupplements = str(float(result.priceSupplements) + new_rooms_prices)

                else:
                    #even discounting supps, the full reservation is negative. It is a 0 euros reservation!
                    result.priceSupplements = str(0)


                logging.info("Negative amount found in reservation! Adjust with supps From %s to %s ", original_sups, result.priceSupplements)
                new_rooms_prices = 0

            result.price = str(new_rooms_prices)

    services_from_adapter = session_manager.get(SERVICES_FROM_ADAPTER)
    if services_from_adapter:
        if supplements:
            try:
                services_from_adapter = json.loads(services_from_adapter)
            except Exception as e:
                services_from_adapter = {}
                logging.error(e)

            for selected_service in supplements:
                if services_from_adapter.get(selected_service["key"], {}).get("extra_info"):
                    extra_info["external_services_extrainfo"] = {}
                    extra_info["external_services_extrainfo"][selected_service["key"]] = services_from_adapter.get(selected_service["key"], {}).get("extra_info")


    if session_manager.get(AGENCY_INFO):
        logging.info("It is an agency!")
        agency_info = copy.deepcopy(session_manager.get(AGENCY_INFO))
        agency_extra_info = build_agency_info_for_reservation(result, agency_info, tax_rate_percent)
        extra_info.update(agency_extra_info)

    if get_config_property_value(advance_configs_names.BOOKING3_CUSTOM_ADDITIONAL_CHECKBOX):
        if not extra_info:
            extra_info = {}

        extra_checkboxes = personalDetailsUtils.retreive_checked_checboxs_from_request(request)
        extra_info['send_extra_notification_by_email'] = extra_checkboxes.pop('send_extra_notification_by_email', [])
        extra_info['additional_checkboxes'] = extra_checkboxes

    if hasattr(personalDetails, 'extra_fields'):
        extra_info['booking3_extra_fields'] = personalDetails.extra_fields

    result.extraInfo = None

    old_reservation = session_manager.get(OLD_RESERVATION)
    if old_reservation:
        for room in range(1, old_reservation.get('numRooms') + 1):
            original_rooms[room] = {
                "id": old_reservation.get('roomType%s' % room),
                "adults": old_reservation.get('adults%s' % room),
                "kids": old_reservation.get('kids%s' % room),
                "babies": old_reservation.get('babies%s' % room),
            }

    try:
        if not original_rooms:
            for room in range(1, result.numRooms + 1):
                original_rooms[room] = {
                    "id": eval("result.roomType%s" % room),
                    "adults": eval("result.adults%s" % room),
                    "kids": eval("result.kids%s" % room),
                    "babies": eval("result.babies%s" % room),
                }

        extra_info['original_rooms'] = original_rooms

    except Exception as e:
        logging.info("Error building original_rooms: %s", e)
        extra_info['original_rooms'] = {}

    c_turno_session = session_manager.get(C_TURNO_VALUE)
    if c_turno_session:
        extra_info['c_turno'] = c_turno_session

    id_user_club_fuerte = session_manager.get(CLUB_FUERTE_USER)
    session_club_fuerte = session_manager.get(CLUB_FUERTE_SESSION)
    session_data_club_fuerte = session_manager.get(CLUB_FUERTE_DATA_SESSION)
    if session_data_club_fuerte:
        extra_info['c_user_club'] = id_user_club_fuerte
        extra_info['c_session_club'] = session_club_fuerte
        extra_info['c_data_session_club'] = session_data_club_fuerte

    if request.cookies.get("login_user_data_fuerte"):
        user_data = request.cookies.get("login_user_data")
        user_data = base64.b64decode(user_data.replace('unescape--', '').replace('igualigual', '='))
        user_data = json.loads(user_data)
        domain = get_config_property_value(advance_configs_names.CUSTOM_DOMAIN)
        user_data['extend'] = True
        try:
            user_info_post = requests.post('%s/users/' % domain, data=user_data, timeout=10)
            extra_info['c_data_session_club'] = json.dumps(json.loads(user_info_post.text).get('data'))
        except Exception as e:
            logging.info("!ERROR querying data session club")

    c_origen_session = session_manager.get(C_ORIGEN_VALUE)
    if c_origen_session:
        extra_info['c_origen'] = c_origen_session

    partner = session_manager.get(PARTNER)
    if partner:
        extra_info['partner'] = partner

        if get_config_property_value(advance_configs_names.HIJIFFY_AS_SOURCE) and partner == "HIJIFFY":
            extra_info['device'] = result.source
            result.source = "HIJIFFY"


    extra_data_package_forfait = session_manager.get(EXTRA_DATA_PACKAGE_FORFAIT)
    if extra_data_package_forfait:
        extra_info['package_forfait'] = extra_data_package_forfait

    if original_extra_info.get("external_package_data"):
        extra_info['old_external_package_data'] = original_extra_info["external_package_data"]
    external_package_data = session_manager.get("external_package_data")
    if external_package_data:
        extra_info['external_package_data'] = external_package_data

    shopping_cart_info_data = session_manager.get(SHOPPING_CART_SELECTED)
    if shopping_cart_info_data and not session_manager.get(BOOKING3_LINK_TPV):
        extra_info['shopping_cart_human_read'] = shopping_cart_info_data
        num_rooms = len(shopping_cart_info_data.get('rooms', []))
        if num_rooms:
            result.numRooms = num_rooms
    elif session_manager.get(BOOKING3_LINK_TPV) and original_extra_info.get("shopping_cart_human_read"):
        extra_info['shopping_cart_human_read'] = original_extra_info["shopping_cart_human_read"]


    if session_manager.get(GATEWAY_COMISSION):
        extra_info["gateway_comission"] = session_manager.get(GATEWAY_COMISSION)
        gateway_comission_percent = float("%.2f" % extra_info["gateway_comission"].get("percent") or 0)
        result.price = str(round((gateway_comission_percent * float(result.price))/100 + float(result.price),2))
        if result.priceIncrease:
            result.priceIncrease = str(round((gateway_comission_percent * float(result.priceIncrease)) / 100 + float(result.priceIncrease),2))

    try:
        time_zone = 'Europe/Madrid'
        specific_timezone = get_config_property_value(advance_configs_names.SPECIFIC_TIMEZONE)
        if specific_timezone:
            time_zone = specific_timezone

        timestamp_session = timezone('UTC').localize(session_manager.get_current_session().timestamp).astimezone(timezone(time_zone))
        extra_info['session_search_info'] = {
            "id": session_manager.get_session_id(),
            "timestamp": timestamp_session.strftime("%Y-%m-%d %H:%M:%S"),
        }

    except Exception as e:
        logging.warning("Couldn't save session info")

    if session_manager.get(ERROR_CARD_SIBS):
        extra_info['error_payment_card_SIBS'] = True

    result, extra_info = handle_clubs_bookings(result, extra_info, request)

    extra_room_options = session_manager.get(EXTRA_CHOICE_OPTION)
    if extra_room_options:
        extra_info['extra_room_options'] = extra_room_options

    try:
        price_info = build_reservation_price_info(selectedPrice)
        if price_info:
            extra_info['price_info'] = price_info
    except Exception as e:
        logging.error('Error building reservation price info')
        logging.error(e)
        message_traceback = auditUtils.makeTraceback()
        logging.error(message_traceback)
        notify_exception('Error building reservation price info', message_traceback, add_hotel_info=True)

    modification_price_data = session_manager.get(RESERVATION_MODIFICATION_PRICE_INFO)
    if modification_price_data:
        extra_info['modification_price_data'] = modification_price_data

    iamaresident = session_manager.get(I_AM_A_RESIDENT) and not session_manager.get(BOOKING3_LINK_TPV)
    if iamaresident:
        booking3_popup_autofill = get_config_property_value(advance_configs_names.BOOKING3_POPUP_AUTILFILL_INFORMATION)
        popup_autofill_config = get_config_autofill_booking3(booking3_popup_autofill)
        if popup_autofill_config.get('local_resident') and popup_autofill_config.get("discount"):
            extra_info["iamresident"] = {
                "discount": popup_autofill_config["discount"]
            }

    save_analytics_campaigns_in_extra_info(extra_info)

    additional_reservation_extra_info = render_hooks(
        'additional_reservation_extra_info',
        personal_details=personalDetails, request=request,
        _return_type=dict
    )
    if additional_reservation_extra_info:
        extra_info.update(additional_reservation_extra_info)

    if extra_info:
        logging.info("saving extraInfo: %s", extra_info)
        result.extraInfo = json.dumps(extra_info)

    noRefundableRate = rateIsNonRefundable(getSelectedRateKey())
    check_transfer_service(result, personalDetails, noRefundableRate)

    if reservation_to_modify:
        logging.info('Checking differences between original reservation and the new one')
        try:
            build_modification_extra_info(reservation_to_modify, result)
        except Exception as e:
            logging.error('Error building reservation modification info')
            logging.error(e)
            message_traceback = auditUtils.makeTraceback()
            logging.error(message_traceback)
            notify_exception('Error building reservation modification info', message_traceback, add_hotel_info=True)

        logging.info('Checking if we have external transfer services and what happen with them in the modification')
        check_external_transfer_provider_service(reservation_to_modify, result)

    session_manager.set(RESERVATION_DICT, result.to_dict())

    if get_config_property_value(VALIDATE_PROMOCODE_BY_EMAIL) and session_manager.get(VALIDATE_PROMOCODE_EMAIL):
        add_promocode_email_database(result.to_dict())

    # fmatheis, TODO
    # user_consent_from_request(request, request.referrer,
    #                           name=' '.join([result.name, result.lastName]),
    #                           phone=result.telephone,
    #                           email=result.email,
    #                           marketing_policies=request.values.get('check-allow-notifications'),
    #                           term_conditions_booking=True,
    #                           privacy_policies=True
    #                           )

    return result


def build_agency_info_for_reservation(result, agency_info, tax_rate_percent):
    # Logic moved from buildReservation; still requires refactoring.
    # Price and commission recalculations occur in multiple places during the booking process, risking inconsistent results.

    agency_config = get_web_configuration(AGENCY_WEB_CONFIGURATION)

    try:
        total_reservation_price = float(result.price) + float(result.priceSupplements)
        rooms_price = float(result.price)
        supplements_prices = getPriceSupplements(apply_default_tax=tax_rate_percent)
        room_taxes_increment = 0
        supplement_taxex_increment = 0

        if tax_is_included_in_price() and not tax_is_included_in_base_price():
            if agency_info.get('type') and agency_info.get('type') == 'pvp':
                # Need to fix prices if taxes
                net_price_before_taxes = remove_tax_from_price(total_reservation_price)
                commission_percentage = float(agency_info.get('commision'))
                commission_multiplier = commission_percentage / 100.00
                supplements_net_price_before_taxes = remove_tax_from_price(result.priceSupplements)
                rooms_net_price_before_taxes = net_price_before_taxes - supplements_net_price_before_taxes
                rooms_generated_commission = rooms_net_price_before_taxes * commission_multiplier
                if agency_config.get("commission_exclude_supplements"):
                    services_generated_commission = 0
                else:
                    services_generated_commission = supplements_net_price_before_taxes * commission_multiplier
                if get_special_tax_for_suplements():
                    rooms_net_price = rooms_net_price_before_taxes - rooms_generated_commission
                    price_with_taxes = add_tax_to_price(rooms_net_price)
                    total_reservation_price = price_with_taxes + supplements_prices + rooms_generated_commission + services_generated_commission
                else:
                    if agency_config.get("commission_exclude_supplements"):
                        generated_commission = rooms_generated_commission
                    else:
                        generated_commission = net_price_before_taxes * commission_multiplier
                    net_price = net_price_before_taxes - generated_commission
                    price_with_taxes = add_tax_to_price(net_price)
                    total_reservation_price = price_with_taxes + generated_commission

            else:
                rooms_price = remove_tax_from_price(rooms_price)
                supplements_prices_without_tax = getPriceSupplements()
                supplement_taxex_increment = supplements_prices - supplements_prices_without_tax
                supplements_prices = supplements_prices_without_tax
                room_taxes_increment = add_tax_to_price(rooms_price) - rooms_price

        extra_info_agency = {
            "agency_name": agency_info.get("name", ""),
            "agency_id": str(agency_info.get("id")),
            "agency_hash": agency_info.get("agency_hash"),
            "agency_pvp_price": str(total_reservation_price),
            "agency_commision": agency_info.get("commision"),
            "agency_commission_amount": agency_info.get("total_agency"),
            "commission_exclude_supplements": agency_config.get("commission_exclude_supplements"),
            "agency_telephone": agency_info.get("telephone"),
            "agency_billing_address": clean_unicode(agency_info.get("billing_address")),
            "agency_city": clean_unicode(agency_info.get("city")),
            "agency_postal_code": agency_info.get("billing_postal_code"),
            "agency_country": clean_unicode(agency_info.get("pais")),
            "agency_province": clean_unicode(agency_info.get("province")),
            "agency_cif": agency_info.get("cif"),
            "agency_type": agency_info.get("agency_type")
        }

        # ATTENTION! this change of reservation price has to be always AFTER this: session_manager.set(TOTAL_PRICE_FORCED, result.price)

        rooms_and_supplements_prices = rooms_price + supplements_prices
        agency_info_price = _calculate_agency_prices(agency_info, rooms_and_supplements_prices, supplements_price=supplements_prices)
        if room_taxes_increment or supplement_taxex_increment:
            # Need to add again the taxes after net and commission calculation
            agency_info_price['total_neto_agency_base_currency'] = float(agency_info_price['total_neto_agency_base_currency']) + float(room_taxes_increment) + float(supplement_taxex_increment)
            agency_info_price['total_neto_agency'] = float(agency_info_price['total_neto_agency']) + float(room_taxes_increment) + float(supplement_taxex_increment)

        extra_info_agency['total_neto_agency'] = agency_info_price.get('total_neto_agency')
        extra_info_agency['total_agency'] = agency_info_price.get('total_agency')

        logging.info(f"Agency extra info: {extra_info_agency}")

        result.price = str(float(agency_info_price.get("total_neto_agency_base_currency")) - float(supplements_prices) - float(supplement_taxex_increment))

        logging.info("Changing reservation price to netos: %s", result.price)

        if float(result.priceSupplements):
            supplements_price = result.priceSupplements
            if get_special_tax_for_suplements():
                commission = float(agency_info.get('commision'))
                supplements_net_price_before_taxes = remove_tax_from_price(result.priceSupplements)
                if agency_config.get("commission_exclude_supplements"):
                    services_generated_commission = 0
                else:
                    services_generated_commission = supplements_net_price_before_taxes * (commission / 100.00)
                price_supplements_with_tax = getPriceSupplements(apply_default_tax=tax_rate_percent)
                if agency_info.get('type') and agency_info['type'] == 'pvp':
                    price_supplements_with_tax += services_generated_commission
                result.priceSupplements = str(price_supplements_with_tax)

            else:
                agency_info_sups = _calculate_agency_prices(agency_info, supplements_price)
                result.priceSupplements = str(agency_info_sups.get("total_neto_agency_base_currency"))
                logging.info("Changing reservation supplements to netos: %s", result.priceSupplements)

        return extra_info_agency
    except Exception as e:
        message_traceback = auditUtils.makeTraceback()
        logging.error(f'Error processing agency info, while building reservation: {e}')
        logging.error(message_traceback)
        notify_exception(f'Error processing agency info, while building reservation', message_traceback, add_hotel_info=True)

        return {}


def get_booking3_custom_fields_from_request():
    custom_booking3_fields = get_config_property_value(advance_configs_names.BOOKING3_EXTRA_FIELDS)
    final_extra_fileds = {}
    if custom_booking3_fields:
        extra_fields = get_extra_booking3_fields(custom_booking3_fields, session_manager.get('language'))
        for extra_field in extra_fields:
            if extra_field.get("title", "") and extra_field.get("spanish_title", ""):
                extra_field_value = request.form.get(extra_field.get("title", ""), "")
                final_extra_fileds[extra_field.get("spanish_title", "")] = extra_field_value
    return final_extra_fileds


def build_reservation_price_info(selected_price):
    """Build a dictionary that contains all price related data (base price, taxes, supplements, etc)"""
    price_info = {
        'total_without_taxes': 0,
        'total_with_taxes': 0,
        'total_with_all_taxes': 0,
        'total_rooms': 0,
        'total_supplements': 0,
        'taxes': {}
    }

    logging.info('Building reservation price info')

    # Total
    price_breakdown = {}
    total_price = getTotalPrice(selected_price, exclude_accomodation_tax=True, price_breakdown=price_breakdown)
    total_rooms = price_breakdown.get('total_rooms', {}).get('value', 0)
    total_supplements = price_breakdown.get('total_supplements', {}).get('value', 0)
    logging.info('Total price: %s' % total_price)

    extra_increment_after_taxes = 0

    info_agency = session_manager.get(AGENCY_INFO)
    agency_config = get_web_configuration(AGENCY_WEB_CONFIGURATION)

    if info_agency:
        pvp_price = total_price
        commission = float(info_agency.get('commision'))
        if agency_config.get('commission_exclude_supplements'):
            generated_commission = (pvp_price - total_supplements) * (commission / 100.00)
        else:
            generated_commission = pvp_price * (commission / 100.00)

        if info_agency.get('type') and info_agency.get('type') == 'pvp':
            extra_increment_after_taxes = generated_commission

        if not agency_config.get("include_commission_for_tax"):
            total_price = pvp_price - generated_commission

    # Taxes
    # --- Country tax ---
    country_tax, total_without_taxes, total_with_taxes, total_with_all_taxes = _build_country_tax_info(total_price)
    if country_tax:
        price_info['taxes']['country'] = country_tax

    # --- Accommodation tax ---
    # TODO - Refactor - https://app.clickup.com/t/865bxa60n
    not_include_accommodation_tax = get_config_property_value(advance_configs_names.NOT_INCLUDE_ACCOMODATION_TAX)
    accommodation_tax_property = get_config_property_value(ACCOMODATION_TAX_INCREMENT_BY_PAX_NIGHT)
    if accommodation_tax_property:
        tax_value, accommodation_tax_info = get_accomodation_total_increment()

        if tax_value:
            accommodation_tax = {
                'included': not not_include_accommodation_tax,
                'value': tax_value
            }

            total_with_all_taxes += tax_value
            if accommodation_tax['included']:
                total_with_taxes += tax_value

            price_info['taxes']['accommodation'] = accommodation_tax

    # --- Custom taxes ---
    custom_taxes = session_manager.get(CUSTOM_TAXES_INCREMENT_LIST)
    if custom_taxes:
        custom_taxes_info = []
        for tax in custom_taxes:
            included = not tax.get('tax_info', {}).get('not_included')
            tax_dict = {
                'name': tax.get('name'),
                'included': included,
                'value': tax.get('total_tax')
            }
            total_with_all_taxes += float(tax.get('total_tax'))
            if included:
                total_with_taxes += float(tax.get('total_tax'))
            custom_taxes_info.append(tax_dict)

        price_info['taxes']['custom_taxes'] = custom_taxes_info

    # --- Package tax ---
    package_tax_percentage = get_package_tax_percentage()
    if package_utils.is_package(getSelectedRateKey()) and package_tax_percentage:
        package_tax_value = add_tax_to_price(total_without_taxes, package_tax_percentage) - total_without_taxes
        if package_tax_value:
            total_with_taxes += package_tax_value
            total_with_all_taxes += package_tax_value
            price_info['taxes']['package'] = {
                'included': True,  # Package tax is always included (at least now)
                'percentage': package_tax_percentage,
                'value': package_tax_value
            }

    if extra_increment_after_taxes:
        total_without_taxes = total_without_taxes + extra_increment_after_taxes
        total_with_taxes = total_with_taxes + extra_increment_after_taxes
        total_with_all_taxes = total_with_all_taxes + extra_increment_after_taxes

    price_info['total_without_taxes'] = total_without_taxes
    price_info['total_with_taxes'] = total_with_taxes
    price_info['total_with_all_taxes'] = total_with_all_taxes
    price_info['total_rooms'] = total_rooms
    price_info['total_supplements'] = total_supplements

    logging.info('Price info: ' + str(price_info))

    return price_info


def _build_country_tax_info(total_price):
    if total_price == 0:
        return 0, 0, 0, 0

    agency_info = session_manager.get(AGENCY_INFO)
    agency_config = get_web_configuration(AGENCY_WEB_CONFIGURATION)

    total_with_taxes = total_price
    total_without_taxes = total_price
    total_with_all_taxes = total_price
    default_tax_percentage = get_tax_percentage()
    special_tax_for_sups = get_special_tax_for_suplements()
    tax_is_already_included_in_price = tax_is_included_in_base_price()
    country_tax = {
        'value': 0,
        'included': tax_is_included_in_price()
    }

    if special_tax_for_sups:
        # In this case we want to separate room and supplement taxes
        total_supplements_without_taxes = getPriceSupplements()
        if agency_info and agency_info.get('type') and agency_info.get('type') == 'pvp' and not agency_config.get("commission_exclude_supplements"):
            agency_commission = float(agency_info.get("commision")) / 100
            total_supplements_without_taxes = float(total_supplements_without_taxes) * (1.00 - agency_commission)

        total_rooms_without_taxes = total_without_taxes - total_supplements_without_taxes
        all_price_supplements = getAllPriceSupplements(apply_default_tax=default_tax_percentage)
        country_tax_value = 0

        rooms_tax = {
            'percentage': default_tax_percentage,
            'value': add_tax_to_price(total_rooms_without_taxes, default_tax_percentage) - total_rooms_without_taxes
        }

        country_tax_value += rooms_tax['value']

        supplements_taxes = {}
        for supplement_key, supplement_info in all_price_supplements.items():
            supplements_taxes[supplement_key] = {
                'percentage': supplement_info.get('tax_percent', 0),
                'value': supplement_info.get('tax', 0)
            }
            country_tax_value += supplement_info.get('tax', 0)

        country_tax['value'] = country_tax_value

        if all(rooms_tax['percentage'] == s['percentage'] for s in supplements_taxes.values()):
            # When all percentages are the same we can say it is a normal case, so we can save a common percentage
            country_tax['percentage'] = default_tax_percentage
        else:
            # Otherwise we need to save tax percentages separately
            country_tax['rooms'] = rooms_tax
            country_tax['supplements'] = supplements_taxes
    else:
        country_tax['percentage'] = default_tax_percentage

    # Get total without tax
    if country_tax.get('value'):
        if tax_is_already_included_in_price:
            total_without_taxes = total_price - country_tax['value']
    elif country_tax.get('percentage'):
        if tax_is_already_included_in_price:
            total_without_taxes = remove_tax_from_price(total_price, country_tax['percentage'])

        country_tax['value'] = add_tax_to_price(total_without_taxes, country_tax['percentage']) - total_without_taxes
    elif default_tax_percentage > 0:
        logging.error('Country tax has neither value nor percentage')

    # Get total with tax
    if country_tax.get('value'):
        if country_tax['included']:
            total_with_taxes = total_without_taxes + country_tax['value']
        else:
            total_with_taxes = total_without_taxes

        total_with_all_taxes = total_without_taxes + country_tax['value']
    else:
        if default_tax_percentage == 0:
            logging.warning('Country tax is explicitly configured to 0%. Make sure it is correct')
        else:
            logging.error('Unexpected error. Country tax value is 0')
            logging.error(country_tax)
            notify_exception('Error calculating country tax', 'Unexpected error. Country tax value is 0', add_hotel_info=True)

    return country_tax, total_without_taxes, total_with_taxes, total_with_all_taxes


def shopping_cart_apply_tax(tax_percent):
    try:
        shopping_cart = session_manager.get(SHOPPING_CART_SELECTED)

        logging.info("Adding %s taxes to all prices in shopping cart")
        logging.info("Shopping cart from session: %s ", shopping_cart)

        if shopping_cart:
            if shopping_cart.get("total"):
                total_increment_amount = (float(shopping_cart.get("total")) * tax_percent) / 100.0
                shopping_cart["total"] = float(shopping_cart.get("total")) + total_increment_amount

            if shopping_cart.get("total_without_discount"):
                total_increment_amount = (float(shopping_cart.get("total_without_discount")) * tax_percent) / 100.0
                shopping_cart["total_without_discount"] = float(shopping_cart.get("total_without_discount")) + total_increment_amount

            for room in shopping_cart.get("rooms"):
                if room.get("price"):
                    total_increment_amount = (float(room.get("price")) * tax_percent) / 100.0
                    room["price"] =  float(room.get("price")) + total_increment_amount


            logging.info("Setting Shopping cart to session with taxs: %s ", shopping_cart)
            session_manager.set(SHOPPING_CART_SELECTED, shopping_cart)

    except Exception as e:
        logging.error("[Shooping Cart] Error shopping_cart_apply_tax: %s" % e)


def adjust_totals_for_discount_applied(result):
    try:
        price = float(result.price)
        price_sups = result.priceSupplements
        if not price_sups:
            price_sups = 0
        total = price + float(price_sups)
        if total != float(session_manager.get(TOTAL_PRICE_FORCED)):
            logging.info("TOTAL_PRICE_FORCED %s not equal to total in reservation: %s. adjusting... ", session_manager.get(TOTAL_PRICE_FORCED), total)
            logging.info("Price: %s Supplements: %s  adjusting... ", result.price, price_sups)
            #don't care about negative and positive
            diff = float(session_manager.get(TOTAL_PRICE_FORCED)) - total
            if abs(diff) > 0.009 and abs(diff) < 0.5:
                result.price = str(price + diff)
                logging.info("Adjusted!: Price: %s Supplements: %s ", result.price, price_sups)
    except Exception as e:
            message = auditUtils.makeTraceback()
            logging.error(message)


def reservation_has_change_of_rates(reservation, extra_info):
    """
    :unit_test: unit_tests.booking_process.utils.booking.test_bookingUtils.TestBookingUtils.test_reservation_has_change_of_rates
    """
    if extra_info:
        rate_condition_origin = extra_info.get(RATE_CONDITIONS_ORIGIN)
        if rate_condition_origin:
            original_rate_key = rate_condition_origin.get('key')
            if reservation.rate == original_rate_key:
                return False

    return True


def _build_callcenter_forced_prices_by_room_for_daily_prices(num_rooms):

    logging.info("_build_callcenter_forced_prices_by_room_for_daily_prices. Num rooms: %s", num_rooms)
    forced_room_price = {}
    try:
        prices_by_room = session_manager.get(PRICE_FORCED_CALLCENTER).strip(";").split(";")
        if prices_by_room:

            if num_rooms == len(prices_by_room):
                #vamos bien
                for i in range(1, num_rooms +1):
                    forced_room_price["%s" % i] = prices_by_room[i-1]

            else:
                logging.info("something extrange occures with PRICE_FORCED_CALLCENTER. Assuming an average per room ")

                forced_total_price = sum([float(x) for x in prices_by_room if x])
                forced_price_by_room = forced_total_price / num_rooms
                forced_room_price = {}
                for i in range(1, num_rooms +1):
                    forced_room_price["%s" % i] = forced_price_by_room

    except Exception as e:
        message = auditUtils.makeTraceback()
        logging.exception(message)
        logging.warning("Exception calculating daily prices with PRICE_FORCED_CALLCENTER")

    logging.info("_build_callcenter_forced_prices_by_room_for_daily_prices. forced_room_price: %s", forced_room_price)
    return forced_room_price


def build_confirmation_qr_code(booking_id, reservation_to_modify):
    internal_url = _get_internal_url()
    try:
        confirmation_url = build_encrypted_url(internal_url + '/send-confirmation/?id=%s&type=customer&html=true' % booking_id)
        if reservation_to_modify:
            confirmation_url += '&modification=true'

        qr_code_generator_url = "https://hotel-tools.ew.r.appspot.com/create_qrCode/" + base64.urlsafe_b64encode(
            confirmation_url.encode("utf-8")).decode("utf-8")

        response = requests.get(qr_code_generator_url)
        generated_qr_code = response.content
        generated_qr_code = base64.b64encode(generated_qr_code)

        return generated_qr_code.decode('utf-8')

    except Exception as e:
        message_traceback = auditUtils.makeTraceback()
        logging.error('Error building confirmation QR code')
        logging.error(message_traceback)
        notify_exception('Error building confirmation QR code', message_traceback, add_hotel_info=True)


def build_hotelverse_confirmation_banner(identifier, hotelverse_banner_section, language):
    section_pictures = get_pictures_from_section_name(hotelverse_banner_section, language)

    if section_pictures:
        main_picture = section_pictures[0]
        advance_properties = get_properties_for_entity(main_picture.get('key'), language)
        landing_url = advance_properties.get('landing_url')

        if not landing_url:
            warning_info = f"Building hotelverse banner but landing_url property is not defined. Section name: {hotelverse_banner_section}"
            logging.warning(warning_info)
            notify_exception(
                "[Design] Exception trying to build hotelverse banner, landing_url is not defined",
                warning_info,
                add_hotel_info=True
            )
            return {}

        namespace = get_namespace() or get_application_id()
        landing_url = f"{landing_url}?hotel_code={namespace}&reservation={identifier}&language_code={get_language_code(language).lower()}"

        hotelverse_banner = {
            'image_url': main_picture.get('servingUrl'),
            'landing_url': landing_url
        }

        return hotelverse_banner


def build_confirmation_banner(banner_section, reservation, language):
    section_pictures = get_pictures_from_section_name(banner_section, language)

    if section_pictures:
        main_picture = section_pictures[0]
        advance_properties = get_properties_for_entity(main_picture.get('key'), language)
        landing_url = advance_properties.get('landing_url')

        if not landing_url:
            warning_info = f"Building checkin banner but landing_url property is not defined. Section name: {banner_section}"
            logging.warning(warning_info)
            notify_exception(
                "[Design] Exception trying to build checkin banner, landing_url is not defined",
                warning_info,
                add_hotel_info=True
            )
            return {}
        namespace = get_namespace() or get_application_id()
        replace_dict =  {
            "@@@hotelCode@@@": namespace,
            "@@@identifier@@@": reservation.identifier,
            "@@@entranceDate@@@": parse_date(reservation.startDate, '%Y-%m-%d').strftime('%Y-%m-%d'),
            "@@@language@@@": get_language_code(language).lower(),
            "@@@name@@@": reservation.name or "",
            "@@@lastname@@@": reservation.lastName or ""
        }
        for element_to_replace, value_for_replace in list(replace_dict.items()):
            landing_url = landing_url.replace(element_to_replace, value_for_replace)

        banner = {
            'image_url': main_picture.get('servingUrl'),
            'landing_url': landing_url
        }

        return banner


def _check_price_callcenter(extra_info, result):
    try:
        if session_manager.get(PRICE_FORCED_CALLCENTER):
            price = session_manager.get(PRICE_FORCED_CALLCENTER)
            if session_manager.get(ORIGINAL_PRICE_FORCED_CALLCENTER):
                original_price = session_manager.get(ORIGINAL_PRICE_FORCED_CALLCENTER)
            else:
                original_price = result.price

            logging.info("PRICE_FORCED_CALLCENTER found!: original price: %s FINAL price forced %s", original_price,  price)
            if session_manager.get(TOTAL_PRICE_FORCED):
                price = "%s" % session_manager.get(TOTAL_PRICE_FORCED)
                session_manager.set(PRICE_FORCED_CALLCENTER, price)
                logging.info("PRICE_FORCED_CALLCENTER... but other (more important) TOTAL_PRICE_FORCED found! FINAL price forced %s", price)


            original_supplement_price = result.priceSupplements
            extra_info["original_price"] = original_price
            extra_info["original_supplement_price"] = original_supplement_price
            result.price = "%s" % sum([float(x) for x in price.split(";") if x])

            comentarios = session_manager.get(COMMENTS_FORCED_CALLCENTER)
            if comentarios:
                logging.info("adding comments callcenter in _check_price_callcenter to extra info: %s", session_manager.get(COMMENTS_FORCED_CALLCENTER))
                extra_info["comments_callcenter"] = comentarios
    except Exception as e:
        message = auditUtils.makeTraceback()
        logging.critical(message)


def get_rate_conditions(language, selectedPrice, date_start=None, date_end=None):

    from booking_process.utils.booking.conditions.rate_conditions import retreive_days_to_cancel_multiple_policies
    from booking_process.utils.booking.conditions.rate_conditions import get_text_condition_by_condition_key

    rate_key = selectedPrice[0][0]
    if package_utils.is_package(rate_key):
        package_key, rate_key = package_utils.get_package_and_rate_from_key(rate_key)

    rate = getRate(rate_key, language)
    logging.info("rate: %s", rate)

    policy = rate.get('cancellationPolicy')

    rate_condition_key = None

    if get_config_property_value(USE_RATE_CONDITIONS_IN_PROMOTION):
        if len(selectedPrice[0]) > 5:
            promotion = get_promotion(selectedPrice[0][5])
            logging.info("selectedPrice[0][5]: %s", selectedPrice[0][5])
            if promotion:
                logging.info("Promotion: %s", str(promotion.__dict__))
                logging.info("Promotion.cancellationPolicy: %s", promotion.cancellationPolicy)
                if promotion.cancellationPolicy:
                    logging.info("Using promotion cancellation policies")
                    all_rate_conditions = common_data_provider.get_all_rate_conditions(language)
                    rate_condition = all_rate_conditions.get(promotion.cancellationPolicy)
                    if rate_condition and rate_condition.get("cancellationPolicy"):
                        rate_condition_key = promotion.cancellationPolicy
                        policy = rate_condition.get("cancellationPolicy")

    if (date_start or date_end) and policy:
        if CANCELLATION_POLICY_MULTIPLE_POLICIES in rate.get('cancellationPolicy'):
            dates_format = '%Y-%m-%d'
            convert_to_datetime = lambda x: datetime.strptime(x, dates_format)
            start_datetime = convert_to_datetime(date_start)
            end_datetime = convert_to_datetime(date_end)
            days_to_cancel = retreive_days_to_cancel_multiple_policies(start_datetime, end_datetime, rate.get('rateCondition'))
            if days_to_cancel:
                policy = days_to_cancel

    if not rate_condition_key:
        rate_condition_key = rate.get('rateCondition')

    if rate_condition_key:
        rateConditionDescription = get_text_condition_by_condition_key(rate_condition_key, language)
    else:
        rateConditionDescription = rate.get('description')

    params = {'key': rate_key,
              'condition_key': rate_condition_key,
              'rateConditionDescription': rateConditionDescription,
              'cancellationPolicy': policy,
              'name': rate.get('name'),
              'localName': rate.get('localName'),
              'cancellationPeriods': rate.get('cancellationPeriods')}

    return params


def reservation_discount_recalculate(booking3_popup_autofill, result, request, extra_info):
    """Used for calculate discount to applied when it comes from booking3_popup_autofill config"""

    popup_autofill_config = get_config_autofill_booking3(booking3_popup_autofill)
    if popup_autofill_config and popup_autofill_config.get('local_resident') and request.values.get('local_resident') and popup_autofill_config.get('discount'):
        session_manager.set(DISCOUNT_PRICE_AUTOFILL_POPUP, popup_autofill_config.get('discount'))
        session_manager.set(LOCAL_RESIDENT_BOOKING, True)
        extra_info['local_resident'] = True
        return

    discount_for_autofill_session = session_manager.get(DISCOUNT_PRICE_AUTOFILL_POPUP)
    if discount_for_autofill_session:
        discount_for_autofill_session = float(discount_for_autofill_session)

        reservation_price = float(result.price)
        price_with_discount = reservation_price * (1 - (discount_for_autofill_session / 100))
        result.price = "%.2f" % price_with_discount

        supplement_price = float(result.priceSupplements)
        supplement_with_discount = supplement_price * (1 + discount_for_autofill_session / 100)
        result.priceSupplements = "%.2f" % supplement_with_discount

        total_price = price_with_discount + supplement_with_discount

        if session_manager.get(GATEWAY_COMISSION):
            gateway = session_manager.get(GATEWAY_COMISSION)
            total_price = gateway["new_price"]

        logging.info("reservation_discount_recalculate TOTAL_PRICE_FORCED: %s", total_price)
        session_manager.set(TOTAL_PRICE_FORCED, total_price)


def build_html_specifics_room_modification(reservation, language):

    res_html = ""

    if hasattr(reservation, 'extraInfo') and reservation.extraInfo:
        extra_info = json.loads(reservation.extraInfo)
        room_list = OrderedDict()
        for room_number_modification in range(0,3):

            room_modification_info = {}
            if extra_info.get('modification_room_%s' % room_number_modification):

                modification_room_extra_info = extra_info.get('modification_room_%s' % room_number_modification)

                room_key = ""
                if room_number_modification == 1:
                    room_key = reservation.roomType1
                elif room_number_modification == 2:
                    room_key = reservation.roomType2
                elif room_number_modification == 3:
                    room_key = reservation.roomType3

                if room_key:
                    webPageProperty = getRoom(room_key, language).get("roomName")
                    if webPageProperty and len(webPageProperty) > 0:
                        room_modification_info["room_name"] = webPageProperty[0].value


                if modification_room_extra_info.get('start_date_room'):
                    room_modification_info["start_date"] =  modification_room_extra_info.get('start_date_room')

                if modification_room_extra_info.get('end_date_room'):
                    room_modification_info["end_date"] =  modification_room_extra_info.get('end_date_room')

                if modification_room_extra_info.get('rate_room'):
                    room_modification_info["rate"] =  modification_room_extra_info.get('rate_room')

                    webPageProperty = getRate(room_modification_info["rate"], language).get("rateName", [])

                    if len(webPageProperty) > 0:
                        room_modification_info["rate_name"] = webPageProperty[0].value

                if modification_room_extra_info.get('regimen_room'):
                    room_modification_info["regimen"] =  modification_room_extra_info.get('regimen_room')

                    webPageProperty =  getRegimen(room_modification_info["regimen"], language).get("regimenName", [])
                    if webPageProperty and len(webPageProperty) > 0:
                        room_modification_info["regimen_name"] = webPageProperty[0].value

                room_list["%s" % room_number_modification] = room_modification_info


        if room_list:
            params = {"room_list": room_list}
            completeDictionary = dict(list(params.items()) + list(get_web_dictionary(language).items()))
            res_html = buildTemplate('booking_2/_specifics_room_modification.html', completeDictionary, allowMobile=False)




    return res_html


def send_mail_payed_reservation_not_delivered_to_manger(identifier, hotel_email, adapter_name, on_request):
    #TODO: translate this!
    comments = "El cliente ha recibido la reserva correctamente. Posiblemente deberá gestionar el cupo manualmente dentro del channel.<br><br>"
    if on_request:
        comments = "El cliente ha visualizado una pre-reserva ON REQUEST y deberá ponerse en contacto con él para confirmar o anular. Posiblemente deberá gestionar el cupo manualmente dentro del channel.<br><br>"

    email_title = "ALERTA URGENTE: Reserva %s PAGADA PERO NO ENTREGADA EN EL CHANNEL %s" % (identifier, adapter_name)
    content_html = "<b>La reserva %s ha sido pagada correctamente, pero no ha podido ser entregada en el channel %s.</b>  <br><br> %s Puede consultar los datos de esta reserva en su manager, sección 'Reservas'." % (identifier, adapter_name, comments)
    content_txt = content_html

    try:
       send_email(hotel_email, email_title, content_txt, content_html)

    except Exception as e:

        #sending the confirmation to paraty team by appengine
        send_email_gae(hotel_email, email_title, content_txt, content_html)


def _add_general_tags_to_email_subject(reservation, subject, language, to_manager):
    if not reservation or not subject:
        return ""

    try:
        email_type = 'confirmation'
        if session_manager.get(LOCATION_MODIFICATION):
            email_type = 'modification'

        if session_manager.get(CANCEL_RESERVATION_DICT):
            email_type = 'cancellation'

        translations = get_web_dictionary(language)
        current_email = 'hotel' if to_manager else 'customer'

        email_subject_integration = get_integration_configuration('EMAIL_SUBJECT_TAGS')
        email_subject_config = email_subject_integration.get('configurations', {})
        section_pictures = get_pictures_from_section_name(email_subject_config.get('general_config_section', ''),
                                                          language)
        for picture in section_pictures:
            picture.update(get_properties_for_entity(picture.get('key'), language))
        custom_tags = {
            picture.get('spanish_title'): picture
            for picture in section_pictures
            if (picture.get(f'only_{current_email}_email') or
                ('only_hotel_email' not in picture and 'only_customer_email' not in picture))
        }

        if reservation_is_onrequest(reservation):
            # It's an onrequest booking, never say that is a confirmation!!!!!
            subject = get_onrequest_email_title(reservation, translations)

        if not _tag_disabled_for_email_type(custom_tags, 'test', email_type) and is_test_reservation(reservation):
            subject = f"[TEST] {subject}"

        if not _tag_disabled_for_email_type(custom_tags, 'last_minute', email_type) and is_last_minutes(reservation):
            subject = f"[{custom_tags.get('last_minute', {}).get('description', 'ATTENTION LAST MINUTE')}] {subject}"

        if email_type == 'confirmation' or email_type == 'modification' or email_subject_config.get('tags_in_cancellation'):

            if reservation_has_billing_data(reservation) and custom_tags.get('billing_data'):
                subject = f"[{custom_tags.get('billing_data', {}).get('description', '')}] {subject}"

            if email_subject_integration.get('rateMap'):
                special_rate_property = email_subject_integration.get('rateMap', {}).get(reservation.rate, '')
                special_rate_tag = custom_tags.get(special_rate_property, {})

                if special_rate_property and special_rate_tag:
                    subject = f"[{special_rate_tag.get('description', '')}] {subject}"

            has_package = package_utils.is_package(reservation.rate) or package_utils.is_package(getSelectedRateKey()) or reservation.priceIncrease
            if has_package and custom_tags.get('package'):
                subject = f"[{custom_tags.get('package', {}).get('description', '')}] {subject}"

            if not _tag_disabled_for_email_type(custom_tags, 'extra_services', email_type) \
                    and reservation_has_supplement(reservation):
                services_tags = []
                selected_services, _, _ = get_additional_services_from_reservation(reservation)

                if selected_services:
                    extra_services_config = email_subject_integration.get('extraMap', {})
                    for service in selected_services:
                        service_tag_key = extra_services_config.get(service.get('key', ''), '')
                        if custom_service_tag := custom_tags.get(service_tag_key, {}).get('description', ''):
                            services_tags.append(f"[{custom_service_tag}]")

                extra_services_tag = ' '.join(services_tags) or f"[{custom_tags.get('extra_services', {}).get('description', 'Extra Services')}]"
                subject = f"{extra_services_tag} - {subject}"

        logging.info(
            f'Applied tags to {email_type} email for {current_email}, reservation: {reservation.identifier}. Subject: {subject}')

    except Exception as e:
        logging.error(f'Error occurred while adding tags to email subject: {e}')
        message = auditUtils.makeTraceback()
        notify_exception("Error occurred while adding tags to email subject", message, add_hotel_info=True)

    return subject


def _tag_disabled_for_email_type(custom_tags, tag_type, email_type):

    tag_config = custom_tags.get(tag_type)

    if tag_config:
        disabled_config = tag_config.get('disabled', '')
        if disabled_config:
            if disabled_config.lower() == 'true':
                return True
            else:
                disabled_types = {item.strip().lower() for item in disabled_config.split(';')}
                return email_type.lower() in disabled_types

    return False


def _get_visible_copy_by_title(title, language="SPANISH"):
    visible_copy = ''
    try:
        email_copy_config = get_config_property_value(EMAIL_COPY_BY_TITLE)
        if not email_copy_config:
            return visible_copy

        copy_config = get_pictures_from_section_name(email_copy_config, language)

        if copy_config:
            visible_copy += ','.join(
                pic.get('description', '').strip().replace(';', ',')
                for pic in copy_config
                if pic.get('title', '').lower() in title.lower() and pic.get('description', '').strip()
            )

    except Exception as e:
        message = "Something wrong with email copy configuration"
        traceback = makeTraceback()
        logging.error(f'{message} : {e}')
        logging.error(traceback)
        notify_exception(message, traceback, add_hotel_info=True)


    return visible_copy


def sendConfirmationEmailToManager(reservation, contentText, contentHtml, language="SPANISH", destination_email=None, payment_method=None, reservation_modification=False, copy_account=False):

    hotelManagerEmail = destination_email or get_hotel_manager_email(reservation)
    languageDict = get_web_dictionary(language)

    extra_info = json.loads(reservation.extraInfo)
    add_modification_prefix_in_subject = reservation_modification
    if session_manager.get(NOT_SENT_AS_MODIFICATION):
       add_modification_prefix_in_subject = False

    if session_manager.get(TPV_PAYMENT_PENDING):
        emailTitle = languageDict["T_booking_in_process"] + " " + str(reservation.identifier)
    else:
        if add_modification_prefix_in_subject:
            emailTitle = languageDict["T_booking_modification"] + " " + str(reservation.identifier)
        else:
            emailTitle = languageDict["T_confirmacion_reserva"] + " " + str(reservation.identifier)

    section_translations = get_section_from_section_spanish_name("_traducciones", language)
    if section_translations:
        section_advance_properties = get_properties_for_entity(section_translations.get('key', False), language)
        if section_advance_properties and section_advance_properties.get('T_confirmacion_reserva'):
            emailTitle = html.unescape(section_advance_properties.get('T_confirmacion_reserva')) + " " + str(reservation.identifier)

    emailTitle = _add_general_tags_to_email_subject(reservation, emailTitle, language, True) or emailTitle

    if payment_method:
        emailTitle = emailTitle if isinstance(emailTitle, str) else emailTitle.decode(encoding='UTF-8')
        emailTitle = payment_method + emailTitle

        if session_manager.get(LOCAL_RESIDENT_BOOKING):
            emailTitle = '[LOCAL RESIDENT] ' + emailTitle

    try:
        if session_manager.get(AMOUNT_SENT_TO_GATEWAY) and float(session_manager.get(AMOUNT_SENT_TO_GATEWAY)):
            if get_config_property_value(advance_configs_names.METHOD_PAYMENT_IN_EMAIL_SUBJECT) and session_manager.get(PAYMENT_GATEWAY):
                prefix_payed = "[%s] " % session_manager.get(PAYMENT_GATEWAY)
                emailTitle = prefix_payed + emailTitle
            if not session_manager.get(SIBS_PAYMENT) or not session_manager.get(SIBS_PAYMENT) == SIBS_MULTIBANCO:
                prefix_payed = "[PAID: %s] " % session_manager.get(AMOUNT_SENT_TO_GATEWAY)
                emailTitle = prefix_payed + emailTitle
        else:
            extra_info = reservation.extraInfo
            if extra_info:
                extra_info = json.loads(extra_info)
                if extra_info.get('gateway_type') and get_config_property_value(advance_configs_names.METHOD_PAYMENT_IN_EMAIL_SUBJECT):
                    prefix_payed = "[%s] " % extra_info.get('gateway_type')
                    emailTitle = prefix_payed + emailTitle
                if extra_info.get('payment_gateway') == "SIBS" and not extra_info.get(SIBS_MULTIBANCO) and extra_info.get("payed"):
                    prefix_payed = "[PAID: %s] " % extra_info.get("payed")
                    emailTitle = prefix_payed + emailTitle
    except Exception as e:
        logging.info("Fail adding prefix [PAID] in subject of the mail confirmation!")
        error_message = auditUtils.makeTraceback()
        logging.info(error_message)

    if session_manager.get(NEW_PRICE_FROM_RATECHECK) and session_manager.get(ORIGINAL_PRICE_FROM_SEARCH):
        email_mark = get_email_mark_paritymaker()
        emailTitle = email_mark + emailTitle

    is_club_reservation = (session_manager.get(MEMBER_CLUB_NAME) or session_manager.get(USER_LOGIN_INFO)
                           or extra_info.get('user_club_info') or extra_info.get('clubMember'))

    if is_club_reservation and not is_agency_logged_with_disabled_club(extra_info):
        user_level = None
        user_info = session_manager.get(USER_LOGIN_INFO)

        try:
            if user_info and type(user_info) is not str:
                user_level = retreive_user_level(user_info.get('idmember'), user_info=user_info).get('category')
        except Exception as e:
            traceback = auditUtils.makeTraceback()
            message = "%s<br>" \
                      "User info:<br>" \
                      "%s" % (traceback, user_info)
            logging.error(traceback)
            logging.info("User info:" + str(user_info))
            notify_exception("Exception at booking4 while getting user category", message, add_hotel_info=True)

        email_title_original = emailTitle
        if user_level:
            emailTitle = "Club Member([%s]) - " % user_level + emailTitle
        else:
            emailTitle = "Club Member - " + emailTitle

        controller = get_config_property_value(advance_configs_names.ALTERNATIVE_CLUB_CONTROLLER)
        if controller and controller == "aphotels":
            emailTitle = "Club Member - " + email_title_original

    if get_config_property_value(advance_configs_names.GATEWAY_SECOND_EMAIL_CONFIRMATION) and session_manager.get(AMOUNT_SENT_TO_GATEWAY):
        send_confirmation_gateway_payment(language, hotelManagerEmail, '[Gateway Payment]' + emailTitle, contentHtml)

    mark_email_callcenter = get_config_property_value(advance_configs_names.CALLCENTER_EMAIL_SUBJECT)
    if session_manager.get(SOURCE) and session_manager.get(SOURCE) == MANAGER_SOURCE_CALL_CENTER and mark_email_callcenter:
        emailTitle = mark_email_callcenter + emailTitle

    mark_email_agency = get_config_property_value(advance_configs_names.AGENCY_EMAIL_SUBJECT)
    if session_manager.get(SOURCE) and session_manager.get(SOURCE) == MANAGER_SOURCE_AGENCY and mark_email_agency:
        emailTitle = mark_email_agency + emailTitle

    if session_manager.get(PAYMENT_GATEWAY_FAILED) and "PAID" not in emailTitle:
        logging.info("PAYMENT_GATEWAY_FAILED value: %s" % str(session_manager.get(PAYMENT_GATEWAY_FAILED)))
        emailTitle = "[Pending payment - gateway error]" + emailTitle

    blind_copy = "" if DEV else get_config_property_value(advance_configs_names.BLIND_COPY_BOOKING_EMAIL) or ""

    if hotelManagerEmail == get_account_manager():
        blind_copy = ""

    try:
        logging.info("hotelManagerEmail: %s", hotelManagerEmail)
        logging.info('Email subject: %s', emailTitle)

        always_from_paraty = False
        if get_config_property_value(advance_configs_names.EMAIL_SENDER_MANAGER_FROM_PARATY):
            always_from_paraty = True

        if copy_account:
            emailTitle = "[COPY] " + emailTitle

        visible_copy = _get_visible_copy_by_title(emailTitle, language)
        if visible_copy:
            logging.info(f'Confirmation visible copy by email title: {visible_copy}')

        send_email(hotelManagerEmail, emailTitle, contentText, contentHtml, visible_copy=visible_copy, blind_copy=blind_copy, always_from_paraty=always_from_paraty)

    except Exception as e:
        message = auditUtils.makeTraceback()
        logging.error("Exception sending email to manager")
        logging.error(message)
        notify_exception("Exception sending email to manager", message, add_hotel_info=True)

        defer(send_email_no_retries, hotelManagerEmail, emailTitle, contentText, contentHtml, _countdown=60)
        # sending the confirmation to paraty team by appengine
        send_email_gae(PARATY_TEAM_EMAIL, emailTitle, contentText, contentHtml)


def send_confirmation_gateway_payment(language, hotel_manager_email, email_title, email_content):
    general_dict_languages = get_web_dictionary(language)

    contentText = general_dict_languages['T_pagado_pasarela'] + '<br>' + email_content
    contentHtml = general_dict_languages['T_pagado_pasarela'] + '<br>' + email_content

    send_email(hotel_manager_email, email_title, contentText, contentHtml)


def _getCallCenterAgentEmail(agentId):

    result = "<EMAIL>"

    emails = {
              "mmorilla": ",<EMAIL>",
              "mcarmen": ",<EMAIL>",
              "acabrera": ",<EMAIL>",
              "cluque": ",<EMAIL>",
              "belen": ",<EMAIL>",
              "aarias": ",<EMAIL>",
              "agente1": ",<EMAIL>",
              "agente2": ",<EMAIL>",
              "agente3": ",<EMAIL>",
              "agente4": ",<EMAIL>"
              }

    result += emails.get(agentId, "")

    return result


def sendConfirmationEmails(reservation, contentText, contentHtml, language='SPANISH', destination_email=None, reservation_modification=False, forced_subject="", hide_identifier_in_confirmation=False):

    if get_config_property_value(advance_configs_names.DONT_SEND_EMAILS_CLIENT):
        logging.info("Not sending email to client because it is active DONT_SEND_EMAILS_CLIENT")
        return

    languageDict = get_web_dictionary(language)

    # logging.info("email Text content:" + contentText)

    replyToEmail = get_config_property_value(advance_configs_names.REPLY_TO_BOOKING)
    email_sender = get_config_property_value(advance_configs_names.EMAIL_SENDER)
    if replyToEmail:
        replyToEmail = "; ".join([
            f"{email_sender} <{email.strip()}>"
            for email in replyToEmail.strip().replace(";", " ").replace(",", " ").split()
            if email
        ])

    reservation_identifier = str(reservation.identifier)
    if hide_identifier_in_confirmation:
        reservation_identifier = ""

    add_modification_prefix_in_subject = reservation_modification
    if session_manager.get(NOT_SENT_AS_MODIFICATION):
        add_modification_prefix_in_subject = False

    emailTitle = ""
    if session_manager.get(TPV_PAYMENT_PENDING):
        emailTitle = languageDict["T_booking_in_process"] + " " + reservation_identifier
    else:
        personalized_email_section_title = get_config_property_value(advance_configs_names.PERSONALIZED_CONFIRMATION_EMAIL)
        personalized_modification_email = get_config_property_value(advance_configs_names.PERSONALIZED_MODIFICATION_EMAIL)
        if reservation_modification and personalized_modification_email:
            personalized_email_section_title = personalized_modification_email

        if personalized_email_section_title:
            confirmation_email_section = get_section_from_section_spanish_name(personalized_email_section_title, language)
            if confirmation_email_section and confirmation_email_section.get("subtitle"):
                emailTitle = html.unescape(confirmation_email_section['subtitle'])
                emailTitle = emailTitle.replace('@identifier@', reservation_identifier)
            if not emailTitle:
                if add_modification_prefix_in_subject:
                    emailTitle = languageDict["T_booking_modification"] + " " + reservation_identifier
                else:
                    emailTitle = languageDict["T_confirmacion_reserva"] + " " + reservation_identifier

        else:
            if add_modification_prefix_in_subject:
                emailTitle = languageDict["T_booking_modification"] + " " + reservation_identifier
            else:
                emailTitle = languageDict["T_confirmacion_reserva"] + " " + reservation_identifier

    emailTitle = _add_general_tags_to_email_subject(reservation, emailTitle, language, False) or emailTitle

    try:
        files = get_attached_files(
            get_config_property_value(advance_configs_names.RESERVATION_CONFIRMATION_PDF),
            get_config_property_value(advance_configs_names.RESERVATION_CONFIRMATION_APPLE_WALLET),
            reservation,
            language,
            reservation_modification=reservation_modification,
            contentHTML=contentHtml
        )
    except Exception as e:
        files = None
        error_traceback = auditUtils.makeTraceback()
        logging.error(f"Error getting attached files in sendConfirmationEmails: {e}. \nTraceback: {error_traceback}")

    if (agent_id := session_manager.get(AGENT_ID)) and (agent_email := _getCallCenterAgentEmail(agent_id)):
        try:
            # Send email to CallCenter Agent
            send_email(agent_email, emailTitle, contentText, contentHtml, replyTo=replyToEmail, files=files)
        except Exception as e:
            logging.exception("Exception sending booking email to agent. Will defer it and send too by appengine to paraty team")
            error_message = auditUtils.makeTraceback()
            logging.exception(error_message)

            notify_exception("Exception sending booking email to agent. Will defer it and send too by appengine to paraty team", "", add_hotel_info=True)
            defer(send_email_no_retries, agent_email, emailTitle, contentText, contentHtml, _countdown=60)

    client_email = reservation.email
    if destination_email:
        client_email = destination_email
        logging.info(f"Custom destination email currently set. Sending confirmation email to {destination_email}")

    try:
        send_email(client_email, emailTitle, contentText, contentHtml, replyTo=replyToEmail, files=files)

        if second_mail := get_config_property_value(advance_configs_names.SECOND_MAIL):
            second_mail_section = get_section_from_section_spanish_name(second_mail, language)
            if second_mail_section and second_mail_section.get('subtitle') and second_mail_section.get('content'):
                second_mail_with_name = second_mail_section.get('content').replace("@@NAME@@", reservation.name)
                send_email(client_email, second_mail_section.get('subtitle'), second_mail_with_name, second_mail_with_name, replyTo=replyToEmail)

    except Exception as e:
        logging.critical("Unexpected exception during email sending: " + str(e))
        error_message = auditUtils.makeTraceback()
        logging.exception(error_message)
        message = "Exception at %s - %s (booking4): %s" % (namespace_utils.get_application_id(), str(namespace_utils.get_namespace()), error_message)

        client_email = unicodedata.normalize("NFKD", client_email).encode('ascii', 'ignore')
        defer(send_email_no_retries, client_email, emailTitle, contentText, contentHtml, reply_to=replyToEmail, _countdown=60)
        # sending the confirmation to paraty team by appengine
        notify_exception('Cant be send email to client, something wrong with emails', message, add_hotel_info=True)
        send_email_gae(PARATY_TEAM_EMAIL, emailTitle, contentText, contentHtml)


def get_attached_files(pdf_config, apple_pay_config, reservation, language, reservation_modification=False, contentHTML=None):
    """
    This function check the configuration and attach required files
    for the email confirmation.
    """
    files = []

    if pdf_config:
        try:

            xml_configs_list = pdf_config.split(";")
            hotels_xml_configs = [x.__dict__ for x in directDataProvider.get('IntegrationConfiguration')]
            matched_xml_configs = [x for x in hotels_xml_configs if x.get('name') in xml_configs_list]

            # if DEV:
            #     namespace_utils.set_namespace(None)

            satisfied_xml_config = check_pdf_bonus_conditions(matched_xml_configs, reservation)
            xml_mapped = satisfied_xml_config.get('configurations', {})

            if not satisfied_xml_config or not xml_mapped.get('pdf_url'):
                logging.info("Didn't satisfied conditions for pdf attachment")
                raise Exception("Didn't satisfied conditions for pdf attachment")

            pdf_dict = {}
            reservation_dict = reservation.to_dict()
            for attr_element in list(reservation_dict.keys()):
                if attr_element in list(xml_mapped.keys()):
                    pdf_dict[xml_mapped[attr_element]] = reservation_dict[attr_element]

                    if 'regimen' in attr_element:
                        pdf_dict[xml_mapped[attr_element]] = html.unescape(
                            getSelectedRegimen(['', '', reservation_dict[attr_element]], reservation_dict.get('language')))

            if xml_mapped.get('hotel_name'):
                hotel_name = get_config_property_value(advance_configs_names.EMAIL_SENDER)
                pdf_dict['hotel_name'] = hotel_name.split("-")[0] if hotel_name else ''

            if xml_mapped.get('custom_content'):
                pdf_dict['custom_content'] = get_section_from_section_spanish_name(xml_mapped['custom_content'],
                                                                              reservation_dict.get('language'))
                pdf_dict['custom_content'] = html.unescape(pdf_dict['custom_content'].get('content')) if pdf_dict[
                    'custom_content'] else ''

            pdf_generated = generate_pdf_with_dict(xml_mapped['pdf_url'], pdf_dict)


            files.append((satisfied_xml_config['configurations']['pdf_title'] + ".pdf", base64.b64encode(pdf_generated),
                          "application/octet-stream"))
        except Exception as e:
            logging.warning("Unexpected error adding apple passbook: " + str(e))

    if apple_pay_config:
        logging.info("Trying to send booking confirmation with apple wallet")

        if apple_pay_config == "True":

            hotel_code = get_namespace()

            try:
                passbook_generator_url = _get_passbook_generator_url(reservation)
                response = requests.get(passbook_generator_url, timeout=12)

                if response.status_code != 200:
                    raise Exception("Error requiring apple pass")

                filename = "reservation_%s.pkpass" % str(reservation.identifier)

                files.append((filename, base64.b64encode(response.content),
                              "application/vnd.apple.pkpass"))
            except Exception as e:
                logging.warning("Unexpected error adding apple passbook: " + str(e))

    if get_config_property_value(advance_configs_names.QR_CODE_IN_BOOKING_CONFIRMATIONS):
        confirmation_qr_code = build_confirmation_qr_code(reservation.identifier, reservation_modification)
        if confirmation_qr_code:
            files.append(('confirmation_qr_code.png', confirmation_qr_code, 'image/png', 'cid:confirmation_qr_code'))

    if get_config_property_value(PASSBOOK_IN_BOOKING_CONFIRMATIONS):
        passbooks_list = _build_confirmation_passbooks_list(reservation, language)
        if passbooks_list:
            for passbook_dict in passbooks_list:
                files.append((passbook_dict.get('file_name'), passbook_dict.get('passbook'), 'application/octet-stream'))

    if contentHTML and get_config_property_value(advance_configs_names.EMAIL_RESERVATION_CONFIRMATION_PDF):
        pdf = generate_pdf_with_html(contentHTML, only_pdf=True)
        if pdf:
            pdf_base64 = base64.b64encode(pdf).decode('utf-8')
            files.append(('email_confirmation.pdf', pdf_base64, 'application/pdf'))

    return files


def check_pdf_bonus_conditions(available_xml_configs, reservation):
    '''Conditions to check with pdf bonus at booking confirmation'''
    default_datetime_format = '%Y-%m-%d'
    satisfied_bonus = []

    for xml_config_element in available_xml_configs:
        xml_configurations = xml_config_element['configurations'] = dict([x.split(" @@ ") for x in xml_config_element.get('configurations')])

        if xml_configurations.get('rates_filter'):
            rate_name = _get_spanish_rate_name(reservation)
            if not rate_name in xml_configurations['rates_filter']:
                logging.info("Rate filter didn't satisfied")
                continue

        if xml_configurations.get('start_filter') and xml_configurations.get('end_filter'):
            xml_start_date = datetime.strptime(xml_configurations['start_filter'], default_datetime_format)
            xml_end_date = datetime.strptime(xml_configurations['end_filter'], default_datetime_format)
            reservation_start_date = datetime.strptime(reservation.startDate, default_datetime_format)
            reservation_end_date = datetime.strptime(reservation.endDate, default_datetime_format)
            if xml_start_date > reservation_start_date or xml_end_date < reservation_end_date:
                logging.info("Dates filter didn't satisfied")
                continue


        if xml_configurations.get('nights_filter'):
            if int(reservation.nights) < int(xml_configurations['nights_filter']):
                logging.info("Nights filter didn't satisfied")
                continue

        if xml_configurations.get('board_filter'):
            selected_board_key = reservation.regimen
            spanish_board_name = _get_spanish_board_name(selected_board_key)
            normalized_board_name = normalizeBoardName(spanish_board_name)
            if not normalized_board_name in xml_configurations['board_filter']:
                logging.info("%s doesn't declared on %s" % (normalized_board_name, xml_configurations['board_filter']))
                continue

        if xml_configurations.get('language_filter'):
            selected_language = reservation.language
            if not selected_language in xml_configurations['language_filter']:
                logging.info("Language filter doesn't satisfied")
                continue

        satisfied_bonus.append(xml_config_element)

    return satisfied_bonus[0] if satisfied_bonus else {}


def _get_spanish_rate_name(reservation):
    rate_key = reservation.rate
    rate_local_name = get_rate_local_name(rate_key)
    return rate_local_name


# @cache_on_datastore(lambda x: "rate_localName_%s" % x[0], tags="Rate,Multirate")
def get_rate_local_name(rate_key):
    # TODO: This need to be removed and use booking_process.utils.data_management.rates_data.get_rate_local_name
    # Not refactored because we are in middle of migration
    selected_rate = get_all_rates_entity_json_map().get(rate_key)
    if selected_rate:
        return selected_rate.get('localName')

    return None
#
# @cache_on_datastore(lambda x: "rate_localName_%s" % x[0], tags="Rate,Multirate")
# def get_rate_local_name(rate_key):
#     selected_rate = directDataProvider.getEntity('Rate', rate_key)
#     if selected_rate:
#         return selected_rate.localName
#
#     return None


def _get_spanish_board_name(board_key):
    if board_key:
        all_boards = directDataProvider.getEntityMap('Regimen')
        selected_board = all_boards.get(board_key)
        if selected_board:
            return selected_board.name

    return None


def sendCancellationEmailToManager(reservation, contentText, contentHtml, language="SPANISH"):

    hotelManagerEmail = getHotelManagerEmail4Cancellations(reservation)

    # Let's use the user's language for the cancellation also
    logging.info("language cancelation email IS %s", reservation.language)

    management_language = get_config_property_value(MANAGEMENT_LANGUAGE)
    if management_language:
        language = management_language
        logging.info("forcing language cancelation email to %s", language)
    elif reservation.language:
        language = reservation.language
        logging.info("forcing language cancelation email to %s", language)

    languageDict = get_web_dictionary(language)

    cancellation_title = languageDict["T_cancelacion_reserva"]

    if get_config_property_value(advance_configs_names.CANCELLATION_ON_REQUEST):
        cancellation_title = languageDict.get("T_peticion_cancelacion")

    emailTitle = cancellation_title + " " + str(reservation.identifier)


    emailTitle = _add_general_tags_to_email_subject(reservation, emailTitle, language, to_manager=True) or emailTitle

    try:

        always_from_paraty = False
        if get_config_property_value(advance_configs_names.EMAIL_SENDER_MANAGER_FROM_PARATY):
            always_from_paraty = True

        visible_copy = _get_visible_copy_by_title(emailTitle, language)
        if visible_copy:
            logging.info(f'Cancellation visible copy by email title: {visible_copy}')

        send_email(hotelManagerEmail, emailTitle, contentText, contentHtml, visible_copy=visible_copy, always_from_paraty=always_from_paraty)

    except Exception as e:
        logging.critical("Unexpected exception during email sending to manager: " + str(e))
        message = "Exception at %s (booking4): %s" % (namespace_utils.get_application_id(), str(e))

        notify_exception("Alerta cancelling booking: Exception Sending Mail. Will defer it and send too by appengine to paraty team)", message)

        defer(send_email_no_retries, hotelManagerEmail, emailTitle, contentText, contentHtml, _countdown=60)
        #sending the confirmation to paraty team by appengine
        send_email_gae(PARATY_TEAM_EMAIL, emailTitle, contentText, contentHtml)


def sendCancellationWithPaymentsEmailToManager(reservation, contentText, contentHtml, language="SPANISH"):
    if get_config_property_value(advance_configs_names.STOP_SEND_EMAIL_CANCELATTION_WITH_PAYMENTS):
        return

    logging.info("sendCancellationWithPaymentsEmailToManager: %s", reservation.identifier)
    hotelManagerEmail = getHotelManagerEmail4Cancellations(reservation)

    languageDict = get_web_dictionary(language)

    cancellation_title = languageDict["T_cancelacion_reserva_pagada"]
    emailTitle = cancellation_title + " " + str(reservation.identifier)

    emailTitle = _add_general_tags_to_email_subject(reservation, emailTitle, language, to_manager=True) or emailTitle

    try:

        always_from_paraty = False
        if get_config_property_value(advance_configs_names.EMAIL_SENDER_MANAGER_FROM_PARATY):
            always_from_paraty = True

        visible_copy = _get_visible_copy_by_title(emailTitle, language)
        if visible_copy:
            logging.info(f'Cancellation with payments visible copy by email title: {visible_copy}')

        send_email(hotelManagerEmail, emailTitle, contentText, contentHtml, always_from_paraty=always_from_paraty)

    except Exception as e:
        logging.critical("Unexpected exception during email sending to manager: " + str(e))
        message = "Exception at %s (booking4): %s" % (namespace_utils.get_application_id(), str(e))

        notify_exception("Alerta cancelling booking: Exception Sending Mail. Will defer it and send too by appengine to paraty team)", message)

        defer(send_email_no_retries, hotelManagerEmail, emailTitle, contentText, contentHtml, _countdown=60)
        #sending the confirmation to paraty team by appengine
        send_email_gae(PARATY_TEAM_EMAIL, emailTitle, contentText, contentHtml)


def sendAvailabilityEmailToManager(reservation, contentText, contentHtml, language="SPANISH"):

    hotelManagerEmail = getHotelManagerEmail4Cancellations(reservation)

    # Let's use the user's language for the cancellation also
    logging.info("language availability email IS %s", reservation.language)
    if reservation.language:
        language = reservation.language
        logging.info("forcing language availability email to %s", language)

    languageDict = get_web_dictionary(language)

    availability_title = languageDict["T_confirmacion_solicitud_reserva"]

    emailTitle = availability_title % str(reservation.identifier)

    emailTitle = _add_general_tags_to_email_subject(reservation, emailTitle, language, to_manager=True) or emailTitle

    try:
        send_email(hotelManagerEmail, emailTitle, contentText, contentHtml)
    except Exception as e:
        logging.critical("Unexpected exception during email sending to manager: " + str(e))
        message = "Exception at %s (accept availability on request): %s" % (namespace_utils.get_application_id(), str(e))

        notify_exception("Alerta availability booking: Exception Sending Mail. Will defer it and send too by appengine to paraty team)", message)

        defer(send_email_no_retries, hotelManagerEmail, emailTitle, contentText, contentHtml, _countdown=60)
        #sending the confirmation to paraty team by appengine
        send_email_gae(PARATY_TEAM_EMAIL, emailTitle, contentText, contentHtml)


def sendAvailabilityEmails(reservation, contentText, contentHtml, language='SPANISH'):

    clientEmail = reservation.email

    #Let's use the user's language for the cancellation also
    logging.info("language cancelation email IS %s",reservation.language)
    if reservation.language:
        language = reservation.language
        logging.info("forcing language cancelation email to %s",language)

    languageDict = get_web_dictionary(language)

    availability_title = languageDict["T_confirmacion_solicitud_reserva"]

    emailTitle = availability_title % str(reservation.identifier)

    emailTitle = _add_general_tags_to_email_subject(reservation, emailTitle, language, to_manager=False) or emailTitle

    try:
        send_email(clientEmail, emailTitle, contentText, contentHtml)
    except Exception as e:
        logging.critical("Unexpected exception during email sending: " + str(e))
        message = "Exception at %s (accept availability on request): %s" % (namespace_utils.get_application_id(), str(e))
        notify_exception("Exception (accept availability on request). Will defer it and send too by appengine to paraty team)", message, add_hotel_info=True)

        defer(send_email_no_retries, clientEmail, emailTitle, contentText, contentHtml, _countdown=60)
        #sending the confirmation to paraty team by appengine
        send_email_gae(PARATY_TEAM_EMAIL, emailTitle, contentText, contentHtml)


def sendCancellationEmails(reservation, contentText, contentHtml, language='SPANISH', email=None):

    clientEmail = reservation.email
    if email:
        clientEmail = email

    #Let's use the user's language for the cancellation also
    logging.info("language cancelation email IS %s",reservation.language)
    if reservation.language:
        language = reservation.language
        logging.info("forcing language cancelation email to %s",language)

    languageDict = get_web_dictionary(language)

    cancellation_title = languageDict["T_cancelacion_reserva"]

    if get_config_property_value(advance_configs_names.CANCELLATION_ON_REQUEST):
        cancellation_title = languageDict.get("T_peticion_cancelacion")

    emailTitle = cancellation_title + " " + str(reservation.identifier)

    personalized_cancellation_email = get_config_property_value(advance_configs_names.PERSONALIZED_CANCELLATION_EMAIL)
    if personalized_cancellation_email:
        cancellation_email_section = get_section_from_section_spanish_name(personalized_cancellation_email, language)
        if cancellation_email_section and cancellation_email_section.get("subtitle"):
            emailTitle = html.unescape(cancellation_email_section['subtitle'])

    emailTitle = _add_general_tags_to_email_subject(reservation, emailTitle, language, to_manager=False) or emailTitle

    try:
        if reservation.agent:
            agent_email = _getCallCenterAgentEmail(reservation.agent)
            send_email(agent_email, emailTitle, contentText, contentHtml)
    except Exception as e:
        logging.exception("Exception sending cancellation email to agent")


    try:
        send_email(clientEmail, emailTitle, contentText, contentHtml)
    except Exception as e:
        logging.critical("Unexpected exception during email sending: " + str(e))
        message = "Exception at %s (booking4): %s" % (namespace_utils.get_application_id(), str(e))
        notify_exception("Exception (booking4). Will defer it and send too by appengine to paraty team)", message, add_hotel_info=True)

        defer(send_email_no_retries, clientEmail, emailTitle, contentText, contentHtml, _countdown=60)
        #sending the confirmation to paraty team by appengine
        send_email_gae(PARATY_TEAM_EMAIL, emailTitle, contentText, contentHtml)


def _get_internal_url():
    """
    :unit_test: unit_tests.booking_process.utils.booking.test_bookingUtils.TestBookingUtils.test_get_internal_url
    """
    actual_namespace = namespace_utils.get_namespace()
    actual_appid = namespace_utils.get_application_id()

    if actual_namespace and (not actual_namespace == actual_appid):
        url_builded = "https://%s-dot-%s.appspot.com" % (actual_namespace, actual_appid)

    else:
        url_builded = "https://%s.appspot.com" % actual_appid

    logging.info("Internal appspot url built: %s", url_builded)

    return url_builded


def sendCallClientEmail(reservation, telephone, frequency, country, language='SPANISH'):

    clientEmail = reservation.email
    nameClient = reservation.name+" "+reservation.lastName

    #Let's use the user's language for the cancellation also
    if reservation.language:
        language = reservation.language

    languageDict = get_web_dictionary(language)

    content = languageDict.get("T_mail_call_client", "").replace("@@NAME@@", nameClient).replace("@@EMAIL@@", clientEmail).replace("@@BOOKING@@", reservation.identifier)


    params = {'name': "Hotelero/a",
              'content': content,
              'logotype': set_url_to_https(getLogotype()),
              'emailSender': get_config_property_value(advance_configs_names.EMAIL_SENDER),
              'telephone': telephone,
              'frequency': frequency,
              'country': country,
              'hotel_domain': get_config_property_value("Dominio asociado"),
              'hotel_phone': get_config_property_value("Telefonos de contacto"),
              'hotel_name': get_config_property_value("Nombre del hotel")
              }


    getAdditionalWebConfiguration(params)

    completeDictionary = dict(list(params.items()) + list(languageDict.items()))

    email = buildTemplate('booking_2/call_client_email.html', completeDictionary, allowMobile=False)

    emailTitle = "Petición de Llamada"

    hotelManagerEmail = get_hotel_manager_email(reservation)

    try:

        send_email(hotelManagerEmail, emailTitle, "", email, blind_copy=hotelManagerEmail)
    except Exception as e:
        logging.critical("Unexpected exception during email sending: " + str(e))
        message = "Exception at %s (booking4): %s" % (namespace_utils.get_application_id(), str(e))

        notify_exception("Exception sending notification of invalid CC email Will defer it and send too by appengine to paraty team)" , message)

        #sending the confirmation to paraty team by appengine
        send_email_gae(PARATY_TEAM_EMAIL, emailTitle, "", email)


def sendInvalidCreditCardEmails(reservation, language='SPANISH', concept=None):

    clientEmail = reservation.email

    #Let's use the user's language for the cancellation also
    if reservation.language:
        language = reservation.language

    languageDict = get_web_dictionary(language)

    #TODO, Define the property when ready
    emailTitle = languageDict["T_tarjeta_reserva_no_valida_titulo"] + " " + str(reservation.identifier)

    if get_config_property_value(advance_configs_names.CC_NO_RECOMENDATION):
        invalidCardMessage = languageDict["T_tarjeta_reserva_no_valida_mensaje_no_recommendation"]
    else:
        invalidCardMessage = languageDict["T_tarjeta_reserva_no_valida_mensaje"]

    link_retry = ""
    if DEV or get_config_property_value(advance_configs_names.CC_NO_VALID_CUSTOMER_RETRY):
        path_retry = "/booking3_retry_cc?identifier=%s" % str(reservation.identifier) + "&language=" + language


        #we are alwys to use the inner URL, maybe it is a widget, or iframe, or something that is not in our servers
        host = _get_internal_url()

        custom_host = get_config_property_value(CUSTOM_DOMAIN)
        if custom_host and not DEV:
            host = custom_host
            path_retry += "&namespace=" + get_namespace()

        invalidCardMessage = languageDict["T_tarjeta_reserva_no_valida_mensaje_link_retry"]

        link_retry = languageDict["T_link_retry_cc"].replace("@@HREF@@", "%s%s" % (host, path_retry))

        logging.info("link_retry built: %s ", link_retry)


    #Algunos hoteles querran tener un mensaje diferente
    custom_message = get_section_content_by_target_name("email tarjeta no valida", language, concept)
    if custom_message:
        invalidCardMessage = custom_message.get('content')

    params = {'name': reservation.name,
            'content': invalidCardMessage,
            'logotype': set_url_to_https(getLogotype()),
            'emailSender': get_config_property_value(advance_configs_names.EMAIL_SENDER),
            "link_retry": link_retry
     }

    getAdditionalWebConfiguration(params)

    completeDictionary = dict(list(params.items()) + list(languageDict.items()))

    email = buildTemplate('booking_2/invalid_credit_card_email.html', completeDictionary, allowMobile=False)

    if is_test_reservation(reservation):
        emailTitle = "[TEST] " + emailTitle

    hotelManagerEmail = get_hotel_manager_email(reservation)
    
    avoid_blind_copy = get_config_property_value(advance_configs_names.AVOID_BLIND_COPY_IN_INVALID_CREDIT_CARD_EMAIL)
    if avoid_blind_copy:
        hotelManagerEmail = ""

    sender = get_config_property_value(advance_configs_names.EMAIL_SENDER_BOOKING)

    try:
        emailTo = clientEmail
        send_email(emailTo, emailTitle, "", email, blind_copy=hotelManagerEmail, sender=sender)
    except Exception as e:
        logging.critical("Unexpected exception during email sending: " + str(e))
        message = "Exception at %s (booking4): %s" % (get_application_id(), str(e))

        notify_exception("Exception sending notification of invalid CC email Will defer it and send too by appengine to paraty team)" , message)

        defer(send_email_no_retries, emailTo, emailTitle, "", email, blind_copy=hotelManagerEmail, _countdown=60)
        #sending the confirmation to paraty team by appengine
        send_email_gae(PARATY_TEAM_EMAIL, emailTitle, "", email)


def getCreditPersonalDetails(request, force_not_gateway = False):
    logging.info("Getting credit card details")
    result = None

    force_datatrans = session_manager.get(FORCE_DATATRANS_BOOKING3_TPV)

    if session_manager.get(NAME_METHOD_PAYMENT_IN_USE) == LATE_BOOKING:
        logging.info("Not generating CreditCardDetails because it's a late booking")
        return None

    if session_manager.get(BANK_TRANSFER_IS_ACTIVE):
        logging.info("Not generating CreditCardDetails because it's a transfer bank")
        return None

    if (session_manager.get(RESERVATION_MODIFICATION) and not request.values.get("cardNumber")) and not force_datatrans:
        logging.info("Not generating CreditCardDetails because it's a modification and has no cardNumber in request")
        return None

    if request.values.get("dont_ask_credit_card", False):
        logging.info("dont_ask_credit_card found!")
        return None

    if session_manager.get(BONOGIFT_USED) and session_manager.get(ORIGINAL_PRICE_BEFORE_DISCOUNT) - session_manager.get(TOTAL_BONO_GIFT_DISCOUNT) <= 0.0:
        return None

    agent_id = session_manager.get(AGENT_ID)
    hide_payment_call = get_config_property_value(advance_configs_names.HIDE_PAYMENT_CALLSEEKER)
    hide_payment_enabled = session_manager.get(HIDE_PAYMENT_ENABLED)
    on_request_active = is_onrequest_active()

    if agent_id and hide_payment_call and hide_payment_enabled or on_request_active and hide_payment_enabled:
        logging.info("Not generating CreditCardDetails because it's a hide payment call")
        return None

    if get_config_property_value(DISABLE_CREDIT_CARD) and request.values.get('hidden_credit_card') and not force_datatrans:
        logging.info("Disabled credit card found!")
        return None

    # I check that credit card type pyment it's PCI_TOKEN and we aren't using a gateway
    using_gateway = session_manager.get(AMOUNT_SENT_TO_GATEWAY) and not force_not_gateway

    if get_config_property_value(advance_configs_names.PCI_TOKEN) and request.values.get("date_card_validator_token", "").strip() and not using_gateway:
        logging.info("PCI_TOKEN found: %s.", request.values.get("date_card_validator_token", ""))

        pci_controller = PCIController()
        result = pci_controller.create_credit_card(request)

    elif is_test_booking():
        result = CreditCardDetails()
        result.company = "4B"
        result.number = "42"
        result.expiryDate = "12/30"
        result.cvv = "123"

    else:
        result = CreditCardDetails()
        result.company = request.values.get("company")
        result.number = request.values.get("cardNumber")
        result.expiryDate = request.values.get("expiryMonth", '') + "/" + request.values.get("expiryYear", '')
        result.cvv = request.values.get("cvv")

    logging.info("CreditCardDetails: %s", result.__dict__)
    return result


"""
This function take url used for take data of credit card.
"""
def create_intermediate_link_for_pci(reservation, language="SPANISH"):
    applicationId = namespace_utils.get_application_id()

    if namespace_utils.get_namespace():
        encodedUrl = 'https://%s-dot-%s.appspot.com/utils?' % (namespace_utils.get_namespace(), applicationId)
    else:
        encodedUrl = 'https://%s.appspot.com/utils?' % applicationId

    if DEV:
        encodedUrl = 'http://localhost:8090/utils?'

    dataidentifier = reservation.identifier + "@@" + str(uuid.uuid4())

    password_cc = get_config_property_value(advance_configs_names.CREDIT_CARD_ENCRYPTION)
    encrypted_data = encryptionUtils.encryptMessage(dataidentifier, password_cc)

    #We convert it to base64 so that we can easily move it around http
    encrypted_data = base64.b64encode(encrypted_data)

    params = urllib.parse.urlencode({'intermediatedatas': encrypted_data, 'action': 'decrypt'})
    encodedUrl = encodedUrl + params
    return '<a href="%s">' % encodedUrl + get_web_dictionary(language).get("T_datos_tarjeta","") + '</a>'


def encryptCreditCard(creditCardDetails, reservation, password, language = "SPANISH"):

    message = "Localizador: %s<br/><br/>" % reservation.identifier

    #Note that names sometimes have weird characters
    try:
        message += "Nombre: %s %s<br/><br/>" % (reservation.name, reservation.lastName)
    except Exception:
        message = auditUtils.makeTraceback()
        logging.exception(message)

    general_dictionary = get_web_dictionary(language)

    message += "<b>" + general_dictionary.get("T_datos_tarjeta","") + "</b><br/>"
    message += general_dictionary.get("T_numero_tarjeta","") + ": %s<br/>" % creditCardDetails.number
    message += general_dictionary.get("T_empresa_tarjeta","") + ": %s<br/>" % creditCardDetails.company
    message += general_dictionary.get("T_fecha_caducidad","") + ": %s<br/>" % creditCardDetails.expiryDate

    show_cvv = show_cvv_card()

    if show_cvv:
        message += "CVV: %s<br/>" % creditCardDetails.cvv

    encryptedMessage = encryptionUtils.encryptMessage(message, password)

    #We convert it to base64 so that we can easily move it around http
    encryptedMessage = base64.b64encode(encryptedMessage)

    applicationId = namespace_utils.get_application_id()

    if namespace_utils.get_namespace():
        encodedUrl = 'https://%s-dot-%s.appspot.com/utils?' % (namespace_utils.get_namespace(), applicationId)
    else:
        encodedUrl = 'https://%s.appspot.com/utils?' % applicationId

    params = urllib.parse.urlencode({'data': encryptedMessage, 'action': 'decrypt'})
    encodedUrl = encodedUrl + params

    return '<a href="%s">' % encodedUrl + general_dictionary.get("T_datos_tarjeta","") + '</a>'

def get_link_encrypted_tokenizator_gateway(token, identifier, language ="SPANISH", tokenizator="pasarela-tpv"):


    if DEV:
        namespace_utils.set_namespace("test-backend")

    encryption_password = get_config_property_value(advance_configs_names.CREDIT_CARD_ENCRYPTION)

    if encryption_password:

        if not get_integration_configuration_properties("TOKENIZATOR"):
            return "Puede acceder a nuestra extranet para realizar cobros"

        applicationId = namespace_utils.get_application_id()
        encrypted_token = encryptionUtils.encryptMessage(token, encryption_password)
        encrypted_token = base64.b64encode(encrypted_token)

        params = urllib.parse.urlencode({'payment_number': encrypted_token, 'identifier': identifier})

        if namespace_utils.get_namespace():
            host_url = 'https://%s-dot-%s.appspot.com' % (namespace_utils.get_namespace(), applicationId)
        else:
            host_url = 'https://%s.appspot.com' % applicationId


        src_to_gateway = '%s/%s?%s' % (host_url, tokenizator, params)

        return '<a href="%s">' % src_to_gateway + get_web_dictionary(language).get("T_link_to_gateway","") + '</a>'

    return ""



def build_pci_link_from_pep_links(extra_info_dict, language):
    if extra_info_dict.get("payed_by_tpv_link"):
        hotel_code = get_hotel_code()
        for payment_by_link in extra_info_dict.get("payed_by_tpv_link"):
            if payment_by_link.get("datatransdata") and hotel_code:
                hotel_url = get_internal_url(hotel_code)
                url_params = {
                    "action": "decrypt",
                    "data": quote(payment_by_link.get("datatransdata"), safe='')
                }
                encoded_url = "%s/utils?%s" % (hotel_url, urlencode(url_params))
                return '<a href="%s">' % encoded_url + get_web_dictionary(language).get("T_datos_tarjeta", "CC data")+'</a>'
    return ""


def get_test_credit_card():
    result = CreditCardDetails()
    result.company = "4B"
    result.number = "42"
    result.expiryDate = "12/30"
    result.cvv = "123"

    return result



INI_STATUS_MESSAGE = "<!--STATUSMESSAGE_INI-->"
END_STATUS_MESSAGE = "<!--STATUSMESSAGE_END-->"

def delete_status_message_from_content(content):

    i = content.find(INI_STATUS_MESSAGE)
    f = content.find(END_STATUS_MESSAGE)
    str_to_replace = content[i:f+len(END_STATUS_MESSAGE)]

    new_content = content.replace(str_to_replace, "")

    return new_content

def build_html_status_message_for_reservation(reservation, re_payment_link=""):

    wrapper_message = ""
    message = ""

    extra_info = reservation.extraInfo
    if extra_info:
        extra_info = json.loads(extra_info)

        language = "SPANISH"
        if reservation.language:
            language = reservation.language

        languageDict = get_web_dictionary(language)


        wrapper_class="status_message_ko"

        status_reservation = extra_info.get("status_reservation", "")
        if status_reservation == "pending":
            message = "<span id='status_message'>%s</span>" % languageDict["T_STATUS_PAYMENT_PENDING"]
        if status_reservation == "rejected":
            message = "<span id='status_message'>%s</span>" % languageDict["T_STATUS_PAYMENT_CANCELLED"]
        if status_reservation == "confirmed":
            message = "<span id='status_message'>%s</span>" % languageDict["T_STATUS_PAYMENT_APPROVED"]
            wrapper_class="status_message_ok"

        if message:
            styles = '''
                    <style type="text/css">
                        #wrapper-status-message {
                            color: white;
                            background: #d29432 url(/static_1/images/booking/warning.png) no-repeat 20px center;
                            margin: 0 22px;
                            min-height: 80px;
                            padding: 25px 0 0 100px;
                            box-sizing: border-box;
                            position: relative;
                            margin-bottom: 12px;
                            margin-top: 20px;
                        }
                        .status_message_ok{
                            background: #a4c64b url(/static_1/images/booking/bulb_light.png) no-repeat 20px center!important;
                        }

                        #status_message{
                            display: block;


                        }


                    </style>
                    '''
            wrapper_message='%s %s<div id="wrapper-status-message" class="%s">%s %s</div>%s' % (INI_STATUS_MESSAGE, styles, wrapper_class, message,re_payment_link, END_STATUS_MESSAGE)

    return wrapper_message


# @cache_on_datastore(lambda x: "get_hotel_extra_data_booking0_%s_%s" % (x[0], x[1]), tags="WebPageProperty")
@managers_cache(entities="WebPageProperty")
def get_hotel_extra_data(booking0key, language):
    extra_data = {"keyBooking0Source": booking0key}
    booking_0_web_properties = get_properties_for_entity(booking0key, language)
    modified_keys = {
        'email reservas': 'email_reservas_filtered',
        'email header': 'email_header',
        'appid priceseekers': 'appid_priceseekers',
        're': 're_filter',
        'title hotel': 'title_hotel'
    }

    for wpp_name, wpp_value in booking_0_web_properties.items():
        if wpp_name in modified_keys:
            extra_data[modified_keys[wpp_name]] = wpp_value
        else:
            extra_data[wpp_name] = wpp_value

    return extra_data



def getSelectedRate(selectedPrice, language, force_query=False, avoid_save=False):
    try:
        selected_rate_key = selectedPrice[0]

        if session_manager.get(FORCED_RESULT_STRUCTURE):
            forced_result_structure =  session_manager.get(FORCED_RESULT_STRUCTURE)
            if forced_result_structure.get(selected_rate_key):
                return forced_result_structure.get(selected_rate_key)[0]


        if force_query:
            if package_utils.is_package(selected_rate_key):
                package_key, selected_rate_key = package_utils.get_package_and_rate_from_key(selected_rate_key)

            webPageProperty = getRate(selected_rate_key, language).get("rateName", [])

            if len(webPageProperty) > 0:
                #Update session for next steps
                rate_info_sess = session_manager.get(RATE_NAME_AND_DESC + selected_rate_key)

                if rate_info_sess and not avoid_save:
                    rate_info_sess[0] = webPageProperty[0].value
                    session_manager.set(RATE_NAME_AND_DESC + selected_rate_key, rate_info_sess)

                return webPageProperty[0].value
            else:
                return ''
        return session_manager.get(RATE_NAME_AND_DESC + selected_rate_key)[0]
    except:
        webPageProperty = getRate(selectedPrice[0], language).get("rateName", [])

        if len(webPageProperty) > 0:
            return webPageProperty[0].value
        else:
            return ''


def show_cvv_card():
    cvv_config = get_config_property_value(advance_configs_names.SHOW_CVV_IN_CARD_DETAILS)
    if 'non_refundable' in cvv_config:
        noRefundableRate = rateIsNonRefundable(getSelectedRateKey())
        if noRefundableRate:
            return True

    elif cvv_config:
        return True


def get_price_breakdown_from_reservation(reservation):
    extra_info = {}
    if hasattr(reservation, 'extraInfo') and reservation.extraInfo:
        extra_info = json.loads(reservation.extraInfo)

    price_breakdown = extra_info.get('price_info', {})
    total_with_taxes = price_breakdown.get('total_with_taxes')
    already_paid = _get_total_payed_amount_from_all_sources(extra_info)

    if total_with_taxes:
        price_breakdown['pending'] = float(total_with_taxes) - float(already_paid)

    if already_paid:
        price_breakdown['payed'] = already_paid

    return price_breakdown

def getPriceRoom(reservation, number_room):

    price = 0
    if reservation:
        extra_info = json.loads(reservation.extraInfo)
        if extra_info:
            price = float(extra_info.get("price_room_%s" % number_room, 0))

    return float(price)


def get_original_total_reservation(selectedPrice):

    total_room = get_original_price_from_selected_price(selectedPrice)
    total_suplements = float(getPriceSupplements())

    if session_manager.get(FORCED_ADDITIONAL_SERVICES_FROM_MANAGER):
        service_price_forzed = session_manager.get(FORCED_ADDITIONAL_SERVICES_FROM_MANAGER)
        total_suplements = float(service_price_forzed)

    return total_room + total_suplements



def getAllPriceSupplements(pay_later=False, prepareNoEnabled=False, apply_default_tax=0):
    selectedServices = session_manager.get(SELECTED_ADDITIONAL_SERVICES) or {}
    agency_info = session_manager.get(AGENCY_INFO)
    agency_config = get_web_configuration(AGENCY_WEB_CONFIGURATION)
    additional_services_by_calendar = session_manager.get(SELECTED_ADDITIONAL_SERVICES_CALENDARS)
    advanced_additional_services = session_manager.get(SELECTED_ADVANCED_ADDITIONAL_SERVICES)

    if not selectedServices and not additional_services_by_calendar and not advanced_additional_services:
        return {}

    if advanced_additional_services:
        tax_breakdown = get_advanced_additional_services_taxes_breakdown(advanced_additional_services)
        processed_result = {}
        services_keys = set([service.get('service_key') for service in advanced_additional_services])
        for service_key in services_keys:
            tax_breakdown_service = tax_breakdown.get(service_key)
            processed_result[service_key] = {
                "price": tax_breakdown_service.get('price_with_taxes'),
                "tax_percent": tax_breakdown_service.get('tax_percentage'),
                "tax": tax_breakdown_service.get('tax_amount'),
                "name": [service.get('original_name') for service in advanced_additional_services if service.get('service_key') == service_key][0]
            }
        return processed_result

    supplements = get_all_supplements_map()

    mergedPricesSupplements = session_manager.get(MERGED_PRICES_SUPPLEMENTS)
    if not mergedPricesSupplements:
        mergedPricesSupplements = {}

    result = {}

    sups_maps_config = {}
    if apply_default_tax:
        sups_maps_config = get_special_tax_for_suplements()

    additional_services_by_calendar = json.loads(
        additional_services_by_calendar) if additional_services_by_calendar else {}

    if selectedServices or additional_services_by_calendar:
        for key, supplement in supplements.items():
            tax = 0
            tax_price = 0
            if apply_default_tax:
                tax = apply_default_tax

            amount = selectedServices.get('amount_' + key, 0)
            days = selectedServices.get('days_' + key, 0)

            if amount or days:

                logging.info("selectedServices amount: %s selectedServices days: %s", amount, days)

                if additional_services_by_calendar.get(key):
                    selections_list = retreive_days_and_amount_from_service_calendar(key, additional_services_by_calendar,
                                                                                     supplements)
                    amount = 1
                    days = sum(map(lambda x: int(x.get('amount')) * len(x.get('days')), selections_list))

                    logging.info("amount and days changed becasue of additional_services_by_calendar. amount: %s days: %s",
                                 amount, days)

                # if reservstion is already made (for example from a modification or a send-confirmation endomo¡point) we have already the price calculated
                forced_price_from_reservation = selectedServices.get('forced_price_' + key, 0)

                name_of_supplement_to_get = to_string_encoding(supplement.name.lower()) if supplement.name else None

                mergedPriceDict = mergedPricesSupplements.get(name_of_supplement_to_get, False)

                supplement_price = supplement.price
                agency_commission = 0
                if agency_info and agency_info.get('type') and agency_info.get('type') == 'pvp' and not agency_config.get("commission_exclude_supplements"):
                    agency_commission = float(supplement_price) * float(agency_info.get('commision')) / 100.00
                    supplement_price = float(supplement_price) - agency_commission
                if tax:
                    if sups_maps_config.get(str(supplement.key)):
                        tax = sups_maps_config.get(str(supplement.key))
                        logging.info("special tax for sup found %s -> %s", supplement.key, tax)
                        tax = float(str(tax).replace("%", ""))

                    if isinstance(tax, str):
                        tax = float(str(tax).replace("%", ""))

                    tax_calculation = lambda x: x * ((100 + tax) / 100)
                    supplement_price = float(tax_calculation(float(supplement_price)))

                if mergedPriceDict and not apply_default_tax:
                    price = mergedPriceDict.get("price", supplement_price)
                    logging.info("price from mergedPriceDict: %s", price)

                else:
                    tax_price = supplement_price - (float(supplement.price) - agency_commission)
                    price = supplement_price
                    logging.info("price directly from supplement: %s", price)

                if get_config_property_value(advance_configs_names.CAMPING):

                    prices_per_amount = session_manager.get(SUPPLEMENT_PRICE_PER_AMOUNT_PREFIX + key)
                    logging.info("CAMPING: prices_per_amount found: %s", prices_per_amount)
                    if prices_per_amount:
                        price = prices_per_amount.get(amount)

                        if not price:
                            price = 0.0
                else:

                    if forced_price_from_reservation and not apply_default_tax:
                        if session_manager.get(FORCED_SAVE_MANAGER2):
                            final_price = float(price) * int(amount) * int(days)
                            final_price = _apply_discount(final_price)

                            result[key] = {
                                "price": round(final_price, 2),
                                "tax_percent": tax,
                                "tax": round(tax_price * int(amount) * int(days), 2),
                                "name": name_of_supplement_to_get if name_of_supplement_to_get else ""
                            }

                        else:
                            final_price = float(forced_price_from_reservation)
                            final_price = _apply_discount(final_price)

                            result[key] = {
                                "price": round(final_price, 2),
                                "tax_percent": tax,
                                "tax": round(tax_price, 2),
                                "name": name_of_supplement_to_get if name_of_supplement_to_get else ""
                            }

                        logging.info("forced_price_from_reservation: %s", result)
                    else:

                        if pay_later and selectedServices.get('pay_later_' + key, False):
                            final_price = float(price) * int(amount) * int(days)
                            final_price = _apply_discount(final_price)
                            result[key] = {
                                "price": round(final_price, 2),
                                "tax_percent": tax,
                                "tax": round(tax_price * int(amount) * int(days), 2),
                                "name": name_of_supplement_to_get if name_of_supplement_to_get else ""
                            }

                        if not pay_later and not selectedServices.get('pay_later_' + key, False):
                            final_price = float(price) * int(amount) * int(days)
                            final_price = _apply_discount(final_price)
                            result[key] = {
                                "price": round(final_price, 2),
                                "tax_percent": tax,
                                "tax": round(tax_price * int(amount) * int(days), 2),
                                "name": name_of_supplement_to_get if name_of_supplement_to_get else ""
                            }
        return result


def _apply_discount(final_price):
    """
    :unit_test: unit_tests.booking_process.utils.booking.test_bookingUtils.TestBookingUtils.test_apply_discount
    """
    if session_manager.get(FORCE_NET_PAYMENT):
        final_price = _convert_pvp_price_to_net(final_price)
    if session_manager.get(DISCOUNT_APPLIED):
        discount = float(session_manager.get(DISCOUNT_APPLIED))
        final_price = round(((float(final_price) - (float(final_price) / float(100 / float(discount))))), 2)
    return final_price



def getSeparatedPricesPerRoom(selectedPrice):
    '''
    Returns a list of prices, one value per each room
    '''
    result = []
    index_room = 0
    priceFromParitymaker = 0

    rateKey = getSelectedRateKey()
    truncate_decimal = get_config_property_value(advance_configs_names.TRUNCATE_DECIMAL_BOOKING)
    round_decimal = get_config_property_value(ROUND_DECIMAL_BOOKING)


    for currentPrice in selectedPrice:

        selectedRegimen = getSelectedRegimen(selectedPrice[index_room], "SPANISH", get_always=True)
        if session_manager.get(PRICES_FROM_RATECHECK):
            priceFromParitymaker = getPriceFromParityMaker(selectedPrice, getSelectedRoom(selectedPrice, "SPANISH"), selectedRegimen, rateIsNonRefundable(rateKey), float(currentPrice[3]))

        if priceFromParitymaker:
            if truncate_decimal or DEV:
                result.append(int(priceFromParitymaker))

            elif round_decimal:
                result.append(int("%.0f" % float(priceFromParitymaker)))

            else:
                result.append(float(priceFromParitymaker))

        else:
            truncate_decimal = get_config_property_value(advance_configs_names.TRUNCATE_DECIMAL_BOOKING)
            if truncate_decimal:
                result.append(int(float(currentPrice[3])))

            elif round_decimal:
                result.append(int("%.0f" % float(currentPrice[3])))

            else:
                result.append(float(currentPrice[3]))

        index_room += 1

    return result



def getSelectedMultipleRateConditions(selectedPrice_list, language):
    conditions_rates_list = []

    filtered_selectedPrices = []
    already_matched = []

    for selection in selectedPrice_list:
        if selection[0] in already_matched:
            continue

        already_matched.append(selection[0])
        filtered_selectedPrices.append(selection)

    from booking_process.utils.booking.conditions.rate_conditions import get_text_rate_conditions
    for selected_price in filtered_selectedPrices:
        selected_rate_conditions = list(getSelectedRateConditionsDatastore(selected_price, language, getRateName=True))
        selected_rate_conditions[1] = get_text_rate_conditions(language, selected_price)
        conditions_rates_list.append(selected_rate_conditions)

    return conditions_rates_list


def getSelectedRateConditionsDatastore(selectedPrice, language, getRateName=False):
    rate_key = selectedPrice[0]
    logging.info("getting conditions for rate: %s", rate_key)

    if package_utils.is_package(rate_key):
        package_key, rate_key = package_utils.get_package_and_rate_from_key(rate_key)

    webPageProperty = getRate(rate_key, language)

    rateDescription = webPageProperty.get("rateDescription")
    rateName = webPageProperty.get('rateName')

    rateConditions = ""
    if rateDescription and len(rateDescription) > 0 and rateDescription[0].value:
        rateConditions = unescape(rateDescription[0].value).replace("</ div>", "</div>")
        logging.info("Conditions for rate found: %s", rateConditions)


    if rateName:
        rateName = unescape(rateName[0].value)

    checkBalancedHTML = fix_unbalanced_html(rateConditions)
    if checkBalancedHTML:
        notify_by_email_with_limit('[AccountOwner] Unbalanced HTML Tags', 'Rate conditions have unbalanced HTML tags, please check it.<br><br><br><br><table style="border: solid 1px #0D8EB5"><tr><td>%s</td></tr></table>' % rateConditions, add_hotel_info=True)
        if getRateName:
            return rateName, checkBalancedHTML

        return checkBalancedHTML

    if getRateName:
        return rateName, rateConditions

    return rateConditions


def getSelectedRateInternalName(selectedPrice):

    result = ''

    current_namespace = namespace_utils.get_namespace()
    try:
        if get_config_property_value(advance_configs_names.GENERIC_BOOKING_PROCESS):
            hotel_code = session_manager.get(advance_configs_names.GENERIC_BOOKING_PROCESS)
            logging.info("GENERIC_BOOKING_PROCESS at getSelectedRateInternalName - Hotel code: %s", hotel_code)
            namespace_utils.set_namespace(hotel_code)

        ratekey_to_use = selectedPrice[0]
        if package_utils.is_package(ratekey_to_use):
            package_key, ratekey_to_use = package_utils.get_package_and_rate_from_key(ratekey_to_use)

        logging.info("ratekey_to_use for get internal name: %s", ratekey_to_use)
        myRate = {}
        try:
            # myRate = Rate.get(db.Key(ratekey_to_use)) #@UndefinedVariable
            myRate = get_rate(ratekey_to_use, "SPANISH")
        except:
            logging.error("Imposible to getl loca name of rate.")
            myRate = None

        if myRate:
            result = myRate.get('localName')

        if package_utils.is_package(ratekey_to_use):
            result += " + PACKAGE: " + get_package_name(package_key)

    finally:
        namespace_utils.set_namespace(current_namespace)

    return result


def getSelectedRegimenDescription(selectedPrice, language):
    webPageProperty = getRegimen(selectedPrice[2], language).get("regimenDescription")
    result = ""
    if webPageProperty and len(webPageProperty) > 0:
        result = webPageProperty[0].value

    if get_config_property_value(advance_configs_names.USE_BOOKING_STRUCTURE_FOR_RESULTS):
        return ""

    if result:
        result_clean = html.unescape(result)
        checkBalancedHTML = fix_unbalanced_html(result_clean)
        if checkBalancedHTML:
            notify_by_email_with_limit('[Account] Unbalanced HTML Tags', 'Regimen description has unbalanced HTML tags, please check it.<br><br><br><br><table style="border: solid 1px #0D8EB5"><tr><td>%s</td></tr></table>' % result_clean, add_hotel_info=True)
        return result_clean
    else:
        return result


def getHotelManagerEmail4Cancellations(reservation):

    logging.info("getHotelManagerEmail4Cancellations")

    email_cancel_and_modification = get_config_property_value(advance_configs_names.CANCEL_AND_MODIFY_EMAIL)
    if email_cancel_and_modification:
        return email_cancel_and_modification

    if reservation:
        forcedEmailManager = reservationEmailIsForcedByRE(reservation)
        if forcedEmailManager:
            return forcedEmailManager


    return get_hotel_manager_email(reservation)


def reservationEmailIsForcedByRE(reservation, return_section_bookin0_key = False):
    '''
    FIRST: check if this hotel has booking0
    SECOND: for each booking_0 section, check if has RE's filters
    THIRD: If second, chack all REs and if match get the 'forced email hotel manager'

    return_section_bookin0_key is used in send_confirmation for modifications and cancellations
    '''


    logging.info("reservationEmailIsForcedByRE")

    #all rooms are set in Spanish in the reservation model
    language = "SPANISH"


    #FIRST: asking if it is a special booking0 (host by sections in the same web)
    booking0_sections = get_config_property_value(advance_configs_names.BOOKING0_BY_SECTION)

    #Note that booking0_section will suppose it is associated to the first applicationId
    if booking0_sections:

        logging.info("reservationEmailIsForcedByRE: Has booking sections %s", booking0_sections)

        list_sections_booking0 = booking0_sections.split(";")
        #SECOND
        for current_section in list_sections_booking0:
            #Get Regular Expression Filter
            booking0 = get_section_from_link_url(current_section + '.html', language)

            all_extra_info = get_hotel_extra_data(booking0.get("key",""), language)

            re_room = all_extra_info.get("re_filter","")

            #THIRD: check if the RE pattern match with the room name of the reservation

            webPageProperty = getRoom(reservation.roomType1, language).get("roomName")
            if webPageProperty and len(webPageProperty) > 0:
                selectedRoom = webPageProperty[0].value
            else:
                selectedRoom = ""

            if re_room and re.search(re_room, selectedRoom, re.IGNORECASE):

                logging.info("reservationEmailIsForcedByRE: Match RE %s VS ROOM %s", re_room, selectedRoom)

                email_reservas_filtered = all_extra_info.get("email_reservas_filtered","")
                if email_reservas_filtered:
                    logging.info("saving email manager forced by filters: %s", email_reservas_filtered)
                    session_manager.set('email_reservas_filtered', email_reservas_filtered)
                else:
                    session_manager.set('email_reservas_filtered', "")

                if return_section_bookin0_key:
                    return booking0.get("key","")


                if email_reservas_filtered:
                    logging.info("reservationEmailIsForcedByRE: returning forced email %s", email_reservas_filtered)
                    return email_reservas_filtered

    return ''


def convert_currency_price_per_day_table(original_data):
    try:
        currency = get_selected_currency_code()
        currency_on_confirmation = get_config_property_value(advance_configs_names.CURRENCY_ON_CONFIRMATION)
        language = SPANISH

        if session_manager.get('language'):
            language = session_manager.get('language')

        if currency and currency_on_confirmation:
            default_currency = get_currency(get_default_config=True)

            if currency != default_currency:
                currencies_info = get_params_for_currency_selector(language)
                conversion_value_retreived = [x for x in currencies_info['currencies'] if x.get('shortName') == currencies_info['selectedCurrency']]

                if conversion_value_retreived:
                    conversion_value_retreived = conversion_value_retreived[0].get('exchangeRate')
                else:
                    conversion_value_retreived = 1

                result_currency_converted = {}

                for room_key, room_prices_by_date in original_data.items():
                    room_parts = room_key.split(": ")
                    room_index = room_parts[0]

                    room_details = room_parts[1].split(" (")

                    if len(room_details) > 1:
                        room_internal_key = room_details[0]
                        room_occupancy = room_details[1].replace(")", "")
                    else:
                        room_internal_key = room_details[0]
                        room_occupancy = ""

                    room_name = getRoom(room_internal_key, language).get("roomName")[0].value
                    room_key = f"{room_index}: {room_name} ({room_occupancy})"

                    for date, prices in room_prices_by_date.items():
                        if date != 'total':
                            for index, price in enumerate(prices):
                                price = float("{0:.2f}".format(float(price) / float(conversion_value_retreived)))
                                result_currency_converted.setdefault(room_key, {}).setdefault(date, []).append(price)
                        else:
                            price = float("{0:.2f}".format(float(prices) / float(conversion_value_retreived)))
                            result_currency_converted[room_key][date] = price

                logging.info("In convert_currency_price_per_day_table after currency conversion, result: %s" % result_currency_converted)

                return result_currency_converted

    except Exception as e:
        logging.info("Error in convert_currency_price_per_day_table: %s" % e)
        return []


def build_search_dict_from_reservation(reservation):
    try:
        extra_info = json.loads(reservation.extraInfo)
    except:
        extra_info = {}


    if extra_info.get("shopping_cart_human_read"):
        search_dictionary = {
            'startDate': reservation.startDate,
            'endDate': reservation.endDate,
            'rooms': []
        }

        for index, room in enumerate(extra_info["shopping_cart_human_read"]["rooms"], 1):
            ocupancy = room.get("occupancy", "0-0-0").split("-")
            search_dictionary["rooms"].append({
                'numAdults': ocupancy[0],
                'numKids': ocupancy[1],
                'numBabies': ocupancy[2],
                'owner_info': get_occupancy_data(reservation, extra_info, index)
            })

    elif extra_info.get("shopping_cart"):
        search_dictionary = {
            'startDate': reservation.startDate,
            'endDate': reservation.endDate,
            'rooms': []
        }

        for index, room in enumerate(extra_info["shopping_cart"], 1):
            search_dictionary["rooms"].append({
                'numAdults': room.get("adults"),
                'numKids': room.get("kids"),
                'numBabies': room.get("babies"),
                'owner_info': get_occupancy_data(reservation, extra_info, index)
            })
    else:
        search_dictionary = {
            'startDate': reservation.startDate,
            'endDate': reservation.endDate,
            'rooms': [
                {
                    'numAdults': reservation.adults1,
                    'numKids': reservation.kids1,
                    'numBabies': reservation.babies1
                }]
        }

        if reservation.numRooms == 2 or reservation.numRooms == 3:
            search_dictionary['rooms'].append(
                {
                    'numAdults': reservation.adults2,
                    'numKids': reservation.kids2,
                    'numBabies': reservation.babies2
                }
            )

        if reservation.numRooms == 3:
            search_dictionary['rooms'].append(
                {
                    'numAdults': reservation.adults3,
                    'numKids': reservation.kids3,
                    'numBabies': reservation.babies3
                }
            )

    return search_dictionary


def selected_rooms_break_down(selected_prices, language):
    rooms_formatted = []

    for room_element in selected_prices:
        rate_key, room_key, regimen_key, price = room_element[:4]

        if "PACKAGE_" in rate_key:
            rate_key = rate_key.split("_@_")[2]

        rate_info = copy.copy(get_rate(rate_key, language))
        room_info = copy.copy(getRoom(room_key, language))
        board_info = copy.copy(getRegimen(regimen_key, language))

        #This has no sense, should be retreived from methods directly
        room_info['roomName'] = room_info['roomName'][0].value if room_info.get('roomName') else ''
        board_info['regimenName'] = board_info['regimenName'][0].value if board_info.get('regimenName') else ''
        rate_info['rateName'] = rate_info['rateName'][0].value if rate_info.get('rateName') else ''

        formatted_dict = {
            'rate': rate_info,
            'room': room_info,
            'board': board_info
        }

        rooms_formatted.append(formatted_dict)

    return rooms_formatted


def find_discount_per_day(promotions, room_key, rate_key, board_key):
    if promotions:
        for promo in promotions:
            values_per_days = promo.get(room_key, {}).get(rate_key, {}).get(board_key, {}).get("valuesPerDay", [])
            if values_per_days:
                return values_per_days

    return []


def getHotelHost():
    """
    :unit_test: unit_tests.booking_process.utils.booking.test_bookingUtils.TestBookingUtils.test_getHotelHost
    """
    if DEV:
        #return "http://demo2-dot-test-hotel.appspot.com"
        return "http://localhost:8090"
    myHost = request.host
    applicationId = namespace_utils.get_namespace()

    logging.info("HTTP_HOST upselling: %s", myHost)

    if myHost.find("-dot-") != -1:
        sufixUrl = myHost[myHost.find("-dot-"):]
    else:
        sufixUrl = myHost

    logging.info("SufixUrl upselling: %s", sufixUrl)

    if (applicationId.replace(REMOTE_PREFIX, "") not in sufixUrl)  and ("appspot" in sufixUrl):
        hotelHost = "http://%s%s" % (applicationId.replace(REMOTE_PREFIX, ""), sufixUrl)
    else:
        hotelHost = "http://%s" % sufixUrl

    logging.info("hotelHost upselling: %s", hotelHost)

    return hotelHost


def news_search_session_expire_data():
    args = {
        "numRooms": "1",
        "adultsRoom1": "2",
        "adultsRoom2": "0",
        "adultsRoom3": "0",
        "childrenRoom1": "0",
        "childrenRoom2": "0",
        "childrenRoom3": "0",
    }

    return args


def getRate(rate_key, language):

   return get_rate(rate_key, language)


# @cache_on_datastore(lambda x: "get_all_regimens_%s" % x[0], tags="Regimen, WebPageProperty")
@managers_cache(entities="WebPageProperty,Regimen")
def getAllRegimens(language):
    logging.info("Getting all regimens")
    all_regimens = directDataProvider.get("Regimen")
    regimen_name = _getRegimensNames(language)
    regimen_description = _getRegimensDescription(language)
    result_regimens = []
    for regimen in all_regimens:
        regimen_args = regimen.to_dict()
        filter_name = [l for l in regimen_name if l.entityKey == str(regimen.key)]
        filter_description = [l for l in regimen_description if l.entityKey == str(regimen.key)]
        regimen_args['regimenName'] = filter_name if filter_name else []
        regimen_args['regimenDescription'] = filter_description if filter_description else []
        regimen_args['key'] = str(regimen.key)
        result_regimens.append(regimen_args)

    logging.info(result_regimens)

    return result_regimens


def getRegimen(entityKey, language):
    # TODO: Remove this, and use booking_process.utils.data_management.boards_data.get_board
    filter_regimen = [l for l in getAllRegimens(language) if l.get("key") == entityKey]
    return filter_regimen[0] if filter_regimen else {}


def _getRegimensNames(language):
    return directDataProvider.get('WebPageProperty', {'languageKey': language, 'mainKey': 'regimenName'})


def _getRegimensDescription(language):
    return directDataProvider.get('WebPageProperty', {'languageKey': language, 'mainKey': 'regimenDescription'})


def has_newsletter_api():
    has_api = False
    list_apis = [advance_configs_names.MAILCHIMP_API_KEY,
                 advance_configs_names.EGOI_API_KEY,
                 advance_configs_names.ACUMBAMAIL_API_KEY,
                 advance_configs_names.MAILERLITE_API_KEY,
                 advance_configs_names.SENDINBLUE_API_KEY,
                 advance_configs_names.MDIRECTOR_API_KEY,
                 advance_configs_names.RD_STATION,
                 advance_configs_names.FIDELTOUR_SUBSCRIBE,
                 advance_configs_names.ACUMBAMAIL_LIST_KEY,
                 advance_configs_names.RAPIDMAIL_API_KEY]

    for api in list_apis:
        if get_config_property_value(api):
            has_api = True
            break

    return has_api


def retreive_free_night_info(night_index):
    if not session_manager.get(SEARCH_KEY_PREFIX):
        return {}
    string_start = session_manager.get(SEARCH_KEY_PREFIX)['startDate']
    night_integer = int(night_index)
    if night_index != 0:
        datetime_start = datetime.strptime(string_start, '%Y-%m-%d')
        datetime_start = datetime_start + timedelta(days=night_integer)
        string_start = datetime_start.strftime('%d/%m/%Y')

    selectedOption = session_manager.get(SELECTED_OPTION_KEY)

    selectedPrice = []
    for partialSelected in selectedOption.split(";"):
        selectedPrice.append(session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected))
    totalPrice = getTotalPrice(selectedPrice)

    available_promotions = []


    for partialSelected in selectedOption.split(";"):
        totalsInfo = session_manager.get(PRICE_OPTION_KEY_PREFIX_V2 + partialSelected)
        available_promotions.append(totalsInfo)

    #Can't be used this because the prices doesn't match
    #promotion_applied = available_promotions[0].get('promotion', {}).get('value')

    all_available_promotions_discounts = []
    for promotion_element in available_promotions[0].get('promotion', {}).get('multiple_promotions', []):
        all_available_promotions_discounts += promotion_element['price']

    promotion_applied = max(all_available_promotions_discounts)

    logging.info("Free night - Selected option: %s" % selectedOption)
    logging.info("Free night - Promotion applied: %s" % promotion_applied)
    logging.info("Free night - Available promotions: %s" % available_promotions)
    logging.info("Free night - Total price: %s" % totalPrice)


    return {'date': string_start, 'price': promotion_applied, 'total_real_price': (totalPrice + promotion_applied)}


def separate_results_by_applications_id(django_results, language, mix_hotels_config, request):
    separated_results = OrderedDict()
    mobile_process = get_config_property_value(advance_configs_names.NEW_BOOKING_PROCESS_MOBILE) and user_agent_is_mobile()
    multiple_rooms_no_merged = get_config_property_value(advance_configs_names.BOOKING1_MULTIPLE_ROOMS_WITHOUT_MERGING) and get_num_rooms() > 1
    if mobile_process or multiple_rooms_no_merged:
        room_result_by_app_id = OrderedDict()
        app_id_list = set()

        for room_block in django_results:
            room_dict_by_id = OrderedDict()
            for room_element in room_block:
                target_app_id = room_element.get('roomStructure')[0].get('application_id')
                app_id_list.add(target_app_id)
                if not room_dict_by_id.get(target_app_id):
                    room_dict_by_id[target_app_id] = []
                room_dict_by_id[target_app_id].append(room_element)

            for app_id in app_id_list:
                if not room_result_by_app_id.get(app_id):
                    room_result_by_app_id[app_id] = {'room_results': []}
                if room_dict_by_id.get(app_id):
                    room_result_by_app_id[app_id]['room_results'].append(room_dict_by_id.get(app_id))
        for app_id in room_result_by_app_id.keys():
            separated_results[app_id] = {'rooms_results': [room_result_by_app_id.get(app_id).get('room_results')]}

        logging.info('Separated results by app id for new booking process mobile: %s' % separated_results)

    else:
        for room_element in django_results:
            if type(room_element) is dict:
                target_app_id = room_element['roomStructure'][0].get('application_id')
            else:
                target_app_id = room_element[0]['roomStructure'][0].get('application_id')
            if not separated_results.get(target_app_id):
                separated_results[target_app_id] = {'rooms_results': []}

            separated_results[target_app_id]['rooms_results'].append(room_element)

    original_namespace = namespace_utils.get_namespace()

    for app_id, results in list(separated_results.items()):
        current_app_results = {
            'hotel_info': {},
            'cheapest_result': {}
        }

        try:
            namespace_utils.set_namespace(app_id)
            alternative_name = get_config_property_value(advance_configs_names.ALTERNATIVE_HOTEL_NAME)
            hotel_name = get_config_property_value(advance_configs_names.EMAIL_SENDER)

            alternative_section_name = get_config_property_value(advance_configs_names.SECTION_HOTEL_MIX_INFO)
            if alternative_section_name:
                alternative_section = get_section_from_section_spanish_name(alternative_section_name, language)
                if alternative_section:
                    if alternative_section.get("subtitle", ""):
                        alternative_name = alternative_section.get("subtitle", "")

                    alternative_properties = {
                        'color': 'hotel_name_color',
                        'color_number': 'hotel_number_color',
                        'hide_bottom_line': 'hotel_name_hide_bottom_line',
                        'dropdown_results': 'hotel_dropdown_results',
                        'show_prices': 'show_prices'
                    }
                    section_properties = get_properties_for_entity(alternative_section.get('key'), language)
                    for manager_name, context_name in alternative_properties.items():
                        alternative_data = section_properties.get(manager_name, '')
                        current_app_results['hotel_info'][context_name] = alternative_data

                    alternative_image = get_pictures_from_section_name(alternative_section_name, language)

                    hotel_header_image = list(filter(lambda x: x.get('title') == 'hotel_header_image', alternative_image))
                    if hotel_header_image:
                        current_app_results['hotel_info']['hotel_header_image'] = hotel_header_image[0].get('servingUrl', '')

                    hotel_location = list(filter(lambda x: x.get('title') == 'hotel_location', alternative_image))
                    if hotel_location:
                        current_app_results['hotel_info']['hotel_location'] = hotel_location[0].get('description', '')

            current_app_results['hotel_info']['hotel_name'] = alternative_name if alternative_name else hotel_name
            current_app_results['cheapest_result'] = get_cheapest_result(results.get('rooms_results', []))
            separated_results[app_id].update(**current_app_results)

        except Exception as e:
            logging.error("Can't be retreived the hotel information for separate results")
            logging.error(e)
        finally:
            namespace_utils.set_namespace(original_namespace)

    ordered_dict = OrderedDict()
    if mix_hotels_config and ';' in mix_hotels_config:
        splitted_list = mix_hotels_config.split(";")
        already_added = []
        for app_id in splitted_list:
            if separated_results.get(app_id):
                already_added.append(app_id)
                ordered_dict[app_id] = separated_results.get(app_id)

        for app_id, results in list(separated_results.items()):
            if app_id not in splitted_list:
                ordered_dict[app_id] = results

    else:
        ordered_dict = separated_results


    return ordered_dict


def get_no_dispo_hotels_name_for_mix(mix_namespaces_list, results, language):
    original_namespace = namespace_utils.get_namespace()
    hotels_no_dispo = []
    for mix_namespace in mix_namespaces_list:
        if not mix_namespace in results.keys():
            try:
                namespace_utils.set_namespace(mix_namespace)

                hotel_data = {
                    'hotel_name': get_hotel_name_for_mix(language)
                }

                alternative_section_name = get_config_property_value(advance_configs_names.SECTION_HOTEL_MIX_INFO)
                if alternative_section_name:
                    alternative_section = get_section_from_section_spanish_name(alternative_section_name, language)
                    alternative_image = get_pictures_from_section_name(alternative_section_name, language)
                    section_properties = get_properties_for_entity(alternative_section.get('key'), language)

                    hotel_header_image = list(
                        filter(lambda x: x.get('title') == 'hotel_header_image', alternative_image))

                    hotel_data['hotel_image'] = hotel_header_image
                    hotel_data['no_dispo_message'] = section_properties.get('no_dispo_message')

                hotels_no_dispo.append(hotel_data)
            except Exception as e:
                logging.error("Can't be retreived the hotel information for separate results")
                logging.error(e)
            finally:
                namespace_utils.set_namespace(original_namespace)
    return hotels_no_dispo


def get_hotel_name_for_mix(language):
    hotel_name = get_config_property_value(advance_configs_names.EMAIL_SENDER)
    alternative_hotel_name = get_config_property_value(advance_configs_names.ALTERNATIVE_HOTEL_NAME)
    if alternative_hotel_name:
        hotel_name = alternative_hotel_name

    alternative_section_name = get_config_property_value(advance_configs_names.SECTION_HOTEL_MIX_INFO)
    if alternative_section_name:
        alternative_section = get_section_from_section_spanish_name(alternative_section_name, language)
        if alternative_section and alternative_section.get('subtitle'):
            hotel_name = alternative_section.get('subtitle')

    return hotel_name


def generate_banners_by_mix(separated_hotels_mix, results, language, is_mobile=False):
    banner_config = 'banners='
    splitted_config = separated_hotels_mix.split(";")
    splitted_config = [x for x in splitted_config if banner_config in x]
    if splitted_config:
        available_hotels = list(results.keys())
        banner_section_name = splitted_config[0].replace(banner_config, '')
        banner_section = get_section_from_section_spanish_name(banner_section_name, language)
        banner_section_pictures = getPicturesForKey(language, banner_section.get('key'), [])
        banner_section_pictures = [x for x in banner_section_pictures if (not is_mobile and not x.get("onlyInMobile")) or (is_mobile and (x.get('allowInMobile')))]
        for picture_element in banner_section_pictures:
            picture_hotels = picture_element.get('title', '').split(";")
            if not [x for x in picture_hotels if x not in available_hotels] and len(picture_hotels) == len(available_hotels):
                return [{'pictures': [picture_element]}]


def _get_room_price_from_reservation(identifier, room_index, original_room_key):
    reservation = directDataProvider.get("Reservation", {'identifier': identifier})
    if reservation:
        reservation = reservation[0]
        extra_info = reservation.extraInfo
        if extra_info:
            extra_info = json.loads(extra_info)
        else:
            extra_info = {}

        if original_room_key:
            room_price = 0.0
            price_per_day_dict = extra_info.get("prices_per_day", {})

            for room_key_prices, room_price_per_day in list(price_per_day_dict.items()):
                if "%s: " % room_index in room_key_prices:

                   for day, day_prices_info in list(room_price_per_day.items()):
                       if isinstance(day_prices_info, list):
                            room_price += float(day_prices_info[2])

            logging.info("getting original price from reservation from prices_per_day for room index: %s room key: %s room price = %s", room_index, original_room_key, room_price)
        else:
            room_price = float(reservation.price) / float(reservation.numRooms)
            logging.info("getting original price from reservation from total (fake room price): %s", room_price)


        return room_price

    return None


def get_first_rate_key_from_results(results):
    for room_element in results.get('result', []):
        if isinstance(room_element, dict):
            for room_key, rates_info in list(room_element.items()):
                available_results_rates = list(rates_info.keys())
                if available_results_rates:
                    return available_results_rates[0]

    return ""


def avoid_rates_worst_cases(results, data_structure, rates_avoid_worst_options):
    '''
    i.e. 1. FLEXIBLE RATETIGER;2. NRF RATETIGER@1.1 FLEXIBLE CLONADA;2.1 NRF CLONADA
    This will remove  results from each Set (separated by @@@) when the price is the same or worse,
    the order indicates priority.

    Typically you don't want to show NR when they are more expensive than FLEXIBLE
    '''
    logging.info("Booking1 avoid worst cases: %s", rates_avoid_worst_options)
    logging.info("Removing unneeded boards")
    available_results_rates = []
    for room_element in results.get('result', []):
        for room_key, rates_info in list(room_element.items()):
            available_results_rates += list(rates_info.keys())

    available_results_rates = set(available_results_rates)

    rates_info = {}
    for rate_key in available_results_rates:
        rates_info[rate_key] = data_structure.get(rate_key)


    splitted_config = rates_avoid_worst_options.split("@@@")

    promotions = results.get('promotions', [])

    for config_element in splitted_config:
        #check if both are present in results
        main_rate, avoid_rate = config_element.split(";")
        logging.info("Booking1 avoid worst cases main_rate %s VS avoid_rate %s", main_rate, avoid_rate)

        main_key = [x_y for x_y in list(rates_info.items()) if x_y[1] and x_y[1][-1] == main_rate]
        avoid_key = [x_y1 for x_y1 in list(rates_info.items()) if x_y1[1] and x_y1[1][-1] == avoid_rate]
        if main_key and avoid_key:
            logging.info("Booking1 avoid worst cases main_key %s VS avoid_key %s", main_key, avoid_key)

            #if both have same prices, let's delete avoid_rate one
            for index, room_results in enumerate(results.get('result')):
                elements_to_remove = _remove_redundant_boards(avoid_key[0][0], main_key[0][0], room_results, promotions[index])
                _apply_worst_rates_remove(room_results, elements_to_remove)

            for index, room_results in enumerate(results.get('resultsPerDay')):
                elements_to_remove = _remove_redundant_boards(avoid_key[0][0], main_key[0][0], room_results, promotions[index], remove_price_per_day=True)
                _apply_worst_rates_remove(room_results, elements_to_remove)


def _apply_worst_rates_remove(room_results, elements_to_remove):
    if not elements_to_remove:
        return

    for room_key, rates_info in list(room_results.items()):
        for remove_element in elements_to_remove:
            if rates_info.get(remove_element[0], {}).get(remove_element[1]):
                del rates_info[remove_element[0]][remove_element[1]]


def _remove_redundant_boards(avoid_key, main_key, room_results, promotions, remove_price_per_day=False):

    max_results_to_avoid_remove_it = 999999

    if not promotions:
        promotions = {}

    elements_to_remove = []

    for room_key, rates_info in list(room_results.items()):
        available_main_regimens = rates_info.get(main_key, {})
        available_to_avoid_regimens = rates_info.get(avoid_key, {})

        logging.info("Booking1 avoid worst cases available_main_regimens %s", available_main_regimens)
        logging.info("Booking1 avoid worst cases available_to_avoid_regimens %s", available_to_avoid_regimens)

        regimens_to_remove = []
        for regimen_key, main_price in list(available_main_regimens.items()):

            price_without_promotion = main_price if not type(main_price) is list else sum(main_price)
            final_main_price = price_without_promotion - (promotions.get(room_key, {}).get(main_key, {}).get(regimen_key, {}).get('value', 0))

            if not remove_price_per_day:
                candidate_price = available_to_avoid_regimens.get(regimen_key, max_results_to_avoid_remove_it)
                candidate_price = candidate_price - (promotions.get(room_key, {}).get(avoid_key, {}).get(regimen_key, {}).get('value', 0))
            else:
                #we're trying to delete resultsPerDay
                candidate_price = sum(available_to_avoid_regimens.get(regimen_key, max_results_to_avoid_remove_it))
                candidate_price = candidate_price - (sum(promotions.get(room_key, {}).get(avoid_key, {}).get(regimen_key, {}).get('valuesPerDay', [0])))

            logging.info("Booking1 avoid worst cases -> final_main_price %s VS candidate_price (to avoid) %s", final_main_price, candidate_price)
            if candidate_price >= final_main_price:
                regimens_to_remove.append(regimen_key)

        for regimen_to_remove_key in regimens_to_remove:
            if rates_info.get(avoid_key, {}).get(regimen_to_remove_key):
                elements_to_remove.append((avoid_key, regimen_to_remove_key))
                # del rates_info[avoid_key][regimen_to_remove_key]

        logging.info("boards to remove for room %s: %s", room_key, regimens_to_remove)
        return elements_to_remove


def is_test_booking():
    return (request.values.get("comments") and ("@@@TEST@@@" in request.values.get("comments").upper() or COMMENT_TRICK_TPV_BYPASS in request.values.get("comments"))) or get_config_property_value(advance_configs_names.USE_TEST_RESERVATIONS_ALWAYS)


def _add_prefix_to_identifier(identifier):
    from booking_process.libs.pasarelas.gateway_utils import get_payments_ways
    custom_booking_identifiers_prefix = get_config_property_value(advance_configs_names.CUSTOM_IDENTIFIERS_PREFIX)
    if custom_booking_identifiers_prefix and not (custom_booking_identifiers_prefix in identifier):
        identifier = custom_booking_identifiers_prefix + identifier

    tpv_selected = session_manager.get(TPV_TYPE_PAYMENT_SELECTED)
    tpv_selected_not_indicated = "True" in str(tpv_selected or "")
    pep_link_configured = "PEP_PAYLINKS" in get_payments_ways()
    pep_link_forced = "PEP_PAYLINKS" in (tpv_selected or "")
    add_r_for_payment_links = pep_link_forced or (tpv_selected_not_indicated and pep_link_configured)
    avoid_r = False
    if tpv_selected:
        tpvs = tpv_selected.split(";")
        avoid_r = (len(tpvs) == 1 and any(x in tpvs[0].upper() for x in [SERMEPA, BIZUM])) or SERMEPA in tpvs[0].upper()
    if avoid_r:
        session_manager.set(AVOID_R_IDENTIFIER, True)
    else:
        session_manager.set(AVOID_R_IDENTIFIER, False)
    logging.info(f"avoid_r: {avoid_r}, tpv_selected_not_indicated: {tpv_selected_not_indicated}, pep_link_configured: {pep_link_configured}, pep_link_forced: {pep_link_forced}")
    from_call_center = session_manager.get(AGENT_ID) or session_manager.get("source") == MANAGER_SOURCE_CALL_CENTER
    logging.info("from_call_center: %s AGENT_ID: %s SOURCE: %s", from_call_center, session_manager.get(AGENT_ID), session_manager.get("source"))
    if not avoid_r and not identifier.startswith("R") and from_call_center and add_r_for_payment_links:
        logging.info(f"Changing identifier from {identifier} to {identifier[:-1] + '0'}")
        identifier = "R" + identifier[:-1] + "0"

    return identifier


def build_autofill_popup(booking3_popup_autofill, result_params, request, language):

    #never apply twicce discount for resident!!
    if session_manager.get(BOOKING3_LINK_TPV):
        return {}

    is_mobile = user_agent_is_mobile() or session_manager.get('userAgentIsMobile') if request else False
    args = dict(get_web_dictionary(language))
    args['language'] = language

    if not result_params.get("sid"):
        result_params['sid'] = session_manager.get_session_id()

    args['sid'] = result_params['sid']

    popup_autofill_config = get_config_autofill_booking3(booking3_popup_autofill)

    if popup_autofill_config.get("discount"):
        session_manager.set(DISCOUNT_PRICE_AUTOFILL_POPUP, "")
        discount_percent = float(popup_autofill_config['discount']) / 100
        booking_price = result_params['reservation_summary']['total_price']
        result_params['reservation_summary']['discount_price'] = float(booking_price * (1 - discount_percent))

        #result_params['booking3_popup_autofill_payment'] = buildTemplate("source/templates/mobile/booking_process/popups_info/_autofill_popup_payment.html", result_params, False)
        result_params['booking3_popup_autofill_payment'] = buildTemplate("booking_2/booking_process_v1/personal_details/_autofill_popup_payment.html", result_params, False)
    if popup_autofill_config.get('local_resident'):
        args['local_resident'] = {'discount': popup_autofill_config.get('discount')}

        if popup_autofill_config.get('conditions'):
            args['local_resident']['conditions'] = get_section_from_section_spanish_name(popup_autofill_config.get('conditions'), language)

    new_booking_process_mobile = get_config_property_value(advance_configs_names.NEW_BOOKING_PROCESS_MOBILE)
    if is_mobile and new_booking_process_mobile:
        args['is_new_booking_process_mobile'] = True

    if is_mobile:
        result_params['booking3_popup_autofill'] = build_template_2("mobile/booking_process/popups_info/_autofill_popup.html", args)
    else:
        result_params['booking3_popup_autofill'] = buildTemplate("booking_2/booking_process_v1/popups_info/_autofill_popup.html", args, False)

    return result_params


def general_reservation_modifications(reservation):
    reservation_copy = reservation
    default_language = get_config_property_value(advance_configs_names.DEFAULT_WEB_LANGUAGE)
    if not default_language:
        default_language = SPANISH

    general_dict = get_web_dictionary(default_language)

    pets_number = session_manager.get(TOTAL_PETS)
    if pets_number:
        pets_string = '\n\n%s: %s' % (general_dict['T_mascotas'], pets_number)
        if reservation.comments:
            reservation_copy.comments = reservation.comments + pets_string
        else:
            reservation_copy.comments = pets_string


    return reservation_copy


def is_upgrading_done_for_option_selected(room_index):

    upgrading_services_selected = session_manager.get(VIRTUAL_SERVICES_SELECTED)

    if upgrading_services_selected:
        logging.info("VIRTUAL_SERVICES_SELECTED all for room id %s: %s", room_index, session_manager.get(VIRTUAL_SERVICES_SELECTED))

        selectedOption = session_manager.get(SELECTED_OPTION_KEY)
        selectedPrice = []
        for partialSelected in selectedOption.split(";"):
            selectedPrice.append(session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected))


        selected_rate = selectedPrice[room_index][0]
        selected_room = selectedPrice[room_index][1]
        selected_board = selectedPrice[room_index][2]
        virtual_id_selected= "%s__%s__%s__%s" % (str(room_index+1), selected_room, selected_rate, selected_board)


        logging.info("virtual_id_selected built: %s", virtual_id_selected)

        for virtual_selected in upgrading_services_selected:

            logging.info("comparing built: %s VS selected %s", virtual_id_selected, upgrading_services_selected)

            if virtual_selected.get("virtual_key") == virtual_id_selected:
                logging.info("ID VIRTUAL SUPPLEMENT SELECTED (upgrading) FOUND")
                return True

    return False


def getSelectedPriceFromSession():
    """
    :unit_test: unit_tests.booking_process.utils.booking.test_bookingUtils.TestBookingUtils.test_getSelectedPriceFromSession
    """
    selectedOption = session_manager.get(SELECTED_OPTION_KEY)
    selectedPrice = []
    for partialSelected in selectedOption.split(";"):
        selectedPrice.append(session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected))

    return selectedPrice


def buildPromocodeTooltips(language, section=None):
    if section:
        tooltip_section = section
    else:
        tooltip_section = get_config_property_value(advance_configs_names.PROMOCODE_TOOLTIPS)

    tooltip_html = ""
    if tooltip_section:
        promocode_tooltips = get_pictures_from_section_name(tooltip_section, language)
        valid_tooltips = []
        for tooltip in promocode_tooltips:
            tooltip.update(get_properties_for_entity(tooltip.get('key', False), language))
            excluded_dates = tooltip.get('excluded_dates')
            if not excluded_dates or not check_promocode_tooltip_exclusion(excluded_dates):
                valid_tooltips.append(tooltip)

        args = {
            'tooltip_section': valid_tooltips
        }
        tooltip_html = build_template_2("booking/_promocode_tooltips.html", args, False)

    return tooltip_html

def check_promocode_tooltip_exclusion(excluded_dates):
    today = datetime.today().strftime('%Y-%m-%d')
    today = datetime.strptime(today, '%Y-%m-%d')
    for excluded_date in excluded_dates.split('@'):
        range_dates = excluded_date.split(';')
        start_date = datetime.strptime(range_dates[0], '%Y-%m-%d')
        if len(range_dates) > 1:
            end_date = datetime.strptime(range_dates[1], '%Y-%m-%d')
            if start_date <= today <= end_date:
                return True
        else:
            if start_date == today:
                return True


def need_to_wait_for_content():

    gateway_booking_finished = session_manager.get(BOOKING_FINISHED) or session_manager.get(BOOKING_CONTENT)
    reservation_already_sent_to_manager = session_manager.get(RESERVATION_SENT_TO_MANAGER)
    gateway_process_started = session_manager.get(GATEWAY_PROCESS_STARTED)

    logging.info("Checking a history -1 has been clicked: gateway_booking_finished: %s external_reservation_sent: %s", gateway_booking_finished, reservation_already_sent_to_manager)
    if gateway_booking_finished or reservation_already_sent_to_manager or gateway_process_started:
        #The user has payed, and he has done a history -1 form gateway to booking3
        #we redirect him to booking4
        session_manager.set(AMOUNT_SENT_TO_GATEWAY,"")
        session_manager.set(BOOKING_IN_PROGRESS,"")

        session_manager.set(PAYMENT_GATEWAY_FAILED, "")

        logging.info("USER HISTORY -1 AFTER PAY. Redirecting to booking4 to show confirmation")
        urlForRedirection  = '/booking4'
        urlForRedirection += "?sid=%s" % session_manager.get_session_id()

        if reservation_already_sent_to_manager:
            urlForRedirection += "&need_to_wait=True"

        return urlForRedirection

    return ""

def check_manager_modification(request):
    if request.values.get('modify_from_manager'):
        logging.info("Modification from manager")

        session_manager.set(NO_ASK_CREDIT_CARD, True)
        session_manager.set(MODIFICATION_FROM_MANAGER, True)

        room_price_forced = {}

        hab1_is_price_forced = get_filtered_param_value('hab1_is_price_forced', 0)
        if hab1_is_price_forced:
            room_price_forced["1"] = get_filtered_param_value('hab1_price_forced', 0)

        hab2_is_price_forced = get_filtered_param_value('hab2_is_price_forced', 0)
        if hab2_is_price_forced:
            room_price_forced["2"] = get_filtered_param_value('hab2_price_forced', 0)

        hab3_is_price_forced = get_filtered_param_value('hab3_is_price_forced', 0)
        if hab3_is_price_forced:
            room_price_forced["3"] = get_filtered_param_value('hab3_price_forced', 0)

        if room_price_forced:
            session_manager.set(FORCED_PRICE_FROM_MANAGER, room_price_forced)

    if get_filtered_param_value('services_is_price_forced', 0):
        room_price_forced = get_filtered_param_value('services_price_forced', 0)
        session_manager.set(FORCED_ADDITIONAL_SERVICES_FROM_MANAGER, room_price_forced)


def set_cc_datas_from_modification(localizador_mod, reservation, creditCardDetails):

    if creditCardDetails:
        reservation_modify = directDataProvider.get("Reservation", {"identifier": localizador_mod.strip().upper()}, 1)[0]

        extra_info = reservation_modify.extraInfo
        extra_info = json.loads(extra_info)
        cc_datas = extra_info.get("cc_datas", "")

        if cc_datas:
            password = get_config_property_value(advance_configs_names.CREDIT_CARD_ENCRYPTION)

            message = base64.b64decode(cc_datas)
            decryptedData = encryptionUtils.decryptMessage(message, password)

            creditCardDetails.number = decryptedData.split("@@")[0]
            creditCardDetails.company = decryptedData.split("@@")[1]
            creditCardDetails.expiryDate = decryptedData.split("@@")[2]
            creditCardDetails.cvv = decryptedData.split("@@")[3]

            extra_info = reservation.extraInfo
            extra_info = json.loads(extra_info)
            extra_info['cc_datas'] = cc_datas

            reservation.extraInfo = json.dumps(extra_info)


def clean_duplicated_payments(paylinks_list, threshold_seconds=10):
    logging.info("clean_duplicated_payments original paylinks_list: %s", paylinks_list)
    try:
        date_objects = [datetime.strptime(pay_link.get("timestamp"), "%Y-%m-%d %H:%M:%S") for pay_link in
                        paylinks_list]

        # Filter dates based on time difference
        cleaned_dates = []
        for i in range(len(date_objects) - 1):
            valid = True
            for j in range(i + 1, len(date_objects)):
                time_difference = abs((date_objects[i] - date_objects[j]).total_seconds())
                if time_difference < threshold_seconds:
                    valid = False
                    break
            if valid:
                cleaned_dates.append(paylinks_list[i])

        # Add the last date (no need to compare it with subsequent dates)
        cleaned_dates.append(paylinks_list[-1])

    except Exception as e:
        logging.error(e)
        logging.warning("Exception in clean_duplicated_payments")
        return paylinks_list

    logging.info("clean_duplicated_payments Cleaned paylinks_list: %s", cleaned_dates)
    return cleaned_dates

def _get_total_payed_amount_from_all_sources(extra_info_reservation):

    payed_amount = 0
    try:

        if not extra_info_reservation:
            return payed_amount
        if extra_info_reservation.get("payed", 0):
            payed_amount = float(extra_info_reservation.get("payed", "0"))
        if extra_info_reservation.get("payed_by_cobrador", 0):
            payed_amount += float(extra_info_reservation.get("payed_by_cobrador", "0"))
        if extra_info_reservation.get("payed_by_tpv_link", 0):
            #payed_by_tpv_link = extra_info_reservation.get("payed_by_tpv_link", [])
            payed_by_tpv_link = clean_duplicated_payments(extra_info_reservation["payed_by_tpv_link"])
            for payment in payed_by_tpv_link:
                if not payment.get("datatransdata"):
                    payed_amount += float(payment.get("amount", "0"))
    except Exception as e:
        message = auditUtils.makeTraceback()
        logging.exception(message)
        logging.warning("Exception calculating already paid in reservation")
        return payed_amount

    return payed_amount

def set_payment_link_tpv_in_reservation(reservation, paymentOrderId, amount, encryptedCreditCard=None):
    logging.info("saving link paypent in reservation. Order: %s, amount: %s", paymentOrderId, amount)

    extra_info = reservation.extraInfo
    extra_info = json.loads(extra_info)

    payed_by_tpv_link = extra_info.get("payed_by_tpv_link", [])

    spain_now = datetime.now(timezone('Europe/Madrid'))

    conflict = []

    for i in payed_by_tpv_link:
        if i.get("order") == str(paymentOrderId) and amount == i.get("amount"):
            if session_manager.get(UNIQUE_USAGE_LINK) and session_manager.get(UNIQUE_USAGE_LINK) == i.get("unique_usage_link"):
                conflict.append(True)
                continue

            if session_manager.get(GATEWAY_EXTERNAL_ID) and session_manager.get(GATEWAY_EXTERNAL_ID) == i.get("gateway_external_id"):
                conflict.append(True)
                continue

            old_timestamp = datetime.strptime(i.get("timestamp"), "%Y-%m-%d %H:%M:%S").astimezone(
                timezone('Europe/Madrid'))
            valid_hour = not (spain_now - timedelta(seconds=10) < old_timestamp < spain_now + timedelta(seconds=10))
            logging.info("[PEP_LINKS] Validating date %s < %s < %s = %s" % (spain_now - timedelta(seconds=10), old_timestamp, spain_now + timedelta(seconds=10), not valid_hour))
            if not valid_hour:
                conflict.append(True)
            else:
                conflict.append(False)

    if not any(conflict):

        current_timestamp = spain_now.strftime("%Y-%m-%d %H:%M:%S")
        logging.info("Saving paylink %s %s with timestamp: %s" % (str(paymentOrderId), amount, spain_now))
        order_data = {"order": paymentOrderId,
            "amount": amount,
            "timestamp": current_timestamp
        }

        if encryptedCreditCard:
            order_data["datatransdata"] = encryptedCreditCard
            order_data["amount"] = 0

        if session_manager.get(UNIQUE_USAGE_LINK):
            order_data["unique_usage_link"] = session_manager.get(UNIQUE_USAGE_LINK)

        if session_manager.get(GATEWAY_EXTERNAL_ID):
            order_data["gateway_external_id"] = session_manager.get(GATEWAY_EXTERNAL_ID)

        payed_by_tpv_link.append(order_data)

        if str(paymentOrderId).isnumeric():
            extra_info['last_merchant_order_used'] = str(paymentOrderId)

        extra_info['payed_by_tpv_link'] = payed_by_tpv_link

        # We wnat to be sure, that a paid reservation is confirmed!!
        extra_info["status_reservation"] = "confirmed"

        reservation.extraInfo = json.dumps(extra_info)



def hide_booking_engine(request, templateValues):
    hide_booking = False

    if templateValues.get('currentSearch') and GiftBonoController.promocode_for_gift(templateValues['currentSearch'].get('promoCode')):
        hide_booking = True

    elif GiftBonoController.promocode_for_gift():
        hide_booking = True

    if request.values.get("hide_booking_engine") or session_manager.get(BOOKING3_LINK_TPV):
        hide_booking = True

    extra_params_search = session_manager.get(EXTRA_PARAMS_SEARCH)
    if extra_params_search and extra_params_search.get('free_night'):
        hide_booking = True

    if session_manager.get(HIDE_WIDGET_PROCESS):
        hide_booking = True

    return hide_booking


def merge_item(result_response, result_rpc, item):
    container = result_rpc.get(item)
    if not container:
        container = [{}]

    container.extend(result_response.get(item))
    result_rpc[item] = container


def merge_results(rpc_list):

    rpc_target = rpc_list[0].get('response').content

    # Controlled wrong responses of manager
    if not rpc_target:
        return {'response': rpc_list[0].get('response').content, 'url': rpc_list[0].get("url")}
    
    if not rpc_list[0].get('response').status_code == 200:
        message = 'Manager didnt answer with status code 200'
        notify_exception('[Backend] Manager response status code different that 200', message, add_hotel_info=True)

        raise Exception("[Controlled] Manager response status code is not 200")
    
    rpc_target = json.loads(rpc_target)

    for rpc in rpc_list[1:]:
        rpc_origin = rpc['response'].content
        rpc_origin = json.loads(rpc_origin)

        merge_item(rpc_origin, rpc_target, 'resultsPerDay')
        merge_item(rpc_origin, rpc_target, 'result')
        merge_item(rpc_origin, rpc_target, 'promotions')

        rpc_target['search']['rooms'].extend(rpc_origin['search']['rooms'])

    return {'response': json.dumps(rpc_target), 'url': rpc_list[0].get('url')}


def get_extra_booking3_fields(extra_fields_section: str, language: str) -> list:
    """
    Get extra fields for booking3 personal details form, using pictures from a section name.

    :param extra_fields_section: Section name indicated in advanced configuration - Booking3 extra fields
    :param language: Current search language
    :return: List of extra fields with all needed info filtered by images properties
    """

    search_performed = session_manager.get(SEARCH_KEY_PREFIX)
    extra_fields = get_pictures_from_section_name(extra_fields_section, language)
    is_agency = is_agency_logged()

    filtered_fields = []

    if search_performed:
        has_pets = sum([x.get('numPets', 0) for x in search_performed.get('rooms', [{}])])

    for extra_field in extra_fields:
        extra_field.update(get_properties_for_entity(extra_field.get('key', False), language))

        if extra_field.get('type') == 'select' and extra_field.get('options'):
            extra_field['options'] = extra_field['options'].split('@@')

        if extra_field.get('need_pets') and not has_pets:
            continue

        if extra_field.get('only_agency') and not is_agency:
            continue

        filtered_fields.append(extra_field)

    # Filters
    management_language = get_management_language()
    room_filter_property = "room_name_filter_include"
    if any([extra_element.get(room_filter_property) for extra_element in filtered_fields]):
        rooms_names = ''
        for i in range(get_num_rooms()):
            selected_room_key = getSelectedRoomKey(i+1)
            room_element = get_room(selected_room_key, management_language)
            rooms_names += room_element.get('room_name')

        filter_method = lambda x: not x.get(room_filter_property) or re.search(x.get(room_filter_property), rooms_names, re.IGNORECASE)
        filtered_fields = list(filter(filter_method, filtered_fields))

    select_extra_field_property = 'depends_on_select'
    if any([extra_element.get(select_extra_field_property) for extra_element in filtered_fields]):
        for extra_element in copy.deepcopy(filtered_fields):
            if select_field_name := extra_element.get(select_extra_field_property):
                select_field = next((element for element in filtered_fields if element.get('identifier', '') == select_field_name), None)
                if not select_field:
                    continue

                select_field['extra_input'] = extra_element
                filtered_fields = [field for field in filtered_fields if field.get('title', '') != extra_element.get('title', '')]

    return filtered_fields


def build_message_error_tpv_with_prebooking(language, data, message, templateValues):

    logging.info("Building message error tpv with prebooking...")

    templateValues["link_prebooking"] = data.get('url')

    expiration_timestamp = data.get('expiration_timestamp')
    expiration_timestamp = datetime.strptime(expiration_timestamp, "%Y-%m-%d %H:%M:%S")
    date = expiration_timestamp.strftime('%d/%m/%Y')
    time = expiration_timestamp.strftime('%H:%M')

    templateValues["payment_error_retry_message"] = message

    message = get_web_dictionary(language).get("T_payment_prebooking_tpv")
    message = message.replace('@@@PREBOOKING@@@', str(data.get('id')))
    message = message.replace('@@@DATE@@@', date)
    message = message.replace('@@@TIME@@@', time)

    templateValues["payment_prebooking_tpv_message"] = message


def find_promotion(room_key, rate_key, board_key, promotions, package_key=None):
    if promotions:
        promotion = None
        if promotions.get(room_key):
            promotion = promotions.get(room_key, {}).get(rate_key, {}).get(board_key, None)

        if (not promotion) and package_key:
            rate_fake_key = PACKAGE_PREFIX.replace("_", "") + PACKAGE_SEPARATOR + package_key + PACKAGE_SEPARATOR + rate_key
            promotion = promotions.get(room_key, {}).get(rate_fake_key, {}).get(board_key, None)

        return promotion


def _filter_package_or_rate_only_promotions(current_promotion, package_key, result_structure):

    if not current_promotion:
        return None

    try:
        modified_promotion = None
        if not current_promotion.get('multiple_promotions'):
            return None

        for current_promo in current_promotion.get('multiple_promotions', []):

            #Expected behaviour in integrations such as prestige
            if current_promotion.get('forced_promotion_name') and not current_promo['key']:
                return None
            if current_promotion.get('forced_promotion_name') and "fake" in  current_promo['key'].lower():
                return current_promotion

            current_properties = result_structure.get(current_promo['key'])
            only_packages_ko = not package_key and current_properties and current_properties.get('rate') and 'onlyPackages' in current_properties.get('rate')
            only_rates_ko = package_key and current_properties and current_properties.get('rate') and 'onlyRates' in current_properties.get('rate')
            if only_packages_ko or only_rates_ko:
                if not modified_promotion:
                    modified_promotion = copy.deepcopy(current_promotion)

                modified_promotion['valuesPerDay'] = [x - y for x, y in zip(modified_promotion['valuesPerDay'], current_promo['price'])]
                modified_promotion['value'] = modified_promotion['value'] - sum(current_promo['price'])
                modified_promotion['multiple_promotions'].remove(current_promo)

        if modified_promotion:
            if current_promotion['multiple_promotions']:
                return modified_promotion
            else:
                return None
        else:
            return current_promotion
    except Exception as e:
        logging.error("Exception while trying to _filter_package_or_rate_only_promotions")
        message = auditUtils.makeTraceback()
        logging.error(e)
        logging.error(message)
        return current_promotion


def get_rooms_price_summary_data(selected_price, myParams, language, one_room_limit=True):
    if len(selected_price) > 1 or not one_room_limit:
        rooms_summary = []

        for room_element in selected_price:
            room_found = getRoom(room_element[1], language)
            rate_key = room_element[0]
            room_price = float(room_element[3])
            if package_utils.is_package(rate_key) and get_config_property_value(SHOW_SUMMARY_PACKAGE_PRICE):
                package_key, rate_key = package_utils.get_package_and_rate_from_key(rate_key)
                package_info = package_utils.get_package_full_info(package_key, language)
                room_price = room_price - sum(package_utils.get_package_total_price_separated(package_info).values())

            room_name = ""
            if room_found.get("roomName"):
                room_name = room_found['roomName'][0].value

            room_structure = {
                'name': room_name,
                'price': float("%.2f" % room_price),
                'offer': ''
            }

            if len(room_element) > 4:
                room_structure['offer'] = room_element[4]

            rooms_summary.append(room_structure)

        search = session_manager.get(SEARCH_KEY_PREFIX)
        for i, my_room in enumerate(search.get('rooms', [])):

            rooms_summary[i]['num_adults'] = my_room['numAdults']
            rooms_summary[i]['num_kids'] = my_room['numKids']
            rooms_summary[i]['num_babies'] = my_room['numBabies']

        myParams['rooms_price_summary'] = rooms_summary


def still_available_search(real_check=False, is_retry=False):

    try:
        my_request = session_manager.get(PARAMS_SEARCH_STRING)
        if my_request and 'ignore_room_availability' in my_request:
            logging.warning("Bypassing double availability check, because param ignore_room_availability was sent in request")
            return True

        my_request = fix_namespace_for_double_check(my_request)
        logging.info("Double check activated. Current search in session: %s", my_request)

        if not my_request:
            return True

        rooms_searched = get_num_rooms()
        if rooms_searched > 3:
            search_results = session_manager.get(SEARCH_KEY_PREFIX)
            double_check_result = []
            for room_element in search_results.get('rooms'):
                search_dict = {
                    'startDate': change_string_date_format(search_results.get('startDate'), '%d/%m/%Y'),
                    'endDate': change_string_date_format(search_results.get('endDate'), '%d/%m/%Y'),
                    'promocode': search_results.get('promocode') or search_results.get('promoCode'),
                    'adultsRoom1': str(room_element.get('numAdults')),
                    'childrenRoom1': str(room_element.get('numKids')),
                    'babiesRoom1': str(room_element.get('numBabies'))
                }
                target_string = hotelManagerCommunicator.buildSearchString(search_dict, search_results.get('countryCode'))
                target_string = fix_namespace_for_double_check(target_string)

                double_check_result.append(hotelManagerCommunicator.perform_search_from_string(target_string))
        else:
            double_check_result = [hotelManagerCommunicator.perform_search_from_string(my_request)]

        selectedOption = session_manager.get(SELECTED_OPTION_KEY)

        is_shopping_cart = is_enabled_shopping_cart()
        cart_options_availability = {}

        #Note that we might have selected multiple rooms, that is why we might have more selected option
        for room_index, partialSelected in enumerate(filter(lambda x: x, selectedOption.split(";"))):
            selectedPrice = session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected)


            #check availability for each room
            rate_selected =  selectedPrice[0]
            room_selected =  selectedPrice[1]
            board_selected =  selectedPrice[2]

            if cart_options_availability.get(room_selected):
                cart_options_availability[room_selected] += 1

            else:
                cart_options_availability[room_selected] = 1

            room_still_available = False
            for results_element in double_check_result:
                results = results_element.get("result", {})

                index_to_search = room_index
                if is_shopping_cart:
                    index_to_search = 0

                    if get_config_property_value(FIXING_SHOPPING_CART_WITHOUT_MERGING):
                        index_to_search = room_index

                    if cart_options_availability[room_selected] > results_element.get("extra", {}).get("availability", {}).get(room_selected, 0):
                        continue

                is_still_available = results[index_to_search].get(room_selected, {}).get(rate_selected, {}).get(board_selected, {})

                if isinstance(is_still_available, (int, float)) and is_still_available == 0 and results_element.get("search", {}).get("promoCode", ""):
                    is_still_available = True

                if not is_still_available and  len(results) > room_index:
                    is_still_available = results[room_index].get(room_selected, {}).get(rate_selected, {}).get(board_selected, {})

                if is_still_available:
                    room_still_available = True



            if room_still_available:
                logging.info("ROOM %s still_available_search for selected option room %s , rate %s, board %s . Results still found: %s", room_index, room_selected, rate_selected, board_selected, is_still_available)
            else:
                logging.warning('Double check availability: Not available for:')
                logging.warning('Room selected: %s', room_selected)
                logging.warning('Rate selected: %s', rate_selected)
                logging.warning('Board selected: %s', board_selected)
                return False

        return True

    except Exception as e:
        if real_check and not is_retry:
            return None
        
        logging.error("Exception performing Double check availability. We are optimistic  and we let make reservation. ")
        message = auditUtils.makeTraceback()
        logging.critical(message)
        message = "Exception performing Double check availability. We are optimistic  and we let make reservation. At %s (booking4): %s" % (get_application_id(), str(e))
        notify_exception("[Backend] Alerta booking4: Double check availability",  message)

        return True


def still_available_search_special(special_params):
    try:
        my_request = special_params.get('my_request')
        my_request = fix_namespace_for_double_check(my_request)
        logging.info("Double check activated. Current search from PendingReservation: %s", my_request)

        if not my_request:
            return True

        rooms_searched = special_params.get('rooms_searched')
        if rooms_searched > 3:
            double_check_result = []
            rooms = special_params['rooms']
            search = special_params.get('search')
            search_dict = {
                'startDate': search['startDate'],
                'endDate': search['endDate'],
                'promocode': search['promoCode'],
                'adultsRoom1': rooms['numAdults'],
                'childrenRoom1': rooms['numKids'],
                'babiesRoom1': rooms['numBabies']
            }
            target_string = hotelManagerCommunicator.buildSearchString(search_dict, search.get('country_code'))
            target_string = fix_namespace_for_double_check(target_string)
            double_check_result.append(hotelManagerCommunicator.perform_search_from_string(target_string))

        else:
            double_check_result = [hotelManagerCommunicator.perform_search_from_string(my_request)]

        selected_options = special_params.get('selected_options')

        result = double_check_result[0].get('result')
        index = 0
        available = False
        for selected_option in selected_options:
            if index == rooms_searched:
                break
            rate = selected_option[0]
            room = selected_option[1]
            board = selected_option[2]
            try:
                available = result[0].get(room).get(rate).get(board)
            except:
                return False
            if not available:
                return False
            index += 1
        return available

    except:
        return False

def check_search_still_available_pending_reservation(hotel_code, response_cobrador):
    logging.info(f"check_search_still_available_pending_reservation: {response_cobrador} hotel_code: {hotel_code}")
    if response_cobrador.get(CODE).upper() != 'OK':
        return False
    identifier = response_cobrador.get(IDENTIFIER) or response_cobrador.get(GATEWAY_ORDER_ID)
    set_namespace(hotel_code)
    pending_reservation = directDataProvider.get("PendingReservation", {"identifier": identifier}, 1)
    if not pending_reservation:
        logging.warning(f"No PendingReservation found in {hotel_code} with identifier {identifier}")
        return False
    reservation = directDataProvider.get("Reservation", {"identifier": identifier}, 1)
    if reservation:
        logging.warning(f"Reservation already exists in {hotel_code} with identifier {identifier}")
        return 'existing_reservation'
    latest_pending_reservation = max(pending_reservation, key=lambda x: x.timestamp)
    try:
        # personal_details = json.loads(decrypt_data(latest_pending_reservation.personal_details))
        reservation_data = json.loads(decrypt_data(latest_pending_reservation.reservation_data))

    except:
        # personal_details = json.loads(latest_pending_reservation.personal_details)
        reservation_data = json.loads(latest_pending_reservation.reservation_data)

    my_request = get_search_url_from_pending_reservation(reservation_data, hotel_code)
    logging.info(f'calculated "my_request" from pending reservation to do still_available_search: {my_request}')
    num_rooms = len(reservation_data.get('selected_options'))
    search = reservation_data.get('search')
    special_params = {
        'my_request': my_request,
        'rooms_searched': num_rooms,
        'search': search,
        'rooms': search.get('rooms'),
        'selected_options': reservation_data.get('selected_options')
        }
    logging.info(f"special_params to process still_available_search_special: {special_params}")
    if still_available_search_special(special_params):
        logging.info(f"search is still available")
        return confirm_pending_reservation(hotel_code, response_cobrador)
    else:
        return 'KO'

def confirm_pending_reservation(hotel_code, response_cobrador):
    payload_to_send = {
        "response_cobrador": response_cobrador,
        "hotel_code": hotel_code,
    }
    headers = {
        "Content-Type": "application/json"
    }

    url_base = ADMIN_MANAGER_SERVER_2
    if DEV and DEV_USE_LOCAL_HOTEL_MANAGER_2:
        url_base = "http://localhost:8070"

    url_to_post = f"{url_base}/reservations/create/create_reservation_post"
    response_creation = requests.post(url_to_post, data=json.dumps(payload_to_send), headers=headers)
    return response_creation.text


def get_search_url_from_pending_reservation(reservation_data, hotel_code):
    """Function to build search URL from pending reservation data"""
    # Get pending reservation

    logging.info(f'Trying to build search url from reservation metadata')

    # Extract search data from reservation
    search_data = reservation_data['search']

    # Convert dates to desired format (DD/MM/YYYY)
    start_date = search_data['startDate']
    end_date = search_data['endDate']

    # Handle different date formats and convert to DD/MM/YYYY
    if '-' in start_date:
        # Convert from YYYY-MM-DD to DD/MM/YYYY
        start_parts = start_date.split('-')
        end_parts = end_date.split('-')
        start_date = f"{start_parts[2]}/{start_parts[1]}/{start_parts[0]}"
        end_date = f"{end_parts[2]}/{end_parts[1]}/{end_parts[0]}"
    elif '/' in start_date:
        # Convert from YYYY/MM/DD to DD/MM/YYYY
        start_parts = start_date.split('/')
        end_parts = end_date.split('/')
        if len(start_parts[0]) == 4:  # If starts with year
            start_date = f"{start_parts[2]}/{start_parts[1]}/{start_parts[0]}"
            end_date = f"{end_parts[2]}/{end_parts[1]}/{end_parts[0]}"

    # Build URL parameters
    params = {
        'applicationId': hotel_code,
        'countryCode': search_data['countryCode'],
        'startDate': start_date,
        'endDate': end_date,
        'numRooms': len(search_data['rooms']),
        'source': search_data.get('device', ""),
        'device': search_data.get('device', ""),
        'language': 'es',
        'referrer': 'hotel-webs-v3'
    }

    # Add promocode only if it exists
    if 'promoCode' in search_data and search_data['promoCode']:
        params['promocode'] = search_data['promoCode']

    # Add room parameters for each room in search data
    for i, room in enumerate(search_data['rooms'], 1):
        params[f'adultsRoom{i}'] = room['numAdults']
        params[f'childrenRoom{i}'] = room['numKids']
        params[f'babiesRoom{i}'] = room['numBabies']

    # Add empty values for remaining rooms up to room 3
    for i in range(len(search_data['rooms']) + 1, 4):
        params[f'adultsRoom{i}'] = 0
        params[f'childrenRoom{i}'] = 0
        params[f'babiesRoom{i}'] = 0

    # Build final URL with URL-encoded parameters
    url = '/search/?' + '&'.join(f'{k}={v}' for k, v in params.items())
    logging.info(f'search url built: {url}')
    return url


def fix_namespace_for_double_check(search_string):
    try:
        real_namespace = get_hotel_code()
        parsed_url = urllib.parse.urlparse(search_string)
        query_params = urllib.parse.parse_qs(parsed_url.query)

        if query_params.get("applicationId"):
            query_params['applicationId'] = [real_namespace]

        # Rebuild the query string with the updated parameters
        new_query_string = urllib.parse.urlencode(query_params, doseq=True)
        # Rebuild the URL with the updated query string
        new_url_string = urllib.parse.urlunparse((parsed_url.scheme, parsed_url.netloc, parsed_url.path, parsed_url.params,
                                              new_query_string, parsed_url.fragment))

        return new_url_string
    except Exception as e:
        traceback = auditUtils.makeTraceback()
        logging.error("Error in fix_namespace_for_double_check")
        logging.error(traceback)
        return search_string

def prepare_search_info_from_reservation(reservation):
    extra_data = json.loads(reservation.extraInfo)
    price_per_day_rooms = extra_data.get('prices_per_day', {})
    current_active_room_index_list = []
    for room in price_per_day_rooms.keys():
        room_index = room.split(':')[0].strip()
        current_active_room_index_list.append(int(room_index))
    current_active_room_index_list.sort()

    search_dictionary = {
        'startDate': reservation.startDate,
        'endDate': reservation.endDate,
        'rooms': []
    }

    rooms_list = []
    if current_active_room_index_list and len(current_active_room_index_list) == reservation.numRooms:
        for room_index in current_active_room_index_list:
            room_info = get_room_occupancy_dict(reservation, room_index)
            if get_config_property_value(advance_configs_names.BOOKING_CONFIRMATION_SHOW_ORIGINAL_INDEX_ROOMS):
                room_info['original_room_index'] = room_index
            rooms_list.append(room_info)

        search_dictionary['rooms'] = rooms_list

    else:
        for i in range(0, reservation.numRooms):
            search_dictionary['rooms'].append(get_room_occupancy_dict(reservation, i+1))

    shopping_cart = extra_data.get('shopping_cart', [])
    if shopping_cart:
        search_dictionary['rooms'] = []
        for room in shopping_cart:
            search_dictionary['rooms'].append(
                {
                    'numAdults': room['adults'],
                    'numKids': room['kids'],
                    'numBabies': room['babies']
                }
            )

    ages = extra_data.get("agesByRoom", {})
    if ages:
        logging.info("extraInfo ages: %s", ages)
        for key_name, key_value in ages.items():
            room_index = key_name.replace('kidsAges', '')
            search_dictionary['agesKid' + room_index] = key_value.get('kids', [])
            search_dictionary['agesBaby' + room_index] = key_value.get('babies', [])

    logging.info("search dictionary agesKid1: %s", search_dictionary.get("agesKid1", "whithout ages kid1"))
    logging.info("search dictionary agesbabies1: %s", search_dictionary.get("agesBaby1", "whithout ages baby1"))
    return search_dictionary


def prepare_selected_price_from_reservation(search, reservation):
    if reservation.extraInfo:
        try:
            extra_info = json.loads(reservation.extraInfo)
        except Exception as e:
            logging.error("Error decoding json in sendReservation: %s" % str(e))
            extra_info = {}

        extra_info_reservation_cart = extra_info.get("shopping_cart")

        if extra_info_reservation_cart:
            rooms_number = len(extra_info_reservation_cart)

    selected_price = []
    rooms_number = reservation.numRooms
    uuid_simulator = [str(uid) for uid in range(1, rooms_number + 1)]
    for uuid in uuid_simulator:

        if uuid == '1':
            roomTypeSelected = reservation.roomType1
        if uuid == '2':
            roomTypeSelected = reservation.roomType2
        if uuid == '3':
            roomTypeSelected = reservation.roomType3

        rate_selected = reservation.rate
        board_selected = reservation.regimen
        price_selected = float(reservation.price) / float(reservation.numRooms)

        if extra_info_reservation_cart:
            rate_selected = extra_info_reservation_cart[int(uuid) - 1].get("rate_key")
            board_selected = extra_info_reservation_cart[int(uuid) - 1].get("regimen_key")
            roomTypeSelected = extra_info_reservation_cart[int(uuid) - 1].get("room_key")
            price_selected = float(extra_info_reservation_cart[int(uuid) - 1].get("price"))

        if get_config_property_value(ROUND_DECIMAL_BOOKING):
            price_selected = float(reservation.price) + float(reservation.priceSupplements)

        price_prefix_list = [rate_selected, roomTypeSelected, board_selected, price_selected]
        if extra_info_reservation_cart and extra_info_reservation_cart[int(uuid) - 1].get("promotion_key") and \
                extra_info_reservation_cart[int(uuid) - 1]['promotion_key'] != 'fake_rescue_promotion':
            offer_information = directDataProvider.getEntity('Promotion', extra_info_reservation_cart[int(uuid) - 1][
                'promotion_key'])
            if offer_information:
                # self.promotion_object_list.append(offer_information)
                price_prefix_list.append(offer_information.name)
                price_prefix_list.append(extra_info_reservation_cart[int(uuid) - 1]['promotion_key'])

        if reservation.promotions and 'fake' not in reservation.promotions.lower() and not extra_info_reservation_cart:
            #just take first promotion to fake the selected prices
            promotion_key = reservation.promotions.split(";")[0]
            offer_information = directDataProvider.getEntity('Promotion', promotion_key)
            if offer_information:
                # self.promotion_object_list.append(offer_information)
                price_prefix_list.append(offer_information.name)
                price_prefix_list.append(reservation.promotions)

        session_manager.set(PRICE_OPTION_KEY_PREFIX + uuid, price_prefix_list)
        selected_price.append(price_prefix_list)

    session_manager.set(RESENDING_CONFIRMATION, True)
    session_manager.set(SELECTED_OPTION_KEY, ';'.join(uuid_simulator))

    session_manager.set(SEARCH_KEY_PREFIX, search)
    session_manager.set(CURRENT_SEARCH, {})
    session_manager.set(PERSONAL_DETAILS, reservation)
    session_manager.set(COUNTRY, reservation.geolocation)
    session_manager.set('avoid_session_for_rate_description', True)
    
    if extra_info.get("currency"):
        session_manager.set(CURRENCY_BY_RATE, extra_info.get("currency"))
        session_manager.set(FROM_MY_BOOKING, extra_info.get("currency"))

    logging.info("prepare_selected_price_from_reservation: %s", selected_price)
    return selected_price


def set_prices_per_day(result):
    if not result:
        return

    rooms_list = result
    if rooms_list and type(rooms_list[0]) is list:
        rooms_list = [room for sublist in rooms_list for room in sublist]

    for room in rooms_list:
        for rate in room.get('rateResult'):
            for board in rate.get('regimenResult'):
                current_uuid = str(board.get('roomResults').get('uuid'))
                selected_price_v2 = session_manager.get(PRICE_OPTION_KEY_PREFIX_V2 + current_uuid)

                search = session_manager.get(SEARCH_KEY_PREFIX)
                start_date = search.get('startDate')
                start_date = datetime.strptime(start_date, '%Y-%m-%d')

                prices_per_day = {}
                for index, price in enumerate(selected_price_v2.get('pricesPerDay')):
                    current_price = price

                    promotion = selected_price_v2.get('promotion', {})
                    if promotion:
                        promotion_price = promotion.get('valuesPerDay', [])[index]
                        current_price = current_price - promotion_price

                    prices_per_day[start_date.strftime('%d/%m/%Y')] = str(current_price)

                    start_date = start_date + timedelta(days=1)

                board['prices_per_day'] = json.dumps(prices_per_day)




def get_all_agent():
    AUTHENTICATION = 'Basic ZGV2OnJ1bGVz'
    target_endpoint = "https://europe-west1-build-tools-2.cloudfunctions.net/get_all_from_entity2?hotel_code=admin-hotel&entity_name=ParatyUser&max_age=3600"
    result = requests.get(target_endpoint, headers={'Authorization': AUTHENTICATION})
    if result.status_code == 200:
        content = result.content
        if result.headers.get('Content-Type') == 'application/zip':
            content = zipfile.ZipFile(io.BytesIO(result.content)).open('ParatyUser.json').read()
        full_hotel_list = json.loads(content)

        return full_hotel_list
    else:
        return ""


def get_agent_ring2travel(agent):
    all_agent = get_all_agent()

    for user in all_agent:
        if user.get("name") == agent:
            return  [True for x in user.get("configurationMap") if x.split(" @@ ")[0] == "Ring2travel" and x.split(" @@ ")[1].lower() == "true"]


def identifier_checker(original_id, new_id=None, num_try=0):
    """
    :unit_test: unit_tests.booking_process.utils.booking.test_bookingUtils.TestBookingUtils.test_identifier_checker
    """
    booking_id = original_id if num_try == 0 else new_id
    try:
        if num_try < 3 and not session_manager.get(LOCATION_MODIFICATION) and not session_manager.get(BOOKING3_LINK_TPV):
            logging.info("identifier_checker try: %s, checking: %s" % (str(num_try + 1), booking_id))
            url_to_post = f"{IDENTIFIER_CHECKER_URL % booking_id}&hotel_code={get_namespace()}"
            result = requests.post(url_to_post, timeout=2)
            if json.loads(result.text).get('result') == 'used':
                if num_try == 0:
                    new_id = original_id + "-1"
                else:
                    new_id = new_id[:-1] + str(int(new_id[-1]) + 1)
                return identifier_checker(original_id, new_id, num_try + 1)
            else:
                if num_try == 0:
                    logging.info("Original identifier %s was not used" % booking_id)
                else:
                    logging.info("Unique bookingId found: %s is the new bookingId because %s was already used" % (
                        booking_id, original_id))
                return booking_id
        else:
            if num_try > 3:
                logging.warning("Maximum number of tries reached (3). Returning original bookingId: %s" % original_id)
            else:
                logging.info("identifier_checker skipped")
            return original_id
    except Exception as e:
        logging.warning("There was an error checking if %s already existed. Returning original bookingId: %s. %s" % (
            booking_id, original_id, e))
        return original_id


def credit_card_by_token_by_countries():
    special_token_by_country = get_config_property_value(GATEWAY_SPECIAL_COUNTRIES_BY_TOKEN)
    if special_token_by_country and session_manager.get('country'):
        if "ALL-LESS" in special_token_by_country:
            real_countries_codes = special_token_by_country.replace("ALL-LESS", "").lower()
            return session_manager.get('country').lower() not in real_countries_codes
        return session_manager.get('country').lower() in special_token_by_country.lower()
    return False


def send_multibanco_payment_email(reservation, contentText, contentHtml, language='SPANISH'):

    clientEmail = reservation.email

    # Let's use the user's language for the cancellation also
    logging.info("language multibanco email IS %s", reservation.language)
    if reservation.language:
        language = reservation.language
        logging.info("forcing language multibanco email to %s", language)

    languageDict = get_web_dictionary(language)

    subject = languageDict.get("T_reservation_payment_data")
    emailTitle = f"{subject} {str(reservation.identifier)}"

    emailTitle = _add_general_tags_to_email_subject(reservation, emailTitle, language, to_manager=False) or emailTitle
    try:
        send_email(clientEmail, emailTitle, contentText, contentHtml)
    except Exception as e:
        logging.critical("Unexpected exception during email sending: " + str(e))
        message = "Exception at %s (Multibanco payment): %s" % (
            namespace_utils.get_application_id(), str(e))
        notify_exception(
            "Exception (Multibanco payment). Will defer it and send too by appengine to paraty team)",
            message, add_hotel_info=True)

        defer(send_email_no_retries, clientEmail, emailTitle, contentText, contentHtml, _countdown=60)
        # sending the confirmation to paraty team by appengine
        send_email_gae(PARATY_TEAM_EMAIL, emailTitle, contentText, contentHtml)



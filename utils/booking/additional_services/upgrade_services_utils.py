import copy
import logging

from flask import request

from booking_process.constants.advance_configs_names import UPGRADES_PRICES_BY_OCCUPANCY_NIGHTS, \
    UPGRADES_VIEW_MORE_POPUP, UPGRADE_POPUP_INFO, ROUND_DECIMAL_BOOKING
from booking_process.constants.advance_configs_names import BOOKING2_UPGRADE_IMAGE, TRUNCATE_DECIMAL_BOOKING, \
    SMART_SHOPPING_CART, BOOKING1_SEE_MORE_VERSION, UPGRADES_SHOW_ONLY_TARGET_NAME, SHARED_AVAILABILITY_BY_NAME, \
    SHOPPING_CART_LIMIT_ONE_ROOM
from booking_process.constants.integrationsConstants import UPGRADING_CONFIGURATION_ROOM, UPGRADING_CONFIGURATION_BOARD
from booking_process.utils.auditing import auditUtils
from booking_process.utils.booking.additional_services.additional_services_utils import is_booking2_react_active
from booking_process.utils.booking.additional_services.constants import FULL_STAY_DURATION, UPGRADE_ROOM, UPGRADE_BOARD, \
    PER_PERSON_TYPE, PER_ROOM_TYPE
from booking_process.utils.booking.bookingUtils import _get_room_price_from_reservation, _getRegimensNames, \
    getSelectedPriceFromSession
from booking_process.utils.booking.booking_datastore_utils import buildResultStructures
from booking_process.utils.booking.common_data_provider import get_rate
from booking_process.utils.booking.selections.selection_utils import getSelectedRegimen, getSelectedRoom
from booking_process.constants.session_data import APPLIED_B3_UPGRADE, SELECTED_OPTION_KEY, PRICE_OPTION_KEY_PREFIX, \
    MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX, \
    UPGRADING_TEXT_ROOM, IS_REAL_UPGRADING_PROCESS, UPGRADING_INCREMENT_ROOM_PRICE, UPGRADING_INCREMENT_ROOM_MADE, \
    SEARCH_KEY_PREFIX, CURRENT_SEARCH, SHOPPING_CART_SELECTED
from booking_process.utils.bookingConstants import UPGRADE_SEPARATOR
from booking_process.utils.data_management.boards_data import get_board
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.content_utils import unescape
from booking_process.utils.data_management.integrations_utils import get_integration_configuration
from booking_process.utils.data_management.pictures_utils import getPicturesForKey
from booking_process.utils.data_management.rooms_data import _getRoomsNames, get_room
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.dates.dates_management import get_nights
from booking_process.utils.email.email_utils_third_party import notify_exception
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.namespaces.namespace_utils import get_namespace, get_application_id, get_hotel_code
from booking_process.utils.paritymaker.paritymaker_utils import normalizeBoardName
from booking_process.utils.session import session_manager
from booking_process.utils.shopping_cart.booking_cart import is_enabled_shopping_cart
from booking_process.utils.templates.template_utils import build_template_2
from models import integrations
from paraty_commons_3.datastore import datastore_utils
from paraty_commons_3.datastore.datastore_utils import get_location_prefix


def perform_upgrade_on_booking3():
    logging.info("Performing upgrade from booking3")
    session_manager.set(APPLIED_B3_UPGRADE, True)


def filter_additional_services_upgrade(additional_services, language, num_rooms):
    """Separate upgrade services and additional services"""

    for service_element in additional_services:
        services_pictures = getPicturesForKey(SPANISH, service_element.get('key'), None)
        for picture_element in services_pictures:
            picture_properties = get_properties_for_entity(picture_element.get('key'), SPANISH)
            if picture_properties.get('regimen_upgrade'):
                service_element['regimen_upgrade'] = picture_properties['regimen_upgrade']
                # Force the complete stay
                service_element['duration'] = FULL_STAY_DURATION
            if picture_properties.get('room_upgrade'):
                service_element['room_upgrade'] = picture_properties['room_upgrade']
                # Force the complete stay
                service_element['duration'] = FULL_STAY_DURATION
            if picture_properties.get('custom_bg_color'):
                service_element['custom_bg_color'] = picture_properties.get('custom_bg_color')
            if picture_properties.get('custom_title_color'):
                service_element['custom_title_color'] = picture_properties.get('custom_title_color')

    upgrade_regimen_services = list(filter(lambda x: x.get('regimen_upgrade'), additional_services))

    upgrade_room_services = []
    if num_rooms == 1:
        # upgrade_room_services only available if ONE ROOM!
        upgrade_room_services = list(filter(lambda x: x.get('room_upgrade'), additional_services))

    additional_services = list(filter(lambda x: (not x.get('regimen_upgrade')) and (not x.get('room_upgrade')), additional_services))

    if session_manager.get(SELECTED_OPTION_KEY):
        upgrade_regimen_services = _upgrade_service_satisfied_regimen(upgrade_regimen_services)
        upgrade_room_services = _upgrade_service_satisfied_room(upgrade_room_services)
        upgrade_services = upgrade_regimen_services + upgrade_room_services
    else:
        upgrade_services = []

    return additional_services, upgrade_services


def _upgrade_service_satisfied_regimen(additional_services):
    """
    Check if regimen has upgrade | Normalization dict has been taked of paritymaker.__init__
    """

    if additional_services:
        normalization_dict = {
            'Solo Alojamiento': 'SA',
            'Alojamiento y Desayuno': 'AD',
            'Media Pension': 'MP',
            'Pension Completa': 'PC',
            'Todo Incluido': 'TI',
            'Unknown': 'UN'
        }

        selectedOption = session_manager.get(SELECTED_OPTION_KEY)
        selectedPrice = []
        for partialSelected in selectedOption.split(";"):
            selectedPrice.append(session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected))

        selected_regimen = getSelectedRegimen(selectedPrice[0], SPANISH)
        normalized_regimen = normalizeBoardName(selected_regimen)
        short_regimen = normalization_dict.get(normalized_regimen)

        logging.info("filtering board short_regimen: %s, in additional_services: %s", short_regimen, additional_services)

        if short_regimen:
            additional_services = [service for service in additional_services if service.get('regimen_upgrade', '') and short_regimen in service.get('regimen_upgrade', '')]

    return additional_services


def _upgrade_service_satisfied_room(additional_services):
    """
    Check if room has upgrade
    """

    if additional_services:

        selectedOption = session_manager.get(SELECTED_OPTION_KEY)

        selectedPrice = []
        for partialSelected in selectedOption.split(";"):
            selectedPrice.append(session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected))

        selected_room = getSelectedRoom(selectedPrice, SPANISH)

        additional_services = [service for service in additional_services if selected_room.lower().strip() == service.get('room_upgrade', '').lower().strip()]

    return additional_services


def build_upgrading_services_from_results(language):
    """
    1. check if exists xml-config for upgrading
    2. check if there is a map for the room selected
    3. check if there is a result from booking for the upgrading room found in step 2
    4. check posible discount
    5. build a additional service structure with the difference of prices and apply possible discount
    """

    services_upgrading_result = []

    logging.info("Searching rooms and boards for upgrading that satisfied conditions")

    upgrading_room_configuration = get_integration_configuration(UPGRADING_CONFIGURATION_ROOM)
    upgrading_boards_configuration = get_integration_configuration(UPGRADING_CONFIGURATION_BOARD)

    if upgrading_room_configuration or upgrading_boards_configuration:

        selectedOption = session_manager.get(SELECTED_OPTION_KEY)
        result_structure = buildResultStructures(language)

        if upgrading_room_configuration:
            room_upgrades = _build_upgrading_room_services_from_results(upgrading_room_configuration, selectedOption, result_structure, language)
            room_upgrades = _filter_upgrades_based_on_room_availability(room_upgrades)
            services_upgrading_result += room_upgrades

        if upgrading_boards_configuration:
            services_upgrading_result += _build_upgrading_board_services_from_results(upgrading_boards_configuration, selectedOption, result_structure, language)

    return services_upgrading_result


def _filter_upgrades_based_on_room_availability(upgrades):
    filtered_upgrades = []
    rooms_availability = copy.deepcopy((session_manager.get(CURRENT_SEARCH) or {}).get('rooms_key_availability', {}))
    process_room_key = lambda x: x

    if get_config_property_value(SHARED_AVAILABILITY_BY_NAME):
        # Reformat the rooms availability to be by room name instead of room key
        rooms_availability_by_name = {}
        process_room_key = lambda x: get_room(x, SPANISH).get('room_name').split("(")[0].strip()
        for room_key, availability in rooms_availability.items():
            room_name = process_room_key(room_key)
            if room_name:
                if availability < rooms_availability_by_name.get(room_name, float('inf')):
                    # In case of multiple availabilities for the same room for some reason, we take the lowest one
                    rooms_availability_by_name[room_name] = availability

        rooms_availability = rooms_availability_by_name

    if rooms_availability:
        # Substract the availability of rooms that are already selected
        selected_prices = getSelectedPriceFromSession()
        for selected_price in selected_prices:
            room_key = process_room_key(selected_price[1])
            if room_key in rooms_availability:
                rooms_availability[room_key] -= 1

        for upgrade in upgrades:
            room_key =  process_room_key(upgrade.get('key').split(UPGRADE_SEPARATOR)[1])
            if room_key in rooms_availability:
                if rooms_availability[room_key] > 0:
                    filtered_upgrades.append(upgrade)
                    rooms_availability[room_key] -= 1
                else:
                    logging.info(f"Skipping upgrade {upgrade.get('name')} because there is no availability for the room {room_key}")
            else:
                logging.warning(f"Skipping upgrade {upgrade.get('name')} because there is no availability information for the room {room_key}")


    else:
        logging.warning("Skipping availability check for upgrades because rooms availability is not available in session")
        filtered_upgrades = upgrades


    return filtered_upgrades

def _build_upgrading_room_services_from_results(upgrading_room_configuration, selectedOption, result_structure, language):
    # build virtual additional upgrading service that for each room searched (multiple room search)

    room_upgrading_result = []
    room_index = 1
    for original_partial_selected in selectedOption.split(";"):
        list_of_room_keys_for_upgrading = []

        selected_price = session_manager.get(PRICE_OPTION_KEY_PREFIX + original_partial_selected)

        room_key = selected_price[1]
        rate_key = selected_price[0]
        board_key = selected_price[2]
        logging.info("searching map upgrading for room: %s", room_key)
        logging.info("searching map upgrading for board: %s", board_key)

        for key_value in upgrading_room_configuration['roomMap'].items():
            if key_value[0] == room_key:

                available_room_ids_for_upgrading = key_value[1]
                list_of_room_ids_for_upgrading = available_room_ids_for_upgrading.split(";")

                for room_id_for_upgrading in list_of_room_ids_for_upgrading:

                    if not room_id_for_upgrading.isdigit():
                        room_key_for_upgrading = room_id_for_upgrading

                    else:
                        room_id_for_upgrading_converted = int(room_id_for_upgrading)
                        room_key_for_upgrading = datastore_utils.entity_id_to_alphanumeric_key(room_id_for_upgrading_converted, "RoomType", get_hotel_code())

                    logging.info("converting roomType id to key: %s -> %s", room_id_for_upgrading, room_key_for_upgrading)

                    if room_key_for_upgrading not in list_of_room_keys_for_upgrading:
                        logging.info("adding %s to list_of_room_keys_for_upgrading", room_key_for_upgrading)
                        list_of_room_keys_for_upgrading.append(room_key_for_upgrading)

        satisfied_board_restriction = True

        for key_value in upgrading_room_configuration['boardMap'].items():

            # if there are boardMap configured, not all boards are valid
            satisfied_board_restriction = False
            if key_value[0] == board_key and key_value[1].lower() == "true":
                satisfied_board_restriction = True
                break

        if satisfied_board_restriction:
            logging.info("NO satisfied_board_restriction found OR satisfied_board_restriction is OK")

            for room_key_for_upgrading in list_of_room_keys_for_upgrading:

                logging.info("room target found for upgrading: %s", room_key_for_upgrading)

                # take price  for the new room from session MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX + "@@" + roomKey + "@@" + rateKey + "@@" + old_regimen_key] = str(myUUID)
                room_upgrading_uuid = session_manager.get(MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX + "@@" + str(room_index) + "@@" + room_key_for_upgrading + "@@" + rate_key + "@@" + board_key)
                logging.info("room_upgrading_uuid obteined form map session. Key: %s: Value %s", MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX + "@@" + str(room_index) + "@@" + room_key_for_upgrading + "@@" + rate_key + "@@" + board_key, room_upgrading_uuid)

                if room_upgrading_uuid:
                    room_upgrading_price = session_manager.get(PRICE_OPTION_KEY_PREFIX + room_upgrading_uuid)
                    logging.info("room_upgrading_price found for upgrading: %s", room_upgrading_price)

                    additional_price = _build_virtual_additional_service_for_upgrading(
                        upgrading_room_configuration, selected_price, room_upgrading_price, room_key,
                        room_key_for_upgrading, result_structure, len(selectedOption.split(";")),
                        room_index, language, UPGRADE_ROOM)

                    if additional_price:
                        room_upgrading_result.append(additional_price)

        room_index += 1
    return room_upgrading_result


def _build_upgrading_board_services_from_results(upgrading_boards_configuration, selectedOption, result_structure, language):
    board_upgrading_result = []
    if selectedOption:
        room_index = 1
        for original_partial_selected in selectedOption.split(';'):
            list_of_board_keys_for_upgrading = []
            _get_board_upgrades_from_selected_option(upgrading_boards_configuration, list_of_board_keys_for_upgrading, original_partial_selected, board_upgrading_result, result_structure, selectedOption, room_index, language)
            room_index += 1

        if not is_enabled_shopping_cart() or session_manager.get(SMART_SHOPPING_CART):
            # We need to filter the board services to join them in one service for all rooms
            board_upgrading_result = _join_board_upgrading_results_without_shopping_cart(board_upgrading_result)

        if get_config_property_value(BOOKING2_UPGRADE_IMAGE):
            pictures_upgrade_section = get_section_from_section_spanish_name(get_config_property_value(BOOKING2_UPGRADE_IMAGE), language)
            pictures_upgrading = getPicturesForKey(language, pictures_upgrade_section.get("key"), [])
            if pictures_upgrading:
                for service_upgrading in board_upgrading_result:
                    service_upgrading['picture'] = pictures_upgrading[0].get("servingUrl").replace('http:', 'https:')

    return board_upgrading_result


def _get_application_info_for_upgrades():
    namespace = get_namespace() or None
    location_prefix = get_location_prefix(get_hotel_code())
    return namespace, f'{location_prefix}{get_application_id()}'

def _get_board_upgrades_from_selected_option(upgrading_boards_configuration, list_of_board_keys_for_upgrading, original_partial_selected, board_upgrading_result, result_structure, selected_option, room_index, language):
    selected_price = session_manager.get(PRICE_OPTION_KEY_PREFIX + original_partial_selected)

    room_key = selected_price[1]
    rate_key = selected_price[0]
    board_key = selected_price[2]

    logging.info("searching map upgrading for room: %s", room_key)
    logging.info("searching map upgrading for board: %s", board_key)

    for key_value in upgrading_boards_configuration['boardMap'].items():
        if key_value[0] == board_key:
            available_board_ids_for_upgrading = key_value[1]
            list_of_board_ids_for_upgrading = available_board_ids_for_upgrading.split(";")
            for board_id_for_upgrading in list_of_board_ids_for_upgrading:
                new_rate_key_upgradingus = ""
                if "_" in board_id_for_upgrading:
                    logging.info("UPGRADINGUS TYPE FOUND in map. board_id_for_upgrading: %s", board_id_for_upgrading)
                    # upgradingus! rate_board configured!!! we need to know also de new rate_id (i.E. PVP_TI)
                    info_board = board_id_for_upgrading.split("_")

                    # take the group of the original rate, we have only to mathc with rates of similar group
                    rate_info = get_rate(rate_key, language)
                    original_rate_group = rate_info.get("groups", "default")

                    new_rate_id = info_board[0]
                    new_rate_id_converted = int(new_rate_id)
                    new_rate_key_upgradingus = datastore_utils.entity_id_to_alphanumeric_key(new_rate_id_converted, "Rate", get_hotel_code())

                    # if upgradingus, only transform to the new rate key, if they have same group
                    # NEVER transform a NR reservation to a PVP reservation!!!
                    new_rate_info = get_rate(new_rate_key_upgradingus, language)
                    target_rate_group = new_rate_info.get("groups", "default")

                    if not target_rate_group == original_rate_group:
                        logging.info("UPGRADINGUS JUMPING RATE_BOARD  destiny found because not same group. original_rate_group: %s, target_rate_group: %s", original_rate_group, target_rate_group)
                        continue

                    rate_key = new_rate_key_upgradingus

                    logging.info("UPGRADINGUS found in upgrading rate_board found: %s RATE_KEY destiny converted: %s",board_id_for_upgrading, rate_key)
                    board_id_for_upgrading = info_board[1]

                if not board_id_for_upgrading.isdigit():
                    board_key_for_upgrading = board_id_for_upgrading
                else:
                    board_id_for_upgrading_converted = int(board_id_for_upgrading)
                    board_key_for_upgrading = datastore_utils.entity_id_to_alphanumeric_key(board_id_for_upgrading_converted, "Regimen", get_hotel_code())

                logging.info("converting board id to key: %s -> %s", board_id_for_upgrading, board_key_for_upgrading)
                if board_key_for_upgrading not in list_of_board_keys_for_upgrading:
                    if new_rate_key_upgradingus:
                        key_to_add = "%s@@%s" % (new_rate_key_upgradingus, board_key_for_upgrading)
                    else:
                        key_to_add = board_key_for_upgrading

                    logging.info("adding %s to list_of_board_keys_for_upgrading", board_key_for_upgrading)
                    list_of_board_keys_for_upgrading.append(key_to_add)

            break

    satisfied_room_restriction = True
    for item in upgrading_boards_configuration['roomMap']:
        # if there are roomMap configured, not all rooms are valid

        satisfied_room_restriction = False
        key_value = item.split(integrations.MAP_SEPARATOR)
        if key_value[0] == room_key and key_value[1].lower() == "true":
            satisfied_room_restriction = True
            break

    if satisfied_room_restriction:
        logging.info("NO satisfied_room_restriction found OR satisfied_room_restriction is OK")

        for key_for_upgrading in list_of_board_keys_for_upgrading:

            forced_rate_key_upgradingus = None
            if "@@" in key_for_upgrading:
                # upgradingus!!!
                info_keys = key_for_upgrading.split("@@")
                rate_key = info_keys[0]
                forced_rate_key_upgradingus = info_keys[0]
                board_key_for_upgrading = info_keys[1]

            else:
                board_key_for_upgrading = key_for_upgrading

            logging.info("Board target found for upgrading: %s", board_key_for_upgrading)

            # take price  for the new room from session MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX + "@@" + roomKey + "@@" + rateKey + "@@" + old_regimen_key] = str(myUUID)
            if is_enabled_shopping_cart() and not get_config_property_value(SHOPPING_CART_LIMIT_ONE_ROOM):
                board_upgrading_uuid = session_manager.get(MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX + "@@" + str(room_index) + "@@" + room_key + "@@" + rate_key + "@@" + board_key_for_upgrading)
                logging.info("board_upgrading_uuid obteined form map session. Key: %s: Value %s",MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX + "@@" + room_key + "@@" + rate_key + "@@" + board_key_for_upgrading, board_upgrading_uuid)
            else:
                board_upgrading_uuid = session_manager.get(MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX + "@@" + str(room_index) + "@@" + room_key + "@@" + rate_key + "@@" + board_key_for_upgrading)
                logging.info("board_upgrading_uuid obteined form map session. Key: %s: Value %s", MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX + "@@" + str(room_index) + "@@" + room_key + "@@" + rate_key + "@@" + board_key_for_upgrading, board_upgrading_uuid)

            if board_upgrading_uuid:
                board_upgrading_price = session_manager.get(PRICE_OPTION_KEY_PREFIX + board_upgrading_uuid)
                logging.info("board_upgrading_price found for upgrading: %s", board_upgrading_price)
                additional_service = _build_virtual_additional_service_for_upgrading(
                    upgrading_boards_configuration, selected_price,
                    board_upgrading_price, board_key, board_key_for_upgrading,
                    result_structure, len(selected_option.split(";")), room_index,
                    language, UPGRADE_BOARD, original_room_key=room_key, selectedOption=selected_option,
                    forced_rate_key_upgradingus=forced_rate_key_upgradingus
                )

                if additional_service:
                    board_upgrading_result.append(additional_service)

    return board_upgrading_result

def _join_board_upgrading_results_without_shopping_cart(original_upgrades_list):
    upgrades_by_key = {}
    filtered_upgrades_list = []

    for upgrade in original_upgrades_list:
        key = upgrade.get('key_for_upgrading')
        if upgrades_by_key.get(key):
            upgrades_by_key[key].append(upgrade)
        else:
            upgrades_by_key[key] = [upgrade]

    for value in upgrades_by_key.values():
        base_element = value[0]
        total_elements = len(value)
        if total_elements > 1:
            room_list = str(value[0].get('room')) + ";"
            keys_list = value[0].get('key') + ";"
            price = value[0].get('price')
            # Build extra rooms info to add in the same service
            for i in range(1, total_elements):
                room_list += "room_" + str(i+1)
                keys_list += value[i].get('key')
                price += value[i].get('price')
                if i < total_elements-1:
                    room_list += ";"
                    keys_list += ";"

            base_element['room'] = room_list
            base_element['key'] = keys_list
            base_element['price'] = price
        base_element['name'] = base_element.get('name').replace(' 1 ', ' ')
        filtered_upgrades_list.append(base_element)

    return filtered_upgrades_list

def _build_virtual_additional_service_for_upgrading(upgrading_configuration, original_selected_price,
                                                    room_upgrading_price, original_key, key_for_upgrading,
                                                    result_structure, num_rooms, room_index, language, upgrading_type,
                                                    original_room_key="", selectedOption=None,
                                                    forced_rate_key_upgradingus=None):

    from booking_process.utils.booking.upgradingUtils import _get_upgrading_picture, _get_valid_upgrading_discount

    logging.info("Building virtual service upgrade for upgrading_type: %s", upgrading_type)

    # truncate decimals booking
    truncate_decimal = get_config_property_value(TRUNCATE_DECIMAL_BOOKING)
    round_decimal = get_config_property_value(ROUND_DECIMAL_BOOKING)
    upgrade_room_see_more_popup = get_config_property_value(UPGRADES_VIEW_MORE_POPUP) if upgrading_type == UPGRADE_ROOM else False

    # built info content to show
    original_info_structure = result_structure.get(original_key)
    info_structure = result_structure.get(key_for_upgrading)
    picture = _get_upgrading_picture(upgrading_configuration, key_for_upgrading)

    original_price = None

    if upgrading_type == UPGRADE_ROOM:

        new_option_name = info_structure.get("roomName")
        original_option_name = original_info_structure.get("roomName")
        if not picture:
            picture = info_structure.get("roomPicture")

        price_label = get_web_dictionary(language).get('T_precio_servicio')
        upgrading_room_price = float(room_upgrading_price[3])
        original_price = original_selected_price[3]

    else:
        # we suppose that it is a board

        new_option_name = info_structure.get("regimenName")
        original_option_name = original_info_structure.get("regimenName")

        # take the pic of the original room
        room_structure = result_structure.get(original_room_key)
        if not picture:
            picture = room_structure.get("roomPicture")

        price_label = get_web_dictionary(language).get('T_precio_servicio')

        # for boards, we have to calculate prices from ALL selected rooms!! For room, not, becasue each room will have it own virtual service for upgradding
        # In case of smart shopping cart, we keep the same logic as without shopping cart
        if (upgrading_type == UPGRADE_BOARD and not is_enabled_shopping_cart()) or (upgrading_type == UPGRADE_BOARD and session_manager.get(SMART_SHOPPING_CART)):
            if selectedOption:
                upgrading_room_price = 0
                original_price = 0

                selected_options = selectedOption.split(";")

                count_room_index = 1

                for one_selected_option in selected_options:

                    one_selected_price = session_manager.get(PRICE_OPTION_KEY_PREFIX + one_selected_option)

                    room_key = one_selected_price[1]
                    rate_key = one_selected_price[0]

                    if forced_rate_key_upgradingus:
                        logging.info("forcing rate upgradingus to %s", forced_rate_key_upgradingus)
                        rate_key = forced_rate_key_upgradingus

                    room_upgrading_uuid = session_manager.get(MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX + "@@" + str(
                        count_room_index) + "@@" + room_key + "@@" + rate_key + "@@" + key_for_upgrading)
                    logging.info("room_upgrading_uuid for PARTIAL BOARDS obteined form map session. Key: %s: Value %s",
                                 MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX + "@@" + str(
                                     count_room_index) + "@@" + room_key + "@@" + rate_key + "@@" + key_for_upgrading,
                                 room_upgrading_uuid)

                    count_room_index += 1

                    if room_upgrading_uuid:
                        partial_room_upgrading_price = session_manager.get(PRICE_OPTION_KEY_PREFIX + room_upgrading_uuid)
                        logging.info("PARTIAL board_upgrading_price found for upgrading: %s", room_upgrading_price)
                        logging.info("PARTIAL original price found selected_price: %s", one_selected_price)

                        upgrading_room_price += float(partial_room_upgrading_price[3])
                        original_price += float(one_selected_price[3])

                logging.info("Final Original price calculated original_price: %s", original_price)
                logging.info("Final destiny price calculated upgrading_board_price: %s", upgrading_room_price)

        else:

            upgrading_room_price = float(room_upgrading_price[3])
            original_price = original_selected_price[3]

            logging.info("full room_upgrading_price : %s, full original_selected_price: %s", room_upgrading_price,
                         original_selected_price)
            logging.info("upgrading_room_price : %s, original_price: %s", upgrading_room_price, original_price)

    if num_rooms > 1 and (upgrading_type == UPGRADE_ROOM or is_enabled_shopping_cart()):
        original_option_name = original_option_name + " " + str(room_index)

    if get_config_property_value(UPGRADES_SHOW_ONLY_TARGET_NAME):
        service_name = new_option_name
        service_spanish_name = service_name
    else:
        service_name = get_web_dictionary(language).get('T_mejora_tu_estancia').replace(
            "@@ORIGINAL_OPTION@@", original_option_name).replace("@@NEW_OPTION@@", new_option_name)
        service_spanish_name = service_name

    if not original_price:
        logging.error(
            "Something bad has ocurred. We have't found original price to calculate prices!! please check all logs. FOr the moment we are not going to show the upgrading service")
        return None
    discount_txt = ""
    # get a valid discount:
    discount, by_difference, fixed_amount = _get_valid_upgrading_discount(upgrading_configuration, room_index,
                                                                          original_key, key_for_upgrading)
    amount_to_discount = 0
    percentage_to_discount = ""
    # never do a discount if we have a fixed amount

    logging.info("[Upgrading] discount: %s", discount)
    if discount == "blackout":
        return None

    if discount and (not fixed_amount):
        if by_difference:

            # note that if it is configured "by diffence. it will be always a percentage"
            discount_percent = float(discount.replace("%", "").strip())
            percentage_to_discount = True

            room_difference = float(upgrading_room_price) - float(original_price)

            if room_difference > 0:
                amount_to_add = room_difference - (room_difference / float(100 / discount_percent))

                # if we have show a truncated virtual service in booking2, we have to do tahe same in booking3 to be coherent
                if truncate_decimal:
                    logging.info("truncating supplement from %s to %s", amount_to_add, int(float(amount_to_add)))
                    amount_to_add = int(float(amount_to_add))

                if round_decimal:
                    logging.info("round supplement from %s to %s", amount_to_add, int("%.0f" % float(amount_to_add)))
                    amount_to_add = int("%.0f" % float(amount_to_add))

                discount_txt = discount

                logging.info("applying discount: %s amount_to_add: %s", discount_txt, amount_to_add)
                upgrading_room_price = round(((float(original_price) + amount_to_add)), 2)


        else:
            if "%" in discount:
                discount_percent = float(discount.replace("%", "").strip())
                amount_to_discount = (upgrading_room_price / float(100 / discount_percent))
                discount_txt = discount
            else:
                # just an amount
                amount_to_discount = float(discount.strip())
                # TODO: use that, but in producion is raising a unicode error
                # currency = get_currency(SPANISH)
                currency = " EUR"
                discount_txt = "%s%s" % (discount, currency)

            logging.info("applying discount: %s amount_to_discount: %s", discount_txt, amount_to_discount)
            upgrading_room_price = round(((upgrading_room_price - amount_to_discount)), 2)

    if amount_to_discount or percentage_to_discount:
        service_name = "%s%s" % (
        service_name, get_web_dictionary(language).get('T_ahorra_upgrading').replace("@@DESCUENTO@@", discount_txt))

    room_key = room_upgrading_price[1]
    rate_key = room_upgrading_price[0]
    board_key = room_upgrading_price[2]
    virtual_key = UPGRADE_SEPARATOR.join(map(str, (room_index, room_key, rate_key, board_key)))

    session_manager.set(UPGRADING_TEXT_ROOM + virtual_key, service_name)

    if fixed_amount and "%" in fixed_amount:
        logging.info("Calculating price of virtual service with a fixed percentage: %s", fixed_amount)
        percent_amount = float(fixed_amount.replace("%", "").strip())

        # if we are upgrading a reservation, fixed amount are always respect to the reservation price.
        if session_manager.get(IS_REAL_UPGRADING_PROCESS):
            original_reservation_price = _get_room_price_from_reservation(
                session_manager.get(IS_REAL_UPGRADING_PROCESS), room_index, original_room_key)
            price = float((float(original_reservation_price) * percent_amount / float(100)))
        else:
            price = float((float(original_price) * percent_amount / float(100)))

        logging.info("price of virtual service with a fixed percent: %s", price)

    elif fixed_amount:
        price = float(fixed_amount)
        logging.info("Calculating price of virtual service with a fixed amount: %s", price)
    else:
        price = float(upgrading_room_price) - float(original_price)
        is_board_upgrade_merged = not is_enabled_shopping_cart() or session_manager.get(SMART_SHOPPING_CART)
        if upgrading_type == UPGRADE_BOARD and is_board_upgrade_merged:
            price = price / int(num_rooms)
            logging.info(
                f"Calculating price of virtual service upgrade {upgrading_type} with a this formula upgrading_room_price ({upgrading_room_price}) - original_price ({original_price}) / num_rooms({num_rooms}) =  {price}")
        else:
            logging.info(f"Calculating price of virtual service upgrade {upgrading_type} with a this formula upgrading_room_price {upgrading_room_price}) - original_price ({original_price} =  {price}")


    session_manager.set(UPGRADING_INCREMENT_ROOM_PRICE + virtual_key, price)
    # be careful, the increment could be 0, (100% of discount), so we have to indicate to booking3, that a upgrade is selected
    session_manager.set(UPGRADING_INCREMENT_ROOM_MADE + virtual_key, True)

    logging.info(f"Setting upgrading {upgrading_type} price room_index: {str(room_index)} virtual id: {virtual_key} upgradding increment_amount (the price of the virtual fake service): {price}")

    if truncate_decimal:
        price = int(float(price))

    if round_decimal:
        price = int("%.0f" % float(price))

    if upgrading_type == UPGRADE_ROOM:
        # beacuso we are going to show a independent servve by num room in search
        type_service = PER_ROOM_TYPE
    else:
        # becasue for board, we are going to show ONLY a service for ALL rooms in search
        type_service = PER_PERSON_TYPE
    # actaully is "Por Servicio" but for configured this we have to configured: 'duration': u'Uno', and Por Persona

    # never do a "Downgrading!!!"
    if price < 0:
        logging.warning("Descarting upgrading because it has negative price!: Name: %s Price: %s", service_spanish_name,
                        price)
        return None

    room_upgrading_service = {
        'hide_amount_select_with_value': 1,
        'key': virtual_key,
        'type': type_service,
        'price_label': price_label,
        'name': service_name,
        'price': round(price, 2),
        'days': 1,
        'upsellingEnabled': True,
        'priority': u'a1',
        'spanish_name': service_spanish_name,

        'release': -1,
        'duration': u'Uno',
        'daysVisible': False,
        'maxRangeDays': 1,
        'only_one': '%s_%s_group' % (room_index, upgrading_type),

        'picture': picture,
        'range': [0, 1],
        'rangeDays': [1],
        'description': '',
        'amount': 0,
        'price_per_service': True,
        'virtual_service_for_upgrading': True,
        'upgrading_type': upgrading_type,
        'key_for_upgrading': key_for_upgrading,
        'room': room_index,
        'has_view_more_popup': upgrade_room_see_more_popup
    }

    regimen_data = get_board(key_for_upgrading, language)
    if regimen_data:
        room_upgrading_service["regimen_name"] = regimen_data.get("board_name", "")
        room_upgrading_service["regimen_description"] = unescape(regimen_data.get("board_description", ""))


    # Calculate price per night/person
    if get_config_property_value(UPGRADES_PRICES_BY_OCCUPANCY_NIGHTS):
        try:
            user_search = session_manager.get(SEARCH_KEY_PREFIX)
            shopping_cart_selected = session_manager.get(SHOPPING_CART_SELECTED)

            rooms = shopping_cart_selected['rooms'] if shopping_cart_selected else user_search['rooms']
            room_info = rooms[room_index - 1]
            total_occupancy = int(room_info.get('numAdults')) + int(room_info.get('numKids'))
            total_nights = get_nights(user_search['startDate'], user_search['endDate'])
            price_per_night_person = float(room_upgrading_service['price']) / float(total_occupancy) / float(total_nights)
            room_upgrading_service['price_per_night_person'] = float('%.2f' % price_per_night_person)
        except IndexError as e:
            is_shopping_cart = is_enabled_shopping_cart()
            traceback = auditUtils.makeTraceback()
            message = "Error calculating price per night/person for upgrading service"
            logging.error(f"{message}: {str(e)}.\nTraceback: {traceback}")
            logging.warning(
                f"Room index: {room_index}\n"
                f"Selected options: {selectedOption}\n"
                f"Search: {user_search}\n"
                f"Is shopping cart enabled: {is_shopping_cart}\n"
                f"Shopping cart: {shopping_cart_selected}"
            )
            notify_exception(message, traceback, add_hotel_info=True)

    if get_config_property_value(UPGRADE_POPUP_INFO):
        room_upgrading_service['upgrade_popup_info'] = get_config_property_value(UPGRADE_POPUP_INFO)

    return room_upgrading_service


def perform_upgrade_rooms_for_shopping_cart(shopping_cart, virtual_indexes_for_apply_increments):
    language = session_manager.get('language')
    rooms_names = _getRoomsNames(language)
    boards_names = _getRegimensNames(language)
    selected_options = session_manager.get(SELECTED_OPTION_KEY, '').split(';')
    upgrade_success = False

    for index, room in enumerate(shopping_cart.get('rooms')):
        original_room = copy.deepcopy(room)
        for virtual_indexes in virtual_indexes_for_apply_increments:
            upgrade_info = virtual_indexes.split(UPGRADE_SEPARATOR)
            if str(index + 1) == upgrade_info[0]:
                logging.info('Upgrading room %s' % str(index + 1))
                rooms_names_filtered = list(filter(lambda l: l.entityKey == upgrade_info[1], rooms_names))
                if len(rooms_names_filtered):
                    room['room_key'] = upgrade_info[1]
                    room['room_name'] = unescape(rooms_names_filtered[0].value)
                    upgrade_success = True

                boards_names_filtered = list(filter(lambda l: l.entityKey == upgrade_info[3], boards_names))
                if len(boards_names_filtered):
                    room['board_key'] = upgrade_info[3]
                    room['board_name'] = unescape(boards_names_filtered[0].value)
                    upgrade_success = True

                room['id'] = selected_options[index]
                room['uuid'] = selected_options[index]

                is_service_upgrade = False
                if is_booking2_react_active():
                    for service_key, service_info in copy.deepcopy(shopping_cart.get('advanced_services', {})).items():
                        if session_manager.get(SMART_SHOPPING_CART):
                            if virtual_indexes in service_info[0].get('key', '').split(';'):
                                del shopping_cart['advanced_services'][service_key]
                        else:
                            if service_info[0].get('key') == virtual_indexes:
                                room['price'] = room.get('price') + service_info[0].get('price')
                                del shopping_cart['advanced_services'][service_key]
                                is_service_upgrade = True
                else:
                    for i, service in enumerate(copy.deepcopy(shopping_cart.get('services', []))):
                        if session_manager.get(SMART_SHOPPING_CART):
                            if virtual_indexes in service.get('key', '').split(';'):
                                shopping_cart.get('services').pop(i)
                        else:
                            if service.get('key') == virtual_indexes:
                                room['price'] = room.get('price') + service.get('price')
                                shopping_cart.get('services').pop(i)
                                is_service_upgrade = True

                # For upgrades made in booking3 directly or smart shopping cart
                if not is_service_upgrade:
                    if request.values.get('action') == 'reset_upgrade':
                        backup_info = room.get('backup_upgrade_info')
                        if backup_info:
                            logging.info('Upgrade removed from booking3. Restoring shopping cart room from backup info')
                            for key, value in backup_info.items():
                                room[key] = value
                    else:
                        increment_price = session_manager.get(UPGRADING_INCREMENT_ROOM_PRICE + virtual_indexes)
                        if increment_price is not None and session_manager.get(UPGRADING_INCREMENT_ROOM_MADE + virtual_indexes):
                            logging.info("Applying updated price for shopping cart room %s increment_price: %s", str(index+1), increment_price)
                            room['price'] = room.get('price') + increment_price
                            room['backup_upgrade_info'] = original_room
    if upgrade_success:
        shopping_cart["upgrade_success"] = upgrade_success

    return shopping_cart


def get_room_upgrades_see_more_popup_html(upgrade_additional_services, language):
    upgrade_additional_services = list(filter(lambda x: x.get('has_view_more_popup', False), upgrade_additional_services))
    resultStructure = buildResultStructures(language)
    keys_for_upgrading_list = []
    room_upgrades_info = []
    final_html = ''

    for upgrade in upgrade_additional_services:
        keys_for_upgrading_list.append(upgrade.get('key_for_upgrading'))

    keys_for_upgrading_list = list(set(keys_for_upgrading_list))

    if len(keys_for_upgrading_list) > 0:
        for key_for_upgrading in keys_for_upgrading_list:
            room_upgrades_info.append({
                'roomStructure': [
                    {
                        'roomKey': key_for_upgrading,
                        'roomName': resultStructure.get(key_for_upgrading).get('roomName'),
                        'name': resultStructure.get(key_for_upgrading).get('name'),
                        'roomDescription': resultStructure.get(key_for_upgrading).get('roomDescription'),
                        'roomPicture': resultStructure.get(key_for_upgrading).get('roomPicture'),
                        'pictures': resultStructure.get(key_for_upgrading).get('pictures') or [],
                        'services_pictures': resultStructure.get(key_for_upgrading).get('services_pictures')
                    }
                ]
            })

    if room_upgrades_info:
        for room in room_upgrades_info:
            final_html += _build_room_popup(room)
    return final_html


def _build_room_popup(room_data):
    is_mobile = user_agent_is_mobile()
    context = {
        'see_more_popup_version': '1',
        'currentRoom': room_data,
        'upgrade_room_render': True
    }
    if is_mobile:
        context['room'] = room_data['roomStructure'][0]

    booking1_popup_see_more_version = get_config_property_value(BOOKING1_SEE_MORE_VERSION)
    if booking1_popup_see_more_version:
        booking1_see_more_popup_version = booking1_popup_see_more_version.split(";")
        context['see_more_popup_version'] = booking1_see_more_popup_version[0]
        if 'room_basic_gallery' in booking1_see_more_popup_version:
            context['booking_show_gallery'] = True

    template = 'booking_2/booking_process_v1/rooms/_see_more_popup.html'

    if is_mobile:
        template = 'mobile/booking_process/room/_room_info_modal.html'

    return build_template_2(template, context)


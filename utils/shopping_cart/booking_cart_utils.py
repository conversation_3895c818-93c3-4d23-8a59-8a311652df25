from booking_process.constants.advance_configs_names import BOOKING_SHOPPING_CART, CALL_CENTER_FORCE_SHOPPING_CART, \
    BOOKING1_MULTIPLE_ROOMS_WITHOUT_MERGING
from booking_process.constants.session_data import AGENT_ID
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.shopping_cart.booking_cart import is_enabled_shopping_cart

from booking_process.utils.session import session_manager


def is_booking_cart_v2() -> bool:
    """
    Check if the booking cart v2 is configured.

    It can be configured in two ways:
        - Generally by the config BOOKING_SHOPPING_CART with value "v2"
        - For call center users only if CALL_CENTER_FORCE_SHOPPING_CART is set to True

    :return: bool: True if enabled, False otherwise
    :unit_test: unit_tests.booking_process.utils.shopping_cart.test_booking_cart_utils.TestBookingCartUtils.test_is_booking_cart_v2
    """
    if session_manager.get(AGENT_ID) and get_config_property_value(CALL_CENTER_FORCE_SHOPPING_CART):
        return True

    advance_config = get_config_property_value(BOOKING_SHOPPING_CART)
    if advance_config and advance_config == "v2":
        return True

    return False


def get_search_info_from_booking_cart(cart):
    result = []
    for room in cart.get('rooms', []):
        result.append(room.get('search_info'))
    return result


def is_allowed_only_one_room_for_cart():
    cart_is_active = is_enabled_shopping_cart() and is_booking_cart_v2()
    cart_is_forced_for_call = session_manager.get(AGENT_ID) and get_config_property_value(CALL_CENTER_FORCE_SHOPPING_CART)
    multiple_rooms_by_steps_enabled = get_config_property_value(BOOKING1_MULTIPLE_ROOMS_WITHOUT_MERGING) or cart_is_forced_for_call

    return cart_is_active and not multiple_rooms_by_steps_enabled

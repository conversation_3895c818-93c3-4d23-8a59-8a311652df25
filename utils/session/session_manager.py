import json
import logging, os, threading, uuid, datetime
import pickle
import sys
import traceback

import zlib

from google.cloud import ndb
from google.cloud.datastore import Entity
from google.cloud.ndb import BlobProperty

from booking_process.utils.cache_seeker import cache_seeker_utils
from booking_process.utils.development.dev_booking_utils import DEV_SESSIONS
from booking_process.utils.namespaces.namespace_utils import get_application_id, get_namespace
from booking_process.utils.ndb_utils.ndb_utils import add_ndb_context
from booking_process.utils.session import redis_session_manager
from booking_process.utils.session.pickle_utils import load_object, dumps_object
from flask import request
from paraty import app

'''
- There are two kinds of session

 1. Normal session, initiated and persisted
 2. Request session, just persistent for a given request

'''

__author__ = 'fmatheis'

_tls = threading.local()
DEV_SESSIONS_STORAGE = {}


@app.before_request
def init_tls():
    _tls.__dict__.clear()


SESSION_MAX_AGE = 18000  # 5 hours (3600 * 5)
FROM_SAVE_BOOKING_SESSION_MAX_AGE = 75600  # 21 hours (3600 * 21)
COMPLETED_BOOKING_SESSION_MAX_AGE_OFFSET = 172800  # 48 hours (3600 * 48)
SESSION_IN_CACHE_MAX_AGE = 3600  # 1 hour has to be enough (it's just for exceptions)

PICKLE_PROTOCOL = 2


class PickleProperty(BlobProperty):
    """A Property whose value is any picklable Python object."""

    def _to_base_type(self, value):
        return dumps_object(value)

    def _from_base_type(self, value):
        try:
            return load_object(value)
        except UnicodeDecodeError:
            if int(os.environ.get('NDB_PY2_UNPICKLE_COMPAT', '0')):
                return pickle.loads(value, encoding='bytes')
            raise


SESSION_CONSISTENCY_KEY = 'session_write_history'


class UserSession(ndb.Model):
    # Note: do not move nor remove this class as it is used in other projects
    # Default
    content = PickleProperty(compressed=False)

    timestamp = ndb.DateTimeProperty(auto_now_add=True)

    # Pickle doesn't work the same out of standard GAE, so we need something more "pure"
    content2 = ndb.BlobProperty(indexed=False)

    # In case we need to save it compressed
    compressedContent = ndb.BlobProperty(indexed=False)

    # In case we need to save it compressed (not pickled)
    compressedContent2 = ndb.BlobProperty(indexed=False)


class ServiceExternal(ndb.Model):
    reservation_id = ndb.StringProperty()
    booking_id = ndb.StringProperty()
    sid = ndb.StringProperty()
    content = PickleProperty(compressed=False)
    compressed_content = ndb.BlobProperty(indexed=False)
    timestamp = ndb.DateTimeProperty()
    source = ndb.StringProperty()
    status = ndb.StringProperty()
    products = ndb.TextProperty()


def get_sid_from_params(param_to_check='sid'):
    if request:
        return request.values.get(param_to_check)


@add_ndb_context
def _get_session_from_datastore(sid):

        user_session = UserSession.get_by_id(sid, use_cache=False, use_memcache=False)
        if user_session and type(user_session.content) is bytes:
            user_session.content = load_object(user_session.content)

        return user_session


def _get_current_session_from_persistence(sid):
    '''
    First we try Redis, if problems, we fail over to default approach of using the Datastore (NDB)
    '''
    try:
        if DEV_SESSIONS and DEV_SESSIONS_STORAGE.get(sid):
            return DEV_SESSIONS_STORAGE.get(sid)

        read_session = redis_session_manager.read_session(sid)
        if read_session:
            logging.debug("Getting session from redis")

            if type(read_session) == Entity:
                model_simulation = UserSession(key=read_session.key)
                for key, value in read_session.items():
                    setattr(model_simulation, key, value)

                return model_simulation

            return read_session
        else:
            logging.debug("Getting session from datastore")
            return _get_session_from_datastore(sid)

    except Exception as e:
        logging.warning("Error reading session from Redis: %s" % e)
        logging.info("Getting session from datastore")
        return _get_session_from_datastore(sid)


def log_caller(message=''):
    import traceback
    my_stack = traceback.format_stack()
    for i, current_stack in enumerate(my_stack):
        if 'session_manager' in current_stack:
            if message:
                logging.info(message)
            logging.debug("Caller: %s", my_stack[i])
            break

@add_ndb_context
def _save_session_to_persistence(async_put=False, disable_redis_storage=False):
    '''
    By default, we are saving to redis only.
    When saving to redis, Hotel-tools periodically saves changes to the datastore (by calling utils of hotel-webs)
    '''
    try:
        logging.debug("Saving session to Redis: %s", get_session_id())

        # Understand who is calling.
        log_caller()

        _update_with_persisted_session()

        # try:
        #     _check_session_consistency()
        #     _add_session_modification_checkpoint()
        # except Exception as e:
        #     logging.warning("Error checking session consistency: %s", e)

        if not disable_redis_storage:
            redis_session_manager.write_session(get_session_id(), _tls.currentSession, update_timestamp=False)

    except Exception as e:
        logging.error("Problem persisting to redis: %s", get_session_id())
        _log_error_traceback()

        # We delete it for safety reasons, otherwise the datastore might be out of sync with redis (i.e. changes in datastore not reflected in redis)
        try:
            redis_session_manager.delete_session(get_session_id())
        except Exception as e:
            logging.error("Problem deleting from redis: %s", get_session_id())
            _log_error_traceback()

    try:
        if _session_is_compressed():
            _compress_and_save_session()
        else:
            if async_put:
                _tls.currentSession.put_async()
            else:
                put_session_on_datastore(_tls.currentSession)

        logging.info("Session saved to Datastore: %s", get_session_id())
    except Exception as e:
        logging.warning(e)
        logging.warning("Session too large, we need to compress it")
        _compress_and_save_session()


def recoverySessionPrebooking(sid):
    '''
    To be called by any request that requires to have a session
    '''
    logging.info("Recoverying Session for Prebooking!")

    # Explicitly create a new one
    if sid:
        sessionId = sid
        logging.info("SessionId: %s" % sessionId)

        currentSession = _get_current_session_from_persistence(sid)

        # Fix for compatibility with standalone
        if currentSession and currentSession.content2:
            currentSession.content = json.loads(currentSession.content2)
            currentSession.content2 = None

        if currentSession and not currentSession.content2 and currentSession.compressedContent2:
            content = None
            try:
                content = json.loads(json.loads(zlib.decompress(currentSession.compressedContent2)))
            except Exception as e:
                content = json.loads(zlib.decompress(currentSession.compressedContent2))
            currentSession.content = content
            currentSession.compressedContent = None

        if currentSession:
            _tls.currentSession = currentSession
            _tls.save_to_datastore_automatically = True
            _tls.requestSession = {}
        else:
            _tls.currentSession = None
            _tls.save_to_datastore_automatically = True
            _tls.requestSession = {}


def recoverySession(sid):
    '''
    To be called by any request that requires to have a session
    '''
    logging.info("Recovering Session!")

    # Explicitly create a new one
    if sid:
        sessionId = sid
        logging.info("SessionId: %s" % sessionId)

        currentSession = _get_current_session_from_persistence(sid)

        # Fix for compatibility with standalone
        if currentSession and not currentSession.content and currentSession.content2:
            recovered_content = load_object(currentSession.content2)
            if recovered_content and not recovered_content is dict:
                try:
                    recovered_content = json.loads(recovered_content)
                except Exception as e:
                    recovered_content = recovered_content

            currentSession.content = recovered_content
            currentSession.content2 = None

        if currentSession and not currentSession.content and not currentSession.content2 and currentSession.compressedContent:
            currentSession.content = load_object(zlib.decompress(currentSession.compressedContent))
            currentSession.compressedContent = None

        if currentSession and not currentSession.content and not currentSession.content2 and currentSession.compressedContent2:
            try:
                currentSession.content = json.loads(zlib.decompress(currentSession.compressedContent2))
            except Exception as e:
                currentSession.content = zlib.decompress(currentSession.compressedContent2)
            currentSession.compressedContent = None

        if currentSession:
            _tls.currentSession = currentSession
            _tls.save_to_datastore_automatically = True
            _tls.requestSession = {}
        else:
            _tls.currentSession = None
            _tls.save_to_datastore_automatically = True
            _tls.requestSession = {}


def set_save_automatically(save_to_datastore_automatically):
    logging.info('Setting automatically save: %s' % save_to_datastore_automatically)
    _tls.save_to_datastore_automatically = save_to_datastore_automatically


def get_save_automatically():
    return _tls.save_to_datastore_automatically

@add_ndb_context
def initSession(createNew=False, save_to_datastore_automatically=True, new_sid=None):
    '''
    To be called by any request that requires to have a session
    '''

    # This gives us the chance to save only at the end
    _tls.save_to_datastore_automatically = save_to_datastore_automatically

    # Explicitly create a new one
    if createNew:
        sessionId = new_sid
        if not sessionId:
            sessionId = str(uuid.uuid4())

        currentSession = UserSession(key=ndb.Key('UserSession', sessionId), content={})
        logging.info("Session created at namespace: %s" % get_namespace())

        currentSession.timestamp = datetime.datetime.utcnow()

        _tls.currentSession = currentSession
        _tls.requestSession = {}

    # We use an existing one if existing or we just a temporary one for the current request
    else:
        sessionId = get_sid_from_params()
        if sessionId:
            logging.info("SessionId: %s" % sessionId)

        if not sessionId:
            allowed_session_creation = ['booking0', 'booking1']
            request_path = request.path if request else ''
            if any(x in request_path for x in allowed_session_creation):
                # By default we dont save to datastore automatically to avoid default websites to create sessions
                # This happens when we are trying to recover an inexistent session
                initSession(createNew=True, save_to_datastore_automatically=False, new_sid=new_sid)
            else:
                _tls.currentSession = None
                _tls.requestSession = {}
            return

        currentSession = _get_current_session_from_persistence(sessionId)

        # Fix for compatibility with standalone

        _prepare_content_of_session_persistance(currentSession, sessionId)

        if currentSession:
            _tls.currentSession = currentSession

            if get_sid_from_params('fromSaveBooking'):
                currentSession.content['fromSaveBooking'] = True
                save_session_to_datastore()

            _tls.requestSession = {}
        else:
            _tls.currentSession = None
            _tls.requestSession = {}


def _prepare_content_of_session_persistance(currentSession, sessionId):
    if currentSession and not currentSession.content and currentSession.content2:
        recovered_content = load_object(currentSession.content2)
        if recovered_content and not recovered_content is dict:
            try:
                recovered_content = json.loads(recovered_content)
            except Exception as e:
                recovered_content = recovered_content

        currentSession.content = recovered_content
        currentSession.content2 = None

    # Sometimes We need to compress things if size is too large
    elif currentSession and not currentSession.content and not currentSession.content2 and currentSession.compressedContent:

        currentSession.content = load_object(zlib.decompress(currentSession.compressedContent))
        currentSession.compressedContent = None

    elif currentSession and not currentSession.content and not currentSession.content2 and currentSession.compressedContent2:
        try:
            currentSession.content = json.loads(json.loads(zlib.decompress(currentSession.compressedContent2)))
        except Exception as e:
            currentSession.content = json.loads(zlib.decompress(currentSession.compressedContent2))
        currentSession.compressedContent = None


def get_session_age():
    '''
    Returns the session age in secondse
    '''
    if not session_exists():
        logging.info("Session doesn't exist")
        return 100000

    session_age = datetime.datetime.utcnow() - _tls.currentSession.timestamp

    return session_age.seconds


def session_ttl() -> int:
    """
    Returns the session max age in seconds.

    :return: Maximum session age in seconds.
    """
    max_time = SESSION_MAX_AGE

    if _tls.currentSession.content:
        if _tls.currentSession.content.get('session_max_age_forced'):
            max_time = int(_tls.currentSession.content.get('session_max_age_forced'))  # (21 hours)

        if _tls.currentSession.content.get('fromSaveBooking'):
            max_time = max(max_time, FROM_SAVE_BOOKING_SESSION_MAX_AGE)  # (21 hours)

        # our client could review his reservation until 48 hours after he made it.
        if _tls.currentSession.content.get('booking-content'):
            max_time += COMPLETED_BOOKING_SESSION_MAX_AGE_OFFSET  # (48 hours)

    return max_time

# logging.info("max session age incremented to %s because of booking-content", max_time)


def session_expired():
    if session_transfer():
        return False

    if session_is_paid():
        return False

    if not session_exists():
        logging.info("Session expired because it doesn't exist")
        return True

    session_age = datetime.datetime.utcnow() - _tls.currentSession.timestamp

    max_time = session_ttl()

    total_session_seconds = session_age.seconds + session_age.days * 24 * 60 * 60
    result = total_session_seconds > max_time

    if result:
        logging.info("Session expired because it's age: %s is older than the max time: %s", session_age.seconds, max_time)

    return result


def session_exists():
    return hasattr(_tls, 'currentSession') and _tls.currentSession != None


def get_current_session():
    if (not _tls.currentSession) or session_expired():
        return _tls.requestSession

    return _tls.currentSession


def set_current_session(session):
    '''
    Useful when we need to share session among different threads.
    '''
    _tls.currentSession = session
    _tls.save_to_datastore_automatically = True


def get(key, backup_in_cache_seeker=False):
    if not hasattr(_tls, "currentSession"):
        initSession()

    if (not _tls.currentSession) or session_expired():
        if backup_in_cache_seeker:
            value_in_cache = get_value_from_session_cache(key)
            if value_in_cache:
                return value_in_cache

        return _tls.requestSession.get(key)

    value_in_sess = _tls.currentSession.content.get(key)

    # Pointless if we are using Redis
    # if not value_in_sess and backup_in_cache_seeker:
    #
    #     value_in_cache = get_value_from_session_cache(key)
    #     if value_in_cache:
    #         return value_in_cache

    return value_in_sess


def get_all():
    if (not _tls.currentSession) or session_expired():
        return _tls.requestSession

    return _tls.currentSession.content


def set(key, value, backup_in_cache_seeker=False, async_put=True):

    if key == 'personal_details_':
        log_caller("Saving personal Details!!!")

    if not hasattr(_tls, "currentSession"):
        initSession()

    has_changed = _set_on_session(key, value)
    if not _tls.save_to_datastore_automatically:
        return

    # No need to save if the value is the same (performance optimization)
    if has_changed:
        _save_session_to_persistence(async_put=async_put)


def set_sync(key, value, backup_in_cache_seeker=False):
    # With redis implementation everything is sync, no need to have diffeent methods
    return set(key, value, backup_in_cache_seeker, async_put=False)


def _session_is_compressed():
    if not _tls.currentSession or not _tls.currentSession.content:
        return False

    return _tls.currentSession.content.get('compressed')


def set_multi(dictionary_to_update):
    '''
    Sets multiple key, values on session using a dictionary.

    Note that this uses synchronous put
    '''

    if not dictionary_to_update:
        return

    if 'personal_details_' in dictionary_to_update:
        log_caller("Saving personal Details!!!")

    for key_to_set, value_to_set in dictionary_to_update.items():
        _set_on_session(key_to_set, value_to_set)

    if not _tls.save_to_datastore_automatically:
        return

    _save_session_to_persistence(async_put=True)


def get_session_id(createNew=False):
    if not hasattr(_tls, "currentSession"):
        initSession(createNew=createNew)

    if _tls.currentSession:
        return _tls.currentSession.key.string_id()
    else:
        return None


def _compress_and_save_session():
    logging.info("Compressing session because booking1 is too large")

    _tls.currentSession.content['compressed'] = True

    # Let's compress it, save to datastore and leave everything as before
    valid_content = _tls.currentSession.content
    _tls.currentSession.compressedContent = zlib.compress(dumps_object(valid_content), 1)
    _tls.currentSession.content = None
    try:
        put_session_on_datastore(_tls.currentSession)
    except Exception as e:
        _log_error_traceback()
        logging.error("Error saving compressed session to datastore: %s", e)


    _tls.currentSession.content = valid_content
    _tls.currentSession.compressedContent = None
    _tls.currentSession.compressedContent2 = None


def _compress_and_save_session_to_content2():
    logging.info("Compressing session because it is too large")

    _tls.currentSession.content['compressed'] = True

    # Let's compress it, save to datastore and leave everything as before

    valid_content = _tls.currentSession.content2

    _tls.currentSession.compressedContent2 = zlib.compress(json.dumps(valid_content))
    _tls.currentSession.content = None
    _tls.currentSession.content2 = None
    put_session_on_datastore(_tls.currentSession)
    _tls.currentSession.content2 = valid_content
    _tls.currentSession.content = json.loads(valid_content)
    _tls.currentSession.compressedContent = None
    _tls.currentSession.compressedContent2 = None


def save_session_to_datastore(force_saving_to_datastore=False, disable_redis_storage=False):
    '''
    Save the current session to persistence (only Redis by default)

    force_saving_to_datastore: if True, we will save the session in all persistence available (i.e. Redis and datastore), but not update the timestamp

    Note: if save_in_all_persistences is True, we will not save anything as it is not necessary (unless save_to_datastore_automatically is True)
    '''
    try:
        if _tls.save_to_datastore_automatically and not force_saving_to_datastore:
            logging.info("Not saving session to datastore because we are saving automatically")
            return

        logging.info("Saving session to datastore")
        # If session is expired for example there is nothing to save
        if not _tls.currentSession or _tls.currentSession.content is None:
            return

        _save_session_to_persistence(disable_redis_storage=disable_redis_storage)

    except Exception as e:
        from booking_process.utils.email.email_utils_third_party import notify_by_email_with_limit
        _log_error_traceback()
        message = str(e)
        message += "\nAt %s" % get_application_id()
        notify_by_email_with_limit("Error saving Session", message)


def safe_save_to_datastore():
    '''
        Used for recover all datas saved for all threads, before merge them and save them again all toguether.
        Ie, used in Booking2 for MIX_MULTIPLE_HOTELS: each hotel is saving its own data in the same Sid in parallel.
    '''
    try:
        logging.info("Saving safety session to datastore")
        session_id = get_session_id()
        if session_id:
            merged_data = _tls.currentSession.content

            already_saved_data = _get_current_session_from_persistence(session_id)
            if already_saved_data:
                merged_data = dict(list(already_saved_data.content.items()) + list(merged_data.items()))

            _tls.currentSession.content = merged_data
            _save_session_to_persistence()

    except Exception as e:
        from booking_process.utils.email.email_utils_third_party import notify_by_email_with_limit
        _log_error_traceback()
        message = str(e)
        message += "\nAt %s" % get_application_id()
        notify_by_email_with_limit("Error saving Session", message)


def safe_save_to_datastore_to_content2():
    '''
    This happens when we want to send this to prebooking, as it is in python3, pickling was causing problems
    so we do a json.dumps instead.

    No need to update redis as this is something that only happens at the end
    '''
    try:
        logging.info("Saving safety session to datastore")
        session_id = get_session_id()
        if session_id:
            merged_data = _tls.currentSession.content

            try:
                already_saved_data = _get_current_session_from_persistence(session_id)
                if already_saved_data and already_saved_data.content2:
                    merged_data.update(json.loads(already_saved_data.content2))
            except Exception as e:
                logging.warning("Error updating session!")

            _tls.currentSession.content = merged_data
            _tls.currentSession.content2 = json.dumps(merged_data)

            _save_session_to_persistence()

            try:
                put_session_on_datastore(_tls.currentSession)
            except Exception as e:
                _log_error_traceback()
                logging.warning("Session too large, we need to compress it")
                _compress_and_save_session_to_content2()

    except Exception as e:
        from booking_process.utils.email.email_utils_third_party import notify_by_email_with_limit
        _log_error_traceback()
        logging.warning("Error saving session: %s", e)
        message = str(e)
        message += "\nAt %s" % get_application_id()
        notify_by_email_with_limit("Error saving Session", message)


def clearSession():

    log_caller("Clearing session, possibly with personal Details!!!")

    if _tls.currentSession:
        _tls.currentSession.content = {}
        _tls.currentSession.content2 = None

        _save_session_to_persistence()

    else:
        _tls.requestSession = {}

    if DEV_SESSIONS:
        session_id = get_session_id()
        DEV_SESSIONS_STORAGE.pop(session_id, None)


def get_value_from_session_cache(key):
    sid = get_session_id()
    logging.info("getting from Cache Seekers SESSION var %s sid: %s", key, sid)

    session_content_cached = cache_seeker_utils.get(sid)

    if session_content_cached and session_content_cached.get(key):
        logging.info("Session from cache seekers recovered OK")
        return session_content_cached.get(key)

    return None


def updateSessionTransfer(sid):
    service = ServiceExternal.get_by_id(sid)

    service.content = _tls.currentSession.content
    service.compressed_content = b''
    service.timestamp = datetime.datetime.now()

    try:
        service.put()
    except Exception as e:
        _log_error_traceback()
        logging.info("Warning! Session too large for transfer session. Compressing...")
        service.compressed_content = zlib.compress(dumps_object(service.content), 1)
        service.content = ""
        service.put()

    service.content = _tls.currentSession.content
    service.compressed_content = b''


def saveSessionTransfer(sid, booking, reservation, status):
    service = ServiceExternal.get_by_id(sid)
    if not service:
        service = ServiceExternal(id=sid)

    service.booking_id = str(booking)
    service.reservation_id = str(reservation)
    service.sid = str(sid)
    service.content = _tls.currentSession.content
    service.compressed_content = b''
    service.timestamp = datetime.datetime.now()
    service.status = str(status)

    try:
        service.put()
    except Exception as e:
        _log_error_traceback()
        logging.info("Warning! Session too large for transfer session. Compressing...")
        service.compressed_content = zlib.compress(pickle.dumps(service.content), 1)
        service.content = ""
        service.put()

    service.content = _tls.currentSession.content
    service.compressed_content = b''


def recoverySessionTransfer(sid):
    '''
    To be called by any request that requires to have a session
    '''
    logging.info("Recoverying Session for Transfer!")

    # Explicitly create a new one
    if sid:
        sessionId = sid
        logging.info("SessionId: %s" % sessionId)

        currentSession = ServiceExternal.get_by_id(sessionId)

        if currentSession and not currentSession.content and currentSession.compressed_content:
            content = load_object(zlib.decompress(currentSession.compressed_content))
            currentSession.content = content
            currentSession.compressed_content = b''

        if currentSession:
            _tls.currentSession = currentSession
            _tls.save_to_datastore_automatically = True
            _tls.requestSession = {}
        else:
            _tls.currentSession = None
            _tls.save_to_datastore_automatically = True
            _tls.requestSession = {}


def session_transfer():
    try:
        if hasattr(_tls.currentSession, 'content'):
            transfer_session = _tls.currentSession.content.get("Transfer Session")
            return transfer_session
    except Exception as e:
        _log_error_traceback()
        logging.info("Exception accessing to session transfer!")
    return False

def session_is_paid():
    try:
        if hasattr(_tls.currentSession, 'content'):
            reservation_from_gateway = _tls.currentSession.content.get("_reservation from gateway")
            return reservation_from_gateway
    except Exception as e:
        _log_error_traceback()
        logging.info("Exception accessing to session paid!")
    return False



def _log_error_traceback():
    excinfo = sys.exc_info()
    message = ('Application: %s\nVersion: %s\n\n%s\n\n'
               % (os.getenv('APPLICATION_ID'),
                  os.getenv('CURRENT_VERSION_ID'),
                  ''.join(traceback.format_exception(*excinfo)),))

    logging.error(message)


def _update_with_persisted_session():
    try:
        # Update persisted session to allow multithreading with sessions
        persisted_session = _get_current_session_from_persistence(get_session_id())
        if not persisted_session:
            return

        _prepare_content_of_session_persistance(persisted_session, get_session_id())
        if not _tls.currentSession.content:
            logging.info("Session content has been cleared, will be saved without info")
            # Keep session history info
            _tls.currentSession.content[SESSION_CONSISTENCY_KEY] = persisted_session.content.get(SESSION_CONSISTENCY_KEY, [])
        else:
            if hasattr(_tls, 'keys_to_update') and _tls.keys_to_update:
                # logging.info("Updating session keys %s" % _tls.keys_to_update)
                for key in _tls.keys_to_update:
                    persisted_session.content[key] = _tls.currentSession.content.get(key)

            _tls.currentSession.content = persisted_session.content

        # _check_session_consistency(persisted_session)
        _add_session_modification_checkpoint()
    except Exception as e:
        from booking_process.utils.auditing.auditUtils import makeTraceback
        message_traceback = makeTraceback()
        logging.warning(message_traceback)
        logging.warning("Error checking session consistency: %s", e)



def _check_session_consistency(persisted_session):
    """
    Checker to be sure that the session is consistent and not corrupted
    If multiple threads handle same session at the same time, it could happen that the session is corrupted

    Notes:
        Actually mandatory unless we are sure that we don't have any issue more with personalDetails, etc...
        Measured performance and takes 0.2s to check the session consistency (locally)
    """
    logging.info("Checking session consistency")

    actual_session = _tls.currentSession
    modification_history = actual_session.content.get(SESSION_CONSISTENCY_KEY, []) if actual_session else None
    if not actual_session or not modification_history:
        return

    if not actual_session or not persisted_session:
        return

    persisted_modifications_history = persisted_session.content.get(SESSION_CONSISTENCY_KEY, [])

    def notify_error_consistency(message):
        actual_session_id = get_session_id()

        actual_session_history = '\n'.join([str(x) for x in modification_history])
        persisted_session_history = '\n'.join([str(x) for x in persisted_modifications_history])

        message = message + "\nSession id: %s\n\nActual session history:\n%s\n\n\nPersisted session history:\n%s" % (
        actual_session_id, actual_session_history, persisted_session_history)

        logging.warning(message)

        message = message.replace('\n', '<br>')
        # notify_exception('Session is corrupted, will be inconsistent data of this', message, add_hotel_info=True)

    if len(modification_history) != len(persisted_modifications_history):
        error_name = "Session is corrupted, inconsistent modification histories length"
        logging.warning(error_name)
        notify_error_consistency(error_name)
        return

    for i, modification in enumerate(modification_history):
        if modification['utc_timestamp'] != persisted_modifications_history[i]['utc_timestamp']:
            error_name = "Session is corrupted, inconsistent modification histories"
            logging.warning(error_name)
            notify_error_consistency(error_name)
            return


def _set_on_session(key, value):
    has_changed = False
    if (not _tls.currentSession) or session_expired():
        _tls.requestSession[key] = value
        return

    previous_value = _tls.currentSession.content.get(key)
    _tls.currentSession.content[key] = value
    if previous_value != value:
        has_changed = True

    if not hasattr(_tls, 'keys_to_update') or type(_tls.keys_to_update) is None:
        _tls.keys_to_update = [] # Cant be used set without refactor due to function name "set"

    if not key in _tls.keys_to_update:
        _tls.keys_to_update.append(key)

    return has_changed


def _add_session_modification_checkpoint():
    _tls.currentSession.content.setdefault(SESSION_CONSISTENCY_KEY, [])
    _tls.currentSession.content[SESSION_CONSISTENCY_KEY].append({
        'path' : os.environ.get('PATH_INFO'),
        'referer': os.environ.get('HTTP_REFERER'),
        'utc_timestamp': datetime.datetime.utcnow().isoformat()
    })

    logging.debug("Session modification checkpoint added: %s", _tls.currentSession.content[SESSION_CONSISTENCY_KEY][-1])


def put_session_on_datastore(tls_current_session):
    if DEV_SESSIONS:
        DEV_SESSIONS_STORAGE[get_session_id()] = tls_current_session
    else:
        tls_current_session.put()

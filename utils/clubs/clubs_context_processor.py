import logging

from booking_process.constants import advance_configs_names
from booking_process.constants.advance_configs_names import MEMBERS_CLUB_NAMESPACE, CLUB_SEGMENTATION_SECTION
from booking_process.constants.web_configs_names import CLUB_CONFIG, SOCIAL_LOGIN_INFO
from booking_process.constants.session_data import AGENT_ID, USER_LOGIN_INFO, LOCKED_EMAIL, CHOICE_CLUB_DISCOUNT
from booking_process.utils.clubs.club_constants import CLUB_SECTION_DATA, CLUB_REGISTER_LOGIN_DATA, EMAIL_ONLY_CLUBS
from booking_process.utils.clubs.club_methods import get_loyalty_seeker_url
from booking_process.utils.country.countryUtils import get_prefix_country_list, get_translated_country_list
from booking_process.utils.country import ALL_COUNTRIES_LIST, countryUtils
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.hotel_data import get_internal_url
from booking_process.utils.data_management.pictures_utils import getLogotypes, getExtraInfoPicture
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.development.dev_booking_utils import DEV_NAMESPACE, DEV, DEV_USE_LOCAL_LOYALTY_SEEKER, \
    DEV_USE_REAL_DATASTORE
from booking_process.utils.data_management.sections_utils import get_section_from_section_spanish_name, \
    get_section_from_section_spanish_name_with_properties
from booking_process.utils.data_management.pictures_utils import get_pictures_from_section_name
from booking_process.utils.data_management.web_page_property_utils import get_properties_for_entity
from booking_process.utils.language import language_utils
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.utils.mobile.mobile_utils import user_agent_is_mobile
from booking_process.utils.namespaces.namespace_utils import get_namespace, set_namespace
from booking_process.utils.session import session_manager
from booking_process.utils.templates.template_utils import build_template_2
from booking_process.utils.users.club_decorators import master_namespace_club
from paraty_commons_3.language_utils import get_language_locale, get_language_code
from booking_process.components.xeerpa.view import build_social_login_xeerpa
from booking_process.components.xeerpa.constants import XEERPA_CLUB_URL


def general_club_context():
    """Used for general params needed everywhere"""
    club_context = {
        'club_context': {
            'loyalty_seeker_endpoint': get_loyalty_seeker_url()
        },
    }

    if get_config_property_value(MEMBERS_CLUB_NAMESPACE):
        club_context['club_context'].update(club_master_namespace_context())

    return club_context


def _build_login_banner_club(context, login_club_banner_config, language):
    login_banner_info = get_pictures_from_section_name(login_club_banner_config, language)
    login_banner_info_section = get_section_from_section_spanish_name(login_club_banner_config, language)
    if not login_banner_info:
        return

    context['complete_language'] = language
    context['loyalty_seeker_endpoint'] = get_loyalty_seeker_url()
    context['login_banner_club'] = {
        'logotype': getLogotypes('club'),
        'pictures': login_banner_info,
        'section_info': login_banner_info_section,
        'country_list': countryUtils.get_translated_country_list(language)
    }

    club_send_password_popup_section = get_config_property_value(advance_configs_names.CLUB_SEND_PASSWORD_POPUP)
    if club_send_password_popup_section:
        context['club_send_password_popup'] = get_section_from_section_spanish_name(club_send_password_popup_section,language)
        context['club_send_password_toggled'] = get_pictures_from_section_name(club_send_password_popup_section,language)

    if get_config_property_value(advance_configs_names.LOCK_RATES_AS_PARTIAL_CLUB_REGISTER):
        context['login_banner_club']['lock_rates_login_is_partial'] = True

    members_club_config = get_config_property_value(advance_configs_names.MEMBERS_CLUB_SECTIONS)
    if members_club_config:
        members_club_section_name = members_club_config.split("@@")
        members_club_pictures = get_pictures_from_section_name(members_club_section_name[0], language)
        members_club_section = get_section_from_section_spanish_name(members_club_section_name[0], language)
        context['login_banner_club']['points_image'] = [x for x in members_club_pictures if x.get('title') == 'coin']
        context['login_banner_club']['register_logo'] = [x for x in members_club_pictures if x.get('title') == 'register_logo']

        club_section_configs = get_properties_for_entity(members_club_section.get('key'), language)
        if club_section_configs.get('login_title'):
            context['login_banner_club']['login_title'] = club_section_configs['login_title']
        if club_section_configs.get('thanks_register'):
            context['login_banner_club']['thanks_register'] = club_section_configs['thanks_register']
        if club_section_configs.get('recovery_password'):
            context['login_banner_club']['recovery_password'] = club_section_configs['recovery_password']

    section_advance_properties = get_properties_for_entity(login_banner_info_section.get('key'), language)

    context['login_banner_club']['login_extra_info'] = {
        'background': [x for x in login_banner_info if x.get('title') == 'background'],
        'icons': [x for x in login_banner_info if not x.get('title') == 'background'],
        'label_welcome_user': section_advance_properties.get('welcome_user_label'),
        'description_logged_user': section_advance_properties.get('description_logged_user'),
        'coin_name': section_advance_properties.get('coin_name')
    }

    if section_advance_properties.get('extra_info_club'):
        context['login_banner_club']['extra_info_club'] = get_section_from_section_spanish_name(section_advance_properties['extra_info_club'], language)
    if section_advance_properties.get('custom_logotype'):
        context['login_banner_club']['custom_logotype'] = section_advance_properties['custom_logotype']
    if section_advance_properties.get('promotions_accept_optional'):
        context['login_banner_club']['promotions_accept_optional'] = True

    if section_advance_properties.get('show_dni'):
        context['login_banner_club']['show_dni'] = section_advance_properties['show_dni']

    if section_advance_properties.get('login_subtitle'):
        context['login_banner_club']['login_subtitle'] = section_advance_properties['login_subtitle']

    if section_advance_properties.get('custom_see_advantages'):
        context['login_banner_club']['custom_see_advantages'] = section_advance_properties['custom_see_advantages']

    if section_advance_properties.get('custom_acceder'):
        context['login_banner_club']['custom_acceder'] = section_advance_properties['custom_acceder']

    if section_advance_properties.get('custom_unirme'):
        context['login_banner_club']['custom_unirme'] = section_advance_properties['custom_unirme']

    if section_advance_properties.get('custom_register_title'):
        context['login_banner_club']['custom_register_title'] = section_advance_properties['custom_register_title']

    if section_advance_properties.get('register_title_tab'):
        context['login_banner_club']['register_title_tab'] = section_advance_properties['register_title_tab']

    if section_advance_properties.get('login_title_tab'):
        context['login_banner_club']['login_title_tab'] = section_advance_properties['login_title_tab']

    if not context['login_banner_club'].get("login_title") and section_advance_properties.get('login_title'):
        context['login_banner_club']['login_title'] = section_advance_properties['login_title']

    club_register_welcome_popup_section = get_config_property_value(advance_configs_names.CLUB_REGISTER_WELCOME_POPUP)
    if club_register_welcome_popup_section:
        context['login_banner_club']['club_register_welcome_popup'] = get_section_from_section_spanish_name(club_register_welcome_popup_section, language)

    alternative_club_controller = get_config_property_value(advance_configs_names.ALTERNATIVE_CLUB_CONTROLLER)
    if alternative_club_controller and ('ap-' in get_namespace() or DEV and 'ap-' in DEV_NAMESPACE):
        context['login_banner_club']['confirm_enabled'] = True

    if get_config_property_value(MEMBERS_CLUB_NAMESPACE) and not DEV:
        context.update(club_master_namespace_context())

    context['club_configs'] = get_web_configuration(CLUB_CONFIG)

    if section_advance_properties.get('avoid_countries') or context['club_configs'].get('avoid_countries'):
        if context['club_configs'].get('avoid_countries'):
            avoid_countries = context['club_configs'].get('avoid_countries').split(';')
        else:
            avoid_countries = section_advance_properties['avoid_countries'].split(';')
        actual_country = session_manager.get('country')
        if actual_country in avoid_countries:
            context['avoid_club'] = True
            if context.get('login_banner_club'):
                del context['login_banner_club']


def _get_specific_picture_of_section(picture_name, section_pictures, multi_search=False):
    pictures_list = []
    for picture_element in section_pictures:
        if multi_search:
            if picture_name in picture_element.get('title'):
                pictures_list.append(picture_element)
        else:
            if picture_element.get('title') == picture_name:
                return picture_element

    return pictures_list


def _build_login_banner_club_v2(language):
    context = {}
    pictures_of_default_section = get_pictures_from_section_name(CLUB_SECTION_DATA, language, True)
    for pic in pictures_of_default_section:
        pic.update(get_properties_for_entity(pic.get('key'), language))

    context.setdefault('card_element', {}).setdefault('login_extra_info', {})
    context['banner_club'] = _get_specific_picture_of_section('banner_club_logotype', pictures_of_default_section)
    context['banner_club_logged'] = _get_specific_picture_of_section('banner_club_logotype_logged', pictures_of_default_section)
    context['banner_club_background'] = _get_specific_picture_of_section('banner_club_background', pictures_of_default_section)
    context['user_points_icon'] = _get_specific_picture_of_section('user_points_icon', pictures_of_default_section)
    context['accumulated_bookings'] = _get_specific_picture_of_section('accumulated_bookings', pictures_of_default_section)
    context['booking_benefit'] = _get_specific_picture_of_section('booking_benefit',pictures_of_default_section)
    context['more_info_club'] = _get_specific_picture_of_section('more_info_club', pictures_of_default_section)
    context['logout_label_text'] = _get_specific_picture_of_section('logout_label_text', pictures_of_default_section)
    context['more_icons_popup'] = _get_specific_picture_of_section('more_icons_popup', pictures_of_default_section)
    context['banner_club_icons'] = _get_specific_picture_of_section('advantage_', pictures_of_default_section, multi_search=True)
    context['main_description'] = _get_specific_picture_of_section('main_description', pictures_of_default_section)
    context['custom_title_mobile'] = _get_specific_picture_of_section('custom_title_mobile', pictures_of_default_section)
    context['logo_wrapper_mobile'] = _get_specific_picture_of_section('logo_wrapper_mobile', pictures_of_default_section)
    context['show_only_google_icon'] = _get_specific_picture_of_section('show_only_google_icon', pictures_of_default_section)
    context['is_booking3'] = True
    context['club_config'] = get_web_configuration(CLUB_CONFIG)
    context['loyalty_seeker_endpoint'] = get_loyalty_seeker_url()

    if context.get('show_only_google_icon'):
        context['google_icon'] = context['show_only_google_icon'].get('activate')

    if context.get('club_config') and context['club_config'].get('Coin name'):
        context['coin_name'] = context['club_config'].get('Coin name')

    if context.get('club_config') and context['club_config'].get('disable_points'):
        context['disable_points'] = True

    if context.get('club_config') and context['club_config'].get('show_prefix'):
        context['show_prefix'] = True
        if get_config_property_value(advance_configs_names.BOOKING3_TELEPHONE_PREFIX_SELECTOR):
            context['prefix_country_list'] = get_prefix_country_list(language)
            context['booking3_telephone_prefix_selector'] = True
        context['translated_country_list'] = get_translated_country_list(language)

    section_login_register_data = get_section_from_section_spanish_name(CLUB_REGISTER_LOGIN_DATA, language)
    extra_login_register_data = get_properties_for_entity(section_login_register_data.get('key'), language)
    extra_login_register_data.update(section_login_register_data)
    context['login_register_data'] = extra_login_register_data
    context['disabled_register_fields'] = []
    if extra_login_register_data.get("disabled_register_fields"):
        context['disabled_register_fields'] = extra_login_register_data.get("disabled_register_fields").split(';')

    if context.get('club_config') and context['club_config'].get('club_version'):
        context['banner_version'] = context['club_config'].get('club_version')
    else:
        context['banner_version'] = 'v3'

    if context.get('club_config') and context['club_config'].get('login_form_full_screen'):
        context['login_form_full_screen'] = True
    context['language'] = language
    context['country_list'] = countryUtils.get_translated_country_list(language)

    context['namespace'] = get_namespace()

    user_info = session_manager.get(USER_LOGIN_INFO)
    if user_info:
        context['logged_user_info'] = user_info

    locked_email = session_manager.get(LOCKED_EMAIL)
    if locked_email:
        context['locked_email'] = locked_email

    if get_config_property_value(MEMBERS_CLUB_NAMESPACE) and (not DEV or context['banner_version'] in EMAIL_ONLY_CLUBS):
        context.update(club_master_namespace_context())

    if session_manager.get(AGENT_ID) and context.get('club_config') and context['club_config'].get('callcenter_generate_password'):
        context['callcenter_generate_password'] = True

    if session_manager.get(AGENT_ID) and context.get('club_config') and context['club_config'].get('callcenter_optional_register_fields'):
        context['callcenter_optional_register_fields'] = context['club_config'].get('callcenter_optional_register_fields')

    if context.get('club_config') and context['club_config'].get('custom_required_inputs'):
        context['custom_required_inputs'] = context['club_config'].get('custom_required_inputs')

    if context.get('club_config') and context['club_config'].get('register_skip_fields'):
        context['register_skip_fields'] = context['club_config'].get('register_skip_fields')

    if context.get('club_config') and context['club_config'].get('register_alt_field_labels'):
        field_labels = context['club_config'].get('register_alt_field_labels').split(";")
        alt_field_labels = {}
        for label in field_labels:
            label_key, label_data = label.split("=")
            label_value = get_web_dictionary(language).get(label_data)
            if label_value:
                alt_field_labels[label_key] = label_value
        context['register_alt_field_labels'] = alt_field_labels

    custom_inputs_club_section = get_config_property_value(advance_configs_names.CUSTOM_CLUB_INPUTS)
    if custom_inputs_club_section:
        custom_inputs_club_pictures = get_pictures_from_section_name(custom_inputs_club_section,language)
        if custom_inputs_club_pictures:
            for picture in custom_inputs_club_pictures:
                picture.update(getExtraInfoPicture(picture['key'], language, True))
            context["custom_inputs"] = custom_inputs_club_pictures

    users_segmentation = context.get('club_config') and context['club_config'].get('users_segmentation')
    if users_segmentation:
        context['segmentation_config'] = get_segmentation_config(language)

    choice_club_discount = session_manager.get(CHOICE_CLUB_DISCOUNT)
    if choice_club_discount and choice_club_discount == 'disabled':
        context['disable_club_discount'] = True

    social_login_info = get_web_configuration(SOCIAL_LOGIN_INFO)
    xeerpa_login = get_config_property_value(XEERPA_CLUB_URL)

    if social_login_info:
        context['facebook_app_id'] = social_login_info.get("facebook_app_id")
        context['google_client_id'] = social_login_info.get("google_client_id")
        context['allow_google_onetap'] = social_login_info.get("allow_google_onetap") and not session_manager.get(USER_LOGIN_INFO)
        context['locale'] = get_language_locale(language)
    elif xeerpa_login:
        context['xeerpa_login'] = build_social_login_xeerpa(custom_class=context['banner_version'])

    if club_non_required_fields := get_config_property_value(advance_configs_names.CLUB_NON_REQUIRED_FIELDS):
        context['club_non_required_fields'] = club_non_required_fields

    context.update(language_utils.get_web_dictionary(language))

    return context


def build_only_email_club_login_popup(context, property_name, language):
    club_config = context.get('club_config') or get_web_configuration(CLUB_CONFIG)
    login_popup = club_config.get(property_name)
    club_version = club_config.get('club_version', '')

    if not login_popup or club_version not in {'v6'}:
        return

    login_popup_section = get_section_from_section_spanish_name_with_properties(login_popup, language)
    popup_context = {
        'club_config': club_config,
        'popup_delay': login_popup_section.get('popup_delay', 5),
        'show_on_click': login_popup_section.get('show_on_click', ''),
        'title_content': login_popup_section.get('subtitle', ''),
        'direct_search_url': context.get('direct_search_url', ''),
        'requestFrom': context.get('requestFrom', ''),
        'namespace': get_config_property_value(advance_configs_names.MEMBERS_CLUB_NAMESPACE) or get_namespace(),
        'language': language,
        'language_code': get_language_code(language),
        **get_web_dictionary(language)
    }

    for picture in get_pictures_from_section_name(login_popup, language):
        picture_name = picture.get('spanish_title')
        if picture_name:
            popup_context[picture_name] = picture

    target_template = \
        f"booking_2/booking_process_v1/popups_info/_club_popup_{club_version}.html" if not user_agent_is_mobile() \
            else f"mobile/booking_process/login_club/{club_version}/_club_popup_{club_version}.html"

    context['club_popup'] = build_template_2(target_template, popup_context)


@master_namespace_club
def get_segmentation_config(language):
    segmentation_config = {}

    try:
        section_name_to_use = get_config_property_value(CLUB_SEGMENTATION_SECTION)
        if section_name_to_use:
            section = get_section_from_section_spanish_name(section_name_to_use, language)
            if section:
                advanced_properties = get_properties_for_entity(section.get('key'), language)
                segmentation_config = {
                    'title': advanced_properties.get('club_segmentation_title', ''),
                    'required': advanced_properties.get('club_segmentation_required', False),
                    'segments': []
                }
                segments = get_pictures_from_section_name(section_name_to_use, language)
                for segment in segments:
                    segment.update(getExtraInfoPicture(segment['key'], language, True))
                    segmentation_config['segments'].append(
                        {'segment_name': segment.get('title', ''),
                         'list_id': segment.get('list_id', '')})

    except Exception as e:
        logging.error(f'Error processing club users segmentation config: {e}')

    return segmentation_config


def build_banner_club_react(language):
    context = {}
    pictures_of_default_section = get_pictures_from_section_name(CLUB_SECTION_DATA, language, True)
    for pic in pictures_of_default_section:
        pic.update(get_properties_for_entity(pic.get('key'), language))

    context['logotype'] = _get_specific_picture_of_section('banner_club_logotype', pictures_of_default_section)
    context['more_info_club'] = _get_specific_picture_of_section('more_info_club', pictures_of_default_section)
    context['main_description'] = _get_specific_picture_of_section('main_description', pictures_of_default_section)
    context['user_logged_text'] = _get_specific_picture_of_section('user_logged_text', pictures_of_default_section)
    context['banner_club_icons'] = _get_specific_picture_of_section('advantage_', pictures_of_default_section, multi_search=True)

    section_login_register_data = get_section_from_section_spanish_name(CLUB_REGISTER_LOGIN_DATA, language)
    extra_login_register_data = get_properties_for_entity(section_login_register_data.get('key'), language)
    extra_login_register_data.update(section_login_register_data)
    context['login_register_data'] = extra_login_register_data

    context['register_inputs'] = build_register_inputs(language, extra_login_register_data)

    return context


def club_master_namespace_context():
    context = {}
    master_namespace = get_config_property_value(MEMBERS_CLUB_NAMESPACE)
    if 'r__' in master_namespace:
        context['club_remote_url'] = get_internal_url(master_namespace)

    if not DEV or (DEV and not DEV_USE_REAL_DATASTORE):
        context['club_internal_url'] = get_internal_url()

    context['club_namespace'] = master_namespace

    return context


def build_register_inputs(language, extra_login_register_data):
    country_options = []
    translations = get_web_dictionary(language)
    default_value = {
        'name': translations.get('T_pais'),
        'default_value': True,
        'disabled': True
    }
    country_options.append(default_value)
    for key, value in ALL_COUNTRIES_LIST.items():
        country_data = {
            'name': value,
            'value': key
        }
        country_options.append(country_data)

    city_options = [
        {
            'name': translations.get('T_ciudad'),
            'default_value': True,
            'disabled': True
        }
    ]

    input_data = [
        {
            'id': 'name',
            'type': 'text',
            'label': translations.get('T_nombre'),
            'visible': True
        },
        {
            'id': 'surname',
            'type': 'text',
            'label': translations.get('T_apellidos'),
            'visible': True
        },
        {
            'id': 'birthday',
            'type': 'date',
            'label': translations.get('T_fecha_nacimiento'),
            'visible': True
        },
        {
            'id': 'dni',
            'type': 'text',
            'label': translations.get('T_DNI'),
            'visible': False
        },
        {
            'id': 'address',
            'type': 'text',
            'label': translations.get('T_direccion'),
            'visible': True
        },
        {
            'id': 'pais',
            'type': 'select',
            'label': translations.get('T_pais'),
            'select_options': country_options,
            'visible': True
        },
        {
            'id': 'city',
            'type': 'select',
            'label': translations.get('T_ciudad'),
            'select_options': city_options,
            'visible': True
        },
        {
            'id': 'email',
            'type': 'email',
            'label': translations.get('T_email'),
            'visible': True
        },
        {
            'id': 'telephone',
            'type': 'tel',
            'label': translations.get('T_telefono'),
            'visible': True
        },
        {
            'id': 'password',
            'type': 'password',
            'label': translations.get('T_contrasena'),
            'visible': True
        },
        {
            'id': 'repeat_password',
            'type': 'password',
            'label': translations.get('T_repita_cotrasena'),
            'custom_validation': True,
            'visible': True
        }
    ]

    if extra_login_register_data:
        hidden_fields = extra_login_register_data.get('disabled_register_fields')
        if hidden_fields:
            for field in hidden_fields.split(';'):
                input_data.remove(next((item for item in input_data if item['id'] == field), None))

        visible_fields = extra_login_register_data.get('visible_fields')
        if visible_fields:
            for field in visible_fields.split(';'):
                for input_item in input_data:
                    if input_item['id'] == field:
                        input_item['visible'] = True
                        break

        optional_fields = extra_login_register_data.get('optional_fields')
        if optional_fields:
            for field in optional_fields.split(';'):
                for input_item in input_data:
                    if input_item['id'] == field:
                        input_item['optional'] = True
                        break

    return input_data

import logging

from booking_process.constants import advance_configs_names
from booking_process.constants.advance_configs_names import RATES_LOGIN_USERS, USE_HOLIDAY_STRUCTURE_FOR_RESULTS, \
    RATES_LOGIN_HIDDEN
from booking_process.constants.integrationsConstants import LOGIN_USER_RATES_XML
from booking_process.constants.web_configs_names import CLUB_LOCK_RATES, CLUB_CONFIG
from booking_process.libs.communication import directDataProvider
from booking_process.utils.auditing import auditUtils
from booking_process.utils.booking.bookingUtils import get_rate_local_name
from booking_process.utils.bookingConstants import ONLY_LEVELS
from booking_process.constants.session_data import LOCKED_RATES_CLUB, PRICE_OPTION_KEY_PREFIX, SELECTED_OPTION_KEY, \
    USER_LOGIN_INFO, SELECTED_RATE_NEED_LOGIN, C_TURNO_VALUE, MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX, \
    AVOID_CLUB_RATES_FILTER_CLUB_DISABLED, SEARCH_KEY_PREFIX
from booking_process.utils.clubs.club_constants import EMAIL_ONLY_CLUBS
from booking_process.utils.clubs.club_levels import retreive_all_information_of_club_levels, retreive_user_level
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.rates_data import get_rate_from_key
from booking_process.utils.data_management.integrations_utils import get_integration_rate_map_properties
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.email.email_utils_third_party import notify_exception
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.packages import package_utils
from booking_process.utils.shopping_cart.booking_cart_utils import is_booking_cart_v2, is_allowed_only_one_room_for_cart
from booking_process.utils.shopping_cart.booking_cart_v2 import ShoppingCartV2
from booking_process.utils.users.users_methods import club_is_active

from booking_process.utils.session import session_manager


def join_lock_rates_with_map(results, remove_club_rates=True):
    '''Will join locked rates with normal rates'''
    club_lock_rates = get_web_configuration(CLUB_LOCK_RATES)

    if not club_lock_rates:
        return

    lock_config_rates = list(club_lock_rates.keys())
    lock_config_rates = list(map(lambda x: x.strip(), lock_config_rates))

    locked_rates_session_map = {}

    for room_element in results:
        is_rooms_without_merge = type(room_element) is list

        if is_rooms_without_merge:
            for room in room_element:
                rates_to_move = []
                rates_to_move += _process_lock_rates(club_lock_rates, lock_config_rates, room)
                rates_without_movement = list(filter(lambda rate_element: not rate_element.get('target_rate_move'), room.get('rateResult')))
                affected_rates_boards = _merge_boards_with_lock(rates_to_move, rates_without_movement, room.get('rateResult'))
                for room_key in room.get('roomStructure'):
                    update_locked_rates_session_map(affected_rates_boards, locked_rates_session_map, room_key, is_rooms_without_merge)

                room['rateResult'] = rates_without_movement if remove_club_rates else room.get('rateResult')
                room['has_lock_boards'] = True

        else:
            rates_to_move = _process_lock_rates(club_lock_rates, lock_config_rates, room_element)
            rates_without_movement = list(filter(lambda rate_element: not rate_element.get('target_rate_move'), room_element.get('rateResult')))
            affected_rates_boards = _merge_boards_with_lock(rates_to_move, rates_without_movement, room_element.get('rateResult'))
            room_keys = []
            for room_key in room_element.get('roomStructure'):
                room_keys.append(room_key.get('roomKey'))

            for room_key in room_element.get('roomStructure'):
                update_locked_rates_session_map(affected_rates_boards, locked_rates_session_map, room_key,
                                                is_rooms_without_merge, room_keys)

            room_element['rateResult'] = rates_without_movement if remove_club_rates else room_element.get('rateResult')
            room_element['has_lock_boards'] = True

    session_manager.set(LOCKED_RATES_CLUB, locked_rates_session_map)


def update_locked_rates_session_map(affected_rates_boards, locked_rates_session_map, room_element,
                                    is_rooms_without_merge, room_keys=[]):
    room_key = room_element.get('roomKey')
    try:
        for rate_key, rate_data in affected_rates_boards.items():
            mapping_key = room_key if is_rooms_without_merge else "_".join(room_keys)
            locked_rates_session_map.setdefault(mapping_key, {}).setdefault(rate_key, {})[
                'related_lock_rate'] = rate_data['related_lock_rate']
            for board_key, board_price in rate_data['boards'].items():
                if room_element.get('occupancy_hostel'):
                    for room_occupancy in room_element['occupancy_hostel'].split(';'):
                        locked_rates_session_map[mapping_key][rate_key].setdefault('boards', {}).setdefault(
                            board_key, {})[room_occupancy] = board_price
                else:
                    locked_rates_session_map[mapping_key][rate_key].setdefault('boards', {}).setdefault(
                        board_key, {})[room_element.get('selected_capacity_string')] = board_price

                    if room_element.get('capacities'):
                        for capacity in room_element['capacities']:
                            previous_price = locked_rates_session_map[mapping_key][rate_key].get('boards', {}).get(
                                    board_key, {}).get(capacity)
                            if capacity != room_element['selected_capacity_string'] and not previous_price:
                                locked_rates_session_map[mapping_key][rate_key].setdefault('boards', {}).setdefault(
                                    board_key, {})[capacity] = board_price

    except Exception as e:
        traceback = auditUtils.makeTraceback()
        message = f'Error while trying to update locked rates for room: {room_key}'
        logging.error(message)
        logging.error(traceback)
        notify_exception(message, traceback, add_hotel_info=True)


def _merge_boards_with_lock(rates_to_move, rates_without_movement, room_rate_result):
    affected_rate_board = {}
    is_holiday_structure = get_config_property_value(USE_HOLIDAY_STRUCTURE_FOR_RESULTS)

    rates_to_move = [x for x in rates_to_move if not x[1].get('package_info')]

    for rate_element in rates_without_movement:
        if rate_element.get('package_info'):
            continue

        rate_identifiers = set(rate_element.get('identifiers_list', [rate_element.get("rateIdentifier")]))
        rates_club_to_append_with_identifiers = [x for x in rates_to_move if rate_identifiers & set(x[0])]
        rates_club_to_append = [x[1] for x in rates_club_to_append_with_identifiers]

        rates_club_to_append = sorted(rates_club_to_append, key=lambda x: float(x.get('minValue')), reverse=True)

        # If has same values, reorder with levels
        list_min_values = list(map(lambda x: float(x.get('minValue')), rates_club_to_append))
        if len(list_min_values) != len(set(list_min_values)):
            rates_club_to_append = _reorder_by_club_levels(rates_club_to_append)

        if not rates_club_to_append:
            continue

        expensive_rate_lock = rates_club_to_append[0]
        lock_rate_key = expensive_rate_lock.get('rateStructure')[2]
        dict_boards_lock = dict([(x.get('regimenStructure').get('regimenName'), x) for x in expensive_rate_lock.get('regimenResult')])

        for board_element in rate_element.get('regimenResult'):
            board_name = board_element.get('regimenStructure').get('regimenName')
            if dict_boards_lock.get(board_name):
                price_results = dict_boards_lock.get(board_name).get('roomResults')
                target_price = price_results.get('promotionTotal') or price_results.get('total')
                target_price_per_night = price_results.get('promotionPerDay') or price_results.get('perDay')
                target_price = float(target_price)
                target_price_per_night = float(target_price_per_night)
                rate_key, board_key = _build_locks_keys(rate_element, board_element)
                affected_rate_board.setdefault(rate_key, {}).setdefault('boards', {}).setdefault(board_key,target_price)
                affected_rate_board[rate_key]['related_lock_rate'] = lock_rate_key
                board_element['lock_board'] = dict_boards_lock.get(board_name)
                if is_holiday_structure:
                    uuid = board_element.get('lock_board', {}).get('roomResults', {}).get('uuid')
                    original_uuid = session_manager.get(PRICE_OPTION_KEY_PREFIX + uuid)
                    if original_uuid:
                        logging.debug('Holiday structure - Getting original rate key for lock from %s to %s' % (lock_rate_key, original_uuid[0]))
                        board_element['lock_board']['related_lock_rate'] = original_uuid[0]
                    else:
                        logging.warning('Holiday structure - Original rate key not found for uuid %s' % uuid)
                else:
                    board_element['lock_board']['related_lock_rate'] = lock_rate_key

                related_lock_rate = list(filter(lambda x: x.get('rateStructure')[2] == lock_rate_key, room_rate_result))
                if related_lock_rate:
                    related_board = list(filter(lambda x: x.get('regimenStructure', {}).get('regimenKey') == board_key, related_lock_rate[0].get('regimenResult')))
                    if related_board:
                        room_results = board_element.get('roomResults', {})
                        price_without_club = room_results.get('promotionTotal') or room_results.get('total', target_price)
                        price_without_club_per_day = room_results.get('promotionPerDay') or room_results.get('perDay', target_price_per_night)
                        related_board[0]['price_without_club'] = price_without_club
                        related_board[0]['price_without_club_per_day'] = price_without_club_per_day

    return affected_rate_board


def _reorder_by_club_levels(rates_club_to_append):
    all_levels_information = retreive_all_information_of_club_levels()
    list_all_levels = list(all_levels_information.values())

    # Join all rates list of each level
    all_rates_ordered = []
    for level in list_all_levels:
        if level.get('rates'):
            all_rates_ordered += level['rates']

    rates_club_to_append = sorted(rates_club_to_append, key=lambda x: all_rates_ordered.index(x.get('rateIdentifier')) if x.get('rateIdentifier') in all_rates_ordered else 999)

    return rates_club_to_append


def _build_locks_keys(rate_element, board_element):
    rate_key = rate_element.get('rateStructure')[2]
    board_key = board_element['regimenStructure']['regimenKey']

    target_uuid = board_element.get('roomResults', {}).get('uuid')

    # Needed for holiday structure
    session_info = session_manager.get(PRICE_OPTION_KEY_PREFIX + str(target_uuid))
    if session_info:
        rate_key, room_key, board_key = session_manager.get(PRICE_OPTION_KEY_PREFIX + str(target_uuid))[0:3]

    return rate_key, board_key


def _process_lock_rates(club_lock_rates, lock_config_rates, room_element):
    rates_to_move = []
    for rate_element in room_element.get('rateResult'):
        target_rate = _mark_rates_to_move_with_lock(rate_element, lock_config_rates, club_lock_rates)
        if target_rate:
            rates_to_move.append((target_rate['target_rate_move'], target_rate))

    return rates_to_move


def _mark_rates_to_move_with_lock(rate_element, lock_config_rates, club_lock_rates):
    rate_identifiers = rate_element.get('identifiers_list', [rate_element.get("rateIdentifier")])

    if set(lock_config_rates) & set(rate_identifiers):
        rate_element['target_rate_move'] = [club_lock_rates.get(x) for x in rate_identifiers]
        return rate_element


def rate_is_for_club(rate_identifier=None):
    if not rate_identifier:
        selectedOption = session_manager.get(SELECTED_OPTION_KEY)
        selectedPrice = []
        for partialSelected in selectedOption.split(";"):
            selectedPrice.append(session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected))

        # TODO: Avoid this call to datastore everytime with usersclub rates
        rate_key = selectedPrice[0][0]
        if package_utils.is_package(rate_key):
            package_key, rate_key = package_utils.get_package_and_rate_from_key(rate_key)

        rate_info = get_rate_from_key(rate_key, SPANISH)
        if not rate_info:
            # Todo: Use a method for rate retrievement, not a direct datastore call
            rate_info = directDataProvider.getEntity('MultiRate', rate_key)
            if not rate_info:
                return

            rate_identifier = rate_info.localName
        else:
            rate_identifier = rate_info['identifier']

    rates_to_loggin = get_rates_to_login_formatted()

    return rate_identifier.lower().strip() in rates_to_loggin


def rate_need_login() -> bool:
    """
	This method check if rate need login and if we need to login.
	:return: True if rate need login, False otherwise.
	"""
    try:
        club_config = get_web_configuration(CLUB_CONFIG)
        is_club_rate = rate_is_for_club()

        if is_club_rate and not session_manager.get(USER_LOGIN_INFO) or club_config.get('club_version') in EMAIL_ONLY_CLUBS:
            session_manager.set(SELECTED_RATE_NEED_LOGIN, True)
            return True

        rates_to_loggin = get_rates_to_login_formatted()

        selectedOption = session_manager.get(SELECTED_OPTION_KEY)
        selectedPrice = []
        for partialSelected in selectedOption.split(";"):
            selectedPrice.append(session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected))

        promotions_to_loggin = [x for x in rates_to_loggin if 'promotion=' in x]
        promotions_to_loggin = [x.replace('promotion=', '') for x in promotions_to_loggin]
        if promotions_to_loggin and len(selectedPrice[0]) > 3:
            promotion_key = selectedPrice[0][-1]
            promtion_identifier = directDataProvider.getEntity('Promotion', promotion_key).identifier
            if promtion_identifier in promotions_to_loggin:
                return True

        return False

    except Exception as e:
        logging.error("Something was wrong at method 'rate_need_login': %s" % e)

    return False


def rates_for_logged_users(rates_login_list, results):
    '''
	:param rates_login_list: Rates for logged users only
	:param results: Restuls to build rooms
	'''

    rates_login_list = list(map(lambda x: x.lower().strip(), rates_login_list))
    promotions_login_list = list(filter(lambda x: 'promotion=' in x, rates_login_list))
    promotions_login_list = list(map(lambda x: x.replace('promotion=', ''), promotions_login_list))
    rates_login_list = list(filter(lambda x: 'promotion=' not in x, rates_login_list))
    hidden_club_rates = get_config_property_value(advance_configs_names.RATES_LOGIN_HIDDEN)
    club_enabled = club_is_active()

    # TODO - Refactor this method to use get_forbidden_club_rates
    rates_login_list, rates_to_avoid = separate_allowed_forbidden_club_rates(rates_login_list)

    precio_desde_standard = 0
    precio_desde_user = 0

    if session_manager.get(AVOID_CLUB_RATES_FILTER_CLUB_DISABLED):
        logging.info('We dont want to filter club rates, show anyway for this search')
        return results, precio_desde_standard, precio_desde_user

    logging.info("rates_for_logged_users. Rates configuration found for: %s ", rates_login_list)

    hide_always_login_rates = get_config_property_value(advance_configs_names.RATES_LOGIN_HIDDEN)

    for room in results:

        if type(room) is dict:
            for rate in room.get('rateResult', []):
                # Comparativa
                # rate_list_info = rate.get('rateStructure', [])
                # rate_name = rate_list_info[0] if rate_list_info else None

                rate_identifier = [rate.get('rateIdentifier').lower().strip()] if rate.get('rateIdentifier') else ''
                if not rate_identifier and rate.get('identifiers_list'):
                    rate_identifier = [x.lower() for x in [l for l in rate.get('identifiers_list') if l]]

                if _check_rate_login(rate_identifier, rates_login_list) or _check_promotion_login(rate, promotions_login_list):
                    rate['logged_users'] = True
                    room['room_has_loggin'] = True
                    if not precio_desde_user or precio_desde_user > float(rate['minValue']):
                        precio_desde_user = float(rate['minValue'])

                    if hidden_club_rates == ONLY_LEVELS and (rate_identifier and rates_to_avoid) and (set(rate_identifier) & set(rates_to_avoid)):
                        rate['rate_to_avoid'] = True

                else:
                    if (rate_identifier and rates_to_avoid) and (set(rate_identifier) & set(rates_to_avoid)):
                        rate['rate_to_avoid'] = True
                    if not precio_desde_standard or precio_desde_standard > float(rate['minValue']):
                        precio_desde_standard = float(rate['minValue'])

            room['rateResult'] = [x for x in room.get('rateResult', []) if not x.get('rate_to_avoid')]

        if type(room) is list:
            for room_element in room:
                for rate in room_element.get('rateResult', []):
                    rate_identifier = rate.get('rateIdentifier').lower().strip() if rate.get('rateIdentifier') else ''
                    if not rate_identifier and rate.get('identifiers_list'):
                        rate_identifier = [x.lower() for x in [l for l in rate.get('identifiers_list') if l]]

                    if _check_rate_login(rate_identifier, rates_login_list) or _check_promotion_login(rate, promotions_login_list):
                        # logging.info("special logged RATE found: %s ", rate_identifier)

                        if not type(rate_identifier) is list:
                            rate_identifier = [rate_identifier]

                        rate['logged_users'] = True
                        room_element['room_has_loggin'] = True
                        if not precio_desde_user or precio_desde_user > float(rate['minValue']):
                            precio_desde_user = float(rate['minValue'])

                        if hidden_club_rates == ONLY_LEVELS and (rate_identifier and rates_to_avoid) and (set(rate_identifier) & set(rates_to_avoid)):
                            rate['rate_to_avoid'] = True

                    else:
                        if not type(rate_identifier) is list:
                            rate_identifier = [rate_identifier]

                        if (rate_identifier and rates_to_avoid) and (set(rate_identifier) & set(rates_to_avoid)):
                            rate['rate_to_avoid'] = True

                        if not precio_desde_standard or precio_desde_standard > float(rate['minValue']):
                            precio_desde_standard = float(rate['minValue'])

                room_element['rateResult'] = [x for x in room_element.get('rateResult', []) if not x.get('rate_to_avoid')]

    turno = session_manager.get(C_TURNO_VALUE)
    rates_lock = get_config_property_value(advance_configs_names.USER_RATES_LOCKER)
    rates_lock_version = get_config_property_value(advance_configs_names.RATES_LOCK_VERSION)
    if not turno and rates_lock and not session_manager.get(USER_LOGIN_INFO) and club_enabled:
        join_lock_rates_with_map(results, rates_lock_version != 'toggle_discount')
        if not club_rates_in_results(results) and not rates_lock_version == 'toggle_discount':
            precio_desde_user = 0

    if not club_is_active() or ((not session_manager.get(USER_LOGIN_INFO) and not turno) and hide_always_login_rates and hide_always_login_rates != ONLY_LEVELS):
        # FILTER LOGGED RATES FOR NOT LOGGED USERS
        logging.info("Removing rates for users because no one is logged or club is disabled")

        for room in results:
            if type(room) is list:
                for individual_choice in room:
                    filtered_rates = [x for x in individual_choice.get('rateResult', []) if not x.get('logged_users')]
                    individual_choice['rateResult'] = filtered_rates
                    individual_choice['room_has_loggin'] = False
                    precio_desde_user = 0

            else:
                filtered_rates = [x for x in room.get('rateResult', []) if not x.get('logged_users')]
                room['rateResult'] = filtered_rates
                room['room_has_loggin'] = False
                precio_desde_user = 0

    return results, precio_desde_standard, precio_desde_user


def separate_allowed_forbidden_club_rates(rates_login_list):
    try:
        rates_to_avoid = []
        all_levels_info = retreive_all_information_of_club_levels()
        rates_user_levels = rates_has_user_level()
        hidden_club_rates = get_config_property_value(advance_configs_names.RATES_LOGIN_HIDDEN)
        user_information = session_manager.get(USER_LOGIN_INFO)

        if rates_user_levels and user_information:
            logging.info("Filtering rates by users levels")
            logging.info(rates_user_levels)
            logging.debug('User info:')
            logging.debug(user_information)

            if user_information.get('external_user'):
                user_level = user_information.get('category')
            else:
                idmember = user_information.get('idmember')
                user_level = retreive_user_level(idmember)
                if user_level:
                    user_level = user_level.get('category')

            if all_levels_info:
                logging.info("Actual user level is: %s" % user_level)
                match_level = all_levels_info.get(user_level)
                if match_level:
                    rates_to_use_for_club = match_level.get('rates')
                else:
                    rates_to_use_for_club = []
            else:
                rates_to_use_for_club = rates_user_levels.get(user_level, [])

            logging.info("Actual user category is: %s" % user_level)
            logging.info("Offered rates categories: %s" % rates_to_use_for_club)
            all_available_rates = rates_login_list
            rates_login_list = list(map(lambda x: x.lower(), rates_to_use_for_club))
            rates_to_avoid = list(filter(lambda x: x not in rates_login_list, all_available_rates))

        if rates_user_levels and not user_information and (not hidden_club_rates or hidden_club_rates == ONLY_LEVELS):
            logging.info("Actual user category is: Unregistered")
            all_available_rates = rates_login_list
            user_level = get_level_from_points(0)
            if user_level:
                rates_login_list = list(map(lambda x: x.lower(), rates_user_levels.get(user_level, [])))
                rates_to_avoid = list(filter(lambda x: x not in rates_login_list, all_available_rates))
            elif hidden_club_rates == ONLY_LEVELS:
                leveled_rates = []
                for level_id, level_info in all_levels_info.items():
                    if level_info.get('rates'):
                        leveled_rates += level_info['rates']

                leveled_rates = [x.lower().strip() for x in leveled_rates]

                rates_to_avoid = [x for x in all_available_rates if x in rates_login_list and x in leveled_rates]

    except Exception as e:
        message = auditUtils.makeTraceback()
        logging.error("Error while trying to filter club rates")
        logging.error(message)
        notify_exception("Error while trying to filter club rates", message, add_hotel_info=True)
        rates_to_avoid = rates_login_list
        rates_login_list = []

    return rates_login_list, rates_to_avoid


def get_forbidden_club_rates() -> list:
    """
    Get the club rates that should be hidden for current user.

    Cases:
        - Club is not active (newsletter club as well), so all club rates are hidden
        - Club is active but user is not logged and there is a configuration to hide club rates, so all club rates are hidden
        - Club is active and user is not logged and there is a configuration to hide only level rates, so only level rates are hidden
        - Club is active and user is logged, so all rates except the current user level rates are hidden

    :return: List of forbidden club rate names
    """
    club_enabled = club_is_active()
    hidden_club_rates = get_config_property_value(RATES_LOGIN_HIDDEN)
    all_club_rates = [x.strip().lower() for x in get_all_club_rates() if 'promotion=' not in x]
    user_info = session_manager.get(USER_LOGIN_INFO)
    forbidden_rates = []

    if club_enabled:
        allowed_rates, forbidden_rates = separate_allowed_forbidden_club_rates(all_club_rates)

    if not club_is_active() or (not user_info and hidden_club_rates and hidden_club_rates != ONLY_LEVELS):
        logging.info('Filtering club rates because club is not active or user is not logged')
        forbidden_rates = all_club_rates

    logging.info('Forbidden rates:')
    logging.info(forbidden_rates)

    return forbidden_rates


def _check_rate_login(rate_identifier, rates_login_list):
    lower_rates_login_list = [identifier_element.lower() for identifier_element in rates_login_list]

    if not rate_identifier:
        return False
    if type(rate_identifier) is list:
        matched_elements = [x for x in rate_identifier if x and x.lower() in lower_rates_login_list]
    else:
        matched_elements = rate_identifier.lower() in lower_rates_login_list

    if matched_elements:
        return True


def _check_promotion_login(rate_information, promotions_list):
    if not promotions_list:
        return False

    for regimen_element in rate_information.get('regimenResult'):
        if regimen_element.get('roomResults', {}).get('promotionIdentifier') in promotions_list:
            return True

    return False


def get_level_from_points(points):
    user_levels_config = get_config_property_value(advance_configs_names.MEMBERS_CLUB_LEVELS)
    levels_config = 'levels'

    if not user_levels_config:
        return

    if levels_config in user_levels_config:
        level_config = [x for x in user_levels_config.split(";") if levels_config in x]
        if level_config:
            level_config = level_config[0]

        level_config = level_config.split('=')[1]
        formatted_levels = {}
        for level_info in level_config.split("|"):
            name_level = level_info.split("-")[1]
            points_level = level_info.split('-')[0].split(":")
            points_level = [int(x) for x in points_level]
            formatted_levels[name_level] = points_level

        for level_name, level_points in list(formatted_levels.items()):
            if int(points) >= level_points[0] and int(points) <= level_points[1]:
                return level_name


def rates_has_user_level():
    user_levels_config = get_config_property_value(advance_configs_names.MEMBERS_CLUB_LEVELS)
    user_information = session_manager.get(USER_LOGIN_INFO)
    hidden_user_rates = get_config_property_value(advance_configs_names.RATES_LOGIN_HIDDEN)
    if (not user_levels_config or not user_information) and hidden_user_rates and hidden_user_rates != ONLY_LEVELS:
        return

    rates_levels_config = 'rates_levels'
    rates_config = None

    if rates_levels_config in user_levels_config:
        rates_config = [x for x in user_levels_config.split(";") if rates_levels_config in x]

    #Members club v2 (Form.io)
    if not rates_config and user_levels_config == 'True':
        levels_info = retreive_all_information_of_club_levels()
        has_level_rates = False
        for level_id, level_info in list(levels_info.items()):
            if level_info.get('rates'):
                has_level_rates = True

        return has_level_rates

    if not rates_config:
        return

    formatted_dict = {}
    rates_config = rates_config[0].split("=")[1]
    for category_rate in rates_config.split("|"):
        level, rates = category_rate.split(":")
        formatted_dict[level] = rates.split(",")

    return formatted_dict


def get_rates_to_login():
    rates_to_loggin = []
    config_advanced = get_config_property_value(RATES_LOGIN_USERS)
    if config_advanced:
        rates_to_loggin = config_advanced.split("@@")
    else:
        config_xml = get_integration_rate_map_properties(LOGIN_USER_RATES_XML)
        if config_xml:
            for key in config_xml:
                rate_name = get_rate_local_name(key)
                if rate_name:
                    rates_to_loggin.append(rate_name)

    return rates_to_loggin


def get_rates_to_login_formatted():
    rates_to_loggin = get_rates_to_login()
    if rates_to_loggin:
        rates_to_loggin_formatted = [rate.lower().strip() for rate in rates_to_loggin]
        return rates_to_loggin_formatted
    return []


def get_all_club_rates():
    rates_list = []
    rates_logged_users = get_rates_to_login()
    if rates_logged_users:
        rates_list += rates_logged_users

    lock_rates = get_web_configuration(CLUB_LOCK_RATES)
    if lock_rates:
        rates_list += list(lock_rates.keys())

    return rates_list


def club_rates_in_results(results):
    has_club_rates_on_results = False
    for room in results:
        if type(room) is dict:
            for rate in room.get('rateResult', []):
                if rate.get('logged_users'):
                    has_club_rates_on_results = True

        elif type(room) is list:
            for room_element in room:
                for rate in room_element.get('rateResult', []):
                    if rate.get('logged_users'):
                        has_club_rates_on_results = True

    return has_club_rates_on_results


def toggle_selected_normal_rates_and_club_rates(choice_club_discount):
    if not session_manager.get(SELECTED_OPTION_KEY):
        return

    is_holiday_structure = get_config_property_value(advance_configs_names.USE_HOLIDAY_STRUCTURE_FOR_RESULTS)

    selected_options = []
    for index, partialSelected in enumerate(session_manager.get(SELECTED_OPTION_KEY).split(";"), start=1):
        selected_option = session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected)
        rate_key, room_key, board_key = selected_option[0:3]

        if not is_holiday_structure:
            new_rate_key = get_related_rate_key_from_locked_rates_map(choice_club_discount, rate_key, room_key)
        else:
            new_rate_key = get_related_rate_key_from_locked_rates_config(choice_club_discount, rate_key)

        if new_rate_key:
            if is_allowed_only_one_room_for_cart():
                index = 1

            option_key = '@@'.join([MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX, str(index), room_key, new_rate_key, board_key])
            option_uuid = session_manager.get(option_key)
            if option_uuid:
                selected_options.append(option_uuid)
                session_manager.set(SELECTED_OPTION_KEY, ';'.join(selected_options))
            else:
                raise ValueError('Option uuid not found for key %s' % option_key)

    if is_booking_cart_v2():
        shopping_cart = ShoppingCartV2()
        shopping_cart.build_cart_from_session(forced=True)


def get_related_rate_key_from_locked_rates_map(choice_club_discount, rate_key, room_key):
    locked_rates = session_manager.get(LOCKED_RATES_CLUB) or {}

    new_rate_key = None
    if choice_club_discount == 'active':
        related_lock_rate = locked_rates.get(room_key, {}).get(rate_key, {}).get('related_lock_rate')
        if related_lock_rate:
            new_rate_key = related_lock_rate

    else:
        for room, rates in locked_rates.items():
            for rate, rate_value in rates.items():
                if rate_value.get('related_lock_rate') == rate_key:
                    new_rate_key = rate
                    break

    return new_rate_key


def get_related_rate_key_from_locked_rates_config(choice_club_discount, rate_key):
    club_lock_rates = get_web_configuration(CLUB_LOCK_RATES)
    rate_name = get_rate_local_name(rate_key)

    new_rate_key = None
    if choice_club_discount == 'active':
        related_rate_name = [x for x in club_lock_rates.keys() if club_lock_rates.get(x) == rate_name]
        if related_rate_name:
            related_rate_name = related_rate_name[0]
    else:
        related_rate_name = club_lock_rates.get(rate_name)
    if related_rate_name:
        rate_info = directDataProvider.get("Rate", {"localName": related_rate_name}, 1)
        if rate_info:
            new_rate_key = rate_info[0].key

    return new_rate_key


def get_agency_rates():
    agency_rates = []
    rates_config = get_config_property_value(advance_configs_names.AGENCY_RATES)
    if rates_config:
        agency_rates = rates_config.split("@@")
        agency_rates = [rate.lower().strip() for rate in agency_rates]

    return agency_rates


def filter_agency_rates(rates, is_agency):
    agency_rates = get_agency_rates()
    filtered_rates = []
    for rate in rates:
        rate_identifier = [rate.get('rateIdentifier').lower().strip()] if rate.get('rateIdentifier') else []
        if not rate_identifier and rate.get('identifiers_list'):
            rate_identifier = [x.lower().strip() for x in [l for l in rate.get('identifiers_list') if l]]

        matched_elements = [x for x in rate_identifier if x and x in agency_rates]
        agency_rate_found = len(matched_elements) > 0
        if is_agency == agency_rate_found:
            filtered_rates.append(rate)

    return filtered_rates
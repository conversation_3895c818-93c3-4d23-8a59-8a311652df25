from datetime import datetime
from booking_process.utils.analytics.ga4_utils import _build_ecommerce_room_item
from booking_process.utils.booking.results_management.results_general import get_all_available_price_options
from booking_process.constants.session_data import SEARCH_KEY_PREFIX
from booking_process.utils.currency.currencyUtils import get_currency
from booking_process.utils.data_management.hotel_data import get_hotel_name
from booking_process.utils.language.language_utils import get_analytics_language
from booking_process.utils.namespaces.namespace_utils import get_namespace
from booking_process.utils.session import session_manager
from booking_process.utils.templates.template_utils import build_template_2


def build_room_items(language):
    return {
        price_option['uuid']: _build_ecommerce_room_item(price_option)
        for price_option in get_all_available_price_options(language)
    }


def group_room_items_by_name(room_items):
    grouped = {}
    for item in room_items.values():
        item_name = item['item_name']
        grouped.setdefault(item_name, []).append(item)
    return grouped


def build_hotel_prices_structured_data(step):
    language = get_analytics_language()
    search = session_manager.get(SEARCH_KEY_PREFIX)

    context = {
        'step': step,
        'hotel_name': get_hotel_name(),
        'namespace': get_namespace(),
        'currency': get_currency(),
    }

    if search:
        context['start_date'] = datetime.strptime(search.get('startDate'), '%Y-%m-%d').strftime('%Y-%m-%d') if search.get('startDate') else ''
        context['end_date'] = datetime.strptime(search.get('endDate'), '%Y-%m-%d').strftime('%Y-%m-%d') if search.get('endDate') else ''
        context['occupancy'] = sum(room.get('total_persons', 0) for room in search.get('rooms', []))

    room_items = build_room_items(language)
    sorted_room_items = dict(sorted(room_items.items(), key=lambda item: item[1]['price']))

    context['room_items'] = sorted_room_items
    context['grouped_room_items'] = group_room_items_by_name(sorted_room_items)

    return build_template_2('general/analytics/hotel_prices_data.html', context)

# -*- coding: utf-8 -*-
import json
import logging
import datetime

from flask import request

from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.email.email_utils_third_party import notify_exception
from booking_process.utils.language.language_utils import get_language_code
from booking_process.utils.newsletter.sendinblue.mailin import Mailin


class SendingBlueController:

    def __init__(self, mail, language, reservation=None, config=None, name='', clubUser=False, extra_params=None):
        if not config:
            config = get_web_configuration("SendingBlue API")

        self.config = config
        if not config:
            notify_exception(
                '[Design] Wrong configuration Sendinblue',
                'Sendinblue is not configured the web config correctly',
                add_hotel_info=True
            )

            raise Exception('Not configured config for SendinBlue web config')

        self.api = config.get("API v3")
        self.url = "https://api.sendinblue.com/v3"
        self.mail = mail
        self.name = name

        self.params_from_request = config.get("params_from_request", "")
        if self.params_from_request:
            self.params_from_request = config['params_from_request'].split(';')

        if 'language' in self.params_from_request:
            language = request.values.get('language', language)

        if config.get("mapping_languages"):
            self.language = self._convert_to_dict(config['mapping_languages']).get(get_language_code(language), language)
        else:
            self.language = get_language_code(language)

        self.mapping_fields = config.get("mapping_fields", "")
        if self.mapping_fields:
            self.mapping_fields = self._convert_to_dict(config['mapping_fields'])

        self.additional_fields = config.get("additional_fields", "")
        if self.additional_fields:
            self.additional_fields = self._convert_to_dict(config['additional_fields'])

        if config.get("main_list", ""):
            self.lists = [int(m) for m in config.get("main_list", "").split(";")]

        if config.get("main_list_by_dates", ""):
            self.lists = self._get_main_list_by_date(config["main_list_by_dates"])

        if config.get('not_club_subscription_list') and not clubUser:
            self.lists = [int(m) for m in config.get('not_club_subscription_list').split(";")]

        if request.values.get('destination_lists'):
            self.lists = [int(m) for m in request.values.get('destination_lists').split(";")]

        if config.get("languages_list"):
            self.lists = int(self._convert_to_dict(config['languages_list']).get(get_language_code(language)))

        if config.get("club_list") and clubUser:
            club_list_value = self._convert_to_dict(config['club_list']).get(get_language_code(language), 0)
            if ':' in str(club_list_value):
                self.lists = [int(x) for x in str(club_list_value).split(':')]
            else:
                self.lists = int(club_list_value)

        if request.values.get('list_ids'):
            self.lists = int(self._convert_to_dict(request.values.get('list_ids')).get(get_language_code(language)))
            logging.info("List ids: %s", self.lists)

        # Convert self.lists into an array
        if not isinstance(self.lists, list):
            self.lists = [self.lists]

        if reservation:
            if not type(reservation) is dict:
                reservation = reservation.to_dict()

            if config.get("staydates_list"):
                staydates_options = self._convert_to_dict(config['staydates_list'])
                startDate = "-".join(reservation.get("startDate").split("-")[-2:])
                date_found = False
                for date, list_id in list(staydates_options.items()):
                    if not date == "other":
                        start, end = date.split("--")

                        if start > end:
                            if start <= startDate or startDate <= end:
                                self.lists.append(int(list_id))
                                date_found = True

                        else:
                            if start <= startDate <= end:
                                self.lists.append(int(list_id))
                                date_found = True

                if not date_found:
                    self.lists.append(int(staydates_options.get("other")))

        self.data = self.set_attributes(reservation, extra_params)

    def set_attributes(self, reservation, extra_params):
        data = {
            "email": self.mail,
            "listIds": self.lists,
            # "listid_unlink": [],
            "attributes": {}
        }

        if self.mapping_fields:
            for field, field_map in self.mapping_fields.items():
                if field == "language" and self.language:
                    data['attributes'][field_map] = self.language

                elif field_map == 'language' and self.language:
                    data['attributes'][field] = self.language

                elif reservation and reservation.get(field):
                    value = reservation[field]
                    if field == 'country':
                        value = self.transform_country(value)

                    if value:
                        data['attributes'][field_map] = value

                elif field == 'name' and self.name:
                    data['attributes'][field_map] = self.name

                elif extra_params and type(extra_params) is dict and extra_params.get(field):
                    value = extra_params[field]
                    if field == 'country' or field == 'pais':
                        value = self.transform_country(value)

                    if value:
                        data['attributes'][field_map] = value

                elif self.params_from_request and field in self.params_from_request and request.values.get(field):
                    value = request.values.get(field, '')
                    if field == 'country' or field == 'pais':
                        value = self.transform_country(value)

                    if value:
                        data['attributes'][field_map] = value

        if self.additional_fields:
            for field, value in self.additional_fields.items():
                if value:
                    data['attributes'][field] = value

        return data

    def transform_country(self, country_code):
        country_transform = self.config.get('country_replace')
        if not country_transform:
            return country_code

        countries_replacement = country_transform.split("@")
        countries_replacement = dict([x.split(";") for x in countries_replacement])

        if countries_replacement.get(country_code):
            return countries_replacement[country_code]

        return country_code

    def _convert_to_dict(self, config):
        return {x.split("@@")[0]: x.split("@@")[1] for x in config.split(";")}


    def _get_main_list_by_date(self, config):
        default_lists = self.lists
        try:
            config_dict = self._convert_to_dict(config)
            today = datetime.datetime.combine(datetime.datetime.now().date(), datetime.time.min)

            for date_range, list_id in config_dict.items():
                start_str, end_str = date_range.split('--')
                start_date = datetime.datetime.strptime(start_str, "%m-%d").replace(year=today.year)
                end_date = datetime.datetime.strptime(end_str, "%m-%d").replace(year=today.year)

                if start_date > end_date:
                    end_date = end_date.replace(year=today.year + 1)  #for range with year overlap

                if start_date <= today <= end_date:
                    return [int(list_id)]

            return default_lists

        except Exception as e:
            logging.error(f'[SendingBlue] error getting main list by dates: {e}')
            return default_lists

    def create_user(self):
        m = Mailin(self.url, self.api)
        logging.info("[SendingBlue] Data: %s", json.dumps(self.data))
        result = m.post("contacts", json.dumps(self.data))
        logging.info("[SendingBlue] Result: %s", json.dumps(result))
        if result.get('code', '') == 'failure':
            raise Exception("SendingBlue error adding new subscriptor -- %s", result.get('message', 'code error not defined'))

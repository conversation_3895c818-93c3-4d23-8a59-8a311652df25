import json
import logging
import re
from collections import OrderedDict
from datetime import datetime, timedelta

from booking_process.constants.advance_configs_names import BOOKING_CONFIRMATION_SHOW_ORIGINAL_INDEX_ROOMS, \
    BOOKING_CONFIRMATION_PRICES_TABLE, TRUNCATE_DECIMAL_BOOKING, BOOKING3_POPUP_AUTILFILL_INFORMATION, \
    FIXING_PRICE_DAY_SHOPPING_CART, ROUND_DECIMAL_BOOKING
from booking_process.constants.dates_standard import SEARCH_DATE_FORMAT, VISUAL_DATE_FORMAT
from booking_process.constants.session_data import USED_POINTS_FOR_BOOKING, I_AM_A_RESIDENT, BOOKING3_LINK_TPV, \
    FORCED_SAVE_MANAGER2, RESCUE_PROMOCODE_APPLIED, RESERVATION_MODIFICATION_ON_REQUEST, SELECTED_OPTION_KEY, \
    PRICE_OPTION_KEY_PREFIX, PRICE_OPTION_KEY_PREFIX_V2
from booking_process.libs.pasarelas.gateway_constants import DISCOUNT_APPLIED
from booking_process.utils.agencies.constants import AGENCY_INFO, AGENCY_WEB_CONFIGURATION
from booking_process.utils.booking.booking3_utils.booking3_context import get_config_autofill_booking3
from booking_process.utils.booking.selections.selection_utils import getSelectedRoom
from booking_process.utils.bookingConstants import SEVERAL_ROOM_SEPARATOR
from booking_process.utils.clubs.clubs_bookings import get_club_discount_from_original_reservation
from booking_process.utils.data_management.configs_utils import get_config_property_value
from booking_process.utils.data_management.web_configs_utils import get_web_configuration
from booking_process.utils.dates.dates_management import get_localized_date
from booking_process.utils.email.email_utils_third_party import notify_exception
from booking_process.utils.language.language_constants import SPANISH
from booking_process.utils.language.language_utils import get_web_dictionary
from booking_process.utils.paritymaker.paritymaker_utils import PRICES_FROM_RATECHECK
from booking_process.utils.prices.price_context import format_price_decimals
from booking_process.utils.session import session_manager
from booking_process.utils.shopping_cart.booking_cart import is_enabled_shopping_cart, \
    build_extra_shopping_cart_reservation
from booking_process.utils.taxes import get_tax_percentage, tax_is_included_in_base_price
from booking_process.utils.taxes.tax_utils import tax_is_included_in_price, add_tax_to_price
from models.reservations import Reservation


# TODO: Check if this methods can be refactored, it's old code and probably can be improved to reduce errors.

# moved from bookingUtils.py
def build_dict_price_per_day_table(
        params: dict,
        save_with_room_names: bool = True,
        language: str = SPANISH,
        basic_info=None,
        apply_discount: bool = True,
        recalculate_prices: bool = False,
        forced_room_price: dict|None = None,
        with_taxes: bool|None = None,
        taxes_already_applied: bool = False
) -> OrderedDict:
    # TODO: Refactor this method and see how unify it with shopping cart one in some points.

    logging.info(f'[build_dict_price_per_day_table] Reservation extraInfo: {json.dumps(params.get("extraInfo"))}')
    logging.info(f'[build_dict_price_per_day_table] Params: {params}')

    if params.get("extraInfo"):
        booking_shopping_cart = bool(params['extraInfo'].get("shopping_cart")) or bool(params['extraInfo'].get("shopping_cart_human_read"))
    else:
        booking_shopping_cart = is_enabled_shopping_cart()

    if not booking_shopping_cart:
        result = build_dict_price_per_day_table_from_normal_way(
            params,
            save_with_room_names,
            language,
            basic_info,
            apply_discount,
            recalculate_prices,
            forced_room_price,
        )
    else:
        # shopping cart!
        result = build_dict_price_per_day_table_from_shopping_cart(
            params,
            save_with_room_names,
            apply_discount,
            recalculate_prices
        )

    # We only have to increment taxes first time (I mean, when we are creating reservation.
    # Never increment again when sending emails)
    tax_rate = get_tax_percentage()
    tax_included_in_base_price = tax_is_included_in_base_price()

    if with_taxes is None:
        with_taxes = tax_is_included_in_price()

    agency_info = session_manager.get(AGENCY_INFO)
    agency_config = get_web_configuration(AGENCY_WEB_CONFIGURATION)

    if with_taxes:
        if not tax_included_in_base_price and not taxes_already_applied:
            if agency_info and agency_info.get('type') and agency_info.get('type') == 'pvp':
                result = _apply_agency_commission_and_tax_country_in_prices_per_day_dict(result, agency_info, tax_rate)
            else:
                result = _apply_tax_country_in_prices_per_day_dict(result, tax_rate)
    else:
        if tax_included_in_base_price or taxes_already_applied:
            result = _apply_tax_country_in_prices_per_day_dict(result, tax_rate, decrement=True)

    # Finally, check if this is a NET Agency or not include commission!
    if agency_info and agency_info.get('type') and agency_info.get('type') != 'pvp' and not agency_config.get('include_commission_for_tax'):
        result = _apply_agency_commision_in_prices_per_day_dict(result, agency_info)

    # Discount of CLUB DISCOUNT (only visually)
    used_points_for_booking = session_manager.get(USED_POINTS_FOR_BOOKING)
    if used_points_for_booking:
        logging.info("Used club points for booking %s" % used_points_for_booking)
        result = _apply_club_discount_in_prices_per_day_dict(result, used_points_for_booking)

    iamaresident = session_manager.get(I_AM_A_RESIDENT) and not session_manager.get(BOOKING3_LINK_TPV)
    if iamaresident:
        booking3_popup_autofill = get_config_property_value(BOOKING3_POPUP_AUTILFILL_INFORMATION)
        popup_autofill_config = get_config_autofill_booking3(booking3_popup_autofill)
        if popup_autofill_config.get('local_resident') and popup_autofill_config.get("discount"):
            for index, price_day in result.items():
                for x in [p for p in price_day if not p == "total"]:
                    price_day[x][0] = "%.2f" % (float(price_day[x][0]) * float((100 - float(popup_autofill_config["discount"])) / 100))
                    price_day[x][2] = "%.2f" % (float(price_day[x][2]) * float((100 - float(popup_autofill_config["discount"])) / 100))

                price_day["total"] = float(price_day["total"]) * float((100 - float(popup_autofill_config["discount"])) / 100)

    logging.info(f"[build_dict_price_per_day_table] Final result without price formatting: {result}")

    _format_result_prices(result)

    logging.info(f"[build_dict_price_per_day_table] Final result with price formatting: {result}")

    return result


def build_dict_price_per_day_table_from_normal_way(
        params: dict,
        save_with_room_names: bool = True,
        language: str = SPANISH,
        basic_info = None,
        apply_discount: bool = True,
        recalculate_prices: bool = False,
        forced_room_price: dict|None = None
) -> OrderedDict:
    """
    Build a dictionary with the price per day for each room in the normal way (without shopping cart).

    :param params: All necessary params to build the table.
    :param save_with_room_names: Used for change name of room key in table.
    :param language: Language to use for translations.
    :param basic_info:
    :param apply_discount: True if we want to apply discount to the price per day from promotions, False otherwise.
    :param recalculate_prices: True if we need to recalculate total room price, False otherwise.
    :param forced_room_price: Dict with rooms prices forced.
    :return: Price per day table.
    """

    result = OrderedDict()

    start_date = datetime.strptime(params['startDate'], SEARCH_DATE_FORMAT).date()
    end_date = datetime.strptime(params['endDate'], SEARCH_DATE_FORMAT).date()
    days = (end_date - start_date).days

    logging.info(f"[build_dict_price_per_day_table_from_normal_way] End date: {end_date}. Days: {days}. Prices forced: {forced_room_price}")

    rooms = params['room'].split(SEVERAL_ROOM_SEPARATOR)
    logging.info(f"[build_dict_price_per_day_table_from_normal_way] NumRooms: {params['numRooms']}. Rooms: {rooms}")
    price_per_day_rooms = params.get("extraInfo", {}).get('prices_per_day', {})
    current_active_room_index_list = []

    for room in price_per_day_rooms.keys():
        room_index = room.split(':')[0].strip()
        current_active_room_index_list.append(int(room_index))

    current_active_room_index_list.sort()

    for room_index in range(0, params['numRooms']):
        logging.info(f"[build_dict_price_per_day_table_from_normal_way] Room {room_index + 1} of {params['numRooms']}")
        total_price_days = 0
        room = rooms[room_index]
        roomName = ''

        fixed_room_index_for_data = visual_fixed_room_index = room_index + 1
        if current_active_room_index_list and len(current_active_room_index_list) == params['numRooms']:
            fixed_room_index_for_data = current_active_room_index_list[room_index]
            if get_config_property_value(BOOKING_CONFIRMATION_SHOW_ORIGINAL_INDEX_ROOMS):
                visual_fixed_room_index = current_active_room_index_list[room_index]

        if save_with_room_names:
            # be careful, index room is manadatory in roomName because of the key dict (trouble with same room name)
            roomName += (
                f"{visual_fixed_room_index}: {room} ({params[f'adults{fixed_room_index_for_data}']}-{params[f'kids{fixed_room_index_for_data}']}-{params[f'babies{fixed_room_index_for_data}']})"
            )
        else:
            # roomName is room_id, but we keep the order index...
            roomName += f"{visual_fixed_room_index}: {room}"

        result[roomName] = OrderedDict()

        discountsPerDay = [0] * days
        if params['promotion'] and params['promotion'][room_index] and apply_discount and params['promotion'][room_index]['valuesPerDay']:
            discountsPerDay = params['promotion'][room_index]['valuesPerDay']
            logging.info("new discountsPerDay: %s", discountsPerDay)

        forced_total_price_room = 0
        if forced_room_price:
            forced_total_price_room = float(forced_room_price.get(str(room_index + 1), 0))
            logging.info(f"forced_total_price_room for room {str(room_index + 1)}: {forced_total_price_room}")
        elif not params['pricesPerDay'] and params['price']:
            forced_total_price_room = float(params['price']) / params['numRooms']
            logging.warning(f"pricesPerDay not arriving correctly!!! making an average: {forced_total_price_room}")

        if forced_total_price_room > 0:
            if not params['pricesPerDay']:
                params['pricesPerDay'] = [0] * params['numRooms']

            discountsPerDay = [0] * days
            forced_price_room_per_day = float(forced_total_price_room) / days
            forced_price_rooms_per_day = [forced_price_room_per_day] * days

            params['pricesPerDay'][room_index] = forced_price_rooms_per_day
            params['pricePerRoom'][room_index] = forced_total_price_room

        logging.info(f"params['pricesPerDay']: {params.get('pricesPerDay')}. discountsPerDay: {discountsPerDay}")

        if len(params['pricesPerDay'][room_index]) == len(discountsPerDay):
            prices_and_discounts = list(zip(params['pricesPerDay'][room_index], discountsPerDay))
        else:
            prices_per_day_length = len(params['pricesPerDay'][room_index])
            promotion_per_day_length = len(discountsPerDay)
            differential = prices_per_day_length - promotion_per_day_length
            try:
                logging.warning("discountsPerDay: %s", discountsPerDay)
                for n in range(differential):
                    discountsPerDay.append(0)
            except Exception as e:
                logging.warning("Error. We use as vivification")
                discountsPerDay.extend([0] * differential)
            prices_and_discounts = list(zip(params['pricesPerDay'][room_index], discountsPerDay))

        for day_range in range(days):
            if save_with_room_names and get_config_property_value(BOOKING_CONFIRMATION_PRICES_TABLE) and not basic_info:
                formatted_date_string = (start_date + timedelta(days=day_range)).strftime(SEARCH_DATE_FORMAT)
                expected_date_string = f"@day@ {get_web_dictionary(language)['T_de'].lower()} @month@"
                date = get_localized_date(formatted_date_string, language, expected_date_string)
            else:
                date = (start_date + timedelta(days=day_range)).strftime(VISUAL_DATE_FORMAT)

            if day_range < len(prices_and_discounts):
                price, discount = prices_and_discounts[day_range]
            else:
                logging.warning("Imposible to calculate discounts per day. Posibly previous old modification broke this (2)")
                recalculate_prices = True
                apply_discount = False
                discount = 0

            if recalculate_prices:
                room_price = params['pricePerRoom'][room_index]
                price = room_price / days

                if apply_discount:
                    price -= discount
                else:
                    discount = 0

                logging.info(
                    f"Recalculating prices for room {room_index}. "
                    f"Room price: {room_price}. Price: {price}. Discount: {discount}. Apply discount: {apply_discount}"
                )

            # If price is minor than 0,05 because of conversion promotions to rates
            if price < 0.05:
                price = 0

            finalPrice = price - discount

            # Applies the discount on the total daily per room when appropriate
            logging.info(f"price: {price}. discount: {discount}. final_price: {finalPrice}")
            if session_manager.get(DISCOUNT_APPLIED):
                discount_by_100 = float(session_manager.get(DISCOUNT_APPLIED))
                # discount += finalPrice * (discount_by_100 / 100)

                aux_final_price = finalPrice * ((100 - discount_by_100) / 100)
                discount = price - aux_final_price if apply_discount else 0
                finalPrice = price - discount

                logging.info(f"DISCOUNT_APPLIED. Price: {price}. Discount: {discount}. Final_price: {finalPrice}")

            # We need to save result without price formatting to avoid prices diff aplying taxes.
            # price = format_price_decimals(price)
            # discount = format_price_decimals(discount)
            # finalPrice = format_price_decimals(finalPrice)

            # Fix error final price per day = -0.00
            if finalPrice < 0:
                finalPrice = 0

            total_price_days += float(f"{finalPrice:.2f}")
            result[roomName][date] = [f"{price:.2f}", f"{discount:.2f}", f"{finalPrice:.2f}"]

        # Applies the discount on the total per room when appropriate
        total_price_per_room = params['pricePerRoom'][room_index]
        if discount_applied := session_manager.get(DISCOUNT_APPLIED):
            logging.info(f"Discount applied set in session. Total room price before apply discount: {total_price_per_room}. Discount: {discount_applied}")
            total_price_per_room *= ((100 - float(discount_applied)) / 100)

        # total_price_per_room = format_price_decimals(total_price_per_room)

        logging.info(f"[build_dict_price_per_day_table_from_normal_way] Total price per room: {total_price_per_room} - {type(total_price_per_room)}")
        try:
            total_price_per_room = float("%0.2f" % total_price_per_room)
        except Exception as e:
            logging.warning(f"[build_dict_price_per_day_table_from_normal_way] error rounding price room: {e}")
            logging.info("[build_dict_price_per_day_table_from_normal_way] setting price room without format")
            total_price_per_room = float(total_price_per_room)

        result[roomName]["total"] = total_price_per_room

        # Discount of paritymaker (First we check if paritymaker is active)
        if session_manager.get(PRICES_FROM_RATECHECK):
            result = _apply_paritymaker_discount_in_price_per_day_dict(result, roomName, params, total_price_days)

        _check_add_fix_round_daily_room_prices(total_price_per_room, result[roomName], room_index)

    return result


# moved from booking_cart.py
def build_dict_price_per_day_table_from_shopping_cart(
		params: dict,
		save_with_room_names: bool,
		apply_discount: bool = True,
		recalculate_prices: bool = False
) -> OrderedDict:
    """
    Build a dictionary with the price per day for each room in the shopping cart.
    :param params: All params to build the table.
    :param save_with_room_names: Used for change name of room key in table.
    :param apply_discount: True if we want to apply discount to the price per day from promotions, False otherwise.
    :param recalculate_prices: True if we need to recalculate total room price, False otherwise.
    :return: Price per day table and total price per room.
    """

    logging.info("build special price table per day for SHOPPING CART")

    start_date = datetime.strptime(params['startDate'], SEARCH_DATE_FORMAT).date()
    end_date = datetime.strptime(params['endDate'], SEARCH_DATE_FORMAT).date()

    list_rooms = params.get("extraInfo", {}).get("shopping_cart") or build_extra_shopping_cart_reservation()
    logging.info("List of rooms: %s", list_rooms)

    rooms_keys = params['room'].split(SEVERAL_ROOM_SEPARATOR)
    result = OrderedDict()

    for index, room in enumerate(list_rooms):
        current_prices_per_day = params['pricesPerDay'][index]
        days = (
        	(datetime.strptime(room['departure_date'], VISUAL_DATE_FORMAT).date() - start_date).days
        	if room.get('departure_date') else
        	(end_date - start_date).days
        )

        logging.info(f"[build_dict_price_per_day_table_from_shopping_cart] End date: {end_date}. Departure date: {room.get('departure_date')}. Days: {days}")

        room_key = rooms_keys[index]
        if save_with_room_names:
            room_table_name = f"{index + 1}: {room_key} ({room['adults']}-{room['kids']}-{room['babies']})"
        else:
            room_table_name = f"{index + 1}: {room_key}"

        discounts_per_day = [0] * days
        try:
            if apply_discount and params['promotion'] and params['promotion'][index] and params['promotion'][index]['valuesPerDay']:
                discounts_per_day = params['promotion'][index]['valuesPerDay']
                logging.info("new discounts_per_day: %s", discounts_per_day)
        except Exception as e:
            logging.error(f"Error trying to apply discount on price per day table: {e}")

        logging.info(f"params['pricesPerDay']: {params.get('pricesPerDay')} ----- discounts_per_day: {discounts_per_day}")
        if len(current_prices_per_day) == len(discounts_per_day):
            prices_and_discounts = list(zip(current_prices_per_day, discounts_per_day))
        else:
            prices_per_day_length = len(current_prices_per_day)
            promotion_per_day_length = len(discounts_per_day)
            differential = prices_per_day_length - promotion_per_day_length
            try:
                logging.warning("discounts_per_day: %s", discounts_per_day)
                for n in range(differential):
                    discounts_per_day.append(0)
            except Exception as e:
                logging.warning("Error. We use as vivification")
                discounts_per_day.extend([0] * differential)
            prices_and_discounts = list(zip(current_prices_per_day, discounts_per_day))

        result[room_table_name] = OrderedDict()
        total_price_days = 0
        for day_range in range(days):
            date = (start_date + timedelta(days=day_range)).strftime(VISUAL_DATE_FORMAT)
            discount = 0
            price_average = float(room["price"]) / days

            if get_config_property_value(FIXING_PRICE_DAY_SHOPPING_CART):
                try:
                    price_average = params.get("pricesPerDay")[index][day_range]
                    if promotion := params.get("promotion", [])[index]:
                        discount = promotion['valuesPerDay'][day_range]

                except Exception as e:
                    logging.info("Error getting price per day shopping cart")
                    discount = 0
                    price_average = float(room["price"]) / days

            if day_range < len(prices_and_discounts):
                price_average, discount = prices_and_discounts[day_range]
            else:
                logging.warning("Imposible to calculate discounts per day. Posibly previous old modification broke this (2)")
                recalculate_prices = True
                apply_discount = False
                discount = 0

            if recalculate_prices:
                pricePerRoom = params['pricePerRoom'][index]
                price_average = pricePerRoom / days
                if apply_discount:
                    price_average -= discount
                else:
                    discount = 0

            # If price is minor than 0,05 because of conversion promotions to rates
            if price_average < 0.05:
                price_average = 0

            final_price = price_average - discount

            logging.info(f"price: {price_average}. discount: {discount}. final_price: {final_price}")
            if discount_applied := session_manager.get(DISCOUNT_APPLIED):
                discount_by_100 = float(discount_applied)
                # discount += final_price * (discount_by_100 / 100)

                aux_final_price = final_price * ((100 - discount_by_100) / 100)
                discount = price_average - aux_final_price
                if not apply_discount:
                    discount = 0
                final_price = price_average - discount

                logging.info(f"DISCOUNT_APPLIED price: {price_average} discount: {discount} final_price: {final_price} ")

            # Fix error final price per day = -0.00
            if final_price < 0:
                final_price = 0

            # We need to save result without price formatting to avoid prices diff aplying taxes.
            # price_average = format_price_decimals(price_average)
            # discount = format_price_decimals(discount)
            # final_price = format_price_decimals(final_price)

            # room["price"] = format_price_decimals(room["price"])

            total_price_days += float(f"{final_price:.2f}")
            result[room_table_name][date] = [f"{price_average:.2f}", f"{discount:.2f}", f"{final_price:.2f}"]

        result[room_table_name]["total"] = total_price_days

        # Discount of paritymaker (First we check if paritymaker is active)
        if session_manager.get(PRICES_FROM_RATECHECK):
            result = _apply_paritymaker_discount_in_price_per_day_dict(result, room_table_name, params, total_price_days)

        _check_add_fix_round_daily_room_prices(total_price_days, result[room_table_name], index)

    logging.info("Special daily price table for SHOPPING CART: %s", result)

    return result


# moved from bookingUtils.py
def build_dict_price_per_day_table_for_single_room(params, room_number, apply_discount=True, recalculate_prices=False):
    start_date = datetime.strptime(params['startDate'], SEARCH_DATE_FORMAT).date()
    end_date = datetime.strptime(params['endDate'], SEARCH_DATE_FORMAT).date()
    days = (end_date - start_date).days

    result = OrderedDict()
    rooms = params['room'].split(SEVERAL_ROOM_SEPARATOR)

    #we are completely sure that we have only one room (becuase it is modification room by boom)
    room_index = 0
    room = rooms[room_index]

    #roomName is room_id, but we keep the order index...
    roomName = f"{room_number}: {room}"

    result[roomName] = OrderedDict()

    discountsPerDay = [0] * days
    if params['promotion'] and params['promotion'][room_index]:
        discountsPerDay = params['promotion'][room_index]['valuesPerDay']

    if len(params['pricesPerDay'][room_index]) == len(discountsPerDay):
        pricesAndDiscounts = list(zip(params['pricesPerDay'][room_index], discountsPerDay))
    else:
        prices_per_day_length = len(params['pricesPerDay'][room_index])
        promotion_per_day_length = len(discountsPerDay)
        differential = prices_per_day_length - promotion_per_day_length
        discountsPerDay.extend([0] * differential)
        pricesAndDiscounts = list(zip(params['pricesPerDay'][room_index], discountsPerDay))

    total_price_days = 0
    for day_range in range(days):
        date = (start_date + timedelta(days=day_range)).strftime(VISUAL_DATE_FORMAT)

        if day_range < len(pricesAndDiscounts):
            price, discount = pricesAndDiscounts[day_range]
        else:
            logging.warning("Imposible to calculate discounts per day. Posibly previous old modification broke this")
            recalculate_prices = True
            apply_discount = False
            discount = 0

        if recalculate_prices:
            pricePerRoom = params['pricePerRoom'][room_index]
            price = pricePerRoom / days
            if apply_discount:
                price -= discount
            else:
                discount = 0

        finalPrice = price - discount

        prices = ["%0.2f" % price, "%0.2f" % discount, "%0.2f" % finalPrice]
        total_price_days += float("%0.2f" % finalPrice)

        result[roomName][date] = prices

    result[roomName]["total"] =  params['pricePerRoom'][room_index]

    # Discount of paritymaker (First we check if paritymaker is active)
    if session_manager.get(PRICES_FROM_RATECHECK):
        price_of_room = result[roomName]["total"]
        for date, prices in list(result[roomName].items()):
            if date != 'total':
                if total_price_days > price_of_room:
                    discount_day = (float(total_price_days) - float(price_of_room)) / len(params['pricesPerDay'][0]) #Differencial and take number of days we stay

                    prices[1] = float("%0.2f" % (float(prices[1]) + float(discount_day)))
                    prices[2] = float("%0.2f" % (float(prices[2]) - float(discount_day)))

    return result


# moved from bookingUtils.py
def build_params_per_prices_table(
        reservation: Reservation,
        price_forced: dict|None = None,
        save_with_room_names: bool = True
) -> dict:
    """
    Build the params for the prices table.
    :param reservation: Reservation to work with.
    :param price_forced: Dict with forced prices.
    :param save_with_room_names: True if we want to save the room names, False if we want to save room ids.
    :return: Complete dict with necessary params to build the table.
    """

    from booking_process.utils.booking.bookingUtils import getSeparatedPricesPerRoom

    selected_price = []
    prices_per_day = []
    promotion = []

    # Note that we might have selected multiple rooms, that is why we might have more selected option
    for partialSelected in session_manager.get(SELECTED_OPTION_KEY).split(";"):
        selected_price.append(session_manager.get(PRICE_OPTION_KEY_PREFIX + partialSelected))

        price_option_v2_value = session_manager.get(PRICE_OPTION_KEY_PREFIX_V2 + partialSelected)
        prices_per_day.append(price_option_v2_value['pricesPerDay'])
        promotion.append(price_option_v2_value['promotion'])

    if price_forced:
        logging.info(f"[build_params_per_prices_table] Forcing prices for rooms: {price_forced}")
        for room_price in list(price_forced.items()):
            index = int(room_price[0]) - 1
            price = price_forced.get(room_price[0])
            if len(selected_price) > index:
                selected_price[index][3] = price

    if save_with_room_names:
        rooms = getSelectedRoom(selected_price, SPANISH)
    else:
        #for extra info we need rooms ids and not room names
        rooms = ""
        for price in selected_price:
            rooms += str(price[1]) + SEVERAL_ROOM_SEPARATOR
        rooms = rooms[0:len(rooms) - len(SEVERAL_ROOM_SEPARATOR)]

    params = {
        "startDate": reservation.startDate,
        "endDate": reservation.endDate,
        "room":  rooms,
        "numRooms": reservation.numRooms,
        "promotion": promotion,
        "pricesPerDay": prices_per_day,
        "pricePerRoom": getSeparatedPricesPerRoom(selected_price),
        'adults1' : reservation.adults1,
        'kids1' : reservation.kids1,
        'babies1' : reservation.babies1,
        'adults2' : reservation.adults2,
        'kids2' : reservation.kids2,
        'babies2' : reservation.babies2,
        'adults3' : reservation.adults3,
        'kids3' : reservation.kids3,
        'babies3' : reservation.babies3
    }

    return params


# moved from bookingUtils.py
def _apply_paritymaker_discount_in_price_per_day_dict(price_per_day_dict, room_name, params, total_price_days):
    try:
        price_of_room = price_per_day_dict[room_name]["total"]

        logging.info(f"[_apply_paritymaker_discount_in_price_per_day_dict] PARITYMAKER original total_price_days: {total_price_days}, price_of_room: {price_of_room}")
        logging.info(f"Original price_per_day_dict before PARITYMAKER DISCOUNT: {price_per_day_dict}")

        if total_price_days > price_of_room:
            total_pm_discount = float(total_price_days) - float(price_of_room)

            for date, prices in price_per_day_dict[room_name].items():
                #note that the  total come already changed!
                if date != 'total':
                        discount_day = total_pm_discount / len(params['pricesPerDay'][0]) #Differencial and take number of days we stay
                        prices[1] = float("%0.2f" % (float(prices[1]) + float(discount_day)))
                        prices[2] = float("%0.2f" % (float(prices[2]) - float(discount_day)))

    except Exception as e:
        logging.warning("[_apply_paritymaker_discount_in_price_per_day_dict] error adding paritymaker discount: %s" % e)

    logging.info(f"Final price_per_day_dict after PARITYMAKER discount: {price_per_day_dict}")

    return price_per_day_dict


# moved from bookingUtils.py
def _apply_club_discount_in_prices_per_day_dict(price_per_day_dict, club_info):
    try:
        # DECREASE ALSO HERE THE TOTAL AND THE PRICE PER DAY TABLE!!!!
        if club_info.get("price_to_discount"):
            total_club_discount = float(club_info.get("price_to_discount"))
            logging.info("Original price_per_day_dict before CLUB DISCOUNT : %s", price_per_day_dict)

            num_rooms = len(price_per_day_dict.items())
            discount_by_room = total_club_discount / num_rooms

            final_price_day = OrderedDict({})
            for room_name, room_prices_by_date in price_per_day_dict.items():
                days_prices_dict = OrderedDict({})

                num_days = len(room_prices_by_date.items()) -1
                daily_discount_by_day_and_room = discount_by_room /num_days


                for day_name, prices_list in room_prices_by_date.items():
                    if type(prices_list) is list:
                        #THE TRIPLA (total_day, discount_day, final_discounted_price_day)

                        prices_list[1] =  "%.2f" % (float(prices_list[1]) + daily_discount_by_day_and_room)
                        prices_list[2] =  "%.2f" % (float(prices_list[2]) - daily_discount_by_day_and_room)

                        days_prices_dict[day_name] = prices_list

                    else:
                        #THE TOTAL
                        days_prices_dict[day_name] = "%.2f" % (float(prices_list) - float(discount_by_room))

                final_price_day[room_name] = days_prices_dict

            logging.info("Final price_per_day_dict after CLUB DISCOUNT discount : %s", final_price_day)
            return final_price_day

    except Exception as e:
        logging.warning("[bookingUtils - apply_club_discount_in_prices_per_day_dict] error adding club discount: %s" % e)

    return price_per_day_dict


# moved from agencies_utils.py. This function was only used in build_dict_price_per_day_table function.
def _apply_agency_commission_and_tax_country_in_prices_per_day_dict(price_per_day_dict, agency_info, tax_rate):
    commission = float(agency_info.get("commision"))
    agencies_final_price_day = OrderedDict({})

    for room_name, room_prices_by_date in price_per_day_dict.items():
        days_prices_dict = OrderedDict({})
        for day_name, prices_list in room_prices_by_date.items():
            if type(prices_list) is list:
                processed_prices_list = []
                for price in prices_list:
                    generated_commission = float(price) * (commission / 100.00)
                    net_price = "%.2f" % (float(price) - generated_commission)
                    price_with_taxes = round(add_tax_to_price(float(net_price), tax_rate), 2)
                    final_price = price_with_taxes + generated_commission
                    processed_prices_list.append(final_price)
                days_prices_dict[day_name] = processed_prices_list
            else:
                generated_commission = float(prices_list) * (commission / 100.00)
                net_price = "%.2f" % (float(prices_list) - generated_commission)
                price_with_taxes = round(add_tax_to_price(float(net_price), tax_rate), 2)
                final_price = price_with_taxes + generated_commission
                days_prices_dict[day_name] = final_price

        agencies_final_price_day[room_name] = days_prices_dict

    logging.info("Final price_per_day_dict after all calculations : %s", agencies_final_price_day)

    return agencies_final_price_day


# moved from agencies_utils.py. This function was only used in build_dict_price_per_day_table function.
def _apply_agency_commision_in_prices_per_day_dict(price_per_day_dict, agency_info):
    #DECREASE ALSO HERE THE TOTAL AND THE PRICE PER DAY TABLE!!!!
    final_price_day = OrderedDict({})
    if agency_info.get("commision") and re.match(r"^-?\d+(?:\.\d+)?$", agency_info["commision"]):
        commision = float(agency_info.get("commision"))

        commision_calculation = lambda x: x - (x * (commision/100))

        logging.info("Original price_per_day_dict before comission : %s", price_per_day_dict)

        for room_name, room_prices_by_date in list(price_per_day_dict.items()):
            days_prices_dict = OrderedDict({})
            for day_name, prices_list in list(room_prices_by_date.items()):
                if type(prices_list) is list:
                    days_prices_dict[day_name] = ["%.2f" % commision_calculation(float(x)) for x in prices_list]
                else:
                    days_prices_dict[day_name] = "%.2f" % commision_calculation(float(prices_list))

            final_price_day[room_name] = days_prices_dict

    price_per_day_dict = final_price_day
    logging.info("Final price_per_day_dict after commission increment : %s", price_per_day_dict)

    return final_price_day


# moved from countryUtils.py. This function was only used in build_dict_price_per_day_table function.
def _apply_tax_country_in_prices_per_day_dict(price_per_day_dict, tax_rate, decrement=False):
    from booking_process.utils.taxes.tax_utils import add_tax_to_price, remove_tax_from_price

    logging.info("Original price_per_day_dict before increment: %s", price_per_day_dict)

    tax_calculation = remove_tax_from_price if decrement else add_tax_to_price

    final_price_day = OrderedDict({})
    for room_name, room_prices_by_date in list(price_per_day_dict.items()):
        days_prices_dict = OrderedDict({})
        for day_name, prices_list in list(room_prices_by_date.items()):
            if type(prices_list) is list:
                days_prices_dict[day_name] = [round(tax_calculation(float(price), tax_rate), 2) for price in prices_list]
            else:
                days_prices_dict[day_name] = round(tax_calculation(float(prices_list), tax_rate), 2)

        final_price_day[room_name] = days_prices_dict

    price_per_day_dict = final_price_day
    logging.info("Final price_per_day_dict after increment: %s", price_per_day_dict)

    return final_price_day


# moved from bookingUtils.py
def _check_add_fix_round_daily_room_prices(room_amount: float, daily_rates: dict, room_index: int) -> None:
    """
    Check if the sum of the daily rates is equal to the total room price. If not, fix it or show an error if the
    difference is bigger than margin error.
    :param room_amount: Total room price.
    :param daily_rates: Daily prices dict calculated.
    :param room_index: Current room index.
    """

    from booking_process.utils.booking.bookingUtils import is_upgrading_done_for_option_selected

    count_price = 0
    num_days = 0
    daily_room_prices = {key: value for key, value in daily_rates.items() if key != 'total'}
    for key in list(daily_room_prices.keys()):
        count_price += format_price_decimals(float(daily_rates[key][2]))
        num_days += 1

    margin_error = 1
    if get_config_property_value(TRUNCATE_DECIMAL_BOOKING) or get_config_property_value(ROUND_DECIMAL_BOOKING):
        # We can have an error by day of a maximun on one euro per day
        margin_error = 1 * num_days

    if count_price != float(room_amount):
        logging.warning(f"Fixing total daily prices. Calculated: {count_price}. Total room price: {room_amount}")

        def should_dont_panic() -> tuple[bool, str, str]:
            """
            Check if we should show errors in case that the price difference is big than the margin error or not.
            :return: Tuple with a boolean indicating if we should panic or not, a string with the condition that
            activated it and a string with the result of the condition.
            """
            conditions = [
                (session_manager.get, FORCED_SAVE_MANAGER2),
                (session_manager.get, RESCUE_PROMOCODE_APPLIED),
                (session_manager.get, RESERVATION_MODIFICATION_ON_REQUEST),
                (is_upgrading_done_for_option_selected, room_index),
            ]

            for condition in conditions:
                if result := condition[0](condition[1]):
                    return (True, f'{condition[0]}({condition[1]})', str(result))

            return (False, '', '')

        dont_panic, condition, result = should_dont_panic()
        if dont_panic:
            logging.info(f"dont_panic active, not applying changes. Condition: {condition}. Result: {result}")
            return

        if abs(count_price - room_amount) > margin_error:
            error_message = "ERROR in fixing round daily price becasue difference is bigger than 1 euro"
            logging.error(error_message)
            message = f"{error_message}: total daily prices calculated: {count_price}. Total price of the room: {room_amount}"
            notify_exception("[Backend] URGENT BAD TOTAL VS DAILY PRICES", message, add_hotel_info=True)
        else:
            diff = float(room_amount) - count_price
            daily_diff = diff / num_days
            logging.info(f"Fixing round daily price in all days. Incrementing {daily_diff} by day")

            for daily_price in daily_room_prices.values():
                daily_price[2] = "%.2f" % (float(daily_price[2]) + daily_diff)


def _format_result_prices(result: OrderedDict) -> OrderedDict:
    """
    Format the prices in the result dictionary based on hotel configurations.
    :param result: The result dictionary to format.
    :return: The formatted result dictionary.
    """

    for room_name, room_prices in result.items():
        for key, prices in room_prices.items():
            if isinstance(prices, list):
                prices = [float(format_price_decimals(float(price))) for price in prices]
            else:
                prices = float(format_price_decimals(float(prices)))

            room_prices[key] = prices

    return result

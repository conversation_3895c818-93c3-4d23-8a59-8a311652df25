import json


def dumps_json_for_javascript(content: dict|list) -> str:
    """
    Dumps JSON cleaning content correctly for JS to avoid parse errors.
    :param content: Content with JSON structure to dumps and clean.
    :return: JSON dumped with correct format for JS.
    """

    dumped_content = json.dumps(content)
    decoded_content = dumped_content.encode().decode('unicode_escape')

    cleaned_content = decoded_content.translate(str.maketrans({
        '\t': '', '\n': ' ', '\r': ' ', '\u2028': ' ', '\u2029': ' '
    }))

    try:
        # Make sure that really it's correct and apply necessary dumps replacings
        json_data = json.loads(cleaned_content)
        return json.dumps(json_data)
    except ValueError:
        return dumped_content

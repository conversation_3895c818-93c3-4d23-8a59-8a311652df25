import json
from typing import Union


def dumps_json_for_javascript(content: Union[dict, list]) -> str:
    """
    Dumps JSON cleaning content correctly for JS to avoid parse errors.
    Properly escapes content for use in JavaScript template literals in Django templates.

    :param content: Content with JSON structure to dumps and clean.
    :return: <PERSON><PERSON><PERSON> dumped with correct format for JS template literals.
    :unit_test: tests/unit_tests/utils/compatibility/test_compatibility_utils.py
    """

    # Generate clean JSON - use ensure_ascii=False to preserve unicode characters
    dumped_content = json.dumps(content, ensure_ascii=False, separators=(',', ':'))

    # Clean problematic characters for JavaScript template literals
    # We need to be careful about the order of replacements to avoid double-escaping
    cleaned_content = dumped_content

    # Replace newlines and tabs with spaces (as in original function)
    cleaned_content = cleaned_content.translate(str.maketrans({
        '\t': ' ',        # Replace tabs with spaces
        '\n': ' ',        # Replace newlines with spaces
        '\r': ' ',        # Replace carriage returns with spaces
        '\u2028': ' ',    # Replace line separator with spaces
        '\u2029': ' '     # Replace paragraph separator with spaces
    }))

    # Double-escape quotes for JavaScript template literals
    # In template literals, \" becomes " so we need \\" to get \" in the final JSON
    cleaned_content = cleaned_content.replace('\\"', '\\\\"')

    # Escape backticks for JavaScript template literals
    # We need to escape them as \\` to create a valid JSON string that contains \`
    cleaned_content = cleaned_content.replace('`', '\\\\`')

    # Escape template literal expressions ${...}
    # We need to escape them as \\${ to create a valid JSON string that contains \${
    cleaned_content = cleaned_content.replace('${', '\\\\${')

    # Note: The result will NOT be valid JSON for Python json.loads() due to double-escaped quotes
    # This is intentional - the function is specifically for JavaScript template literal usage
    # The double-escaped quotes will become properly escaped quotes after template literal processing
    return cleaned_content

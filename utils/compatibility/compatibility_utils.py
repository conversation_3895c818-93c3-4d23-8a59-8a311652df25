import json


def dumps_json_for_javascript(content: dict|list) -> str:
    """
    Dumps JSON cleaning content correctly for JS to avoid parse errors.
    Properly escapes content for use in JavaScript template literals in Django templates.

    :param content: Content with JSON structure to dumps and clean.
    :return: <PERSON><PERSON><PERSON> dumped with correct format for JS template literals.
    :unit_test: tests/unit_tests/utils/compatibility/test_compatibility_utils.py
    """

    # Generate clean JSON - use ensure_ascii=False to preserve unicode characters
    dumped_content = json.dumps(content, ensure_ascii=False, separators=(',', ':'))

    # Clean problematic characters for JavaScript template literals
    # We need to be careful about the order of replacements to avoid double-escaping
    cleaned_content = dumped_content

    # Replace newlines and tabs with spaces (as in original function)
    cleaned_content = cleaned_content.translate(str.maketrans({
        '\t': ' ',        # Replace tabs with spaces
        '\n': ' ',        # Replace newlines with spaces
        '\r': ' ',        # Replace carriage returns with spaces
        '\u2028': ' ',    # Replace line separator with spaces
        '\u2029': ' '     # Replace paragraph separator with spaces
    }))

    # Escape backticks for JavaScript template literals
    # We need to escape them as \\` to create a valid JSON string that contains \`
    cleaned_content = cleaned_content.replace('`', '\\\\`')

    # Escape template literal expressions ${...}
    # We need to escape them as \\${ to create a valid JSON string that contains \${
    cleaned_content = cleaned_content.replace('${', '\\\\${')

    # Verify the result is valid JSON
    try:
        json.loads(cleaned_content)
        return cleaned_content
    except (ValueError, json.JSONDecodeError):
        # Fallback to basic JSON dumps if cleaning failed
        return json.dumps(content, ensure_ascii=True, separators=(',', ':'))

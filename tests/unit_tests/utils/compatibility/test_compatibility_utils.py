import json
from unittest.mock import patch

from booking_process.utils.compatibility.compatibility_utils import dumps_json_for_javascript
from tests.test_base import TestBase


class TestCompatibilityUtils(TestBase):

    def test_dumps_json_for_javascript_basic_functionality(self):
        """Test basic JSON dumping functionality"""
        
        with self.subTest('Simple dictionary'):
            test_data = {"key": "value", "number": 123}
            result = dumps_json_for_javascript(test_data)
            
            # Should be valid JSON
            parsed = json.loads(result)
            self.assertEqual(parsed, test_data)
            
        with self.subTest('Simple list'):
            test_data = ["item1", "item2", 123]
            result = dumps_json_for_javascript(test_data)
            
            # Should be valid JSON
            parsed = json.loads(result)
            self.assertEqual(parsed, test_data)

    def test_dumps_json_for_javascript_quotes_handling(self):
        """Test handling of quotes in string values"""
        
        with self.subTest('Double quotes in string'):
            test_data = {"property_name": '<PERSON>a balinesa "Fresquito" '}
            result = dumps_json_for_javascript(test_data)
            
            # Should be valid JSON
            parsed = json.loads(result)
            self.assertEqual(parsed, test_data)
            
            # Should properly escape quotes for JSON
            self.assertIn('\\"', result)
            
        with self.subTest('Single quotes in string'):
            test_data = {"property_name": "Text with 'single quotes'"}
            result = dumps_json_for_javascript(test_data)
            
            # Should be valid JSON
            parsed = json.loads(result)
            self.assertEqual(parsed, test_data)

    def test_dumps_json_for_javascript_whitespace_handling(self):
        """Test handling of whitespace characters"""
        
        with self.subTest('Newlines and tabs'):
            test_data = {"multiline": "Text with\nnewlines\tand\ttabs"}
            result = dumps_json_for_javascript(test_data)
            
            # Should be valid JSON
            parsed = json.loads(result)
            
            # Whitespace should be replaced with spaces
            self.assertNotIn('\n', result)
            self.assertNotIn('\t', result)
            self.assertIn(' ', parsed["multiline"])
            
        with self.subTest('Unicode line separators'):
            test_data = {"text": "Text with\u2028line\u2029separators"}
            result = dumps_json_for_javascript(test_data)
            
            # Should be valid JSON
            parsed = json.loads(result)
            
            # Unicode separators should be replaced with spaces
            self.assertNotIn('\u2028', result)
            self.assertNotIn('\u2029', result)

    def test_dumps_json_for_javascript_template_literal_safety(self):
        """Test safety for JavaScript template literals"""
        
        with self.subTest('Backticks escaping'):
            test_data = {"with_backticks": "Text with `backticks` here"}
            result = dumps_json_for_javascript(test_data)
            
            # Should be valid JSON
            parsed = json.loads(result)
            
            # Backticks should be escaped in the result
            self.assertIn('\\\\`', result)
            
            # Parsed data should contain escaped backticks
            self.assertIn('\\`', parsed["with_backticks"])
            
        with self.subTest('Template expressions escaping'):
            test_data = {"template_expr": "Text with ${expression} here"}
            result = dumps_json_for_javascript(test_data)
            
            # Should be valid JSON
            parsed = json.loads(result)
            
            # Template expressions should be escaped in the result
            self.assertIn('\\\\${', result)
            
            # Parsed data should contain escaped template expressions
            self.assertIn('\\${', parsed["template_expr"])

    def test_dumps_json_for_javascript_complex_scenarios(self):
        """Test complex scenarios with multiple edge cases"""
        
        with self.subTest('Mixed content'):
            test_data = {
                "complex": 'Mixed "quotes" with `backticks` and ${expr} and\nnewlines',
                "simple": "Normal text",
                "number": 123,
                "boolean": True,
                "null_value": None
            }
            result = dumps_json_for_javascript(test_data)
            
            # Should be valid JSON
            parsed = json.loads(result)
            
            # Check that all dangerous characters are handled
            self.assertNotIn('\n', result)  # Newlines replaced
            self.assertIn('\\\\`', result)  # Backticks escaped
            self.assertIn('\\\\${', result)  # Template expressions escaped
            self.assertIn('\\"', result)    # Quotes escaped
            
        with self.subTest('Empty content'):
            test_data = {}
            result = dumps_json_for_javascript(test_data)
            
            # Should be valid JSON
            parsed = json.loads(result)
            self.assertEqual(parsed, test_data)
            
        with self.subTest('Nested structures'):
            test_data = {
                "nested": {
                    "level1": {
                        "level2": "Text with `backticks` and ${expr}"
                    }
                },
                "array": ["item1", {"key": "value with `backticks`"}]
            }
            result = dumps_json_for_javascript(test_data)
            
            # Should be valid JSON
            parsed = json.loads(result)
            
            # Check nested escaping works
            self.assertIn('\\\\`', result)
            self.assertIn('\\\\${', result)

    def test_dumps_json_for_javascript_django_template_compatibility(self):
        """Test compatibility with Django template usage"""
        
        with self.subTest('Original problematic case'):
            test_data = {"property_name": 'Cama balinesa "Fresquito" '}
            result = dumps_json_for_javascript(test_data)
            
            # Simulate Django template: JSON.parse(`{{ result|safe }}`)
            django_template_simulation = f"JSON.parse(`{result}`)"
            
            # Should not contain unescaped backticks that would break template literal
            unescaped_backticks = result.count('`') - result.count('\\`')
            self.assertEqual(unescaped_backticks, 0, "Should not contain unescaped backticks")
            
            # Should not contain unescaped template expressions
            unescaped_expressions = result.count('${') - result.count('\\${')
            self.assertEqual(unescaped_expressions, 0, "Should not contain unescaped template expressions")

    @patch('booking_process.utils.compatibility.compatibility_utils.json.loads')
    def test_dumps_json_for_javascript_fallback_behavior(self, mock_json_loads):
        """Test fallback behavior when JSON validation fails"""
        
        # Mock json.loads to raise an exception
        mock_json_loads.side_effect = json.JSONDecodeError("Test error", "", 0)
        
        test_data = {"key": "value"}
        result = dumps_json_for_javascript(test_data)
        
        # Should fallback to basic JSON dumps with ensure_ascii=True
        expected_fallback = json.dumps(test_data, ensure_ascii=True, separators=(',', ':'))
        self.assertEqual(result, expected_fallback)

    def test_dumps_json_for_javascript_unicode_handling(self):
        """Test handling of unicode characters"""
        
        with self.subTest('Unicode characters'):
            test_data = {"unicode": "Café, naïve, résumé, 中文, 🚀"}
            result = dumps_json_for_javascript(test_data)
            
            # Should be valid JSON
            parsed = json.loads(result)
            self.assertEqual(parsed, test_data)
            
            # Should preserve unicode characters (ensure_ascii=False)
            self.assertIn('Café', result)
            self.assertIn('中文', result)
            self.assertIn('🚀', result)

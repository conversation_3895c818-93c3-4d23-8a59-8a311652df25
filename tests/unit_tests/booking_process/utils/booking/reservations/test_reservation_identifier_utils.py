import logging
from unittest.mock import patch

from booking_process.constants.session_data import INCLUDE_IN_EXTRA_INFO_FROM_SEARCH
from booking_process.utils.booking.reservations.reservation_identifier_utils import _get_identifier_from_search_extra_info
from test_base import TestBase


class TestReservationIdentifierUtils(TestBase):

    @patch('booking_process.utils.session.session_manager.get')
    def test_get_identifier_from_search_extra_info(self, mock_session_get):
        """Test _get_identifier_from_search_extra_info function with various input scenarios."""

        with self.subTest('No session data'):
            mock_session_get.return_value = None
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, '')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('Empty list'):
            mock_session_get.return_value = []
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, '')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('List with no PREBOOKING__ elements'):
            mock_session_get.return_value = ['OTHER_PREFIX__123', 'DIFFERENT__456', 'no_prefix']
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, '')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('List with one PREBOOKING__ element'):
            mock_session_get.return_value = ['OTHER__123', 'PREBOOKING__ABC123', 'DIFFERENT__456']
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, 'ABC123')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('List with multiple PREBOOKING__ elements'):
            mock_session_get.return_value = ['PREBOOKING__FIRST123', 'OTHER__456', 'PREBOOKING__SECOND789']
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, 'FIRST123')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('List with PREBOOKING__ element but no suffix'):
            mock_session_get.return_value = ['OTHER__123', 'PREBOOKING__', 'DIFFERENT__456']
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, '')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('String with PREBOOKING__ prefix'):
            mock_session_get.return_value = 'PREBOOKING__XYZ789'
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, 'XYZ789')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('String without PREBOOKING__ prefix'):
            mock_session_get.return_value = 'OTHER_PREFIX__123'
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, '')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('String that is just PREBOOKING__'):
            mock_session_get.return_value = 'PREBOOKING__'
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, '')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('Other data types - integer'):
            mock_session_get.return_value = 12345
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, '')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('Other data types - dictionary'):
            mock_session_get.return_value = {'key': 'PREBOOKING__123'}
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, '')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('List with complex PREBOOKING__ identifier'):
            mock_session_get.return_value = ['OTHER__123', 'PREBOOKING__ABC-123_XYZ', 'DIFFERENT__456']
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, 'ABC-123_XYZ')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('String with complex PREBOOKING__ identifier'):
            mock_session_get.return_value = 'PREBOOKING__HOTEL_BOOKING_2024_001'
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, 'HOTEL_BOOKING_2024_001')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('List with PREBOOKING__ containing double underscores'):
            mock_session_get.return_value = ['PREBOOKING__ID__WITH__UNDERSCORES']
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, 'ID__WITH__UNDERSCORES')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

        with self.subTest('String with PREBOOKING__ containing double underscores'):
            mock_session_get.return_value = 'PREBOOKING__COMPLEX__ID__123'
            result = _get_identifier_from_search_extra_info()
            self.assertEqual(result, 'COMPLEX__ID__123')
            mock_session_get.assert_called_once_with(INCLUDE_IN_EXTRA_INFO_FROM_SEARCH)
            mock_session_get.reset_mock()

    @patch('booking_process.utils.booking.reservations.reservation_identifier_utils.logging.error')
    @patch('booking_process.utils.session.session_manager.get')
    def test_get_identifier_from_search_extra_info_exception_handling(self, mock_session_get, mock_logging_error):
        """Test _get_identifier_from_search_extra_info exception handling scenarios."""

        with self.subTest('Exception during list processing - index error'):
            # Create a list with an element that will cause an IndexError when accessing split result
            class MockElement:
                def startswith(self, prefix):
                    return True
                def split(self, delimiter):
                    return ['PREBOOKING__']  # This will cause IndexError when accessing index [1]

            mock_session_get.return_value = [MockElement()]
            result = _get_identifier_from_search_extra_info()

            self.assertEqual(result, '')
            mock_logging_error.assert_called_once()
            # Verify the error message contains expected information
            error_call_args = mock_logging_error.call_args[0][0]
            self.assertIn("Error getting booking id from search extra info:", error_call_args)
            mock_logging_error.reset_mock()

        with self.subTest('Exception during string processing - index error'):
            # Create a mock string that will cause an IndexError
            class MockString(str):
                def __new__(cls, value):
                    return str.__new__(cls, value)
                def startswith(self, prefix):
                    return True
                def split(self, delimiter):
                    return ['PREBOOKING__']  # This will cause IndexError when accessing index [1]

            mock_session_get.return_value = MockString('PREBOOKING__test')
            result = _get_identifier_from_search_extra_info()

            self.assertEqual(result, '')
            mock_logging_error.assert_called_once()
            # Verify the error message contains expected information
            error_call_args = mock_logging_error.call_args[0][0]
            self.assertIn("Error getting booking id from search extra info:", error_call_args)
            mock_logging_error.reset_mock()

        with self.subTest('Exception during list processing - attribute error'):
            # Test with an object that doesn't have the expected methods
            mock_session_get.return_value = [123]  # Integer doesn't have startswith method
            result = _get_identifier_from_search_extra_info()

            self.assertEqual(result, '')
            mock_logging_error.assert_called_once()
            error_call_args = mock_logging_error.call_args[0][0]
            self.assertIn("Error getting booking id from search extra info:", error_call_args)
            mock_logging_error.reset_mock()
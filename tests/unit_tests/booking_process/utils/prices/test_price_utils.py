from unittest.mock import patch

from booking_process.libs.pasarelas.gateway_constants import DISCOUNT_APPLIED
from tests.test_base import TestBase
from booking_process.constants.session_data import MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX
from booking_process.utils.prices.price_utils import get_price_option_uuids_by_room_index, set_booking_discount


class TestPriceUtils(TestBase):

    @patch('booking_process.utils.session.session_manager.set')
    @patch('booking_process.utils.shopping_cart.booking_cart_v2.ShoppingCartV2')
    @patch('booking_process.utils.prices.price_utils.is_booking_cart_v2')
    @patch('booking_process.utils.prices.price_utils.is_enabled_shopping_cart')
    def test_set_booking_discount(self, mock_is_enabled_shopping_cart, mock_is_booking_cart_v2, mock_ShoppingCartV2,
                                  mock_set_session):
        test_discount = 10
        test_session = {}
        mock_set_session.side_effect = test_session.__setitem__

        with self.subTest('Set booking discount in session but not in cart'):
            test_session.clear()

            mock_is_enabled_shopping_cart.return_value = False
            mock_is_booking_cart_v2.return_value = False

            set_booking_discount(test_discount)

            self.assertEqual(test_session, {DISCOUNT_APPLIED: test_discount})
            mock_ShoppingCartV2.set_discount.assert_not_called()

            mock_ShoppingCartV2.reset_mock()

        with self.subTest('Set booking discount in session - cart not v2'):
            test_session.clear()

            mock_is_enabled_shopping_cart.return_value = True
            mock_is_booking_cart_v2.return_value = False

            set_booking_discount(test_discount)

            self.assertEqual(test_session, {DISCOUNT_APPLIED: test_discount})
            mock_ShoppingCartV2.set_discount.assert_not_called()

            mock_ShoppingCartV2.reset_mock()

        with self.subTest('Set booking discount in session and cart v2'):
            test_session.clear()

            mock_is_enabled_shopping_cart.return_value = True
            mock_is_booking_cart_v2.return_value = True

            set_booking_discount(test_discount)

            self.assertEqual(test_session, {DISCOUNT_APPLIED: test_discount})
            mock_ShoppingCartV2.set_discount.assert_called_once_with(test_discount)

            mock_ShoppingCartV2.reset_mock()




    @patch('booking_process.utils.session.session_manager.get_all')
    def test_get_price_option_uuids_by_room_index(self, mock_get_all):
        test_session = {
            f'{MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX}@@1@@room1@@rate1@@board1': 'uuid1',
            f'{MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX}@@1@@room2@@rate2@@board2': 'uuid2',
            f'{MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX}@@2@@room1@@rate1@@board1': 'uuid3',
            f'{MAP_ROOM_RATE_BOARD_VS_UUID_PREFIX}@@3@@room1@@rate1@@board1': 'uuid4',
        }


        with self.subTest('No price options in session'):
            mock_get_all.return_value = {}
            self.assertEqual(get_price_option_uuids_by_room_index(), {})

        mock_get_all.return_value = test_session

        with self.subTest('3 different room indexes'):
            self.assertEqual(get_price_option_uuids_by_room_index(), {
                1: ['uuid1', 'uuid2'],
                2: ['uuid3'],
                3: ['uuid4'],
            })


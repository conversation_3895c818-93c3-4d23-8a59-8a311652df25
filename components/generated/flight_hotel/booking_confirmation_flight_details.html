{% macro individual_segment_details(segment) %}
    <table style="width:100%;padding: 16px 0;{% if segment.flight_segment_index > 1 %}border-top: 1px dashed lightgrey;{% endif %}">
        <tr>
            <td style="width: 35%; vertical-align: top">
                <div style="font-size: 22px; font-weight: 700; margin-bottom: 10px;">{{ segment.departure_time }}</div>
                <div style="">{{ segment.departure_city }}</div>
                <div style="font-weight: 300;">{{ segment.departure_airport_code }}</div>
                <div style="font-weight: 300;margin-top:24px;">{{ segment.flight_code }}</div>
            </td>
            <td style="width: 30%; padding: 5px; text-align: center; vertical-align: middle; letter-spacing: 0">
                <div style="margin-bottom: 5px; font-size: 12px;">{{ segment.flight_duration }}</div>
                <div style="margin-bottom: 5px; width: 100%; height: 3px; background-color: #0088CC"></div>
                <div style="font-size: 12px;">{{ segment.flight_segment_label }}</div>
            </td>
            <td style="width: 35%; vertical-align: top; text-align: right">
                <div style="font-size: 22px; font-weight: 700; margin-bottom: 10px;">{{ segment.arrival_time }}</div>
                <div style="">{{ segment.arrival_city }}</div>
                <div style="font-weight: 300;">{{ segment.arrival_airport_code }}</div>
                <div style="font-weight: 300;margin-top:24px;">{{ segment.airline_name }}</div>
            </td>
        </tr>
    </table>
{% endmacro %}
{% macro individual_flight_details(flight) %}
    <table width="100%" style="border-spacing: 0; border-collapse: collapse; font-weight: 400; font-size: 16px; letter-spacing: 1px">
        <tr>
            <td colspan="3" style="padding-bottom: 10px;">
                <img style="width: 25px; vertical-align: middle; margin-right: 5px;" src="{{ flight.flight_direction_icon }}" alt="">
                <span style="text-transform: uppercase">{{ flight.flight_label }}</span>
                <span style="margin: 0 5px;">|</span>
                <span style="font-weight: 700;">{{ flight.flight_date }}</span>
            </td>
        </tr>
        <tr>
            <td>
                {% for segment in flight.segments_info %}
                    {{ individual_segment_details(segment) }}
                {% endfor %}
            </td>
        </tr>
    </table>
{% endmacro %}

<!-- Flight details -->
<table width="100%" class="flight_details" style="{% if not payment_confirmation_email %}max-width: calc(100% - 40px);{% endif %}width:100%;margin:24px auto 0;text-align:left;color: #333333">
    {% if not payment_confirmation_email %}
        <tr style="width:100%">
            <td colspan="2" style="padding-top:20px;">
                <table style="border-collapse: collapse; width: 100%;">
                    <tr>
                        <td colspan="2" style="padding-right: 25px;width:50%;">
                            <p class="main_title_block" style="font-size: 24px;margin-bottom: 20px;margin-top: 0;font-weight: 700;color: {% if table_title_color %}{{ table_title_color|safe }}{% else %}#00257a{% endif %};font-style: italic;font-family: 'Open Sans', sans-serif;{% if custom_font_title %}font-family: {{ custom_font_title|safe }};{% endif %}{% if title_custom_styles %}{{ title_custom_styles|safe }};{% endif %}{% if main_title_block %}{{ main_title_block|safe }};{% endif %}">
                                {{ T_flights_details|safe }} {% if is_atol_flight %}({{ T_atol_protected|safe }}){% endif %}
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <p style="font-size: 14px; font-weight: 400;color: #707070;font-style: italic;line-height: 19px;margin: 0 0 20px 0;">
                                {{ T_flight_service_pending|safe }}
                            </p>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <p style="font-size: 14px; font-weight: 400;color: #707070;font-style: italic;line-height: 19px;margin: 0 0 20px 0;">
                                {{ T_important_notice_flights|safe }}
                            </p>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    {% endif %}
    <tr style="width:100%">
        <td>
            <table style="width:100%;border: 1px solid lightgrey;border-collapse: collapse">
                <tr>
                    <td style="padding: 20px 50px;">
                        {{ individual_flight_details(flight_details.outbound) }}
                    </td>
                </tr>
                <tr>
                    <td style="padding: 20px 50px;border-top: 1px solid lightgrey;">
                        {{ individual_flight_details(flight_details.inbound) }}
                    </td>
                </tr>
                <tr>
                    <td colspan="2" style="padding: 20px 50px;border-top: 1px solid lightgrey;">
                        <p style="font-size: 16px; text-transform: uppercase;margin: 0;">{{ flight_disclaimer_info.disclaimer_title|safe }}</p>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" style="padding: 0 50px 20px;">
                        <p class="disclaimer_text" style="font-size: 14px; font-weight: 300;color: #707070;font-style: italic; line-height: 19px;margin: 0;">{{ flight_disclaimer_info.disclaimer_text|safe }}</p>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" style="padding: 0 50px 20px;">
                        <p class="disclaimer_text" style="font-size: 14px; font-weight: 300;color: #707070;font-style: italic; line-height: 19px;margin: 0;">{{ flight_disclaimer_info.disclaimer_contact|safe }}</p>
                    </td>
                </tr>
                {% if flight_disclaimer_info.disclaimer_text_atol %}
                <tr>
                    <td colspan="2" style="padding: 0 50px 20px;">
                        <p class="disclaimer_text" style="font-size: 14px; font-weight: 300;color: #707070;font-style: italic; line-height: 19px;margin: 0;">{{ flight_disclaimer_info.disclaimer_text_atol|safe }}</p>
                    </td>
                </tr>
                {% endif %}
            </table>
        </td>
    </tr>
</table>

{% if flight_passengers_and_extras %}
<!-- Luggage and Passenger details -->
<table width="100%" style="{% if not payment_confirmation_email %}max-width: calc(100% - 40px);{% endif %}width:100%;margin:24px auto 0;text-align:left;color: #333333">
    <tr style="width:100%">
        <td colspan="2">
            <p class="main_title_block" style="font-size: 24px;margin-bottom: 20px;margin-top: 0;display: block;font-weight: 700;color: {% if table_title_color %}{{ table_title_color|safe }}{% else %}#00257a{% endif %};font-style: italic;font-family: 'Open Sans', sans-serif;{% if custom_font_title %}font-family: {{ custom_font_title|safe }};{% endif %}{% if title_custom_styles %}{{ title_custom_styles|safe }};{% endif %}{% if main_title_block %}{{ main_title_block|safe }};{% endif %}">
                {{ T_pax_and_services|safe }}
            </p>
        </td>
    </tr>
    <tr style="width: 100%;">
        <td colspan="2">
            <table style="width:100%;border: 1px solid lightgrey;border-collapse: collapse;">
                {% for passenger in flight_passengers_and_extras %}
                    <tr style="width: 100%">
                        <td colspan="2" style="padding:16px 50px;">
                            <table style="width:100%;">
                                <tr style="width: 100%">
                                    <td colspan="2" style="padding:5px 0px;">
                                        <p style="font-size: 16px;font-weight: bold;margin: 0;{% if passenger.passenger_index > 1 %}border-top: 1px dashed lightgray; padding-top: 12px;{% endif %}">{{ T_Passenger|safe }} {{ passenger.passenger_index|safe }}</p>
                                    </td>
                                </tr>
                                <tr style="width: 100%">
                                    <td colspan="2" style="padding: 5px 0px; font-style: italic;">
                                        {{ passenger.passenger_full_name|safe }}
                                    </td>
                                </tr>
                                {% if passenger.passenger_extras %}
                                    <tr style="width: 100%">
                                        <td colspan="2" style="padding: 5px 0px;">
                                            <p style="font-size: 16px;font-weight: bold;margin: 0;">{{ T_additional_service_pax|safe }} {{ passenger.passenger_index|safe }}</p>
                                        </td>
                                    </tr>
                                    {% for extra in passenger.passenger_extras %}
                                        <tr style="width: 100%">
                                            <td style="width: 50%;padding: 5px 0px;font-style: italic;">{{ extra.luggage_details|safe }}</td>
                                            <td style="width: 50%;padding: 5px 0px;font-style: italic;text-align: right;">
                                                <b>
                                                    <span class="value_elem currencyValue">{{ extra.luggage_price|safe }}</span>
                                                    <span class="monedaConv">{{ currency|safe }}</span>
                                                </b>
                                            </td>
                                        </tr>
                                    {% endfor %}
                                {% endif %}
                            </table>
                        </td>
                    </tr>
                {% endfor %}
            </table>
        </td>
    </tr>
</table>
{% endif %}
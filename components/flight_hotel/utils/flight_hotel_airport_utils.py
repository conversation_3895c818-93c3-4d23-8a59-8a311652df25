import json
import os

from booking_process.components.flight_hotel.flight_hotel_constants import FLIGHT_HOTEL_SUPPORTED_AIRPORT_LANGUAGES
from booking_process.components.flight_hotel.utils.flight_hotel_demo_utils import is_demo_mode, get_demo_airport_codes
from booking_process.constants.session_data import COUNTRY
from booking_process.utils.language.language_constants import ENGLISH
from booking_process.utils.performance.timerdecorator import timeit
from booking_process.utils.request.request_utils import get_google_geo_info_from_request, \
    get_distance_between_two_points
from booking_process.utils.session import session_manager

from paraty_commons_3.utils.country.country_utils import get_countries_by_language
from utils.managers_cache.manager_cache import managers_cache


def get_all_airports(language: str) -> dict:
    """
    Get all the airports from a json file.

    The airport names are translated to the provided language. If the language is not supported, the english airport names are used.

    :param language: The language to use for the airport names.
    :return: The dictionary of all airports.
    :unit_test: unit_tests.booking_process.components.flight_hotel.utils.test_flight_hotel_airport_utils.TestFlightHotelAirportUtils.test_get_all_airports
    """

    with open(os.path.dirname(os.path.abspath(__file__)) + '/../json/airports/airports.json') as f:
        all_airports = json.load(f)

    if is_demo_mode():
        demo_airport_codes = get_demo_airport_codes()
        all_airports = {airport_code: all_airports[airport_code] for airport_code in demo_airport_codes}

    if language in FLIGHT_HOTEL_SUPPORTED_AIRPORT_LANGUAGES and language != ENGLISH:
        with open(os.path.dirname(os.path.abspath(__file__)) + f'/../json/airports/airports_names_{language}.json') as f:
            airport_names = json.load(f)

        for airport_code, airport_data in all_airports.items():
            if airport_names.get(airport_code):
                airport_data['name'] = airport_names[airport_code]

    return all_airports


def get_airport_data(airport_code: str, language: str) -> dict:
    """
    Get the data of an airport by its code.

    :param airport_code: The IATA code of the airport.
    :param language: The language of the data.
    :return: The data of the airport.
    :unit_test: unit_tests.booking_process.components.flight_hotel.utils.test_flight_hotel_airport_utils.TestFlightHotelAirportUtils.test_get_airport_data
    """
    all_airports = get_all_airports(language)
    return all_airports.get(airport_code) or {}


@managers_cache(only_thread_local_and_memory=True)
def get_airports_list(language: str) -> list[dict]:
    """
    Get the list of airports without preselecting one.

    :param language: The language used to localize names.
    :return: The list of airports.
    :unit_test: unit_tests.booking_process.components.flight_hotel.utils.test_flight_hotel_airport_utils.TestFlightHotelAirportUtils.test_get_airports_list
    """
    all_airports = get_all_airports(language)
    countries = get_countries_by_language(language)
    all_airports_list = []

    for airport_code, airport_data in all_airports.items():
        airport_country_code = airport_data.get('country')
        new_airport = {
            'name': airport_data.get('name'),
            'code': airport_code,
            'country_code': airport_country_code,
            'country': countries.get(airport_country_code, airport_country_code),
            'city': airport_data.get('city'),
            'latitude': airport_data.get('latitude'),
            'longitude': airport_data.get('longitude')
        }
        all_airports_list.append(new_airport)

    return all_airports_list


@timeit
def get_origin_airports_list(language: str, selected_airport: str = None) -> list[dict]:
    """
    Get the list of origin airports with one preselected based on provided airport IATA code or user's location.

    Preselection is done by city or coordinates, but not by country.

    :param language: The language code.
    :param selected_airport: The IATA code of the selected airport.
    :return: The list of origin airports with selection status.
    :unit_test: unit_tests.booking_process.components.flight_hotel.utils.test_flight_hotel_airport_utils.TestFlightHotelAirportUtils.test_get_origin_airports_list
    """
    all_airports_list = get_airports_list(language)
    geo_info = _get_user_geo_info_from_request()

    airport_match_by_city = None
    airport_match_by_coordinates = None  # (distance, airport)

    for airport in all_airports_list:
        if not selected_airport:
            if not airport_match_by_city and geo_info['city'] and geo_info['city'].upper() in airport.get('city', '').upper():
                airport_match_by_city = airport

            if geo_info['latitude'] and geo_info['longitude'] and airport['latitude'] and airport['longitude']:
                distance = get_distance_between_two_points(geo_info['latitude'], geo_info['longitude'], airport['latitude'], airport['longitude'])
                if not airport_match_by_coordinates or distance < airport_match_by_coordinates[0]:
                    airport_match_by_coordinates = (distance, airport)

        elif selected_airport == airport['code']:
            airport['selected'] = True
            break


    if airport_match_by_coordinates:
        airport_match_by_coordinates[1]['selected'] = True
    elif airport_match_by_city:
        airport_match_by_city['selected'] = True

    return all_airports_list


def group_airports_by_country(airports_list: list) -> dict:
    """
    Group the airports by country. The user's country will appear first, followed by other countries in alphabetical order.

    :param airports_list: The list of airports.
    :return: The airports grouped by country, with user's country first.
    :unit_test: unit_tests.booking_process.components.flight_hotel.utils.test_flight_hotel_airport_utils.TestFlightHotelAirportUtils.test_group_airports_by_country
    """
    airports_by_country = {}
    for airport in airports_list:
        airports_by_country.setdefault(airport.get('country'), []).append(airport)

    user_geo_info = _get_user_geo_info_from_request()
    user_country_code = user_geo_info.get('country_code')

    # Find user's country name from the airports list
    user_country = None
    if user_country_code:
        for airport in airports_list:
            if airport.get('country_code') == user_country_code:
                user_country = airport.get('country')
                break

    ordered_airports = {}

    # Add user's country first if found
    if user_country and user_country in airports_by_country:
        ordered_airports[user_country] = airports_by_country.pop(user_country)

    # Add remaining countries in alphabetical order
    for country in sorted(airports_by_country.keys()):
        ordered_airports[country] = airports_by_country[country]

    return ordered_airports


def get_airport_options_for_selector(language: str, selected_airport: str = None) -> list[dict]:
    """
    Get the airports for the selector.

    :param language: The language code.
    :param selected_airport: The IATA code of the selected airport.
    :return: The airports for the selector.
    :unit_test: unit_tests.booking_process.components.flight_hotel.utils.test_flight_hotel_airport_utils.TestFlightHotelAirportUtils.test_get_airports_for_selector
    """
    all_airports_list = get_origin_airports_list(language, selected_airport)
    airports_by_country = group_airports_by_country(all_airports_list)

    formatted_airports = []
    for country, airports in airports_by_country.items():
        airports_group = {
            'text': country,
            'children': []
        }

        for airport in airports:
            airports_group['children'].append({
                'text': f"{airport['code']} - {airport['name']}",
                'id': airport['code'],
                'selected': airport.get('selected', False)
            })
        formatted_airports.append(airports_group)

    return formatted_airports


def _get_user_geo_info_from_request() -> dict[str, str | None]:
    """
    Get the relevant user's geo info from the request.

    :return: The user's geo info.
    :unit_test: unit_tests.booking_process.components.flight_hotel.utils.test_flight_hotel_airport_utils.TestFlightHotelAirportUtils.test_get_user_geo_info_from_request
    """
    geo_info = get_google_geo_info_from_request()
    result = {
        'country_code': session_manager.get(COUNTRY) or geo_info.get('X-AppEngine-Country'),
        'city': geo_info.get('X-AppEngine-City'),
        'latitude': None,
        'longitude': None,
    }

    if result['country_code'] is not None:
        result['country_code'] = result['country_code'].upper()

    if geo_info.get('latitud') and geo_info.get('longitud'):
        result['latitude'] = geo_info.get('latitud')
        result['longitude'] = geo_info.get('longitud')

    return result
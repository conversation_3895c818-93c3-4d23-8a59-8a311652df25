.hotel_popup_wrapper {
  width: 100% !important;
  top: 0 !important;
  left: 0 !important;
  font-family: $font-2 !important;
  font-weight: 400 !important;
  color: $color-text !important;

  .fancybox-inner {
    overflow: initial !important;
    width: 100% !important;
  }

  .fancybox-close {
    display: none;
  }
}

.hotel_gallery_wrapper {
  width: 100% !important;
  left: 0 !important;

  .fancybox-inner {
    overflow: initial !important;
  }
}

.hotel_gallery_wrapper {
  width: 100%;

  .close_btn {
    position: absolute;
    font-size: 40px;
    color: white;
    top: -50px;
    right: 20px;
    cursor: pointer;
    z-index: 2;
  }

  .fancybox-close-small, .fancybox-close {
    display: none;
  }

  .fancybox-prev, .fancybox-next {
    span {
      display: none;
    }

    i {
      position: absolute;
      top: 50%;
      transform: translate(0%,-50%);
      font-size: 30px;
      color: white;
    }

    &.fancybox-prev i {
      left: 10px;
    }

    &.fancybox-next i {
      right: 10px;
    }
  }
}

.hotel_popup {
  display: none;
  padding: 0;
  width: 100%;
  overflow: initial;
  border-radius: 5px;

  .fancybox-close-small {
    display: none;
  }

  .close_btn {
    position: absolute;
    font-size: 40px;
    color: white;
    top: 5px;
    right: 20px;
    cursor: pointer;
    z-index: 2;
  }

  .hotel_gallery {
    width: 100%;
    height: 250px;
    overflow: hidden;
    border-radius: 5px 5px 0 0;

    &:before {
      content: "";
      background: transparent linear-gradient(180deg, #00000000 0%, #000000CC 100%) 0 0 no-repeat padding-box;
      opacity: 0.49;
      position: absolute;
      bottom: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
      pointer-events: none;
    }

    .picture_wrapper {
      width: 776px;
      height: 318px;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      i {
        position: absolute;
        color: white;
        top: 20px;
        left: 20px;
        font-size: 30px;
      }
    }

    .owl-dots {
      display: flex;
      justify-content: center;
      position: absolute;
      bottom: 20px;
      left: 0;
      right: 0;
      z-index: 2;

      .owl-dot {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin: 0 5px;
        background-color: white;
        opacity: 50%;
        position: relative;

        &::before {
          position: absolute;
          content: '';
          border: solid 1px white;
          top: -4px;
          bottom: -4px;
          left: -4px;
          right: -4px;
          opacity: 0;
          border-radius: 50%;
        }

        &.active {
          opacity: 1;

          &::before {
            opacity: 1;
          }
        }
      }
    }
  }

  .hotel_desc_wrapper {
    padding: 30px 40px 20px 30px;
    font-family: $font-2;

    .hotel_name {
      font-family: $font-1;
      font-size: 18px;
      letter-spacing: 0.4px;
      margin-bottom: 12px;
    }

    .hotel_small_desc, .see_map_btn {
      font-size: 16px;
      letter-spacing: 0.7px;
      line-height: 20px;
      margin-bottom: 25px;

      .distance {
        margin-top: 10px;
      }
    }

    .services_wrapper {
      margin-bottom: 20px;

      ul {
        li {
          font-size: 14px;
          letter-spacing: 0.31px;
          margin-right: 25px;
          margin-bottom: 15px;

          i {
            font-size: 20px;
            margin-right: 8px;
          }
        }
      }
    }

    .hotel_desc {
      font-size: 14px;
      line-height: 26px;
      margin-bottom: 25px;
      padding-right: 10px;
    }

    .see_map_btn {
      cursor: pointer;
      text-decoration: underline;
    }
  }
}

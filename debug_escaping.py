#!/usr/bin/env python3
"""
Debug script to understand the escaping issue
"""

import json
from utils.compatibility.compatibility_utils import dumps_json_for_javascript

def debug_escaping():
    """Debug the escaping process step by step"""
    
    test_data = {"with_backticks": "Text with `backticks` here"}
    
    print("=== Debug Escaping Process ===")
    print(f"Input: {test_data}")
    
    # Step 1: Initial JSON dump
    step1 = json.dumps(test_data, ensure_ascii=False, separators=(',', ':'))
    print(f"Step 1 - Initial JSON: {step1}")
    print(f"Step 1 - repr: {repr(step1)}")
    
    # Step 2: Check for backticks
    print(f"Contains backticks: {'`' in step1}")
    print(f"Backtick positions: {[i for i, c in enumerate(step1) if c == '`']}")
    
    # Step 3: Apply escaping
    step3 = step1.replace('`', '\\`')
    print(f"Step 3 - After backtick escaping: {step3}")
    print(f"Step 3 - repr: {repr(step3)}")
    
    # Step 4: Check if it's valid JSON
    try:
        parsed = json.loads(step3)
        print(f"Step 4 - Valid JSON: {parsed}")
    except Exception as e:
        print(f"Step 4 - Invalid JSON: {e}")
    
    # Test the actual function
    print(f"\nActual function result: {dumps_json_for_javascript(test_data)}")
    print(f"Actual function repr: {repr(dumps_json_for_javascript(test_data))}")

if __name__ == "__main__":
    debug_escaping()

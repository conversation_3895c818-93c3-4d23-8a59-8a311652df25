{% if not banner_tickets.mobile_version and forBrowser %}
    <div class="header_banner_tickets" style="display: none">
        <div class="content_wrapper">
            <div class="left_wrapper">
                <div class="bar_tickets">
                    <span class="bar">
                        90%
                    </span>
                </div>
                <div class="title_tickets">
                    {{ banner_tickets.description.subtitle|safe }}
                </div>
            </div>
            <a class="button_tickets" href="{{ banner_tickets.link|safe }}">
                {{ banner_tickets.picture.description|safe }}<i class="fa-solid fa-arrow-right-long"></i>
            </a>
        </div>
    </div>
{% endif %}

<div class="banner_tickets" style="border: 1px solid #707070;
    border-radius: 62px;
    {% if forBrowser %}width: calc(1140px - 36px);{% else %}width:700px;{% endif %}
    margin: 40px auto 25px auto;
    overflow: hidden;
    padding: 10px 18px;
    background: white;">
    <table style="margin: 0 auto;">
        <tbody>
            <tr>
                <td style="">
                    <img src="{{ banner_tickets.picture.servingUrl|safe }}" alt="{{ banner_tickets.picture.altText|safe }}" style="width: 90px;height: 90px;">
                </td>
                <td style="vertical-align: middle;">
                    <div style="padding: 0 30px;font-size: 16px;color: #707070;font-weight: 600;margin-bottom: 7px">
                        {{ banner_tickets.description.subtitle|safe }}
                    </div>
                    {% if forBrowser %}
                        <div style="padding: 0 30px;font-size: 16px;color: #707070;padding-right: 90px;">
                            {{ banner_tickets.description.content|safe }}
                        </div>
                    {% endif %}
                </td>
                <td>
                    <a href="{{ banner_tickets.link|safe }}" style="border-radius: 27px;background: {% if banner_tickets.main_color %}{{ banner_tickets.main_color|safe }}{% else %}linear-gradient(77deg, #456BA7, #0088CC){% endif %};color: white;font-size: 16px;display: inline-block;width: 226px;text-align: center;padding: 18px 0;text-decoration: none">
                        {{ banner_tickets.picture.description|safe }}
                    </a>
                </td>
            </tr>
        </tbody>
    </table>
</div>
{% if banner_tickets.mobile_version and forBrowser %}
    <div class="banner_tickets mobile" style="display: none;
    border: 1px solid #707070;
    border-radius: 62px;
    width: 95%;
    margin: 40px auto 25px auto;
    overflow: hidden;
    padding: 10px 18px;
    background: white;">
        <table style="margin: 0 auto;">
            <tbody>
                <tr>
                    <td style="">
                        <img src="{{ banner_tickets.picture.servingUrl|safe }}" alt="{{ banner_tickets.picture.altText|safe }}" style="width: 90px;height: 90px;">
                    </td>
                    <td style="vertical-align: middle;">
                        <div style="padding: 0 30px;font-size: 16px;color: #707070;font-weight: 600;margin-bottom: 7px">
                            {{ banner_tickets.description.subtitle|safe }}
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
        <div style="margin: 10px 0 20px 0;">
            <div style="padding: 0 10px;font-size: 16px;color: #707070;">
                {{ banner_tickets.description.content|safe }}
            </div>
        </div>
        <div style="margin-bottom: 10px;text-align: center;">
            <a href="{{ banner_tickets.link|safe }}" style="border-radius: 27px;background: {% if banner_tickets.main_color %}{{ banner_tickets.main_color|safe }}{% else %}linear-gradient(77deg, #456BA7, #0088CC){% endif %};color: white;font-size: 16px;display: inline-block;width: 226px;text-align: center;padding: 18px 0;text-decoration: none">
                {{ banner_tickets.picture.description|safe }}
            </a>
        </div>
    </div>
{% endif %}

{% if forBrowser %}
    <style>
         {% if banner_tickets.mobile_version %}
            .banner_tickets:not(.mobile) {
                display: none;
            }
            .banner_tickets.mobile {
                display: block !important;
             }
         {% endif %}

        .header_banner_tickets {
            position: fixed;
            top: 0;
            left: 0;
            background: white;
            z-index: 10;
            box-shadow: 0 6px 6px #00000029;
            padding: 15px 50px;
            width: calc(100% - 100px);
        }

        .header_banner_tickets .content_wrapper {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header_banner_tickets .left_wrapper {
            display: flex;
            align-items: center;
        }

        .header_banner_tickets .left_wrapper .bar_tickets {
            width: 160px;
            height: 26px;
            border: 3px solid #80808045;
            border-radius: 10px;
            margin-right: 30px;
            padding: 3px;
        }

        .header_banner_tickets .left_wrapper .bar_tickets .bar {
            position: relative;
            height: 100%;
            display: flex;
            align-items: center;
            color: white;
            border-radius: 5px;
            overflow: hidden;
            font-weight: 600;
            font-size: 17px;
            font-family: 'Open Sans', sans-serif;
            padding-left: 90px;
        }

        .header_banner_tickets .left_wrapper .bar_tickets .bar:after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 80%;
            height: 100%;
            background: {% if banner_tickets.main_color %}{{ banner_tickets.main_color|safe }}{% else %}#456BA7{% endif %};
            z-index: -1;
        }

        .header_banner_tickets .left_wrapper .title_tickets {
            font-size: 16px;
            color: {% if banner_tickets.main_color %}{{ banner_tickets.main_color|safe }}{% else %}#002E6C{% endif %};
            text-decoration: underline;
        }

        .header_banner_tickets .button_tickets {
            border-radius: 5px;
            background: {% if banner_tickets.main_color %}{{ banner_tickets.main_color|safe }}{% else %}#456BA7{% endif %};
            color: white;
            font-size: 18px;
            align-items: center;
            width: 250px;
            text-align: center;
            text-decoration: none;
            padding: 15px 0;
            display: flex;
            justify-content: center;
        }

        .header_banner_tickets .button_tickets i {
            margin-left: 5px;
        }
    </style>

    <script>
        let menu_is_fixed = false;

        function toggle_fixed(activate) {
            if (activate && !menu_is_fixed) {
                $(".header_banner_tickets").show();
                menu_is_fixed = true;
            }

            if (!activate && menu_is_fixed) {
                $(".header_banner_tickets").hide();
                menu_is_fixed = false;
            }
        }

        $(window).on("scroll", () => {
            toggle_fixed($(window).scrollTop() > 350);
        });
    </script>
{% endif %}
<br>
<div class="shopping_cart_wrapper" style="max-width: 500px;margin: auto;">
    <div class="rooms_wrapper">
        <div class="room_title"
             style="padding: 10px;box-sizing: border-box;font-size: 16px;text-transform: uppercase;
                     {% if shopping_cart_styles.main_color %}background-color:{{ shopping_cart_styles.main_color }};color: white;{% endif %}">
            {{ T_habitaciones }} {% if not shopping_cart_styles.disable_b4_number %}({{ extraInfo.shopping_cart_human_read.rooms|length }}){% endif %}
        </div>

        <div class="room_options_wrapper">
            {% for room_cart in extraInfo.shopping_cart_human_read.rooms %}
                <br>
                <div class="room_element_title" style="font-size: 14px;"><b>{{ T_habitacion }} {{ forloop.counter }}</b></div>
                <br>
                <div class="room_element">
                    {% if dates_per_room %}
                        <div class="room_item"><b>{{ T_entrada }}: </b> {% if room_cart.entry_date %}{{ room_cart.entry_date|safe }}{% else %}{{ localizedStartDate|safe }}{% endif %}</div>
                        <div class="room_item"><b>{{ T_salida }}: </b> {% if room_cart.departure_date %}{{ room_cart.departure_date|safe }}{% else %}{{ localizedEndDate|safe }}{% endif %}</div>
                    {% endif %}
                    <div class="room_item"><b>{{ T_nombre }}: </b> {{ room_cart.room_name|safe }}</div>
                    <div class="room_item"><b>{{ T_tarifa }}: </b> {{ room_cart.rate_name|safe }}</div>
                    <div class="room_item"><b>{{ T_regimen }}: </b> {{ room_cart.board_name|safe }}</div>
                    {% if room_cart.occupancy %}
                        <div class="room_item"><b>{{ T_ocupacion }}: </b>
                        {% for occupancy in room_cart.occupancy|split:"-" %}
                            {% cycle T_adultos T_ninos T_bebes %}: {{ occupancy }}{% if not forloop.last %},{% endif %}
                        {% endfor %}
                        </div>
                    {% endif %}
                    {% if room_cart.promocode %}
                        <div class="room_item"><b>{{ T_envio_promocode }}: </b> {{ room_cart.promocode|safe }}</div>
                    {% endif %}
                    {% if room_cart.package_name %}
                        <div class="room_item"><b>{{ room_cart.package_name|safe }}</b></div>
                    {% endif %}
                    {% if room_cart.forfait %}
                        <div class="room_item">
                            <b>{{ room_cart.forfait.forfait_name|safe }}</b>
                        </div>
                        {% for adult in room_cart.forfait.adults %}
                            <br>
                            <div class="forfait_item"><b>{{ T_adulto }} {{ forloop.counter }}</b></div>
                            <div class="forfait_item"><b>{{ T_dias }}: </b> {{ adult.days }}</div>
                            <div class="forfait_item"><b>{{ T_fecha }}: </b> {{ adult.date }}</div>
                        {% endfor %}

                        {% for kid in room_cart.forfait.kids %}
                            <br>
                            <div class="forfait_item"><b>{{ T_nino }} {{ forloop.counter }}</b></div>
                            <div class="forfait_item"><b>{{ T_dias }}: </b> {{ kid.days }}</div>
                            <div class="forfait_item"><b>{{ T_fecha }}: </b> {{ kid.date }}</div>
                        {% endfor %}
                    {% endif %} 
                </div>
            {% endfor %}
        </div>
    </div>
    {% if extraInfo.shopping_cart_human_read.services %}
        <div class="services_wrapper">
            <div class="hidden" style="display: none">
                {{ extraInfo.shopping_cart_human_read }}
            </div>
            <div class="service_title"
                 style="padding: 10px;box-sizing: border-box;font-size: 16px;text-transform: uppercase;text-align: center;
                         {% if shopping_cart_styles.main_color %}background-color:{{ shopping_cart_styles.main_color }};color: white;{% endif %}">{{ T_servicios_adicionales }} ({{ extraInfo.shopping_cart_human_read.services|length }})</div>

            <div class="service_options_wrapper">
                {% for service_cart in extraInfo.shopping_cart_human_read.services %}
                    <br>
                    <div class="service_element_title" style="font-size: 14px;"><b>{{ T_servicio }} {{ forloop.counter }}</b></div>
                    <br>
                    <div class="service_element">
                        <div class="service_item"><b>{{ T_nombre }}: </b> {{ service_cart.name|safe }}</div>
                        <div class="service_item"><b>{{ T_cantidad }}: </b> {{ service_cart.amount|safe }}</div>
                        <div class="service_item"><b>{{ T_numero_dias }}: </b> {{ service_cart.days|safe }}</div>
                    </div>
                {% endfor %}
            </div>
        </div>
    {% endif %}
</div>
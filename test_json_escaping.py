#!/usr/bin/env python3
"""
Test script to reproduce and fix the JSON escaping issue in dumps_json_for_javascript
"""

import json
import sys
import os

# Import the function directly
from utils.compatibility.compatibility_utils import dumps_json_for_javascript

def test_fixed_implementation():
    """Test the fixed implementation with comprehensive test cases"""
    
    # Comprehensive test data including all edge cases
    test_cases = [
        {
            "name": "Original problematic case",
            "data": {"property_name": 'Cama balinesa "Fresquito" ', "other_property": "Normal text"}
        },
        {
            "name": "Backticks case", 
            "data": {"with_backticks": "Text with `backticks` here"}
        },
        {
            "name": "Template expressions case",
            "data": {"template_expr": "Text with ${expression} here"}
        },
        {
            "name": "Newlines and tabs case",
            "data": {"multiline": "Text with\nnewlines\tand\ttabs"}
        },
        {
            "name": "Mixed complex case",
            "data": {
                "complex": 'Mixed "quotes" with `backticks` and ${expr} and\nnewlines',
                "simple": "Normal text",
                "number": 123,
                "boolean": True
            }
        }
    ]
    
    print("=== Testing Fixed Implementation ===")
    
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i}: {test_case['name']} ---")
        print(f"Input: {test_case['data']}")
        
        # Test the function
        result = dumps_json_for_javascript(test_case['data'])
        print(f"Output: {result}")
        
        # Test 1: Python json.loads compatibility
        try:
            parsed_python = json.loads(result)
            print("✓ Python json.loads: SUCCESS")
            
            # Verify data integrity
            if parsed_python == test_case['data']:
                print("✓ Data integrity: PRESERVED")
            else:
                print("✗ Data integrity: CORRUPTED")
                print(f"  Expected: {test_case['data']}")
                print(f"  Got: {parsed_python}")
                all_passed = False
                
        except Exception as e:
            print(f"✗ Python json.loads: FAILED - {e}")
            all_passed = False
        
        # Test 2: JavaScript template literal safety
        js_issues = []
        if '`' in result and result.count('`') != result.count('\\`'):
            js_issues.append("Unescaped backticks")
        if '${' in result and result.count('${') != result.count('\\${'):
            js_issues.append("Unescaped template expressions")
        if '\n' in result or '\t' in result:
            js_issues.append("Unescaped newlines/tabs")
            
        if js_issues:
            print(f"✗ JS template safety: FAILED - {', '.join(js_issues)}")
            all_passed = False
        else:
            print("✓ JS template safety: PASSED")
    
    print(f"\n=== Overall Result: {'✓ ALL TESTS PASSED' if all_passed else '✗ SOME TESTS FAILED'} ===")
    return all_passed

def test_django_template_simulation():
    """Simulate actual Django template usage"""
    
    print("\n=== Django Template Simulation ===")
    
    # Test the specific problematic case
    test_data = {"property_name": 'Cama balinesa "Fresquito" '}
    result = dumps_json_for_javascript(test_data)
    
    print(f"Test data: {test_data}")
    print(f"Function result: {result}")
    
    # Simulate Django template: JSON.parse(`{{ result|safe }}`)
    django_template_output = f"JSON.parse(`{result}`)"
    print(f"Django template would generate: {django_template_output}")
    
    # Check if this would work in JavaScript
    print("\nJavaScript compatibility check:")
    
    # Check for common issues
    issues = []
    if '`' in result and result.count('`') != result.count('\\`'):
        issues.append("Unescaped backticks could break template literal")
    if '${' in result and result.count('${') != result.count('\\${'):
        issues.append("Unescaped template expressions could cause injection")
    if '\n' in result or '\t' in result:
        issues.append("Newlines/tabs could break JavaScript")
    
    if issues:
        print("✗ Issues found:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✓ No JavaScript compatibility issues found")
        return True

if __name__ == "__main__":
    print("✓ Using actual function from utils.compatibility.compatibility_utils")
    
    # Test the fixed implementation
    all_tests_passed = test_fixed_implementation()
    
    # Test Django template simulation
    django_test_passed = test_django_template_simulation()
    
    print(f"\n{'='*50}")
    print(f"FINAL RESULT: {'✓ ALL TESTS PASSED' if all_tests_passed and django_test_passed else '✗ SOME TESTS FAILED'}")
    print(f"{'='*50}")
